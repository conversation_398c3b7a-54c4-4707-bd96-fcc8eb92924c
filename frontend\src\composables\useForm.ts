import { ref, reactive, computed, watch, toRaw } from 'vue'
// import type { Ref } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

export interface UseFormOptions<T> {
  initialValues?: Partial<T>
  rules?: FormRules
  resetAfterSubmit?: boolean
}

export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T> = {}
) {
  const { initialValues = {}, resetAfterSubmit = false } = options
  
  // 表单引用
  const formRef = ref<FormInst | null>(null)
  
  // 表单数据
  const formData = reactive<T>({ ...initialValues } as T)
  
  // 表单状态
  const loading = ref(false)
  const submitting = ref(false)
  
  // 验证状态
  const errors = ref<Record<string, string>>({})
  
  // 计算属性
  const isValid = computed(() => Object.keys(errors.value).length === 0)
  const hasErrors = computed(() => Object.keys(errors.value).length > 0)
  
  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    Object.assign(formData, data)
  }
  
  // 重置表单
  const resetForm = () => {
    Object.assign(formData, initialValues)
    errors.value = {}
    formRef.value?.restoreValidation()
  }
  
  // 清空表单
  const clearForm = () => {
    Object.keys(formData).forEach(key => {
      if (typeof formData[key] === 'string') {
        formData[key] = '' as any
      } else if (typeof formData[key] === 'number') {
        formData[key] = 0 as any
      } else if (Array.isArray(formData[key])) {
        formData[key] = [] as any
      } else if (typeof formData[key] === 'object' && formData[key] !== null) {
        formData[key] = {} as any
      } else {
        formData[key] = null as any
      }
    })
    errors.value = {}
    formRef.value?.restoreValidation()
  }
  
  // 验证表单
  const validate = async (): Promise<boolean> => {
    try {
      if (!formRef.value) return false
      await formRef.value.validate()
      errors.value = {}
      return true
    } catch (validationErrors: any) {
      if (Array.isArray(validationErrors)) {
        const errorMap: Record<string, string> = {}
        validationErrors.forEach((error: any) => {
          if (error.field && error.message) {
            errorMap[error.field] = error.message
          }
        })
        errors.value = errorMap
      }
      return false
    }
  }
  
  // 验证指定字段
  const validateField = async (field: keyof T): Promise<boolean> => {
    try {
      if (!formRef.value) return false
      await formRef.value.validate(undefined, (rule) => rule.key === field)
      if (errors.value[field as string]) {
        delete errors.value[field as string]
      }
      return true
    } catch {
      return false
    }
  }
  
  // 设置字段错误
  const setFieldError = (field: keyof T, message: string) => {
    errors.value[field as string] = message
  }
  
  // 清除字段错误
  const clearFieldError = (field: keyof T) => {
    if (errors.value[field as string]) {
      delete errors.value[field as string]
    }
  }
  
  // 设置多个字段错误
  const setErrors = (newErrors: Record<string, string>) => {
    errors.value = { ...errors.value, ...newErrors }
  }
  
  // 清除所有错误
  const clearErrors = () => {
    errors.value = {}
    formRef.value?.restoreValidation()
  }
  
  // 提交表单
  const handleSubmit = async (
    submitFn: (data: T) => Promise<any>,
    options: { showLoading?: boolean } = {}
  ) => {
    const { showLoading = true } = options
    
    try {
      if (showLoading) {
        submitting.value = true
      }
      
      // 验证表单
      const isValid = await validate()
      if (!isValid) {
        return false
      }
      
      // 执行提交函数，将reactive对象转换为普通对象
      const result = await submitFn({ ...formData } as T)
      
      // 如果设置了提交后重置，则重置表单
      if (resetAfterSubmit) {
        resetForm()
      }
      
      return result
    } catch (error: any) {
      // 如果是验证错误，设置错误信息
      if (error.errors && typeof error.errors === 'object') {
        setErrors(error.errors)
      }
      throw error
    } finally {
      if (showLoading) {
        submitting.value = false
      }
    }
  }
  
  // 获取字段值
  const getFieldValue = (field: keyof T) => {
    return (toRaw(formData) as any)[field]
  }

  // 设置字段值
  const setFieldValue = (field: keyof T, value: any) => {
    ;(formData as any)[field] = value
  }

  // 监听字段变化
  const watchField = (field: keyof T, callback: (value: any) => void) => {
    watch(
      () => (toRaw(formData) as any)[field],
      callback,
      { immediate: true }
    )
  }
  
  return {
    // 引用
    formRef,
    
    // 数据
    formData,
    loading,
    submitting,
    errors,
    
    // 计算属性
    isValid,
    hasErrors,
    
    // 方法
    setFormData,
    resetForm,
    clearForm,
    validate,
    validateField,
    setFieldError,
    clearFieldError,
    setErrors,
    clearErrors,
    handleSubmit,
    getFieldValue,
    setFieldValue,
    watchField
  }
}