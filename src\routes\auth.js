const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
  const axios = require('axios');
const router = express.Router();

// JWT密钥（实际项目中应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'yysh_jwt_secret_key_2024';

// 登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    const db = req.app.locals.db;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找用户 - 修复数据库查询方法
    const sql = 'SELECT * FROM users WHERE username = ? LIMIT 1';
    const result = await db.query(sql, [username]);
    const user = result.rows[0] || null;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证密码
    let isValidPassword = false;
    
    if (user.password) {
      // 如果用户有加密密码，使用bcrypt验证
      isValidPassword = await bcrypt.compare(password, user.password);
    } else {
      // 兼容性处理：如果没有密码字段，使用默认密码
      isValidPassword = password === 'password' || password === 'admin123';
    }

    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '密码错误'
      });
    }

    // 检查用户权限 - 允许所有有效角色访问
    const validRoles = ['admin', 'manager', 'sales', 'designer', 'supervisor', 'director', 'general_manager', 'it_admin'];
    if (!validRoles.includes(user.role)) {
      return res.status(403).json({
        success: false,
        message: '无权限访问后台管理系统'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.name,
        role: user.role,
        departmentId: user.department_id
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 更新最后登录时间
    await db.query(
      'UPDATE users SET last_login_time = ? WHERE id = ?',
      [new Date(), user.id]
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          avatar: user.avatar,
          role: user.role,
          department_id: user.department_id,
          position: user.position,
          mobile: user.mobile,
          email: user.email,
          last_login_time: user.last_login_time
        }
      }
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const db = req.app.locals.db;
    const result = await db.query('SELECT * FROM users WHERE id = ? LIMIT 1', [req.user.userId]);
    const user = result.rows[0] || null;

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        avatar: user.avatar,
        department_id: user.department_id,
        position: user.position,
        mobile: user.mobile,
        email: user.email,
        last_login_time: user.last_login_time
      }
    });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 刷新token
router.post('/refresh', authenticateToken, (req, res) => {
  try {
    const newToken = jwt.sign(
      {
        userId: req.user.userId,
        username: req.user.username,
        role: req.user.role,
        departmentId: req.user.departmentId
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      data: { token: newToken }
    });

  } catch (error) {
    console.error('刷新token错误:', error);
    res.status(500).json({
      success: false,
      message: '刷新token失败'
    });
  }
});

// 企业微信登录 - 获取授权URL
router.get('/wechat/auth-url', (req, res) => {
  try {
    // 企业微信应用配置（实际项目中应该从环境变量或配置文件获取）
    const corpId = process.env.WECHAT_CORP_ID || 'your_corp_id';
    const agentId = process.env.WECHAT_AGENT_ID || 'your_agent_id';
    const redirectUri = encodeURIComponent(process.env.WECHAT_REDIRECT_URI || 'http://localhost:3000/api/auth/wechat/callback');
    const state = Math.random().toString(36).substring(2, 15);
    
    // 构建企业微信授权URL
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&agentid=${agentId}&state=${state}#wechat_redirect`;
    
    res.json({
      success: true,
      data: {
        authUrl,
        state
      }
    });
  } catch (error) {
    console.error('获取企业微信授权URL错误:', error);
    res.status(500).json({
      success: false,
      message: '获取授权URL失败'
    });
  }
});

// 企业微信登录回调
router.get('/wechat/callback', async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!code) {
      return res.redirect('/login.html?error=授权失败');
    }

          // 企业微信应用配置（与wechat.js保持一致）
    const wechatConfig = {
      corpId: process.env.WECHAT_CORP_ID,
      secret: process.env.WECHAT_SECRET,
      agentId: process.env.WECHAT_AGENT_ID
    };
    
    // 验证环境变量
    if (!wechatConfig.corpId || !wechatConfig.secret) {
      console.error('企业微信配置缺失:', {
        corpId: wechatConfig.corpId ? '已配置' : '未配置',
        secret: wechatConfig.secret ? '已配置' : '未配置'
      });
      return res.redirect('/login.html?error=企业微信配置缺失');
    }
    
    // 记录环境变量信息（用于调试）
    console.log('登录接口环境变量:', {
      corpId: wechatConfig.corpId,
      secret: wechatConfig.secret ? wechatConfig.secret.substring(0, 8) + '...' : 'undefined'
    });
    
    // 1. 获取access_token
    const tokenResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${wechatConfig.corpId}&corpsecret=${wechatConfig.secret}`);
    const tokenData = tokenResponse.data;
    
    if (tokenData.errcode !== 0) {
      console.error('获取access_token失败:', {
        errcode: tokenData.errcode,
        errmsg: tokenData.errmsg,
        corpId: wechatConfig.corpId,
        secret: wechatConfig.secret ? wechatConfig.secret.substring(0, 8) + '...' : 'undefined'
      });
      return res.redirect('/login.html?error=获取访问令牌失败');
    }

    // 2. 通过code获取用户信息
      const userResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=${tokenData.access_token}&code=${code}`);
      const userData = userResponse.data;
    
    if (userData.errcode !== 0) {
        console.error('获取用户信息失败:', {
          errcode: userData.errcode,
          errmsg: userData.errmsg,
          code: code,
          access_token: tokenData.access_token ? tokenData.access_token.substring(0, 20) + '...' : 'undefined'
        });
      return res.redirect('/login.html?error=获取用户信息失败');
    }

    const { UserId } = userData;
    
    if (!UserId) {
      return res.redirect('/login.html?error=用户信息不完整');
    }

    // 3. 根据企业微信UserId查找系统用户
    const db = req.app.locals.db;
    let user = await db.findOne('users', 'wechat_userid = ?', [UserId]);
    
    if (!user) {
      // 如果用户不存在，尝试通过企业微信API获取详细信息并创建用户
        const detailResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${tokenData.access_token}&userid=${UserId}`);
        const detailData = detailResponse.data;
      
      if (detailData.errcode === 0) {
        // 创建新用户
        const newUser = {
          name: detailData.name || UserId,
          username: UserId,
          phone: detailData.mobile || '',
          email: detailData.email || '',
          avatar: detailData.avatar || '',
          wechat_userid: UserId,
          role: 'sales', // 默认角色，可根据需要调整
          department_id: 1, // 默认部门，可根据需要调整
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        };
        
        const userId = await db.insert('users', newUser);
        user = { id: userId, ...newUser };
      } else {
        return res.redirect('/login.html?error=获取用户详细信息失败');
      }
    }

    // 检查用户权限
    const validRoles = ['admin', 'manager', 'sales', 'designer', 'supervisor', 'director', 'general_manager', 'it_admin'];
    if (!validRoles.includes(user.role)) {
      return res.redirect('/login.html?error=无权限访问系统');
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.name,
        role: user.role,
        departmentId: user.department_id
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 更新最后登录时间
    await db.update(
      'users',
      { last_login_time: new Date() },
      'id = ?',
      [user.id]
    );

    // 重定向到主页面，并传递token
    res.redirect(`/?token=${token}&user=${encodeURIComponent(JSON.stringify({
      id: user.id,
      name: user.name,
      role: user.role,
      avatar: user.avatar,
      department_id: user.department_id
    }))}`);

  } catch (error) {
    console.error('企业微信登录回调错误:', error);
    res.redirect('/login.html?error=登录失败');
  }
});

// 企业微信登录 - 支持code和userId两种方式
router.post('/wechat/login', async (req, res) => {
  try {
    const { code, userId } = req.body;
    
    if (!code && !userId) {
      return res.status(400).json({
        success: false,
        message: '授权码或用户ID不能为空'
      });
    }

    let finalUserId = userId;
    
    // 如果提供的是code，需要先获取userId
    if (code && !userId) {
              // 企业微信应用配置（与wechat.js保持一致）
      const wechatConfig = {
        corpId: process.env.WECHAT_CORP_ID,
        secret: process.env.WECHAT_SECRET,
        agentId: process.env.WECHAT_AGENT_ID
      };
      
      // 验证环境变量
      if (!wechatConfig.corpId || !wechatConfig.secret) {
        console.error('企业微信配置缺失:', {
          corpId: wechatConfig.corpId ? '已配置' : '未配置',
          secret: wechatConfig.secret ? '已配置' : '未配置'
        });
        return res.status(500).json({
          success: false,
          message: '企业微信配置缺失'
        });
      }
      
      // 1. 获取access_token
      const tokenResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${wechatConfig.corpId}&corpsecret=${wechatConfig.secret}`);
      const tokenData = tokenResponse.data;
      
      if (tokenData.errcode !== 0) {
        console.error('获取access_token失败:', {
          errcode: tokenData.errcode,
          errmsg: tokenData.errmsg,
          corpId: wechatConfig.corpId,
          secret: wechatConfig.secret ? wechatConfig.secret.substring(0, 8) + '...' : 'undefined'
        });
        return res.status(500).json({
          success: false,
          message: '获取访问令牌失败'
        });
      }

      // 2. 通过code获取用户信息
        const userResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=${tokenData.access_token}&code=${code}`);
        const userData = userResponse.data;
      
      if (userData.errcode !== 0) {
        console.error('获取用户信息失败:', userData);
        return res.status(500).json({
          success: false,
          message: '获取用户信息失败'
        });
      }

      finalUserId = userData.UserId;
    }
    
    if (!finalUserId) {
      return res.status(400).json({
        success: false,
        message: '用户信息不完整'
      });
    }

    // 3. 根据企业微信UserId查找系统用户
    const db = req.app.locals.db;
    let user = await db.findOne('users', 'wechat_userid = ?', [finalUserId]);
    
    if (!user) {
      // 如果用户不存在，尝试通过企业微信API获取详细信息并创建用户
        const wechatConfig2 = {
          corpId: process.env.WECHAT_CORP_ID,
          secret: process.env.WECHAT_SECRET,
          agentId: process.env.WECHAT_AGENT_ID
        };
        
        // 验证环境变量
        if (!wechatConfig2.corpId || !wechatConfig2.secret) {
          console.error('企业微信配置缺失:', {
            corpId: wechatConfig2.corpId ? '已配置' : '未配置',
            secret: wechatConfig2.secret ? '已配置' : '未配置'
          });
          return res.status(500).json({
            success: false,
            message: '企业微信配置缺失'
          });
        }
      
        const tokenResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${wechatConfig2.corpId}&corpsecret=${wechatConfig2.secret}`);
        const tokenData = tokenResponse.data;
      
      if (tokenData.errcode !== 0) {
          console.error('获取access_token失败:', {
            errcode: tokenData.errcode,
            errmsg: tokenData.errmsg,
            corpId: corpId,
            corpSecret: corpSecret ? corpSecret.substring(0, 8) + '...' : 'undefined'
          });
        return res.status(500).json({
          success: false,
          message: '获取访问令牌失败'
        });
      }
      
        const detailResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${tokenData.access_token}&userid=${finalUserId}`);
        const detailData = detailResponse.data;
      
      if (detailData.errcode === 0) {
        // 创建新用户
        const newUser = {
          name: detailData.name || finalUserId,
          username: finalUserId,
          phone: detailData.mobile || '',
          email: detailData.email || '',
          avatar: detailData.avatar || '',
          wechat_userid: finalUserId,
          role: 'sales', // 默认角色，可根据需要调整
          department_id: 1, // 默认部门，可根据需要调整
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        };
        
        const userId = await db.insert('users', newUser);
        user = { id: userId, ...newUser };
      } else {
        return res.status(500).json({
          success: false,
          message: '获取用户详细信息失败'
        });
      }
    }

    // 检查用户权限
    const validRoles = ['admin', 'manager', 'sales', 'designer', 'supervisor', 'director', 'general_manager', 'it_admin'];
    if (!validRoles.includes(user.role)) {
      return res.status(403).json({
        success: false,
        message: '无权限访问系统'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.name,
        role: user.role,
        departmentId: user.department_id
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 更新最后登录时间
    await db.update(
      'users',
      { last_login_time: new Date() },
      'id = ?',
      [user.id]
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          department_id: user.department_id,
          position: user.position,
          mobile: user.mobile,
          email: user.email,
          last_login_time: user.last_login_time
        }
      }
    });

  } catch (error) {
    console.error('企业微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

// 调试接口 - 详细登录调试
router.post('/debug/login', async (req, res) => {
  try {
    const { username, password, debug } = req.body;
    const db = req.app.locals.db;
    
    // 调试信息对象
    const debugInfo = {
      timestamp: new Date().toISOString(),
      request: { username, hasPassword: !!password },
      steps: []
    };
    
    console.log('=== 登录调试开始 ===');
    console.log('请求参数:', { username, password: password ? '***' : 'empty' });
    
    if (!username || !password) {
      debugInfo.error = '用户名和密码不能为空';
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空',
        debug: debugInfo
      });
    }

    // 步骤1: 查找用户
    debugInfo.steps.push('开始查找用户...');
    console.log('步骤1: 查找用户:', username);
    
    const sql = 'SELECT * FROM users WHERE username = ? LIMIT 1';
    const result = await db.query(sql, [username]);
    const user = result.rows[0] || null;
    
    debugInfo.userFound = !!user;
    debugInfo.steps.push(`用户查找结果: ${user ? '找到' : '未找到'}`);
    console.log('用户查找结果:', user ? '找到用户' : '用户不存在');
    
    if (user) {
      debugInfo.userInfo = {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        hasPassword: !!user.password,
        department_id: user.department_id
      };
      console.log('用户信息:', debugInfo.userInfo);
    }

    if (!user) {
      debugInfo.steps.push('登录失败: 用户不存在');
      return res.status(401).json({
        success: false,
        message: '用户不存在',
        debug: debugInfo
      });
    }

    // 步骤2: 验证密码
    debugInfo.steps.push('开始验证密码...');
    console.log('步骤2: 验证密码');
    console.log('输入密码:', password);
    console.log('数据库密码哈希:', user.password);
    console.log('bcrypt版本:', require('bcryptjs/package.json').version);
    
    let isValidPassword = false;
    
    if (user.password) {
      // 如果用户有加密密码，使用bcrypt验证
      console.log('开始bcrypt.compare验证...');
      try {
        isValidPassword = await bcrypt.compare(password, user.password);
        console.log('bcrypt.compare执行完成，结果:', isValidPassword);
      } catch (bcryptError) {
        console.error('bcrypt.compare执行出错:', bcryptError);
        debugInfo.steps.push(`bcrypt验证出错: ${bcryptError.message}`);
      }
      debugInfo.steps.push(`密码验证方式: bcrypt比较`);
      console.log('使用bcrypt验证密码');
    } else {
      // 兼容性处理：如果没有密码字段，使用默认密码
      isValidPassword = password === 'password' || password === 'admin123';
      debugInfo.steps.push(`密码验证方式: 默认密码比较`);
      console.log('使用默认密码验证');
    }
    
    debugInfo.passwordValid = isValidPassword;
    debugInfo.passwordHash = user.password;
    debugInfo.inputPassword = password;
    debugInfo.bcryptVersion = require('bcryptjs/package.json').version;
    debugInfo.steps.push(`密码验证结果: ${isValidPassword ? '正确' : '错误'}`);
    console.log('密码验证结果:', isValidPassword ? '正确' : '错误');

    if (!isValidPassword) {
      debugInfo.steps.push('登录失败: 密码错误');
      return res.status(401).json({
        success: false,
        message: '密码错误',
        debug: debugInfo
      });
    }

    // 步骤3: 检查用户权限
    debugInfo.steps.push('开始检查用户权限...');
    console.log('步骤3: 检查用户权限');
    
    const validRoles = ['admin', 'manager', 'sales', 'designer', 'supervisor', 'director', 'general_manager', 'it_admin'];
    const roleValid = validRoles.includes(user.role);
    
    debugInfo.roleValid = roleValid;
    debugInfo.validRoles = validRoles;
    debugInfo.userRole = user.role;
    debugInfo.steps.push(`权限检查结果: ${roleValid ? '通过' : '失败'}`);
    console.log('权限检查:', { userRole: user.role, validRoles, result: roleValid });
    
    if (!roleValid) {
      debugInfo.steps.push('登录失败: 无权限访问后台管理系统');
      return res.status(403).json({
        success: false,
        message: '无权限访问后台管理系统',
        debug: debugInfo
      });
    }

    // 步骤4: 生成JWT token
    debugInfo.steps.push('开始生成JWT token...');
    console.log('步骤4: 生成JWT token');
    
    const tokenPayload = {
      userId: user.id,
      username: user.name,
      role: user.role,
      departmentId: user.department_id
    };
    
    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });
    
    debugInfo.tokenGenerated = true;
    debugInfo.tokenPayload = tokenPayload;
    debugInfo.jwtSecret = JWT_SECRET.substring(0, 10) + '...';
    debugInfo.steps.push('JWT token生成成功');
    console.log('JWT token生成成功');

    // 步骤5: 更新最后登录时间
    debugInfo.steps.push('更新最后登录时间...');
    console.log('步骤5: 更新最后登录时间');
    
    await db.query(
      'UPDATE users SET last_login_time = ? WHERE id = ?',
      [new Date(), user.id]
    );
    
    debugInfo.steps.push('登录成功完成');
    console.log('=== 登录调试完成 ===');

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          avatar: user.avatar,
          role: user.role,
          department_id: user.department_id,
          position: user.position,
          mobile: user.mobile,
          email: user.email,
          last_login_time: user.last_login_time
        }
      },
      debug: debugInfo
    });

  } catch (error) {
    console.error('登录调试错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试',
      debug: {
        error: error.message,
        stack: error.stack
      }
    });
  }
});

// 调试接口 - 认证状态检查
router.get('/debug/auth-status', (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    const debugInfo = {
      timestamp: new Date().toISOString(),
      hasAuthHeader: !!authHeader,
      authHeader: authHeader ? authHeader.substring(0, 20) + '...' : null,
      hasToken: !!token,
      tokenLength: token ? token.length : 0
    };
    
    console.log('=== 认证状态检查 ===');
    console.log('Authorization Header:', authHeader ? 'Present' : 'Missing');
    console.log('Token:', token ? 'Present' : 'Missing');
    
    if (!token) {
      debugInfo.result = 'no_token';
      debugInfo.message = '访问令牌缺失';
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失',
        debug: debugInfo
      });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
      if (err) {
        debugInfo.result = 'invalid_token';
        debugInfo.error = err.message;
        debugInfo.jwtSecret = JWT_SECRET.substring(0, 10) + '...';
        console.log('Token验证失败:', err.message);
        
        return res.status(403).json({
          success: false,
          message: '访问令牌无效',
          debug: debugInfo
        });
      }
      
      debugInfo.result = 'valid_token';
      debugInfo.user = user;
      debugInfo.message = '认证成功';
      console.log('Token验证成功:', user);
      
      res.json({
        success: true,
        message: '认证成功',
        data: { user },
        debug: debugInfo
      });
    });

  } catch (error) {
    console.error('认证状态检查错误:', error);
    res.status(500).json({
      success: false,
      message: '认证状态检查失败',
      debug: {
        error: error.message
      }
    });
  }
});

// 调试接口 - 用户信息验证
router.get('/debug/user-info', authenticateToken, async (req, res) => {
  try {
    const db = req.app.locals.db;
    
    const debugInfo = {
      timestamp: new Date().toISOString(),
      tokenUser: req.user,
      steps: []
    };
    
    console.log('=== 用户信息验证 ===');
    console.log('Token中的用户信息:', req.user);
    
    debugInfo.steps.push('从token中获取用户ID: ' + req.user.userId);
    
    const result = await db.query('SELECT * FROM users WHERE id = ? LIMIT 1', [req.user.userId]);
    const user = result.rows[0] || null;
    
    debugInfo.userFound = !!user;
    debugInfo.steps.push(`数据库查询结果: ${user ? '找到用户' : '用户不存在'}`);
    
    if (!user) {
      debugInfo.steps.push('验证失败: 用户不存在');
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        debug: debugInfo
      });
    }
    
    debugInfo.databaseUser = {
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      department_id: user.department_id,
      status: user.status,
      last_login_time: user.last_login_time
    };
    
    debugInfo.steps.push('用户信息验证成功');
    console.log('用户信息验证成功');

    res.json({
      success: true,
      message: '用户信息验证成功',
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        avatar: user.avatar,
        department_id: user.department_id,
        position: user.position,
        mobile: user.mobile,
        email: user.email,
        last_login_time: user.last_login_time
      },
      debug: debugInfo
    });

  } catch (error) {
    console.error('用户信息验证错误:', error);
    res.status(500).json({
      success: false,
      message: '用户信息验证失败',
      debug: {
        error: error.message,
        stack: error.stack
      }
    });
  }
});

// 登出
router.post('/logout', authenticateToken, (req, res) => {
  // 这里可以将token加入黑名单
  res.json({
    success: true,
    message: '登出成功'
  });
});

// JWT认证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: '访问令牌无效'
      });
    }
    req.user = user;
    next();
  });
}

// 权限检查中间件
function requireRole(roles) {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }
    next();
  };
}

module.exports = router;
module.exports.authenticateToken = authenticateToken;
module.exports.requireRole = requireRole;