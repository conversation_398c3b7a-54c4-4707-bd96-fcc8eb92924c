{"migrationId": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "timestamp": "2025-08-18T07:34:19.491Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 21, "failedTables": 0, "totalRecords": 165, "migratedRecords": 165, "failedRecords": 0}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:33:40.138Z", "errors": [], "endTime": "2025-08-18T07:33:43.692Z", "duration": 3554}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:33:43.697Z", "errors": [], "endTime": "2025-08-18T07:33:45.559Z", "duration": 1862}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:33:45.560Z", "errors": [], "endTime": "2025-08-18T07:33:47.076Z", "duration": 1516}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:33:47.079Z", "errors": [], "endTime": "2025-08-18T07:33:48.882Z", "duration": 1803}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:33:48.885Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:33:49.251Z", "errors": [], "endTime": "2025-08-18T07:33:51.075Z", "duration": 1824}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:33:51.078Z", "errors": [], "endTime": "2025-08-18T07:33:53.143Z", "duration": 2065}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:33:53.145Z", "errors": [], "endTime": "2025-08-18T07:33:56.306Z", "duration": 3161}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:33:56.310Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:33:56.734Z", "errors": [], "endTime": "2025-08-18T07:33:58.319Z", "duration": 1585}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:33:58.323Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:33:58.687Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:34:00.330Z", "errors": [], "endTime": "2025-08-18T07:34:03.764Z", "duration": 3434}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:34:03.767Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:34:04.871Z", "errors": [], "endTime": "2025-08-18T07:34:07.974Z", "duration": 3103}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:34:07.977Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:34:08.347Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:34:09.548Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:34:10.542Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:34:11.484Z", "errors": [], "endTime": "2025-08-18T07:34:14.094Z", "duration": 2610}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 1, "failedRecords": 0, "startTime": "2025-08-18T07:34:14.096Z", "errors": [], "endTime": "2025-08-18T07:34:19.488Z", "duration": 5392}], "logs": [{"id": "fc9e1c3c-db10-492e-90dc-82455ccdd807", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:33:40.138Z", "end_time": "2025-08-18T07:33:43.692Z", "duration_ms": 3554}, {"id": "f3711af8-7469-45fa-862d-e164279f8d6f", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:33:43.697Z", "end_time": "2025-08-18T07:33:45.559Z", "duration_ms": 1862}, {"id": "79e4813c-ff6d-4b00-9c72-f6ef9d7e0a40", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:33:45.560Z", "end_time": "2025-08-18T07:33:47.076Z", "duration_ms": 1516}, {"id": "daa5a751-fd2c-44ec-8da8-789f91976f3c", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:33:47.079Z", "end_time": "2025-08-18T07:33:48.882Z", "duration_ms": 1803}, {"id": "0ff186b5-9eca-4ec2-9654-dcc991949f9d", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:33:48.885Z", "end_time": "2025-08-18T07:33:49.251Z", "duration_ms": 366}, {"id": "9e5d43c9-fdaa-477a-8178-39685ed1712d", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:33:49.251Z", "end_time": "2025-08-18T07:33:51.075Z", "duration_ms": 1824}, {"id": "29e8b5d8-5ed7-4bc4-bae7-0c1fca35c980", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:33:51.078Z", "end_time": "2025-08-18T07:33:53.143Z", "duration_ms": 2065}, {"id": "96a8df0f-6c45-4a60-b8b2-29064b684a6e", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:33:53.145Z", "end_time": "2025-08-18T07:33:56.306Z", "duration_ms": 3161}, {"id": "85659d19-f009-495a-b0a6-98dbcdc63376", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:33:56.310Z", "end_time": "2025-08-18T07:33:56.734Z", "duration_ms": 424}, {"id": "30ccb6fb-dc25-4c83-94b6-f2115c3607fa", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:33:56.734Z", "end_time": "2025-08-18T07:33:58.319Z", "duration_ms": 1585}, {"id": "1113ceba-ac06-478f-9b4e-05e56c460b23", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:33:58.323Z", "end_time": "2025-08-18T07:33:58.687Z", "duration_ms": 364}, {"id": "98c64336-2b77-4c38-b9c6-58275ede2c6c", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:33:58.687Z", "end_time": "2025-08-18T07:34:00.330Z", "duration_ms": 1643}, {"id": "d74ccb54-1060-4090-b382-ee5077ea0893", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:34:00.330Z", "end_time": "2025-08-18T07:34:03.764Z", "duration_ms": 3434}, {"id": "95e631b3-9b49-4517-a9ff-40dd3ca256db", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:34:03.767Z", "end_time": "2025-08-18T07:34:04.870Z", "duration_ms": 1103}, {"id": "65ccd7c8-ec2c-4812-a520-ebeaf54127fc", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:34:04.871Z", "end_time": "2025-08-18T07:34:07.974Z", "duration_ms": 3103}, {"id": "2564c91c-8e96-4669-a2ca-7aa546eff50a", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:34:07.977Z", "end_time": "2025-08-18T07:34:08.346Z", "duration_ms": 369}, {"id": "faa03cee-a246-4427-b5ff-3bb552d3842c", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:34:08.347Z", "end_time": "2025-08-18T07:34:09.548Z", "duration_ms": 1201}, {"id": "b7cc808c-d7f4-4cc1-aa6f-ff0e6e6d0575", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:34:09.548Z", "end_time": "2025-08-18T07:34:10.542Z", "duration_ms": 994}, {"id": "845df5d8-d742-4de7-ba8b-5739b4932fc6", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:34:10.542Z", "end_time": "2025-08-18T07:34:11.484Z", "duration_ms": 942}, {"id": "c1423593-b7cb-4800-be97-7a1b32fb22d9", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:34:11.484Z", "end_time": "2025-08-18T07:34:14.094Z", "duration_ms": 2610}, {"id": "188fe06b-dcfa-4aaf-b3aa-8d26f9cf2795", "migration_id": "1125b683-0bdc-44bc-a4ce-33ff330d4442", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 1, "start_time": "2025-08-18T07:34:14.096Z", "end_time": "2025-08-18T07:34:19.488Z", "duration_ms": 5392}]}