<template>
  <div class="share-detail-panel">
    <!-- 分享基本信息 -->
    <div class="share-header">
      <div class="share-user">
        <n-avatar :size="48" :src="share.user_avatar" fallback-src="/default-avatar.png" />
        <div class="user-info">
          <h3>{{ share.user_nickname }}</h3>
          <div class="user-meta">
            <span>用户ID: {{ share.user_id }}</span>
            <n-tag size="small" :type="getPlatformTagType(share.platform)">
              {{ getPlatformLabel(share.platform) }}
            </n-tag>
          </div>
        </div>
      </div>
      <div class="share-time">
        <n-icon size="16" color="#666">
          <TimeOutline />
        </n-icon>
        <span>{{ new Date(share.shared_at).toLocaleString() }}</span>
      </div>
    </div>

    <!-- 分享内容 -->
    <div class="share-content">
      <h4>分享内容</h4>
      <div class="content-card">
        <div class="content-header">
          <div class="content-title">{{ share.content_title }}</div>
          <n-tag :type="getShareTypeTagType(share.share_type)" size="small">
            {{ getShareTypeLabel(share.share_type) }}
          </n-tag>
        </div>
        <div class="content-description">{{ share.content_description }}</div>
        <div class="content-url">
          <n-icon size="14">
            <LinkOutline />
          </n-icon>
          <a :href="share.content_url" target="_blank">{{ share.content_url }}</a>
        </div>
        <div class="content-preview" v-if="contentPreview">
          <img :src="contentPreview.image" alt="内容预览" />
        </div>
      </div>
    </div>

    <!-- 分享数据统计 -->
    <div class="share-stats">
      <h4>数据统计</h4>
      <n-grid :cols="3" :x-gap="16">
        <n-grid-item>
          <n-card size="small">
            <n-statistic label="浏览量" :value="share.view_count">
              <template #prefix>
                <n-icon color="#18a058">
                  <EyeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small">
            <n-statistic label="点击量" :value="share.click_count">
              <template #prefix>
                <n-icon color="#2080f0">
                  <FingerPrintOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small">
            <n-statistic label="转化数" :value="share.conversion_count">
              <template #prefix>
                <n-icon color="#f0a020">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
      
      <!-- 转化率和详细指标 -->
      <div class="detailed-metrics">
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <div class="metric-item">
              <div class="metric-label">转化率</div>
              <div class="metric-value">
                <span class="rate-value" :style="{ color: getConversionRateColor(share.conversion_rate) }">
                  {{ share.conversion_rate }}%
                </span>
                <n-progress
                  :percentage="share.conversion_rate"
                  :color="getConversionRateColor(share.conversion_rate)"
                  :show-indicator="false"
                  style="margin-top: 4px"
                />
              </div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="metric-item">
              <div class="metric-label">点击率</div>
              <div class="metric-value">
                <span class="rate-value">
                  {{ ((share.click_count / share.view_count) * 100).toFixed(1) }}%
                </span>
                <n-progress
                  :percentage="(share.click_count / share.view_count) * 100"
                  color="#2080f0"
                  :show-indicator="false"
                  style="margin-top: 4px"
                />
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>
    </div>

    <!-- 浏览轨迹 -->
    <div class="view-tracks">
      <h4>浏览轨迹</h4>
      <div class="tracks-timeline">
        <n-timeline>
          <n-timeline-item
            v-for="track in viewTracks"
            :key="track.id"
            type="info"
          >
            <template #header>
              <div class="track-header">
                <span class="track-user">{{ track.viewer_nickname }}</span>
                <span class="track-time">{{ new Date(track.viewed_at).toLocaleString() }}</span>
              </div>
            </template>
            <div class="track-content">
              <div class="track-action">{{ track.action }}</div>
              <div class="track-details">
                <span>停留时间: {{ track.duration }}s</span>
                <span>来源: {{ track.source }}</span>
                <span v-if="track.converted" class="converted-tag">
                  <n-tag type="success" size="small">已转化</n-tag>
                </span>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
        
        <div v-if="!viewTracks.length" class="no-tracks">
          <n-empty description="暂无浏览轨迹" size="small" />
        </div>
      </div>
    </div>

    <!-- 转化详情 -->
    <div class="conversion-details" v-if="conversions.length">
      <h4>转化详情</h4>
      <div class="conversions-list">
        <div class="conversion-item" v-for="conversion in conversions" :key="conversion.id">
          <div class="conversion-user">
            <n-avatar size="small" :src="conversion.user_avatar" fallback-src="/default-avatar.png" />
            <div class="conversion-info">
              <div class="conversion-name">{{ conversion.user_nickname }}</div>
              <div class="conversion-time">{{ new Date(conversion.converted_at).toLocaleString() }}</div>
            </div>
          </div>
          <div class="conversion-action">
            <n-tag type="success" size="small">
              {{ conversion.conversion_type }}
            </n-tag>
            <span class="conversion-value">¥{{ conversion.conversion_value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享效果分析 -->
    <div class="effect-analysis">
      <h4>效果分析</h4>
      <div class="analysis-chart" ref="analysisChartRef"></div>
    </div>

    <!-- 相关推荐 -->
    <div class="recommendations">
      <h4>优化建议</h4>
      <div class="recommendation-list">
        <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
          <div class="recommendation-icon">
            <n-icon size="20" :color="rec.priority === '高' ? '#d03050' : rec.priority === '中' ? '#f0a020' : '#18a058'">
              <BulbOutline />
            </n-icon>
          </div>
          <div class="recommendation-content">
            <div class="recommendation-title">{{ rec.title }}</div>
            <div class="recommendation-desc">{{ rec.description }}</div>
          </div>
          <div class="recommendation-priority">
            <n-tag :type="getPriorityTagType(rec.priority)" size="small">
              {{ rec.priority }}
            </n-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-space>
        <n-button type="primary" @click="handleAnalyzeEffect">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          深度分析
        </n-button>
        <n-button @click="handleExportReport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报告
        </n-button>
        <n-button @click="handleCreateSimilar">
          <template #icon>
            <n-icon><CopyOutline /></n-icon>
          </template>
          创建相似分享
        </n-button>
        <n-button @click="$emit('close')">
          关闭
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import {
  NCard,
  NGrid,
  NGridItem,
  NStatistic,
  NAvatar,
  NTag,
  NIcon,
  NProgress,
  NTimeline,
  NTimelineItem,
  NEmpty,
  NButton,
  NSpace,
  useMessage
} from 'naive-ui'
import {
  TimeOutline,
  LinkOutline,
  EyeOutline,
  FingerPrintOutline,
  TrendingUpOutline,
  BulbOutline,
  AnalyticsOutline,
  DownloadOutline,
  CopyOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface Props {
  share: {
    id: number
    user_id: string
    user_nickname: string
    user_avatar: string
    content_title: string
    content_description: string
    content_url: string
    share_type: string
    platform: string
    view_count: number
    click_count: number
    conversion_count: number
    conversion_rate: number
    shared_at: string
  }
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 引用
const analysisChartRef = ref<HTMLElement>()

// 图表实例
let analysisChart: echarts.ECharts | null = null

// 内容预览
const contentPreview = ref({
  image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=product%20showcase%20banner%20modern%20design&image_size=landscape_16_9'
})

// 浏览轨迹
const viewTracks = ref([
  {
    id: 1,
    viewer_nickname: '王小红',
    action: '查看分享内容',
    duration: 45,
    source: '朋友圈',
    converted: true,
    viewed_at: '2024-01-05T11:00:00Z'
  },
  {
    id: 2,
    viewer_nickname: '李大明',
    action: '点击链接',
    duration: 120,
    source: '朋友圈',
    converted: false,
    viewed_at: '2024-01-05T11:15:00Z'
  },
  {
    id: 3,
    viewer_nickname: '张美丽',
    action: '查看详情',
    duration: 180,
    source: '朋友圈',
    converted: true,
    viewed_at: '2024-01-05T11:30:00Z'
  }
])

// 转化详情
const conversions = ref([
  {
    id: 1,
    user_nickname: '王小红',
    user_avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=young%20woman%20avatar%20profile%20picture&image_size=square',
    conversion_type: '购买',
    conversion_value: 299,
    converted_at: '2024-01-05T11:05:00Z'
  },
  {
    id: 2,
    user_nickname: '张美丽',
    user_avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=middle%20aged%20woman%20avatar%20profile%20picture&image_size=square',
    conversion_type: '注册',
    conversion_value: 0,
    converted_at: '2024-01-05T11:35:00Z'
  }
])

// 优化建议
const recommendations = ref([
  {
    id: 1,
    title: '优化分享标题',
    description: '当前标题吸引力不足，建议增加更具诱惑力的词汇',
    priority: '高'
  },
  {
    id: 2,
    title: '增加分享激励',
    description: '可以考虑为分享者提供一定的奖励或优惠',
    priority: '中'
  },
  {
    id: 3,
    title: '优化落地页',
    description: '提升分享链接指向页面的用户体验和转化率',
    priority: '中'
  }
])

// 方法
const getPlatformLabel = (platform: string) => {
  const platformMap: Record<string, string> = {
    wechat: '微信',
    weibo: '微博',
    qq: 'QQ',
    other: '其他'
  }
  return platformMap[platform] || platform
}

const getPlatformTagType = (platform: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    wechat: 'success',
    weibo: 'warning',
    qq: 'info',
    other: 'default'
  }
  return typeMap[platform] || 'default'
}

const getShareTypeLabel = (shareType: string) => {
  const typeMap: Record<string, string> = {
    moments: '朋友圈',
    group: '群聊',
    private: '私聊',
    official: '公众号'
  }
  return typeMap[shareType] || shareType
}

const getShareTypeTagType = (shareType: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    moments: 'success',
    group: 'info',
    private: 'warning',
    official: 'error'
  }
  return typeMap[shareType] || 'default'
}

const getConversionRateColor = (rate: number) => {
  if (rate >= 20) return '#18a058'
  if (rate >= 10) return '#f0a020'
  return '#d03050'
}

const getPriorityTagType = (priority: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'default'
}

const handleAnalyzeEffect = () => {
  message.info('深度分析功能开发中')
  // TODO: 实现深度分析功能
}

const handleExportReport = () => {
  message.info('导出报告功能开发中')
  // TODO: 实现导出报告功能
}

const handleCreateSimilar = () => {
  message.info('创建相似分享功能开发中')
  // TODO: 实现创建相似分享功能
}

// 图表渲染
const renderAnalysisChart = () => {
  if (!analysisChartRef.value) return
  
  if (!analysisChart) {
    analysisChart = echarts.init(analysisChartRef.value)
  }
  
  const option = {
    title: {
      text: '分享效果趋势',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['浏览量', '点击量', '转化数'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['第1小时', '第2小时', '第3小时', '第4小时', '第5小时', '第6小时']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '浏览量',
        type: 'line',
        data: [20, 45, 78, 120, 145, 156],
        smooth: true,
        itemStyle: { color: '#18a058' }
      },
      {
        name: '点击量',
        type: 'line',
        data: [2, 5, 8, 15, 20, 23],
        smooth: true,
        itemStyle: { color: '#2080f0' }
      },
      {
        name: '转化数',
        type: 'line',
        data: [0, 1, 1, 3, 4, 5],
        smooth: true,
        itemStyle: { color: '#f0a020' }
      }
    ]
  }
  
  analysisChart.setOption(option)
}

// 响应式处理
const handleResize = () => {
  analysisChart?.resize()
}

// 初始化
onMounted(async () => {
  window.addEventListener('resize', handleResize)
  
  await nextTick()
  renderAnalysisChart()
})

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  analysisChart?.dispose()
}

// 组件卸载时清理
const { onBeforeUnmount } = require('vue')
onBeforeUnmount(cleanup)
</script>

<style scoped>
.share-detail-panel {
  padding: 0;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.share-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.share-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.share-content {
  margin-bottom: 24px;
}

.share-content h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
}

.content-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.content-title {
  font-size: 16px;
  font-weight: 500;
}

.content-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.content-url {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 12px;
}

.content-url a {
  color: #2080f0;
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-url a:hover {
  text-decoration: underline;
}

.content-preview img {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  border-radius: 6px;
}

.share-stats {
  margin-bottom: 24px;
}

.share-stats h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.detailed-metrics {
  margin-top: 16px;
}

.metric-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  display: flex;
  flex-direction: column;
}

.rate-value {
  font-size: 18px;
  font-weight: 600;
}

.view-tracks {
  margin-bottom: 24px;
}

.view-tracks h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.tracks-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.track-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.track-user {
  font-weight: 500;
}

.track-time {
  font-size: 12px;
  color: #999;
}

.track-content {
  margin-top: 4px;
}

.track-action {
  font-weight: 500;
  margin-bottom: 4px;
}

.track-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.converted-tag {
  display: flex;
  align-items: center;
}

.no-tracks {
  padding: 20px 0;
}

.conversion-details {
  margin-bottom: 24px;
}

.conversion-details h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.conversions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conversion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.conversion-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.conversion-info {
  display: flex;
  flex-direction: column;
}

.conversion-name {
  font-weight: 500;
}

.conversion-time {
  font-size: 12px;
  color: #666;
}

.conversion-action {
  display: flex;
  align-items: center;
  gap: 8px;
}

.conversion-value {
  font-weight: 600;
  color: #18a058;
}

.effect-analysis {
  margin-bottom: 24px;
}

.effect-analysis h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.analysis-chart {
  height: 300px;
}

.recommendations {
  margin-bottom: 24px;
}

.recommendations h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.recommendation-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.recommendation-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.recommendation-priority {
  display: flex;
  align-items: center;
}

.action-buttons {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>