<template>
  <div class="customer-behavior-analysis">
    <!-- 分析配置 -->
    <div class="analysis-config">
      <div class="config-row">
        <div class="config-item">
          <label>分析维度</label>
          <n-select
            v-model:value="config.dimension"
            :options="dimensionOptions"
            placeholder="选择分析维度"
          />
        </div>
        <div class="config-item">
          <label>时间范围</label>
          <n-date-picker
            v-model:value="config.dateRange"
            type="daterange"
            clearable
          />
        </div>
        <div class="config-item">
          <label>客户分组</label>
          <n-select
            v-model:value="config.groupBy"
            :options="groupOptions"
            placeholder="选择分组方式"
          />
        </div>
        <div class="config-item">
          <label>行为类型</label>
          <n-select
            v-model:value="config.behaviorTypes"
            :options="behaviorTypeOptions"
            placeholder="选择行为类型"
            multiple
            clearable
          />
        </div>
      </div>
      <div class="config-actions">
        <n-button type="primary" @click="runAnalysis" :loading="loading">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          开始分析
        </n-button>
        <n-button @click="exportReport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报告
        </n-button>
      </div>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="!loading">
      <!-- 行为概览 -->
      <div class="behavior-overview">
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#2080f0"><EyeOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ formatNumber(overview.totalViews) }}</div>
            <div class="card-label">总浏览量</div>
            <div class="card-change positive">+{{ overview.viewsGrowth }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#18a058"><TimeOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.avgDuration }}分钟</div>
            <div class="card-label">平均停留时间</div>
            <div class="card-change positive">+{{ overview.durationGrowth }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#f0a020"><MoveOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.avgClicks }}</div>
            <div class="card-label">平均点击次数</div>
            <div class="card-change positive">+{{ overview.clicksGrowth }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#d03050"><ExitOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.bounceRate }}%</div>
            <div class="card-label">跳出率</div>
            <div class="card-change negative">-{{ overview.bounceImprovement }}%</div>
          </div>
        </div>
      </div>

      <!-- 行为趋势分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>行为趋势分析</h3>
          <div class="chart-controls">
            <n-radio-group v-model:value="trendPeriod" size="small">
              <n-radio-button value="7d">近7天</n-radio-button>
              <n-radio-button value="30d">近30天</n-radio-button>
              <n-radio-button value="90d">近90天</n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="chart-container">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </div>

      <!-- 页面行为分析 -->
      <div class="analysis-row">
        <div class="analysis-section">
          <div class="section-header">
            <h3>页面浏览分析</h3>
          </div>
          <div class="chart-container">
            <div ref="pageViewChartRef" class="chart"></div>
          </div>
          <div class="page-stats">
            <div class="stat-item" v-for="page in pageStats" :key="page.path">
              <div class="stat-content">
                <div class="stat-name">{{ page.name }}</div>
                <div class="stat-path">{{ page.path }}</div>
              </div>
              <div class="stat-metrics">
                <div class="metric">
                  <span class="metric-label">浏览量</span>
                  <span class="metric-value">{{ formatNumber(page.views) }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">停留时间</span>
                  <span class="metric-value">{{ page.avgDuration }}分钟</span>
                </div>
                <div class="metric">
                  <span class="metric-label">跳出率</span>
                  <span class="metric-value">{{ page.bounceRate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-section">
          <div class="section-header">
            <h3>用户流向分析</h3>
          </div>
          <div class="flow-analysis">
            <div class="flow-chart">
              <div ref="flowChartRef" class="chart"></div>
            </div>
            <div class="flow-insights">
              <div class="insight-item">
                <div class="insight-title">主要流向</div>
                <div class="insight-content">
                  <div class="flow-path" v-for="path in mainFlows" :key="path.id">
                    <div class="path-route">{{ path.from }} → {{ path.to }}</div>
                    <div class="path-count">{{ path.count }}人</div>
                    <div class="path-rate">{{ path.rate }}%</div>
                  </div>
                </div>
              </div>
              <div class="insight-item">
                <div class="insight-title">流失节点</div>
                <div class="insight-content">
                  <div class="loss-point" v-for="point in lossPoints" :key="point.page">
                    <div class="point-name">{{ point.page }}</div>
                    <div class="point-rate">流失率: {{ point.lossRate }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互行为分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>交互行为分析</h3>
        </div>
        <div class="interaction-analysis">
          <div class="interaction-charts">
            <div class="chart-item">
              <div class="chart-title">点击热力图</div>
              <div class="chart-container">
                <div ref="heatmapChartRef" class="chart"></div>
              </div>
            </div>
            <div class="chart-item">
              <div class="chart-title">滚动深度分析</div>
              <div class="chart-container">
                <div ref="scrollChartRef" class="chart"></div>
              </div>
            </div>
          </div>
          <div class="interaction-insights">
            <div class="insights-grid">
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#2080f0"><MoveOutline /></n-icon>
                  <span>点击行为</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>平均点击次数</span>
                    <span>{{ interactionData.avgClicks }}</span>
                  </div>
                  <div class="metric-row">
                    <span>最热点击区域</span>
                    <span>{{ interactionData.hotClickArea }}</span>
                  </div>
                  <div class="metric-row">
                    <span>点击转化率</span>
                    <span>{{ interactionData.clickConversionRate }}%</span>
                  </div>
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#18a058"><ArrowDownOutline /></n-icon>
                  <span>滚动行为</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>平均滚动深度</span>
                    <span>{{ interactionData.avgScrollDepth }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>完整阅读率</span>
                    <span>{{ interactionData.fullReadRate }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>快速离开率</span>
                    <span>{{ interactionData.quickExitRate }}%</span>
                  </div>
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#f0a020"><DocumentTextOutline /></n-icon>
                  <span>表单行为</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>表单开始率</span>
                    <span>{{ interactionData.formStartRate }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>表单完成率</span>
                    <span>{{ interactionData.formCompletionRate }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>表单放弃率</span>
                    <span>{{ interactionData.formAbandonRate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备和渠道分析 -->
      <div class="analysis-row">
        <div class="analysis-section">
          <div class="section-header">
            <h3>设备使用分析</h3>
          </div>
          <div class="chart-container">
            <div ref="deviceChartRef" class="chart"></div>
          </div>
          <div class="device-stats">
            <div class="device-item" v-for="device in deviceStats" :key="device.type">
              <div class="device-info">
                <div class="device-icon">
                  <n-icon size="20" :color="device.color"><component :is="device.icon" /></n-icon>
                </div>
                <div class="device-content">
                  <div class="device-name">{{ device.name }}</div>
                  <div class="device-percentage">{{ device.percentage }}%</div>
                </div>
              </div>
              <div class="device-metrics">
                <div class="device-metric">
                  <span>平均停留</span>
                  <span>{{ device.avgDuration }}分钟</span>
                </div>
                <div class="device-metric">
                  <span>转化率</span>
                  <span>{{ device.conversionRate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-section">
          <div class="section-header">
            <h3>访问来源分析</h3>
          </div>
          <div class="chart-container">
            <div ref="sourceChartRef" class="chart"></div>
          </div>
          <div class="source-insights">
            <div class="source-item" v-for="source in sourceStats" :key="source.name">
              <div class="source-header">
                <div class="source-name">{{ source.name }}</div>
                <div class="source-percentage">{{ source.percentage }}%</div>
              </div>
              <div class="source-bar">
                <div class="source-fill" :style="{ width: source.percentage + '%', backgroundColor: source.color }"></div>
              </div>
              <div class="source-details">
                <span>访问量: {{ formatNumber(source.visits) }}</span>
                <span>转化率: {{ source.conversionRate }}%</span>
                <span>质量评分: {{ source.qualityScore }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 行为路径分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>用户行为路径</h3>
        </div>
        <div class="path-analysis">
          <div class="path-visualization">
            <div class="path-title">典型用户路径</div>
            <div class="path-flow">
              <div class="path-step" v-for="(step, index) in userPaths" :key="index">
                <div class="step-content">
                  <div class="step-icon">
                    <n-icon :color="step.color"><component :is="step.icon" /></n-icon>
                  </div>
                  <div class="step-info">
                    <div class="step-name">{{ step.name }}</div>
                    <div class="step-count">{{ step.count }}人</div>
                    <div class="step-rate">{{ step.rate }}%</div>
                  </div>
                </div>
                <div class="step-arrow" v-if="index < userPaths.length - 1">
                  <n-icon><ArrowForwardOutline /></n-icon>
                </div>
              </div>
            </div>
          </div>
          <div class="path-insights">
            <div class="path-metrics">
              <div class="metric-card">
                <div class="metric-title">路径完成率</div>
                <div class="metric-value">{{ pathMetrics.completionRate }}%</div>
                <div class="metric-desc">完整走完路径的用户比例</div>
              </div>
              <div class="metric-card">
                <div class="metric-title">平均路径长度</div>
                <div class="metric-value">{{ pathMetrics.avgLength }}步</div>
                <div class="metric-desc">用户平均访问页面数</div>
              </div>
              <div class="metric-card">
                <div class="metric-title">关键节点转化</div>
                <div class="metric-value">{{ pathMetrics.keyNodeConversion }}%</div>
                <div class="metric-desc">关键节点的转化效率</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化建议 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>优化建议</h3>
        </div>
        <div class="recommendations">
          <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
            <div class="recommendation-header">
              <div class="recommendation-icon">
                <n-icon :color="rec.color"><component :is="rec.icon" /></n-icon>
              </div>
              <div class="recommendation-priority" :class="rec.priority">
                {{ getPriorityText(rec.priority) }}
              </div>
            </div>
            <div class="recommendation-content">
              <div class="recommendation-title">{{ rec.title }}</div>
              <div class="recommendation-desc">{{ rec.description }}</div>
              <div class="recommendation-impact">
                <span class="impact-label">预期影响：</span>
                <span class="impact-value">{{ rec.impact }}</span>
              </div>
            </div>
            <div class="recommendation-action">
              <n-button size="small" type="primary" ghost>
                {{ rec.actionText }}
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <n-spin size="large">
        <template #description>
          正在分析客户行为数据，请稍候...
        </template>
      </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NSelect, NDatePicker, NButton, NIcon, NRadioGroup, NRadioButton,
  NSpin, useMessage
} from 'naive-ui'
import {
  AnalyticsOutline, DownloadOutline, EyeOutline, TimeOutline, MoveOutline,
  ExitOutline, ArrowDownOutline, DocumentTextOutline, ArrowForwardOutline,
  DesktopOutline, PhonePortraitOutline, TabletPortraitOutline, BulbOutline,
  AtOutline, AlertCircleOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface AnalysisConfig {
  dimension: string
  dateRange: [number, number] | null
  groupBy: string
  behaviorTypes: string[]
}

const emit = defineEmits<{
  close: []
}>()

const message = useMessage()
const loading = ref(false)
const trendPeriod = ref('30d')

// 分析配置
const config = reactive<AnalysisConfig>({
  dimension: 'page_behavior',
  dateRange: null,
  groupBy: 'page',
  behaviorTypes: []
})

// 配置选项
const dimensionOptions = [
  { label: '页面行为', value: 'page_behavior' },
  { label: '交互行为', value: 'interaction_behavior' },
  { label: '转化行为', value: 'conversion_behavior' },
  { label: '设备行为', value: 'device_behavior' }
]

const groupOptions = [
  { label: '按页面分组', value: 'page' },
  { label: '按设备分组', value: 'device' },
  { label: '按来源分组', value: 'source' },
  { label: '按时间分组', value: 'time' }
]

const behaviorTypeOptions = [
  { label: '页面浏览', value: 'page_view' },
  { label: '点击事件', value: 'click' },
  { label: '滚动行为', value: 'scroll' },
  { label: '表单交互', value: 'form' },
  { label: '文件下载', value: 'download' },
  { label: '视频播放', value: 'video' }
]

// 图表引用
const trendChartRef = ref<HTMLElement>()
const pageViewChartRef = ref<HTMLElement>()
const flowChartRef = ref<HTMLElement>()
const heatmapChartRef = ref<HTMLElement>()
const scrollChartRef = ref<HTMLElement>()
const deviceChartRef = ref<HTMLElement>()
const sourceChartRef = ref<HTMLElement>()

// 分析数据
const overview = ref({
  totalViews: 125847,
  viewsGrowth: 15.8,
  avgDuration: 8.5,
  durationGrowth: 12.3,
  avgClicks: 12.8,
  clicksGrowth: 8.7,
  bounceRate: 28.5,
  bounceImprovement: 5.2
})

const pageStats = ref([
  {
    name: '首页',
    path: '/',
    views: 45230,
    avgDuration: 5.8,
    bounceRate: 32.5
  },
  {
    name: '产品页',
    path: '/products',
    views: 28940,
    avgDuration: 12.3,
    bounceRate: 18.7
  },
  {
    name: '关于我们',
    path: '/about',
    views: 18560,
    avgDuration: 6.2,
    bounceRate: 45.8
  },
  {
    name: '联系我们',
    path: '/contact',
    views: 12890,
    avgDuration: 8.9,
    bounceRate: 25.3
  }
])

const mainFlows = ref([
  { id: '1', from: '首页', to: '产品页', count: 15680, rate: 34.7 },
  { id: '2', from: '产品页', to: '联系页', count: 8940, rate: 30.9 },
  { id: '3', from: '首页', to: '关于页', count: 7230, rate: 16.0 },
  { id: '4', from: '关于页', to: '联系页', count: 4560, rate: 24.6 }
])

const lossPoints = ref([
  { page: '产品详情页', lossRate: 45.8 },
  { page: '价格页面', lossRate: 38.2 },
  { page: '注册页面', lossRate: 32.5 }
])

const interactionData = ref({
  avgClicks: 12.8,
  hotClickArea: '产品介绍区域',
  clickConversionRate: 15.8,
  avgScrollDepth: 68.5,
  fullReadRate: 32.8,
  quickExitRate: 28.5,
  formStartRate: 45.8,
  formCompletionRate: 68.2,
  formAbandonRate: 31.8
})

const deviceStats = ref([
  {
    type: 'desktop',
    name: '桌面端',
    percentage: 58.5,
    avgDuration: 12.8,
    conversionRate: 18.5,
    color: '#2080f0',
    icon: DesktopOutline
  },
  {
    type: 'mobile',
    name: '移动端',
    percentage: 35.2,
    avgDuration: 6.5,
    conversionRate: 12.3,
    color: '#18a058',
    icon: PhonePortraitOutline
  },
  {
    type: 'tablet',
    name: '平板端',
    percentage: 6.3,
    avgDuration: 9.2,
    conversionRate: 15.8,
    color: '#f0a020',
    icon: TabletPortraitOutline
  }
])

const sourceStats = ref([
  {
    name: '直接访问',
    percentage: 42.5,
    visits: 53480,
    conversionRate: 18.5,
    qualityScore: 85,
    color: '#2080f0'
  },
  {
    name: '搜索引擎',
    percentage: 28.3,
    visits: 35620,
    conversionRate: 22.8,
    qualityScore: 92,
    color: '#18a058'
  },
  {
    name: '社交媒体',
    percentage: 18.7,
    visits: 23540,
    conversionRate: 15.2,
    qualityScore: 78,
    color: '#f0a020'
  },
  {
    name: '邮件营销',
    percentage: 10.5,
    visits: 13230,
    conversionRate: 25.6,
    qualityScore: 88,
    color: '#d03050'
  }
])

const userPaths = ref([
  {
    name: '首页访问',
    count: 10000,
    rate: 100,
    icon: EyeOutline,
    color: '#2080f0'
  },
  {
    name: '产品浏览',
    count: 6800,
    rate: 68,
    icon: EyeOutline,
    color: '#18a058'
  },
  {
    name: '详情查看',
    count: 4080,
    rate: 40.8,
    icon: DocumentTextOutline,
    color: '#f0a020'
  },
  {
    name: '联系咨询',
    count: 2040,
    rate: 20.4,
    icon: MoveOutline,
    color: '#d03050'
  },
  {
    name: '成功转化',
    count: 1530,
    rate: 15.3,
    icon: AtOutline,
    color: '#722ed1'
  }
])

const pathMetrics = ref({
  completionRate: 15.3,
  avgLength: 4.2,
  keyNodeConversion: 68.5
})

const recommendations = ref([
  {
    id: '1',
    icon: AtOutline,
    color: '#d03050',
    priority: 'high',
    title: '优化产品详情页',
    description: '产品详情页流失率较高，建议优化页面布局和内容展示',
    impact: '转化率提升8-12%',
    actionText: '立即优化'
  },
  {
    id: '2',
    icon: BulbOutline,
    color: '#f0a020',
    priority: 'high',
    title: '改善移动端体验',
    description: '移动端停留时间和转化率偏低，需要优化移动端用户体验',
    impact: '移动端转化率提升15%',
    actionText: '制定方案'
  },
  {
    id: '3',
    icon: AlertCircleOutline,
    color: '#2080f0',
    priority: 'medium',
    title: '增强表单引导',
    description: '表单放弃率较高，建议增加引导提示和简化填写流程',
    impact: '表单完成率提升20%',
    actionText: '查看详情'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

const runAnalysis = async () => {
  loading.value = true
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    await nextTick()
    initCharts()
    message.success('分析完成')
  } catch (error) {
    message.error('分析失败，请重试')
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  message.info('报告导出功能开发中')
}

const initCharts = () => {
  // 行为趋势图
  if (trendChartRef.value) {
    const chart = echarts.init(trendChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['页面浏览', '点击事件', '表单交互'] },
      xAxis: {
        type: 'category',
        data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '页面浏览',
          type: 'line',
          data: [1200, 1320, 1010, 1340, 900, 2300],
          itemStyle: { color: '#2080f0' }
        },
        {
          name: '点击事件',
          type: 'line',
          data: [820, 932, 901, 934, 1290, 1330],
          itemStyle: { color: '#18a058' }
        },
        {
          name: '表单交互',
          type: 'line',
          data: [150, 232, 201, 154, 190, 330],
          itemStyle: { color: '#f0a020' }
        }
      ]
    })
  }

  // 页面浏览图
  if (pageViewChartRef.value) {
    const chart = echarts.init(pageViewChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: pageStats.value.map(item => item.name)
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: pageStats.value.map(item => item.views),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#2080f0' },
            { offset: 1, color: '#87ceeb' }
          ])
        }
      }]
    })
  }

  // 用户流向图
  if (flowChartRef.value) {
    const chart = echarts.init(flowChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'sankey',
        data: [
          { name: '首页' },
          { name: '产品页' },
          { name: '关于页' },
          { name: '联系页' }
        ],
        links: [
          { source: '首页', target: '产品页', value: 15680 },
          { source: '首页', target: '关于页', value: 7230 },
          { source: '产品页', target: '联系页', value: 8940 },
          { source: '关于页', target: '联系页', value: 4560 }
        ]
      }]
    })
  }

  // 点击热力图
  if (heatmapChartRef.value) {
    const chart = echarts.init(heatmapChartRef.value)
    const hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a', '7a', '8a', '9a', '10a', '11a']
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    const data = []
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 12; j++) {
        data.push([j, i, Math.floor(Math.random() * 100)])
      }
    }
    chart.setOption({
      tooltip: { position: 'top' },
      grid: { height: '50%', top: '10%' },
      xAxis: { type: 'category', data: hours },
      yAxis: { type: 'category', data: days },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        type: 'heatmap',
        data: data,
        label: { show: true }
      }]
    })
  }

  // 滚动深度图
  if (scrollChartRef.value) {
    const chart = echarts.init(scrollChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['0-25%', '25-50%', '50-75%', '75-100%']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [8500, 6200, 4800, 3200],
        itemStyle: { color: '#18a058' }
      }]
    })
  }

  // 设备分布图
  if (deviceChartRef.value) {
    const chart = echarts.init(deviceChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: deviceStats.value.map(item => ({
          value: item.percentage,
          name: item.name,
          itemStyle: { color: item.color }
        }))
      }]
    })
  }

  // 访问来源图
  if (sourceChartRef.value) {
    const chart = echarts.init(sourceChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: sourceStats.value.map(item => ({
          value: item.visits,
          name: item.name,
          itemStyle: { color: item.color }
        }))
      }]
    })
  }
}

onMounted(() => {
  runAnalysis()
})
</script>

<style scoped>
.customer-behavior-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analysis-config {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.config-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.behavior-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.overview-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  align-items: center;
}

.card-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(32, 128, 240, 0.1);
  border-radius: 12px;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.card-change {
  font-size: 12px;
  font-weight: 500;
}

.card-change.positive {
  color: #18a058;
}

.card-change.negative {
  color: #d03050;
}

.analysis-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.chart-container {
  padding: 20px;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.analysis-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.page-stats {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-content {
  flex: 1;
}

.stat-name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-path {
  font-size: 12px;
  color: #666;
}

.stat-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-label {
  font-size: 10px;
  color: #999;
}

.metric-value {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.flow-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  padding: 20px;
}

.flow-chart {
  height: 250px;
}

.flow-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.insight-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.insight-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.flow-path {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.path-route {
  font-size: 12px;
  color: #666;
}

.path-count {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.path-rate {
  font-size: 12px;
  color: #2080f0;
}

.loss-point {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.point-name {
  font-size: 12px;
  color: #666;
}

.point-rate {
  font-size: 12px;
  color: #d03050;
  font-weight: 500;
}

.interaction-analysis {
  padding: 20px;
}

.interaction-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.chart-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.interaction-insights {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.insight-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
}

.insight-header {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.insight-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.metric-row span:first-child {
  color: #666;
}

.metric-row span:last-child {
  font-weight: 500;
  color: #1a1a1a;
}

.device-stats {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.device-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(32, 128, 240, 0.1);
  border-radius: 6px;
}

.device-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.device-name {
  font-weight: 500;
  color: #1a1a1a;
}

.device-percentage {
  font-size: 12px;
  color: #666;
}

.device-metrics {
  display: flex;
  gap: 16px;
}

.device-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.device-metric span:first-child {
  font-size: 10px;
  color: #999;
}

.device-metric span:last-child {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.source-insights {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
}

.source-item {
  margin-bottom: 16px;
}

.source-item:last-child {
  margin-bottom: 0;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.source-name {
  font-weight: 500;
  color: #1a1a1a;
}

.source-percentage {
  font-weight: 600;
  color: #2080f0;
}

.source-bar {
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  margin-bottom: 8px;
  overflow: hidden;
}

.source-fill {
  height: 100%;
  border-radius: 3px;
}

.source-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.path-analysis {
  padding: 20px;
}

.path-visualization {
  margin-bottom: 20px;
}

.path-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.path-flow {
  display: flex;
  gap: 16px;
  align-items: center;
  overflow-x: auto;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.path-step {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 120px;
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  border: 2px solid #e0e0e0;
}

.step-info {
  text-align: center;
}

.step-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.step-count {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.step-rate {
  font-size: 12px;
  color: #2080f0;
  font-weight: 500;
}

.step-arrow {
  color: #666;
  font-size: 16px;
}

.path-insights {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.path-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.metric-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-desc {
  font-size: 10px;
  color: #999;
  line-height: 1.4;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.recommendation-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.recommendation-priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

.recommendation-priority.high {
  background: #fee;
  color: #d03050;
}

.recommendation-priority.medium {
  background: #fff7e6;
  color: #f0a020;
}

.recommendation-priority.low {
  background: #f0f9ff;
  color: #2080f0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.recommendation-desc {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.recommendation-impact {
  display: flex;
  gap: 4px;
  align-items: center;
}

.impact-label {
  font-size: 10px;
  color: #999;
}

.impact-value {
  font-size: 10px;
  font-weight: 500;
  color: #18a058;
}

.recommendation-action {
  display: flex;
  align-items: flex-start;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>