"""基础CRUD操作服务

提供通用的数据库CRUD操作方法
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from uuid import uuid4

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.models.base import BaseModel as DBBaseModel

ModelType = TypeVar("ModelType", bound=DBBaseModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础CRUD操作类"""
    
    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD对象
        
        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
    
    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """
        根据ID获取单个记录
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            模型实例或None
        """
        result = await db.execute(select(self.model).where(self.model.id == id))
        return result.scalar_one_or_none()
    
    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        获取多个记录
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数
            filters: 过滤条件
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            模型实例列表
        """
        query = select(self.model)
        
        # 应用过滤条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        conditions.append(getattr(self.model, key).in_(value))
                    elif isinstance(value, dict):
                        # 支持范围查询
                        if 'gte' in value:
                            conditions.append(getattr(self.model, key) >= value['gte'])
                        if 'lte' in value:
                            conditions.append(getattr(self.model, key) <= value['lte'])
                        if 'like' in value:
                            conditions.append(getattr(self.model, key).like(f"%{value['like']}%"))
                    else:
                        conditions.append(getattr(self.model, key) == value)
            
            if conditions:
                query = query.where(and_(*conditions))
        
        # 应用排序
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        else:
            # 默认按创建时间降序
            if hasattr(self.model, 'created_at'):
                query = query.order_by(desc(self.model.created_at))
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count(
        self,
        db: AsyncSession,
        *,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        统计记录数量
        
        Args:
            db: 数据库会话
            filters: 过滤条件
            
        Returns:
            记录数量
        """
        query = select(func.count(self.model.id))
        
        # 应用过滤条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        conditions.append(getattr(self.model, key).in_(value))
                    elif isinstance(value, dict):
                        if 'gte' in value:
                            conditions.append(getattr(self.model, key) >= value['gte'])
                        if 'lte' in value:
                            conditions.append(getattr(self.model, key) <= value['lte'])
                        if 'like' in value:
                            conditions.append(getattr(self.model, key).like(f"%{value['like']}%"))
                    else:
                        conditions.append(getattr(self.model, key) == value)
            
            if conditions:
                query = query.where(and_(*conditions))
        
        result = await db.execute(query)
        return result.scalar()
    
    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """
        创建新记录
        
        Args:
            db: 数据库会话
            obj_in: 创建数据
            
        Returns:
            创建的模型实例
        """
        obj_in_data = jsonable_encoder(obj_in)
        
        # 如果模型有id字段且未提供，则生成UUID
        if hasattr(self.model, 'id') and 'id' not in obj_in_data:
            obj_in_data['id'] = str(uuid4())
        
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新记录
        
        Args:
            db: 数据库会话
            db_obj: 数据库对象
            obj_in: 更新数据
            
        Returns:
            更新后的模型实例
        """
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, *, id: Any) -> Optional[ModelType]:
        """
        删除记录
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            删除的模型实例或None
        """
        obj = await self.get(db=db, id=id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj
    
    async def soft_delete(self, db: AsyncSession, *, id: Any) -> Optional[ModelType]:
        """
        软删除记录（如果模型支持）
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            软删除的模型实例或None
        """
        obj = await self.get(db=db, id=id)
        if obj and hasattr(obj, 'is_deleted'):
            obj.is_deleted = True
            db.add(obj)
            await db.commit()
            await db.refresh(obj)
        return obj
    
    async def get_by_field(
        self,
        db: AsyncSession,
        *,
        field: str,
        value: Any
    ) -> Optional[ModelType]:
        """
        根据指定字段获取记录
        
        Args:
            db: 数据库会话
            field: 字段名
            value: 字段值
            
        Returns:
            模型实例或None
        """
        if not hasattr(self.model, field):
            return None
        
        result = await db.execute(
            select(self.model).where(getattr(self.model, field) == value)
        )
        return result.scalar_one_or_none()
    
    async def get_multi_by_field(
        self,
        db: AsyncSession,
        *,
        field: str,
        value: Any,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """
        根据指定字段获取多个记录
        
        Args:
            db: 数据库会话
            field: 字段名
            value: 字段值
            skip: 跳过记录数
            limit: 限制记录数
            
        Returns:
            模型实例列表
        """
        if not hasattr(self.model, field):
            return []
        
        query = select(self.model).where(getattr(self.model, field) == value)
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def exists(self, db: AsyncSession, *, id: Any) -> bool:
        """
        检查记录是否存在
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            是否存在
        """
        result = await db.execute(
            select(func.count(self.model.id)).where(self.model.id == id)
        )
        return result.scalar() > 0
    
    async def bulk_create(
        self,
        db: AsyncSession,
        *,
        objs_in: List[CreateSchemaType]
    ) -> List[ModelType]:
        """
        批量创建记录
        
        Args:
            db: 数据库会话
            objs_in: 创建数据列表
            
        Returns:
            创建的模型实例列表
        """
        db_objs = []
        for obj_in in objs_in:
            obj_in_data = jsonable_encoder(obj_in)
            
            # 如果模型有id字段且未提供，则生成UUID
            if hasattr(self.model, 'id') and 'id' not in obj_in_data:
                obj_in_data['id'] = str(uuid4())
            
            db_obj = self.model(**obj_in_data)
            db_objs.append(db_obj)
        
        db.add_all(db_objs)
        await db.commit()
        
        # 刷新所有对象
        for db_obj in db_objs:
            await db.refresh(db_obj)
        
        return db_objs