import { createClient } from '@supabase/supabase-js'

// 数据验证规则接口
interface ValidationRule {
  field: string
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'email' | 'phone' | 'date' | 'json'
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  enum?: string[]
  custom?: (value: any) => boolean | string
}

// 验证结果接口
interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// 数据验证器类
export class DataValidator {
  private static phonePattern = /^1[3-9]\d{9}$/
  private static emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  private static datePattern = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/

  // 客户数据验证规则
  private static customerRules: ValidationRule[] = [
    { field: 'name', required: true, type: 'string', minLength: 1, maxLength: 100 },
    { field: 'phone', required: true, type: 'phone' },
    { field: 'email', type: 'email' },
    { field: 'company', type: 'string', maxLength: 200 },
    { field: 'position', type: 'string', maxLength: 100 },
    { field: 'wechat_id', type: 'string', maxLength: 50 },
    { field: 'address', type: 'string', maxLength: 500 },
    { field: 'source', type: 'string', enum: ['website', 'referral', 'advertisement', 'social_media', 'cold_call', 'exhibition', 'other', 'unknown'] },
    { field: 'status', type: 'string', enum: ['potential', 'contacted', 'converted', 'lost'] },
    { field: 'level', type: 'string', enum: ['A', 'B', 'C', 'D'] },
    { field: 'gender', type: 'string', enum: ['male', 'female', 'other'] },
    { field: 'is_vip', type: 'boolean' },
    { field: 'is_high_value', type: 'boolean' },
    { field: 'tags', type: 'json' },
    { field: 'region', type: 'json' },
    { field: 'notes', type: 'string', maxLength: 1000 },
    { field: 'deal_amount', type: 'number', custom: (value) => value >= 0 || '成交金额不能为负数' },
    { field: 'deal_probability', type: 'number', custom: (value) => (value >= 0 && value <= 100) || '成交概率必须在0-100之间' },
    { field: 'follow_count', type: 'number', custom: (value) => value >= 0 || '跟进次数不能为负数' },
    { field: 'created_at', type: 'date' },
    { field: 'updated_at', type: 'date' }
  ]

  // 营销活动验证规则
  private static campaignRules: ValidationRule[] = [
    { field: 'name', required: true, type: 'string', minLength: 1, maxLength: 200 },
    { field: 'type', required: true, type: 'string', enum: ['flash_sale', 'lottery', 'share', 'coupon', 'points_exchange', 'other'] },
    { field: 'description', type: 'string', maxLength: 1000 },
    { field: 'status', type: 'string', enum: ['draft', 'active', 'paused', 'completed', 'cancelled'] },
    { field: 'start_date', type: 'date' },
    { field: 'end_date', type: 'date' },
    { field: 'target_audience', type: 'string', enum: ['all', 'vip', 'high_value', 'new', 'active', 'inactive'] },
    { field: 'budget', type: 'number', custom: (value) => value >= 0 || '预算不能为负数' },
    { field: 'participants_count', type: 'number', custom: (value) => value >= 0 || '参与人数不能为负数' },
    { field: 'conversion_rate', type: 'number', custom: (value) => (value >= 0 && value <= 100) || '转化率必须在0-100之间' },
    { field: 'config', type: 'json' },
    { field: 'created_at', type: 'date' },
    { field: 'updated_at', type: 'date' }
  ]

  // 跟进记录验证规则
  private static followRecordRules: ValidationRule[] = [
    { field: 'customer_id', required: true, type: 'string' },
    { field: 'user_id', required: true, type: 'string' },
    { field: 'contact_type', required: true, type: 'string', enum: ['phone', 'meeting', 'wechat', 'email', 'visit', 'other'] },
    { field: 'stage', type: 'string', enum: ['initial', 'follow_up', 'negotiation', 'closing', 'completed'] },
    { field: 'content', required: true, type: 'string', minLength: 1, maxLength: 2000 },
    { field: 'result', type: 'string', maxLength: 500 },
    { field: 'result_detail', type: 'string', maxLength: 1000 },
    { field: 'has_next_plan', type: 'boolean' },
    { field: 'next_content', type: 'string', maxLength: 1000 },
    { field: 'duration', type: 'number', custom: (value) => value > 0 || '持续时间必须大于0' },
    { field: 'location', type: 'string', maxLength: 200 },
    { field: 'created_at', type: 'date' },
    { field: 'updated_at', type: 'date' }
  ]

  // 活动参与者验证规则
  private static participantRules: ValidationRule[] = [
    { field: 'campaign_id', required: true, type: 'string' },
    { field: 'customer_id', required: true, type: 'string' },
    { field: 'customer_name', required: true, type: 'string', minLength: 1, maxLength: 100 },
    { field: 'customer_phone', required: true, type: 'phone' },
    { field: 'customer_wechat', type: 'string', maxLength: 50 },
    { field: 'participation_time', required: true, type: 'date' },
    { field: 'status', type: 'string', enum: ['active', 'completed', 'cancelled'] },
    { field: 'result', type: 'string', maxLength: 500 },
    { field: 'reward', type: 'string', maxLength: 200 },
    { field: 'notes', type: 'string', maxLength: 1000 },
    { field: 'created_at', type: 'date' },
    { field: 'updated_at', type: 'date' }
  ]

  // 验证单个字段
  private static validateField(value: any, rule: ValidationRule): string[] {
    const errors: string[] = []
    const { field, required, type, minLength, maxLength, pattern, enum: enumValues, custom } = rule

    // 检查必填字段
    if (required && (value === null || value === undefined || value === '')) {
      errors.push(`${field} 是必填字段`)
      return errors
    }

    // 如果值为空且非必填，跳过其他验证
    if (value === null || value === undefined || value === '') {
      return errors
    }

    // 类型验证
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          errors.push(`${field} 必须是字符串类型`)
        }
        break
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          errors.push(`${field} 必须是有效数字`)
        }
        break
      case 'boolean':
        if (typeof value !== 'boolean') {
          errors.push(`${field} 必须是布尔类型`)
        }
        break
      case 'email':
        if (typeof value === 'string' && !this.emailPattern.test(value)) {
          errors.push(`${field} 邮箱格式不正确`)
        }
        break
      case 'phone':
        if (typeof value === 'string' && !this.phonePattern.test(value)) {
          errors.push(`${field} 手机号格式不正确（应为11位数字）`)
        }
        break
      case 'date':
        if (typeof value === 'string' && !this.datePattern.test(value)) {
          errors.push(`${field} 日期格式不正确`)
        }
        break
      case 'json':
        if (typeof value !== 'object') {
          errors.push(`${field} 必须是对象类型`)
        }
        break
    }

    // 长度验证
    if (typeof value === 'string') {
      if (minLength !== undefined && value.length < minLength) {
        errors.push(`${field} 长度不能少于 ${minLength} 个字符`)
      }
      if (maxLength !== undefined && value.length > maxLength) {
        errors.push(`${field} 长度不能超过 ${maxLength} 个字符`)
      }
    }

    // 正则表达式验证
    if (pattern && typeof value === 'string' && !pattern.test(value)) {
      errors.push(`${field} 格式不符合要求`)
    }

    // 枚举值验证
    if (enumValues && !enumValues.includes(value)) {
      errors.push(`${field} 值必须是以下之一: ${enumValues.join(', ')}`)
    }

    // 自定义验证
    if (custom) {
      const result = custom(value)
      if (result !== true) {
        errors.push(typeof result === 'string' ? result : `${field} 自定义验证失败`)
      }
    }

    return errors
  }

  // 验证数据对象
  private static validateData(data: any, rules: ValidationRule[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    for (const rule of rules) {
      const fieldErrors = this.validateField(data[rule.field], rule)
      errors.push(...fieldErrors)
    }

    // 检查未知字段
    const knownFields = rules.map(rule => rule.field)
    const unknownFields = Object.keys(data).filter(field => !knownFields.includes(field))
    if (unknownFields.length > 0) {
      warnings.push(`发现未知字段: ${unknownFields.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // 验证客户数据
  static validateCustomer(customer: any): ValidationResult {
    return this.validateData(customer, this.customerRules)
  }

  // 验证营销活动数据
  static validateCampaign(campaign: any): ValidationResult {
    return this.validateData(campaign, this.campaignRules)
  }

  // 验证跟进记录数据
  static validateFollowRecord(record: any): ValidationResult {
    return this.validateData(record, this.followRecordRules)
  }

  // 验证活动参与者数据
  static validateParticipant(participant: any): ValidationResult {
    return this.validateData(participant, this.participantRules)
  }

  // 批量验证数据
  static validateBatch<T>(data: T[], validator: (item: T) => ValidationResult): {
    validData: T[]
    invalidData: { item: T, errors: string[], warnings: string[] }[]
    summary: { total: number, valid: number, invalid: number }
  } {
    const validData: T[] = []
    const invalidData: { item: T, errors: string[], warnings: string[] }[] = []

    for (const item of data) {
      const result = validator(item)
      if (result.isValid) {
        validData.push(item)
      } else {
        invalidData.push({
          item,
          errors: result.errors,
          warnings: result.warnings
        })
      }
    }

    return {
      validData,
      invalidData,
      summary: {
        total: data.length,
        valid: validData.length,
        invalid: invalidData.length
      }
    }
  }
}

// 错误处理器类
export class ErrorHandler {
  private errors: Array<{ type: string, message: string, data?: any, timestamp: Date }> = []
  private warnings: Array<{ type: string, message: string, data?: any, timestamp: Date }> = []

  // 记录错误
  logError(type: string, message: string, data?: any) {
    const error = {
      type,
      message,
      data,
      timestamp: new Date()
    }
    this.errors.push(error)
    console.error(`❌ [${type}] ${message}`, data ? data : '')
  }

  // 记录警告
  logWarning(type: string, message: string, data?: any) {
    const warning = {
      type,
      message,
      data,
      timestamp: new Date()
    }
    this.warnings.push(warning)
    console.warn(`⚠️  [${type}] ${message}`, data ? data : '')
  }

  // 记录成功
  logSuccess(type: string, message: string, data?: any) {
    console.log(`✅ [${type}] ${message}`, data ? data : '')
  }

  // 记录信息
  logInfo(type: string, message: string, data?: any) {
    console.log(`ℹ️  [${type}] ${message}`, data ? data : '')
  }

  // 获取错误统计
  getErrorStats() {
    const errorsByType = this.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const warningsByType = this.warnings.reduce((acc, warning) => {
      acc[warning.type] = (acc[warning.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      totalErrors: this.errors.length,
      totalWarnings: this.warnings.length,
      errorsByType,
      warningsByType
    }
  }

  // 生成错误报告
  generateReport(): string {
    const stats = this.getErrorStats()
    let report = '\n📊 错误处理报告\n'
    report += '='.repeat(50) + '\n'
    report += `总错误数: ${stats.totalErrors}\n`
    report += `总警告数: ${stats.totalWarnings}\n\n`

    if (Object.keys(stats.errorsByType).length > 0) {
      report += '❌ 错误分类统计:\n'
      Object.entries(stats.errorsByType).forEach(([type, count]) => {
        report += `  ${type}: ${count} 个\n`
      })
      report += '\n'
    }

    if (Object.keys(stats.warningsByType).length > 0) {
      report += '⚠️  警告分类统计:\n'
      Object.entries(stats.warningsByType).forEach(([type, count]) => {
        report += `  ${type}: ${count} 个\n`
      })
      report += '\n'
    }

    if (this.errors.length > 0) {
      report += '❌ 详细错误列表:\n'
      this.errors.slice(0, 10).forEach((error, index) => {
        report += `  ${index + 1}. [${error.type}] ${error.message}\n`
      })
      if (this.errors.length > 10) {
        report += `  ... 还有 ${this.errors.length - 10} 个错误\n`
      }
      report += '\n'
    }

    return report
  }

  // 清空错误记录
  clear() {
    this.errors = []
    this.warnings = []
  }

  // 检查是否有错误
  hasErrors(): boolean {
    return this.errors.length > 0
  }

  // 检查是否有警告
  hasWarnings(): boolean {
    return this.warnings.length > 0
  }
}

// 数据完整性检查器
export class DataIntegrityChecker {
  private supabase: any

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  // 检查外键完整性
  async checkForeignKeyIntegrity(): Promise<{
    isValid: boolean
    issues: string[]
  }> {
    const issues: string[] = []

    try {
      // 检查客户跟进记录的客户ID是否存在
      const { data: orphanedFollowRecords } = await this.supabase
        .from('customer_follow_records')
        .select('id, customer_id')
        .not('customer_id', 'in', 
          this.supabase
            .from('customers')
            .select('id')
        )

      if (orphanedFollowRecords && orphanedFollowRecords.length > 0) {
        issues.push(`发现 ${orphanedFollowRecords.length} 条跟进记录的客户ID不存在`)
      }

      // 检查活动参与者的活动ID是否存在
      const { data: orphanedParticipants } = await this.supabase
        .from('campaign_participants')
        .select('id, campaign_id')
        .not('campaign_id', 'in',
          this.supabase
            .from('marketing_campaigns')
            .select('id')
        )

      if (orphanedParticipants && orphanedParticipants.length > 0) {
        issues.push(`发现 ${orphanedParticipants.length} 条参与记录的活动ID不存在`)
      }

      // 检查会议记录的客户ID是否存在
      const { data: orphanedMeetings } = await this.supabase
        .from('meetings')
        .select('id, customer_id')
        .not('customer_id', 'in',
          this.supabase
            .from('customers')
            .select('id')
        )

      if (orphanedMeetings && orphanedMeetings.length > 0) {
        issues.push(`发现 ${orphanedMeetings.length} 条会议记录的客户ID不存在`)
      }

    } catch (error) {
      issues.push(`外键完整性检查失败: ${error}`)
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  // 检查数据一致性
  async checkDataConsistency(): Promise<{
    isValid: boolean
    issues: string[]
  }> {
    const issues: string[] = []

    try {
      // 检查客户的跟进次数是否与实际跟进记录数量一致
      const { data: customerFollowCounts } = await this.supabase
        .from('customers')
        .select(`
          id,
          name,
          follow_count,
          customer_follow_records(count)
        `)

      if (customerFollowCounts) {
        for (const customer of customerFollowCounts) {
          const actualCount = customer.customer_follow_records[0]?.count || 0
          if (customer.follow_count !== actualCount) {
            issues.push(`客户 ${customer.name} 的跟进次数不一致: 记录为 ${customer.follow_count}，实际为 ${actualCount}`)
          }
        }
      }

      // 检查营销活动的参与人数是否与实际参与记录数量一致
      const { data: campaignParticipantCounts } = await this.supabase
        .from('marketing_campaigns')
        .select(`
          id,
          name,
          participants_count,
          campaign_participants(count)
        `)

      if (campaignParticipantCounts) {
        for (const campaign of campaignParticipantCounts) {
          const actualCount = campaign.campaign_participants[0]?.count || 0
          if (campaign.participants_count !== actualCount) {
            issues.push(`营销活动 ${campaign.name} 的参与人数不一致: 记录为 ${campaign.participants_count}，实际为 ${actualCount}`)
          }
        }
      }

    } catch (error) {
      issues.push(`数据一致性检查失败: ${error}`)
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  // 检查重复数据
  async checkDuplicateData(): Promise<{
    isValid: boolean
    issues: string[]
  }> {
    const issues: string[] = []

    try {
      // 检查重复的客户手机号
      const { data: duplicatePhones } = await this.supabase
        .from('customers')
        .select('phone, count(*)')
        .group('phone')
        .having('count(*)', 'gt', 1)

      if (duplicatePhones && duplicatePhones.length > 0) {
        issues.push(`发现 ${duplicatePhones.length} 个重复的客户手机号`)
      }

      // 检查重复的营销活动名称
      const { data: duplicateNames } = await this.supabase
        .from('marketing_campaigns')
        .select('name, count(*)')
        .group('name')
        .having('count(*)', 'gt', 1)

      if (duplicateNames && duplicateNames.length > 0) {
        issues.push(`发现 ${duplicateNames.length} 个重复的营销活动名称`)
      }

    } catch (error) {
      issues.push(`重复数据检查失败: ${error}`)
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  // 执行完整的数据完整性检查
  async performFullCheck(): Promise<{
    isValid: boolean
    foreignKeyIssues: string[]
    consistencyIssues: string[]
    duplicateIssues: string[]
    summary: string
  }> {
    console.log('\n🔍 开始数据完整性检查...')

    const foreignKeyCheck = await this.checkForeignKeyIntegrity()
    const consistencyCheck = await this.checkDataConsistency()
    const duplicateCheck = await this.checkDuplicateData()

    const isValid = foreignKeyCheck.isValid && consistencyCheck.isValid && duplicateCheck.isValid
    const totalIssues = foreignKeyCheck.issues.length + consistencyCheck.issues.length + duplicateCheck.issues.length

    let summary = '\n📋 数据完整性检查报告\n'
    summary += '='.repeat(50) + '\n'
    summary += `外键完整性: ${foreignKeyCheck.isValid ? '✅ 通过' : '❌ 失败'}\n`
    summary += `数据一致性: ${consistencyCheck.isValid ? '✅ 通过' : '❌ 失败'}\n`
    summary += `重复数据检查: ${duplicateCheck.isValid ? '✅ 通过' : '❌ 失败'}\n`
    summary += `总问题数: ${totalIssues}\n`

    if (totalIssues === 0) {
      summary += '\n🎉 数据完整性检查全部通过！\n'
    }

    console.log(summary)

    return {
      isValid,
      foreignKeyIssues: foreignKeyCheck.issues,
      consistencyIssues: consistencyCheck.issues,
      duplicateIssues: duplicateCheck.issues,
      summary
    }
  }
}