import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// MySQL连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'workchat_admin',
  charset: 'utf8mb4'
};

// 测试客户数据
const testCustomers = [
  {
    id: uuidv4(),
    name: '张三',
    phone: '13800138001',
    email: 'zhang<PERSON>@example.com',
    company: '阿里巴巴集团',
    position: '产品经理',
    region: '杭州市',
    gender: 'male',
    age: 28,
    source: 'website',
    level: 'A',
    address: '浙江省杭州市西湖区文三路269号',
    decoration_type: 'full',
    house_status: 'new',
    budget_range: '10-20万',
    contact_time: 'morning',
    tags: JSON.stringify(['VIP客户', '高意向']),
    notes: '对智能家居产品很感兴趣，预算充足',
    status: 'active',
    last_contact_at: new Date('2024-01-15 10:30:00'),
    next_follow_up_at: new Date('2024-01-20 14:00:00')
  },
  {
    id: uuidv4(),
    name: '李四',
    phone: '13800138002',
    email: '<EMAIL>',
    company: '腾讯科技',
    position: '技术总监',
    region: '深圳市',
    gender: 'male',
    age: 35,
    source: 'referral',
    level: 'B',
    address: '广东省深圳市南山区科技园',
    decoration_type: 'partial',
    house_status: 'existing',
    budget_range: '15-30万',
    contact_time: 'afternoon',
    tags: JSON.stringify(['技术专家', '理性消费']),
    notes: '注重产品技术参数和性价比',
    status: 'active',
    last_contact_at: new Date('2024-01-16 15:20:00'),
    next_follow_up_at: new Date('2024-01-22 10:00:00')
  },
  {
    id: uuidv4(),
    name: '王美丽',
    phone: '13800138003',
    email: '<EMAIL>',
    company: '字节跳动',
    position: '设计师',
    region: '北京市',
    gender: 'female',
    age: 26,
    source: 'social_media',
    level: 'A',
    address: '北京市朝阳区望京SOHO',
    decoration_type: 'luxury',
    house_status: 'new',
    budget_range: '20-50万',
    contact_time: 'evening',
    tags: JSON.stringify(['设计师', '追求品质', '年轻群体']),
    notes: '对产品外观设计要求很高，喜欢简约风格',
    status: 'active',
    last_contact_at: new Date('2024-01-17 19:45:00'),
    next_follow_up_at: new Date('2024-01-25 16:30:00')
  },
  {
    id: uuidv4(),
    name: '陈建国',
    phone: '13800138004',
    email: '<EMAIL>',
    company: '华为技术',
    position: '销售总监',
    region: '上海市',
    gender: 'male',
    age: 42,
    source: 'exhibition',
    level: 'B',
    address: '上海市浦东新区张江高科技园区',
    decoration_type: 'full',
    house_status: 'renovation',
    budget_range: '30-50万',
    contact_time: 'morning',
    tags: JSON.stringify(['企业高管', '决策者']),
    notes: '家庭装修项目负责人，需要整体解决方案',
    status: 'active',
    last_contact_at: new Date('2024-01-18 09:15:00'),
    next_follow_up_at: new Date('2024-01-23 11:00:00')
  },
  {
    id: uuidv4(),
    name: '刘小红',
    phone: '13800138005',
    email: '<EMAIL>',
    company: '百度在线',
    position: '市场专员',
    region: '广州市',
    gender: 'female',
    age: 29,
    source: 'advertisement',
    level: 'C',
    address: '广东省广州市天河区珠江新城',
    decoration_type: 'simple',
    house_status: 'existing',
    budget_range: '5-10万',
    contact_time: 'afternoon',
    tags: JSON.stringify(['首次购买', '价格敏感']),
    notes: '刚需客户，对价格比较敏感，需要性价比高的产品',
    status: 'active',
    last_contact_at: new Date('2024-01-19 14:30:00'),
    next_follow_up_at: new Date('2024-01-26 15:00:00')
  }
];

async function createTestCustomers() {
  console.log('🚀 开始创建客户测试数据...');
  
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(mysqlConfig);
    console.log('✅ MySQL数据库连接成功');
    
    // 检查customers表是否存在
    const [tables] = await connection.execute(
      "SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customers'",
      [mysqlConfig.database]
    );
    
    if ((tables as any[]).length === 0) {
      console.log('❌ customers表不存在，请先创建数据库表结构');
      return;
    }
    
    console.log('📋 customers表存在，开始插入测试数据...');
    
    // 清空现有测试数据（可选）
    console.log('🧹 清理现有测试数据...');
    await connection.execute(
      "DELETE FROM customers WHERE phone IN ('13800138001', '13800138002', '13800138003', '13800138004', '13800138005')"
    );
    
    // 插入测试数据
    const insertSQL = `
      INSERT INTO customers (
        id, name, phone, email, company, position, region, gender, age, source, level,
        address, decoration_type, house_status, budget_range, contact_time, tags, notes,
        status, last_contact_at, next_follow_up_at, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    let successCount = 0;
    
    for (const customer of testCustomers) {
      try {
        await connection.execute(insertSQL, [
          customer.id,
          customer.name,
          customer.phone,
          customer.email,
          customer.company,
          customer.position,
          customer.region,
          customer.gender,
          customer.age,
          customer.source,
          customer.level,
          customer.address,
          customer.decoration_type,
          customer.house_status,
          customer.budget_range,
          customer.contact_time,
          customer.tags,
          customer.notes,
          customer.status,
          customer.last_contact_at,
          customer.next_follow_up_at
        ]);
        
        successCount++;
        console.log(`✅ 成功插入客户: ${customer.name} (${customer.phone})`);
      } catch (error) {
        console.error(`❌ 插入客户 ${customer.name} 失败:`, error);
      }
    }
    
    console.log(`\n🎉 测试数据创建完成！`);
    console.log(`📊 成功插入 ${successCount}/${testCustomers.length} 条客户记录`);
    
    // 验证插入结果
    const [result] = await connection.execute(
      "SELECT COUNT(*) as count FROM customers WHERE phone LIKE '13800138%'"
    );
    
    const count = (result as any[])[0].count;
    console.log(`🔍 验证结果: 数据库中共有 ${count} 条测试客户记录`);
    
    // 显示插入的客户信息
    const [customers] = await connection.execute(
      "SELECT name, phone, company, position, region, gender FROM customers WHERE phone LIKE '13800138%' ORDER BY created_at DESC"
    );
    
    console.log('\n📋 插入的客户列表:');
    (customers as any[]).forEach((customer, index) => {
      console.log(`${index + 1}. ${customer.name} - ${customer.phone} - ${customer.company} - ${customer.position} - ${customer.region} - ${customer.gender}`);
    });
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行脚本
createTestCustomers()
  .then(() => {
    console.log('\n✨ 客户测试数据创建脚本执行完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error);
    process.exit(1);
  });

export { createTestCustomers };