{"migrationId": "2a0abec6-388c-4f5f-938c-7636db846b3f", "timestamp": "2025-08-18T06:51:06.345Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 14, "failedTables": 7, "totalRecords": 165, "migratedRecords": 107, "failedRecords": 58}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T06:50:28.002Z", "errors": [], "endTime": "2025-08-18T06:50:31.792Z", "duration": 3790}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:50:31.797Z", "errors": [], "endTime": "2025-08-18T06:50:33.586Z", "duration": 1789}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T06:50:33.588Z", "errors": [], "endTime": "2025-08-18T06:50:35.535Z", "duration": 1947}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:50:35.538Z", "errors": [], "endTime": "2025-08-18T06:50:38.820Z", "duration": 3282}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:38.824Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:50:39.238Z", "errors": [], "endTime": "2025-08-18T06:50:41.115Z", "duration": 1877}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:50:41.118Z", "errors": ["Field 'name' doesn't have a default value"], "endTime": "2025-08-18T06:50:45.041Z", "duration": 3923}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:50:45.043Z", "errors": ["Invalid JSON text: \"Invalid value.\" at position 0 in value for column 'customers.tags'."], "endTime": "2025-08-18T06:50:47.258Z", "duration": 2215}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:47.261Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:50:47.742Z", "errors": ["Unknown column 'prizes' in 'field list'"], "endTime": "2025-08-18T06:50:50.008Z", "duration": 2266}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:50.011Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:50.505Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:50:51.535Z", "errors": ["Unknown column 'materials' in 'field list'"], "endTime": "2025-08-18T06:50:55.774Z", "duration": 4239}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:55.777Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:50:56.174Z", "errors": ["Unknown column 'applies_to_departments' in 'field list'"], "endTime": "2025-08-18T06:50:57.821Z", "duration": 1647}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:57.824Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:58.099Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:50:58.898Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:51:00.017Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:51:00.840Z", "errors": ["Unknown column 'next_action' in 'field list'"], "endTime": "2025-08-18T06:51:04.211Z", "duration": 3371}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:51:04.212Z", "errors": ["Unknown column 'claimed_at' in 'field list'"], "endTime": "2025-08-18T06:51:06.342Z", "duration": 2130}], "logs": [{"id": "ae385b1b-14e6-4505-9e25-5a8f73aca5c0", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T06:50:28.002Z", "end_time": "2025-08-18T06:50:31.792Z", "duration_ms": 3790}, {"id": "bb5092aa-c0ca-415f-866a-a94a35fb8ec0", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:50:31.797Z", "end_time": "2025-08-18T06:50:33.586Z", "duration_ms": 1789}, {"id": "c4989e41-4d86-4750-851e-f9c2bca62ec7", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T06:50:33.588Z", "end_time": "2025-08-18T06:50:35.535Z", "duration_ms": 1947}, {"id": "bf758400-47ec-450f-9952-b83b455728ea", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:50:35.538Z", "end_time": "2025-08-18T06:50:38.820Z", "duration_ms": 3282}, {"id": "a7314d98-a227-403f-9ee9-fdfe2fa55033", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:38.824Z", "end_time": "2025-08-18T06:50:39.238Z", "duration_ms": 414}, {"id": "5aa78a60-e4a9-4297-9122-6f10fb9f9ee2", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:50:39.238Z", "end_time": "2025-08-18T06:50:41.115Z", "duration_ms": 1877}, {"id": "e53a0e7d-5907-4d71-ba65-fed6440107b9", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:41.118Z", "end_time": "2025-08-18T06:50:45.041Z", "duration_ms": 3923}, {"id": "80fd717a-e76b-445a-b957-c65efd45da85", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:45.043Z", "end_time": "2025-08-18T06:50:47.258Z", "duration_ms": 2215}, {"id": "6fa7ce15-f3db-452c-96a8-e6514a0f6b12", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:47.261Z", "end_time": "2025-08-18T06:50:47.742Z", "duration_ms": 481}, {"id": "a4910824-7772-420f-b6f8-bc159f60ae48", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:47.742Z", "end_time": "2025-08-18T06:50:50.008Z", "duration_ms": 2266}, {"id": "8ebf57b9-ad1d-4503-ac1f-073eb8b4d0cd", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:50.011Z", "end_time": "2025-08-18T06:50:50.505Z", "duration_ms": 494}, {"id": "13da5758-23c7-405f-86d8-cf679cbfa8cd", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:50.505Z", "end_time": "2025-08-18T06:50:51.534Z", "duration_ms": 1029}, {"id": "43832d82-f207-4f57-a3d5-34e2a1b30d3a", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:51.535Z", "end_time": "2025-08-18T06:50:55.774Z", "duration_ms": 4239}, {"id": "ce0887f8-ebc1-494f-9d9b-dcc83875ea4c", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:55.777Z", "end_time": "2025-08-18T06:50:56.173Z", "duration_ms": 396}, {"id": "a29e6e80-dbc4-4947-aa21-b022d0ec7e0f", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:56.174Z", "end_time": "2025-08-18T06:50:57.821Z", "duration_ms": 1647}, {"id": "354467fd-5173-4449-9005-a3060129bbf7", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:57.824Z", "end_time": "2025-08-18T06:50:58.099Z", "duration_ms": 275}, {"id": "01fb4cfd-cc9c-4fb1-a9ee-19f1537e015a", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:58.099Z", "end_time": "2025-08-18T06:50:58.897Z", "duration_ms": 798}, {"id": "5e4a3745-278c-43e4-a3ac-0108ac79d297", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:50:58.898Z", "end_time": "2025-08-18T06:51:00.017Z", "duration_ms": 1119}, {"id": "ab80d283-5c5a-465f-a9fc-906cc77c2202", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:51:00.017Z", "end_time": "2025-08-18T06:51:00.840Z", "duration_ms": 823}, {"id": "9268153f-a2a6-4265-a033-e7ab2ad1477e", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:51:00.840Z", "end_time": "2025-08-18T06:51:04.211Z", "duration_ms": 3371}, {"id": "f5df15d7-bb7b-4854-8c0e-c030f7bf0753", "migration_id": "2a0abec6-388c-4f5f-938c-7636db846b3f", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:51:04.212Z", "end_time": "2025-08-18T06:51:06.342Z", "duration_ms": 2130}]}