<template>
  <div class="share-record-list">
    <!-- 页面标题 -->
    <div class="page-header">
  
      <div class="header-actions">
        <n-space>
          <n-button type="primary" @click="handleAnalyzeSharing">
            <template #icon>
              <n-icon><AnalyticsOutline /></n-icon>
            </template>
            分享分析
          </n-button>
          <n-button @click="handleExportData">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出数据
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="总分享次数" :value="stats.total_shares">
              <template #prefix>
                <n-icon color="#18a058">
                  <ShareSocialOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="分享用户数" :value="stats.share_users">
              <template #prefix>
                <n-icon color="#2080f0">
                  <PeopleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="平均转化率" :value="stats.avg_conversion_rate + '%'">
              <template #prefix>
                <n-icon color="#f0a020">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="总浏览量" :value="stats.total_views">
              <template #prefix>
                <n-icon color="#d03050">
                  <EyeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <n-form inline :model="filterForm" label-placement="left">
        <n-form-item label="关键词">
          <n-input
            v-model:value="filterForm.keyword"
            placeholder="搜索分享内容、用户昵称"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="分享类型">
          <n-select
            v-model:value="filterForm.share_type"
            placeholder="选择分享类型"
            style="width: 150px"
            clearable
            :options="shareTypeOptions"
          />
        </n-form-item>
        <n-form-item label="分享平台">
          <n-select
            v-model:value="filterForm.platform"
            placeholder="选择分享平台"
            style="width: 150px"
            clearable
            :options="platformOptions"
          />
        </n-form-item>
        <n-form-item label="分享时间">
          <n-date-picker
            v-model:value="filterForm.share_time_range"
            type="daterange"
            clearable
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="转化状态">
          <n-select
            v-model:value="filterForm.conversion_status"
            placeholder="选择转化状态"
            style="width: 120px"
            clearable
            :options="conversionStatusOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button @click="handleResetFilter">
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </div>

    <!-- 分享记录列表 -->
    <div class="table-section">
      <n-data-table
        :columns="columns"
        :data="shareRecords"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="checkedRowKeys.length > 0">
      <n-space>
        <span>已选择 {{ checkedRowKeys.length }} 项</span>
        <n-button size="small" @click="handleBatchAnalyze">
          批量分析
        </n-button>
        <n-button size="small" @click="handleBatchExport">
          批量导出
        </n-button>
        <n-button size="small" type="error" @click="handleBatchDelete">
          批量删除
        </n-button>
      </n-space>
    </div>

    <!-- 分享详情抽屉 -->
    <n-drawer
      v-model:show="showShareDetail"
      :width="600"
      placement="right"
    >
      <n-drawer-content title="分享详情">
        <ShareDetailPanel
          v-if="selectedShare"
          :share="selectedShare"
          @close="showShareDetail = false"
        />
      </n-drawer-content>
    </n-drawer>

    <!-- 分享分析模态框 -->
    <n-modal
      v-model:show="showSharingAnalysis"
      preset="card"
      title="分享效果分析"
      style="width: 1000px"
    >
      <SharingAnalysis
        v-if="selectedShareIds.length"
        :share-ids="selectedShareIds"
        @close="showSharingAnalysis = false"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h, markRaw } from 'vue'
import {
  NCard,
  NGrid,
  NGridItem,
  NStatistic,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NButton,
  NSpace,
  NIcon,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NModal,
  NTag,
  NAvatar,
  NTooltip,
  useMessage,
  useDialog,
  type DataTableColumns
} from 'naive-ui'
import {
  AnalyticsOutline,
  DownloadOutline,
  ShareSocialOutline,
  PeopleOutline,
  TrendingUpOutline,
  EyeOutline,
  SearchOutline,
  CreateOutline,
  TrashOutline,
  LinkOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import ShareDetailPanel from './components/ShareDetailPanel.vue'
import SharingAnalysis from './components/SharingAnalysis.vue'

const wechatStore = useWechatStore()
const message = useMessage()
const dialog = useDialog()

// 预创建图标组件，避免在render函数中重复创建
const ViewIcon = () => h(NIcon, () => h(markRaw(EyeOutline)))
const AnalyticsIcon = () => h(NIcon, () => h(markRaw(AnalyticsOutline)))
const DeleteIcon = () => h(NIcon, () => h(markRaw(TrashOutline)))

// 状态
const loading = ref(false)
const showShareDetail = ref(false)
const showSharingAnalysis = ref(false)
const selectedShare = ref<any>(null)
const selectedShareIds = ref<number[]>([])
const checkedRowKeys = ref<number[]>([])

// 筛选表单
const filterForm = reactive({
  keyword: '',
  share_type: null,
  platform: null,
  share_time_range: null,
  conversion_status: null
})

// 选项数据
const shareTypeOptions = [
  { label: '朋友圈', value: 'moments' },
  { label: '群聊', value: 'group' },
  { label: '私聊', value: 'private' },
  { label: '公众号', value: 'official' }
]

const platformOptions = [
  { label: '微信', value: 'wechat' },
  { label: '微博', value: 'weibo' },
  { label: 'QQ', value: 'qq' },
  { label: '其他', value: 'other' }
]

const conversionStatusOptions = [
  { label: '已转化', value: 'converted' },
  { label: '未转化', value: 'not_converted' },
  { label: '待跟进', value: 'pending' }
]

// 统计数据
const stats = reactive({
  total_shares: 2580,
  share_users: 1240,
  avg_conversion_rate: 15.8,
  total_views: 12450
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 分享记录数据
const shareRecords = ref<any[]>([])

// 表格列定义
const columns: DataTableColumns = [
  {
    type: 'selection'
  },
  {
    title: '分享用户',
    key: 'user',
    width: 200,
    render: (row: any) => {
      return h('div', { class: 'user-info' }, [
        h(NAvatar, {
          size: 'small',
          src: row.user_avatar,
          fallbackSrc: '/default-avatar.png'
        }),
        h('div', { class: 'user-details' }, [
          h('div', { class: 'user-name' }, row.user_nickname),
          h('div', { class: 'user-id' }, `ID: ${row.user_id}`)
        ])
      ])
    }
  },
  {
    title: '分享内容',
    key: 'content',
    width: 300,
    render: (row: any) => {
      return h('div', { class: 'share-content' }, [
        h('div', { class: 'content-title' }, row.content_title),
        h('div', { class: 'content-desc' }, row.content_description),
        h('div', { class: 'content-url' }, [
          h(NIcon, { size: 14, style: 'margin-right: 4px' }, {
            default: () => h(markRaw(LinkOutline))
          }),
          row.content_url
        ])
      ])
    }
  },
  {
    title: '分享类型',
    key: 'share_type',
    width: 100,
    render: (row: any) => {
      const typeMap: Record<string, { label: string; type: string }> = {
        moments: { label: '朋友圈', type: 'success' },
        group: { label: '群聊', type: 'info' },
        private: { label: '私聊', type: 'warning' },
        official: { label: '公众号', type: 'error' }
      }
      const config = typeMap[row.share_type] || { label: row.share_type, type: 'default' }
      return h(NTag, { type: config.type as 'success' | 'info' | 'warning' | 'error' | 'default', size: 'small' }, { default: () => config.label })
    }
  },
  {
    title: '分享平台',
    key: 'platform',
    width: 100,
    render: (row: any) => {
      const platformMap: Record<string, { label: string; color: string }> = {
        wechat: { label: '微信', color: '#07c160' },
        weibo: { label: '微博', color: '#ff8200' },
        qq: { label: 'QQ', color: '#12b7f5' },
        other: { label: '其他', color: '#666' }
      }
      const config = platformMap[row.platform] || { label: row.platform, color: '#666' }
      return h(NTag, { 
        color: { color: config.color, textColor: '#fff' },
        size: 'small'
      }, { default: () => config.label })
    }
  },
  {
    title: '浏览数据',
    key: 'views',
    width: 120,
    render: (row: any) => {
      return h('div', { class: 'view-stats' }, [
        h('div', `浏览: ${row.view_count}`),
        h('div', `点击: ${row.click_count}`),
        h('div', `转化: ${row.conversion_count}`)
      ])
    }
  },
  {
    title: '转化率',
    key: 'conversion_rate',
    width: 100,
    render: (row: any) => {
      const rate = row.conversion_rate
      const color = rate >= 20 ? '#18a058' : rate >= 10 ? '#f0a020' : '#d03050'
      return h(NTag, {
        color: { color, textColor: '#fff' },
        size: 'small'
      }, { default: () => `${rate}%` })
    }
  },
  {
    title: '分享时间',
    key: 'shared_at',
    width: 160,
    render: (row: any) => new Date(row.shared_at).toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row: any) => {
      return h(NSpace, { size: 'small' }, [
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'primary',
            quaternary: true,
            onClick: () => handleViewDetail(row)
          }, {
            icon: ViewIcon
          }),
          default: () => '查看详情'
        }),
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'info',
            quaternary: true,
            onClick: () => handleAnalyzeShare(row)
          }, {
            icon: AnalyticsIcon
          }),
          default: () => '分析效果'
        }),
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            quaternary: true,
            onClick: () => handleDeleteShare(row)
          }, {
            icon: DeleteIcon
          }),
          default: () => '删除'
        })
      ])
    }
  }
]

// 方法
const loadShareRecords = async () => {
  loading.value = true
  try {
    // 模拟数据
    shareRecords.value = [
      {
        id: 1,
        user_id: 'wx_001',
        user_nickname: '张小明',
        user_avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=young%20man%20avatar%20profile%20picture&image_size=square',
        content_title: '春季新品发布会',
        content_description: '全新产品线即将上市，敬请期待',
        content_url: 'https://example.com/spring-launch',
        share_type: 'moments',
        platform: 'wechat',
        view_count: 156,
        click_count: 23,
        conversion_count: 5,
        conversion_rate: 21.7,
        shared_at: '2024-01-05T10:30:00Z'
      },
      {
        id: 2,
        user_id: 'wx_002',
        user_nickname: '李美丽',
        user_avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=young%20woman%20avatar%20profile%20picture&image_size=square',
        content_title: '限时优惠活动',
        content_description: '全场8折，仅限今日',
        content_url: 'https://example.com/discount',
        share_type: 'group',
        platform: 'wechat',
        view_count: 89,
        click_count: 12,
        conversion_count: 2,
        conversion_rate: 16.7,
        shared_at: '2024-01-05T14:15:00Z'
      },
      {
        id: 3,
        user_id: 'wx_003',
        user_nickname: '王大力',
        user_avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=middle%20aged%20man%20avatar%20profile%20picture&image_size=square',
        content_title: '产品使用心得',
        content_description: '使用一个月后的真实感受分享',
        content_url: 'https://example.com/review',
        share_type: 'private',
        platform: 'wechat',
        view_count: 45,
        click_count: 8,
        conversion_count: 1,
        conversion_rate: 12.5,
        shared_at: '2024-01-05T16:45:00Z'
      }
    ]
    
    pagination.itemCount = shareRecords.value.length
  } catch (error) {
    message.error('加载分享记录失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadShareRecords()
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    keyword: '',
    share_type: null,
    platform: null,
    share_time_range: null,
    conversion_status: null
  })
  handleSearch()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadShareRecords()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadShareRecords()
}

const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
  checkedRowKeys.value = keys.map(key => Number(key))
}

const handleViewDetail = (share: any) => {
  selectedShare.value = share
  showShareDetail.value = true
}

const handleAnalyzeShare = (share: any) => {
  selectedShareIds.value = [share.id]
  showSharingAnalysis.value = true
}

const handleDeleteShare = (share: any) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除用户 ${share.user_nickname} 的分享记录吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // TODO: 实现删除功能
      message.success('删除成功')
      loadShareRecords()
    }
  })
}

const handleAnalyzeSharing = () => {
  if (checkedRowKeys.value.length > 0) {
    selectedShareIds.value = checkedRowKeys.value
  } else {
    selectedShareIds.value = shareRecords.value.map(record => record.id)
  }
  showSharingAnalysis.value = true
}

const handleExportData = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const handleBatchAnalyze = () => {
  selectedShareIds.value = checkedRowKeys.value
  showSharingAnalysis.value = true
}

const handleBatchExport = () => {
  // TODO: 实现批量导出功能
  message.info('批量导出功能开发中')
}

const handleBatchDelete = () => {
  dialog.warning({
    title: '确认批量删除',
    content: `确定要删除选中的 ${checkedRowKeys.value.length} 条分享记录吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // TODO: 实现批量删除功能
      message.success('批量删除成功')
      checkedRowKeys.value = []
      loadShareRecords()
    }
  })
}

// 初始化
onMounted(() => {
  loadShareRecords()
})
</script>

<style scoped>
.share-record-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.stats-cards {
  margin-bottom: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.table-section {
  margin-bottom: 16px;
}

.batch-actions {
  padding: 12px 16px;
  background: #f0f2f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.share-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-title {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-desc {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-url {
  font-size: 12px;
  color: #2080f0;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.view-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}
</style>