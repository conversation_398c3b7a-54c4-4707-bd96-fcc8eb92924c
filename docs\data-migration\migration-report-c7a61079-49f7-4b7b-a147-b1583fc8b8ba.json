{"migrationId": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "timestamp": "2025-08-18T06:34:07.782Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 11, "failedTables": 10, "totalRecords": 165, "migratedRecords": 92, "failedRecords": 73}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:33:33.130Z", "errors": ["Unknown column 'status' in 'field list'"], "endTime": "2025-08-18T06:33:36.557Z", "duration": 3427}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:33:36.562Z", "errors": ["Unknown column 'status' in 'field list'"], "endTime": "2025-08-18T06:33:38.303Z", "duration": 1741}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T06:33:38.306Z", "errors": [], "endTime": "2025-08-18T06:33:40.789Z", "duration": 2483}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:33:40.792Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`role_permissions`, CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T06:33:42.535Z", "duration": 1743}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:33:42.538Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:33:43.014Z", "errors": [], "endTime": "2025-08-18T06:33:44.507Z", "duration": 1493}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:33:44.509Z", "errors": ["Unknown column 'icon' in 'field list'"], "endTime": "2025-08-18T06:33:48.577Z", "duration": 4068}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:33:48.580Z", "errors": ["Unknown column 'last_contact_at' in 'field list'"], "endTime": "2025-08-18T06:33:51.207Z", "duration": 2627}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:33:51.210Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:33:51.585Z", "errors": ["Unknown column 'end_time' in 'field list'"], "endTime": "2025-08-18T06:33:53.563Z", "duration": 1978}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:33:53.565Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:33:54.067Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:33:54.754Z", "errors": ["Unknown column 'organizer_id' in 'field list'"], "endTime": "2025-08-18T06:33:58.835Z", "duration": 4081}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:33:58.838Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:33:59.324Z", "errors": ["Unknown column 'max_claims_per_day' in 'field list'"], "endTime": "2025-08-18T06:34:00.765Z", "duration": 1441}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:34:00.768Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:34:01.093Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:34:01.967Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:34:03.207Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:34:03.855Z", "errors": ["Unknown column 'content' in 'field list'"], "endTime": "2025-08-18T06:34:06.302Z", "duration": 2447}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:34:06.305Z", "errors": ["Unknown column 'moved_at' in 'field list'"], "endTime": "2025-08-18T06:34:07.779Z", "duration": 1474}], "logs": [{"id": "9a337927-8aea-4e65-b315-e8fad4d2a066", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:33.130Z", "end_time": "2025-08-18T06:33:36.557Z", "duration_ms": 3427}, {"id": "36ecfaad-58f3-42b9-a4bb-fbe2ccca706b", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:36.562Z", "end_time": "2025-08-18T06:33:38.303Z", "duration_ms": 1741}, {"id": "3ae419ed-c7bd-44b4-8e50-b962f6bad84f", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T06:33:38.306Z", "end_time": "2025-08-18T06:33:40.789Z", "duration_ms": 2483}, {"id": "d3a105bd-a594-431d-911b-301f208f6a32", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:40.792Z", "end_time": "2025-08-18T06:33:42.535Z", "duration_ms": 1743}, {"id": "74b2cbb3-8355-43f6-ab02-41c45c896ebe", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:42.538Z", "end_time": "2025-08-18T06:33:43.014Z", "duration_ms": 476}, {"id": "53fc383c-fd6d-4b04-81f2-607c1f95ee83", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:33:43.014Z", "end_time": "2025-08-18T06:33:44.507Z", "duration_ms": 1493}, {"id": "e95df0f2-7ccd-4654-a2ca-eea5323fdd67", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:44.509Z", "end_time": "2025-08-18T06:33:48.577Z", "duration_ms": 4068}, {"id": "abfd8580-efdd-42a9-82f9-dea359ae312f", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:48.580Z", "end_time": "2025-08-18T06:33:51.207Z", "duration_ms": 2627}, {"id": "ab6bad83-a919-491d-adf8-6037cf4a7237", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:51.210Z", "end_time": "2025-08-18T06:33:51.584Z", "duration_ms": 374}, {"id": "cf139916-2541-489d-87bd-3a251aa0b92f", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:51.585Z", "end_time": "2025-08-18T06:33:53.563Z", "duration_ms": 1978}, {"id": "dc76ecbb-f179-4cd0-9ebb-e91c77cf1ef2", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:53.565Z", "end_time": "2025-08-18T06:33:54.067Z", "duration_ms": 502}, {"id": "071d2295-8326-4015-bce6-418cb073f4dc", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:54.067Z", "end_time": "2025-08-18T06:33:54.754Z", "duration_ms": 687}, {"id": "cb1b483d-a5f1-45a4-9918-d41a3f48a0a3", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:54.754Z", "end_time": "2025-08-18T06:33:58.835Z", "duration_ms": 4081}, {"id": "63014f40-3f50-47b0-836a-64874362e133", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:58.838Z", "end_time": "2025-08-18T06:33:59.324Z", "duration_ms": 486}, {"id": "1807593b-8396-4645-91be-11e394327e2e", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:33:59.324Z", "end_time": "2025-08-18T06:34:00.765Z", "duration_ms": 1441}, {"id": "cf8f0fde-ed4e-482a-ba3e-79be75470f22", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:00.768Z", "end_time": "2025-08-18T06:34:01.093Z", "duration_ms": 325}, {"id": "4c61e536-a850-4023-8927-6817ec8facfb", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:01.093Z", "end_time": "2025-08-18T06:34:01.967Z", "duration_ms": 874}, {"id": "d9d88f8f-ae05-469a-a25c-f9c5fd9b9f3b", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:01.967Z", "end_time": "2025-08-18T06:34:03.206Z", "duration_ms": 1239}, {"id": "8d1d0b8c-265b-41ad-aa47-ec8dea501671", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:03.207Z", "end_time": "2025-08-18T06:34:03.855Z", "duration_ms": 648}, {"id": "840e02f9-39e5-4d61-86fd-e84b1288c8bd", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:03.855Z", "end_time": "2025-08-18T06:34:06.302Z", "duration_ms": 2447}, {"id": "9729e986-40ae-49fb-98c2-1cc508684add", "migration_id": "c7a61079-49f7-4b7b-a147-b1583fc8b8ba", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:34:06.305Z", "end_time": "2025-08-18T06:34:07.779Z", "duration_ms": 1474}]}