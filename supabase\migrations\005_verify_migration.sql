-- 验证数据迁移结果
-- 检查选项分类表数据
SELECT 'option_categories' as table_name, COUNT(*) as record_count FROM option_categories;

-- 检查选项项目表数据
SELECT 'option_items' as table_name, COUNT(*) as record_count FROM option_items;

-- 检查各分类的选项数量
SELECT 
    oc.code as category_code,
    oc.name as category_name,
    COUNT(oi.id) as item_count
FROM option_categories oc
LEFT JOIN option_items oi ON oc.id = oi.category_id
GROUP BY oc.id, oc.code, oc.name
ORDER BY oc.sort_order;

-- 检查客户来源选项数据
SELECT 
    oc.name as category_name,
    oi.code,
    oi.label,
    oi.value,
    oi.color,
    oi.icon,
    oi.sort_order
FROM option_categories oc
JOIN option_items oi ON oc.id = oi.category_id
WHERE oc.code = 'customer_source'
ORDER BY oi.sort_order;

-- 检查客户等级选项数据
SELECT 
    oc.name as category_name,
    oi.code,
    oi.label,
    oi.value,
    oi.color,
    oi.icon,
    oi.sort_order
FROM option_categories oc
JOIN option_items oi ON oc.id = oi.category_id
WHERE oc.code = 'customer_category'
ORDER BY oi.sort_order;

-- 检查业务线选项数据
SELECT 
    oc.name as category_name,
    oi.code,
    oi.label,
    oi.value,
    oi.color,
    oi.icon,
    oi.sort_order
FROM option_categories oc
JOIN option_items oi ON oc.id = oi.category_id
WHERE oc.code = 'business_line'
ORDER BY oi.sort_order;

-- 检查表权限设置
SELECT 
    grantee,
    table_name,
    privilege_type
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
    AND grantee IN ('anon', 'authenticated') 
    AND table_name IN ('option_categories', 'option_items')
ORDER BY table_name, grantee;

-- 检查RLS策略状态
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN ('option_categories', 'option_items');