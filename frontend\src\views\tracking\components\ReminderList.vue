<template>
  <div class="reminder-list">
    <n-spin :show="loading">
      <div v-if="reminders.length === 0" class="empty-state">
        <n-empty description="暂无提醒" />
      </div>
      
      <div v-else class="reminder-items">
        <div
          v-for="reminder in reminders"
          :key="reminder.id"
          class="reminder-item"
          :class="{
            'reminder-unread': reminder.status === 'pending' || reminder.status === 'sent',
            'reminder-overdue': isOverdue(reminder)
          }"
        >
          <div class="reminder-header">
            <div class="reminder-title">
              <n-space align="center">
                <n-icon :color="getTypeColor(reminder.type)">
                  <component :is="getTypeIcon(reminder.type)" />
                </n-icon>
                <span class="title-text">{{ reminder.title }}</span>
                <n-tag
                  :type="getStatusTagType(reminder.status)"
                  size="small"
                >
                  {{ getStatusLabel(reminder.status) }}
                </n-tag>
                <n-tag
                  v-if="isOverdue(reminder)"
                  type="error"
                  size="small"
                >
                  逾期
                </n-tag>
              </n-space>
            </div>
            
            <div class="reminder-actions">
              <n-space size="small">
                <n-button
                  v-if="reminder.status === 'pending' || reminder.status === 'sent'"
                  size="small"
                  type="success"
                  ghost
                  @click="$emit('read', reminder)"
                >
                  <template #icon>
                    <n-icon><CheckmarkCircle /></n-icon>
                  </template>
                  已读
                </n-button>
                
                <n-button
                  v-if="reminder.status === 'pending' || reminder.status === 'sent'"
                  size="small"
                  type="warning"
                  ghost
                  @click="$emit('dismiss', reminder)"
                >
                  <template #icon>
                    <n-icon><Close /></n-icon>
                  </template>
                  忽略
                </n-button>
                
                <n-button
                  size="small"
                  type="info"
                  ghost
                  @click="$emit('edit', reminder)"
                >
                  <template #icon>
                    <n-icon><CreateOutline /></n-icon>
                  </template>
                  编辑
                </n-button>
                
                <n-button
                  size="small"
                  type="error"
                  ghost
                  @click="$emit('delete', reminder)"
                >
                  <template #icon>
                    <n-icon><Trash /></n-icon>
                  </template>
                  删除
                </n-button>
              </n-space>
            </div>
          </div>
          
          <div class="reminder-content">
            <p class="content-text">{{ reminder.content }}</p>
          </div>
          
          <div class="reminder-meta">
            <n-space>
              <span class="meta-item">
                <n-icon><Person /></n-icon>
                {{ reminder.customer_name || '未知客户' }}
              </span>
              <span class="meta-item">
                <n-icon><Time /></n-icon>
                {{ formatDateTime(reminder.remind_time) }}
              </span>
              <span class="meta-item">
                <n-icon><Calendar /></n-icon>
                {{ formatDateTime(reminder.created_at) }}
              </span>
            </n-space>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="pagination && reminders.length > 0" class="pagination-wrapper">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          show-size-picker
          show-quick-jumper
          @update:page="$emit('page-change', $event)"
          @update:page-size="$emit('page-size-change', $event)"
        >
          <template #prefix="{ itemCount }">
            共 {{ itemCount }} 条
          </template>
        </n-pagination>
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  CheckmarkCircle,
  Close,
  CreateOutline,
  Trash,
  Person,
  Time,
  Calendar,
  Notifications,
  Call,
  Mail,
  Gift,
  Document,
  Settings
} from '@vicons/ionicons5'
import type { AutoReminder } from '@/api/trackingService'

interface Props {
  reminders: AutoReminder[]
  loading?: boolean
  pagination?: {
    page: number
    pageSize: number
    itemCount: number
    pageSizes: number[]
  }
}

interface Emits {
  (e: 'read', reminder: AutoReminder): void
  (e: 'dismiss', reminder: AutoReminder): void
  (e: 'edit', reminder: AutoReminder): void
  (e: 'delete', reminder: AutoReminder): void
  (e: 'page-change', page: number): void
  (e: 'page-size-change', pageSize: number): void
}

defineProps<Props>()
defineEmits<Emits>()

// 工具函数
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}

const isOverdue = (reminder: AutoReminder) => {
  const now = new Date()
  const remindTime = new Date(reminder.remind_time)
  return remindTime < now && (reminder.status === 'pending' || reminder.status === 'sent')
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    sent: '已发送',
    read: '已读',
    dismissed: '已忽略'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    sent: 'info',
    read: 'success',
    dismissed: 'default'
  }
  return statusMap[status] || 'default'
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    follow_up: '#18a058',
    birthday: '#f0a020',
    contract_renewal: '#2080f0',
    payment_due: '#d03050',
    custom: '#722ed1'
  }
  return colorMap[type] || '#8c8c8c'
}

const getTypeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    follow_up: Call,
    birthday: Gift,
    contract_renewal: Document,
    payment_due: Mail,
    custom: Settings
  }
  return iconMap[type] || Notifications
}
</script>

<style scoped>
.reminder-list {
  min-height: 200px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.reminder-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reminder-item {
  padding: 16px;
  border: 1px solid #e0e0e6;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.reminder-item:hover {
  border-color: #36ad6a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reminder-unread {
  border-left: 4px solid #18a058;
  background: #f6ffed;
}

.reminder-overdue {
  border-left: 4px solid #d03050;
  background: #fff2f0;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.reminder-title {
  flex: 1;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.reminder-actions {
  flex-shrink: 0;
}

.reminder-content {
  margin-bottom: 12px;
}

.content-text {
  margin: 0;
  color: #595959;
  line-height: 1.6;
}

.reminder-meta {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.meta-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.meta-item .n-icon {
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>