import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 加载环境变量
dotenv.config({ path: join(__dirname, '..', '.env') })

// 创建 Supabase 客户端
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://cdtlgjmgxwuffmenzdvi.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdGxnam1neHd1ZmZtZW56ZHZpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDM5NDA2NCwiZXhwIjoyMDY5OTcwMDY0fQ.delj0aibOkVzMH7JCGvfEVNmIOJqOpVX8IOep4-3GSc'
)

// 选项分类数据
const optionCategories = [
  {
    code: 'customer_source',
    name: '客户来源',
    description: '客户的来源渠道',
    is_active: true,
    sort_order: 1
  },
  {
    code: 'customer_level',
    name: '客户级别',
    description: '客户的重要程度级别',
    is_active: true,
    sort_order: 2
  },
  {
    code: 'decoration_type',
    name: '装修类型',
    description: '装修的类型分类',
    is_active: true,
    sort_order: 3
  },
  {
    code: 'house_status',
    name: '房屋状态',
    description: '房屋的当前状态',
    is_active: true,
    sort_order: 4
  },
  {
    code: 'budget_range',
    name: '预算范围',
    description: '装修预算的范围',
    is_active: true,
    sort_order: 5
  }
]

// 选项项数据
const optionItems = [
  // 客户来源
  {
    category_code: 'customer_source',
    code: 'website',
    label: '官网',
    value: 'website',
    sort_order: 1
  },
  {
    category_code: 'customer_source',
    code: 'referral',
    label: '朋友推荐',
    value: 'referral',
    sort_order: 2
  },
  {
    category_code: 'customer_source',
    code: 'social_media',
    label: '社交媒体',
    value: 'social_media',
    sort_order: 3
  },
  {
    category_code: 'customer_source',
    code: 'advertisement',
    label: '广告',
    value: 'advertisement',
    sort_order: 4
  },
  {
    category_code: 'customer_source',
    code: 'exhibition',
    label: '展会',
    value: 'exhibition',
    sort_order: 5
  },
  
  // 客户级别
  {
    category_code: 'customer_level',
    code: 'vip',
    label: 'VIP客户',
    value: 'vip',
    color: '#ff4d4f',
    sort_order: 1
  },
  {
    category_code: 'customer_level',
    code: 'high',
    label: '高价值客户',
    value: 'high',
    color: '#fa8c16',
    sort_order: 2
  },
  {
    category_code: 'customer_level',
    code: 'medium',
    label: '普通客户',
    value: 'medium',
    color: '#1890ff',
    sort_order: 3
  },
  {
    category_code: 'customer_level',
    code: 'low',
    label: '潜在客户',
    value: 'low',
    color: '#52c41a',
    sort_order: 4
  },
  
  // 装修类型
  {
    category_code: 'decoration_type',
    code: 'full_decoration',
    label: '全包装修',
    value: 'full_decoration',
    sort_order: 1
  },
  {
    category_code: 'decoration_type',
    code: 'half_decoration',
    label: '半包装修',
    value: 'half_decoration',
    sort_order: 2
  },
  {
    category_code: 'decoration_type',
    code: 'clear_decoration',
    label: '清包装修',
    value: 'clear_decoration',
    sort_order: 3
  },
  {
    category_code: 'decoration_type',
    code: 'soft_decoration',
    label: '软装设计',
    value: 'soft_decoration',
    sort_order: 4
  },
  
  // 房屋状态
  {
    category_code: 'house_status',
    code: 'new_house',
    label: '新房',
    value: 'new_house',
    sort_order: 1
  },
  {
    category_code: 'house_status',
    code: 'second_hand',
    label: '二手房',
    value: 'second_hand',
    sort_order: 2
  },
  {
    category_code: 'house_status',
    code: 'old_house',
    label: '老房翻新',
    value: 'old_house',
    sort_order: 3
  },
  {
    category_code: 'house_status',
    code: 'villa',
    label: '别墅',
    value: 'villa',
    sort_order: 4
  },
  
  // 预算范围
  {
    category_code: 'budget_range',
    code: 'budget_5w',
    label: '5万以下',
    value: '5w',
    sort_order: 1
  },
  {
    category_code: 'budget_range',
    code: 'budget_5_10w',
    label: '5-10万',
    value: '5-10w',
    sort_order: 2
  },
  {
    category_code: 'budget_range',
    code: 'budget_10_20w',
    label: '10-20万',
    value: '10-20w',
    sort_order: 3
  },
  {
    category_code: 'budget_range',
    code: 'budget_20_50w',
    label: '20-50万',
    value: '20-50w',
    sort_order: 4
  },
  {
    category_code: 'budget_range',
    code: 'budget_50w',
    label: '50万以上',
    value: '50w+',
    sort_order: 5
  }
]

// 初始化选项数据
async function initOptionsData() {
  try {
    console.log('开始初始化选项数据...')
    
    // 1. 插入选项分类
    console.log('插入选项分类...')
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('option_categories')
      .upsert(optionCategories, { 
        onConflict: 'code',
        ignoreDuplicates: false 
      })
      .select()
    
    if (categoriesError) {
      console.error('插入选项分类失败:', categoriesError)
      return
    }
    
    console.log(`成功插入 ${categoriesData?.length || 0} 个选项分类`)
    
    // 2. 获取分类ID映射
    const { data: allCategories, error: getCategoriesError } = await supabase
      .from('option_categories')
      .select('id, code')
    
    if (getCategoriesError) {
      console.error('获取分类ID失败:', getCategoriesError)
      return
    }
    
    const categoryMap = new Map()
    allCategories?.forEach(cat => {
      categoryMap.set(cat.code, cat.id)
    })
    
    // 3. 准备选项项数据（添加category_id）
    const itemsWithCategoryId = optionItems.map(item => ({
      category_id: categoryMap.get(item.category_code),
      code: item.code,
      label: item.label,
      value: item.value,
      color: item.color || null,
      icon: item.icon || null,
      description: item.description || null,
      is_active: true,
      sort_order: item.sort_order
    })).filter(item => item.category_id) // 过滤掉没有找到分类ID的项
    
    // 4. 插入选项项
    console.log('插入选项项...')
    const { data: itemsData, error: itemsError } = await supabase
      .from('option_items')
      .upsert(itemsWithCategoryId, { 
        onConflict: 'category_id,code',
        ignoreDuplicates: false 
      })
      .select()
    
    if (itemsError) {
      console.error('插入选项项失败:', itemsError)
      return
    }
    
    console.log(`成功插入 ${itemsData?.length || 0} 个选项项`)
    
    // 5. 验证数据
    console.log('\n验证插入的数据:')
    for (const category of optionCategories) {
      const { data: items, error } = await supabase
        .from('option_items')
        .select('*')
        .eq('category_id', categoryMap.get(category.code))
        .eq('is_active', true)
        .order('sort_order')
      
      if (!error && items) {
        console.log(`${category.name}: ${items.length} 个选项`)
        items.forEach(item => {
          console.log(`  - ${item.label} (${item.value})`)
        })
      }
    }
    
    console.log('\n选项数据初始化完成！')
    
  } catch (error) {
    console.error('初始化选项数据时发生错误:', error)
  }
}

// 执行初始化
initOptionsData()
  .then(() => {
    console.log('脚本执行完成')
    process.exit(0)
  })
  .catch((error) => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })