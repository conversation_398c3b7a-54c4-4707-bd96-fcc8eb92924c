<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="导入客户数据"
    :style="{ width: '600px' }"
    :mask-closable="false"
  >
    <div class="import-modal">
      <!-- 步骤指示器 -->
      <n-steps
        :current="currentStep"
        :status="stepStatus"
        size="small"
        class="mb-6"
      >
        <n-step title="选择文件" />
        <n-step title="数据预览" />
        <n-step title="导入结果" />
      </n-steps>

      <!-- 步骤1: 选择文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="upload-area">
          <n-upload
            ref="uploadRef"
            :file-list="fileList"
            :max="1"
            accept=".xlsx,.xls,.csv"
            :before-upload="handleBeforeUpload"
            :on-remove="handleRemoveFile"
            :show-file-list="false"
            class="upload-dragger"
          >
            <n-upload-dragger>
              <div class="upload-content">
                <n-icon size="48" class="upload-icon">
                  <CloudUploadOutline />
                </n-icon>
                <n-text class="upload-text">
                  点击或拖拽文件到此区域上传
                </n-text>
                <n-text depth="3" class="upload-hint">
                  支持 .xlsx、.xls、.csv 格式，文件大小不超过 10MB
                </n-text>
              </div>
            </n-upload-dragger>
          </n-upload>

          <!-- 文件信息 -->
          <div v-if="selectedFile" class="file-info">
            <n-card size="small">
              <div class="file-item">
                <n-icon class="file-icon">
                  <DocumentTextOutline />
                </n-icon>
                <div class="file-details">
                  <div class="file-name">{{ selectedFile.name }}</div>
                  <div class="file-size">{{ formatFileSize(selectedFile.file?.size || 0) }}</div>
                </div>
                <n-button
                  text
                  type="error"
                  @click="handleRemoveFile"
                >
                  <template #icon>
                    <n-icon><CloseOutline /></n-icon>
                  </template>
                </n-button>
              </div>
            </n-card>
          </div>
        </div>

        <!-- 模板下载 -->
        <div class="template-section">
          <n-alert type="info" class="mb-4">
            <template #icon>
              <n-icon><InformationCircleOutline /></n-icon>
            </template>
            请确保您的文件格式符合要求。如果您是第一次导入，建议先下载模板文件。
          </n-alert>
          
          <n-button
            type="primary"
            ghost
            :loading="downloadingTemplate"
            @click="handleDownloadTemplate"
          >
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            下载导入模板
          </n-button>
        </div>
      </div>

      <!-- 步骤2: 数据预览 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="preview-section">
          <n-alert type="warning" class="mb-4">
            <template #icon>
              <n-icon><WarningOutline /></n-icon>
            </template>
            请仔细检查预览数据，确认无误后点击"开始导入"。
          </n-alert>

          <!-- 数据统计 -->
          <div class="data-stats">
            <n-statistic label="总记录数" :value="previewData.length" />
            <n-statistic label="有效记录" :value="validRecords" />
            <n-statistic label="错误记录" :value="errorRecords" />
          </div>

          <!-- 数据表格 -->
          <n-data-table
            :columns="previewColumns"
            :data="previewData.slice(0, 10)"
            :max-height="300"
            size="small"
            class="preview-table"
          />

          <div v-if="previewData.length > 10" class="preview-note">
            <n-text depth="3">仅显示前10条记录，共{{ previewData.length }}条</n-text>
          </div>

          <!-- 错误信息 -->
          <div v-if="validationErrors.length > 0" class="error-section">
            <n-collapse>
              <n-collapse-item title="数据验证错误" name="errors">
                <div class="error-list">
                  <div v-for="(error, index) in validationErrors.slice(0, 20)" :key="index" class="error-item">
                    <n-text type="error">第{{ error.row }}行: {{ error.message }}</n-text>
                  </div>
                  <div v-if="validationErrors.length > 20" class="error-more">
                    <n-text depth="3">还有{{ validationErrors.length - 20 }}个错误...</n-text>
                  </div>
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <div class="result-icon">
            <n-icon
              :size="64"
              :color="importResult.success > 0 ? '#18a058' : '#d03050'"
            >
              <CheckmarkCircleOutline v-if="importResult.success > 0" />
              <CloseCircleOutline v-else />
            </n-icon>
          </div>

          <div class="result-stats">
            <n-statistic label="成功导入" :value="importResult.success" />
            <n-statistic label="导入失败" :value="importResult.failed" />
          </div>

          <!-- 错误详情 -->
          <div v-if="importResult.errors && importResult.errors.length > 0" class="import-errors">
            <n-collapse>
              <n-collapse-item title="导入错误详情" name="import-errors">
                <div class="error-list">
                  <div v-for="(error, index) in importResult.errors" :key="index" class="error-item">
                    <n-text type="error">{{ error }}</n-text>
                  </div>
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #action>
      <div class="modal-actions">
        <n-button
          v-if="currentStep > 1 && currentStep < 3"
          @click="handlePrevStep"
        >
          上一步
        </n-button>
        
        <n-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="!selectedFile"
          :loading="parsing"
          @click="handleParseFile"
        >
          下一步
        </n-button>
        
        <n-button
          v-if="currentStep === 2"
          type="primary"
          :disabled="validRecords === 0"
          :loading="importing"
          @click="handleImport"
        >
          开始导入
        </n-button>
        
        <n-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleFinish"
        >
          完成
        </n-button>
        
        <n-button @click="handleCancel">
          {{ currentStep === 3 ? '关闭' : '取消' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  CloudUploadOutline,
  DocumentTextOutline,
  CloseOutline,
  InformationCircleOutline,
  DownloadOutline,
  WarningOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'
import { CustomerService } from '@/api/customerService'
import * as XLSX from 'xlsx'

// Props定义
interface Props {
  show: boolean
}

const props = defineProps<Props>()

// Emits定义
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const message = useMessage()
const uploadRef = ref()

// 弹窗状态
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 步骤状态
const currentStep = ref(1)
const stepStatus = ref<'process' | 'finish' | 'error' | 'wait'>('process')

// 文件状态
const fileList = ref([])
const selectedFile = ref<any>(null)
const parsing = ref(false)
const downloading = ref(false)
const downloadingTemplate = ref(false)
const importing = ref(false)

// 预览数据
const previewData = ref<any[]>([])
const validationErrors = ref<{ row: number; message: string }[]>([])

// 导入结果
const importResult = reactive({
  success: 0,
  failed: 0,
  errors: [] as string[]
})

// 预览表格列定义
const previewColumns = [
  {
    title: '行号',
    key: 'rowIndex',
    width: 60,
    render: (_: any, index: number) => index + 1
  },
  {
    title: '客户姓名',
    key: 'name',
    width: 100
  },
  {
    title: '手机号',
    key: 'phone',
    width: 120
  },
  {
    title: '性别',
    key: 'gender',
    width: 60
  },
  {
    title: '装修面积',
    key: 'area',
    width: 80
  },
  {
    title: '装修类型',
    key: 'decorationType',
    width: 100
  },
  {
    title: '客户来源',
    key: 'source',
    width: 100
  },
  {
    title: '客户等级',
    key: 'level',
    width: 80
  },
  {
    title: '地址',
    key: 'address',
    width: 150
  },
  {
    title: '备注',
    key: 'remark',
    width: 150
  }
]

// 计算属性
const validRecords = computed(() => {
  return previewData.value.filter(item => !item._hasError).length
})

const errorRecords = computed(() => {
  return previewData.value.filter(item => item._hasError).length
})

// 文件大小格式化
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 文件上传前处理
const handleBeforeUpload = (data: { file: File }) => {
  const file = data.file
  
  // 检查文件类型
  const allowedTypes = ['.xlsx', '.xls', '.csv']
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
  
  if (!allowedTypes.includes(fileExtension)) {
    message.error('只支持 .xlsx、.xls、.csv 格式的文件')
    return false
  }
  
  // 检查文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过 10MB')
    return false
  }
  
  selectedFile.value = {
    name: file.name,
    file: file
  }
  
  return false // 阻止自动上传
}

// 移除文件
const handleRemoveFile = () => {
  selectedFile.value = null
  fileList.value = []
  previewData.value = []
  validationErrors.value = []
}

// 下载导入模板
const handleDownloadTemplate = async () => {
  try {
    downloadingTemplate.value = true
    await CustomerService.downloadImportTemplate()
    message.success('模板下载成功')
  } catch (error: any) {
    message.error(error.message || '模板下载失败')
  } finally {
    downloadingTemplate.value = false
  }
}

// 解析文件
const handleParseFile = async () => {
  if (!selectedFile.value?.file) {
    message.error('请先选择文件')
    return
  }
  
  try {
    parsing.value = true
    
    const file = selectedFile.value.file
    const data = await readFileAsArrayBuffer(file)
    
    let workbook: XLSX.WorkBook
    
    if (file.name.toLowerCase().endsWith('.csv')) {
      const text = new TextDecoder('utf-8').decode(data)
      workbook = XLSX.read(text, { type: 'string' })
    } else {
      workbook = XLSX.read(data, { type: 'array' })
    }
    
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]
    
    // 解析数据
    const parsedData = parseCustomerData(jsonData)
    previewData.value = parsedData.data
    validationErrors.value = parsedData.errors
    
    currentStep.value = 2
    message.success('文件解析成功')
  } catch (error: any) {
    message.error('文件解析失败: ' + error.message)
  } finally {
    parsing.value = false
  }
}

// 读取文件为ArrayBuffer
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

// 解析客户数据
const parseCustomerData = (jsonData: any[][]) => {
  const data: any[] = []
  const errors: { row: number; message: string }[] = []
  
  // 跳过表头
  const rows = jsonData.slice(1)
  
  rows.forEach((row, index) => {
    const rowIndex = index + 2 // 从第2行开始
    
    if (!row || row.length === 0) return
    
    const customerData: any = {
      name: row[0]?.toString().trim() || '',
      phone: row[1]?.toString().trim() || '',
      gender: row[2]?.toString().trim() || '',
      area: row[3] ? Number(row[3]) : undefined,
      decorationType: row[4]?.toString().trim() || '',
      source: row[5]?.toString().trim() || '',
      level: row[6]?.toString().trim() || '',
      address: row[7]?.toString().trim() || '',
      remark: row[8]?.toString().trim() || '',
      _hasError: false
    }
    
    // 数据验证
    const rowErrors: string[] = []
    
    if (!customerData.name) {
      rowErrors.push('客户姓名不能为空')
    }
    
    if (!customerData.phone) {
      rowErrors.push('手机号不能为空')
    } else if (!/^1[3-9]\d{9}$/.test(customerData.phone)) {
      rowErrors.push('手机号格式不正确')
    }
    
    if (customerData.gender && !['male', 'female', 'unknown'].includes(customerData.gender)) {
      rowErrors.push('性别只能是: male, female, unknown')
    }
    
    if (customerData.area && (customerData.area < 1 || customerData.area > 9999)) {
      rowErrors.push('装修面积必须在1-9999之间')
    }
    
    if (customerData.decorationType && !['rough', 'fine', 'simple', 'luxury'].includes(customerData.decorationType)) {
      rowErrors.push('装修类型只能是: rough, fine, simple, luxury')
    }
    
    if (customerData.source && !['online', 'referral', 'exhibition', 'other'].includes(customerData.source)) {
      rowErrors.push('客户来源只能是: online, referral, exhibition, other')
    }
    
    if (customerData.level && !['A', 'B', 'C', 'D'].includes(customerData.level)) {
      rowErrors.push('客户等级只能是: A, B, C, D')
    }
    
    if (rowErrors.length > 0) {
      customerData._hasError = true
      rowErrors.forEach(error => {
        errors.push({ row: rowIndex, message: error })
      })
    }
    
    data.push(customerData)
  })
  
  return { data, errors }
}

// 执行导入
const handleImport = async () => {
  try {
    importing.value = true
    
    // 过滤有效数据
    const validData = previewData.value.filter(item => !item._hasError)
    
    if (validData.length === 0) {
      message.error('没有有效的数据可以导入')
      return
    }
    
    // 创建FormData
    const formData = new FormData()
    
    // 将有效数据转换为Excel文件
    const worksheet = XLSX.utils.json_to_sheet(validData.map(item => {
      const { _hasError, ...cleanData } = item
      return cleanData
    }))
    
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers')
    
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    
    formData.append('file', blob, 'customers.xlsx')
    
    // 调用导入API
    const result = await CustomerService.importCustomers(blob as any)
    
    importResult.success = result.success
    importResult.failed = result.failed
    importResult.errors = result.errors || []
    
    currentStep.value = 3
    stepStatus.value = result.success > 0 ? 'finish' : 'error'
    
    if (result.success > 0) {
      message.success(`成功导入 ${result.success} 条客户数据`)
    }
    
    if (result.failed > 0) {
      message.warning(`${result.failed} 条数据导入失败`)
    }
  } catch (error: any) {
    message.error('导入失败: ' + error.message)
    stepStatus.value = 'error'
  } finally {
    importing.value = false
  }
}

// 上一步
const handlePrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 完成
const handleFinish = () => {
  emit('success')
  handleCancel()
}

// 取消
const handleCancel = () => {
  showModal.value = false
}

// 重置状态
const resetState = () => {
  currentStep.value = 1
  stepStatus.value = 'process'
  selectedFile.value = null
  fileList.value = []
  previewData.value = []
  validationErrors.value = []
  importResult.success = 0
  importResult.failed = 0
  importResult.errors = []
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (show) => {
    if (show) {
      resetState()
    }
  }
)
</script>

<style scoped lang="scss">
.import-modal {
  .step-content {
    min-height: 400px;
    padding: 16px 0;
  }
  
  .upload-area {
    margin-bottom: 24px;
    
    .upload-dragger {
      width: 100%;
      
      .upload-content {
        text-align: center;
        padding: 40px 20px;
        
        .upload-icon {
          color: #18a058;
          margin-bottom: 16px;
        }
        
        .upload-text {
          display: block;
          font-size: 16px;
          margin-bottom: 8px;
        }
        
        .upload-hint {
          display: block;
          font-size: 14px;
        }
      }
    }
    
    .file-info {
      margin-top: 16px;
      
      .file-item {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .file-icon {
          font-size: 24px;
          color: #18a058;
        }
        
        .file-details {
          flex: 1;
          
          .file-name {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .file-size {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
  
  .template-section {
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }
  
  .preview-section {
    .data-stats {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
    }
    
    .preview-table {
      margin-bottom: 16px;
    }
    
    .preview-note {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .error-section {
      .error-list {
        max-height: 200px;
        overflow-y: auto;
        
        .error-item {
          padding: 4px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
        }
        
        .error-more {
          padding: 8px 0;
          text-align: center;
        }
      }
    }
  }
  
  .result-section {
    text-align: center;
    
    .result-icon {
      margin-bottom: 24px;
    }
    
    .result-stats {
      display: flex;
      justify-content: center;
      gap: 48px;
      margin-bottom: 24px;
    }
    
    .import-errors {
      text-align: left;
      
      .error-list {
        max-height: 200px;
        overflow-y: auto;
        
        .error-item {
          padding: 4px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
  
  .modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}
</style>