import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { marketingService, type Campaign, type Participant, type CampaignShare } from '@/api/marketingService'
import { createDiscreteApi } from 'naive-ui'

// 创建独立的message API，用于在store中使用
const { message } = createDiscreteApi(['message'])

export const useMarketingStore = defineStore('marketing', () => {


  // 状态
  const campaigns = ref<Campaign[]>([])
  const currentCampaign = ref<Campaign | null>(null)
  const participants = ref<Participant[]>([])
  const campaignShares = ref<CampaignShare[]>([])
  const loading = ref(false)
  const participantsLoading = ref(false)
  const sharesLoading = ref(false)
  const total = ref(0)
  const participantsTotal = ref(0)
  const sharesTotal = ref(0)

  // 计算属性
  const campaignCount = computed(() => campaigns.value.length)
  const activeCampaigns = computed(() => campaigns.value.filter(c => c.status === 'active'))
  const draftCampaigns = computed(() => campaigns.value.filter(c => c.status === 'draft'))
  const endedCampaigns = computed(() => campaigns.value.filter(c => c.status === 'ended'))

  // 活动管理操作
  const fetchCampaigns = async (params?: {
    page?: number
    pageSize?: number
    status?: string
    type?: string
    start_date?: string
    end_date?: string
    keyword?: string
  }) => {
    loading.value = true
    try {
      const response: any = await marketingService.getCampaigns(params || {})
      campaigns.value = response.data
      total.value = response.total
      return response
    } catch (error: any) {
      console.error('Failed to fetch campaigns:', error?.message || error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchCampaign = async (id: number) => {
    loading.value = true
    try {
      const response = await marketingService.getCampaign(id)
      currentCampaign.value = response.data
      return response
    } catch (error) {
      message.error('获取活动详情失败')
      console.error('Fetch campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createCampaign = async (data: Campaign) => {
    loading.value = true
    try {
      const response = await marketingService.createCampaign(data)
      campaigns.value.unshift(response.data)
      total.value += 1
      message.success('创建活动成功')
      return response
    } catch (error) {
      message.error('创建活动失败')
      console.error('Create campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateCampaign = async (id: number, data: Partial<Campaign>) => {
    loading.value = true
    try {
      const response = await marketingService.updateCampaign(id, data)
      const index = campaigns.value.findIndex(c => c.id === id)
      if (index !== -1) {
        campaigns.value[index] = { ...campaigns.value[index], ...response.data }
      }
      if (currentCampaign.value?.id === id) {
        currentCampaign.value = { ...currentCampaign.value, ...response.data }
      }
      message.success('更新活动成功')
      return response
    } catch (error) {
      message.error('更新活动失败')
      console.error('Update campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteCampaign = async (id: number) => {
    loading.value = true
    try {
      await marketingService.deleteCampaign(id)
      campaigns.value = campaigns.value.filter(c => c.id !== id)
      total.value -= 1
      if (currentCampaign.value?.id === id) {
        currentCampaign.value = null
      }
      message.success('删除活动成功')
    } catch (error) {
      message.error('删除活动失败')
      console.error('Delete campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const startCampaign = async (id: number) => {
    loading.value = true
    try {
      const response = await marketingService.startCampaign(id)
      const index = campaigns.value.findIndex(c => c.id === id)
      if (index !== -1) {
        campaigns.value[index].status = 'active'
      }
      if (currentCampaign.value?.id === id) {
        currentCampaign.value.status = 'active'
      }
      message.success('启动活动成功')
      return response
    } catch (error) {
      message.error('启动活动失败')
      console.error('Start campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const pauseCampaign = async (id: number) => {
    loading.value = true
    try {
      const response = await marketingService.pauseCampaign(id)
      const index = campaigns.value.findIndex(c => c.id === id)
      if (index !== -1) {
        campaigns.value[index].status = 'paused'
      }
      if (currentCampaign.value?.id === id) {
        currentCampaign.value.status = 'paused'
      }
      message.success('暂停活动成功')
      return response
    } catch (error) {
      message.error('暂停活动失败')
      console.error('Pause campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const endCampaign = async (id: number) => {
    loading.value = true
    try {
      const response = await marketingService.endCampaign(id)
      const index = campaigns.value.findIndex(c => c.id === id)
      if (index !== -1) {
        campaigns.value[index].status = 'ended'
      }
      if (currentCampaign.value?.id === id) {
        currentCampaign.value.status = 'ended'
      }
      message.success('结束活动成功')
      return response
    } catch (error) {
      message.error('结束活动失败')
      console.error('End campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const copyCampaign = async (id: number, data: Partial<Campaign>) => {
    loading.value = true
    try {
      const response = await marketingService.copyCampaign(id, data)
      campaigns.value.unshift(response.data)
      total.value += 1
      message.success('复制活动成功')
      return response
    } catch (error) {
      message.error('复制活动失败')
      console.error('Copy campaign error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 参与者管理操作
  const fetchParticipants = async (campaignId: number, params: any = {}) => {
    loading.value = true
    try {
      const response: any = await marketingService.getParticipants(campaignId, params)
      participants.value = response.data
      participantsTotal.value = response.total
      return response
    } catch (error) {
      message.error('获取参与者列表失败')
      console.error('Fetch participants error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addParticipant = async (campaignId: number, data: any) => {
    loading.value = true
    try {
      const response = await marketingService.addParticipant(campaignId, data)
      participants.value.unshift(response.data)
      participantsTotal.value += 1
      message.success('添加参与者成功')
      return response
    } catch (error) {
      message.error('添加参与者失败')
      console.error('Add participant error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateParticipant = async (campaignId: number, participantId: number, data: any) => {
    loading.value = true
    try {
      const response = await marketingService.updateParticipant(campaignId, participantId, data)
      const index = participants.value.findIndex(p => p.id === participantId)
      if (index !== -1) {
        participants.value[index] = { ...participants.value[index], ...response.data }
      }
      message.success('更新参与者成功')
      return response
    } catch (error) {
      message.error('更新参与者失败')
      console.error('Update participant error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteParticipant = async (campaignId: number, participantId: number) => {
    participantsLoading.value = true
    try {
      await marketingService.deleteParticipant(campaignId, participantId)
      participants.value = participants.value.filter(p => p.id !== participantId)
      participantsTotal.value -= 1
      message.success('删除参与者成功')
    } catch (error) {
      message.error('删除参与者失败')
      console.error('Delete participant error:', error)
      throw error
    } finally {
      participantsLoading.value = false
    }
  }

  // 分享记录管理
  const fetchCampaignShares = async (campaignId: number, params: any = {}) => {
    sharesLoading.value = true
    try {
      const response = await marketingService.getCampaignShares(campaignId, params)
      campaignShares.value = response.data.data
      sharesTotal.value = response.data.total
      return response.data.data
    } catch (error) {
      message.error('获取分享记录失败')
      console.error('Fetch campaign shares error:', error)
      throw error
    } finally {
      sharesLoading.value = false
    }
  }

  const recordShare = async (campaignId: number, data: any) => {
    loading.value = true
    try {
      // 将campaignId包含在data中传递给API
      const shareData = { ...data, campaign_id: campaignId }
      const response = await marketingService.recordShare(shareData)
      const resultData = response.data || response
      campaignShares.value.unshift(resultData)
      sharesTotal.value += 1
      message.success('记录分享成功')
      return resultData
    } catch (error) {
      message.error('记录分享失败')
      console.error('Record share error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 批量操作
  const batchUpdateCampaigns = async (ids: number[], action: string, data?: any) => {
    loading.value = true
    try {
      const response = await marketingService.batchUpdateCampaigns(ids, action, data)
      
      // 更新本地状态
      if (action === 'delete') {
        campaigns.value = campaigns.value.filter(c => !ids.includes(c.id!))
        total.value -= ids.length
      } else if (action === 'updateStatus' && data?.status) {
        campaigns.value.forEach(c => {
          if (ids.includes(c.id!)) {
            c.status = data.status
          }
        })
      }
      
      message.success('批量操作成功')
      return response
    } catch (error) {
      message.error('批量操作失败')
      console.error('Batch update campaigns error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 统计数据
  const getCampaignStats = async (campaignId: number) => {
    loading.value = true
    try {
      const response = await marketingService.getCampaignStats(campaignId)
      return response.data || response
    } catch (error) {
      message.error('获取活动统计失败')
      console.error('Get campaign stats error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getCampaignTrends = async (campaignId: number, params: any = {}) => {
    loading.value = true
    try {
      const response = await marketingService.getCampaignTrends(campaignId, params)
      return response.data || response
    } catch (error) {
      message.error('获取活动趋势失败')
      console.error('Get campaign trends error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    campaigns.value = []
    currentCampaign.value = null
    participants.value = []
    campaignShares.value = []
    loading.value = false
    participantsLoading.value = false
    sharesLoading.value = false
    total.value = 0
    participantsTotal.value = 0
    sharesTotal.value = 0
  }

  return {
    // 状态
    campaigns,
    currentCampaign,
    participants,
    campaignShares,
    loading,
    participantsLoading,
    sharesLoading,
    total,
    participantsTotal,
    sharesTotal,
    
    // 计算属性
    campaignCount,
    activeCampaigns,
    draftCampaigns,
    endedCampaigns,
    
    // 活动管理方法
    fetchCampaigns,
    fetchCampaign,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    startCampaign,
    pauseCampaign,
    endCampaign,
    copyCampaign,
    
    // 参与者管理方法
    fetchParticipants,
    addParticipant,
    updateParticipant,
    deleteParticipant,
    
    // 分享记录方法
    fetchCampaignShares,
    recordShare,
    
    // 批量操作方法
    batchUpdateCampaigns,
    
    // 统计方法
    getCampaignStats,
    getCampaignTrends,
    
    // 工具方法
    resetState
  }
})