<template>
  <div class="settings">
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-description">管理系统的基础配置和安全设置</p>
    </div>

    <n-grid :cols="1" :y-gap="20">
      <n-card title="设置管理" class="settings-card">
        <template #header-extra>
          <n-space>
            <n-button type="primary" @click="handleSave">
              <template #icon>
                <n-icon><SaveOutline /></n-icon>
              </template>
              保存设置
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </template>

        <n-tabs type="line" animated>
          <n-tab-pane name="basic" tab="基础设置">
            <n-form
              ref="basicFormRef"
              :model="basicSettings"
              :rules="basicRules"
              label-placement="left"
              label-width="150px"
              class="settings-form"
            >
              <n-form-item label="系统名称" path="systemName">
                <n-input
                  v-model:value="basicSettings.systemName"
                  placeholder="请输入系统名称"
                />
              </n-form-item>
              
              <n-form-item label="公司名称" path="companyName">
                <n-input
                  v-model:value="basicSettings.companyName"
                  placeholder="请输入公司名称"
                />
              </n-form-item>
              
              <n-form-item label="系统版本" path="version">
                <n-input
                  v-model:value="basicSettings.version"
                  placeholder="请输入系统版本"
                  readonly
                />
              </n-form-item>
              
              <n-form-item label="联系邮箱" path="contactEmail">
                <n-input
                  v-model:value="basicSettings.contactEmail"
                  placeholder="请输入联系邮箱"
                />
              </n-form-item>
            </n-form>
          </n-tab-pane>

          <n-tab-pane name="customer" tab="客户设置">
            <n-tabs type="card" size="small">
              <n-tab-pane name="categories" tab="客户分类">
                <div class="customer-setting-section">
                  <div class="section-header">
                    <h4>客户分类管理</h4>
                    <n-button type="primary" size="small" @click="addCustomerCategory">
                      <template #icon>
                        <n-icon><AddOutline /></n-icon>
                      </template>
                      新增分类
                    </n-button>
                  </div>
                  <n-data-table
                    :columns="categoryColumns"
                    :data="customerCategories"
                    size="small"
                  />
                </div>
              </n-tab-pane>
              
              <n-tab-pane name="sources" tab="客户来源">
                <div class="customer-setting-section">
                  <div class="section-header">
                    <h4>客户来源管理</h4>
                    <n-button type="primary" size="small" @click="addCustomerSource">
                      <template #icon>
                        <n-icon><AddOutline /></n-icon>
                      </template>
                      新增来源
                    </n-button>
                  </div>
                  <n-data-table
                    :columns="sourceColumns"
                    :data="customerSources"
                    size="small"
                  />
                </div>
              </n-tab-pane>
              
              <n-tab-pane name="abandon-reasons" tab="弃单理由">
                <div class="customer-setting-section">
                  <div class="section-header">
                    <h4>弃单理由管理</h4>
                    <n-button type="primary" size="small" @click="addAbandonReason">
                      <template #icon>
                        <n-icon><AddOutline /></n-icon>
                      </template>
                      新增理由
                    </n-button>
                  </div>
                  <n-data-table
                    :columns="reasonColumns"
                    :data="abandonReasons"
                    size="small"
                  />
                </div>
              </n-tab-pane>
              
              <n-tab-pane name="scripts" tab="话术管理">
                <div class="customer-setting-section">
                  <div class="section-header">
                    <h4>话术模板管理</h4>
                    <n-button type="primary" size="small" @click="addScript">
                      <template #icon>
                        <n-icon><AddOutline /></n-icon>
                      </template>
                      新增话术
                    </n-button>
                  </div>
                  <n-data-table
                    :columns="scriptColumns"
                    :data="scripts"
                    size="small"
                  />
                </div>
              </n-tab-pane>
              
              <n-tab-pane name="roles" tab="角色管理">
                <div class="customer-setting-section">
                  <div class="section-header">
                    <h4>系统角色管理</h4>
                    <n-button type="primary" size="small" @click="addRole">
                      <template #icon>
                        <n-icon><AddOutline /></n-icon>
                      </template>
                      新增角色
                    </n-button>
                  </div>
                  <n-data-table
                    :columns="roleColumns"
                    :data="roles"
                    size="small"
                  />
                </div>
              </n-tab-pane>
            </n-tabs>
          </n-tab-pane>
        </n-tabs>
      </n-card>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { SaveOutline, RefreshOutline, AddOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'
import type { FormInst, FormRules, DataTableColumns } from 'naive-ui'
import type { RowData } from 'naive-ui/es/data-table/src/interface'

const message = useMessage()
const dialog = useDialog()

// 表单引用
const basicFormRef = ref<FormInst | null>(null)

// 基础设置
const basicSettings = reactive({
  systemName: 'YYSH客户管理系统',
  companyName: 'YYSH科技有限公司',
  version: 'v1.0.0',
  contactEmail: '<EMAIL>'
})

// 表单验证规则
const basicRules: FormRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' }
  ]
}

// 客户分类数据
const customerCategories = ref([
  { id: 1, name: '潜在客户', description: '有意向但未成交的客户', sort: 1, status: '启用' },
  { id: 2, name: '成交客户', description: '已经成交的客户', sort: 2, status: '启用' },
  { id: 3, name: '重点客户', description: '高价值重点维护客户', sort: 3, status: '启用' }
])

// 客户来源数据
const customerSources = ref([
  { id: 1, name: '网络推广', description: '通过网络广告获得的客户', sort: 1, status: '启用' },
  { id: 2, name: '朋友推荐', description: '通过朋友介绍的客户', sort: 2, status: '启用' },
  { id: 3, name: '电话营销', description: '通过电话营销获得的客户', sort: 3, status: '启用' }
])

// 弃单理由数据
const abandonReasons = ref([
  { id: 1, name: '价格太高', description: '客户认为价格超出预算', sort: 1, status: '启用' },
  { id: 2, name: '需求不匹配', description: '产品不符合客户需求', sort: 2, status: '启用' },
  { id: 3, name: '竞争对手', description: '客户选择了竞争对手', sort: 3, status: '启用' }
])

// 话术模板数据
const scripts = ref([
  { id: 1, name: '初次接触话术', category: '开场白', content: '您好，我是YYSH的客户经理...', status: '启用' },
  { id: 2, name: '产品介绍话术', category: '产品说明', content: '我们的产品具有以下特点...', status: '启用' },
  { id: 3, name: '价格谈判话术', category: '价格沟通', content: '关于价格方面，我们可以...', status: '启用' }
])

// 角色数据
const roles = ref([
  { id: 1, name: '超级管理员', description: '拥有所有权限', userCount: 1, status: '启用' },
  { id: 2, name: '销售经理', description: '销售团队管理权限', userCount: 3, status: '启用' },
  { id: 3, name: '客服专员', description: '客户服务相关权限', userCount: 5, status: '启用' }
])

// 表格列定义
const categoryColumns: DataTableColumns<RowData> = [
  { title: '分类名称', key: 'name' },
  { title: '描述', key: 'description' },
  { title: '排序', key: 'sort' },
  { title: '状态', key: 'status' },
  {
    title: '操作',
    key: 'actions',
    render: (row: RowData) => h('div', [
      h('n-button', { size: 'small', type: 'primary', style: 'margin-right: 8px' }, '编辑'),
      h('n-button', { size: 'small', type: 'error' }, '删除')
    ])
  }
]

const sourceColumns: DataTableColumns<RowData> = [
  { title: '来源名称', key: 'name' },
  { title: '描述', key: 'description' },
  { title: '排序', key: 'sort' },
  { title: '状态', key: 'status' },
  {
    title: '操作',
    key: 'actions',
    render: (row: RowData) => h('div', [
      h('n-button', { size: 'small', type: 'primary', style: 'margin-right: 8px' }, '编辑'),
      h('n-button', { size: 'small', type: 'error' }, '删除')
    ])
  }
]

const reasonColumns: DataTableColumns<RowData> = [
  { title: '理由名称', key: 'name' },
  { title: '描述', key: 'description' },
  { title: '排序', key: 'sort' },
  { title: '状态', key: 'status' },
  {
    title: '操作',
    key: 'actions',
    render: (row: RowData) => h('div', [
      h('n-button', { size: 'small', type: 'primary', style: 'margin-right: 8px' }, '编辑'),
      h('n-button', { size: 'small', type: 'error' }, '删除')
    ])
  }
]

const scriptColumns: DataTableColumns<RowData> = [
  { title: '话术名称', key: 'name' },
  { title: '分类', key: 'category' },
  { title: '内容', key: 'content', ellipsis: { tooltip: true } },
  { title: '状态', key: 'status' },
  {
    title: '操作',
    key: 'actions',
    render: (row: RowData) => h('div', [
      h('n-button', { size: 'small', type: 'primary', style: 'margin-right: 8px' }, '编辑'),
      h('n-button', { size: 'small', type: 'error' }, '删除')
    ])
  }
]

const roleColumns: DataTableColumns<RowData> = [
  { title: '角色名称', key: 'name' },
  { title: '描述', key: 'description' },
  { title: '用户数', key: 'userCount' },
  { title: '状态', key: 'status' },
  {
    title: '操作',
    key: 'actions',
    render: (row: RowData) => h('div', [
      h('n-button', { size: 'small', type: 'primary', style: 'margin-right: 8px' }, '编辑'),
      h('n-button', { size: 'small', type: 'info', style: 'margin-right: 8px' }, '权限'),
      h('n-button', { size: 'small', type: 'error' }, '删除')
    ])
  }
]

// 方法
const handleSave = async () => {
  try {
    await basicFormRef.value?.validate()
    message.success('设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const handleReset = () => {
  dialog.warning({
    title: '确认重置',
    content: '确定要重置所有设置吗？此操作不可撤销。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      basicSettings.systemName = 'YYSH客户管理系统'
      basicSettings.companyName = 'YYSH科技有限公司'
      basicSettings.version = 'v1.0.0'
      basicSettings.contactEmail = '<EMAIL>'
      message.success('设置已重置')
    }
  })
}

// 客户分类管理方法
const addCustomerCategory = () => {
  message.info('添加客户分类功能')
}

// 客户来源管理方法
const addCustomerSource = () => {
  message.info('添加客户来源功能')
}

// 弃单理由管理方法
const addAbandonReason = () => {
  message.info('添加弃单理由功能')
}

// 话术管理方法
const addScript = () => {
  message.info('添加话术模板功能')
}

// 角色管理方法
const addRole = () => {
  message.info('添加角色功能')
}

// 初始化
onMounted(() => {
  // 加载设置数据
})
</script>

<style scoped>
.settings {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.settings-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-form {
  max-width: 800px;
  padding: 20px 0;
}

.settings-form .n-form-item {
  margin-bottom: 24px;
}

/* 客户设置部分样式 */
.customer-setting-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--n-border-color);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

/* 选项卡样式优化 */
:deep(.n-tabs .n-tabs-nav) {
  margin-bottom: 20px;
}

:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}

:deep(.n-tabs-tab) {
  padding: 12px 20px;
  font-weight: 500;
}

:deep(.n-tabs-tab--active) {
  color: var(--n-color-primary);
}

/* 嵌套选项卡样式 */
:deep(.n-tabs .n-tabs .n-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.n-tabs .n-tabs .n-tabs-tab) {
  padding: 8px 16px;
  font-size: 14px;
}

/* 数据表格样式 */
:deep(.n-data-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.n-data-table-th) {
  background-color: var(--n-color-target);
  font-weight: 600;
}

/* 按钮组样式 */
:deep(.n-button + .n-button) {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings {
    padding: 16px;
  }
  
  .settings-form {
    max-width: 100%;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>