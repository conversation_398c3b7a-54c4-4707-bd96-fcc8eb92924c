<template>
  <div class="sales-funnel-analysis">

    <!-- 筛选条件 -->
    <n-card class="filter-card">
      <n-form inline :label-width="80">
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="filters.dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="销售人员">
          <n-select
            v-model:value="filters.salesperson"
            placeholder="选择销售人员"
            clearable
            style="width: 150px"
            :options="salespersonOptions"
          />
        </n-form-item>
        <n-form-item label="客户来源">
          <n-select
            v-model:value="filters.source"
            placeholder="选择客户来源"
            clearable
            style="width: 150px"
            :options="sourceOptions"
          />
        </n-form-item>
        <n-form-item label="产品类型">
          <n-select
            v-model:value="filters.productType"
            placeholder="选择产品类型"
            clearable
            style="width: 150px"
            :options="productTypeOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button @click="handleSearch" type="primary">
              <template #icon>
                <n-icon><Search /></n-icon>
              </template>
              查询
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><Refresh /></n-icon>
              </template>
              重置
            </n-button>
            <n-button @click="handleExport">
              <template #icon>
                <n-icon><Download /></n-icon>
              </template>
              导出
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 漏斗概览 -->
    <n-card title="销售漏斗概览" class="funnel-overview">
      <div class="funnel-container">
        <div class="funnel-stage" v-for="(stage, index) in funnelStages" :key="stage.name">
          <div class="stage-header">
            <h3>{{ stage.name }}</h3>
            <div class="stage-metrics">
              <span class="count">{{ stage.count }}</span>
              <span class="rate" v-if="index > 0">{{ stage.conversionRate }}%</span>
            </div>
          </div>
          <div class="stage-bar" :style="{ width: stage.percentage + '%', backgroundColor: stage.color }">
            <span class="percentage">{{ stage.percentage }}%</span>
          </div>
          <div class="stage-details">
            <span>平均停留: {{ stage.avgDuration }}天</span>
            <span v-if="index < funnelStages.length - 1">流失: {{ stage.lossCount }}</span>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 图表分析 -->
    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="漏斗转化图">
          <div ref="funnelChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="转化率趋势">
          <div ref="conversionTrendChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="各阶段停留时间">
          <div ref="durationChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="流失原因分析">
          <div ref="lossReasonChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 详细数据表格 -->
    <n-card title="阶段详细数据" class="table-card">
      <template #header-extra>
        <n-space>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="stageColumns"
        :data="stageDetailData"
        :loading="loading"
        size="medium"
        :row-key="(row: StageDetail) => row.stage"
      />
    </n-card>

    <!-- 客户流转记录 -->
    <n-card title="客户流转记录" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客户姓名或手机号"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
        </n-space>
      </template>
      
      <n-data-table
        :columns="customerFlowColumns"
        :data="filteredCustomerFlowData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: CustomerFlow) => row.id"
        size="medium"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NIcon,
  NTag,
  NGrid,
  NGridItem,
  NProgress,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import {
  SearchOutline as Search,
  RefreshOutline as Refresh,
  DownloadOutline as Download
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface FunnelStage {
  name: string
  count: number
  percentage: number
  conversionRate: number
  avgDuration: number
  lossCount: number
  color: string
}

interface StageDetail {
  stage: string
  totalCount: number
  convertedCount: number
  conversionRate: number
  avgDuration: number
  lossCount: number
  lossRate: number
  mainLossReason: string
}

interface CustomerFlow {
  id: number
  customerName: string
  phone: string
  currentStage: string
  entryDate: string
  lastUpdateDate: string
  stayDuration: number
  source: string
  salesperson: string
  productType: string
  status: string
  nextAction: string
}

const message = useMessage()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const stageDetailData = ref<StageDetail[]>([])
const customerFlowData = ref<CustomerFlow[]>([])

// 图表引用
const funnelChart = ref<HTMLElement>()
const conversionTrendChart = ref<HTMLElement>()
const durationChart = ref<HTMLElement>()
const lossReasonChart = ref<HTMLElement>()

// 图表实例
let funnelInstance: echarts.ECharts | null = null
let conversionTrendInstance: echarts.ECharts | null = null
let durationInstance: echarts.ECharts | null = null
let lossReasonInstance: echarts.ECharts | null = null

// 筛选条件
const filters = reactive({
  dateRange: null as [number, number] | null,
  salesperson: null as string | null,
  source: null as string | null,
  productType: null as string | null
})

// 漏斗阶段数据
const funnelStages = ref<FunnelStage[]>([
  {
    name: '潜在客户',
    count: 1000,
    percentage: 100,
    conversionRate: 0,
    avgDuration: 0,
    lossCount: 0,
    color: '#5470c6'
  },
  {
    name: '初步接触',
    count: 650,
    percentage: 65,
    conversionRate: 65,
    avgDuration: 3,
    lossCount: 350,
    color: '#91cc75'
  },
  {
    name: '需求确认',
    count: 420,
    percentage: 42,
    conversionRate: 64.6,
    avgDuration: 7,
    lossCount: 230,
    color: '#fac858'
  },
  {
    name: '方案报价',
    count: 280,
    percentage: 28,
    conversionRate: 66.7,
    avgDuration: 5,
    lossCount: 140,
    color: '#ee6666'
  },
  {
    name: '商务谈判',
    count: 180,
    percentage: 18,
    conversionRate: 64.3,
    avgDuration: 10,
    lossCount: 100,
    color: '#73c0de'
  },
  {
    name: '签约成交',
    count: 120,
    percentage: 12,
    conversionRate: 66.7,
    avgDuration: 8,
    lossCount: 60,
    color: '#3ba272'
  }
])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 选项配置
const salespersonOptions = [
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: '赵六', value: 'zhaoliu' }
]

const sourceOptions = [
  { label: '线上推广', value: 'online' },
  { label: '电话营销', value: 'telemarketing' },
  { label: '客户推荐', value: 'referral' },
  { label: '展会活动', value: 'exhibition' }
]

const productTypeOptions = [
  { label: '产品A', value: 'product_a' },
  { label: '产品B', value: 'product_b' },
  { label: '产品C', value: 'product_c' },
  { label: '服务套餐', value: 'service_package' }
]

// 阶段详细数据表格列
const stageColumns: DataTableColumns<StageDetail> = [
  {
    title: '销售阶段',
    key: 'stage',
    width: 120,
    fixed: 'left'
  },
  {
    title: '总客户数',
    key: 'totalCount',
    width: 100,
    sorter: (a, b) => a.totalCount - b.totalCount
  },
  {
    title: '转化客户数',
    key: 'convertedCount',
    width: 120,
    sorter: (a, b) => a.convertedCount - b.convertedCount
  },
  {
    title: '转化率',
    key: 'conversionRate',
    width: 100,
    render(row) {
      return h(NProgress, {
        type: 'line',
        percentage: row.conversionRate,
        showIndicator: true,
        status: row.conversionRate >= 60 ? 'success' : row.conversionRate >= 40 ? 'warning' : 'error'
      })
    },
    sorter: (a, b) => a.conversionRate - b.conversionRate
  },
  {
    title: '平均停留(天)',
    key: 'avgDuration',
    width: 120,
    sorter: (a, b) => a.avgDuration - b.avgDuration
  },
  {
    title: '流失客户数',
    key: 'lossCount',
    width: 120,
    sorter: (a, b) => a.lossCount - b.lossCount
  },
  {
    title: '流失率',
    key: 'lossRate',
    width: 100,
    render(row) {
      const color = row.lossRate >= 50 ? '#d03050' : row.lossRate >= 30 ? '#f0a020' : '#18a058'
      return h('span', { style: { color, fontWeight: 'bold' } }, `${row.lossRate}%`)
    },
    sorter: (a, b) => a.lossRate - b.lossRate
  },
  {
    title: '主要流失原因',
    key: 'mainLossReason',
    width: 150
  }
]

// 客户流转记录表格列
const customerFlowColumns: DataTableColumns<CustomerFlow> = [
  {
    title: '客户姓名',
    key: 'customerName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '手机号',
    key: 'phone',
    width: 130
  },
  {
    title: '当前阶段',
    key: 'currentStage',
    width: 120,
    render(row) {
      const stageMap = {
        '潜在客户': 'default' as const,
        '初步接触': 'info' as const,
        '需求确认': 'warning' as const,
        '方案报价': 'success' as const,
        '商务谈判': 'error' as const,
        '签约成交': 'success' as const
      }
      return h(NTag, { type: stageMap[row.currentStage as keyof typeof stageMap] || 'default' as const, size: 'small' }, 
        { default: () => row.currentStage }
      )
    }
  },
  {
    title: '进入时间',
    key: 'entryDate',
    width: 120,
    render(row) {
      return new Date(row.entryDate).toLocaleDateString()
    }
  },
  {
    title: '停留时长(天)',
    key: 'stayDuration',
    width: 120,
    sorter: (a, b) => a.stayDuration - b.stayDuration
  },
  {
    title: '客户来源',
    key: 'source',
    width: 100
  },
  {
    title: '销售人员',
    key: 'salesperson',
    width: 100
  },
  {
    title: '产品类型',
    key: 'productType',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        active: { text: '跟进中', type: 'success' as const },
        stalled: { text: '停滞', type: 'warning' as const },
        lost: { text: '流失', type: 'error' as const },
        converted: { text: '已转化', type: 'info' as const }
      }
      const status = statusMap[row.status as keyof typeof statusMap] || { text: row.status, type: 'default' as const }
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    }
  },
  {
    title: '下一步行动',
    key: 'nextAction',
    width: 150
  }
]

// 计算属性
const filteredCustomerFlowData = computed(() => {
  if (!searchKeyword.value) return customerFlowData.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return customerFlowData.value.filter(customer => 
    customer.customerName.toLowerCase().includes(keyword) ||
    customer.phone.includes(keyword)
  )
})

// 方法
const handleSearch = () => {
  loadData()
  loadCharts()
}

const handleReset = () => {
  Object.assign(filters, {
    dateRange: null,
    salesperson: null,
    source: null,
    productType: null
  })
  searchKeyword.value = ''
  loadData()
  loadCharts()
}

const handleRefresh = () => {
  loadData()
  loadCharts()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟阶段详细数据
    stageDetailData.value = [
      {
        stage: '潜在客户',
        totalCount: 1000,
        convertedCount: 650,
        conversionRate: 65,
        avgDuration: 3,
        lossCount: 350,
        lossRate: 35,
        mainLossReason: '无明确需求'
      },
      {
        stage: '初步接触',
        totalCount: 650,
        convertedCount: 420,
        conversionRate: 64.6,
        avgDuration: 7,
        lossCount: 230,
        lossRate: 35.4,
        mainLossReason: '预算不足'
      },
      {
        stage: '需求确认',
        totalCount: 420,
        convertedCount: 280,
        conversionRate: 66.7,
        avgDuration: 5,
        lossCount: 140,
        lossRate: 33.3,
        mainLossReason: '竞品选择'
      },
      {
        stage: '方案报价',
        totalCount: 280,
        convertedCount: 180,
        conversionRate: 64.3,
        avgDuration: 10,
        lossCount: 100,
        lossRate: 35.7,
        mainLossReason: '价格过高'
      },
      {
        stage: '商务谈判',
        totalCount: 180,
        convertedCount: 120,
        conversionRate: 66.7,
        avgDuration: 8,
        lossCount: 60,
        lossRate: 33.3,
        mainLossReason: '决策延迟'
      }
    ]
    
    // 模拟客户流转数据
    customerFlowData.value = [
      {
        id: 1,
        customerName: '张三',
        phone: '13800138001',
        currentStage: '商务谈判',
        entryDate: '2024-01-10T10:30:00Z',
        lastUpdateDate: '2024-01-15T14:20:00Z',
        stayDuration: 5,
        source: '线上推广',
        salesperson: '李四',
        productType: '产品A',
        status: 'active',
        nextAction: '准备合同'
      },
      {
        id: 2,
        customerName: '王五',
        phone: '13800138002',
        currentStage: '需求确认',
        entryDate: '2024-01-08T09:15:00Z',
        lastUpdateDate: '2024-01-12T16:45:00Z',
        stayDuration: 7,
        source: '客户推荐',
        salesperson: '张三',
        productType: '产品B',
        status: 'stalled',
        nextAction: '跟进需求细节'
      },
      {
        id: 3,
        customerName: '赵六',
        phone: '13800138003',
        currentStage: '签约成交',
        entryDate: '2024-01-01T11:30:00Z',
        lastUpdateDate: '2024-01-15T10:00:00Z',
        stayDuration: 14,
        source: '展会活动',
        salesperson: '王五',
        productType: '服务套餐',
        status: 'converted',
        nextAction: '项目实施'
      }
    ]
    
    pagination.itemCount = customerFlowData.value.length
  } catch (error) {
    message.error('获取销售漏斗数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (funnelChart.value) {
      funnelInstance = echarts.init(funnelChart.value)
    }
    if (conversionTrendChart.value) {
      conversionTrendInstance = echarts.init(conversionTrendChart.value)
    }
    if (durationChart.value) {
      durationInstance = echarts.init(durationChart.value)
    }
    if (lossReasonChart.value) {
      lossReasonInstance = echarts.init(lossReasonChart.value)
    }
    
    loadCharts()
  })
}

// 加载图表数据
const loadCharts = () => {
  // 漏斗转化图
  if (funnelInstance) {
    const option = {
      title: { text: '销售漏斗', left: 'center' },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}人 ({d}%)'
      },
      series: [{
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: funnelStages.value.map(stage => ({
          value: stage.count,
          name: stage.name
        }))
      }]
    }
    funnelInstance.setOption(option)
  }
  
  // 转化率趋势图
  if (conversionTrendInstance) {
    const option = {
      title: { text: '转化率趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['整体转化率', '初步接触转化率', '需求确认转化率', '方案报价转化率'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '整体转化率',
          type: 'line',
          data: [12, 13, 11, 14, 12, 13],
          smooth: true
        },
        {
          name: '初步接触转化率',
          type: 'line',
          data: [65, 68, 62, 70, 65, 67],
          smooth: true
        },
        {
          name: '需求确认转化率',
          type: 'line',
          data: [64, 67, 61, 69, 64, 66],
          smooth: true
        },
        {
          name: '方案报价转化率',
          type: 'line',
          data: [66, 69, 63, 71, 66, 68],
          smooth: true
        }
      ]
    }
    conversionTrendInstance.setOption(option)
  }
  
  // 各阶段停留时间图
  if (durationInstance) {
    const option = {
      title: { text: '各阶段平均停留时间', left: 'center' },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: funnelStages.value.slice(1).map(stage => stage.name)
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}天'
        }
      },
      series: [{
        name: '停留时间',
        type: 'bar',
        data: funnelStages.value.slice(1).map(stage => stage.avgDuration),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    }
    durationInstance.setOption(option)
  }
  
  // 流失原因分析图
  if (lossReasonInstance) {
    const option = {
      title: { text: '流失原因分析', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 350, name: '无明确需求' },
          { value: 230, name: '预算不足' },
          { value: 140, name: '竞品选择' },
          { value: 100, name: '价格过高' },
          { value: 60, name: '决策延迟' },
          { value: 40, name: '其他原因' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    lossReasonInstance.setOption(option)
  }
}

// 初始化
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.sales-funnel-analysis {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.funnel-overview {
  margin-bottom: 20px;
}

.funnel-container {
  padding: 20px 0;
}

.funnel-stage {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  background: var(--n-card-color);
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stage-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.stage-metrics {
  display: flex;
  gap: 15px;
  align-items: center;
}

.count {
  font-size: 18px;
  font-weight: bold;
  color: var(--n-text-color);
}

.rate {
  font-size: 14px;
  color: var(--n-success-color);
  font-weight: 500;
}

.stage-bar {
  height: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}

.percentage {
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.stage-details {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: var(--n-text-color-2);
}

.charts-grid {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}
</style>