<template>
  <div class="options-management">
    <div class="page-header">
      <h1 class="page-title">选项管理</h1>
      <p class="page-description">管理系统选项分类、选项数据和客户信息配置</p>
    </div>

    <n-card class="options-card">
      <n-tabs 
        v-model:value="activeTab" 
        type="line" 
        animated
        @update:value="handleTabChange"
      >
        <n-tab-pane name="categories" tab="选项分类管理">
          <OptionCategoriesManagement />
        </n-tab-pane>
        <n-tab-pane name="items" tab="选项数据管理">
          <OptionItemsManagement />
        </n-tab-pane>
        <n-tab-pane name="customers" tab="客户管理">
          <div class="customer-management-wrapper">
            <CustomerManagement 
              :embedded="true"
              :show-actions="true"
              @customers-updated="handleCustomersUpdated"
              @customer-selected="handleCustomerSelected"
            />
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { NCard, NTabs, NTabPane } from 'naive-ui'
import OptionCategoriesManagement from './components/OptionCategoriesManagement.vue'
import OptionItemsManagement from './components/OptionItemsManagement.vue'
import CustomerManagement from '@/views/customer/CustomerManagement.vue'
import type { Customer } from '@/api/customerService'

// 路由和消息
const route = useRoute()
const router = useRouter()
const message = useMessage()

// 当前激活的标签页
const activeTab = ref<string>('categories')

// 支持的标签页类型
type TabType = 'categories' | 'items' | 'customers'

// 标签页切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  
  // 更新URL参数
  const query = { ...route.query }
  if (tabName === 'categories') {
    delete query.tab
  } else {
    query.tab = tabName
  }
  
  router.replace({ query })
}

// 客户相关事件处理
const handleCustomersUpdated = () => {
  message.success('客户信息已更新')
}

const handleCustomerSelected = (customer: Customer) => {
  console.log('选中客户:', customer)
  // 可以在这里处理客户选中事件，比如显示详情等
}

// 初始化标签页
const initializeTab = () => {
  const tabParam = route.query.tab as string
  const validTabs: TabType[] = ['categories', 'items', 'customers']
  
  if (tabParam && validTabs.includes(tabParam as TabType)) {
    activeTab.value = tabParam
  } else {
    activeTab.value = 'categories'
  }
}

// 监听路由变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const validTabs: TabType[] = ['categories', 'items', 'customers']
      if (validTabs.includes(newTab as TabType)) {
        activeTab.value = newTab
      }
    } else {
      activeTab.value = 'categories'
    }
  }
)

// 生命周期
onMounted(() => {
  initializeTab()
})
</script>

<style scoped>
.options-management {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.options-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 选项卡样式优化 */
:deep(.n-tabs .n-tabs-nav) {
  margin-bottom: 20px;
}

:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}

:deep(.n-tabs-tab) {
  padding: 12px 20px;
  font-weight: 500;
}

:deep(.n-tabs-tab--active) {
  color: var(--n-color-primary);
}

/* 客户管理包装器样式 */
.customer-management-wrapper {
  min-height: 600px;
  background: var(--n-color-base);
  border-radius: 6px;
  overflow: hidden;
}

/* 嵌入模式下的客户管理组件优化 */
:deep(.customer-management.embedded-mode) {
  padding: 0;
  gap: 12px;
}

:deep(.customer-management.embedded-mode .customer-filters) {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--n-color-base);
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
}

:deep(.customer-management.embedded-mode .customer-table) {
  border-radius: 6px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .options-management {
    padding: 16px;
  }
  
  .customer-management-wrapper {
    min-height: 500px;
  }
  
  :deep(.customer-management.embedded-mode) {
    gap: 8px;
  }
  
  :deep(.customer-management.embedded-mode .customer-filters) {
    padding: 12px;
    margin-bottom: 12px;
  }
}
</style>