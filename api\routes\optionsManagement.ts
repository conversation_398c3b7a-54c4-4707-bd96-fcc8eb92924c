import express, { type Request, type Response } from 'express';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const router = express.Router();

// 创建 Supabase 客户端（使用 service_role_key 用于后端）
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://cdtlgjmgxwuffmenzdvi.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdGxnam1neHd1ZmZtZW56ZHZpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDM5NDA2NCwiZXhwIjoyMDY5OTcwMDY0fQ.delj0aibOkVzMH7JCGvfEVNmIOJqOpVX8IOep4-3GSc'
);

// 定义接口类型
interface CreateCategoryRequest {
  code: string;
  name: string;
  description?: string;
  sort_order?: number;
}

interface UpdateCategoryRequest {
  code?: string;
  name?: string;
  description?: string;
  is_active?: boolean;
  sort_order?: number;
}

interface CreateItemRequest {
  category_id: string;
  code: string;
  value: string;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
}

interface UpdateItemRequest {
  code?: string;
  value?: string;
  label?: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
  sort_order?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// ==================== 选项分类管理 ====================

/**
 * 获取选项分类列表（管理端专用）
 * GET /api/options-management/categories
 */
router.get('/categories', async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      search, 
      is_active, 
      page = 1, 
      page_size = 10,
      sort_by = 'sort_order',
      sort_order = 'asc'
    } = req.query;

    let query = supabase
      .from('option_categories')
      .select('*');

    // 搜索筛选
    if (search) {
      query = query.or(`code.ilike.%${search}%,name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // 状态筛选
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    // 排序
    const validSortFields = ['id', 'code', 'name', 'sort_order', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'sort_order';
    const sortDirection = validSortOrders.includes(sort_order as string) ? sort_order as string : 'asc';
    
    query = query.order(sortField, { ascending: sortDirection === 'asc' });

    // 分页
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSize = Math.max(1, Math.min(100, parseInt(page_size as string) || 10));
    const offset = (pageNum - 1) * pageSize;

    // 获取总数
    const { count } = await supabase
      .from('option_categories')
      .select('*', { count: 'exact', head: true });

    // 获取分页数据
    const { data, error } = await query
      .range(offset, offset + pageSize - 1);

    if (error) {
      res.status(500).json({
        success: false,
        error: `获取选项分类列表失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    const totalPages = Math.ceil((count || 0) / pageSize);

    res.json({
      success: true,
      data: {
        data: data || [],
        total: count || 0,
        page: pageNum,
        page_size: pageSize,
        total_pages: totalPages
      }
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 创建选项分类
 * POST /api/options-management/categories
 */
router.post('/categories', async (req: Request, res: Response): Promise<void> => {
  try {
    const { code, name, description, sort_order = 0 }: CreateCategoryRequest = req.body;

    if (!code || !name) {
      res.status(400).json({
        success: false,
        error: '分类代码和名称不能为空'
      } as ApiResponse);
      return;
    }

    // 检查分类代码是否已存在
    const { data: existing } = await supabase
      .from('option_categories')
      .select('id')
      .eq('code', code)
      .single();

    if (existing) {
      res.status(400).json({
        success: false,
        error: '分类代码已存在'
      } as ApiResponse);
      return;
    }

    const { data, error } = await supabase
      .from('option_categories')
      .insert({
        code,
        name,
        description,
        sort_order,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      res.status(500).json({
        success: false,
        error: `创建分类失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    res.status(201).json({
      success: true,
      data,
      message: '分类创建成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 更新选项分类
 * PUT /api/options-management/categories/:id
 */
router.put('/categories/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { code, name, description, is_active, sort_order }: UpdateCategoryRequest = req.body;

    const updateData: any = {};
    if (code !== undefined) updateData.code = code;
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (is_active !== undefined) updateData.is_active = is_active;
    if (sort_order !== undefined) updateData.sort_order = sort_order;

    if (Object.keys(updateData).length === 0) {
      res.status(400).json({
        success: false,
        error: '没有提供要更新的数据'
      } as ApiResponse);
      return;
    }

    // 如果更新代码，检查是否与其他分类重复
    if (code) {
      const { data: existing } = await supabase
        .from('option_categories')
        .select('id')
        .eq('code', code)
        .neq('id', id)
        .single();

      if (existing) {
        res.status(400).json({
          success: false,
          error: '分类代码已存在'
        } as ApiResponse);
        return;
      }
    }

    const { data, error } = await supabase
      .from('option_categories')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      res.status(500).json({
        success: false,
        error: `更新分类失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    if (!data) {
      res.status(404).json({
        success: false,
        error: '分类不存在'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      data,
      message: '分类更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 删除选项分类
 * DELETE /api/options-management/categories/:id
 */
router.delete('/categories/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 检查分类下是否还有选项项
    const { data: items } = await supabase
      .from('option_items')
      .select('id')
      .eq('category_id', id)
      .eq('is_active', true);

    if (items && items.length > 0) {
      res.status(400).json({
        success: false,
        error: '该分类下还有选项项，无法删除'
      } as ApiResponse);
      return;
    }

    const { error } = await supabase
      .from('option_categories')
      .delete()
      .eq('id', id);

    if (error) {
      res.status(500).json({
        success: false,
        error: `删除分类失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '分类删除成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

// ==================== 选项项管理 ====================

/**
 * 获取选项项列表（管理端专用）
 * GET /api/options-management/items
 */
router.get('/items', async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      category_id, 
      category_code, 
      search, 
      is_active, 
      page = 1, 
      page_size = 10,
      sort_by = 'sort_order',
      sort_order = 'asc'
    } = req.query;

    let query = supabase
      .from('option_items')
      .select(`
        *,
        option_categories(id, code, name)
      `);

    // 根据分类ID筛选
    if (category_id) {
      query = query.eq('category_id', category_id);
    }

    // 根据分类代码筛选
    if (category_code) {
      const { data: category } = await supabase
        .from('option_categories')
        .select('id')
        .eq('code', category_code)
        .eq('is_active', true)
        .single();
      
      if (category) {
        query = query.eq('category_id', category.id);
      }
    }

    // 搜索筛选
    if (search) {
      query = query.or(`label.ilike.%${search}%,code.ilike.%${search}%,value.ilike.%${search}%`);
    }

    // 状态筛选
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    // 排序
    const validSortFields = ['id', 'code', 'value', 'label', 'sort_order', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'sort_order';
    const sortDirection = validSortOrders.includes(sort_order as string) ? sort_order as string : 'asc';
    
    query = query.order(sortField, { ascending: sortDirection === 'asc' });

    // 分页
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSize = Math.max(1, Math.min(100, parseInt(page_size as string) || 10));
    const offset = (pageNum - 1) * pageSize;

    // 获取总数
    const { count } = await supabase
      .from('option_items')
      .select('*', { count: 'exact', head: true });

    // 获取分页数据
    const { data, error } = await query
      .range(offset, offset + pageSize - 1);

    if (error) {
      res.status(500).json({
        success: false,
        error: `获取选项项列表失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    const totalPages = Math.ceil((count || 0) / pageSize);

    res.json({
      success: true,
      data: {
        data: data || [],
        total: count || 0,
        page: pageNum,
        page_size: pageSize,
        total_pages: totalPages
      }
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 创建选项项
 * POST /api/options-management/items
 */
router.post('/items', async (req: Request, res: Response): Promise<void> => {
  try {
    const { category_id, code, value, label, description, color, icon, sort_order = 0 }: CreateItemRequest = req.body;

    if (!category_id || !code || !value || !label) {
      res.status(400).json({
        success: false,
        error: '分类ID、代码、值和标签不能为空'
      } as ApiResponse);
      return;
    }

    // 检查分类是否存在
    const { data: category } = await supabase
      .from('option_categories')
      .select('id')
      .eq('id', category_id)
      .eq('is_active', true)
      .single();

    if (!category) {
      res.status(400).json({
        success: false,
        error: '分类不存在或已禁用'
      } as ApiResponse);
      return;
    }

    // 检查同一分类下代码是否已存在
    const { data: existing } = await supabase
      .from('option_items')
      .select('id')
      .eq('category_id', category_id)
      .eq('code', code)
      .single();

    if (existing) {
      res.status(400).json({
        success: false,
        error: '该分类下已存在相同的代码'
      } as ApiResponse);
      return;
    }

    const { data, error } = await supabase
      .from('option_items')
      .insert({
        category_id,
        code,
        value,
        label,
        description,
        color,
        icon,
        sort_order,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      res.status(500).json({
        success: false,
        error: `创建选项项失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    res.status(201).json({
      success: true,
      data,
      message: '选项项创建成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 更新选项项
 * PUT /api/options-management/items/:id
 */
router.put('/items/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: UpdateItemRequest = req.body;

    // 如果更新代码，检查同一分类下是否已存在
    if (updateData.code) {
      const { data: currentItem } = await supabase
        .from('option_items')
        .select('category_id')
        .eq('id', id)
        .single();

      if (currentItem) {
        const { data: existing } = await supabase
          .from('option_items')
          .select('id')
          .eq('category_id', currentItem.category_id)
          .eq('code', updateData.code)
          .neq('id', id)
          .single();

        if (existing) {
          res.status(400).json({
            success: false,
            error: '该分类下已存在相同的代码'
          } as ApiResponse);
          return;
        }
      }
    }

    const { data, error } = await supabase
      .from('option_items')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      res.status(500).json({
        success: false,
        error: `更新选项项失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    if (!data) {
      res.status(404).json({
        success: false,
        error: '选项项不存在'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      data,
      message: '选项项更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 删除选项项
 * DELETE /api/options-management/items/:id
 */
router.delete('/items/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('option_items')
      .delete()
      .eq('id', id);

    if (error) {
      res.status(500).json({
        success: false,
        error: `删除选项项失败: ${error.message}`
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '选项项删除成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 批量更新选项项排序
 * PUT /api/options-management/items/batch-sort
 */
router.put('/items/batch-sort', async (req: Request, res: Response): Promise<void> => {
  try {
    const { items }: { items: { id: string; sort_order: number }[] } = req.body;

    if (!items || !Array.isArray(items)) {
      res.status(400).json({
        success: false,
        error: '请提供有效的选项项列表'
      } as ApiResponse);
      return;
    }

    // 批量更新排序
    const updatePromises = items.map(item =>
      supabase
        .from('option_items')
        .update({ 
          sort_order: item.sort_order,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id)
    );

    const results = await Promise.all(updatePromises);
    
    // 检查是否有错误
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      res.status(500).json({
        success: false,
        error: '批量更新排序失败'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '排序更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 批量删除选项项
 * DELETE /api/options-management/items/batch
 * body: { ids: number[] }
 */
router.delete('/items/batch', async (req: Request, res: Response): Promise<void> => {
  try {
    const { ids } = req.body as { ids: number[] };
    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({ success: false, error: 'ids 参数不能为空' } as ApiResponse);
      return;
    }

    const { error } = await supabase
      .from('option_items')
      .delete()
      .in('id', ids);

    if (error) {
      res.status(500).json({ success: false, error: `批量删除失败: ${error.message}` } as ApiResponse);
      return;
    }

    res.json({ success: true } as ApiResponse);
  } catch (error) {
    res.status(500).json({ success: false, error: '服务器内部错误' } as ApiResponse);
  }
});

/**
 * 批量更新选项项状态
 * PUT /api/options-management/items/batch-status
 * body: { ids: number[], is_active: boolean }
 */
router.put('/items/batch-status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { ids, is_active } = req.body as { ids: number[]; is_active: boolean };
    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({ success: false, error: 'ids 参数不能为空' } as ApiResponse);
      return;
    }

    if (typeof is_active !== 'boolean') {
      res.status(400).json({ success: false, error: 'is_active 必须为布尔值' } as ApiResponse);
      return;
    }

    const { error } = await supabase
      .from('option_items')
      .update({ is_active })
      .in('id', ids);

    if (error) {
      res.status(500).json({ success: false, error: `批量更新状态失败: ${error.message}` } as ApiResponse);
      return;
    }

    res.json({ success: true } as ApiResponse);
  } catch (error) {
    res.status(500).json({ success: false, error: '服务器内部错误' } as ApiResponse);
  }
});

export default router;