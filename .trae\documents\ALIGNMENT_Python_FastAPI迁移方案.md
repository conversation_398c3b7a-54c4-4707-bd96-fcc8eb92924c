# ALIGNMENT - Python+FastAPI架构迁移方案

## 1. 项目上下文分析

### 1.1 现有项目架构
- **后端技术栈**: Node.js + Express + TypeScript
- **前端技术栈**: Vue 3 + TypeScript + Pinia + Naive UI + Vite
- **数据库**: MySQL + Supabase (双数据库架构)
- **部署**: 前后端分离架构
- **API端口**: 后端3001，前端8080

### 1.2 现有功能模块
- **认证系统**: 用户登录、注册、权限管理
- **客户管理**: CRUD操作、搜索、分页、批量操作、导入导出
- **跟进管理**: 跟进记录、见面记录、公海池管理
- **选项管理**: 系统配置选项管理
- **文件上传**: 支持Excel导入、图片上传

### 1.3 现有API路由结构
```
/api/auth/*          - 认证相关API
/api/customer/*      - 客户管理API
/api/options/*       - 选项查询API
/api/options-management/* - 选项管理API
/api/health          - 健康检查
```

### 1.4 数据库结构
- **用户管理**: users, departments
- **客户管理**: customers, customer_tags, customer_tag_relations
- **跟进管理**: follow_records, meeting_records
- **公海池**: customer_pool_records
- **营销活动**: marketing_campaigns
- **系统配置**: 各种选项表

## 2. 原始需求分析

### 2.1 迁移需求
用户要求将现有项目的后端架构从 **Node.js/Express** 迁移到 **Python+FastAPI**，同时保持：
- 数据结构不变
- 前端不变
- API接口兼容性

### 2.2 迁移目标
- 使用Python+FastAPI重写所有后端API
- 保持与前端的完全兼容性
- 维持现有的数据库结构和数据
- 保持相同的API响应格式
- 保持相同的错误处理机制

## 3. 边界确认

### 3.1 迁移范围
**包含**:
- 所有API路由的Python+FastAPI重写
- 数据库连接和ORM配置
- 认证和权限系统
- 文件上传处理
- 错误处理和验证中间件
- 测试用例迁移

**不包含**:
- 前端代码修改
- 数据库结构变更
- 新功能开发
- 部署配置优化

### 3.2 技术约束
- 必须使用FastAPI框架
- 保持MySQL数据库连接
- 保持现有的API接口规范
- 保持现有的响应数据格式
- 支持现有的文件上传功能

## 4. 需求理解

### 4.1 对现有项目的理解
1. **架构模式**: 典型的前后端分离架构，使用RESTful API
2. **数据访问**: 使用自定义的MySQLManager类进行数据库操作
3. **认证机制**: 基于Token的简单认证（admin/password硬编码）
4. **错误处理**: 统一的错误处理中间件和响应格式
5. **验证机制**: 自定义的验证中间件
6. **文件处理**: 使用multer处理文件上传，支持Excel解析

### 4.2 技术特点
- 使用TypeScript提供类型安全
- 模块化的路由设计
- 完善的错误处理机制
- 支持事务处理
- 批量操作支持
- 分页和搜索功能

## 5. 疑问澄清

### 5.1 已明确的技术决策
1. **ORM选择**: 使用SQLAlchemy作为Python的ORM
2. **数据验证**: 使用Pydantic进行数据验证
3. **异步支持**: 使用FastAPI的异步特性
4. **文件处理**: 使用python-multipart和pandas处理文件上传
5. **数据库连接**: 使用aiomysql或asyncpg进行异步数据库连接

### 5.2 需要确认的关键决策点

#### 5.2.1 认证系统迁移策略
**问题**: 现有系统使用简单的硬编码认证，是否需要改进？
**建议**: 保持现有的简单认证机制，确保前端兼容性

#### 5.2.2 依赖管理
**问题**: Python项目的依赖管理工具选择？
**建议**: 使用requirements.txt或poetry进行依赖管理

#### 5.2.3 项目结构
**问题**: Python项目的目录结构如何组织？
**建议**: 采用标准的FastAPI项目结构，保持与现有Node.js结构的对应关系

#### 5.2.4 环境配置
**问题**: 如何处理环境变量和配置？
**建议**: 使用python-dotenv加载.env文件，保持与现有配置的兼容性

#### 5.2.5 测试策略
**问题**: 如何迁移现有的测试用例？
**建议**: 使用pytest重写测试用例，保持相同的测试覆盖率

## 6. 技术实现方案预览

### 6.1 核心技术栈
- **Web框架**: FastAPI
- **ORM**: SQLAlchemy + aiomysql
- **数据验证**: Pydantic
- **文件处理**: python-multipart + pandas + openpyxl
- **配置管理**: python-dotenv
- **测试框架**: pytest + httpx

### 6.2 项目结构设计
```
api_python/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models/              # SQLAlchemy模型
│   ├── schemas/             # Pydantic模式
│   ├── routers/             # API路由
│   ├── middleware/          # 中间件
│   ├── utils/               # 工具函数
│   └── tests/               # 测试用例
├── requirements.txt         # 依赖列表
├── .env                     # 环境配置
└── README.md               # 项目说明
```

### 6.3 API兼容性保证
- 保持相同的URL路径结构
- 保持相同的HTTP方法
- 保持相同的请求/响应数据格式
- 保持相同的状态码和错误消息

## 7. 验收标准

### 7.1 功能验收
- [ ] 所有现有API接口正常工作
- [ ] 前端应用无需修改即可正常使用
- [ ] 数据库操作功能完整
- [ ] 文件上传功能正常
- [ ] 认证和权限控制正常

### 7.2 性能验收
- [ ] API响应时间不超过现有系统的1.5倍
- [ ] 支持相同的并发用户数
- [ ] 内存使用合理

### 7.3 质量验收
- [ ] 代码覆盖率达到80%以上
- [ ] 所有API接口有对应的测试用例
- [ ] 代码符合Python PEP8规范
- [ ] 完整的错误处理和日志记录

## 8. 风险评估

### 8.1 技术风险
- **数据类型兼容性**: Python和Node.js的数据类型差异
- **异步处理差异**: FastAPI和Express的异步模型差异
- **第三方库兼容性**: 文件处理、数据库连接等库的差异

### 8.2 迁移风险
- **API兼容性**: 确保完全的向后兼容
- **数据一致性**: 迁移过程中的数据安全
- **测试覆盖**: 确保所有功能都有充分测试

### 8.3 风险缓解策略
- 分阶段迁移，逐个模块验证
- 建立完整的测试套件
- 保留原有系统作为备份
- 详细的迁移文档和回滚计划