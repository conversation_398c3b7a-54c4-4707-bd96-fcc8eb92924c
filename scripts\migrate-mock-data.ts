import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

// Supabase 配置
const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置信息')
  console.error('请确保设置了以下环境变量:')
  console.error('- VITE_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// 模拟客户数据 - 简化版本
const mockCustomers = [
  {
    name: '张三',
    phone: '13800138001',
    company: '阿里巴巴',
    position: '技术总监',
    source: '朋友介绍',
    level: 'A',
    status: 'active',
    region: '浙江省,杭州市',
    address: '杭州市西湖区文三路',
    notes: '技术背景强，有合作意向',
    tags: 'VIP,技术'
  },
  {
    name: '李四',
    phone: '13800138002',
    company: '腾讯科技',
    position: '产品经理',
    source: '网络推广',
    level: 'B',
    status: 'active',
    region: '广东省,深圳市',
    address: '深圳市南山区科技园',
    notes: '对产品很感兴趣',
    tags: '产品,互联网'
  },
  {
    name: '王五',
    phone: '13800138003',
    company: '百度',
    position: '运营总监',
    source: '展会',
    level: 'A',
    status: 'active',
    region: '北京市,海淀区',
    address: '北京市海淀区上地十街',
    notes: '运营经验丰富',
    tags: '运营,大厂'
  }
]

// 模拟营销活动数据 - 根据实际表结构调整
const mockCampaigns = [
  {
    name: '春季产品发布会',
    description: '2024年春季新产品发布活动',
    type: 'event',
    status: 'active',
    start_time: '2024-03-15T09:00:00Z',
    end_time: '2024-03-15T18:00:00Z',
    budget: 50000,
    spent: 45000,
    target_audience: { type: '企业客户', industry: ['科技', '金融'] },
    rules: { max_participants: 200, registration_required: true },
    prizes: [{ name: '一等奖', value: 5000, quantity: 1 }, { name: '二等奖', value: 2000, quantity: 3 }],
    participant_count: 180,
    conversion_count: 28,
    share_count: 45,
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    name: '夏季促销活动',
    description: '夏季产品优惠促销',
    type: 'promotion',
    status: 'completed',
    start_time: '2024-06-01T00:00:00Z',
    end_time: '2024-06-30T23:59:59Z',
    budget: 30000,
    spent: 28500,
    target_audience: { type: '个人用户', age_range: ['25-35', '36-45'] },
    rules: { discount_rate: 0.2, min_purchase: 100 },
    prizes: [{ name: '优惠券', value: 50, quantity: 100 }],
    participant_count: 650,
    conversion_count: 83,
    share_count: 120,
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    name: '秋季会员招募',
    description: '秋季新会员招募活动',
    type: 'membership',
    status: 'draft',
    start_time: '2024-09-01T00:00:00Z',
    end_time: '2024-09-30T23:59:59Z',
    budget: 20000,
    spent: 0,
    target_audience: { type: '潜在客户', source: ['网络', '推荐'] },
    rules: { membership_fee: 99, benefits: ['专属折扣', '优先服务'] },
    prizes: [{ name: '会员礼包', value: 200, quantity: 50 }],
    participant_count: 0,
    conversion_count: 0,
    share_count: 0,
    created_by: '00000000-0000-0000-0000-000000000000'
  }
]

async function migrateCustomers() {
  try {
    console.log('🔄 开始迁移客户数据...')
    
    // 清空现有数据（可选）
    await supabase.from('customers').delete().neq('id', 0)
    
    // 插入客户数据
    const { data, error } = await supabase
      .from('customers')
      .insert(mockCustomers)
      .select()
    
    if (error) {
      console.error('❌ 客户数据迁移失败:', error.message)
      return false
    }
    
    console.log(`✅ 成功迁移 ${data.length} 条客户数据`)
    return true
  } catch (error) {
    console.error('❌ 客户数据迁移异常:', error)
    return false
  }
}

async function migrateCampaigns() {
  try {
    console.log('🔄 开始迁移营销活动数据...')
    
    // 清空现有数据（可选）
    await supabase.from('marketing_campaigns').delete().neq('id', 0)
    
    // 插入营销活动数据
    const { data, error } = await supabase
      .from('marketing_campaigns')
      .insert(mockCampaigns)
      .select()
    
    if (error) {
      console.error('❌ 营销活动数据迁移失败:', error.message)
      return false
    }
    
    console.log(`✅ 成功迁移 ${data.length} 条营销活动数据`)
    return true
  } catch (error) {
    console.error('❌ 营销活动数据迁移异常:', error)
    return false
  }
}

async function validateMigration() {
  try {
    console.log('🔄 验证迁移结果...')
    
    // 检查客户数据
    const { data: customers, error: customerError } = await supabase
      .from('customers')
      .select('count')
    
    if (customerError) {
      console.error('❌ 客户数据验证失败:', customerError.message)
      return false
    }
    
    // 检查营销活动数据
    const { data: campaigns, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('count')
    
    if (campaignError) {
      console.error('❌ 营销活动数据验证失败:', campaignError.message)
      return false
    }
    
    console.log('✅ 数据验证完成')
    console.log(`   - 客户数据: ${customers?.length || 0} 条`)
    console.log(`   - 营销活动: ${campaigns?.length || 0} 条`)
    
    return true
  } catch (error) {
    console.error('❌ 数据验证异常:', error)
    return false
  }
}

async function main() {
  console.log('🚀 开始数据迁移\n')
  
  // 迁移客户数据
  const customersOk = await migrateCustomers()
  if (!customersOk) {
    console.error('❌ 客户数据迁移失败，停止执行')
    process.exit(1)
  }
  
  // 迁移营销活动数据
  const campaignsOk = await migrateCampaigns()
  if (!campaignsOk) {
    console.error('❌ 营销活动数据迁移失败，停止执行')
    process.exit(1)
  }
  
  // 验证迁移结果
  const validationOk = await validateMigration()
  if (!validationOk) {
    console.error('❌ 数据验证失败')
    process.exit(1)
  }
  
  console.log('\n🎉 数据迁移完成！所有测试数据已成功导入到 Supabase 数据库。')
  console.log('\n📝 迁移摘要:')
  console.log('   - ✅ 客户数据迁移完成')
  console.log('   - ✅ 营销活动数据迁移完成')
  console.log('   - ✅ 数据完整性验证通过')
  console.log('\n🔗 现在前端可以通过 Supabase 客户端调用这些测试数据了！')
}

// 执行迁移
main().catch(console.error)