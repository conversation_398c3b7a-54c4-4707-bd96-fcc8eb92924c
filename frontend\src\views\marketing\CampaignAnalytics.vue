<template>
  <div class="campaign-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <p class="page-description">分析营销活动的效果数据，生成深度洞察报告</p>
      </div>
      <div class="header-right">
        <n-space>
          <n-button type="primary" @click="exportReport">
            <template #icon>
              <n-icon><Download /></n-icon>
            </template>
            导出报告
          </n-button>
          <n-button type="info" @click="showCompareModal = true">
            <template #icon>
              <n-icon><GitCompare /></n-icon>
            </template>
            对比分析
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 活动选择和时间范围 -->
    <n-card class="filter-card">
      <n-space align="center">
        <span>选择活动：</span>
        <n-select
          v-model:value="selectedCampaignId"
          placeholder="请选择营销活动"
          style="width: 250px"
          :options="campaignOptions"
          @update:value="handleCampaignChange"
        />
        <span>时间范围：</span>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          placeholder="选择分析时间范围"
          style="width: 240px"
          @update:value="handleDateRangeChange"
        />
        <n-button type="primary" @click="refreshAnalytics">
          <template #icon>
            <n-icon><Refresh /></n-icon>
          </template>
          刷新数据
        </n-button>
      </n-space>
    </n-card>

    <!-- 核心指标概览 -->
    <div v-if="selectedCampaign" class="metrics-overview">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="总参与人数" :value="metrics.totalParticipants">
              <template #prefix>
                <n-icon color="#18a058">
                  <People />
                </n-icon>
              </template>
            </n-statistic>
            <div class="metric-trend">
              <n-tag :type="metrics.participantsTrend > 0 ? 'success' : 'error'" size="small">
                {{ metrics.participantsTrend > 0 ? '+' : '' }}{{ metrics.participantsTrend }}%
              </n-tag>
              <span class="trend-text">较上期</span>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="转化率" :value="metrics.conversionRate" suffix="%">
              <template #prefix>
                <n-icon color="#2080f0">
                  <TrendingUp />
                </n-icon>
              </template>
            </n-statistic>
            <div class="metric-trend">
              <n-tag :type="metrics.conversionTrend > 0 ? 'success' : 'error'" size="small">
                {{ metrics.conversionTrend > 0 ? '+' : '' }}{{ metrics.conversionTrend }}%
              </n-tag>
              <span class="trend-text">较上期</span>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="总收入" :value="metrics.totalRevenue" prefix="¥">
              <template #prefix>
                <n-icon color="#f0a020">
                  <Cash />
                </n-icon>
              </template>
            </n-statistic>
            <div class="metric-trend">
              <n-tag :type="metrics.revenueTrend > 0 ? 'success' : 'error'" size="small">
                {{ metrics.revenueTrend > 0 ? '+' : '' }}{{ metrics.revenueTrend }}%
              </n-tag>
              <span class="trend-text">较上期</span>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="ROI" :value="metrics.roi" suffix="%">
              <template #prefix>
                <n-icon color="#d03050">
                  <Trophy />
                </n-icon>
              </template>
            </n-statistic>
            <div class="metric-trend">
              <n-tag :type="metrics.roiTrend > 0 ? 'success' : 'error'" size="small">
                {{ metrics.roiTrend > 0 ? '+' : '' }}{{ metrics.roiTrend }}%
              </n-tag>
              <span class="trend-text">较上期</span>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 图表分析 -->
    <div v-if="selectedCampaign" class="charts-section">
      <n-grid :cols="2" :x-gap="16" :y-gap="16">
        <!-- 参与趋势图 -->
        <n-grid-item>
          <n-card title="参与趋势分析">
            <div class="chart-container">
              <canvas ref="participationChart" width="400" height="200"></canvas>
            </div>
          </n-card>
        </n-grid-item>
        
        <!-- 转化漏斗图 -->
        <n-grid-item>
          <n-card title="转化漏斗分析">
            <div class="chart-container">
              <canvas ref="funnelChart" width="400" height="200"></canvas>
            </div>
          </n-card>
        </n-grid-item>
        
        <!-- 渠道分析 -->
        <n-grid-item>
          <n-card title="渠道效果分析">
            <div class="chart-container">
              <canvas ref="channelChart" width="400" height="200"></canvas>
            </div>
          </n-card>
        </n-grid-item>
        
        <!-- 用户行为分析 -->
        <n-grid-item>
          <n-card title="用户行为分析">
            <div class="chart-container">
              <canvas ref="behaviorChart" width="400" height="200"></canvas>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 详细数据表格 -->
    <div v-if="selectedCampaign" class="data-tables">
      <n-tabs type="line" animated>
        <n-tab-pane name="participants" tab="参与用户">
          <n-data-table
            :columns="participantColumns"
            :data="participantData"
            :loading="dataLoading"
            :pagination="participantPagination"
          />
        </n-tab-pane>
        
        <n-tab-pane name="conversions" tab="转化记录">
          <n-data-table
            :columns="conversionColumns"
            :data="conversionData"
            :loading="dataLoading"
            :pagination="conversionPagination"
          />
        </n-tab-pane>
        
        <n-tab-pane name="channels" tab="渠道数据">
          <n-data-table
            :columns="channelColumns"
            :data="channelData"
            :loading="dataLoading"
            :pagination="false"
          />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 洞察报告 -->
    <div v-if="selectedCampaign" class="insights-section">
      <n-card title="数据洞察">
        <n-space vertical size="large">
          <div class="insight-item">
            <h4>关键发现</h4>
            <n-ul>
              <n-li v-for="finding in insights.keyFindings" :key="finding.id">
                <n-tag :type="finding.type" size="small">{{ finding.category }}</n-tag>
                {{ finding.description }}
              </n-li>
            </n-ul>
          </div>
          
          <div class="insight-item">
            <h4>优化建议</h4>
            <n-ul>
              <n-li v-for="suggestion in insights.suggestions" :key="suggestion.id">
                <n-tag type="info" size="small">{{ suggestion.priority }}</n-tag>
                {{ suggestion.description }}
              </n-li>
            </n-ul>
          </div>
          
          <div class="insight-item">
            <h4>风险提醒</h4>
            <n-ul>
              <n-li v-for="risk in insights.risks" :key="risk.id">
                <n-tag type="warning" size="small">{{ risk.level }}</n-tag>
                {{ risk.description }}
              </n-li>
            </n-ul>
          </div>
        </n-space>
      </n-card>
    </div>

    <!-- 对比分析弹窗 -->
    <n-modal v-model:show="showCompareModal" preset="dialog" title="对比分析" style="width: 800px">
      <n-form label-placement="left" label-width="100px">
        <n-form-item label="对比活动">
          <n-select
            v-model:value="compareForm.campaignIds"
            placeholder="选择要对比的活动（最多3个）"
            multiple
            :max-tag-count="3"
            :options="campaignOptions"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="对比维度">
          <n-checkbox-group v-model:value="compareForm.dimensions">
            <n-checkbox value="participation">参与度</n-checkbox>
            <n-checkbox value="conversion">转化率</n-checkbox>
            <n-checkbox value="revenue">收入</n-checkbox>
            <n-checkbox value="roi">ROI</n-checkbox>
            <n-checkbox value="channels">渠道效果</n-checkbox>
          </n-checkbox-group>
        </n-form-item>
        <n-form-item label="时间维度">
          <n-radio-group v-model:value="compareForm.timeframe">
            <n-radio value="daily">按天</n-radio>
            <n-radio value="weekly">按周</n-radio>
            <n-radio value="monthly">按月</n-radio>
          </n-radio-group>
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showCompareModal = false">取消</n-button>
          <n-button type="primary" @click="handleCompareAnalysis">开始对比</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  Download,
  GitCompare,
  Refresh,
  People,
  TrendingUp,
  Cash,
  Trophy
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

interface Campaign {
  id: number
  name: string
}

interface Metrics {
  totalParticipants: number
  participantsTrend: number
  conversionRate: number
  conversionTrend: number
  totalRevenue: number
  revenueTrend: number
  roi: number
  roiTrend: number
}

interface Participant {
  id: number
  name: string
  phone: string
  channel: string
  joinTime: string
  status: string
}

interface Conversion {
  id: number
  userId: number
  userName: string
  action: string
  value: number
  time: string
}

interface Channel {
  name: string
  participants: number
  conversions: number
  conversionRate: number
  cost: number
  roi: number
}

const message = useMessage()

// 响应式数据
const selectedCampaignId = ref<number | null>(null)
const selectedCampaign = ref<Campaign | null>(null)
const dateRange = ref<[number, number] | null>(null)
const dataLoading = ref(false)
const showCompareModal = ref(false)

// 图表引用
const participationChart = ref<HTMLCanvasElement>()
const funnelChart = ref<HTMLCanvasElement>()
const channelChart = ref<HTMLCanvasElement>()
const behaviorChart = ref<HTMLCanvasElement>()

// 表单数据
const compareForm = reactive({
  campaignIds: [] as number[],
  dimensions: ['participation', 'conversion'],
  timeframe: 'daily'
})

// 模拟数据
const campaignOptions = ref([
  { label: '春季促销活动', value: 1 },
  { label: '新品发布会', value: 2 },
  { label: '会员专享活动', value: 3 }
])

const metrics = ref<Metrics>({
  totalParticipants: 15420,
  participantsTrend: 12.5,
  conversionRate: 8.7,
  conversionTrend: -2.1,
  totalRevenue: 234567,
  revenueTrend: 18.3,
  roi: 245,
  roiTrend: 15.2
})

const participantData = ref<Participant[]>([
  {
    id: 1,
    name: '张三',
    phone: '138****1234',
    channel: '微信朋友圈',
    joinTime: '2024-01-15 10:30:00',
    status: '已转化'
  },
  {
    id: 2,
    name: '李四',
    phone: '139****5678',
    channel: '微博推广',
    joinTime: '2024-01-15 11:20:00',
    status: '未转化'
  }
])

const conversionData = ref<Conversion[]>([
  {
    id: 1,
    userId: 1,
    userName: '张三',
    action: '购买商品',
    value: 299,
    time: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    userId: 3,
    userName: '王五',
    action: '注册会员',
    value: 0,
    time: '2024-01-15 15:20:00'
  }
])

const channelData = ref<Channel[]>([
  {
    name: '微信朋友圈',
    participants: 5420,
    conversions: 472,
    conversionRate: 8.7,
    cost: 12000,
    roi: 245
  },
  {
    name: '微博推广',
    participants: 3210,
    conversions: 198,
    conversionRate: 6.2,
    cost: 8000,
    roi: 156
  },
  {
    name: '抖音广告',
    participants: 6790,
    conversions: 543,
    conversionRate: 8.0,
    cost: 15000,
    roi: 198
  }
])

const insights = ref({
  keyFindings: [
    {
      id: 1,
      category: '参与度',
      type: 'success',
      description: '活动参与人数较上期增长12.5%，表现优异'
    },
    {
      id: 2,
      category: '转化率',
      type: 'warning',
      description: '转化率较上期下降2.1%，需要优化转化流程'
    },
    {
      id: 3,
      category: '渠道',
      type: 'info',
      description: '抖音广告渠道表现突出，建议加大投入'
    }
  ],
  suggestions: [
    {
      id: 1,
      priority: '高',
      description: '优化活动页面加载速度，减少用户流失'
    },
    {
      id: 2,
      priority: '中',
      description: '增加客服引导，提升转化率'
    },
    {
      id: 3,
      priority: '低',
      description: '丰富活动奖品，提升参与积极性'
    }
  ],
  risks: [
    {
      id: 1,
      level: '中',
      description: '部分渠道成本上升，需要控制预算'
    },
    {
      id: 2,
      level: '低',
      description: '竞品活动增多，可能影响效果'
    }
  ]
})

// 分页配置
const participantPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 100,
  showSizePicker: true
})

const conversionPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 50,
  showSizePicker: true
})

// 表格列定义
const participantColumns: DataTableColumns<Participant> = [
  { title: '用户姓名', key: 'name', width: 120 },
  { title: '手机号', key: 'phone', width: 140 },
  { title: '来源渠道', key: 'channel', width: 120 },
  { title: '参与时间', key: 'joinTime', width: 160 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h('n-tag', {
        type: row.status === '已转化' ? 'success' : 'default'
      }, { default: () => row.status })
    }
  }
]

const conversionColumns: DataTableColumns<Conversion> = [
  { title: '用户姓名', key: 'userName', width: 120 },
  { title: '转化行为', key: 'action', width: 120 },
  {
    title: '转化价值',
    key: 'value',
    width: 120,
    render(row) {
      return row.value > 0 ? `¥${row.value}` : '-'
    }
  },
  { title: '转化时间', key: 'time', width: 160 }
]

const channelColumns: DataTableColumns<Channel> = [
  { title: '渠道名称', key: 'name', width: 120 },
  { title: '参与人数', key: 'participants', width: 100 },
  { title: '转化人数', key: 'conversions', width: 100 },
  {
    title: '转化率',
    key: 'conversionRate',
    width: 100,
    render(row) {
      return `${row.conversionRate}%`
    }
  },
  {
    title: '投入成本',
    key: 'cost',
    width: 120,
    render(row) {
      return `¥${row.cost.toLocaleString()}`
    }
  },
  {
    title: 'ROI',
    key: 'roi',
    width: 100,
    render(row) {
      return `${row.roi}%`
    }
  }
]

// 方法
const handleCampaignChange = (campaignId: number) => {
  const campaign = campaignOptions.value.find(c => c.value === campaignId)
  if (campaign) {
    selectedCampaign.value = {
      id: campaignId,
      name: campaign.label
    }
    loadAnalyticsData()
    nextTick(() => {
      renderCharts()
    })
  }
}

const handleDateRangeChange = () => {
  if (selectedCampaign.value) {
    loadAnalyticsData()
  }
}

const refreshAnalytics = () => {
  if (selectedCampaign.value) {
    loadAnalyticsData()
    renderCharts()
    message.success('数据已刷新')
  } else {
    message.warning('请先选择活动')
  }
}

const loadAnalyticsData = async () => {
  dataLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    dataLoading.value = false
  }
}

const renderCharts = () => {
  // 这里应该使用真实的图表库如 Chart.js 或 ECharts
  // 为了演示，我们只是简单地在canvas上绘制一些内容
  
  if (participationChart.value) {
    const ctx = participationChart.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, 400, 200)
      ctx.fillStyle = '#18a058'
      ctx.fillRect(50, 150, 300, 30)
      ctx.fillStyle = '#000'
      ctx.font = '14px Arial'
      ctx.fillText('参与趋势图 (演示)', 150, 100)
    }
  }
  
  if (funnelChart.value) {
    const ctx = funnelChart.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, 400, 200)
      ctx.fillStyle = '#2080f0'
      ctx.fillRect(100, 50, 200, 30)
      ctx.fillRect(120, 90, 160, 30)
      ctx.fillRect(140, 130, 120, 30)
      ctx.fillStyle = '#000'
      ctx.font = '14px Arial'
      ctx.fillText('转化漏斗图 (演示)', 150, 30)
    }
  }
  
  if (channelChart.value) {
    const ctx = channelChart.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, 400, 200)
      ctx.fillStyle = '#f0a020'
      ctx.beginPath()
      ctx.arc(200, 100, 60, 0, 2 * Math.PI)
      ctx.fill()
      ctx.fillStyle = '#000'
      ctx.font = '14px Arial'
      ctx.fillText('渠道分析图 (演示)', 150, 180)
    }
  }
  
  if (behaviorChart.value) {
    const ctx = behaviorChart.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, 400, 200)
      ctx.fillStyle = '#d03050'
      ctx.fillRect(50, 50, 50, 100)
      ctx.fillRect(120, 70, 50, 80)
      ctx.fillRect(190, 90, 50, 60)
      ctx.fillRect(260, 110, 50, 40)
      ctx.fillStyle = '#000'
      ctx.font = '14px Arial'
      ctx.fillText('用户行为图 (演示)', 150, 30)
    }
  }
}

const exportReport = () => {
  if (!selectedCampaign.value) {
    message.warning('请先选择活动')
    return
  }
  message.success('报告导出功能开发中')
}

const handleCompareAnalysis = () => {
  if (compareForm.campaignIds.length < 2) {
    message.warning('请至少选择2个活动进行对比')
    return
  }
  message.success('对比分析功能开发中')
  showCompareModal.value = false
}

// 生命周期
onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.metrics-overview {
  margin-bottom: 20px;
}

.metric-trend {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-text {
  font-size: 12px;
  color: #666;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.data-tables {
  margin-bottom: 20px;
}

.insights-section {
  margin-bottom: 20px;
}

.insight-item {
  margin-bottom: 16px;
}

.insight-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}
</style>