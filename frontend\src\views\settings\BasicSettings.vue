<template>
  <div class="basic-settings">

    <n-card>
      <n-tabs type="line" animated>
        <n-tab-pane name="basic" tab="基础设置">
          <BasicSettingsForm />
        </n-tab-pane>
        <n-tab-pane name="tracking" tab="跟踪配置">
          <TrackingConfig />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NTabs, NTabPane } from 'naive-ui'
import PageHeader from '@/components/common/PageHeader.vue'
import BasicSettingsForm from './components/BasicSettingsForm.vue'
import TrackingConfig from './TrackingConfig.vue'
</script>

<style scoped>
.basic-settings {
  padding: 0;
}
</style>