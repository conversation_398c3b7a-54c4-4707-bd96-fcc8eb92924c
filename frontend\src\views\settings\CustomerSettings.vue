<template>
  <div class="customer-settings">
    
    <div class="settings-content">
      <n-form
      ref="customerFormRef"
      :model="customerSettings"
      :rules="customerRules"
      label-placement="left"
      label-width="150px"
      class="settings-form"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="默认客户等级" path="defaultCustomerLevel">
          <n-select
            v-model:value="customerSettings.defaultCustomerLevel"
            placeholder="请选择默认客户等级"
            :options="customerLevelOptions"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="客户编号前缀" path="customerCodePrefix">
          <n-input
            v-model:value="customerSettings.customerCodePrefix"
            placeholder="请输入客户编号前缀"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="自动分配销售" path="autoAssignSales">
          <n-switch v-model:value="customerSettings.autoAssignSales" />
        </n-form-item-gi>
        
        <n-form-item-gi label="客户来源管理">
          <n-button type="info" @click="showSourceModal = true">
            <template #icon>
              <n-icon><settings-outline /></n-icon>
            </template>
            管理来源
          </n-button>
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="客户状态管理">
          <n-button type="info" @click="showStatusModal = true">
            <template #icon>
              <n-icon><settings-outline /></n-icon>
            </template>
            管理状态
          </n-button>
        </n-form-item-gi>
        
        <n-form-item-gi label="客户标签管理">
          <n-button type="info" @click="showTagModal = true">
            <template #icon>
              <n-icon><settings-outline /></n-icon>
            </template>
            管理标签
          </n-button>
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="客户等级配置">
        <n-data-table
          :columns="levelColumns"
          :data="customerLevels"
          :pagination="false"
          size="small"
        />
        <n-space style="margin-top: 16px;">
          <n-button type="primary" @click="addLevel">
            <template #icon>
              <n-icon><add-outline /></n-icon>
            </template>
            添加等级
          </n-button>
        </n-space>
      </n-form-item>
      
      <n-form-item>
        <n-space>
          <n-button type="primary" @click="handleSave">
            <template #icon>
              <n-icon><save-outline /></n-icon>
            </template>
            保存设置
          </n-button>
          <n-button type="default" @click="handleReset">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>

    <!-- 客户来源管理弹窗 -->
    <n-modal v-model:show="showSourceModal" preset="dialog" title="客户来源管理">
      <n-space vertical>
        <n-input-group>
          <n-input v-model:value="newSource" placeholder="输入新来源" />
          <n-button type="primary" @click="addSource">添加</n-button>
        </n-input-group>
        <n-list>
          <n-list-item v-for="(source, index) in customerSources" :key="index">
            <n-space justify="space-between" style="width: 100%;">
              <span>{{ source }}</span>
              <n-button size="small" type="error" @click="removeSource(index)">删除</n-button>
            </n-space>
          </n-list-item>
        </n-list>
      </n-space>
    </n-modal>

    <!-- 客户状态管理弹窗 -->
    <n-modal v-model:show="showStatusModal" preset="dialog" title="客户状态管理">
      <n-space vertical>
        <n-input-group>
          <n-input v-model:value="newStatus" placeholder="输入新状态" />
          <n-button type="primary" @click="addStatus">添加</n-button>
        </n-input-group>
        <n-list>
          <n-list-item v-for="(status, index) in customerStatuses" :key="index">
            <n-space justify="space-between" style="width: 100%;">
              <span>{{ status }}</span>
              <n-button size="small" type="error" @click="removeStatus(index)">删除</n-button>
            </n-space>
          </n-list-item>
        </n-list>
      </n-space>
    </n-modal>

    <!-- 客户标签管理弹窗 -->
    <n-modal v-model:show="showTagModal" preset="dialog" title="客户标签管理">
      <n-space vertical>
        <n-input-group>
          <n-input v-model:value="newTag" placeholder="输入新标签" />
          <n-button type="primary" @click="addTag">添加</n-button>
        </n-input-group>
        <n-list>
          <n-list-item v-for="(tag, index) in customerTags" :key="index">
            <n-space justify="space-between" style="width: 100%;">
              <span>{{ tag }}</span>
              <n-button size="small" type="error" @click="removeTag(index)">删除</n-button>
            </n-space>
          </n-list-item>
        </n-list>
      </n-space>
    </n-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { 
  NForm, NFormItem, NFormItemGi, NGrid, NInput, NInputGroup, NButton, NIcon, NSpace, 
  NSelect, NSwitch, NDataTable, NModal, NList, NListItem, useMessage 
} from 'naive-ui'
import { SaveOutline, RefreshOutline, SettingsOutline, AddOutline, TrashOutline } from '@vicons/ionicons5'
import { createDiscreteApi } from 'naive-ui'
import { markRaw } from 'vue'

const { message } = createDiscreteApi(['message'])

interface CustomerSettings {
  defaultCustomerLevel: string
  customerCodePrefix: string
  autoAssignSales: boolean
}

const customerFormRef = ref()
const showSourceModal = ref(false)
const showStatusModal = ref(false)
const showTagModal = ref(false)
const newSource = ref('')
const newStatus = ref('')
const newTag = ref('')

const customerSettings = reactive<CustomerSettings>({
  defaultCustomerLevel: 'normal',
  customerCodePrefix: 'CUS',
  autoAssignSales: true
})

const customerLevelOptions = [
  { label: '普通客户', value: 'normal' },
  { label: 'VIP客户', value: 'vip' },
  { label: '重要客户', value: 'important' },
  { label: '潜在客户', value: 'potential' }
]

const customerLevels = ref([
  { id: 1, name: '普通客户', value: 'normal', description: '一般客户' },
  { id: 2, name: 'VIP客户', value: 'vip', description: '重要客户' },
  { id: 3, name: '重要客户', value: 'important', description: '核心客户' },
  { id: 4, name: '潜在客户', value: 'potential', description: '潜在客户' }
])

const customerSources = ref(['网站注册', '电话咨询', '朋友推荐', '广告投放', '展会活动'])
const customerStatuses = ref(['潜在客户', '意向客户', '成交客户', '流失客户'])
const customerTags = ref(['高价值', '活跃用户', '新客户', '老客户', '重点关注'])

const levelColumns = [
  { title: '等级名称', key: 'name' },
  { title: '等级值', key: 'value' },
  { title: '描述', key: 'description' },
  {
    title: '操作',
    key: 'actions',
    render: (row: any) => {
      return h(NSpace, null, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'info',
            onClick: () => editLevel(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => deleteLevel(row.id)
          }, { default: () => '删除' })
        ]
      })
    }
  }
]

const customerRules = {
  defaultCustomerLevel: {
    required: true,
    message: '请选择默认客户等级',
    trigger: 'change'
  },
  customerCodePrefix: {
    required: true,
    message: '请输入客户编号前缀',
    trigger: 'blur'
  }
}

const addSource = () => {
  if (newSource.value.trim()) {
    customerSources.value.push(newSource.value.trim())
    newSource.value = ''
    message.success('添加成功')
  }
}

const removeSource = (index: number) => {
  customerSources.value.splice(index, 1)
  message.success('删除成功')
}

const addStatus = () => {
  if (newStatus.value.trim()) {
    customerStatuses.value.push(newStatus.value.trim())
    newStatus.value = ''
    message.success('添加成功')
  }
}

const removeStatus = (index: number) => {
  customerStatuses.value.splice(index, 1)
  message.success('删除成功')
}

const addTag = () => {
  if (newTag.value.trim()) {
    customerTags.value.push(newTag.value.trim())
    newTag.value = ''
    message.success('添加成功')
  }
}

const removeTag = (index: number) => {
  customerTags.value.splice(index, 1)
  message.success('删除成功')
}

const addLevel = () => {
  // TODO: 实现添加等级功能
  message.info('添加等级功能开发中')
}

const editLevel = (row: any) => {
  // TODO: 实现编辑等级功能
  message.info('编辑等级功能开发中')
}

const deleteLevel = (id: number) => {
  // TODO: 实现删除等级功能
  message.info('删除等级功能开发中')
}

const handleSave = async () => {
  try {
    await customerFormRef.value?.validate()
    // TODO: 调用API保存设置
    message.success('客户设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const handleReset = () => {
  customerFormRef.value?.restoreValidation()
  Object.assign(customerSettings, {
    defaultCustomerLevel: 'normal',
    customerCodePrefix: 'CUS',
    autoAssignSales: true
  })
}
</script>

<style scoped>
.customer-settings {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.settings-content {
  background: var(--n-card-color);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-form {
  max-width: 800px;
}

/* 操作按钮统一样式 */
:deep(.n-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

:deep(.n-button--primary-type:hover) {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

:deep(.n-button--info-type) {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

:deep(.n-button--info-type:hover) {
  background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
}

:deep(.n-button--error-type) {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

:deep(.n-button--error-type:hover) {
  background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
}

:deep(.n-button--default-type) {
  background: var(--n-button-color);
  border: 1px solid var(--n-border-color);
}

:deep(.n-button--default-type:hover) {
  background: var(--n-button-color-hover);
  border-color: var(--n-border-color-hover);
  transform: translateY(-1px);
}

/* 小尺寸按钮样式 */
:deep(.n-button--small-type) {
  padding: 4px 12px;
  font-size: 12px;
}
</style>