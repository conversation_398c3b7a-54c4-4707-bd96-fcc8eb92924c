#!/usr/bin/env python3
"""
添加password_hash字段并迁移数据

这个脚本将：
1. 添加password_hash字段到users表
2. 将现有的password字段数据复制到password_hash字段
3. 删除旧的password字段
"""

import pymysql
from app.config import settings
from app.utils.security import get_password_hash

def main():
    # settings已经是实例，直接使用
    
    # 连接数据库
    connection = pymysql.connect(
        host=settings.database.host,
        port=settings.database.port,
        user=settings.database.username,
        password=settings.database.password,
        database=settings.database.database,
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            print("开始数据库迁移...")
            
            # 1. 检查password_hash字段是否已存在
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'password_hash'
            """, (settings.database.database,))
            
            result = cursor.fetchone()
            if result[0] > 0:
                print("password_hash字段已存在，跳过添加步骤")
            else:
                # 2. 添加password_hash字段
                print("添加password_hash字段...")
                cursor.execute("""
                    ALTER TABLE users 
                    ADD COLUMN password_hash VARCHAR(255) NULL COMMENT '密码哈希'
                """)
                print("password_hash字段添加成功")
            
            # 3. 检查是否有password字段
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'password'
            """, (settings.database.database,))
            
            result = cursor.fetchone()
            if result[0] > 0:
                print("发现password字段，开始迁移数据...")
                
                # 4. 获取所有用户的password数据
                cursor.execute("SELECT id, username, password FROM users WHERE password IS NOT NULL")
                users = cursor.fetchall()
                
                print(f"找到 {len(users)} 个用户需要迁移密码")
                
                # 5. 迁移每个用户的密码
                for user_id, username, password in users:
                    # 检查密码是否已经是bcrypt格式
                    if password.startswith('$2a$') or password.startswith('$2b$') or password.startswith('$2y$'):
                        # 已经是bcrypt格式，直接复制
                        hashed_password = password
                        print(f"用户 {username} 的密码已经是bcrypt格式，直接复制")
                    else:
                        # 明文密码，需要哈希
                        hashed_password = get_password_hash(password)
                        print(f"用户 {username} 的密码已哈希处理")
                    
                    # 更新password_hash字段
                    cursor.execute(
                        "UPDATE users SET password_hash = %s WHERE id = %s",
                        (hashed_password, user_id)
                    )
                
                print("密码迁移完成")
                
                # 6. 删除旧的password字段
                print("删除旧的password字段...")
                cursor.execute("ALTER TABLE users DROP COLUMN password")
                print("旧password字段删除成功")
            else:
                print("未发现password字段，可能已经迁移过了")
            
            # 7. 确保admin用户的密码正确
            print("检查admin用户...")
            cursor.execute("SELECT id, username, password_hash FROM users WHERE username = 'admin'")
            admin_user = cursor.fetchone()
            
            if admin_user:
                user_id, username, current_hash = admin_user
                # 生成admin123的正确哈希
                correct_hash = get_password_hash('admin123')
                
                # 更新admin用户的密码哈希
                cursor.execute(
                    "UPDATE users SET password_hash = %s WHERE id = %s",
                    (correct_hash, user_id)
                )
                print(f"admin用户密码已更新为admin123的正确哈希")
            else:
                print("未找到admin用户")
            
            # 8. 检查是否有NULL的password_hash
            cursor.execute("SELECT COUNT(*) FROM users WHERE password_hash IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"发现 {null_count} 个用户的password_hash为NULL，为其设置默认密码...")
                # 为NULL的用户设置默认密码 'password123'
                default_hash = get_password_hash('password123')
                cursor.execute(
                    "UPDATE users SET password_hash = %s WHERE password_hash IS NULL",
                    (default_hash,)
                )
                print("已为NULL用户设置默认密码: password123")
            
            # 9. 设置password_hash字段为NOT NULL
            print("设置password_hash字段为NOT NULL...")
            cursor.execute("""
                ALTER TABLE users 
                MODIFY COLUMN password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希'
            """)
            
            # 提交事务
            connection.commit()
            print("数据库迁移完成！")
            
    except Exception as e:
        print(f"迁移过程中出现错误: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()

if __name__ == "__main__":
    main()