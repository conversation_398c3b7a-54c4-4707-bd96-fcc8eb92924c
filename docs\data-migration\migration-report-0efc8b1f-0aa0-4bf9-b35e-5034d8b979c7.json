{"migrationId": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "timestamp": "2025-08-18T07:31:28.933Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 12, "failedTables": 9, "totalRecords": 165, "migratedRecords": 6, "failedRecords": 159}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:30:46.995Z", "errors": ["Duplicate entry 'e4af9ff6-7ad5-4b9c-ae03-a49484f4d95b' for key 'users.PRIMARY'", "数据验证失败: MySQL记录数(3) != 迁移记录数(0)"], "endTime": "2025-08-18T07:30:50.982Z", "duration": 3987}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T07:30:50.986Z", "errors": ["Duplicate entry '1' for key 'roles.PRIMARY'", "数据验证失败: MySQL记录数(6) != 迁移记录数(0)"], "endTime": "2025-08-18T07:30:54.448Z", "duration": 3462}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T07:30:54.452Z", "errors": ["Duplicate entry '1' for key 'permissions.PRIMARY'", "数据验证失败: MySQL记录数(77) != 迁移记录数(0)"], "endTime": "2025-08-18T07:30:56.190Z", "duration": 1738}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T07:30:56.194Z", "errors": ["Duplicate entry '1' for key 'role_permissions.PRIMARY'", "数据验证失败: MySQL记录数(6) != 迁移记录数(0)"], "endTime": "2025-08-18T07:30:58.675Z", "duration": 2481}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:30:58.678Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 0, "failedRecords": 15, "startTime": "2025-08-18T07:30:58.985Z", "errors": ["Duplicate entry '1' for key 'option_categories.PRIMARY'", "数据验证失败: MySQL记录数(15) != 迁移记录数(0)"], "endTime": "2025-08-18T07:31:00.565Z", "duration": 1580}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T07:31:00.567Z", "errors": ["Duplicate entry '1' for key 'option_items.PRIMARY'", "数据验证失败: MySQL记录数(42) != 迁移记录数(0)"], "endTime": "2025-08-18T07:31:03.101Z", "duration": 2534}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T07:31:03.104Z", "errors": ["Duplicate entry '63' for key 'customers.PRIMARY'", "数据验证失败: MySQL记录数(5) != 迁移记录数(0)"], "endTime": "2025-08-18T07:31:05.479Z", "duration": 2375}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:05.481Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:31:05.899Z", "errors": ["Column 'created_by' cannot be null"], "endTime": "2025-08-18T07:31:08.560Z", "duration": 2661}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:08.563Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:09.224Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:31:10.031Z", "errors": ["Duplicate entry '4' for key 'meetings.PRIMARY'", "数据验证失败: MySQL记录数(2) != 迁移记录数(0)"], "endTime": "2025-08-18T07:31:13.414Z", "duration": 3383}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:13.417Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:31:13.902Z", "errors": [], "endTime": "2025-08-18T07:31:15.800Z", "duration": 1898}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:15.802Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:16.149Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:17.069Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:31:18.186Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:31:19.808Z", "errors": [], "endTime": "2025-08-18T07:31:24.961Z", "duration": 5153}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 1, "failedRecords": 0, "startTime": "2025-08-18T07:31:24.975Z", "errors": [], "endTime": "2025-08-18T07:31:28.931Z", "duration": 3956}], "logs": [{"id": "888069c3-2aae-4954-b4a6-7e06955cf9bc", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:46.995Z", "end_time": "2025-08-18T07:30:50.982Z", "duration_ms": 3987}, {"id": "19a3fa06-4977-402a-ae31-a69b35ddce0b", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:50.986Z", "end_time": "2025-08-18T07:30:54.448Z", "duration_ms": 3462}, {"id": "03053fd9-5b84-493b-ac62-b3fb64b842cb", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:54.452Z", "end_time": "2025-08-18T07:30:56.190Z", "duration_ms": 1738}, {"id": "6e8addb2-ab8c-4084-981c-00c3529ed397", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:56.194Z", "end_time": "2025-08-18T07:30:58.676Z", "duration_ms": 2482}, {"id": "9861e54a-ce4b-45b5-9342-07d475e51338", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:58.678Z", "end_time": "2025-08-18T07:30:58.984Z", "duration_ms": 306}, {"id": "865bc6d7-1cef-4052-8514-45f23803f5fa", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:30:58.985Z", "end_time": "2025-08-18T07:31:00.565Z", "duration_ms": 1580}, {"id": "90e40b71-4801-4c03-9e51-56179e131104", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:00.567Z", "end_time": "2025-08-18T07:31:03.101Z", "duration_ms": 2534}, {"id": "05721508-058a-444a-8b8a-844c0e4eeb56", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:03.105Z", "end_time": "2025-08-18T07:31:05.479Z", "duration_ms": 2374}, {"id": "8501fc58-270b-4a40-ab59-45e3fa330366", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:05.481Z", "end_time": "2025-08-18T07:31:05.898Z", "duration_ms": 417}, {"id": "3fee7cec-9230-4f34-ba7b-0e50de191e8f", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:05.899Z", "end_time": "2025-08-18T07:31:08.560Z", "duration_ms": 2661}, {"id": "aabdebc2-dbb3-447c-b4a3-491d8f227c56", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:08.563Z", "end_time": "2025-08-18T07:31:09.223Z", "duration_ms": 660}, {"id": "2f3fdba4-e63b-4f97-b24d-d84e299789b6", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:09.224Z", "end_time": "2025-08-18T07:31:10.031Z", "duration_ms": 807}, {"id": "de155913-6ba2-47b5-b753-71d46fffdad2", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:10.031Z", "end_time": "2025-08-18T07:31:13.414Z", "duration_ms": 3383}, {"id": "6ab123f4-86ad-453d-a88c-4fde30df62c1", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:13.417Z", "end_time": "2025-08-18T07:31:13.902Z", "duration_ms": 485}, {"id": "7aa808a9-b691-47da-99af-09aebaff8393", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:31:13.902Z", "end_time": "2025-08-18T07:31:15.800Z", "duration_ms": 1898}, {"id": "b8cdf2a2-382b-4fb9-83e1-0a96c8a31fff", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:15.802Z", "end_time": "2025-08-18T07:31:16.149Z", "duration_ms": 347}, {"id": "442b7143-6448-42b4-ba89-6372371a8438", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:16.149Z", "end_time": "2025-08-18T07:31:17.069Z", "duration_ms": 920}, {"id": "784c24f7-7306-4a48-a8de-f828b8dff3d0", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:17.069Z", "end_time": "2025-08-18T07:31:18.186Z", "duration_ms": 1117}, {"id": "3cb67efd-0765-4745-9d7e-fccc42904854", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:31:18.186Z", "end_time": "2025-08-18T07:31:19.807Z", "duration_ms": 1621}, {"id": "a7959012-95ac-4a36-9703-349b2ea67a40", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:31:19.808Z", "end_time": "2025-08-18T07:31:24.961Z", "duration_ms": 5153}, {"id": "1284de63-0a24-4828-b45c-e7658230b53e", "migration_id": "0efc8b1f-0aa0-4bf9-b35e-5034d8b979c7", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 1, "start_time": "2025-08-18T07:31:24.975Z", "end_time": "2025-08-18T07:31:28.931Z", "duration_ms": 3956}]}