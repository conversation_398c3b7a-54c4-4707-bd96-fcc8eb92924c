-- ========================================
-- YYSH 客户关系管理系统 - 数据库结构
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 描述: 基于企业微信的客户管理系统数据库
-- ========================================

-- ========================================
-- 1. 用户管理表
-- ========================================

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `work_wechat_id` varchar(100) DEFAULT NULL COMMENT '企业微信用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(50) NOT NULL COMMENT '用户姓名',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `role` enum('admin','manager','sales','designer','supervisor','director','general_manager','it_admin','user') NOT NULL DEFAULT 'sales' COMMENT '角色：管理员/主管/销售/设计师/主管/总监/总经理/IT管理员/用户',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常 0-禁用',
  `permissions` json DEFAULT NULL COMMENT '权限列表',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_work_wechat_id` (`work_wechat_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 部门表
CREATE TABLE IF NOT EXISTS `departments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '上级部门ID',
  `manager_id` bigint(20) DEFAULT NULL COMMENT '部门主管ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常 0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ========================================
-- 2. 客户管理表
-- ========================================

-- 客户表
CREATE TABLE IF NOT EXISTS `customers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(100) NOT NULL COMMENT '客户姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `company` varchar(200) DEFAULT NULL COMMENT '公司名称',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `wechat` varchar(100) DEFAULT NULL COMMENT '微信号',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `source` enum('online','referral','event','telemarketing','store','other') NOT NULL DEFAULT 'other' COMMENT '客户来源',
  `status` enum('potential','interested','deal','lost') NOT NULL DEFAULT 'potential' COMMENT '客户状态',
  `level` enum('A','B','C','D') NOT NULL DEFAULT 'C' COMMENT '客户等级',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `gender` enum('male','female','unknown') DEFAULT 'unknown' COMMENT '性别',
  `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否VIP客户',
  `is_high_value` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否高价值客户',
  `tags` json DEFAULT NULL COMMENT '客户标签',
  `remark` text DEFAULT NULL COMMENT '备注',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `team_id` bigint(20) DEFAULT NULL COMMENT '团队ID',
  `collaborators` json DEFAULT NULL COMMENT '协同跟进人员ID列表',
  `is_in_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在公海池',
  `pool_time` datetime DEFAULT NULL COMMENT '进入公海时间',
  `last_follow_time` datetime DEFAULT NULL COMMENT '最后跟进时间',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `deal_amount` decimal(10,2) DEFAULT 0.00 COMMENT '成交金额',
  `deal_time` datetime DEFAULT NULL COMMENT '成交时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_status` (`status`),
  KEY `idx_level` (`level`),
  KEY `idx_source` (`source`),
  KEY `idx_is_in_pool` (`is_in_pool`),
  KEY `idx_last_follow_time` (`last_follow_time`),
  KEY `idx_next_follow_time` (`next_follow_time`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 客户标签表
CREATE TABLE IF NOT EXISTS `customer_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `category` varchar(50) DEFAULT 'custom' COMMENT '标签分类',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统标签',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签表';

-- 客户标签关联表
CREATE TABLE IF NOT EXISTS `customer_tag_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_tag` (`customer_id`,`tag_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签关联表';

-- ========================================
-- 3. 跟进管理表
-- ========================================

-- 跟进记录表
CREATE TABLE IF NOT EXISTS `follow_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `user_id` bigint(20) NOT NULL COMMENT '跟进人ID',
  `type` enum('phone','wechat','email','visit','meeting','other') NOT NULL COMMENT '跟进方式',
  `stage` varchar(50) NOT NULL COMMENT '跟进阶段',
  `content` text NOT NULL COMMENT '跟进内容',
  `images` json DEFAULT NULL COMMENT '图片附件',
  `result` enum('effective','invalid','pending') NOT NULL COMMENT '跟进结果',
  `result_detail` text DEFAULT NULL COMMENT '结果详情',
  `has_next_plan` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有下次计划',
  `next_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `next_content` text DEFAULT NULL COMMENT '下次跟进内容',
  `duration` int(11) DEFAULT NULL COMMENT '跟进时长(分钟)',
  `location` varchar(200) DEFAULT NULL COMMENT '跟进地点',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_stage` (`stage`),
  KEY `idx_result` (`result`),
  KEY `idx_next_time` (`next_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟进记录表';

-- 见面记录表
CREATE TABLE IF NOT EXISTS `meeting_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '见面记录ID',
  `follow_record_id` bigint(20) NOT NULL COMMENT '跟进记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `user_id` bigint(20) NOT NULL COMMENT '跟进人ID',
  `meeting_type` enum('measure','visit','external') NOT NULL COMMENT '见面类型：量房/到店/外见',
  `meeting_time` datetime NOT NULL COMMENT '见面时间',
  `designer_id` bigint(20) DEFAULT NULL COMMENT '设计师ID',
  `designer_name` varchar(50) DEFAULT NULL COMMENT '设计师姓名',
  `visit_count` int(11) DEFAULT 1 COMMENT '到店次数',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_follow_record_id` (`follow_record_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_designer_id` (`designer_id`),
  KEY `idx_meeting_type` (`meeting_type`),
  KEY `idx_meeting_time` (`meeting_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='见面记录表';

-- ========================================
-- 4. 公海池管理表
-- ========================================

-- 公海池记录表
CREATE TABLE IF NOT EXISTS `customer_pool_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `action` enum('release','claim') NOT NULL COMMENT '操作类型：释放/领取',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `to_user_id` bigint(20) DEFAULT NULL COMMENT '新负责人ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `auto_release` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动释放',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_from_user_id` (`from_user_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公海池记录表';

-- ========================================
-- 5. 营销工具表
-- ========================================

-- 营销链接表
CREATE TABLE IF NOT EXISTS `marketing_links` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `title` varchar(200) NOT NULL COMMENT '链接标题',
  `description` text DEFAULT NULL COMMENT '链接描述',
  `url` varchar(1000) NOT NULL COMMENT '目标URL',
  `short_url` varchar(200) DEFAULT NULL COMMENT '短链接',
  `qr_code` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `category` varchar(50) DEFAULT 'general' COMMENT '分类',
  `tags` json DEFAULT NULL COMMENT '标签',
  `click_count` int(11) NOT NULL DEFAULT 0 COMMENT '点击次数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用 0-禁用',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_url` (`short_url`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销链接表';

-- 链接访问记录表
CREATE TABLE IF NOT EXISTS `link_access_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `link_id` bigint(20) NOT NULL COMMENT '链接ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `referer` varchar(1000) DEFAULT NULL COMMENT '来源页面',
  `access_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_link_id` (`link_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_access_time` (`access_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='链接访问记录表';

-- ========================================
-- 6. 数据分析表
-- ========================================

-- 数据统计表
CREATE TABLE IF NOT EXISTS `analytics_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '客户总数',
  `new_customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '新增客户数',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `deal_count` int(11) NOT NULL DEFAULT 0 COMMENT '成交客户数',
  `deal_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成交金额',
  `pool_claim_count` int(11) NOT NULL DEFAULT 0 COMMENT '公海领取数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据统计表';

-- ========================================
-- 7. 系统配置表
-- ========================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统配置',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) NOT NULL COMMENT '操作用户ID',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型',
  `target_id` bigint(20) DEFAULT NULL COMMENT '目标ID',
  `content` text DEFAULT NULL COMMENT '操作内容',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 文件上传表
CREATE TABLE IF NOT EXISTS `uploaded_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `filename` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` int(11) NOT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `file_type` enum('avatar','attachment','image','document') NOT NULL COMMENT '文件类型',
  `uploaded_by` bigint(20) NOT NULL COMMENT '上传人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- ========================================
-- 8. 初始数据
-- ========================================

-- 插入默认部门
INSERT INTO `departments` (`id`, `name`, `parent_id`, `sort_order`) VALUES
(1, '销售部', NULL, 1),
(2, '设计部', NULL, 2),
(3, '管理部', NULL, 3)
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 插入默认管理员用户
INSERT INTO `users` (`id`, `username`, `password`, `name`, `role`, `department_id`, `status`) VALUES
(1, 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin', 3, 1)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 插入系统标签
INSERT INTO `customer_tags` (`name`, `color`, `category`, `is_system`, `created_by`) VALUES
('重要客户', '#f50', 'level', 1, 1),
('高价值', '#fa8c16', 'level', 1, 1),
('决策者', '#52c41a', 'role', 1, 1),
('技术专家', '#1890ff', 'role', 1, 1),
('价格敏感', '#722ed1', 'behavior', 1, 1),
('服务导向', '#eb2f96', 'behavior', 1, 1),
('创新型', '#13c2c2', 'type', 1, 1),
('传统型', '#faad14', 'type', 1, 1),
('大客户', '#f5222d', 'size', 1, 1),
('中小企业', '#fa541c', 'size', 1, 1),
('个人用户', '#fadb14', 'size', 1, 1),
('政府机构', '#a0d911', 'size', 1, 1)
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 插入系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('auto_pool_days', '30', 'number', '客户自动进入公海天数', 1),
('max_customer_per_user', '500', 'number', '每个销售最大客户数', 1),
('follow_reminder_hours', '24', 'number', '跟进提醒提前小时数', 1),
('wechat_corp_id', '', 'string', '企业微信CorpID', 1),
('wechat_agent_id', '', 'string', '企业微信应用AgentID', 1),
('wechat_secret', '', 'string', '企业微信应用Secret', 1),
('site_name', 'YYSH客户管理系统', 'string', '系统名称', 1),
('site_logo', '', 'string', '系统Logo', 1)
ON DUPLICATE KEY UPDATE `config_key` = VALUES(`config_key`);