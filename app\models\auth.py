"""认证授权模型

定义角色和权限相关的数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Table, func
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

# 角色权限关联表
role_permissions = Table(
    'role_permissions',
    BaseModel.metadata,
    Column('role_id', String(36), ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', String(36), ForeignKey('permissions.id'), primary_key=True)
)


class Role(BaseModel):
    """角色模型
    
    用于定义系统中的角色信息
    """
    __tablename__ = "roles"
    
    name = Column(String(50), nullable=False, unique=True, comment="角色名称")
    description = Column(Text, comment="角色描述")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关联关系
    permissions = relationship(
        "Permission",
        secondary=role_permissions,
        back_populates="roles"
    )
    # TODO: 暂时注释掉users关系，避免循环导入问题
    # users = relationship("User", secondary="user_roles", back_populates="roles")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name={self.name})>"
    
    def has_permission(self, permission_code: str) -> bool:
        """检查角色是否有指定权限"""
        return any(perm.code == permission_code for perm in self.permissions)


class Permission(BaseModel):
    """权限模型
    
    用于定义系统中的权限信息
    """
    __tablename__ = "permissions"
    
    name = Column(String(100), nullable=False, unique=True, comment="权限名称")
    code = Column(String(100), nullable=False, unique=True, comment="权限编码")
    description = Column(Text, comment="权限描述")
    resource = Column(String(100), nullable=False, comment="资源标识")
    action = Column(String(50), nullable=False, comment="操作类型")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, code={self.code})>"


class OptionCategory(BaseModel):
    """选项分类模型
    
    用于定义系统选项分类
    """
    __tablename__ = "option_categories"
    
    name = Column(String(100), nullable=False, comment="分类名称")
    code = Column(String(50), unique=True, nullable=False, comment="分类编码")
    description = Column(Text, nullable=True, comment="分类描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 关联关系
    items = relationship("OptionItem", back_populates="category")
    
    def __repr__(self):
        return f"<OptionCategory(id={self.id}, name={self.name})>"


class OptionItem(BaseModel):
    """选项项目模型
    
    用于定义系统选项项目
    """
    __tablename__ = "option_items"
    
    category_id = Column(String(36), ForeignKey('option_categories.id'), nullable=False, comment="分类ID")
    name = Column(String(100), nullable=False, comment="选项名称")
    value = Column(String(100), nullable=False, comment="选项值")
    description = Column(Text, nullable=True, comment="选项描述")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 关联关系
    category = relationship("OptionCategory", back_populates="items")
    
    def __repr__(self):
        return f"<OptionItem(id={self.id}, name={self.name}, value={self.value})>"