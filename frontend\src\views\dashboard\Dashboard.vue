<template>
  <div class="dashboard">

    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          {{ getGreeting() }}，{{ user?.name || '用户' }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ formatDate(new Date(), 'YYYY年MM月DD日') }}，祝您工作愉快
        </p>
      </div>
      <div class="quick-actions">
        <n-button type="primary" @click="$router.push('/customer/list')">
          <template #icon>
            <n-icon><PeopleOutline /></n-icon>
          </template>
          客户管理
        </n-button>
        <n-button @click="$router.push('/follow/records')">
          <template #icon>
            <n-icon><ClipboardOutline /></n-icon>
          </template>
          跟进记录
        </n-button>
        <n-button @click="showAddCustomerModal = true">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增客户
        </n-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
          <n-icon size="24">
            <component :is="stat.icon" />
          </n-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.trend">
            <n-icon size="14">
              <TrendingUpOutline v-if="stat.trend === 'up'" />
              <TrendingDownOutline v-else />
            </n-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 销售趋势图 -->
        <n-card title="销售趋势" class="chart-card">
          <template #header-extra>
            <n-select
              v-model:value="salesPeriod"
              :options="periodOptions"
              size="small"
              style="width: 120px;"
            />
          </template>
          <div class="chart-container">
            <SalesChart :data="salesData" :period="salesPeriod" />
          </div>
        </n-card>
        
        <!-- 客户来源分布 -->
        <n-card title="客户来源" class="chart-card">
          <div class="chart-container">
            <CustomerSourceChart :data="customerSourceData" />
          </div>
        </n-card>
      </div>
      
      <div class="chart-row">
        <!-- 跟进状态统计 -->
        <n-card title="跟进状态" class="chart-card">
          <div class="chart-container">
            <FollowStatusChart :data="followStatusData" />
          </div>
        </n-card>
        
        <!-- 团队业绩排行 -->
        <n-card title="团队业绩" class="chart-card">
          <template #header-extra>
            <n-button text size="small" @click="$router.push('/analytics/sales')">
              查看详情
            </n-button>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in teamRanking"
              :key="item.id"
              class="ranking-item"
            >
              <div class="ranking-number" :class="`rank-${index + 1}`">
                {{ index + 1 }}
              </div>
              <n-avatar size="small" :src="item.avatar">
                {{ item.name.charAt(0) }}
              </n-avatar>
              <div class="ranking-info">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-department">{{ item.department }}</div>
              </div>
              <div class="ranking-value">
                <div class="ranking-amount">¥{{ item.amount.toLocaleString() }}</div>
                <div class="ranking-count">{{ item.count }}个客户</div>
              </div>
            </div>
          </div>
        </n-card>
      </div>
    </div>
    
    <!-- 待办事项和最新动态 -->
    <div class="bottom-section">
      <div class="section-row">
        <!-- 待办事项 -->
        <n-card title="待办事项" class="todo-card">
          <template #header-extra>
            <n-button text size="small" @click="$router.push('/follow/todos')">
              查看全部
            </n-button>
          </template>
          <div class="todo-list">
            <div
              v-for="todo in todoList"
              :key="todo.id"
              class="todo-item"
              :class="{ 'overdue': todo.isOverdue }"
            >
              <n-checkbox v-model:checked="todo.completed" @update:checked="updateTodo(todo)" />
              <div class="todo-content">
                <div class="todo-title">{{ todo.title }}</div>
                <div class="todo-meta">
                  <span class="todo-customer">{{ todo.customer }}</span>
                  <span class="todo-time">{{ formatDate(todo.dueDate, 'MM-DD HH:mm') }}</span>
                </div>
              </div>
              <div class="todo-priority" :class="`priority-${todo.priority}`">
                {{ getPriorityText(todo.priority) }}
              </div>
            </div>
          </div>
        </n-card>
        
        <!-- 最新动态 -->
        <n-card title="最新动态" class="activity-card">
          <template #header-extra>
            <n-button text size="small" @click="$router.push('/follow/records')">
              查看全部
            </n-button>
          </template>
          <div class="activity-list">
            <div
              v-for="activity in activityList"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-avatar">
                <n-avatar size="small" :src="activity.user.avatar">
                  {{ activity.user.name.charAt(0) }}
                </n-avatar>
              </div>
              <div class="activity-content">
                <div class="activity-text">
                  <strong>{{ activity.user.name }}</strong>
                  {{ activity.action }}
                  <strong>{{ activity.target }}</strong>
                </div>
                <div class="activity-time">
                  {{ formatTime(activity.createdAt) }}
                </div>
              </div>
              <div class="activity-type" :class="`type-${activity.type}`">
                <n-icon size="16">
                  <component :is="getActivityIcon(activity.type)" />
                </n-icon>
              </div>
            </div>
          </div>
        </n-card>
      </div>
    </div>
    
    <!-- 新增客户弹窗 -->
    <CustomerFormModal
      v-model:show="showAddCustomerModal"
      @success="handleCustomerAdded"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  NCard,
  NButton,
  NIcon,
  NSelect,
  NAvatar,
  NCheckbox
} from 'naive-ui'
import {
  PeopleOutline,
  ClipboardOutline,
  AddOutline,
  TrendingUpOutline,
  TrendingDownOutline,
  CallOutline,
  MailOutline,
  ChatbubbleOutline,
  DocumentTextOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores'
import { formatDate } from '@/utils'
import SalesChart from '@/components/charts/SalesChart.vue'
import CustomerSourceChart from '@/components/charts/CustomerSourceChart.vue'
import FollowStatusChart from '@/components/charts/FollowStatusChart.vue'
import CustomerFormModal from '@/components/business/CustomerFormModal.vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const salesPeriod = ref('week')
const showAddCustomerModal = ref(false)

// 计算属性
const user = computed(() => authStore.user)

// 时间段选项
const periodOptions = [
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本季度', value: 'quarter' },
  { label: '本年', value: 'year' }
]

// 统计数据
const stats = ref([
  {
    key: 'customers',
    label: '客户总数',
    value: '1,234',
    change: '+12.5%',
    trend: 'up',
    color: '#1890ff',
    icon: PeopleOutline
  },
  {
    key: 'follows',
    label: '本月跟进',
    value: '856',
    change: '+8.2%',
    trend: 'up',
    color: '#52c41a',
    icon: ClipboardOutline
  },
  {
    key: 'deals',
    label: '成交客户',
    value: '142',
    change: '+15.3%',
    trend: 'up',
    color: '#faad14',
    icon: TrendingUpOutline
  },
  {
    key: 'revenue',
    label: '销售额',
    value: '¥2.4M',
    change: '+22.1%',
    trend: 'up',
    color: '#f5222d',
    icon: TrendingUpOutline
  }
])

// 销售数据
const salesData = ref({
  week: [
    { date: '周一', value: 120 },
    { date: '周二', value: 150 },
    { date: '周三', value: 180 },
    { date: '周四', value: 200 },
    { date: '周五', value: 240 },
    { date: '周六', value: 160 },
    { date: '周日', value: 140 }
  ]
})

// 客户来源数据
const customerSourceData = ref([
  { name: '线上推广', value: 35 },
  { name: '客户推荐', value: 25 },
  { name: '电话营销', value: 20 },
  { name: '展会活动', value: 15 },
  { name: '其他', value: 5 }
])

// 跟进状态数据
const followStatusData = ref([
  { name: '待跟进', value: 45 },
  { name: '跟进中', value: 35 },
  { name: '已成交', value: 15 },
  { name: '已流失', value: 5 }
])

// 团队业绩排行
const teamRanking = ref([
  {
    id: '1',
    name: '张三',
    department: '销售一部',
    avatar: '',
    amount: 580000,
    count: 25
  },
  {
    id: '2',
    name: '李四',
    department: '销售二部',
    avatar: '',
    amount: 520000,
    count: 22
  },
  {
    id: '3',
    name: '王五',
    department: '销售一部',
    avatar: '',
    amount: 480000,
    count: 20
  },
  {
    id: '4',
    name: '赵六',
    department: '销售三部',
    avatar: '',
    amount: 450000,
    count: 18
  }
])

// 待办事项
const todoList = ref([
  {
    id: '1',
    title: '回访客户张三',
    customer: '张三',
    dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000),
    priority: 'high',
    completed: false,
    isOverdue: false
  },
  {
    id: '2',
    title: '发送产品资料',
    customer: '李四',
    dueDate: new Date(Date.now() + 4 * 60 * 60 * 1000),
    priority: 'medium',
    completed: false,
    isOverdue: false
  },
  {
    id: '3',
    title: '安排产品演示',
    customer: '王五',
    dueDate: new Date(Date.now() - 1 * 60 * 60 * 1000),
    priority: 'high',
    completed: false,
    isOverdue: true
  }
])

// 最新动态
const activityList = ref([
  {
    id: '1',
    user: { name: '张三', avatar: '' },
    action: '创建了客户',
    target: '李明',
    type: 'customer',
    createdAt: new Date(Date.now() - 10 * 60 * 1000)
  },
  {
    id: '2',
    user: { name: '李四', avatar: '' },
    action: '添加了跟进记录',
    target: '王强',
    type: 'follow',
    createdAt: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: '3',
    user: { name: '王五', avatar: '' },
    action: '完成了电话沟通',
    target: '赵敏',
    type: 'call',
    createdAt: new Date(Date.now() - 60 * 60 * 1000)
  }
])

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const map = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return map[priority as keyof typeof map] || '中'
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap = {
    customer: PeopleOutline,
    follow: ClipboardOutline,
    call: CallOutline,
    email: MailOutline,
    meeting: ChatbubbleOutline,
    document: DocumentTextOutline
  }
  return iconMap[type as keyof typeof iconMap] || ClipboardOutline
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return formatDate(date, 'MM-DD HH:mm')
}

// 更新待办事项
const updateTodo = (todo: any) => {
  if (todo.completed) {
    // 待办事项已完成
  }
}

// 处理客户添加成功
const handleCustomerAdded = () => {
  // 客户添加成功
  // 刷新统计数据
  loadDashboardData()
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    // 这里调用API获取实际数据
    // const data = await dashboardApi.getData()
    // 更新各种数据
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
  background: white;
  min-height: 100%;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 16px;
  color: var(--n-text-color-disabled);
  margin: 0;
  line-height: 1.4;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.welcome-content h1 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-content p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--n-text-color);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--n-text-color-disabled);
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #f5222d;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-row:last-child {
  margin-bottom: 0;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  padding: 16px 0;
}

.ranking-list {
  padding: 16px 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.rank-1 {
  background: #faad14;
}

.rank-2 {
  background: #d9d9d9;
}

.rank-3 {
  background: #cd7f32;
}

.ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: var(--n-text-color-disabled);
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
}

.ranking-department {
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

.ranking-value {
  text-align: right;
}

.ranking-amount {
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.ranking-count {
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

.bottom-section {
  margin-bottom: 24px;
}

.section-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.todo-card,
.activity-card {
  height: 400px;
}

.todo-list,
.activity-list {
  padding: 16px 0;
  max-height: 320px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.overdue {
  background-color: #fff2f0;
  border-color: #ffccc7;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
  margin-bottom: 4px;
}

.todo-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

.todo-priority {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.priority-high {
  background: #fff2f0;
  color: #f5222d;
}

.priority-medium {
  background: #fff7e6;
  color: #faad14;
}

.priority-low {
  background: #f6ffed;
  color: #52c41a;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: var(--n-text-color);
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

.activity-type {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-customer {
  background: #e6f7ff;
  color: #1890ff;
}

.type-follow {
  background: #f6ffed;
  color: #52c41a;
}

.type-call {
  background: #fff7e6;
  color: #faad14;
}

@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .section-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>