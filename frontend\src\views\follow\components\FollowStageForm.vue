<template>
  <div class="follow-stage-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
    >
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="客户" path="customerId">
          <n-select
            v-model:value="formData.customerId"
            placeholder="请选择客户"
            :options="customerOptions"
            filterable
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi label="子阶段" path="subStage">
          <n-select
            v-model:value="formData.subStage"
            placeholder="请选择子阶段"
            :options="subStageOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="跟进方式" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择跟进方式"
            :options="typeOptions"
          />
        </n-form-item-gi>
        <n-form-item-gi label="跟进状态" path="status">
          <n-select
            v-model:value="formData.status"
            placeholder="请选择跟进状态"
            :options="statusOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="跟进时间" path="followTime">
          <n-date-picker
            v-model:value="formData.followTime"
            type="datetime"
            placeholder="请选择跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
        <n-form-item-gi label="下次跟进">
          <n-date-picker
            v-model:value="formData.nextFollowTime"
            type="datetime"
            placeholder="请选择下次跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="跟进内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入跟进内容"
          :rows="4"
        />
      </n-form-item>
      
      <n-form-item label="备注">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

interface FormData {
  id: number | null
  customerId: number | null
  type: string
  status: string
  followTime: number | null
  content: string
  nextFollowTime: number | null
  remark: string
  stage: 'follow' | 'visit' | 'deal'
  subStage: string
  designer: string
  designerId: number | null
  amount: number | null
  contractNo: string
  paymentStatus: string
  visitDate: number | null
  measureDate: number | null
  dealDate: number | null
}

interface Props {
  formData: FormData
  customerOptions: Array<{ label: string; value: number }>
  designerOptions?: Array<{ label: string; value: number }>
}

interface Emits {
  'update:formData': [value: FormData]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)

// 子阶段选项
const subStageOptions = [
  { label: '初次联系', value: 'initial' },
  { label: '跟进中', value: 'follow_up' },
  { label: '有意向', value: 'interested' },
  { label: '考虑中', value: 'considering' }
]

// 跟进方式选项
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '邮件沟通', value: 'email' },
  { label: '面谈', value: 'meeting' },
  { label: '其他', value: 'other' }
]

// 跟进状态选项
const statusOptions = [
  { label: '已联系', value: 'contacted' },
  { label: '有意向', value: 'interested' },
  { label: '无意向', value: 'not_interested' },
  { label: '待跟进', value: 'pending' },
  { label: '已成交', value: 'closed' }
]

// 表单验证规则
const formRules: FormRules = {
  customerId: {
    required: true,
    type: 'number',
    message: '请选择客户',
    trigger: 'change'
  },
  subStage: {
    required: true,
    message: '请选择子阶段',
    trigger: 'change'
  },
  type: {
    required: true,
    message: '请选择跟进方式',
    trigger: 'change'
  },
  status: {
    required: true,
    message: '请选择跟进状态',
    trigger: 'change'
  },
  followTime: {
    required: true,
    type: 'number',
    message: '请选择跟进时间',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入跟进内容',
    trigger: 'blur'
  }
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newValue) => {
    emit('update:formData', newValue)
  },
  { deep: true }
)

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.follow-stage-form {
  padding: 16px 0;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-date-picker) {
  border-radius: 6px;
}
</style>