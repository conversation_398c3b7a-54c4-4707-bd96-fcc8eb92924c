// Supabase配置已禁用，改用本地MySQL数据库
// import { createClient } from '@supabase/supabase-js'

// const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
// const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// if (!supabaseUrl || !supabaseAnonKey) {
//   throw new Error('Missing Supabase environment variables')
// }

// export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 临时导出空对象，避免其他文件引用报错
export const supabase = null

// 数据库表类型定义
export interface Database {
  public: {
    Tables: {
      roles: {
        Row: {
          id: number
          name: string
          display_name: string
          description: string | null
          permissions: any
          is_system: boolean
          status: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          display_name: string
          description?: string | null
          permissions?: any
          is_system?: boolean
          status?: boolean
        }
        Update: {
          name?: string
          display_name?: string
          description?: string | null
          permissions?: any
          is_system?: boolean
          status?: boolean
        }
      }
      permissions: {
        Row: {
          id: number
          name: string
          display_name: string
          module: string
          action: string
          resource: string | null
          description: string | null
          is_system: boolean
          created_at: string
        }
        Insert: {
          name: string
          display_name: string
          module: string
          action: string
          resource?: string | null
          description?: string | null
          is_system?: boolean
        }
        Update: {
          name?: string
          display_name?: string
          module?: string
          action?: string
          resource?: string | null
          description?: string | null
          is_system?: boolean
        }
      }
      user_roles: {
        Row: {
          id: number
          user_id: string
          role_id: number
          assigned_by: string | null
          assigned_at: string
          expires_at: string | null
        }
        Insert: {
          user_id: string
          role_id: number
          assigned_by?: string | null
          expires_at?: string | null
        }
        Update: {
          user_id?: string
          role_id?: number
          assigned_by?: string | null
          expires_at?: string | null
        }
      }
      marketing_campaigns: {
        Row: {
          id: number
          name: string
          type: string
          description: string | null
          cover_image: string | null
          start_time: string
          end_time: string
          status: string
          target_audience: any
          rules: any
          prizes: any
          budget: number
          spent: number
          participant_count: number
          conversion_count: number
          share_count: number
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          type: string
          description?: string | null
          cover_image?: string | null
          start_time: string
          end_time: string
          status?: string
          target_audience?: any
          rules?: any
          prizes?: any
          budget?: number
          spent?: number
          participant_count?: number
          conversion_count?: number
          share_count?: number
          created_by: string
        }
        Update: {
          name?: string
          type?: string
          description?: string | null
          cover_image?: string | null
          start_time?: string
          end_time?: string
          status?: string
          target_audience?: any
          rules?: any
          prizes?: any
          budget?: number
          spent?: number
          participant_count?: number
          conversion_count?: number
          share_count?: number
        }
      }
      meetings: {
        Row: {
          id: number
          title: string
          type: string
          description: string | null
          location: string | null
          meeting_url: string | null
          start_time: string
          end_time: string
          status: string
          organizer_id: string
          agenda: any
          materials: any
          recording_url: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          title: string
          type: string
          description?: string | null
          location?: string | null
          meeting_url?: string | null
          start_time: string
          end_time: string
          status?: string
          organizer_id: string
          agenda?: any
          materials?: any
          recording_url?: string | null
          notes?: string | null
        }
        Update: {
          title?: string
          type?: string
          description?: string | null
          location?: string | null
          meeting_url?: string | null
          start_time?: string
          end_time?: string
          status?: string
          organizer_id?: string
          agenda?: any
          materials?: any
          recording_url?: string | null
          notes?: string | null
        }
      }
      pool_rules: {
        Row: {
          id: number
          name: string
          description: string | null
          conditions: any
          auto_release_days: number
          reminder_days: number
          max_claims_per_day: number | null
          priority: number | null
          is_active: boolean | null
          applies_to_roles: any
          applies_to_departments: any
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          description?: string | null
          conditions: any
          auto_release_days?: number
          reminder_days?: number
          max_claims_per_day?: number | null
          priority?: number | null
          is_active?: boolean | null
          applies_to_roles?: any
          applies_to_departments?: any
          created_by: string
        }
        Update: {
          name?: string
          description?: string | null
          conditions?: any
          auto_release_days?: number
          reminder_days?: number
          max_claims_per_day?: number | null
          priority?: number | null
          is_active?: boolean | null
          applies_to_roles?: any
          applies_to_departments?: any
        }
      }
    }
  }
}

// 导出类型化的Supabase客户端
export type SupabaseClient = typeof supabase
export default supabase