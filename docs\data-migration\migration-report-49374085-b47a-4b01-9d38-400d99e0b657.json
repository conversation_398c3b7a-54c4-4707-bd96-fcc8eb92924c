{"migrationId": "49374085-b47a-4b01-9d38-400d99e0b657", "timestamp": "2025-08-18T06:45:33.923Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 14, "failedTables": 7, "totalRecords": 165, "migratedRecords": 107, "failedRecords": 58}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T06:44:48.160Z", "errors": [], "endTime": "2025-08-18T06:44:51.622Z", "duration": 3462}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:44:51.628Z", "errors": [], "endTime": "2025-08-18T06:44:53.366Z", "duration": 1738}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T06:44:53.368Z", "errors": [], "endTime": "2025-08-18T06:44:56.756Z", "duration": 3388}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:44:56.759Z", "errors": [], "endTime": "2025-08-18T06:45:01.282Z", "duration": 4523}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:01.285Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:45:01.716Z", "errors": [], "endTime": "2025-08-18T06:45:03.949Z", "duration": 2233}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:45:03.952Z", "errors": ["Unknown column 'extra_data' in 'field list'"], "endTime": "2025-08-18T06:45:08.976Z", "duration": 5024}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:45:08.979Z", "errors": ["Unknown column 'next_follow_up_at' in 'field list'"], "endTime": "2025-08-18T06:45:12.040Z", "duration": 3061}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:12.042Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:45:12.455Z", "errors": ["Unknown column 'rules' in 'field list'"], "endTime": "2025-08-18T06:45:15.787Z", "duration": 3332}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:15.791Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:16.468Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:45:17.310Z", "errors": ["Unknown column 'agenda' in 'field list'"], "endTime": "2025-08-18T06:45:21.024Z", "duration": 3714}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:21.027Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:45:21.485Z", "errors": ["Unknown column 'applies_to_roles' in 'field list'"], "endTime": "2025-08-18T06:45:23.678Z", "duration": 2193}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:23.681Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:23.979Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:25.101Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:45:26.703Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:45:28.218Z", "errors": ["Unknown column 'result' in 'field list'"], "endTime": "2025-08-18T06:45:32.192Z", "duration": 3974}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:45:32.195Z", "errors": ["Unknown column 'claimed_by' in 'field list'"], "endTime": "2025-08-18T06:45:33.921Z", "duration": 1726}], "logs": [{"id": "deb16977-2a38-446c-9d5c-bba499b55826", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T06:44:48.160Z", "end_time": "2025-08-18T06:44:51.622Z", "duration_ms": 3462}, {"id": "81028600-d146-4c1c-bfd8-8f7d5f5016b9", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:44:51.628Z", "end_time": "2025-08-18T06:44:53.366Z", "duration_ms": 1738}, {"id": "1dde7dfa-7533-437c-b53e-e5fca2298ac9", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T06:44:53.368Z", "end_time": "2025-08-18T06:44:56.756Z", "duration_ms": 3388}, {"id": "7f287462-b9f8-4c3e-9b06-f12bc59cdfb1", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:44:56.759Z", "end_time": "2025-08-18T06:45:01.282Z", "duration_ms": 4523}, {"id": "91941b0d-9d7a-439f-8d1e-40bdca9b56e1", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:01.285Z", "end_time": "2025-08-18T06:45:01.716Z", "duration_ms": 431}, {"id": "7bbc19c4-128d-48e4-b6c3-0ce2565e809a", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:45:01.716Z", "end_time": "2025-08-18T06:45:03.949Z", "duration_ms": 2233}, {"id": "1c486214-06f5-49f8-9c1b-a167d6a74d87", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:03.952Z", "end_time": "2025-08-18T06:45:08.976Z", "duration_ms": 5024}, {"id": "d4a5681f-351c-45d4-9a89-790ca22c5105", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:08.979Z", "end_time": "2025-08-18T06:45:12.040Z", "duration_ms": 3061}, {"id": "b21657ff-7f4d-48c5-b0ca-bb38aae83fe8", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:12.042Z", "end_time": "2025-08-18T06:45:12.455Z", "duration_ms": 413}, {"id": "8c7b85e6-4c26-409f-8c12-06f5cf4fe28f", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:12.455Z", "end_time": "2025-08-18T06:45:15.787Z", "duration_ms": 3332}, {"id": "9552bc51-d8ff-4bbe-b600-c87e86c93251", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:15.791Z", "end_time": "2025-08-18T06:45:16.467Z", "duration_ms": 676}, {"id": "25b26c4d-5112-4a0b-9e20-d4ad283b2227", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:16.468Z", "end_time": "2025-08-18T06:45:17.310Z", "duration_ms": 842}, {"id": "7c0b46c5-e465-4242-b5ed-976e519b57f7", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:17.310Z", "end_time": "2025-08-18T06:45:21.024Z", "duration_ms": 3714}, {"id": "9b134489-257d-4c2e-b084-0fed3d8a4a66", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:21.027Z", "end_time": "2025-08-18T06:45:21.485Z", "duration_ms": 458}, {"id": "ab7ec069-2d3e-4de3-b5c5-fe789829272e", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:21.485Z", "end_time": "2025-08-18T06:45:23.678Z", "duration_ms": 2193}, {"id": "639da32a-69d4-4ced-bcd6-1c8bb14b2ef3", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:23.681Z", "end_time": "2025-08-18T06:45:23.979Z", "duration_ms": 298}, {"id": "17659655-40ec-45a5-b280-1d42f855b179", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:23.979Z", "end_time": "2025-08-18T06:45:25.101Z", "duration_ms": 1122}, {"id": "680f9ced-3d75-434b-a5f7-b0e0524caa9b", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:25.101Z", "end_time": "2025-08-18T06:45:26.703Z", "duration_ms": 1602}, {"id": "d608994b-3bf5-43a9-b763-b739ce6508fb", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:26.703Z", "end_time": "2025-08-18T06:45:28.218Z", "duration_ms": 1515}, {"id": "b1b3d178-929f-43fd-bf36-aa63041e46d3", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:28.218Z", "end_time": "2025-08-18T06:45:32.192Z", "duration_ms": 3974}, {"id": "92a54814-b15f-4960-8e5b-340b3fc71bc7", "migration_id": "49374085-b47a-4b01-9d38-400d99e0b657", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:45:32.195Z", "end_time": "2025-08-18T06:45:33.921Z", "duration_ms": 1726}]}