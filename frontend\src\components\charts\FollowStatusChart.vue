<template>
  <div class="follow-status-chart">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface StatusData {
  name: string
  value: number
}

interface Props {
  data: StatusData[]
}

const props = defineProps<Props>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 状态颜色配置
const statusColors = {
  '待跟进': '#faad14',
  '跟进中': '#1890ff',
  '已成交': '#52c41a',
  '已流失': '#f5222d'
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: 600;">${params.name}</div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="display: inline-block; width: 8px; height: 8px; background: ${params.color}; border-radius: 50%;"></span>
              <span>数量: ${params.value}</span>
            </div>
            <div style="margin-top: 4px; color: #ccc;">占比: ${percent}%</div>
          </div>
        `
      }
    },
    legend: {
      bottom: '5%',
      left: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 24,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = props.data.find(d => d.name === name)
        return `${name} ${item?.value || 0}`
      }
    },
    series: [
      {
        name: '跟进状态',
        type: 'pie',
        radius: ['30%', '60%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 12,
          color: '#666',
          formatter: (params: any) => {
            const percent = ((params.value / total) * 100).toFixed(1)
            return `${percent}%`
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#ccc'
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        data: props.data.map(item => ({
          ...item,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                { offset: 0, color: statusColors[item.name as keyof typeof statusColors] || '#1890ff' },
                { offset: 1, color: adjustBrightness(statusColors[item.name as keyof typeof statusColors] || '#1890ff', -30) }
              ]
            }
          }
        }))
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as const,
    animationDelay: (idx: number) => idx * 100
  }
  
  chartInstance.setOption(option, true)
}

// 调整颜色亮度
const adjustBrightness = (color: string, amount: number): string => {
  const usePound = color[0] === '#'
  const col = usePound ? color.slice(1) : color
  const num = parseInt(col, 16)
  let r = (num >> 16) + amount
  let g = (num >> 8 & 0x00FF) + amount
  let b = (num & 0x0000FF) + amount
  r = r > 255 ? 255 : r < 0 ? 0 : r
  g = g > 255 ? 255 : g < 0 ? 0 : g
  b = b > 255 ? 255 : b < 0 ? 0 : b
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
}

// 暴露清理方法
defineExpose({
  cleanup
})
</script>

<style scoped>
.follow-status-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>