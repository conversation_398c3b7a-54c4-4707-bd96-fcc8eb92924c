<template>
  <div class="customer-analysis">
    <!-- 分析配置 -->
    <div class="analysis-config">
      <div class="config-row">
        <div class="config-item">
          <label>分析维度</label>
          <n-select
            v-model:value="config.dimension"
            :options="dimensionOptions"
            placeholder="选择分析维度"
          />
        </div>
        <div class="config-item">
          <label>时间范围</label>
          <n-date-picker
            v-model:value="config.dateRange"
            type="daterange"
            clearable
          />
        </div>
        <div class="config-item">
          <label>客户分组</label>
          <n-select
            v-model:value="config.groupBy"
            :options="groupOptions"
            placeholder="选择分组方式"
          />
        </div>
      </div>
      <div class="config-row">
        <div class="config-item">
          <label>客户来源</label>
          <n-select
            v-model:value="config.source"
            :options="sourceOptions"
            placeholder="选择客户来源"
            multiple
            clearable
          />
        </div>
        <div class="config-item">
          <label>客户状态</label>
          <n-select
            v-model:value="config.status"
            :options="statusOptions"
            placeholder="选择客户状态"
            multiple
            clearable
          />
        </div>
        <div class="config-item">
          <label>对比分析</label>
          <n-checkbox v-model:checked="config.enableComparison">
            启用同期对比
          </n-checkbox>
        </div>
      </div>
      <div class="config-actions">
        <n-button type="primary" @click="runAnalysis" :loading="loading">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          开始分析
        </n-button>
        <n-button @click="exportReport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报告
        </n-button>
      </div>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="!loading">
      <!-- 概览指标 -->
      <div class="overview-metrics">
        <div class="metric-card">
          <div class="metric-icon">
            <n-icon size="32" color="#2080f0"><PeopleOutline /></n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatNumber(metrics.totalCustomers) }}</div>
            <div class="metric-label">总客户数</div>
            <div class="metric-change positive">+{{ metrics.customerGrowth }}%</div>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon">
            <n-icon size="32" color="#18a058"><TrendingUpOutline /></n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.conversionRate }}%</div>
            <div class="metric-label">转化率</div>
            <div class="metric-change positive">+{{ metrics.conversionGrowth }}%</div>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon">
            <n-icon size="32" color="#f0a020"><CashOutline /></n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">¥{{ formatNumber(metrics.avgValue) }}</div>
            <div class="metric-label">平均客户价值</div>
            <div class="metric-change positive">+{{ metrics.valueGrowth }}%</div>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon">
            <n-icon size="32" color="#d03050"><TimeOutline /></n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.avgCycle }}天</div>
            <div class="metric-label">平均转化周期</div>
            <div class="metric-change negative">-{{ metrics.cycleImprovement }}%</div>
          </div>
        </div>
      </div>

      <!-- 客户趋势分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>客户趋势分析</h3>
          <div class="chart-controls">
            <n-radio-group v-model:value="trendPeriod" size="small">
              <n-radio-button value="7d">近7天</n-radio-button>
              <n-radio-button value="30d">近30天</n-radio-button>
              <n-radio-button value="90d">近90天</n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="chart-container">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </div>

      <!-- 客户来源分析 -->
      <div class="analysis-row">
        <div class="analysis-section">
          <div class="section-header">
            <h3>客户来源分析</h3>
          </div>
          <div class="chart-container">
            <div ref="sourceChartRef" class="chart"></div>
          </div>
          <div class="source-stats">
            <div class="stat-item" v-for="source in sourceStats" :key="source.name">
              <div class="stat-indicator" :style="{ backgroundColor: source.color }"></div>
              <div class="stat-content">
                <div class="stat-name">{{ source.name }}</div>
                <div class="stat-metrics">
                  <span>{{ source.count }}人</span>
                  <span>{{ source.percentage }}%</span>
                  <span class="conversion-rate">转化率: {{ source.conversionRate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-section">
          <div class="section-header">
            <h3>客户状态分布</h3>
          </div>
          <div class="chart-container">
            <div ref="statusChartRef" class="chart"></div>
          </div>
          <div class="status-flow">
            <div class="flow-item" v-for="flow in statusFlow" :key="flow.from">
              <div class="flow-from">{{ flow.from }}</div>
              <div class="flow-arrow">→</div>
              <div class="flow-to">{{ flow.to }}</div>
              <div class="flow-count">{{ flow.count }}人</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户行为分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>客户行为分析</h3>
        </div>
        <div class="behavior-metrics">
          <div class="behavior-card">
            <div class="behavior-title">浏览行为</div>
            <div class="behavior-chart">
              <div ref="viewBehaviorChartRef" class="chart"></div>
            </div>
            <div class="behavior-insights">
              <div class="insight-item">
                <span class="insight-label">平均浏览页面</span>
                <span class="insight-value">{{ behaviorData.avgPages }}页</span>
              </div>
              <div class="insight-item">
                <span class="insight-label">平均停留时间</span>
                <span class="insight-value">{{ behaviorData.avgDuration }}分钟</span>
              </div>
              <div class="insight-item">
                <span class="insight-label">跳出率</span>
                <span class="insight-value">{{ behaviorData.bounceRate }}%</span>
              </div>
            </div>
          </div>

          <div class="behavior-card">
            <div class="behavior-title">互动行为</div>
            <div class="behavior-chart">
              <div ref="interactionChartRef" class="chart"></div>
            </div>
            <div class="behavior-insights">
              <div class="insight-item">
                <span class="insight-label">平均消息数</span>
                <span class="insight-value">{{ behaviorData.avgMessages }}条</span>
              </div>
              <div class="insight-item">
                <span class="insight-label">响应时间</span>
                <span class="insight-value">{{ behaviorData.responseTime }}分钟</span>
              </div>
              <div class="insight-item">
                <span class="insight-label">活跃度</span>
                <span class="insight-value">{{ behaviorData.activityScore }}分</span>
              </div>
            </div>
          </div>

          <div class="behavior-card">
            <div class="behavior-title">转化路径</div>
            <div class="conversion-path">
              <div class="path-step" v-for="(step, index) in conversionPath" :key="index">
                <div class="step-icon">
                  <n-icon :color="step.color"><component :is="step.icon" /></n-icon>
                </div>
                <div class="step-content">
                  <div class="step-name">{{ step.name }}</div>
                  <div class="step-rate">{{ step.rate }}%</div>
                </div>
                <div class="step-arrow" v-if="index < conversionPath.length - 1">→</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户价值分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>客户价值分析</h3>
        </div>
        <div class="value-analysis">
          <div class="value-distribution">
            <div class="distribution-title">客户价值分布</div>
            <div class="distribution-chart">
              <div ref="valueDistributionChartRef" class="chart"></div>
            </div>
          </div>
          <div class="value-segments">
            <div class="segment-title">客户分层</div>
            <div class="segment-list">
              <div class="segment-item" v-for="segment in valueSegments" :key="segment.name">
                <div class="segment-header">
                  <div class="segment-name">{{ segment.name }}</div>
                  <div class="segment-percentage">{{ segment.percentage }}%</div>
                </div>
                <div class="segment-bar">
                  <div class="segment-fill" :style="{ width: segment.percentage + '%', backgroundColor: segment.color }"></div>
                </div>
                <div class="segment-details">
                  <span>{{ segment.count }}人</span>
                  <span>平均价值: ¥{{ formatNumber(segment.avgValue) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- RFM分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>RFM客户分析</h3>
        </div>
        <div class="rfm-analysis">
          <div class="rfm-matrix">
            <div class="matrix-title">RFM矩阵</div>
            <div class="matrix-chart">
              <div ref="rfmChartRef" class="chart"></div>
            </div>
          </div>
          <div class="rfm-segments">
            <div class="rfm-title">客户群体</div>
            <div class="rfm-list">
              <div class="rfm-item" v-for="rfm in rfmSegments" :key="rfm.name">
                <div class="rfm-header">
                  <div class="rfm-name">{{ rfm.name }}</div>
                  <div class="rfm-count">{{ rfm.count }}人</div>
                </div>
                <div class="rfm-description">{{ rfm.description }}</div>
                <div class="rfm-strategy">{{ rfm.strategy }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化建议 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>优化建议</h3>
        </div>
        <div class="recommendations">
          <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
            <div class="recommendation-header">
              <div class="recommendation-icon">
                <n-icon :color="rec.color"><component :is="rec.icon" /></n-icon>
              </div>
              <div class="recommendation-priority" :class="rec.priority">
                {{ getPriorityText(rec.priority) }}
              </div>
            </div>
            <div class="recommendation-content">
              <div class="recommendation-title">{{ rec.title }}</div>
              <div class="recommendation-desc">{{ rec.description }}</div>
              <div class="recommendation-impact">
                <span class="impact-label">预期影响：</span>
                <span class="impact-value">{{ rec.impact }}</span>
              </div>
            </div>
            <div class="recommendation-action">
              <n-button size="small" type="primary" ghost>
                {{ rec.actionText }}
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <n-spin size="large">
        <template #description>
          正在分析客户数据，请稍候...
        </template>
      </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NSelect, NDatePicker, NCheckbox, NButton, NIcon, NRadioGroup, NRadioButton,
  NSpin, useMessage
} from 'naive-ui'
import {
  AnalyticsOutline, DownloadOutline, PeopleOutline, TrendingUpOutline,
  CashOutline, TimeOutline, EyeOutline, ChatbubbleOutline, ShareOutline,
  CheckmarkCircleOutline, AtOutline, BulbOutline, AlertCircleOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface AnalysisConfig {
  dimension: string
  dateRange: [number, number] | null
  groupBy: string
  source: string[]
  status: string[]
  enableComparison: boolean
}

const emit = defineEmits<{
  close: []
}>()

const message = useMessage()
const loading = ref(false)
const trendPeriod = ref('30d')

// 分析配置
const config = reactive<AnalysisConfig>({
  dimension: 'acquisition',
  dateRange: null,
  groupBy: 'source',
  source: [],
  status: [],
  enableComparison: false
})

// 配置选项
const dimensionOptions = [
  { label: '获客分析', value: 'acquisition' },
  { label: '行为分析', value: 'behavior' },
  { label: '转化分析', value: 'conversion' },
  { label: '价值分析', value: 'value' }
]

const groupOptions = [
  { label: '按来源分组', value: 'source' },
  { label: '按状态分组', value: 'status' },
  { label: '按时间分组', value: 'time' },
  { label: '按价值分组', value: 'value' }
]

const sourceOptions = [
  { label: '二维码', value: 'qr_code' },
  { label: '分享链接', value: 'share_link' },
  { label: '好友邀请', value: 'friend_invite' },
  { label: '群聊', value: 'group_chat' },
  { label: '搜索添加', value: 'search' }
]

const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '潜在', value: 'potential' },
  { label: '非活跃', value: 'inactive' },
  { label: '已流失', value: 'lost' }
]

// 图表引用
const trendChartRef = ref<HTMLElement>()
const sourceChartRef = ref<HTMLElement>()
const statusChartRef = ref<HTMLElement>()
const viewBehaviorChartRef = ref<HTMLElement>()
const interactionChartRef = ref<HTMLElement>()
const valueDistributionChartRef = ref<HTMLElement>()
const rfmChartRef = ref<HTMLElement>()

// 分析数据
const metrics = ref({
  totalCustomers: 15847,
  customerGrowth: 12.5,
  conversionRate: 15.8,
  conversionGrowth: 3.2,
  avgValue: 8500,
  valueGrowth: 18.7,
  avgCycle: 28,
  cycleImprovement: 8.5
})

const sourceStats = ref([
  { name: '二维码', count: 6850, percentage: 43.2, conversionRate: 18.5, color: '#2080f0' },
  { name: '分享链接', count: 4230, percentage: 26.7, conversionRate: 22.3, color: '#18a058' },
  { name: '好友邀请', count: 2890, percentage: 18.2, conversionRate: 28.7, color: '#f0a020' },
  { name: '群聊', count: 1450, percentage: 9.1, conversionRate: 12.8, color: '#d03050' },
  { name: '搜索添加', count: 427, percentage: 2.8, conversionRate: 15.2, color: '#722ed1' }
])

const statusFlow = ref([
  { from: '新客户', to: '潜在客户', count: 1250 },
  { from: '潜在客户', to: '活跃客户', count: 890 },
  { from: '活跃客户', to: '已转化', count: 456 },
  { from: '活跃客户', to: '非活跃', count: 234 }
])

const behaviorData = ref({
  avgPages: 5.8,
  avgDuration: 8.5,
  bounceRate: 25.8,
  avgMessages: 12.3,
  responseTime: 15.2,
  activityScore: 78.5
})

const conversionPath = ref([
  { name: '首次访问', rate: 100, icon: EyeOutline, color: '#2080f0' },
  { name: '深度浏览', rate: 68, icon: EyeOutline, color: '#18a058' },
  { name: '留下联系方式', rate: 35, icon: ChatbubbleOutline, color: '#f0a020' },
  { name: '产品咨询', rate: 22, icon: ChatbubbleOutline, color: '#d03050' },
  { name: '成功转化', rate: 16, icon: CheckmarkCircleOutline, color: '#722ed1' }
])

const valueSegments = ref([
  { name: '高价值客户', count: 1580, percentage: 10, avgValue: 45000, color: '#d03050' },
  { name: '中价值客户', count: 4750, percentage: 30, avgValue: 15000, color: '#f0a020' },
  { name: '低价值客户', count: 6320, percentage: 40, avgValue: 3500, color: '#18a058' },
  { name: '潜在客户', count: 3197, percentage: 20, avgValue: 0, color: '#2080f0' }
])

const rfmSegments = ref([
  {
    name: '重要价值客户',
    count: 1580,
    description: '最近购买、频次高、金额大',
    strategy: '提供VIP服务，维护关系'
  },
  {
    name: '重要发展客户',
    count: 2340,
    description: '最近购买、频次低、金额大',
    strategy: '增加互动频次，提升忠诚度'
  },
  {
    name: '重要保持客户',
    count: 1890,
    description: '较久未购买、频次高、金额大',
    strategy: '主动联系，了解需求变化'
  },
  {
    name: '重要挽留客户',
    count: 980,
    description: '较久未购买、频次低、金额大',
    strategy: '特别关注，制定挽留策略'
  }
])

const recommendations = ref([
  {
    id: '1',
    icon: AtOutline,
    color: '#d03050',
    priority: 'high',
    title: '优化二维码转化率',
    description: '二维码是主要获客渠道但转化率偏低，建议优化落地页和引导流程',
    impact: '转化率提升5-8%',
    actionText: '立即优化'
  },
  {
    id: '2',
    icon: BulbOutline,
    color: '#f0a020',
    priority: 'high',
    title: '加强好友邀请激励',
    description: '好友邀请转化率最高，建议加大激励力度扩大这一渠道',
    impact: '获客量增长20%',
    actionText: '制定方案'
  },
  {
    id: '3',
    icon: AlertCircleOutline,
    color: '#2080f0',
    priority: 'medium',
    title: '关注客户流失预警',
    description: '部分高价值客户出现流失迹象，建议建立预警机制',
    impact: '减少流失15%',
    actionText: '查看详情'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

const runAnalysis = async () => {
  loading.value = true
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    await nextTick()
    initCharts()
    message.success('分析完成')
  } catch (error) {
    message.error('分析失败，请重试')
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  message.info('报告导出功能开发中')
}

const initCharts = () => {
  // 客户趋势图
  if (trendChartRef.value) {
    const chart = echarts.init(trendChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['新增客户', '活跃客户', '转化客户'] },
      xAxis: {
        type: 'category',
        data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '新增客户',
          type: 'line',
          data: [120, 132, 101, 134, 90, 230],
          itemStyle: { color: '#2080f0' }
        },
        {
          name: '活跃客户',
          type: 'line',
          data: [220, 182, 191, 234, 290, 330],
          itemStyle: { color: '#18a058' }
        },
        {
          name: '转化客户',
          type: 'line',
          data: [150, 232, 201, 154, 190, 330],
          itemStyle: { color: '#f0a020' }
        }
      ]
    })
  }

  // 客户来源图
  if (sourceChartRef.value) {
    const chart = echarts.init(sourceChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: sourceStats.value.map(item => ({
          value: item.count,
          name: item.name,
          itemStyle: { color: item.color }
        }))
      }]
    })
  }

  // 客户状态图
  if (statusChartRef.value) {
    const chart = echarts.init(statusChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 8923, name: '活跃', itemStyle: { color: '#18a058' } },
          { value: 4230, name: '潜在', itemStyle: { color: '#f0a020' } },
          { value: 2156, name: '非活跃', itemStyle: { color: '#909399' } },
          { value: 538, name: '已流失', itemStyle: { color: '#d03050' } }
        ]
      }]
    })
  }

  // 浏览行为图
  if (viewBehaviorChartRef.value) {
    const chart = echarts.init(viewBehaviorChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['首页', '产品页', '价格页', '案例页', '联系页']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [2847, 1890, 1456, 1234, 890],
        itemStyle: { color: '#2080f0' }
      }]
    })
  }

  // 互动行为图
  if (interactionChartRef.value) {
    const chart = echarts.init(interactionChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['咨询', '分享', '点赞', '评论', '转发']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [1234, 890, 567, 345, 234],
        itemStyle: { color: '#18a058' }
      }]
    })
  }

  // 价值分布图
  if (valueDistributionChartRef.value) {
    const chart = echarts.init(valueDistributionChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['0-1K', '1K-5K', '5K-1W', '1W-3W', '3W-5W', '5W+']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [3197, 2890, 3430, 2340, 1580, 890],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#2080f0' },
            { offset: 1, color: '#87ceeb' }
          ])
        }
      }]
    })
  }

  // RFM散点图
  if (rfmChartRef.value) {
    const chart = echarts.init(rfmChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      xAxis: { name: 'Frequency', type: 'value' },
      yAxis: { name: 'Monetary', type: 'value' },
      series: [{
        type: 'scatter',
        symbolSize: (data: number[]) => Math.sqrt(data[2]) * 2,
        data: [
          [8.5, 45000, 1580],
          [3.2, 15000, 2340],
          [6.8, 8500, 1890],
          [1.5, 3500, 980]
        ],
        itemStyle: { color: '#2080f0' }
      }]
    })
  }
}

onMounted(() => {
  runAnalysis()
})
</script>

<style scoped>
.customer-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analysis-config {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.config-row:last-child {
  margin-bottom: 0;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.config-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.overview-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.metric-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  align-items: center;
}

.metric-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(32, 128, 240, 0.1);
  border-radius: 12px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.analysis-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.chart-container {
  padding: 20px;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.analysis-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.source-stats,
.status-flow {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
}

.stat-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-metrics {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.conversion-rate {
  color: #18a058;
  font-weight: 500;
}

.flow-item {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.flow-item:last-child {
  border-bottom: none;
}

.flow-from,
.flow-to {
  font-weight: 500;
  color: #1a1a1a;
}

.flow-arrow {
  color: #666;
}

.flow-count {
  margin-left: auto;
  font-size: 12px;
  color: #666;
}

.behavior-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  padding: 20px;
}

.behavior-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.behavior-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.behavior-chart {
  height: 200px;
  margin-bottom: 12px;
}

.behavior-insights {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.insight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.insight-label {
  font-size: 12px;
  color: #666;
}

.insight-value {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.conversion-path {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 20px;
  overflow-x: auto;
}

.path-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.step-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  border: 2px solid #e0e0e0;
}

.step-content {
  text-align: center;
}

.step-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.step-rate {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.step-arrow {
  font-size: 20px;
  color: #666;
  margin: 0 8px;
}

.value-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 20px;
}

.value-distribution,
.value-segments {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.distribution-title,
.segment-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.distribution-chart {
  height: 200px;
}

.segment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-item {
  background: white;
  border-radius: 6px;
  padding: 12px;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.segment-name {
  font-weight: 500;
  color: #1a1a1a;
}

.segment-percentage {
  font-weight: 600;
  color: #2080f0;
}

.segment-bar {
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  margin-bottom: 8px;
  overflow: hidden;
}

.segment-fill {
  height: 100%;
  border-radius: 3px;
}

.segment-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.rfm-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 20px;
}

.rfm-matrix,
.rfm-segments {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.matrix-title,
.rfm-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.matrix-chart {
  height: 250px;
}

.rfm-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rfm-item {
  background: white;
  border-radius: 6px;
  padding: 12px;
}

.rfm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rfm-name {
  font-weight: 500;
  color: #1a1a1a;
}

.rfm-count {
  font-size: 12px;
  color: #666;
}

.rfm-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.rfm-strategy {
  font-size: 12px;
  color: #2080f0;
  font-weight: 500;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.recommendation-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.recommendation-priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

.recommendation-priority.high {
  background: #fee;
  color: #d03050;
}

.recommendation-priority.medium {
  background: #fff7e6;
  color: #f0a020;
}

.recommendation-priority.low {
  background: #f0f9ff;
  color: #2080f0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.recommendation-desc {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.recommendation-impact {
  display: flex;
  gap: 4px;
  align-items: center;
}

.impact-label {
  font-size: 10px;
  color: #999;
}

.impact-value {
  font-size: 10px;
  font-weight: 500;
  color: #18a058;
}

.recommendation-action {
  display: flex;
  align-items: flex-start;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>