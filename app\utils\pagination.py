from typing import TypeVar, Generic, List, Optional, Dict, Any
from pydantic import BaseModel, Field
from sqlalchemy.orm import Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from math import ceil


T = TypeVar('T')


class PaginationParams(BaseModel):
    """分页参数"""
    
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量，最大100")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size


class PaginationMeta(BaseModel):
    """分页元数据"""
    
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页数量")
    total: int = Field(description="总记录数")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    next_page: Optional[int] = Field(description="下一页页码")
    prev_page: Optional[int] = Field(description="上一页页码")


class PaginationResult(BaseModel, Generic[T]):
    """分页结果"""
    
    items: List[T] = Field(description="数据列表")
    meta: PaginationMeta = Field(description="分页元数据")
    
    class Config:
        arbitrary_types_allowed = True


class Paginator:
    """分页器"""
    
    @staticmethod
    def create_meta(
        page: int,
        page_size: int,
        total: int
    ) -> PaginationMeta:
        """创建分页元数据"""
        total_pages = ceil(total / page_size) if total > 0 else 0
        
        return PaginationMeta(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1,
            next_page=page + 1 if page < total_pages else None,
            prev_page=page - 1 if page > 1 else None
        )
    
    @staticmethod
    async def paginate_query(
        session: AsyncSession,
        query: Any,
        params: PaginationParams
    ) -> PaginationResult[Any]:
        """对SQLAlchemy查询进行分页"""
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar() or 0
        
        # 获取分页数据
        paginated_query = query.offset(params.offset).limit(params.limit)
        result = await session.execute(paginated_query)
        items = result.scalars().all()
        
        # 创建分页元数据
        meta = Paginator.create_meta(params.page, params.page_size, total)
        
        return PaginationResult(items=items, meta=meta)
    
    @staticmethod
    def paginate_list(
        items: List[T],
        params: PaginationParams
    ) -> PaginationResult[T]:
        """对列表进行分页"""
        total = len(items)
        start = params.offset
        end = start + params.page_size
        
        paginated_items = items[start:end]
        meta = Paginator.create_meta(params.page, params.page_size, total)
        
        return PaginationResult(items=paginated_items, meta=meta)
    
    @staticmethod
    def get_pagination_links(
        base_url: str,
        meta: PaginationMeta,
        query_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Optional[str]]:
        """生成分页链接"""
        if query_params is None:
            query_params = {}
        
        def build_url(page: int) -> str:
            params = query_params.copy()
            params['page'] = page
            params['page_size'] = meta.page_size
            
            query_string = '&'.join(f"{k}={v}" for k, v in params.items())
            return f"{base_url}?{query_string}"
        
        return {
            'first': build_url(1) if meta.total_pages > 0 else None,
            'last': build_url(meta.total_pages) if meta.total_pages > 0 else None,
            'prev': build_url(meta.prev_page) if meta.prev_page else None,
            'next': build_url(meta.next_page) if meta.next_page else None,
            'current': build_url(meta.page)
        }


class SearchParams(BaseModel):
    """搜索参数"""
    
    keyword: Optional[str] = Field(default=None, description="搜索关键词")
    fields: Optional[List[str]] = Field(default=None, description="搜索字段")
    
    def is_empty(self) -> bool:
        """检查搜索参数是否为空"""
        return not self.keyword or not self.keyword.strip()


class SortParams(BaseModel):
    """排序参数"""
    
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: str = Field(default="asc", regex="^(asc|desc)$", description="排序方向")
    
    def is_desc(self) -> bool:
        """是否降序"""
        return self.sort_order.lower() == "desc"


class FilterParams(BaseModel):
    """过滤参数基类"""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，排除None值"""
        return {k: v for k, v in self.dict().items() if v is not None}
    
    def has_filters(self) -> bool:
        """检查是否有过滤条件"""
        return bool(self.to_dict())


class QueryBuilder:
    """查询构建器"""
    
    @staticmethod
    def apply_search(
        query: Any,
        model: Any,
        search_params: SearchParams
    ) -> Any:
        """应用搜索条件"""
        if search_params.is_empty():
            return query
        
        keyword = f"%{search_params.keyword.strip()}%"
        
        if search_params.fields:
            # 在指定字段中搜索
            conditions = []
            for field in search_params.fields:
                if hasattr(model, field):
                    attr = getattr(model, field)
                    conditions.append(attr.ilike(keyword))
            
            if conditions:
                from sqlalchemy import or_
                query = query.where(or_(*conditions))
        
        return query
    
    @staticmethod
    def apply_sort(
        query: Any,
        model: Any,
        sort_params: SortParams
    ) -> Any:
        """应用排序条件"""
        if not sort_params.sort_by:
            return query
        
        if hasattr(model, sort_params.sort_by):
            attr = getattr(model, sort_params.sort_by)
            if sort_params.is_desc():
                query = query.order_by(attr.desc())
            else:
                query = query.order_by(attr.asc())
        
        return query
    
    @staticmethod
    def apply_filters(
        query: Any,
        model: Any,
        filters: Dict[str, Any]
    ) -> Any:
        """应用过滤条件"""
        for field, value in filters.items():
            if hasattr(model, field) and value is not None:
                attr = getattr(model, field)
                
                if isinstance(value, list):
                    query = query.where(attr.in_(value))
                elif isinstance(value, dict):
                    # 支持范围查询 {"gte": 10, "lte": 20}
                    if "gte" in value:
                        query = query.where(attr >= value["gte"])
                    if "lte" in value:
                        query = query.where(attr <= value["lte"])
                    if "gt" in value:
                        query = query.where(attr > value["gt"])
                    if "lt" in value:
                        query = query.where(attr < value["lt"])
                else:
                    query = query.where(attr == value)
        
        return query


# 便捷函数
def create_pagination_params(
    page: int = 1,
    page_size: int = 20
) -> PaginationParams:
    """创建分页参数"""
    return PaginationParams(page=page, page_size=page_size)


def create_search_params(
    keyword: Optional[str] = None,
    fields: Optional[List[str]] = None
) -> SearchParams:
    """创建搜索参数"""
    return SearchParams(keyword=keyword, fields=fields)


def create_sort_params(
    sort_by: Optional[str] = None,
    sort_order: str = "asc"
) -> SortParams:
    """创建排序参数"""
    return SortParams(sort_by=sort_by, sort_order=sort_order)