import mysql from 'mysql2/promise';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config();

async function createTables() {
  console.log('🔄 开始创建 MySQL 数据库表结构...');
  
 // 从环境变量读取数据库配置
const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'workchat_admin',
    charset: 'utf8mb4'
  };

  console.log('📋 连接配置:');
  console.log(`   主机: ${config.host}:${config.port}`);
  console.log(`   数据库: ${config.database}`);
  console.log(`   用户: ${config.user}`);

  // 首先连接到MySQL服务器（不指定数据库）
  const serverConfig = {
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
    charset: config.charset
  };

  let serverConnection;
  let dbConnection;
  
  try {
    // 先连接到MySQL服务器（不指定数据库）
    serverConnection = await mysql.createConnection(serverConfig);
    console.log('✅ MySQL 服务器连接创建成功!');
    
    // 创建数据库（如果不存在）
    await serverConnection.query(`CREATE DATABASE IF NOT EXISTS \`${config.database}\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 '${config.database}' 创建成功!`);
    
    // 关闭服务器连接
    await serverConnection.end();
    
    // 连接到指定数据库
    dbConnection = await mysql.createConnection(config);
    console.log('✅ 数据库连接创建成功!');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'mysql', 'create-mysql-tables.sql');
    const sqlContent = await fs.readFile(sqlFilePath, 'utf-8');
    console.log('📄 SQL文件读取成功');

    // 先按行分割，移除注释和空行
    const lines = sqlContent.split('\n').map(line => line.trim());
    const cleanedLines = lines.filter(line => {
      return line.length > 0 && 
             !line.startsWith('--') && 
             !line.startsWith('/*') &&
             !line.startsWith('*/');
    });
    
    // 重新组合成完整的SQL内容
    const cleanedContent = cleanedLines.join('\n');
    
    // 按分号分割SQL语句
    const allStatements = cleanedContent.split(';').map(stmt => stmt.trim());
    console.log(`🔍 总共分割出 ${allStatements.length} 条语句`);
    
    const sqlStatements = allStatements.filter((stmt, index) => {
        if (stmt.length === 0) {
          console.log(`  语句 ${index + 1}: 空语句，跳过`);
          return false;
        }
        
        const upperStmt = stmt.toUpperCase();
        if (upperStmt.startsWith('CREATE DATABASE')) {
          console.log(`  语句 ${index + 1}: CREATE DATABASE，跳过`);
          return false;
        }
        if (upperStmt.startsWith('USE ')) {
          console.log(`  语句 ${index + 1}: USE，跳过`);
          return false;
        }
        if (upperStmt.startsWith('SET ')) {
          console.log(`  语句 ${index + 1}: SET，跳过`);
          return false;
        }
        
        console.log(`  语句 ${index + 1}: ${stmt.substring(0, 50)}... - 保留`);
        return true;
      });

    console.log(`📊 准备执行 ${sqlStatements.length} 条SQL语句`);

    // 逐条执行SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      if (statement) {
        try {
          await dbConnection.execute(statement);
          
          // 提取表名（如果是CREATE TABLE语句）
          const createTableMatch = statement.match(/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?`?([^`\s]+)`?/i);
          if (createTableMatch) {
            console.log(`✅ 表 '${createTableMatch[1]}' 创建成功`);
          } else {
            console.log(`✅ SQL语句 ${i + 1} 执行成功`);
          }
        } catch (error) {
          // 如果是表已存在的错误，跳过
          if (error instanceof Error && (error.message.includes('already exists') || error.message.includes('Table') && error.message.includes('already exists'))) {
            const createTableMatch = statement.match(/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?`?([^`\s]+)`?/i);
            if (createTableMatch) {
              console.log(`⚠️  表 '${createTableMatch[1]}' 已存在，跳过创建`);
            }
          } else {
            console.error(`❌ SQL语句 ${i + 1} 执行失败:`, error);
            console.error(`SQL内容: ${statement.substring(0, 100)}...`);
            throw error;
          }
        }
      }
    }

    // 验证表创建结果
    const [tables] = await dbConnection.query('SHOW TABLES');
    console.log(`\n📋 数据库中的表 (${(tables as any[]).length} 个):`);
    (tables as any[]).forEach((table: any, index: number) => {
      const tableName = Object.values(table)[0];
      console.log(`   ${index + 1}. ${tableName}`);
    });

    console.log('\n🎉 MySQL 表结构创建完成!');
    return true;
    
  } catch (error) {
    console.error('❌ 创建表结构失败:');
    console.error('错误详情:', error);
    return false;
    
  } finally {
    if (serverConnection) {
      try {
        await serverConnection.end();
      } catch (closeError) {
        console.error('关闭服务器连接时出错:', closeError);
      }
    }
    if (dbConnection) {
      try {
        await dbConnection.end();
        console.log('🔌 MySQL 连接已关闭');
      } catch (closeError) {
        console.error('关闭数据库连接时出错:', closeError);
      }
    }
  }
}

// 运行创建表脚本
createTables()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('创建表脚本执行失败:', error);
    process.exit(1);
  });

export default createTables;