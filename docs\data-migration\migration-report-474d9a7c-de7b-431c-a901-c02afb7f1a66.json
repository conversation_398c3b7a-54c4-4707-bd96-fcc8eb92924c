{"migrationId": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "timestamp": "2025-08-18T07:19:11.928Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:18:15.595Z", "errors": [], "endTime": "2025-08-18T07:18:20.133Z", "duration": 4538}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:18:20.137Z", "errors": [], "endTime": "2025-08-18T07:18:22.828Z", "duration": 2691}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:18:22.830Z", "errors": [], "endTime": "2025-08-18T07:18:25.685Z", "duration": 2855}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:18:25.688Z", "errors": [], "endTime": "2025-08-18T07:18:28.093Z", "duration": 2405}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:28.096Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:18:28.614Z", "errors": [], "endTime": "2025-08-18T07:18:32.240Z", "duration": 3626}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:18:32.242Z", "errors": [], "endTime": "2025-08-18T07:18:36.164Z", "duration": 3922}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:18:36.167Z", "errors": [], "endTime": "2025-08-18T07:18:39.307Z", "duration": 3140}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:39.309Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:18:39.962Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:18:41.882Z", "duration": 1920}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:41.883Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:42.164Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:18:49.355Z", "errors": ["Unknown column 'summary' in 'field list'"], "endTime": "2025-08-18T07:18:55.282Z", "duration": 5927}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:55.284Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:18:55.684Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:18:58.500Z", "duration": 2816}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:58.503Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:58.780Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:18:59.827Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:19:03.515Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:19:05.136Z", "errors": ["Field 'title' doesn't have a default value"], "endTime": "2025-08-18T07:19:09.464Z", "duration": 4328}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:19:09.466Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:19:11.925Z", "duration": 2459}], "logs": [{"id": "a080835f-38de-4d95-85f3-e26995b5443d", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:18:15.595Z", "end_time": "2025-08-18T07:18:20.133Z", "duration_ms": 4538}, {"id": "58bc1ca2-9b20-46ef-b75c-d3050352f4ac", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:18:20.137Z", "end_time": "2025-08-18T07:18:22.828Z", "duration_ms": 2691}, {"id": "b636742b-226d-48b3-ab9d-a0ce0733eb1b", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:18:22.830Z", "end_time": "2025-08-18T07:18:25.685Z", "duration_ms": 2855}, {"id": "3de1f702-5973-4ebc-a594-bd5c7fb186f7", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:18:25.688Z", "end_time": "2025-08-18T07:18:28.093Z", "duration_ms": 2405}, {"id": "8fb13273-7c9f-45c3-8419-b18bf8180c7d", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:28.096Z", "end_time": "2025-08-18T07:18:28.614Z", "duration_ms": 518}, {"id": "e1e82cbd-fa9f-49d6-b78f-4cc77d51687b", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:18:28.614Z", "end_time": "2025-08-18T07:18:32.240Z", "duration_ms": 3626}, {"id": "cceef9bc-2bac-40dd-8edf-66afc4f56436", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:18:32.242Z", "end_time": "2025-08-18T07:18:36.164Z", "duration_ms": 3922}, {"id": "56a4bf75-5254-4a46-ae61-c3eb784b0aba", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:18:36.167Z", "end_time": "2025-08-18T07:18:39.307Z", "duration_ms": 3140}, {"id": "440ca657-9eba-40ea-b811-7370ec67ecea", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:39.309Z", "end_time": "2025-08-18T07:18:39.962Z", "duration_ms": 653}, {"id": "3f0141bb-a9d7-4db9-aa45-fcbd0dc47e7c", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:39.962Z", "end_time": "2025-08-18T07:18:41.882Z", "duration_ms": 1920}, {"id": "d121f119-7ce3-46ea-ae2f-816fc6ce06a9", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:41.884Z", "end_time": "2025-08-18T07:18:42.163Z", "duration_ms": 279}, {"id": "ec3d0b2f-26bb-4b99-a752-96a6914d6c0e", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:42.164Z", "end_time": "2025-08-18T07:18:49.355Z", "duration_ms": 7191}, {"id": "4b5ef07e-6f9b-4d43-8315-c2a9fc39f4c2", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:49.355Z", "end_time": "2025-08-18T07:18:55.282Z", "duration_ms": 5927}, {"id": "772eab8a-7c86-43d4-a592-0020df6a0bf1", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:55.284Z", "end_time": "2025-08-18T07:18:55.684Z", "duration_ms": 400}, {"id": "4d01ca38-4b3d-4a5f-8e83-d68da76350bf", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:55.684Z", "end_time": "2025-08-18T07:18:58.500Z", "duration_ms": 2816}, {"id": "cff428cf-a545-4228-bf71-6ec01b560c09", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:58.503Z", "end_time": "2025-08-18T07:18:58.780Z", "duration_ms": 277}, {"id": "6578e18c-41b8-466d-96e5-90161b029996", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:58.780Z", "end_time": "2025-08-18T07:18:59.826Z", "duration_ms": 1046}, {"id": "a3f9f307-c0c7-4f62-8120-49667a8c9d95", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:18:59.827Z", "end_time": "2025-08-18T07:19:03.515Z", "duration_ms": 3688}, {"id": "02ee5645-be7f-4373-9cad-7c15ac23b0d7", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:19:03.515Z", "end_time": "2025-08-18T07:19:05.136Z", "duration_ms": 1621}, {"id": "50faeb48-d697-476b-b160-be1269cae3ab", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:19:05.136Z", "end_time": "2025-08-18T07:19:09.464Z", "duration_ms": 4328}, {"id": "a2748113-9da4-41f3-8b76-1d69ef9631ec", "migration_id": "474d9a7c-de7b-431c-a901-c02afb7f1a66", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:19:09.466Z", "end_time": "2025-08-18T07:19:11.925Z", "duration_ms": 2459}]}