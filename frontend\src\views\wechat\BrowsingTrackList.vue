<template>
  <div class="browsing-track-list">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
  
        <p class="page-description">分析客户浏览行为，了解用户兴趣和偏好</p>
      </div>
      <div class="header-right">
        <n-space>
          <n-button type="primary" @click="showHeatmapModal = true">
            <template #icon>
              <n-icon><BarChartOutline /></n-icon>
            </template>
            热力图分析
          </n-button>
          <n-button @click="handleExportData">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出数据
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="总浏览量" :value="browsingTracksTotal">
              <template #prefix>
                <n-icon color="#18a058">
                  <EyeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="独立访客" :value="uniqueVisitors">
              <template #prefix>
                <n-icon color="#2080f0">
                  <PersonOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="平均停留时间" :value="averageDuration + 's'">
              <template #prefix>
                <n-icon color="#f0a020">
                  <TimeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="跳出率" :value="bounceRate + '%'">
              <template #prefix>
                <n-icon color="#d03050">
                  <TrendingDownOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <n-form inline :model="filterForm" label-placement="left">
        <n-form-item label="搜索">
          <n-input
            v-model:value="filterForm.keyword"
            placeholder="搜索页面标题或URL"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="客户">
          <n-select
            v-model:value="filterForm.customer_id"
            placeholder="选择客户"
            clearable
            filterable
            style="width: 200px"
            :options="customerOptions"
            :loading="customersLoading"
          />
        </n-form-item>
        <n-form-item label="页面类型">
          <n-select
            v-model:value="filterForm.page_type"
            placeholder="选择页面类型"
            clearable
            style="width: 150px"
            :options="pageTypeOptions"
          />
        </n-form-item>
        <n-form-item label="访问时间">
          <n-date-picker
            v-model:value="filterForm.date_range"
            type="daterange"
            clearable
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="停留时间">
          <n-input-group>
            <n-input-number
              v-model:value="filterForm.min_duration"
              placeholder="最小"
              style="width: 80px"
              :min="0"
            />
            <n-input-number
              v-model:value="filterForm.max_duration"
              placeholder="最大"
              style="width: 80px"
              :min="0"
            />
          </n-input-group>
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button @click="handleResetFilter">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 浏览轨迹列表 -->
    <n-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>浏览轨迹</span>
          <n-space>
            <n-button
              size="small"
              :disabled="!selectedRowKeys.length"
              @click="handleBatchAnalyze"
            >
              批量分析
            </n-button>
            <n-button
              size="small"
              :disabled="!selectedRowKeys.length"
              @click="handleBatchDelete"
            >
              批量删除
            </n-button>
          </n-space>
        </div>
      </template>

      <n-data-table
        :columns="columns"
        :data="browsingTracks"
        :loading="browsingTracksLoading"
        :pagination="pagination"
        :row-key="(row: BrowsingTrack) => row.id"
        v-model:checked-row-keys="selectedRowKeys"
        :scroll-x="1400"
        striped
      />
    </n-card>

    <!-- 轨迹详情抽屉 -->
    <n-drawer
      v-model:show="showTrackDetail"
      :width="600"
      placement="right"
    >
      <n-drawer-content title="浏览轨迹详情">
        <TrackDetailPanel
          v-if="currentBrowsingTrack"
          :track="currentBrowsingTrack"
          @close="showTrackDetail = false"
        />
      </n-drawer-content>
    </n-drawer>

    <!-- 热力图分析模态框 -->
    <n-modal
      v-model:show="showHeatmapModal"
      preset="card"
      title="页面热力图分析"
      style="width: 800px"
    >
      <HeatmapAnalysis @close="showHeatmapModal = false" />
    </n-modal>

    <!-- 行为分析模态框 -->
    <n-modal
      v-model:show="showBehaviorModal"
      preset="card"
      title="用户行为分析"
      style="width: 900px"
    >
      <BehaviorAnalysis
        :track-ids="selectedRowKeys"
        @close="showBehaviorModal = false"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h, markRaw } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NIcon,
  NGrid,
  NGridItem,
  NStatistic,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NInputGroup,
  NInputNumber,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NModal,
  NTag,
  NTooltip,
  NTime,
  useMessage,
  useDialog,
  type DataTableColumns
} from 'naive-ui'
import {
  BarChartOutline,
  DownloadOutline,
  EyeOutline,
  PersonOutline,
  TimeOutline,
  TrendingDownOutline,
  SearchOutline,
  RefreshOutline,
  CreateOutline,
  TrashOutline,
  AnalyticsOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import TrackDetailPanel from './components/TrackDetailPanel.vue'
import HeatmapAnalysis from './components/HeatmapAnalysis.vue'
import BehaviorAnalysis from './components/BehaviorAnalysis.vue'
import type { BrowsingTrack } from '@/types'

const message = useMessage()
const dialog = useDialog()
const wechatStore = useWechatStore()

// 预创建图标组件，避免在render函数中重复创建
const ViewIcon = () => h(NIcon, null, { default: () => h(markRaw(EyeOutline)) })
const AnalyticsIcon = () => h(NIcon, null, { default: () => h(markRaw(AnalyticsOutline)) })
const DeleteIcon = () => h(NIcon, null, { default: () => h(markRaw(TrashOutline)) })

// 解构store状态
const {
  browsingTracks,
  browsingTracksLoading,
  browsingTracksTotal,
  currentBrowsingTrack
} = wechatStore

// 筛选表单
const filterForm = reactive({
  keyword: '',
  customer_id: null as number | null,
  page_type: null as string | null,
  date_range: null as [number, number] | null,
  min_duration: null as number | null,
  max_duration: null as number | null
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page
    fetchTracks()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    fetchTracks()
  },
  itemCount: computed(() => browsingTracksTotal)
})

// 选择的行
const selectedRowKeys = ref<number[]>([])

// 模态框状态
const showTrackDetail = ref(false)
const showHeatmapModal = ref(false)
const showBehaviorModal = ref(false)

// 客户选项
const customerOptions = ref<Array<{ label: string; value: number }>>([])
const customersLoading = ref(false)

// 页面类型选项
const pageTypeOptions = [
  { label: '首页', value: 'home' },
  { label: '产品页', value: 'product' },
  { label: '文章页', value: 'article' },
  { label: '活动页', value: 'activity' },
  { label: '关于我们', value: 'about' },
  { label: '联系我们', value: 'contact' },
  { label: '其他', value: 'other' }
]

// 统计数据
const uniqueVisitors = computed(() => {
  const visitors = new Set(browsingTracks.map(track => track.customer_id))
  return visitors.size
})

const averageDuration = computed(() => {
  if (browsingTracks.length === 0) return 0
  const totalDuration = browsingTracks.reduce((sum, track) => sum + track.duration, 0)
  return Math.round(totalDuration / browsingTracks.length)
})

const bounceRate = computed(() => {
  if (browsingTracks.length === 0) return 0
  const bounces = browsingTracks.filter(track => track.duration < 10).length
  return Math.round((bounces / browsingTracks.length) * 100)
})

// 表格列配置
const columns: DataTableColumns<BrowsingTrack> = [
  {
    type: 'selection'
  },
  {
    title: '客户信息',
    key: 'customer_info',
    width: 150,
    render: (row) => {
      return h('div', { class: 'customer-info' }, [
        h('div', { class: 'customer-name' }, row.openid ? row.openid.substring(0, 16) + '...' : '未知用户'),
        h('div', { class: 'customer-id' }, `ID: ${row.customer_id}`)
      ])
    }
  },
  {
    title: '页面信息',
    key: 'page_info',
    width: 250,
    render: (row) => {
      return h('div', { class: 'page-info' }, [
        h('div', { class: 'page-title' }, row.page_title || '无标题'),
        h('div', { class: 'page-url' }, row.page_url)
      ])
    }
  },
  {
    title: '页面类型',
    key: 'page_type',
    width: 100,
    render: (row) => {
      const typeMap: Record<string, { type: string; text: string }> = {
        home: { type: 'info', text: '首页' },
        product: { type: 'success', text: '产品页' },
        article: { type: 'warning', text: '文章页' },
        activity: { type: 'error', text: '活动页' },
        about: { type: 'default', text: '关于我们' },
        contact: { type: 'default', text: '联系我们' },
        other: { type: 'default', text: '其他' }
      }
      const pageType = typeMap[row.page_type || 'other'] || { type: 'default', text: '未知' }
      return h(NTag, { type: pageType.type as any, size: 'small' }, { default: () => pageType.text })
    }
  },
  {
    title: '停留时间',
    key: 'duration',
    width: 100,
    render: (row) => {
      const duration = row.duration
      let type = 'default'
      if (duration < 10) type = 'error'
      else if (duration < 60) type = 'warning'
      else type = 'success'
      
      return h(NTag, { type: type as any, size: 'small' }, { default: () => `${duration}s` })
    }
  },
  {
    title: '来源',
    key: 'referrer',
    width: 150,
    render: (row) => {
      if (!row.referrer) return '-'
      try {
        const url = new URL(row.referrer)
        return url.hostname
      } catch {
        return row.referrer.substring(0, 20) + '...'
      }
    }
  },
  {
    title: '用户代理',
    key: 'user_agent',
    width: 120,
    render: (row) => {
      if (!row.user_agent) return '-'
      
      // 简单的用户代理解析
      const ua = row.user_agent.toLowerCase()
      let device = '未知'
      let browser = '未知'
      
      if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
        device = '移动端'
      } else {
        device = '桌面端'
      }
      
      if (ua.includes('chrome')) browser = 'Chrome'
      else if (ua.includes('firefox')) browser = 'Firefox'
      else if (ua.includes('safari')) browser = 'Safari'
      else if (ua.includes('edge')) browser = 'Edge'
      
      return h('div', { class: 'user-agent-info' }, [
        h('div', device),
        h('div', { style: 'font-size: 12px; color: #666;' }, browser)
      ])
    }
  },
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 120,
    render: (row) => row.ip_address || '-'
  },
  {
    title: '访问时间',
    key: 'created_at',
    width: 150,
    render: (row) => {
      return h(NTime, { time: new Date(row.created_at), format: 'yyyy-MM-dd HH:mm:ss' })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render: (row) => {
      return h(NSpace, { size: 'small' }, [
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleViewTrack(row)
          }, { default: () => ViewIcon }),
          default: () => '查看详情'
        }),
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => handleAnalyzeTrack(row)
          }, { default: () => AnalyticsIcon }),
          default: () => '行为分析'
        }),
        h(NTooltip, { trigger: 'hover' }, {
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleDeleteTrack(row)
          }, { default: () => DeleteIcon }),
          default: () => '删除'
        })
      ])
    }
  }
]

// 获取浏览轨迹列表
const fetchTracks = async () => {
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filterForm,
      customer_id: filterForm.customer_id || undefined,
      date_range: filterForm.date_range && filterForm.date_range.length === 2 ? [
        new Date(filterForm.date_range[0]).toISOString().split('T')[0],
        new Date(filterForm.date_range[1]).toISOString().split('T')[0]
      ] as [string, string] : undefined
    }
    await wechatStore.fetchBrowsingTracks(params)
  } catch (error) {
    message.error('获取浏览轨迹失败')
  }
}

// 获取客户选项
const fetchCustomerOptions = async () => {
  customersLoading.value = true
  try {
    const response = await wechatStore.fetchWechatCustomers({ pageSize: 1000 })
    customerOptions.value = response.data.map(customer => ({
      label: customer.nickname || (customer.openid ? customer.openid.substring(0, 16) + '...' : customer.wechat_id),
      value: customer.id
    }))
  } catch (error) {
    console.error('获取客户选项失败:', error)
  } finally {
    customersLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTracks()
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    keyword: '',
    customer_id: null,
    page_type: null,
    date_range: null,
    min_duration: null,
    max_duration: null
  })
  handleSearch()
}

// 查看轨迹详情
const handleViewTrack = async (track: BrowsingTrack) => {
  try {
    await wechatStore.fetchBrowsingTrack(track.id)
    showTrackDetail.value = true
  } catch (error) {
    message.error('获取轨迹详情失败')
  }
}

// 分析轨迹
const handleAnalyzeTrack = (track: BrowsingTrack) => {
  selectedRowKeys.value = [track.id]
  showBehaviorModal.value = true
}

// 删除轨迹
const handleDeleteTrack = (track: BrowsingTrack) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这条浏览轨迹吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 实现删除轨迹API
        message.success('删除成功')
        fetchTracks()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 批量分析
const handleBatchAnalyze = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择轨迹记录')
    return
  }
  showBehaviorModal.value = true
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择轨迹记录')
    return
  }
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条浏览轨迹吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 实现批量删除轨迹API
        message.success('批量删除成功')
        fetchTracks()
        selectedRowKeys.value = []
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

// 导出数据
const handleExportData = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

// 初始化
onMounted(() => {
  fetchTracks()
  fetchCustomerOptions()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 24px;
}

.table-card {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.customer-id {
  font-size: 12px;
  color: #666;
}

.page-info {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-url {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-agent-info {
  display: flex;
  flex-direction: column;
}
</style>