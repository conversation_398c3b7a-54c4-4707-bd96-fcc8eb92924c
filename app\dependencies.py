"""FastAPI依赖注入

定义应用中常用的依赖项
"""

from typing import Optional, Generator
from datetime import datetime
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from app.database import get_db
from app.utils.security import verify_token
from app.services.crud import crud_user
from app.utils.logger import logger
from .config import settings
from .models.user import User

# HTTP Bearer认证
security = HTTPBearer(auto_error=False)


# 数据库会话依赖
def get_database() -> Generator[Session, None, None]:
    """获取数据库会话依赖
    
    这是get_db的别名，用于更清晰的依赖注入
    """
    yield from get_db()


# 请求日志依赖
def log_request(request: Request) -> Request:
    """记录请求日志
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Request: 原始请求对象
    """
    if settings.app.debug:
        logger.info(
            f"请求: {request.method} {request.url.path} "
            f"来源: {request.client.host if request.client else 'unknown'}"
        )
    return request


# 认证令牌依赖
def get_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """获取认证令牌
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        Optional[str]: JWT令牌或None
    """
    if credentials:
        return credentials.credentials
    return None


# 可选认证依赖
async def get_optional_current_user(
    token: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
) -> Optional[dict]:
    """获取当前用户（可选）
    
    如果有有效令牌则返回用户信息，否则返回None
    
    Args:
        token: JWT令牌
        db: 数据库会话
        
    Returns:
        Optional[dict]: 用户信息或None
    """
    if not token:
        return None
    
    try:
        # 验证JWT令牌
        payload = verify_token(token)
        if not payload:
            return None
            
        user_id = payload.get("sub")
        if not user_id:
            return None
            
        # 从数据库获取用户信息
        user = await crud_user.get(db, id=user_id)
        
        if not user or not user.is_active:
            return None
            
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "real_name": user.real_name,
            "is_superuser": user.is_superuser,
            "department_id": user.department_id
        }
    except Exception as e:
        logger.warning(f"令牌验证失败: {e}")
        return None


# 必需认证依赖
async def get_current_user(
    token: str = Depends(get_token),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """获取当前用户（必需）
    
    强制要求认证，如果没有有效令牌则抛出异常
    
    Args:
        token: JWT令牌
        db: 数据库会话
        
    Returns:
        dict: 用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = await get_optional_current_user(token, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


# 管理员权限依赖
async def get_admin_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """获取管理员用户
    
    验证当前用户是否具有管理员权限
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        dict: 管理员用户信息
        
    Raises:
        HTTPException: 权限不足时抛出403错误
    """
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    
    return current_user


# 分页参数依赖
def get_pagination_params(
    page: int = 1,
    page_size: int = 20
) -> dict:
    """获取分页参数
    
    Args:
        page: 页码，从1开始
        page_size: 每页数量，最大100
        
    Returns:
        dict: 分页参数
        
    Raises:
        HTTPException: 参数无效时抛出400错误
    """
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="页码必须大于0"
        )
    
    if page_size < 1 or page_size > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="每页数量必须在1-100之间"
        )
    
    return {
        "page": page,
        "page_size": page_size,
        "offset": (page - 1) * page_size,
        "limit": page_size
    }


# 排序参数依赖
def get_sort_params(
    sort_by: Optional[str] = None,
    sort_order: str = "asc"
) -> dict:
    """获取排序参数
    
    Args:
        sort_by: 排序字段
        sort_order: 排序顺序，asc或desc
        
    Returns:
        dict: 排序参数
        
    Raises:
        HTTPException: 参数无效时抛出400错误
    """
    if sort_order not in ["asc", "desc"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="排序顺序必须是asc或desc"
        )
    
    return {
        "sort_by": sort_by,
        "sort_order": sort_order
    }


# 搜索参数依赖
def get_search_params(
    q: Optional[str] = None,
    fields: Optional[str] = None
) -> dict:
    """获取搜索参数
    
    Args:
        q: 搜索关键词
        fields: 搜索字段，逗号分隔
        
    Returns:
        dict: 搜索参数
    """
    search_fields = []
    if fields:
        search_fields = [field.strip() for field in fields.split(",") if field.strip()]
    
    return {
        "query": q,
        "fields": search_fields
    }


# 请求时间戳依赖
def get_request_timestamp() -> datetime:
    """获取请求时间戳
    
    Returns:
        datetime: 当前时间戳
    """
    return datetime.now()


# 客户端信息依赖
def get_client_info(request: Request) -> dict:
    """获取客户端信息
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        dict: 客户端信息
    """
    return {
        "ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "referer": request.headers.get("referer"),
        "accept_language": request.headers.get("accept-language")
    }


# 组合依赖：通用查询参数
def get_common_query_params(
    pagination: dict = Depends(get_pagination_params),
    sort: dict = Depends(get_sort_params),
    search: dict = Depends(get_search_params)
) -> dict:
    """获取通用查询参数
    
    组合分页、排序和搜索参数
    
    Returns:
        dict: 通用查询参数
    """
    return {
        **pagination,
        **sort,
        **search
    }