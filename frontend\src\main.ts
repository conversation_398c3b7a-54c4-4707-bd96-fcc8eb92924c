import { createApp } from 'vue'
// import { create<PERSON><PERSON> } from 'pinia'

import App from './App.vue'
import router from './router'
import { setupStore, useAuthStore } from './stores'

// Naive UI
import {
  create,
  NConfigProvider,
  NDialogProvider,
  NLoadingBarProvider,
  NMessageProvider,
  NNotificationProvider,
  NButton,
  NCard,
  NSpace,
  NIcon,
  NInput,
  NSelect,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NGridItem,
  NDataTable,
  NModal,
  NDrawer,
  NDrawerContent,
  NTag,
  NAvatar,
  NStatistic,
  NProgress,
  NCheckbox,
  NCheckboxGroup,
  NRadio,
  NRadioGroup,
  NSwitch,
  NSlider,
  NInputNumber,
  NDatePicker,
  NTimeline,
  NTimelineItem,
  NTabs,
  NTabPane,
  NCollapse,
  NCollapseItem,
  NDescriptions,
  NDescriptionsItem,
  NEllipsis,
  NList,
  NListItem,
  NEmpty,
  N<PERSON>lert,
  NTooltip,
  NDivider,
  NMenu,
  NDropdown,
  NSpin,
  NUpload,
  NUploadDragger
} from 'naive-ui'

const naive = create({
  components: [
    NConfigProvider,
    NDialogProvider,
    NLoadingBarProvider,
    NMessageProvider,
    NNotificationProvider,
    NButton,
    NCard,
    NSpace,
    NIcon,
    NInput,
    NSelect,
    NForm,
    NFormItem,
    NFormItemGi,
    NGrid,
    NGridItem,
    NDataTable,
    NModal,
    NDrawer,
    NDrawerContent,
    NTag,
    NAvatar,
    NStatistic,
    NProgress,
    NCheckbox,
    NCheckboxGroup,
    NRadio,
    NRadioGroup,
    NSwitch,
    NSlider,
    NInputNumber,
    NDatePicker,
    NTimeline,
    NTimelineItem,
    NTabs,
    NTabPane,
    NCollapse,
    NCollapseItem,
    NDescriptions,
    NDescriptionsItem,
    NEllipsis,
    NList,
    NListItem,
    NEmpty,
    NAlert,
    NTooltip,
    NDivider,
    NMenu,
    NDropdown,
    NSpin,
    NUpload,
    NUploadDragger
  ]
})

const app = createApp(App)

// 设置store - 必须在router之前初始化
setupStore(app)

app.use(router)
app.use(naive)

// 初始化认证状态
const authStore = useAuthStore()
authStore.initAuth()

app.mount('#app')
