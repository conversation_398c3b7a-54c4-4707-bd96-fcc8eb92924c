#!/usr/bin/env python3
"""
检查数据库表结构，找出缺少的字段
"""

import pymysql
from app.config import settings
from app.models.user import User
from sqlalchemy import inspect

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=settings.database.host,
        port=settings.database.port,
        user=settings.database.username,
        password=settings.database.password,
        database=settings.database.database,
        charset='utf8mb4'
    )

def check_table_structure():
    """检查users表结构"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("=== 检查users表结构 ===")
        
        # 获取表结构
        cursor.execute("DESCRIBE users")
        db_columns = cursor.fetchall()
        
        print("\n数据库中的字段:")
        db_field_names = []
        for column in db_columns:
            field_name = column[0]
            field_type = column[1]
            nullable = column[2]
            key = column[3]
            default = column[4]
            extra = column[5]
            
            db_field_names.append(field_name)
            print(f"  {field_name}: {field_type} (NULL: {nullable}, Key: {key}, Default: {default}, Extra: {extra})")
        
        # 获取User模型中定义的字段
        print("\n\nUser模型中定义的字段:")
        model_columns = User.__table__.columns
        model_field_names = []
        
        for column in model_columns:
            model_field_names.append(column.name)
            print(f"  {column.name}: {column.type} (nullable: {column.nullable})")
        
        # 找出缺少的字段
        missing_in_db = set(model_field_names) - set(db_field_names)
        extra_in_db = set(db_field_names) - set(model_field_names)
        
        print("\n\n=== 字段对比结果 ===")
        if missing_in_db:
            print(f"❌ 数据库中缺少的字段: {', '.join(missing_in_db)}")
        else:
            print("✅ 数据库包含所有模型字段")
            
        if extra_in_db:
            print(f"⚠️  数据库中多余的字段: {', '.join(extra_in_db)}")
        else:
            print("✅ 没有多余字段")
            
        return missing_in_db, extra_in_db
        
    finally:
        cursor.close()
        conn.close()

def generate_alter_statements(missing_fields):
    """生成ALTER TABLE语句"""
    if not missing_fields:
        return []
    
    print("\n=== 生成ALTER TABLE语句 ===")
    
    # 字段定义映射
    field_definitions = {
        'real_name': 'VARCHAR(100) DEFAULT NULL COMMENT \'真实姓名\'',
        'nickname': 'VARCHAR(100) DEFAULT NULL COMMENT \'昵称\'',
        'avatar': 'VARCHAR(500) DEFAULT NULL COMMENT \'头像URL\'',
        'gender': 'ENUM(\'male\', \'female\', \'other\') DEFAULT NULL COMMENT \'性别\'',
        'birthday': 'DATE DEFAULT NULL COMMENT \'生日\'',
        'employee_id': 'VARCHAR(50) DEFAULT NULL COMMENT \'员工编号\'',
        'position': 'VARCHAR(100) DEFAULT NULL COMMENT \'职位\'',
        'hire_date': 'DATE DEFAULT NULL COMMENT \'入职日期\'',
        'work_phone': 'VARCHAR(20) DEFAULT NULL COMMENT \'工作电话\'',
        'address': 'TEXT DEFAULT NULL COMMENT \'地址\'',
        'is_verified': 'BOOLEAN DEFAULT FALSE COMMENT \'是否已验证\'',
        'last_login': 'DATETIME DEFAULT NULL COMMENT \'最后登录时间\'',
        'login_count': 'INT DEFAULT 0 COMMENT \'登录次数\'',
        'notes': 'TEXT DEFAULT NULL COMMENT \'备注\''
    }
    
    alter_statements = []
    for field in missing_fields:
        if field in field_definitions:
            statement = f"ALTER TABLE users ADD COLUMN {field} {field_definitions[field]};"
            alter_statements.append(statement)
            print(f"  {statement}")
        else:
            print(f"⚠️  未知字段 {field}，需要手动定义")
    
    return alter_statements

def main():
    """主函数"""
    try:
        missing_fields, extra_fields = check_table_structure()
        
        if missing_fields:
            alter_statements = generate_alter_statements(missing_fields)
            
            print("\n=== 修复建议 ===")
            print("请执行以下SQL语句来添加缺少的字段:")
            for statement in alter_statements:
                print(statement)
        else:
            print("\n✅ 表结构完整，无需修复")
            
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()