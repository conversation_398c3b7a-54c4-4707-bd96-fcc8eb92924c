import re
import uuid
import random
import string
import hashlib
import base64
import secrets
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, Generic
from decimal import Decimal, ROUND_HALF_UP
from urllib.parse import quote, unquote
import json
from datetime import datetime, date
from enum import Enum


T = TypeVar('T')


class StringUtils:
    """字符串工具类"""
    
    @staticmethod
    def is_empty(s: Optional[str]) -> bool:
        """检查字符串是否为空"""
        return not s or not s.strip()
    
    @staticmethod
    def is_not_empty(s: Optional[str]) -> bool:
        """检查字符串是否不为空"""
        return bool(s and s.strip())
    
    @staticmethod
    def truncate(s: str, length: int, suffix: str = "...") -> str:
        """截断字符串"""
        if len(s) <= length:
            return s
        return s[:length - len(suffix)] + suffix
    
    @staticmethod
    def camel_to_snake(s: str) -> str:
        """驼峰转下划线"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', s)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def to_snake_case(s: str) -> str:
        """转换为蛇形命名"""
        import re
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', s)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def to_camel_case(s: str) -> str:
        """转换为驼峰命名"""
        components = s.split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def snake_to_camel(s: str, capitalize_first: bool = False) -> str:
        """下划线转驼峰"""
        components = s.split('_')
        if capitalize_first:
            return ''.join(word.capitalize() for word in components)
        else:
            return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def kebab_to_camel(s: str, capitalize_first: bool = False) -> str:
        """短横线转驼峰"""
        components = s.split('-')
        if capitalize_first:
            return ''.join(word.capitalize() for word in components)
        else:
            return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def camel_to_kebab(s: str) -> str:
        """驼峰转短横线"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1-\2', s)
        return re.sub('([a-z0-9])([A-Z])', r'\1-\2', s1).lower()
    
    @staticmethod
    def mask_string(s: str, start: int = 3, end: int = 4, mask_char: str = "*") -> str:
        """掩码字符串"""
        if len(s) <= start + end:
            return mask_char * len(s)
        
        return s[:start] + mask_char * (len(s) - start - end) + s[-end:]
    
    @staticmethod
    def mask_email(email: str) -> str:
        """掩码邮箱"""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """掩码手机号"""
        # 移除非数字字符
        digits = re.sub(r'\D', '', phone)
        
        if len(digits) == 11:  # 中国手机号
            return f"{digits[:3]}****{digits[-4:]}"
        elif len(digits) >= 7:
            return f"{digits[:3]}****{digits[-4:]}"
        else:
            return '*' * len(phone)
    
    @staticmethod
    def generate_random_string(
        length: int = 8,
        include_digits: bool = True,
        include_lowercase: bool = True,
        include_uppercase: bool = True,
        include_symbols: bool = False,
        exclude_ambiguous: bool = True
    ) -> str:
        """生成随机字符串"""
        chars = ""
        
        if include_lowercase:
            chars += string.ascii_lowercase
        if include_uppercase:
            chars += string.ascii_uppercase
        if include_digits:
            chars += string.digits
        if include_symbols:
            chars += "!@#$%^&*"
        
        if exclude_ambiguous:
            # 排除容易混淆的字符
            ambiguous = "0O1lI"
            chars = ''.join(c for c in chars if c not in ambiguous)
        
        if not chars:
            raise ValueError("No character set selected")
        
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_valid_phone(phone: str, country: str = "CN") -> bool:
        """验证手机号格式"""
        if country == "CN":
            # 中国手机号
            pattern = r'^1[3-9]\d{9}$'
            return bool(re.match(pattern, re.sub(r'\D', '', phone)))
        else:
            # 简单的国际手机号验证
            digits = re.sub(r'\D', '', phone)
            return 7 <= len(digits) <= 15
    
    @staticmethod
    def extract_numbers(s: str) -> List[str]:
        """提取字符串中的数字"""
        return re.findall(r'\d+(?:\.\d+)?', s)
    
    @staticmethod
    def remove_html_tags(html: str) -> str:
        """移除HTML标签"""
        clean = re.compile('<.*?>')
        return re.sub(clean, '', html)
    
    @staticmethod
    def url_encode(s: str) -> str:
        """URL编码"""
        return quote(s, safe='')
    
    @staticmethod
    def url_decode(s: str) -> str:
        """URL解码"""
        return unquote(s)


class NumberUtils:
    """数字工具类"""
    
    @staticmethod
    def safe_int(value: Any, default: int = 0) -> int:
        """安全转换为整数"""
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_float(value: Any, default: float = 0.0) -> float:
        """安全转换为浮点数"""
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def format_number(num: Union[int, float], precision: int = 2) -> str:
        """格式化数字"""
        if isinstance(num, int):
            return str(num)
        return f"{num:.{precision}f}"
    
    @staticmethod
    def safe_decimal(value: Any, default: Optional[Decimal] = None) -> Decimal:
        """安全转换为Decimal"""
        if default is None:
            default = Decimal('0')
        
        try:
            return Decimal(str(value))
        except (ValueError, TypeError, decimal.InvalidOperation):
            return default
    
    @staticmethod
    def round_decimal(
        value: Union[float, Decimal],
        places: int = 2,
        rounding: str = ROUND_HALF_UP
    ) -> Decimal:
        """精确四舍五入"""
        if isinstance(value, float):
            value = Decimal(str(value))
        elif not isinstance(value, Decimal):
            value = Decimal(str(value))
        
        quantizer = Decimal('0.1') ** places
        return value.quantize(quantizer, rounding=rounding)
    
    @staticmethod
    def format_currency(
        amount: Union[int, float, Decimal],
        currency: str = "¥",
        places: int = 2
    ) -> str:
        """格式化货币"""
        if isinstance(amount, (int, float)):
            amount = Decimal(str(amount))
        
        rounded = NumberUtils.round_decimal(amount, places)
        return f"{currency}{rounded:,.{places}f}"
    
    @staticmethod
    def format_percentage(
        value: Union[int, float, Decimal],
        places: int = 2
    ) -> str:
        """格式化百分比"""
        if isinstance(value, (int, float)):
            value = Decimal(str(value))
        
        percentage = value * 100
        rounded = NumberUtils.round_decimal(percentage, places)
        return f"{rounded}%"
    
    @staticmethod
    def is_number(value: Any) -> bool:
        """检查是否为数字"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def clamp(value: Union[int, float], min_val: Union[int, float], max_val: Union[int, float]) -> Union[int, float]:
        """限制数值范围"""
        return max(min_val, min(value, max_val))


class ListUtils:
    """列表工具类"""
    
    @staticmethod
    def chunk(lst: List[T], size: int) -> List[List[T]]:
        """将列表分块"""
        return [lst[i:i + size] for i in range(0, len(lst), size)]
    
    @staticmethod
    def flatten(lst: List[Any]) -> List[Any]:
        """扁平化列表"""
        result = []
        for item in lst:
            if isinstance(item, list):
                result.extend(ListUtils.flatten(item))
            else:
                result.append(item)
        return result
    
    @staticmethod
    def unique(lst: List[T], key: Optional[Callable[[T], Any]] = None) -> List[T]:
        """去重"""
        if key is None:
            seen = set()
            result = []
            for item in lst:
                if item not in seen:
                    seen.add(item)
                    result.append(item)
            return result
        else:
            seen = set()
            result = []
            for item in lst:
                k = key(item)
                if k not in seen:
                    seen.add(k)
                    result.append(item)
            return result
    
    @staticmethod
    def group_by(lst: List[T], key: Callable[[T], Any]) -> Dict[Any, List[T]]:
        """按键分组"""
        groups = {}
        for item in lst:
            k = key(item)
            if k not in groups:
                groups[k] = []
            groups[k].append(item)
        return groups
    
    @staticmethod
    def find(lst: List[T], predicate: Callable[[T], bool]) -> Optional[T]:
        """查找第一个匹配的元素"""
        for item in lst:
            if predicate(item):
                return item
        return None
    
    @staticmethod
    def find_index(lst: List[T], predicate: Callable[[T], bool]) -> int:
        """查找第一个匹配元素的索引"""
        for i, item in enumerate(lst):
            if predicate(item):
                return i
        return -1
    
    @staticmethod
    def safe_get(lst: List[T], index: int, default: Optional[T] = None) -> Optional[T]:
        """安全获取列表元素"""
        try:
            return lst[index]
        except (IndexError, TypeError):
            return default


class DictUtils:
    """字典工具类"""
    
    @staticmethod
    def safe_get(d: Dict[str, Any], key: str, default: Any = None) -> Any:
        """安全获取字典值"""
        return d.get(key, default)
    
    @staticmethod
    def deep_get(d: Dict[str, Any], path: str, default: Any = None, separator: str = ".") -> Any:
        """深度获取嵌套字典值"""
        keys = path.split(separator)
        current = d
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return current
    
    @staticmethod
    def deep_set(d: Dict[str, Any], path: str, value: Any, separator: str = ".") -> None:
        """深度设置嵌套字典值"""
        keys = path.split(separator)
        current = d
        
        for key in keys[:-1]:
            if key not in current or not isinstance(current[key], dict):
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    @staticmethod
    def flatten_dict(d: Dict[str, Any], separator: str = ".", prefix: str = "") -> Dict[str, Any]:
        """扁平化字典"""
        result = {}
        
        for key, value in d.items():
            new_key = f"{prefix}{separator}{key}" if prefix else key
            
            if isinstance(value, dict):
                result.update(DictUtils.flatten_dict(value, separator, new_key))
            else:
                result[new_key] = value
        
        return result
    
    @staticmethod
    def filter_none(d: Dict[str, Any]) -> Dict[str, Any]:
        """过滤None值"""
        return {k: v for k, v in d.items() if v is not None}
    
    @staticmethod
    def filter_empty(d: Dict[str, Any]) -> Dict[str, Any]:
        """过滤空值（None、空字符串、空列表等）"""
        def is_empty(value):
            if value is None:
                return True
            if isinstance(value, (str, list, dict)) and len(value) == 0:
                return True
            return False
        
        return {k: v for k, v in d.items() if not is_empty(v)}
    
    @staticmethod
    def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个字典"""
        result = {}
        for d in dicts:
            result.update(d)
        return result


class HashUtils:
    """哈希工具类"""
    
    @staticmethod
    def md5(s: str, encoding: str = "utf-8") -> str:
        """MD5哈希"""
        return hashlib.md5(s.encode(encoding)).hexdigest()
    
    @staticmethod
    def sha1(s: str, encoding: str = "utf-8") -> str:
        """SHA1哈希"""
        return hashlib.sha1(s.encode(encoding)).hexdigest()
    
    @staticmethod
    def sha256(s: str, encoding: str = "utf-8") -> str:
        """SHA256哈希"""
        return hashlib.sha256(s.encode(encoding)).hexdigest()
    
    @staticmethod
    def sha512(s: str, encoding: str = "utf-8") -> str:
        """SHA512哈希"""
        return hashlib.sha512(s.encode(encoding)).hexdigest()


class UUIDUtils:
    """UUID工具类"""
    
    @staticmethod
    def uuid4() -> str:
        """生成UUID4"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate() -> str:
        """生成UUID（别名方法）"""
        return UUIDUtils.uuid4()
    
    @staticmethod
    def uuid4_hex() -> str:
        """生成UUID4（无连字符）"""
        return uuid.uuid4().hex
    
    @staticmethod
    def short_uuid(length: int = 8) -> str:
        """生成短UUID"""
        return UUIDUtils.uuid4_hex()[:length]
    
    @staticmethod
    def is_valid_uuid(uuid_string: str) -> bool:
        """验证UUID格式"""
        try:
            uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False


class Base64Utils:
    """Base64工具类"""
    
    @staticmethod
    def encode(s: str, encoding: str = "utf-8") -> str:
        """Base64编码"""
        return base64.b64encode(s.encode(encoding)).decode('ascii')
    
    @staticmethod
    def decode(s: str, encoding: str = "utf-8") -> str:
        """Base64解码"""
        return base64.b64decode(s).decode(encoding)
    
    @staticmethod
    def encode_bytes(data: bytes) -> str:
        """Base64编码字节数据"""
        return base64.b64encode(data).decode('ascii')
    
    @staticmethod
    def decode_bytes(s: str) -> bytes:
        """Base64解码为字节数据"""
        return base64.b64decode(s)
    
    @staticmethod
    def is_valid_base64(s: str) -> bool:
        """验证Base64格式"""
        try:
            base64.b64decode(s, validate=True)
            return True
        except Exception:
            return False


class JSONUtils:
    """JSON工具类"""
    
    @staticmethod
    def safe_loads(s: str, default: Any = None) -> Any:
        """安全解析JSON"""
        try:
            return json.loads(s)
        except (json.JSONDecodeError, TypeError):
            return default
    
    @staticmethod
    def safe_dumps(
        obj: Any,
        default: Any = None,
        ensure_ascii: bool = False,
        indent: Optional[int] = None
    ) -> str:
        """安全序列化JSON"""
        def json_serializer(obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, Decimal):
                return float(obj)
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            return str(obj)
        
        try:
            return json.dumps(
                obj,
                default=json_serializer,
                ensure_ascii=ensure_ascii,
                indent=indent
            )
        except (TypeError, ValueError):
            return json.dumps(default or {}, ensure_ascii=ensure_ascii, indent=indent)
    
    @staticmethod
    def pretty_print(obj: Any) -> str:
        """美化打印JSON"""
        return JSONUtils.safe_dumps(obj, indent=2)


# 便捷函数
def is_empty_string(s: str) -> bool:
    """检查字符串是否为空"""
    return StringUtils.is_empty(s)

def generate_uuid() -> str:
    """生成UUID"""
    return UUIDUtils.generate()

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """安全JSON解析"""
    return JSONUtils.safe_loads(json_str, default)

def is_valid_email(email: str) -> bool:
    """验证邮箱格式"""
    return StringUtils.is_valid_email(email)

def format_number(num: Union[int, float], precision: int = 2) -> str:
    """格式化数字"""
    return NumberUtils.format_number(num, precision)

def calculate_hash(data: str, algorithm: str = 'md5') -> str:
    """计算哈希值"""
    if algorithm.lower() == 'md5':
        return HashUtils.md5(data)
    elif algorithm.lower() == 'sha1':
        return HashUtils.sha1(data)
    elif algorithm.lower() == 'sha256':
        return HashUtils.sha256(data)
    elif algorithm.lower() == 'sha512':
        return HashUtils.sha512(data)
    else:
        return HashUtils.md5(data)