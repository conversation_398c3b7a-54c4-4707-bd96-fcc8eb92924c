{"migrationId": "ab228cf6-af53-4c95-b967-60bd6183836c", "timestamp": "2025-08-18T06:11:21.140Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 10, "failedTables": 11, "totalRecords": 165, "migratedRecords": 15, "failedRecords": 150}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:10:29.217Z", "errors": ["Unknown column 'department' in 'field list'", "数据验证失败: MySQL记录数(4) != 迁移记录数(0)"], "endTime": "2025-08-18T06:10:34.297Z", "duration": 5080}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:10:34.301Z", "errors": ["Unknown column 'display_name' in 'field list'"], "endTime": "2025-08-18T06:10:38.539Z", "duration": 4238}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T06:10:38.542Z", "errors": ["Unknown column 'display_name' in 'field list'"], "endTime": "2025-08-18T06:10:41.459Z", "duration": 2917}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:10:41.462Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`yysh_miniprogram`.`role_permissions`, CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T06:10:43.640Z", "duration": 2178}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:10:43.642Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:10:44.210Z", "errors": [], "endTime": "2025-08-18T06:10:47.625Z", "duration": 3415}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:10:47.628Z", "errors": ["Unknown column 'code' in 'field list'"], "endTime": "2025-08-18T06:10:50.513Z", "duration": 2885}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:10:50.515Z", "errors": ["Unknown column 'region' in 'field list'", "数据验证失败: MySQL记录数(10) != 迁移记录数(0)"], "endTime": "2025-08-18T06:10:53.668Z", "duration": 3153}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:10:53.670Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:10:54.703Z", "errors": ["Table 'yysh_miniprogram.marketing_campaigns' doesn't exist"], "endTime": "2025-08-18T06:10:56.894Z", "duration": 2191}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:10:56.898Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:10:57.189Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:10:57.953Z", "errors": ["Table 'yysh_miniprogram.meetings' doesn't exist"], "endTime": "2025-08-18T06:11:04.343Z", "duration": 6390}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:11:04.347Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:11:05.630Z", "errors": ["Unknown column 'auto_release_days' in 'field list'"], "endTime": "2025-08-18T06:11:08.200Z", "duration": 2570}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:11:08.203Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:11:08.900Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:11:10.365Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:11:11.989Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:11:13.689Z", "errors": ["Table 'yysh_miniprogram.follow_ups' doesn't exist"], "endTime": "2025-08-18T06:11:19.291Z", "duration": 5602}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:11:19.294Z", "errors": ["Table 'yysh_miniprogram.public_pool' doesn't exist"], "endTime": "2025-08-18T06:11:21.136Z", "duration": 1842}], "logs": [{"id": "99e315d7-8b93-4197-aec8-047cd7de66b3", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:29.217Z", "end_time": "2025-08-18T06:10:34.297Z", "duration_ms": 5080}, {"id": "4c21d192-7d01-418e-be58-87589141eec4", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:34.301Z", "end_time": "2025-08-18T06:10:38.539Z", "duration_ms": 4238}, {"id": "368458d0-1941-44b4-92a9-c38d50e1d43a", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:38.542Z", "end_time": "2025-08-18T06:10:41.459Z", "duration_ms": 2917}, {"id": "2b2a9326-bd88-4d09-af11-2f923dc52aa7", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:41.462Z", "end_time": "2025-08-18T06:10:43.640Z", "duration_ms": 2178}, {"id": "6012bb89-b032-4c65-9b1f-7de436137d53", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:43.642Z", "end_time": "2025-08-18T06:10:44.210Z", "duration_ms": 568}, {"id": "2b4e5734-c9cd-41fc-91e2-0ed2ea2571df", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:10:44.210Z", "end_time": "2025-08-18T06:10:47.625Z", "duration_ms": 3415}, {"id": "c94b24ab-26f6-47fa-8eb8-d48fc26e5fe2", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:47.628Z", "end_time": "2025-08-18T06:10:50.513Z", "duration_ms": 2885}, {"id": "21ba14e1-e4ca-4443-a1bf-5d403e4c4e8f", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:50.515Z", "end_time": "2025-08-18T06:10:53.668Z", "duration_ms": 3153}, {"id": "ad801160-cc18-404e-9242-6c0297ec5919", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:53.670Z", "end_time": "2025-08-18T06:10:54.703Z", "duration_ms": 1033}, {"id": "9842a30b-03f8-471d-8c2b-1b786ee58b1e", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:54.703Z", "end_time": "2025-08-18T06:10:56.894Z", "duration_ms": 2191}, {"id": "125d1b7f-08fc-43ce-8e29-f6ac943ad7b9", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:56.898Z", "end_time": "2025-08-18T06:10:57.189Z", "duration_ms": 291}, {"id": "76c197e5-be71-4d03-9ae1-99d0c7f4f4a4", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:57.189Z", "end_time": "2025-08-18T06:10:57.953Z", "duration_ms": 764}, {"id": "580f3bd9-6366-4d60-a004-f27c0bdcb145", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:10:57.953Z", "end_time": "2025-08-18T06:11:04.343Z", "duration_ms": 6390}, {"id": "d84bc32f-c94d-45af-aa20-d55e29fa3c60", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:04.347Z", "end_time": "2025-08-18T06:11:05.630Z", "duration_ms": 1283}, {"id": "ec243045-5608-41a4-8529-361d273cebe1", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:05.630Z", "end_time": "2025-08-18T06:11:08.200Z", "duration_ms": 2570}, {"id": "1ce9883e-646b-4857-bc22-510327ffbb4d", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:08.203Z", "end_time": "2025-08-18T06:11:08.900Z", "duration_ms": 697}, {"id": "7447aba0-0bb2-4e78-b50c-df3580a1527e", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:08.900Z", "end_time": "2025-08-18T06:11:10.365Z", "duration_ms": 1465}, {"id": "55106546-f486-44ce-a568-b636c3dd2338", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:10.365Z", "end_time": "2025-08-18T06:11:11.989Z", "duration_ms": 1624}, {"id": "5eb4f49b-4fb4-4011-ad45-271c9b62ecfa", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:11.989Z", "end_time": "2025-08-18T06:11:13.689Z", "duration_ms": 1700}, {"id": "21a112e7-d653-4dd4-85ff-4927c352c135", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:13.689Z", "end_time": "2025-08-18T06:11:19.291Z", "duration_ms": 5602}, {"id": "84398315-789c-4733-b876-445b60bb35d6", "migration_id": "ab228cf6-af53-4c95-b967-60bd6183836c", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:11:19.294Z", "end_time": "2025-08-18T06:11:21.136Z", "duration_ms": 1842}]}