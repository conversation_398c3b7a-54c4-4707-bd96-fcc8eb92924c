{"migrationId": "56540370-b57a-482e-8ea6-573c753f5af8", "timestamp": "2025-08-18T07:28:59.400Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 17, "failedTables": 4, "totalRecords": 165, "migratedRecords": 156, "failedRecords": 9}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:28:17.015Z", "errors": [], "endTime": "2025-08-18T07:28:20.166Z", "duration": 3151}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:28:20.172Z", "errors": [], "endTime": "2025-08-18T07:28:22.794Z", "duration": 2622}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:28:22.796Z", "errors": [], "endTime": "2025-08-18T07:28:26.548Z", "duration": 3752}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:28:26.552Z", "errors": [], "endTime": "2025-08-18T07:28:28.922Z", "duration": 2370}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:28.925Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:28:29.384Z", "errors": [], "endTime": "2025-08-18T07:28:31.496Z", "duration": 2112}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:28:31.501Z", "errors": [], "endTime": "2025-08-18T07:28:34.921Z", "duration": 3420}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:28:34.924Z", "errors": [], "endTime": "2025-08-18T07:28:36.551Z", "duration": 1627}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:36.554Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:28:36.841Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:28:40.577Z", "duration": 3736}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:40.579Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:41.080Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:28:41.835Z", "errors": [], "endTime": "2025-08-18T07:28:47.301Z", "duration": 5466}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:47.302Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:28:47.619Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:28:50.110Z", "duration": 2491}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:50.113Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:50.613Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:51.492Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:28:53.076Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:28:54.712Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`follow_ups`, CONSTRAINT `fk_follow_ups_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:28:57.449Z", "duration": 2737}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:28:57.451Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:28:59.396Z", "duration": 1945}], "logs": [{"id": "49a73dc1-5596-4a3f-be7d-9905f80d30c5", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:28:17.015Z", "end_time": "2025-08-18T07:28:20.166Z", "duration_ms": 3151}, {"id": "4e2766cd-f142-4e0e-8983-534206bf0fa8", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:28:20.172Z", "end_time": "2025-08-18T07:28:22.794Z", "duration_ms": 2622}, {"id": "a14261b0-acdb-4782-a77d-4de056e33007", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:28:22.796Z", "end_time": "2025-08-18T07:28:26.548Z", "duration_ms": 3752}, {"id": "f0da3498-65ae-4f5a-bd50-129afcd24cda", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:28:26.552Z", "end_time": "2025-08-18T07:28:28.922Z", "duration_ms": 2370}, {"id": "92f50fb3-1b56-45cc-8b59-34bf8e17de00", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:28.925Z", "end_time": "2025-08-18T07:28:29.384Z", "duration_ms": 459}, {"id": "d992dd4c-b900-44ed-85a0-a68810d155d3", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:28:29.384Z", "end_time": "2025-08-18T07:28:31.496Z", "duration_ms": 2112}, {"id": "16b33561-0548-4147-a1fd-fee0936c602d", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:28:31.501Z", "end_time": "2025-08-18T07:28:34.921Z", "duration_ms": 3420}, {"id": "150e759d-35a6-46cb-a86f-d0b3721b0aff", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:28:34.924Z", "end_time": "2025-08-18T07:28:36.551Z", "duration_ms": 1627}, {"id": "825d20bb-3830-4e19-b492-761f17ffef34", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:36.554Z", "end_time": "2025-08-18T07:28:36.841Z", "duration_ms": 287}, {"id": "82e7b2c4-f028-4261-ab10-4e347b87c6cc", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:36.841Z", "end_time": "2025-08-18T07:28:40.577Z", "duration_ms": 3736}, {"id": "ee71af63-60cd-435b-8310-5b6a4c0aaab4", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:40.579Z", "end_time": "2025-08-18T07:28:41.079Z", "duration_ms": 500}, {"id": "b9cf7967-fc85-4dfc-87d4-87ed01ea06b3", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:41.080Z", "end_time": "2025-08-18T07:28:41.835Z", "duration_ms": 755}, {"id": "524873f0-a743-4328-9d53-10cae8678723", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:28:41.835Z", "end_time": "2025-08-18T07:28:47.301Z", "duration_ms": 5466}, {"id": "40750429-1a06-4f34-b5fe-b6f23c456317", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:47.302Z", "end_time": "2025-08-18T07:28:47.619Z", "duration_ms": 317}, {"id": "74bb1593-6d51-4f30-8f6c-f24c998d9016", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:47.619Z", "end_time": "2025-08-18T07:28:50.110Z", "duration_ms": 2491}, {"id": "f1903aff-4866-4ee6-8b03-ef3be1612a82", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:50.113Z", "end_time": "2025-08-18T07:28:50.613Z", "duration_ms": 500}, {"id": "8a0b186c-61d6-46cb-a258-4bd6365eca36", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:50.613Z", "end_time": "2025-08-18T07:28:51.492Z", "duration_ms": 879}, {"id": "3a925f80-fc46-4cdf-a666-c86bd17301ac", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:51.492Z", "end_time": "2025-08-18T07:28:53.076Z", "duration_ms": 1584}, {"id": "5db31d3e-8892-438d-8624-62bbe65afa06", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:53.076Z", "end_time": "2025-08-18T07:28:54.712Z", "duration_ms": 1636}, {"id": "32ca78d2-bbee-4a7d-93e9-3e96a625c944", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:54.712Z", "end_time": "2025-08-18T07:28:57.449Z", "duration_ms": 2737}, {"id": "e61df91b-5d2d-4d28-b9ec-ab5b0460c5a6", "migration_id": "56540370-b57a-482e-8ea6-573c753f5af8", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:28:57.451Z", "end_time": "2025-08-18T07:28:59.396Z", "duration_ms": 1945}]}