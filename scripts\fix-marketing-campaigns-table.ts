import mysql from 'mysql2/promise'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config({ path: '../.env' })

async function fixMarketingCampaignsTable() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'workchat_admin',
    charset: 'utf8mb4'
  })

  try {
    console.log('🔧 修改 marketing_campaigns 表结构...')
    
    // 修改 created_by 字段为允许 NULL
    await connection.execute(`
      ALTER TABLE marketing_campaigns 
      MODIFY COLUMN created_by varchar(36)
    `)
    
    console.log('✅ marketing_campaigns 表的 created_by 字段已修改为允许 NULL')
    
  } catch (error) {
    console.error('❌ 修改表结构失败:', error)
    throw error
  } finally {
    await connection.end()
    console.log('MySQL连接已关闭')
  }
}

// 执行修改
fixMarketingCampaignsTable().catch(console.error)