<template>
  <div class="tracking-list">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
  
        <p class="page-description">管理客户跟踪记录，设置自动提醒，分析客户行为</p>
      </div>
      <div class="header-right">
        <n-space>
          <n-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <n-icon><Add /></n-icon>
            </template>
            新建跟踪记录
          </n-button>
          <n-button type="default" @click="showReminderModal = true">
            <template #icon>
              <n-icon><Notifications /></n-icon>
            </template>
            提醒管理
            <n-badge v-if="unreadReminderCount > 0" :value="unreadReminderCount" />
          </n-button>
          <n-button type="info" @click="showStatisticsModal = true">
            <template #icon>
              <n-icon><Analytics /></n-icon>
            </template>
            统计分析
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="总跟踪记录" :value="trackingTotal">
              <template #prefix>
                <n-icon color="#18a058">
                  <Document />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="待跟进" :value="pendingTrackingRecords.length">
              <template #prefix>
                <n-icon color="#f0a020">
                  <Time />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="已完成" :value="completedTrackingRecords.length">
              <template #prefix>
                <n-icon color="#18a058">
                  <CheckmarkCircle />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="逾期记录" :value="overdueTrackingRecords.length">
              <template #prefix>
                <n-icon color="#d03050">
                  <Warning />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 搜索和筛选 -->
    <n-card class="search-card">
      <n-space vertical>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客户姓名、手机号或跟踪内容"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="typeFilter"
            placeholder="跟踪类型"
            style="width: 120px"
            clearable
            :options="typeOptions"
          />
          <n-select
            v-model:value="statusFilter"
            placeholder="状态"
            style="width: 120px"
            clearable
            :options="statusOptions"
          />
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 240px"
            clearable
          />
          <n-button type="primary" @click="handleSearch">
            <template #icon>
              <n-icon><Search /></n-icon>
            </template>
            搜索
          </n-button>
          <n-button type="default" @click="handleReset">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            重置
          </n-button>
        </n-space>
      </n-space>
    </n-card>

    <!-- 跟踪记录表格 -->
    <n-card>
      <template #header>
        <n-space justify="space-between">
          <span>跟踪记录列表</span>
          <n-space>
            <n-button
              v-if="selectedRowKeys.length > 0"
              type="error"
              size="small"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRowKeys.length }})
            </n-button>
            <n-button size="small" @click="loadTrackingRecords">
              <template #icon>
                <n-icon><Refresh /></n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
        </n-space>
      </template>

      <n-data-table
        :columns="columns"
        :data="trackingRecords"
        :loading="trackingLoading"
        :pagination="pagination"
        :row-key="(row: TrackingRecord) => row.id"
        v-model:checked-row-keys="selectedRowKeys"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 创建/编辑跟踪记录弹窗 -->
    <TrackingFormModal
      v-model:show="showCreateModal"
      :tracking-record="currentTrackingRecord"
      @success="handleCreateSuccess"
    />

    <!-- 提醒管理弹窗 -->
    <ReminderModal
      v-model:show="showReminderModal"
    />

    <!-- 统计分析弹窗 -->
    <StatisticsModal
      v-model:show="showStatisticsModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, markRaw } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  Add,
  Search,
  Refresh,
  Document,
  Time,
  CheckmarkCircle,
  Warning,
  Notifications,
  Analytics,
  Eye,
  CreateOutline,
  TrashOutline,
  CheckmarkDone
} from '@vicons/ionicons5'
import { useTrackingStore } from '@/stores/trackingStore'
import type { TrackingRecord } from '@/api/trackingService'
import type { DataTableColumns } from 'naive-ui'
import TrackingFormModal from './components/TrackingFormModal.vue'
import ReminderModal from './components/ReminderModal.vue'
import StatisticsModal from './components/StatisticsModal.vue'

const message = useMessage()
const dialog = useDialog()
const trackingStore = useTrackingStore()

// 响应式数据
const showCreateModal = ref(false)
const showReminderModal = ref(false)
const showStatisticsModal = ref(false)
const currentTrackingRecord = ref<TrackingRecord | null>(null)
const selectedRowKeys = ref<number[]>([])
const searchKeyword = ref('')
const typeFilter = ref<string | null>(null)
const statusFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)

// 从store获取数据
const {
  trackingRecords,
  trackingLoading,
  trackingTotal,
  pendingTrackingRecords,
  completedTrackingRecords,
  overdueTrackingRecords,
  unreadReminderCount
} = trackingStore

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: computed(() => trackingTotal),
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 筛选选项
const typeOptions = [
  { label: '电话', value: 'call' },
  { label: '拜访', value: 'visit' },
  { label: '邮件', value: 'email' },
  { label: '微信', value: 'wechat' },
  { label: '会议', value: 'meeting' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '待跟进', value: 'pending' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// 表格列配置
const columns: DataTableColumns<TrackingRecord> = [
  {
    type: 'selection'
  },
  {
    title: '客户姓名',
    key: 'customer_name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '跟踪类型',
    key: 'type',
    width: 100,
    render(row) {
      const typeMap: Record<string, { label: string; color: string }> = {
        call: { label: '电话', color: '#18a058' },
        visit: { label: '拜访', color: '#2080f0' },
        email: { label: '邮件', color: '#f0a020' },
        wechat: { label: '微信', color: '#52c41a' },
        meeting: { label: '会议', color: '#722ed1' },
        other: { label: '其他', color: '#8c8c8c' }
      }
      const type = typeMap[row.type] || { label: row.type, color: '#8c8c8c' }
      return h('n-tag', { color: { color: type.color, textColor: '#fff' } }, { default: () => type.label })
    }
  },
  {
    title: '跟踪内容',
    key: 'content',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap: Record<string, { label: string; type: string }> = {
        pending: { label: '待跟进', type: 'warning' },
        completed: { label: '已完成', type: 'success' },
        cancelled: { label: '已取消', type: 'error' }
      }
      const status = statusMap[row.status] || { label: row.status, type: 'default' }
      return h('n-tag', { type: status.type }, { default: () => status.label })
    }
  },
  {
    title: '下次跟进时间',
    key: 'next_follow_time',
    width: 160,
    render(row) {
      if (!row.next_follow_time) return '-'
      const nextTime = new Date(row.next_follow_time)
      const now = new Date()
      const isOverdue = nextTime < now && row.status === 'pending'
      return h(
        'span',
        { style: { color: isOverdue ? '#d03050' : undefined } },
        nextTime.toLocaleString()
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h('n-space', { size: 'small' }, {
        default: () => [
          h(
            'n-button',
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => handleView(row)
            },
            { default: () => '查看', icon: () => h('n-icon', null, { default: () => h(markRaw(Eye)) }) }
          ),
          h(
            'n-button',
            {
              size: 'small',
              type: 'info',
              ghost: true,
              onClick: () => handleEdit(row)
            },
            { default: () => '编辑', icon: () => h('n-icon', null, { default: () => h(markRaw(CreateOutline)) }) }
          ),
          row.status === 'pending' && h(
            'n-button',
            {
              size: 'small',
              type: 'success',
              ghost: true,
              onClick: () => handleComplete(row)
            },
            { default: () => '完成', icon: () => h('n-icon', null, { default: () => h(markRaw(CheckmarkDone)) }) }
          ),
          h(
            'n-button',
            {
              size: 'small',
              type: 'error',
              ghost: true,
              onClick: () => handleDelete(row)
            },
            { default: () => '删除', icon: () => h('n-icon', null, { default: () => h(markRaw(TrashOutline)) }) }
          )
        ].filter(Boolean)
      })
    }
  }
]

// 方法
const loadTrackingRecords = async () => {
  try {
    const params: any = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    }

    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    if (typeFilter.value) {
      params.type = typeFilter.value
    }
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    if (dateRange.value) {
      params.start_date = new Date(dateRange.value[0]).toISOString()
      params.end_date = new Date(dateRange.value[1]).toISOString()
    }

    await trackingStore.fetchTrackingRecords(params)
  } catch (error) {
    message.error('加载跟踪记录失败')
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  loadTrackingRecords()
}

const handleReset = () => {
  searchKeyword.value = ''
  typeFilter.value = null
  statusFilter.value = null
  dateRange.value = null
  pagination.value.page = 1
  loadTrackingRecords()
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadTrackingRecords()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  loadTrackingRecords()
}

const handleView = (record: TrackingRecord) => {
  currentTrackingRecord.value = record
  showCreateModal.value = true
}

const handleEdit = (record: TrackingRecord) => {
  currentTrackingRecord.value = record
  showCreateModal.value = true
}

const handleComplete = (record: TrackingRecord) => {
  dialog.create({
    title: '完成跟踪记录',
    content: '请输入跟踪结果：',
    positiveText: '确定',
    negativeText: '取消',
    showIcon: false,
    onPositiveClick: async () => {
      try {
        await trackingStore.completeTrackingRecord(record.id, '跟踪完成')
        message.success('跟踪记录已完成')
        loadTrackingRecords()
      } catch (error) {
        message.error('完成跟踪记录失败')
      }
    }
  })
}

const handleDelete = (record: TrackingRecord) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除客户"${record.customer_name}"的跟踪记录吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await trackingStore.deleteTrackingRecord(record.id)
        message.success('删除成功')
        loadTrackingRecords()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleBatchDelete = () => {
  dialog.warning({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条跟踪记录吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await trackingStore.batchDeleteTrackingRecords(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadTrackingRecords()
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  currentTrackingRecord.value = null
  loadTrackingRecords()
}

// 生命周期
onMounted(() => {
  loadTrackingRecords()
  trackingStore.fetchTodayReminders()
  trackingStore.fetchOverdueReminders()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}
</style>