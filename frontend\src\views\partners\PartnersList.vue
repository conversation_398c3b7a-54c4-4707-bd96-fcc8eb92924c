<template>
  <div class="partners-list">

    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索合伙人姓名、手机号"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
        
        <n-select
          v-model:value="levelFilter"
          placeholder="等级筛选"
          clearable
          style="width: 150px"
          :options="levelOptions"
        />
        
        <n-button type="primary" @click="addPartner">
          <template #icon>
            <AddIcon />
          </template>
          新增合伙人
        </n-button>
        
        <n-button type="info" @click="showCustomerModal = true">
          <template #icon>
            <AddIcon />
          </template>
          提交客户信息
        </n-button>
      </n-space>
    </div>

    <div class="stats-cards">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="总合伙人数" :value="stats.total" />
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="活跃合伙人" :value="stats.active" />
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="本月新增" :value="stats.newThisMonth" />
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="总积分发放" :value="stats.totalPoints" />
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <n-card class="table-card">
      <n-data-table
        :columns="columns"
        :data="filteredPartners"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: Partner) => row.id"
      />
    </n-card>

    <!-- 新增/编辑合伙人弹窗 -->
    <n-modal v-model:show="showModal" preset="card" style="width: 600px" title="合伙人信息">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="姓名" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入姓名" />
        </n-form-item>
        
        <n-form-item label="手机号" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
        </n-form-item>
        
        <n-form-item label="等级" path="level">
          <n-select
            v-model:value="formData.level"
            placeholder="请选择等级"
            :options="levelOptions"
          />
        </n-form-item>
        
        <n-form-item label="推荐人" path="referrer">
          <n-input v-model:value="formData.referrer" placeholder="请输入推荐人" />
        </n-form-item>
        
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 客户信息提交弹窗 -->
    <n-modal v-model:show="showCustomerModal" preset="card" style="width: 700px" title="提交客户信息">
      <n-form
        ref="customerFormRef"
        :model="customerFormData"
        :rules="customerFormRules"
        label-placement="left"
        label-width="100px"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="客户姓名" path="name">
              <n-input v-model:value="customerFormData.name" placeholder="请输入客户姓名" />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="手机号" path="phone">
              <n-input v-model:value="customerFormData.phone" placeholder="请输入手机号" />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="公司名称" path="company">
              <n-input v-model:value="customerFormData.company" placeholder="请输入公司名称" />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="职位" path="position">
              <n-input v-model:value="customerFormData.position" placeholder="请输入职位" />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="行业" path="industry">
              <n-select
                v-model:value="customerFormData.industry"
                placeholder="请选择行业"
                :options="industryOptions"
              />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="预算范围" path="budget">
              <n-input v-model:value="customerFormData.budget" placeholder="请输入预算范围" />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="客户来源" path="source">
              <n-select
                v-model:value="customerFormData.source"
                placeholder="请选择客户来源"
                :options="sourceOptions"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        
        <n-form-item label="客户需求" path="needs">
          <n-input
            v-model:value="customerFormData.needs"
            type="textarea"
            placeholder="请详细描述客户需求"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="customerFormData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="2"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showCustomerModal = false">取消</n-button>
          <n-button type="primary" @click="handleCustomerSubmit">提交</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import {
  NCard,
  NDataTable,
  NInput,
  NButton,
  NSelect,
  NSpace,
  NGrid,
  NGridItem,
  NStatistic,
  NModal,
  NForm,
  NFormItem,
  NTag,
  NAvatar,
  NDropdown,
  useMessage,
  type DataTableColumns,
  type FormInst
} from 'naive-ui'
import { SearchOutline as SearchIcon, Add as AddIcon, EllipsisHorizontal as MoreIcon } from '@vicons/ionicons5'

interface Partner {
  id: string
  name: string
  phone: string
  level: string
  status: 'active' | 'inactive' | 'pending'
  totalCustomers: number
  totalPoints: number
  joinDate: string
  lastActiveDate: string
  referrer?: string
  remark?: string
}

interface Stats {
  total: number
  active: number
  newThisMonth: number
  totalPoints: number
}

const message = useMessage()
const loading = ref(false)
const showModal = ref(false)
const showCustomerModal = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const levelFilter = ref<string | null>(null)

const formRef = ref<FormInst | null>(null)
const customerFormRef = ref<FormInst | null>(null)
const formData = ref<Partial<Partner>>({
  id: '',
  name: '',
  phone: '',
  level: '',
  referrer: '',
  remark: ''
})

const customerFormData = ref({
  name: '',
  phone: '',
  company: '',
  position: '',
  industry: '',
  budget: '',
  needs: '',
  source: '',
  remark: ''
})

const formRules = {
  name: {
    required: true,
    message: '请输入姓名',
    trigger: 'blur'
  },
  phone: {
    required: true,
    message: '请输入手机号',
    trigger: 'blur'
  },
  level: {
    required: true,
    message: '请选择等级',
    trigger: 'change'
  }
}

const customerFormRules = {
  name: {
    required: true,
    message: '请输入客户姓名',
    trigger: 'blur'
  },
  phone: {
    required: true,
    message: '请输入手机号',
    trigger: 'blur'
  },
  company: {
    required: true,
    message: '请输入公司名称',
    trigger: 'blur'
  },
  industry: {
    required: true,
    message: '请选择行业',
    trigger: 'change'
  },
  needs: {
    required: true,
    message: '请输入客户需求',
    trigger: 'blur'
  }
}

const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '非活跃', value: 'inactive' },
  { label: '待审核', value: 'pending' }
]

const levelOptions = [
  { label: '初级合伙人', value: 'junior' },
  { label: '中级合伙人', value: 'intermediate' },
  { label: '高级合伙人', value: 'senior' },
  { label: '金牌合伙人', value: 'gold' }
]

const industryOptions = [
  { label: '互联网/电商', value: 'internet' },
  { label: '金融/保险', value: 'finance' },
  { label: '制造业', value: 'manufacturing' },
  { label: '房地产', value: 'realestate' },
  { label: '教育培训', value: 'education' },
  { label: '医疗健康', value: 'healthcare' },
  { label: '零售/贸易', value: 'retail' },
  { label: '其他', value: 'other' }
]

const sourceOptions = [
  { label: '朋友推荐', value: 'friend' },
  { label: '网络搜索', value: 'search' },
  { label: '社交媒体', value: 'social' },
  { label: '展会活动', value: 'event' },
  { label: '电话营销', value: 'telemarketing' },
  { label: '其他', value: 'other' }
]

const stats = ref<Stats>({
  total: 0,
  active: 0,
  newThisMonth: 0,
  totalPoints: 0
})

const partners = ref<Partner[]>([])

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
}

const filteredPartners = computed(() => {
  let result = partners.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(partner => 
      partner.name.toLowerCase().includes(keyword) ||
      partner.phone.includes(keyword)
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(partner => partner.status === statusFilter.value)
  }
  
  if (levelFilter.value) {
    result = result.filter(partner => partner.level === levelFilter.value)
  }
  
  return result
})

const getStatusTag = (status: string) => {
  const statusMap = {
    active: { type: 'success', text: '活跃' },
    inactive: { type: 'warning', text: '非活跃' },
    pending: { type: 'info', text: '待审核' }
  }
  const config = statusMap[status as keyof typeof statusMap]
  return h(NTag, { type: config.type as any }, { default: () => config.text })
}

const getLevelTag = (level: string) => {
  const levelMap = {
    junior: { type: 'default', text: '初级' },
    intermediate: { type: 'info', text: '中级' },
    senior: { type: 'warning', text: '高级' },
    gold: { type: 'success', text: '金牌' }
  }
  const config = levelMap[level as keyof typeof levelMap]
  return h(NTag, { type: config.type as any }, { default: () => config.text })
}

const createDropdown = (partner: Partner) => {
  const options = [
    {
      label: '查看详情',
      key: 'view',
      props: {
        onClick: () => viewPartner(partner)
      }
    },
    {
      label: '编辑',
      key: 'edit',
      props: {
        onClick: () => editPartner(partner)
      }
    },
    {
      label: '删除',
      key: 'delete',
      props: {
        onClick: () => deletePartner(partner)
      }
    }
  ]
  
  return h(
    NDropdown,
    { options, trigger: 'click' },
    {
      default: () => h(NButton, { quaternary: true, size: 'small' }, {
        icon: () => h(MoreIcon)
      })
    }
  )
}

const columns: DataTableColumns<Partner> = [
  {
    title: '合伙人',
    key: 'name',
    render: (row) => h('div', { class: 'flex items-center gap-3' }, [
      h(NAvatar, { 
        size: 'small',
        src: `https://api.dicebear.com/7.x/avataaars/svg?seed=${row.name}`
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-gray-500 text-sm' }, row.phone)
      ])
    ])
  },
  {
    title: '等级',
    key: 'level',
    render: (row) => getLevelTag(row.level)
  },
  {
    title: '状态',
    key: 'status',
    render: (row) => getStatusTag(row.status)
  },
  {
    title: '客户数',
    key: 'totalCustomers'
  },
  {
    title: '累计积分',
    key: 'totalPoints'
  },
  {
    title: '加入时间',
    key: 'joinDate'
  },
  {
    title: '最后活跃',
    key: 'lastActiveDate'
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => createDropdown(row)
  }
]

const loadPartners = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    partners.value = [
      {
        id: '1',
        name: '张三',
        phone: '13800138001',
        level: 'gold',
        status: 'active',
        totalCustomers: 25,
        totalPoints: 5200,
        joinDate: '2024-01-15',
        lastActiveDate: '2024-01-20',
        referrer: '李四'
      },
      {
        id: '2',
        name: '王五',
        phone: '13800138002',
        level: 'senior',
        status: 'active',
        totalCustomers: 18,
        totalPoints: 3600,
        joinDate: '2024-01-10',
        lastActiveDate: '2024-01-19'
      },
      {
        id: '3',
        name: '赵六',
        phone: '13800138003',
        level: 'intermediate',
        status: 'inactive',
        totalCustomers: 12,
        totalPoints: 2400,
        joinDate: '2024-01-05',
        lastActiveDate: '2024-01-15'
      }
    ]
    
    stats.value = {
      total: partners.value.length,
      active: partners.value.filter(p => p.status === 'active').length,
      newThisMonth: 2,
      totalPoints: partners.value.reduce((sum, p) => sum + p.totalPoints, 0)
    }
  } catch (error) {
    message.error('加载合伙人数据失败')
  } finally {
    loading.value = false
  }
}

const addPartner = () => {
  formData.value = {
    id: '',
    name: '',
    phone: '',
    level: '',
    status: 'pending' as 'active' | 'inactive' | 'pending',
    totalCustomers: 0,
    totalPoints: 0,
    joinDate: '',
    lastActiveDate: '',
    referrer: '',
    remark: ''
  }
  showModal.value = true
}

const editPartner = (partner: Partner) => {
  formData.value = { ...partner }
  showModal.value = true
}

const viewPartner = (partner: Partner) => {
  message.info(`查看合伙人：${partner.name}`)
}

const deletePartner = (partner: Partner) => {
  message.warning(`删除合伙人：${partner.name}`)
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (formData.value.id) {
      message.success('更新成功')
    } else {
      message.success('添加成功')
    }
    
    showModal.value = false
    loadPartners()
  } catch (error) {
    message.error('操作失败')
  }
}

const handleCustomerSubmit = async () => {
  try {
    await customerFormRef.value?.validate()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('客户信息提交成功！已转入客户管理系统，将安排营销人员跟进')
    
    customerFormData.value = {
      name: '',
      phone: '',
      company: '',
      position: '',
      industry: '',
      budget: '',
      needs: '',
      source: '',
      remark: ''
    }
    
    showCustomerModal.value = false
    
    setTimeout(() => {
      message.info('恭喜！您因提交客户信息获得50积分奖励')
    }, 1500)
    
  } catch (error) {
    message.error('提交失败，请重试')
  }
}

onMounted(() => {
  loadPartners()
})
</script>

<style scoped>
.partners-list {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filters {
  margin-bottom: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-3 {
  gap: 12px;
}

.font-medium {
  font-weight: 500;
}

.text-gray-500 {
  color: #6b7280;
}

.text-sm {
  font-size: 14px;
}
</style>