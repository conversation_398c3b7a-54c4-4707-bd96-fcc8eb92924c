# 企业微信登录配置指南

## 概述

本系统已集成企业微信登录功能，管理员可以通过企业微信身份验证直接登录后台管理系统，无需单独设置账号密码。

## 配置步骤

### 1. 企业微信管理后台配置

1. 登录企业微信管理后台：https://work.weixin.qq.com/
2. 进入"应用管理" -> "应用" -> "创建应用"
3. 填写应用信息：
   - 应用名称：YYSH后台管理系统
   - 应用介绍：客户关系管理系统
   - 应用logo：上传系统logo
4. 创建完成后，记录以下信息：
   - 企业ID (CorpId)
   - 应用ID (AgentId)
   - 应用Secret

### 2. 可信域名配置

1. 在应用详情页面，找到"企业微信授权登录"
2. 设置可信域名：
   - 开发环境：`localhost:3000`
   - 生产环境：您的实际域名（如：`admin.yourcompany.com`）

### 3. 环境变量配置

编辑 `.env` 文件，配置企业微信相关参数：

```env
# 企业微信配置
WECHAT_CORP_ID=ww1234567890abcdef     # 替换为您的企业ID
WECHAT_SECRET=your_wechat_secret_key   # 替换为您的应用Secret
WECHAT_AGENT_ID=1000001               # 替换为您的应用ID
```

### 4. 重启服务

配置完成后，重启后台服务：

```bash
npm start
```

## 功能特性

### 自动用户创建

- 首次通过企业微信登录的用户会自动在系统中创建账户
- 用户信息从企业微信同步，包括：
  - 用户名（企业微信UserId）
  - 真实姓名
  - 邮箱
  - 手机号
  - 部门信息
  - 头像

### 用户信息同步

- 每次登录时会自动更新用户信息
- 保持与企业微信通讯录的同步

### 权限管理

- 新用户默认角色为"员工"
- 管理员可以在用户管理中调整用户权限
- 支持禁用用户账户

## 使用方法

### 管理员登录

1. 访问后台管理系统：http://localhost:3000
2. 点击"企业微信登录"按钮
3. 在企业微信中确认授权
4. 自动跳转到管理后台

### 用户权限设置

1. 管理员登录后台
2. 进入"用户管理"页面
3. 找到通过企业微信登录的用户
4. 设置相应的角色权限：
   - 管理员：完整系统权限
   - 员工：基础操作权限

## 安全特性

### 身份验证

- 基于企业微信OAuth2.0授权
- 使用JWT token进行会话管理
- 支持token自动刷新

### 访问控制

- 只有企业微信通讯录中的用户才能登录
- 支持用户状态管理（启用/禁用）
- 详细的操作日志记录

### 数据安全

- 用户敏感信息加密存储
- API接口权限验证
- 防止SQL注入和XSS攻击

## 故障排除

### 常见问题

1. **企业微信登录按钮显示为禁用状态**
   - 检查企业微信JS-SDK是否正确加载
   - 确认可信域名配置是否正确
   - 查看浏览器控制台错误信息

2. **登录时提示"用户未在企业微信中"**
   - 确认用户在企业微信通讯录中
   - 检查应用的可见范围设置

3. **获取用户信息失败**
   - 验证企业微信配置参数是否正确
   - 检查应用Secret是否有效
   - 确认应用权限设置

### 调试模式

开启企业微信JS-SDK调试模式：

```javascript
wx.config({
  debug: true,  // 开启调试模式
  // ... 其他配置
});
```

### 日志查看

查看服务器日志：

```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## API接口

### 获取JS-SDK配置

```
GET /api/wechat/config?url={当前页面URL}
```

### 企业微信登录

```
POST /api/wechat/login
{
  "code": "企业微信授权码"
}
```

### 获取用户信息

```
GET /api/wechat/userinfo?code={授权码}
```

## 技术支持

如遇到配置问题，请联系技术支持团队，并提供以下信息：

1. 企业微信配置截图
2. 浏览器控制台错误信息
3. 服务器日志文件
4. 环境变量配置（隐藏敏感信息）

---

更新时间：2024年12月
版本：v1.0.0