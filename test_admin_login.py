#!/usr/bin/env python3
"""
测试admin用户登录功能

验证admin/admin123是否可以正常登录
"""

import asyncio
import sys
from app.database import AsyncSessionLocal
from app.services.crud import crud_user
from app.utils.security import verify_password

async def test_admin_login():
    """测试admin用户登录"""
    async with AsyncSessionLocal() as db:
        try:
            print("=== 测试admin用户登录 ===")
            
            # 1. 获取admin用户
            admin_user = await crud_user.get_by_username(db, username="admin")
            
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            print(f"✅ 找到admin用户: {admin_user.username}")
            print(f"   用户ID: {admin_user.id}")
            print(f"   邮箱: {admin_user.email}")
            print(f"   密码哈希: {admin_user.password_hash[:50]}...")
            
            # 2. 验证密码
            password = "admin123"
            is_valid = verify_password(password, admin_user.password_hash)
            
            if is_valid:
                print(f"✅ 密码验证成功！admin用户可以使用密码 '{password}' 登录")
                return True
            else:
                print(f"❌ 密码验证失败！密码 '{password}' 不正确")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False

async def test_authentication_api():
    """测试认证API"""
    async with AsyncSessionLocal() as db:
        try:
            print("\n=== 测试认证API ===")
            
            # 测试authenticate方法
            authenticated_user = await crud_user.authenticate(
                db, 
                username="admin", 
                password="admin123"
            )
            
            if authenticated_user:
                print("✅ 认证API测试成功！")
                print(f"   认证用户: {authenticated_user.username}")
                return True
            else:
                print("❌ 认证API测试失败！")
                return False
                
        except Exception as e:
            print(f"❌ 认证API测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主函数"""
    print("开始测试admin用户登录功能...\n")
    
    # 测试1: 直接密码验证
    test1_result = await test_admin_login()
    
    # 测试2: 认证API
    test2_result = await test_authentication_api()
    
    print("\n=== 测试结果汇总 ===")
    print(f"密码验证测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"认证API测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！admin用户可以正常登录")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)