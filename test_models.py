#!/usr/bin/env python3
"""完整的模型测试脚本"""

import asyncio
import logging
import os
from datetime import datetime
from decimal import Decimal

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_user_model():
    """测试用户模型"""
    try:
        from app.models.user import User
        from app.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            # 创建用户
            user = User(
                username="test_user",
                email="<EMAIL>",
                phone="13800138000",
                real_name="测试用户",
                password_hash="hashed_password",
                department_id=1,
                role="user",
                status="active"
            )
            
            session.add(user)
            await session.commit()
            await session.refresh(user)
            
            logger.info(f"✅ 用户创建成功: ID={user.id}, 用户名={user.username}")
            
            # 查询用户
            from sqlalchemy import select
            result = await session.execute(select(User).where(User.username == "test_user"))
            found_user = result.scalar_one_or_none()
            
            if found_user:
                logger.info(f"✅ 用户查询成功: {found_user.real_name}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 用户模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_department_model():
    """测试部门模型"""
    try:
        from app.models.user import Department
        from app.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            # 创建部门
            dept = Department(
                name="测试部门",
                description="这是一个测试部门",
                status="active"
            )
            
            session.add(dept)
            await session.commit()
            await session.refresh(dept)
            
            logger.info(f"✅ 部门创建成功: ID={dept.id}, 名称={dept.name}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 部门模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_customer_model():
    """测试客户模型"""
    try:
        from app.models.customer import Customer
        from app.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            # 创建客户
            customer = Customer(
                name="测试客户",
                phone="13900139000",
                email="<EMAIL>",
                company="测试公司",
                position="经理",
                source="网络",
                status="active",
                assigned_user_id=1,
                department_id=1
            )
            
            session.add(customer)
            await session.commit()
            await session.refresh(customer)
            
            logger.info(f"✅ 客户创建成功: ID={customer.id}, 姓名={customer.name}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 客户模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_follow_record_model():
    """测试跟进记录模型"""
    try:
        from app.models.follow import FollowRecord
        from app.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            # 创建跟进记录
            follow = FollowRecord(
                customer_id=1,
                user_id=1,
                follow_type="电话",
                content="电话沟通客户需求",
                result="有意向",
                next_follow_time=datetime.now(),
                status="completed"
            )
            
            session.add(follow)
            await session.commit()
            await session.refresh(follow)
            
            logger.info(f"✅ 跟进记录创建成功: ID={follow.id}, 类型={follow.follow_type}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 跟进记录模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_customer_tag_model():
    """测试客户标签模型"""
    try:
        from app.models.customer import CustomerTag, CustomerTagRelation
        from app.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            # 创建标签
            tag = CustomerTag(
                name="重要客户",
                color="#FF0000",
                description="重要客户标签",
                status="active"
            )
            
            session.add(tag)
            await session.commit()
            await session.refresh(tag)
            
            logger.info(f"✅ 客户标签创建成功: ID={tag.id}, 名称={tag.name}")
            
            # 创建标签关系
            relation = CustomerTagRelation(
                customer_id=1,
                tag_id=tag.id
            )
            
            session.add(relation)
            await session.commit()
            
            logger.info(f"✅ 客户标签关系创建成功")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 客户标签模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_operations():
    """测试数据库操作"""
    try:
        from app.database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试健康检查
        is_healthy = await db_manager.health_check()
        if is_healthy:
            logger.info("✅ 数据库健康检查通过")
        else:
            logger.error("❌ 数据库健康检查失败")
            return False
        
        # 测试统计信息
        stats = await db_manager.get_database_stats()
        logger.info(f"✅ 数据库统计信息: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始完整的模型和数据库操作测试")
    logger.info("=" * 60)
    
    # 确保使用SQLite配置
    os.environ['DATABASE_DATABASE'] = 'test_yysh.db'
    
    # 初始化数据库
    from app.database import create_tables
    await create_tables()
    logger.info("✅ 数据库表初始化完成")
    
    tests = [
        ("部门模型", test_department_model),
        ("用户模型", test_user_model),
        ("客户模型", test_customer_model),
        ("跟进记录模型", test_follow_record_model),
        ("客户标签模型", test_customer_tag_model),
        ("数据库操作", test_database_operations),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            success = await test_func()
            if success:
                success_count += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        logger.info("🎉 所有测试通过！数据库层实现完成！")
        return True
    else:
        logger.error(f"❌ 有 {total_count - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    asyncio.run(main())