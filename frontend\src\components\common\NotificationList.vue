<template>
  <div class="notification-list">
    <!-- 通知筛选 -->
    <div class="notification-filter">
      <n-tabs v-model:value="activeTab" type="line" size="small">
        <n-tab-pane name="all" tab="全部">
          <template #tab>
            <span>全部</span>
            <n-badge
              v-if="allNotifications.length > 0"
              :value="allNotifications.length"
              :max="99"
              class="tab-badge"
            />
          </template>
        </n-tab-pane>
        <n-tab-pane name="unread" tab="未读">
          <template #tab>
            <span>未读</span>
            <n-badge
              v-if="unreadNotifications.length > 0"
              :value="unreadNotifications.length"
              :max="99"
              class="tab-badge"
            />
          </template>
        </n-tab-pane>
        <n-tab-pane name="system" tab="系统">
          <template #tab>
            <span>系统</span>
            <n-badge
              v-if="systemNotifications.length > 0"
              :value="systemNotifications.length"
              :max="99"
              class="tab-badge"
            />
          </template>
        </n-tab-pane>
      </n-tabs>
    </div>
    
    <!-- 操作按钮 -->
    <div class="notification-actions">
      <n-button
        text
        size="small"
        @click="markAllAsRead"
        :disabled="unreadNotifications.length === 0"
      >
        全部标记为已读
      </n-button>
      <n-button
        text
        size="small"
        @click="clearAll"
        :disabled="currentNotifications.length === 0"
      >
        清空
      </n-button>
    </div>
    
    <!-- 通知列表 -->
    <div class="notification-content">
      <n-scrollbar style="max-height: 400px;">
        <div v-if="currentNotifications.length === 0" class="empty-state">
          <n-empty description="暂无通知" />
        </div>
        
        <div v-else class="notification-items">
          <div
            v-for="notification in currentNotifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <!-- 通知图标 -->
            <div class="notification-icon">
              <n-icon
                size="20"
                :color="getNotificationIconColor(notification.type)"
              >
                <component :is="getNotificationIcon(notification.type)" />
              </n-icon>
            </div>
            
            <!-- 通知内容 -->
            <div class="notification-body">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">
                {{ formatTime(notification.createdAt) }}
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="notification-actions-item">
              <n-button
                v-if="!notification.read"
                text
                size="tiny"
                @click.stop="markAsRead(notification.id)"
              >
                标记已读
              </n-button>
              <n-button
                text
                size="tiny"
                @click.stop="deleteNotification(notification.id)"
              >
                删除
              </n-button>
            </div>
          </div>
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  NTabs,
  NTabPane,
  NBadge,
  NButton,
  NScrollbar,
  NEmpty,
  NIcon,
  useMessage
} from 'naive-ui'
import {
  InformationCircleOutline,
  CheckmarkCircleOutline,
  WarningOutline,
  CloseCircleOutline,
  NotificationsOutline
} from '@vicons/ionicons5'
import { formatDate } from '@/utils'

interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error' | 'system'
  title: string
  message: string
  read: boolean
  createdAt: string
  link?: string
}

const message = useMessage()

// 响应式数据
const activeTab = ref('all')

// 模拟通知数据
const notifications = ref<Notification[]>([
  {
    id: '1',
    type: 'info',
    title: '新客户分配',
    message: '您有一个新的客户"张三"已分配给您，请及时跟进。',
    read: false,
    createdAt: '2024-01-15T10:30:00Z',
    link: '/customer/detail/1'
  },
  {
    id: '2',
    type: 'warning',
    title: '跟进提醒',
    message: '客户"李四"已超过7天未跟进，请尽快联系。',
    read: false,
    createdAt: '2024-01-15T09:15:00Z',
    link: '/customer/detail/2'
  },
  {
    id: '3',
    type: 'success',
    title: '成交通知',
    message: '恭喜！客户"王五"已成功签约，合同金额：50万元。',
    read: true,
    createdAt: '2024-01-14T16:45:00Z',
    link: '/customer/detail/3'
  },
  {
    id: '4',
    type: 'system',
    title: '系统维护通知',
    message: '系统将于今晚22:00-24:00进行维护升级，期间可能影响使用。',
    read: false,
    createdAt: '2024-01-14T14:20:00Z'
  },
  {
    id: '5',
    type: 'error',
    title: '数据同步失败',
    message: '客户数据同步失败，请检查网络连接或联系管理员。',
    read: true,
    createdAt: '2024-01-14T11:10:00Z'
  }
])

// 计算属性
const allNotifications = computed(() => notifications.value)
const unreadNotifications = computed(() => 
  notifications.value.filter(n => !n.read)
)
const systemNotifications = computed(() => 
  notifications.value.filter(n => n.type === 'system')
)

const currentNotifications = computed(() => {
  switch (activeTab.value) {
    case 'unread':
      return unreadNotifications.value
    case 'system':
      return systemNotifications.value
    default:
      return allNotifications.value
  }
})

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap = {
    info: InformationCircleOutline,
    success: CheckmarkCircleOutline,
    warning: WarningOutline,
    error: CloseCircleOutline,
    system: NotificationsOutline
  }
  return iconMap[type as keyof typeof iconMap] || InformationCircleOutline
}

// 获取通知图标颜色
const getNotificationIconColor = (type: string) => {
  const colorMap = {
    info: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    system: '#722ed1'
  }
  return colorMap[type as keyof typeof colorMap] || '#1890ff'
}

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return formatDate(date, 'MM-DD HH:mm')
}

// 处理通知点击
const handleNotificationClick = (notification: Notification) => {
  // 标记为已读
  if (!notification.read) {
    markAsRead(notification.id)
  }
  
  // 如果有链接，跳转到对应页面
  if (notification.link) {
    // 这里可以使用 router.push 或 emit 事件给父组件处理
    console.log('Navigate to:', notification.link)
  }
}

// 标记为已读
const markAsRead = (id: string) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.read = true
  }
}

// 全部标记为已读
const markAllAsRead = () => {
  notifications.value.forEach(n => {
    n.read = true
  })
  message.success('已全部标记为已读')
}

// 删除通知
const deleteNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index !== -1) {
    notifications.value.splice(index, 1)
    message.success('通知已删除')
  }
}

// 清空通知
const clearAll = () => {
  if (activeTab.value === 'unread') {
    notifications.value = notifications.value.filter(n => n.read)
  } else if (activeTab.value === 'system') {
    notifications.value = notifications.value.filter(n => n.type !== 'system')
  } else {
    notifications.value = []
  }
  message.success('通知已清空')
}
</script>

<style scoped>
.notification-list {
  padding: 16px 0;
}

.notification-filter {
  padding: 0 16px 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.tab-badge {
  margin-left: 4px;
}

.notification-actions {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.notification-content {
  padding: 8px 0;
}

.empty-state {
  padding: 40px 16px;
  text-align: center;
}

.notification-items {
  padding: 0 8px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background-color: var(--n-hover-color);
}

.notification-item.unread {
  background-color: var(--n-info-color-suppl);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: var(--n-primary-color);
  border-radius: 2px;
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: var(--n-text-color-disabled);
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

.notification-actions-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions-item {
  opacity: 1;
}
</style>