<template>
  <div class="performance-analysis">
    <!-- 分析配置 -->
    <n-card title="业绩分析配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>分析维度</label>
            <n-select v-model:value="analysisConfig.dimension" :options="dimensionOptions" placeholder="选择分析维度" />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="analysisConfig.dateRange" type="daterange" clearable />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>业务线</label>
            <n-select v-model:value="analysisConfig.businessLine" :options="businessLineOptions" multiple placeholder="选择业务线" />
          </div>
          <div class="form-item">
            <label>对比基准</label>
            <n-select v-model:value="analysisConfig.baseline" :options="baselineOptions" placeholder="选择对比基准" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 业绩概览 -->
    <n-card title="业绩概览" class="overview-card">
      <div class="performance-overview">
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#2080f0"><CashOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">¥{{ performanceOverview.totalRevenue.toLocaleString() }}</div>
              <div class="stat-label">总收入</div>
              <div class="stat-change" :class="performanceOverview.revenueChange >= 0 ? 'positive' : 'negative'">
                {{ performanceOverview.revenueChange >= 0 ? '+' : '' }}{{ performanceOverview.revenueChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#18a058"><TrendingUpOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceOverview.growthRate }}%</div>
              <div class="stat-label">增长率</div>
              <div class="stat-change" :class="performanceOverview.growthChange >= 0 ? 'positive' : 'negative'">
                {{ performanceOverview.growthChange >= 0 ? '+' : '' }}{{ performanceOverview.growthChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#f0a020"><AtOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceOverview.targetCompletion }}%</div>
              <div class="stat-label">目标完成率</div>
              <div class="stat-change" :class="performanceOverview.targetChange >= 0 ? 'positive' : 'negative'">
                {{ performanceOverview.targetChange >= 0 ? '+' : '' }}{{ performanceOverview.targetChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#d03050"><PersonOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceOverview.customerCount.toLocaleString() }}</div>
              <div class="stat-label">客户数量</div>
              <div class="stat-change" :class="performanceOverview.customerChange >= 0 ? 'positive' : 'negative'">
                {{ performanceOverview.customerChange >= 0 ? '+' : '' }}{{ performanceOverview.customerChange }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 业绩趋势分析 -->
    <n-card title="业绩趋势分析" class="trend-card">
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 业绩分解分析 -->
    <div class="chart-row">
      <n-card title="收入构成分析" class="composition-card">
        <div class="chart-container">
          <div ref="compositionChartRef" class="chart"></div>
        </div>
      </n-card>
      
      <n-card title="地区业绩分布" class="region-card">
        <div class="chart-container">
          <div ref="regionChartRef" class="chart"></div>
        </div>
      </n-card>
    </div>
    
    <!-- 团队业绩对比 -->
    <n-card title="团队业绩对比" class="team-card">
      <div class="team-performance">
        <div class="team-chart">
          <div ref="teamChartRef" class="chart"></div>
        </div>
        
        <div class="team-ranking">
          <div class="ranking-header">
            <h3>团队排行榜</h3>
            <n-select v-model:value="rankingMetric" :options="rankingMetricOptions" style="width: 150px;" />
          </div>
          
          <div class="ranking-list">
            <div class="ranking-item" v-for="(team, index) in teamRanking" :key="team.id">
              <div class="ranking-position">
                <div class="position-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
                <div v-if="index < 3" class="position-medal">
                  <n-icon :color="index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32'">
                    <TrophyOutline />
                  </n-icon>
                </div>
              </div>
              
              <div class="team-info">
                <div class="team-name">{{ team.name }}</div>
                <div class="team-leader">{{ team.leader }}</div>
              </div>
              
              <div class="team-metrics">
                <div class="metric-value">{{ formatMetricValue(team[rankingMetric as keyof typeof team] as number) }}</div>
                <div class="metric-change" :class="team.change >= 0 ? 'positive' : 'negative'">
                  {{ team.change >= 0 ? '+' : '' }}{{ team.change }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 产品业绩分析 -->
    <n-card title="产品业绩分析" class="product-card">
      <div class="product-analysis">
        <div class="product-tabs">
          <n-tabs v-model:value="activeProductTab" type="line">
            <n-tab-pane name="revenue" tab="收入分析">
              <div class="chart-container">
                <div ref="productRevenueChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
            <n-tab-pane name="growth" tab="增长分析">
              <div class="chart-container">
                <div ref="productGrowthChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
            <n-tab-pane name="margin" tab="利润分析">
              <div class="chart-container">
                <div ref="productMarginChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>
    </n-card>
    
    <!-- 业绩预测 -->
    <n-card title="业绩预测" class="forecast-card">
      <div class="forecast-content">
        <div class="forecast-chart">
          <div ref="forecastChartRef" class="chart"></div>
        </div>
        
        <div class="forecast-details">
          <div class="forecast-summary">
            <h3>预测摘要</h3>
            <div class="summary-item">
              <span class="summary-label">预测模型:</span>
              <span class="summary-value">时间序列分析</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">预测准确度:</span>
              <span class="summary-value">85.6%</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">下月预测:</span>
              <span class="summary-value">¥3,200,000</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">季度预测:</span>
              <span class="summary-value">¥9,800,000</span>
            </div>
          </div>
          
          <div class="forecast-factors">
            <h3>影响因素</h3>
            <div class="factor-item" v-for="factor in forecastFactors" :key="factor.name">
              <div class="factor-name">{{ factor.name }}</div>
              <div class="factor-impact" :class="factor.impact >= 0 ? 'positive' : 'negative'">
                {{ factor.impact >= 0 ? '+' : '' }}{{ factor.impact }}%
              </div>
              <div class="factor-description">{{ factor.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 业绩改进建议 -->
    <n-card title="业绩改进建议" class="improvement-card">
      <div class="improvement-list">
        <div class="improvement-item" v-for="improvement in improvementSuggestions" :key="improvement.id">
          <div class="improvement-header">
            <div class="improvement-icon">
              <n-icon :color="improvement.priority === 'high' ? '#d03050' : improvement.priority === 'medium' ? '#f0a020' : '#18a058'">
                <BulbOutline />
              </n-icon>
            </div>
            <div class="improvement-info">
              <div class="improvement-title">{{ improvement.title }}</div>
              <div class="improvement-description">{{ improvement.description }}</div>
            </div>
            <div class="improvement-priority">
              <n-tag :type="improvement.priority === 'high' ? 'error' : improvement.priority === 'medium' ? 'warning' : 'success'">
                {{ improvement.priority === 'high' ? '高优先级' : improvement.priority === 'medium' ? '中优先级' : '低优先级' }}
              </n-tag>
            </div>
          </div>
          
          <div class="improvement-details">
            <div class="detail-section">
              <h4>当前状况</h4>
              <p>{{ improvement.currentSituation }}</p>
            </div>
            
            <div class="detail-section">
              <h4>改进措施</h4>
              <ul>
                <li v-for="action in improvement.actions" :key="action">{{ action }}</li>
              </ul>
            </div>
            
            <div class="detail-section">
              <h4>预期效果</h4>
              <div class="expected-results">
                <div class="result-item">
                  <span class="result-label">收入提升:</span>
                  <span class="result-value">{{ improvement.expectedResults.revenue }}</span>
                </div>
                <div class="result-item">
                  <span class="result-label">实施周期:</span>
                  <span class="result-value">{{ improvement.expectedResults.timeline }}</span>
                </div>
                <div class="result-item">
                  <span class="result-label">投入成本:</span>
                  <span class="result-value">{{ improvement.expectedResults.cost }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NTag, NTabs, NTabPane
} from 'naive-ui'
import {
  CashOutline, TrendingUpOutline, AtOutline, PersonOutline,
  TrophyOutline, BulbOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const trendChartRef = ref<HTMLElement>()
const compositionChartRef = ref<HTMLElement>()
const regionChartRef = ref<HTMLElement>()
const teamChartRef = ref<HTMLElement>()
const productRevenueChartRef = ref<HTMLElement>()
const productGrowthChartRef = ref<HTMLElement>()
const productMarginChartRef = ref<HTMLElement>()
const forecastChartRef = ref<HTMLElement>()

const activeProductTab = ref('revenue')
const rankingMetric = ref<'revenue' | 'customers' | 'orders'>('revenue')

// 分析配置
const analysisConfig = reactive({
  dimension: 'time',
  dateRange: null,
  businessLine: [],
  baseline: 'last_year'
})

// 业绩概览数据
const performanceOverview = ref({
  totalRevenue: 2580000,
  revenueChange: 12.5,
  growthRate: 15.8,
  growthChange: 3.2,
  targetCompletion: 87.5,
  targetChange: 5.8,
  customerCount: 1245,
  customerChange: 8.3
})

// 团队排行数据
const teamRanking = ref([
  {
    id: 1,
    name: '华东销售团队',
    leader: '张经理',
    revenue: 850000,
    customers: 320,
    orders: 1250,
    change: 15.2
  },
  {
    id: 2,
    name: '华南销售团队',
    leader: '李经理',
    revenue: 720000,
    customers: 280,
    orders: 980,
    change: 8.7
  },
  {
    id: 3,
    name: '华北销售团队',
    leader: '王经理',
    revenue: 680000,
    customers: 260,
    orders: 890,
    change: -2.3
  },
  {
    id: 4,
    name: '西南销售团队',
    leader: '赵经理',
    revenue: 520000,
    customers: 190,
    orders: 720,
    change: 12.8
  },
  {
    id: 5,
    name: '华中销售团队',
    leader: '刘经理',
    revenue: 480000,
    customers: 175,
    orders: 650,
    change: -5.1
  }
])

// 预测影响因素
const forecastFactors = ref([
  {
    name: '市场趋势',
    impact: 8.5,
    description: '行业整体向好，市场需求增长'
  },
  {
    name: '季节性因素',
    impact: -3.2,
    description: '淡季影响，销售额可能下降'
  },
  {
    name: '新产品发布',
    impact: 12.8,
    description: '新产品上市，预期带来增量'
  },
  {
    name: '竞争加剧',
    impact: -5.6,
    description: '竞争对手价格战，影响利润'
  },
  {
    name: '营销活动',
    impact: 6.3,
    description: '促销活动预期提升销量'
  }
])

// 改进建议
const improvementSuggestions = ref([
  {
    id: 1,
    title: '加强客户关系管理',
    description: '通过CRM系统优化客户跟进流程，提升客户满意度和复购率',
    priority: 'high',
    currentSituation: '客户流失率较高，复购率偏低，客户生命周期价值未充分挖掘',
    actions: [
      '建立完善的客户分级管理体系',
      '定期进行客户满意度调研',
      '实施个性化客户服务策略',
      '建立客户忠诚度奖励机制'
    ],
    expectedResults: {
      revenue: '+15-20%',
      timeline: '3-6个月',
      cost: '中等'
    }
  },
  {
    id: 2,
    title: '优化产品组合策略',
    description: '调整产品结构，重点推广高利润产品，淘汰低效产品',
    priority: 'high',
    currentSituation: '产品线冗杂，部分产品利润率低，资源配置不够合理',
    actions: [
      '分析各产品线的盈利能力',
      '制定产品优化策略',
      '加大高利润产品推广力度',
      '逐步淘汰低效产品'
    ],
    expectedResults: {
      revenue: '+10-15%',
      timeline: '2-4个月',
      cost: '较低'
    }
  },
  {
    id: 3,
    title: '扩大市场覆盖范围',
    description: '开拓新的地理市场和客户群体，增加市场份额',
    priority: 'medium',
    currentSituation: '市场覆盖不够全面，部分潜在市场未开发',
    actions: [
      '进行市场调研和分析',
      '制定市场拓展计划',
      '建立新的销售渠道',
      '培训销售团队适应新市场'
    ],
    expectedResults: {
      revenue: '+20-25%',
      timeline: '6-12个月',
      cost: '较高'
    }
  },
  {
    id: 4,
    title: '提升销售团队能力',
    description: '加强销售培训，提升团队专业技能和销售效率',
    priority: 'medium',
    currentSituation: '销售团队技能参差不齐，部分团队业绩不达标',
    actions: [
      '制定系统性培训计划',
      '建立销售技能认证体系',
      '实施导师制培养机制',
      '定期进行业绩评估和改进'
    ],
    expectedResults: {
      revenue: '+8-12%',
      timeline: '3-6个月',
      cost: '中等'
    }
  }
])

// 选项数据
const dimensionOptions = [
  { label: '时间维度', value: 'time' },
  { label: '地区维度', value: 'region' },
  { label: '产品维度', value: 'product' },
  { label: '团队维度', value: 'team' }
]

const businessLineOptions = [
  { label: '企业服务', value: 'enterprise' },
  { label: '消费电子', value: 'consumer' },
  { label: '工业设备', value: 'industrial' },
  { label: '医疗器械', value: 'medical' }
]

const baselineOptions = [
  { label: '去年同期', value: 'last_year' },
  { label: '上月同期', value: 'last_month' },
  { label: '上季度', value: 'last_quarter' },
  { label: '行业平均', value: 'industry_avg' }
]

const rankingMetricOptions = [
  { label: '收入', value: 'revenue' },
  { label: '客户数', value: 'customers' },
  { label: '订单数', value: 'orders' }
]

// 工具方法
const formatMetricValue = (value: number) => {
  if (rankingMetric.value === 'revenue') {
    return `¥${(value / 10000).toFixed(1)}万`
  }
  return value.toLocaleString()
}

// 图表渲染方法
const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const currentYear = [180, 210, 230, 200, 240, 260, 220, 280, 250, 290, 270, 258]
  const lastYear = [160, 190, 200, 180, 220, 230, 200, 250, 220, 260, 240, 230]
  const target = [200, 220, 240, 220, 260, 280, 240, 300, 270, 310, 290, 280]
  
  const option = {
    title: {
      text: '业绩趋势对比分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['今年实际', '去年同期', '目标业绩'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}万'
      }
    },
    series: [
      {
        name: '今年实际',
        type: 'line',
        data: currentYear,
        lineStyle: { color: '#2080f0', width: 3 },
        areaStyle: { color: 'rgba(32, 128, 240, 0.1)' },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '去年同期',
        type: 'line',
        data: lastYear,
        lineStyle: { color: '#18a058', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '目标业绩',
        type: 'line',
        data: target,
        lineStyle: { color: '#f0a020', width: 2, type: 'dashed' },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }
  
  chart.setOption(option)
}

const renderCompositionChart = () => {
  if (!compositionChartRef.value) return
  
  const chart = echarts.init(compositionChartRef.value)
  
  const compositionData = [
    { value: 35, name: '产品销售' },
    { value: 25, name: '服务收入' },
    { value: 20, name: '授权费用' },
    { value: 15, name: '维护收入' },
    { value: 5, name: '其他收入' }
  ]
  
  const option = {
    title: {
      text: '收入构成分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '收入构成',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: compositionData
      }
    ]
  }
  
  chart.setOption(option)
}

const renderRegionChart = () => {
  if (!regionChartRef.value) return
  
  const chart = echarts.init(regionChartRef.value)
  
  const regions = ['华东', '华南', '华北', '西南', '华中']
  const regionRevenue = [850, 720, 680, 520, 480]
  
  const option = {
    title: {
      text: '地区业绩分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: regions
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}万'
      }
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: regionRevenue,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#2080f0' },
            { offset: 1, color: '#1890ff' }
          ])
        },
        label: {
          show: true,
          position: 'top',
          formatter: '¥{c}万'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const renderTeamChart = () => {
  if (!teamChartRef.value) return
  
  const chart = echarts.init(teamChartRef.value)
  
  const teams = teamRanking.value.map(team => team.name.replace('销售团队', ''))
  const revenue = teamRanking.value.map(team => team.revenue / 10000)
  const customers = teamRanking.value.map(team => team.customers)
  
  const option = {
    title: {
      text: '团队业绩对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['销售额', '客户数'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: teams
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额(万元)',
        position: 'left',
        axisLabel: {
          formatter: '{value}万'
        }
      },
      {
        type: 'value',
        name: '客户数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        yAxisIndex: 0,
        data: revenue,
        itemStyle: { color: '#2080f0' }
      },
      {
        name: '客户数',
        type: 'line',
        yAxisIndex: 1,
        data: customers,
        lineStyle: { color: '#18a058', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }
  
  chart.setOption(option)
}

const renderProductCharts = () => {
  // 产品收入图表
  if (productRevenueChartRef.value) {
    const chart = echarts.init(productRevenueChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const revenue = [850, 720, 680, 520, 480]
    
    const option = {
      title: {
        text: '产品收入分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '¥{value}万'
        }
      },
      series: [
        {
          name: '收入',
          type: 'bar',
          data: revenue,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2080f0' },
              { offset: 1, color: '#1890ff' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
  
  // 产品增长图表
  if (productGrowthChartRef.value) {
    const chart = echarts.init(productGrowthChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const growth = [15.2, 8.7, -2.3, 12.8, -5.1]
    
    const option = {
      title: {
        text: '产品增长分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '增长率',
          type: 'bar',
          data: growth,
          itemStyle: {
            color: (params: any) => {
              return params.value >= 0 ? '#18a058' : '#d03050'
            }
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
  
  // 产品利润图表
  if (productMarginChartRef.value) {
    const chart = echarts.init(productMarginChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const margin = [25.5, 32.8, 28.6, 22.3, 35.2]
    
    const option = {
      title: {
        text: '产品利润分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '利润率',
          type: 'bar',
          data: margin,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#f0a020' },
              { offset: 1, color: '#faad14' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
}

const renderForecastChart = () => {
  if (!forecastChartRef.value) return
  
  const chart = echarts.init(forecastChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const actual = [180, 210, 230, 200, 240, 260, 220, 280, 250, null, null, null]
  const forecast = [null, null, null, null, null, null, null, null, 250, 290, 320, 350]
  const confidence = [null, null, null, null, null, null, null, null, [230, 270], [270, 310], [300, 340], [330, 370]]
  
  const option = {
    title: {
      text: '业绩预测分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['实际业绩', '预测业绩', '置信区间'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}万'
      }
    },
    series: [
      {
        name: '实际业绩',
        type: 'line',
        data: actual,
        lineStyle: { color: '#2080f0', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '预测业绩',
        type: 'line',
        data: forecast,
        lineStyle: { color: '#f0a020', width: 3, type: 'dashed' },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }
  
  chart.setOption(option)
}

// 监听产品标签页切换
watch(activeProductTab, () => {
  nextTick(() => {
    renderProductCharts()
  })
})

// 生命周期
onMounted(async () => {
  await nextTick()
  renderTrendChart()
  renderCompositionChart()
  renderRegionChart()
  renderTeamChart()
  renderProductCharts()
  renderForecastChart()
})
</script>

<style scoped>
.performance-analysis {
  padding: 24px;
}

.config-card,
.overview-card,
.trend-card,
.team-card,
.product-card,
.forecast-card,
.improvement-card {
  margin-bottom: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #18a058;
}

.stat-change.negative {
  color: #d03050;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.team-performance {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.ranking-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.ranking-position {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 60px;
}

.position-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  font-size: 14px;
}

.position-number.rank-1 {
  background: #ffd700;
}

.position-number.rank-2 {
  background: #c0c0c0;
}

.position-number.rank-3 {
  background: #cd7f32;
}

.position-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: #999;
}

.position-medal {
  font-size: 16px;
}

.team-info {
  flex: 1;
}

.team-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2px;
  font-size: 14px;
}

.team-leader {
  color: #666;
  font-size: 12px;
}

.team-metrics {
  text-align: right;
}

.metric-value {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2px;
  font-size: 14px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.forecast-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.forecast-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.forecast-summary h3,
.forecast-factors h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-label {
  color: #666;
}

.summary-value {
  font-weight: 500;
  color: #1a1a1a;
}

.factor-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.factor-name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.factor-impact {
  font-weight: 600;
  margin-bottom: 4px;
}

.factor-impact.positive {
  color: #18a058;
}

.factor-impact.negative {
  color: #d03050;
}

.factor-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.improvement-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.improvement-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
}

.improvement-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.improvement-icon {
  font-size: 24px;
}

.improvement-info {
  flex: 1;
}

.improvement-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.improvement-description {
  color: #666;
  font-size: 14px;
}

.improvement-details {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-section p {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.detail-section ul {
  color: #666;
  line-height: 1.5;
  padding-left: 20px;
}

.detail-section li {
  margin-bottom: 4px;
}

.expected-results {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.result-label {
  color: #666;
  font-size: 14px;
}

.result-value {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}
</style>