import { http } from './http'
import type { Customer, ApiResponse, PaginationResponse } from '@/types'

// 客户API接口
export const customerApi = {
  // 获取客户列表
  getCustomers: (params: any): Promise<ApiResponse<PaginationResponse<Customer>>> => {
    return http.get('/customers', { params })
  },

  // 获取客户详情
  getCustomer: (id: string): Promise<ApiResponse<Customer>> => {
    return http.get(`/customers/${id}`)
  },

  // 创建客户
  createCustomer: (data: Partial<Customer>): Promise<ApiResponse<Customer>> => {
    return http.post('/customers', data)
  },

  // 更新客户
  updateCustomer: (id: string, data: Partial<Customer>): Promise<ApiResponse<Customer>> => {
    return http.put(`/customers/${id}`, data)
  },

  // 删除客户
  deleteCustomer: (id: string): Promise<ApiResponse<void>> => {
    return http.delete(`/customers/${id}`)
  },

  // 分配客户
  assignCustomer: (customerId: string, userId: string): Promise<ApiResponse<void>> => {
    return http.post(`/customers/${customerId}/assign`, { userId })
  },

  // 批量删除客户
  batchDeleteCustomers: (ids: string[]): Promise<ApiResponse<void>> => {
    return http.post('/customers/batch-delete', { ids })
  },

  // 导入客户
  importCustomers: (file: File): Promise<ApiResponse<any>> => {
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/customers/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出客户
  exportCustomers: (params: any): Promise<Blob> => {
    return http.get('/customers/export', {
      params,
      responseType: 'blob'
    })
  },

  // 获取客户统计
  getCustomerStats: (): Promise<ApiResponse<any>> => {
    return http.get('/customers/stats')
  },

  // 获取客户来源统计
  getCustomerSourceStats: (): Promise<ApiResponse<any>> => {
    return http.get('/customers/source-stats')
  },

  // 获取客户标签
  getCustomerTags: (): Promise<ApiResponse<any[]>> => {
    return http.get('/customers/tags')
  },

  // 创建客户标签
  createCustomerTag: (data: { name: string; color?: string }): Promise<ApiResponse<any>> => {
    return http.post('/customers/tags', data)
  },

  // 为客户添加标签
  addCustomerTag: (customerId: string, tagId: string): Promise<ApiResponse<void>> => {
    return http.post(`/customers/${customerId}/tags`, { tagId })
  },

  // 移除客户标签
  removeCustomerTag: (customerId: string, tagId: string): Promise<ApiResponse<void>> => {
    return http.delete(`/customers/${customerId}/tags/${tagId}`)
  }
}