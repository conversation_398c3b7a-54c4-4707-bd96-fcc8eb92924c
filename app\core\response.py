from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """统一API响应格式"""
    code: int = 200
    message: str = "success"
    data: Optional[T] = None
    
    class Config:
        schema_extra = {
            "example": {
                "code": 200,
                "message": "success",
                "data": {}
            }
        }


def success_response(data: Any = None, message: str = "操作成功", code: int = 200) -> ApiResponse:
    """成功响应"""
    return ApiResponse(code=code, message=message, data=data)


def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> ApiResponse:
    """错误响应"""
    return ApiResponse(code=code, message=message, data=data)