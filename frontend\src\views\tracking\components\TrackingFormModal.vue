<template>
  <n-modal v-model:show="showModal" preset="dialog" style="width: 800px">
    <template #header>
      <span>{{ isEdit ? '编辑跟踪记录' : '新建跟踪记录' }}</span>
    </template>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="客户" path="customer_id">
            <n-select
              v-model:value="formData.customer_id"
              placeholder="选择客户"
              filterable
              remote
              :options="customerOptions"
              :loading="customerLoading"
              @search="handleCustomerSearch"
              @update:value="handleCustomerChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="跟踪类型" path="type">
            <n-select
              v-model:value="formData.type"
              placeholder="选择跟踪类型"
              :options="typeOptions"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <n-form-item label="跟踪内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入跟踪内容"
          :rows="4"
          show-count
          maxlength="500"
        />
      </n-form-item>

      <n-form-item label="跟踪结果" path="result">
        <n-input
          v-model:value="formData.result"
          type="textarea"
          placeholder="请输入跟踪结果（可选）"
          :rows="3"
          show-count
          maxlength="300"
        />
      </n-form-item>

      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="下次跟进时间" path="next_follow_time">
            <n-date-picker
              v-model:value="nextFollowTime"
              type="datetime"
              placeholder="选择下次跟进时间"
              style="width: 100%"
              clearable
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="状态" path="status">
            <n-select
              v-model:value="formData.status"
              placeholder="选择状态"
              :options="statusOptions"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <n-form-item label="附件">
        <n-upload
          v-model:file-list="fileList"
          multiple
          directory-dnd
          :max="5"
          @before-upload="handleBeforeUpload"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <CloudUpload />
              </n-icon>
            </div>
            <n-text style="font-size: 16px">
              点击或者拖动文件到该区域来上传
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              支持单个或批量上传，最多上传5个文件
            </n-p>
          </n-upload-dragger>
        </n-upload>
      </n-form-item>

      <!-- 自动提醒设置 -->
      <n-divider>自动提醒设置</n-divider>
      
      <n-form-item>
        <n-checkbox v-model:checked="enableReminder">
          启用自动提醒
        </n-checkbox>
      </n-form-item>

      <template v-if="enableReminder">
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="提醒时间">
              <n-date-picker
                v-model:value="reminderTime"
                type="datetime"
                placeholder="选择提醒时间"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="提醒类型">
              <n-select
                v-model:value="reminderType"
                placeholder="选择提醒类型"
                :options="reminderTypeOptions"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="提醒标题">
          <n-input
            v-model:value="reminderTitle"
            placeholder="请输入提醒标题"
            maxlength="100"
          />
        </n-form-item>

        <n-form-item label="提醒内容">
          <n-input
            v-model:value="reminderContent"
            type="textarea"
            placeholder="请输入提醒内容"
            :rows="3"
            maxlength="200"
          />
        </n-form-item>
      </template>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import { CloudUpload } from '@vicons/ionicons5'
import { useTrackingStore } from '@/stores/trackingStore'
import { useCustomerStore } from '@/stores/modules/customer'
import type { TrackingRecord } from '@/api/trackingService'
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui'

interface Props {
  show: boolean
  trackingRecord?: TrackingRecord | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const trackingStore = useTrackingStore()
const customerStore = useCustomerStore()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const customerLoading = ref(false)
const customerOptions = ref<Array<{ label: string; value: number }>>([])
const fileList = ref<UploadFileInfo[]>([])
const nextFollowTime = ref<number | null>(null)
const enableReminder = ref(false)
const reminderTime = ref<number | null>(null)
const reminderType = ref('follow_up')
const reminderTitle = ref('')
const reminderContent = ref('')

// 表单数据
const formData = ref({
  customer_id: null as number | null,
  user_id: 1, // 当前用户ID，实际应该从用户状态获取
  type: 'call' as 'call' | 'visit' | 'email' | 'wechat' | 'meeting' | 'other',
  content: '',
  result: '',
  next_follow_time: '',
  status: 'pending' as 'pending' | 'completed' | 'cancelled'
})

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.trackingRecord)

// 选项配置
const typeOptions = [
  { label: '电话', value: 'call' },
  { label: '拜访', value: 'visit' },
  { label: '邮件', value: 'email' },
  { label: '微信', value: 'wechat' },
  { label: '会议', value: 'meeting' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '待跟进', value: 'pending' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

const reminderTypeOptions = [
  { label: '跟进提醒', value: 'follow_up' },
  { label: '生日提醒', value: 'birthday' },
  { label: '合同续约', value: 'contract_renewal' },
  { label: '付款到期', value: 'payment_due' },
  { label: '自定义', value: 'custom' }
]

// 表单验证规则
const rules: FormRules = {
  customer_id: {
    required: true,
    type: 'number',
    message: '请选择客户',
    trigger: ['blur', 'change']
  },
  type: {
    required: true,
    message: '请选择跟踪类型',
    trigger: ['blur', 'change']
  },
  content: {
    required: true,
    message: '请输入跟踪内容',
    trigger: ['blur', 'input']
  },
  status: {
    required: true,
    message: '请选择状态',
    trigger: ['blur', 'change']
  }
}

// 监听器
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.trackingRecord) {
      loadTrackingRecord()
    }
    loadCustomers()
  }
})

watch(nextFollowTime, (newVal) => {
  if (newVal) {
    formData.value.next_follow_time = new Date(newVal).toISOString()
  } else {
    formData.value.next_follow_time = ''
  }
})

// 方法
const resetForm = () => {
  formData.value = {
    customer_id: null,
    user_id: 1,
    type: 'call',
    content: '',
    result: '',
    next_follow_time: '',
    status: 'pending'
  }
  nextFollowTime.value = null
  fileList.value = []
  enableReminder.value = false
  reminderTime.value = null
  reminderType.value = 'follow_up'
  reminderTitle.value = ''
  reminderContent.value = ''
  
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

const loadTrackingRecord = () => {
  if (!props.trackingRecord) return
  
  const record = props.trackingRecord
  formData.value = {
    customer_id: record.customer_id,
    user_id: record.user_id,
    type: record.type,
    content: record.content,
    result: record.result || '',
    next_follow_time: record.next_follow_time || '',
    status: record.status
  }
  
  if (record.next_follow_time) {
    nextFollowTime.value = new Date(record.next_follow_time).getTime()
  }
  
  // 如果有客户信息，添加到选项中
  if (record.customer_name) {
    customerOptions.value = [{
      label: record.customer_name,
      value: record.customer_id
    }]
  }
}

const loadCustomers = async (keyword = '') => {
  customerLoading.value = true
  try {
    const response = await customerStore.fetchCustomers({
      page: 1,
      page_size: 50
    })
    
    const customers = response.data?.data || []
    customerOptions.value = customers.map((customer: any) => ({
      label: `${customer.name} (${customer.phone})`,
      value: customer.id
    }))
  } catch (error) {
    console.error('Failed to load customers:', error)
  } finally {
    customerLoading.value = false
  }
}

const handleCustomerSearch = (query: string) => {
  if (query) {
    loadCustomers(query)
  }
}

const handleCustomerChange = (value: number) => {
  // 可以在这里加载客户详细信息
}

const handleBeforeUpload = (data: { file: UploadFileInfo }) => {
  // 这里可以添加文件上传前的验证逻辑
  const allowedTypes = ['image/', 'application/pdf', 'application/msword', 'text/']
  const isAllowed = allowedTypes.some(type => data.file.type?.startsWith(type))
  
  if (!isAllowed) {
    message.error('只支持图片、PDF、Word文档和文本文件')
    return false
  }
  
  if (data.file.file && data.file.file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  return true
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.value.customer_id === null) {
      message.error('请选择客户')
      return
    }
    
    loading.value = true
    
    const submitData = {
      ...formData.value,
      customer_id: formData.value.customer_id as number
    }
    
    if (isEdit.value && props.trackingRecord) {
      // 更新跟踪记录
      await trackingStore.updateTrackingRecord(props.trackingRecord.id, submitData)
      message.success('跟踪记录更新成功')
    } else {
      // 创建跟踪记录
      await trackingStore.createTrackingRecord(submitData)
      message.success('跟踪记录创建成功')
    }
    
    // 如果启用了提醒，创建提醒
    if (enableReminder.value && reminderTime.value && formData.value.customer_id) {
      try {
        await trackingStore.createReminder({
          customer_id: formData.value.customer_id,
          user_id: formData.value.user_id,
          title: reminderTitle.value || '跟进提醒',
          content: reminderContent.value || formData.value.content,
          remind_time: new Date(reminderTime.value).toISOString(),
          type: reminderType.value as any,
          status: 'pending'
        })
        message.success('提醒设置成功')
      } catch (error) {
        message.warning('跟踪记录创建成功，但提醒设置失败')
      }
    }
    
    emit('success')
  } catch (error) {
    console.error('Submit error:', error)
    if (error instanceof Error) {
      message.error(error.message)
    } else {
      message.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  showModal.value = false
}
</script>

<style scoped>
.n-form {
  padding: 20px 0;
}
</style>