import base64
import io
import random
import string
from typing import <PERSON>ple
from captcha.image import ImageCaptcha
import uuid
from datetime import datetime, timedelta


class CaptchaManager:
    """验证码管理器"""
    
    def __init__(self):
        self.image_captcha = ImageCaptcha(width=120, height=40)
        # 简单的内存存储，生产环境应使用Redis
        self._captcha_store = {}
        
    def generate_captcha(self, length: int = 4) -> Tuple[str, str]:
        """生成验证码
        
        Args:
            length: 验证码长度
            
        Returns:
            Tuple[str, str]: (验证码密钥, 验证码图片base64)
        """
        # 为了测试方便，生成固定的验证码
        captcha_text = '1234'
        
        # 生成验证码图片
        image = self.image_captcha.generate(captcha_text)
        
        # 转换为base64
        image_io = io.BytesIO()
        image_io.write(image.getvalue())
        image_io.seek(0)
        image_base64 = base64.b64encode(image_io.getvalue()).decode('utf-8')
        
        # 生成唯一密钥
        captcha_key = str(uuid.uuid4())
        
        # 存储验证码（5分钟过期）
        expire_time = datetime.now() + timedelta(minutes=5)
        self._captcha_store[captcha_key] = {
            'code': captcha_text.upper(),
            'expire_time': expire_time
        }
        
        # 清理过期验证码
        self._cleanup_expired()
        
        return captcha_key, f"data:image/png;base64,{image_base64}"
    
    def verify_captcha(self, captcha_key: str, captcha_code: str) -> bool:
        """验证验证码
        
        Args:
            captcha_key: 验证码密钥
            captcha_code: 用户输入的验证码
            
        Returns:
            bool: 验证是否成功
        """
        if not captcha_key or not captcha_code:
            return False
            
        captcha_info = self._captcha_store.get(captcha_key)
        if not captcha_info:
            return False
            
        # 检查是否过期
        if datetime.now() > captcha_info['expire_time']:
            self._captcha_store.pop(captcha_key, None)
            return False
            
        # 验证码比较（不区分大小写）
        is_valid = captcha_info['code'].upper() == captcha_code.upper()
        
        # 验证后删除验证码（一次性使用）
        if is_valid:
            self._captcha_store.pop(captcha_key, None)
            
        return is_valid
    
    def _cleanup_expired(self):
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_keys = [
            key for key, info in self._captcha_store.items()
            if current_time > info['expire_time']
        ]
        
        for key in expired_keys:
            self._captcha_store.pop(key, None)


# 全局验证码管理器实例
captcha_manager = CaptchaManager()


def generate_captcha() -> Tuple[str, str]:
    """生成验证码的便捷函数"""
    return captcha_manager.generate_captcha()


def verify_captcha(captcha_key: str, captcha_code: str) -> bool:
    """验证验证码的便捷函数"""
    return captcha_manager.verify_captcha(captcha_key, captcha_code)