{"migrationId": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "timestamp": "2025-08-18T07:03:52.281Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:03:16.305Z", "errors": [], "endTime": "2025-08-18T07:03:19.565Z", "duration": 3260}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:03:19.572Z", "errors": [], "endTime": "2025-08-18T07:03:21.064Z", "duration": 1492}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:03:21.067Z", "errors": [], "endTime": "2025-08-18T07:03:22.876Z", "duration": 1809}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:03:22.879Z", "errors": [], "endTime": "2025-08-18T07:03:25.278Z", "duration": 2399}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:25.282Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:03:25.828Z", "errors": [], "endTime": "2025-08-18T07:03:27.519Z", "duration": 1691}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:03:27.523Z", "errors": [], "endTime": "2025-08-18T07:03:30.437Z", "duration": 2914}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:03:30.439Z", "errors": [], "endTime": "2025-08-18T07:03:32.519Z", "duration": 2080}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:32.522Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:03:32.814Z", "errors": ["Unknown column 'share_count' in 'field list'"], "endTime": "2025-08-18T07:03:34.307Z", "duration": 1493}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:34.310Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:34.570Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:03:35.487Z", "errors": ["Unknown column 'customer_id' in 'field list'"], "endTime": "2025-08-18T07:03:38.890Z", "duration": 3403}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:38.892Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:03:39.198Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:03:41.234Z", "duration": 2036}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:41.237Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:41.993Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:42.863Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:03:43.534Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:03:44.307Z", "errors": ["Unknown column 'created_by' in 'field list'"], "endTime": "2025-08-18T07:03:48.950Z", "duration": 4643}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:03:48.953Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:03:52.279Z", "duration": 3326}], "logs": [{"id": "593e4a7a-248a-420d-b548-0c789590854d", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:03:16.305Z", "end_time": "2025-08-18T07:03:19.566Z", "duration_ms": 3261}, {"id": "56b7ac48-6db8-4279-9333-fe44b1d3bf22", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:03:19.572Z", "end_time": "2025-08-18T07:03:21.064Z", "duration_ms": 1492}, {"id": "36c1e3f5-584b-4fbc-8587-2c67851ffe7a", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:03:21.067Z", "end_time": "2025-08-18T07:03:22.876Z", "duration_ms": 1809}, {"id": "d635ec7b-09ca-4b80-a709-4a97633c0d8b", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:03:22.879Z", "end_time": "2025-08-18T07:03:25.278Z", "duration_ms": 2399}, {"id": "3aff96ed-74fe-43c9-b54e-c62f764eabdb", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:25.282Z", "end_time": "2025-08-18T07:03:25.828Z", "duration_ms": 546}, {"id": "cd08fe79-cbf0-4b3a-97a0-c3b946cbf4fb", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:03:25.828Z", "end_time": "2025-08-18T07:03:27.519Z", "duration_ms": 1691}, {"id": "55cee74e-6b77-41f4-808f-489dc385849c", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:03:27.523Z", "end_time": "2025-08-18T07:03:30.437Z", "duration_ms": 2914}, {"id": "9da3a27b-f949-46f1-947c-dfedbb5cb4df", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:03:30.439Z", "end_time": "2025-08-18T07:03:32.519Z", "duration_ms": 2080}, {"id": "07edec22-69dd-4a53-8e6a-34550022b9ec", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:32.522Z", "end_time": "2025-08-18T07:03:32.814Z", "duration_ms": 292}, {"id": "190a72d6-c345-4c5c-b862-3a1a77703f7f", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:32.814Z", "end_time": "2025-08-18T07:03:34.307Z", "duration_ms": 1493}, {"id": "4942e6e3-7083-457d-8b9b-26d0cedabcdf", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:34.310Z", "end_time": "2025-08-18T07:03:34.570Z", "duration_ms": 260}, {"id": "4091eece-394e-4338-b10b-5fa5819cb501", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:34.570Z", "end_time": "2025-08-18T07:03:35.487Z", "duration_ms": 917}, {"id": "3a34c9d5-de4f-417a-8124-ce2017d0daf4", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:35.487Z", "end_time": "2025-08-18T07:03:38.890Z", "duration_ms": 3403}, {"id": "b275ccbb-fa27-45f8-8e15-1475abb630f9", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:38.892Z", "end_time": "2025-08-18T07:03:39.198Z", "duration_ms": 306}, {"id": "6802c242-2666-4abe-a664-1e06e655dacf", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:39.198Z", "end_time": "2025-08-18T07:03:41.234Z", "duration_ms": 2036}, {"id": "00caa45f-51b1-4321-aec1-d9860fb697d8", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:41.237Z", "end_time": "2025-08-18T07:03:41.993Z", "duration_ms": 756}, {"id": "7163e6d4-ae17-42fe-bef2-38be0d94221d", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:41.994Z", "end_time": "2025-08-18T07:03:42.863Z", "duration_ms": 869}, {"id": "acccaa1f-a107-4c7d-9009-bb8271f63ddc", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:42.863Z", "end_time": "2025-08-18T07:03:43.534Z", "duration_ms": 671}, {"id": "34b2b22a-b464-4e8b-a2ec-d5213adb95b2", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:43.534Z", "end_time": "2025-08-18T07:03:44.307Z", "duration_ms": 773}, {"id": "de9a2f21-3180-4da0-b7a9-2371bbc37a4e", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:44.307Z", "end_time": "2025-08-18T07:03:48.950Z", "duration_ms": 4643}, {"id": "8b88ffea-2b42-4aac-b726-64cc1c3a8d92", "migration_id": "dd420da1-0e62-46eb-8ec1-4a79e220ed69", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:03:48.953Z", "end_time": "2025-08-18T07:03:52.279Z", "duration_ms": 3326}]}