<template>
  <div class="site-management">
    <n-card title="工地管理" :bordered="false">
      <template #header-extra>
        <n-button type="primary" @click="showAddModal = true">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增工地
        </n-button>
      </template>

      <!-- 搜索栏 -->
      <n-space class="mb-4" justify="space-between">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索工地名称或地址"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        
        <n-select
          v-model:value="statusFilter"
          placeholder="筛选状态"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
      </n-space>

      <!-- 工地列表 -->
      <n-data-table
        :columns="columns"
        :data="filteredSites"
        :pagination="pagination"
        :loading="loading"
        :row-key="(row: Site) => row.id"
      />
    </n-card>

    <!-- 新增/编辑工地弹窗 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="工地信息">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="{
          name: {
            required: true,
            message: '请输入工地名称',
            trigger: ['input', 'blur']
          },
          address: {
            required: true,
            message: '请输入工地地址',
            trigger: ['input', 'blur']
          },
          manager: {
            required: true,
            message: '请输入负责人姓名',
            trigger: ['input', 'blur']
          },
          phone: {
            required: true,
            message: '请输入联系电话',
            trigger: ['input', 'blur']
          },
          startDate: {
            required: true,
            validator: (_rule: any, value: number | null) => {
              if (value === null) return new Error('请选择开工日期')
            },
            trigger: ['change', 'blur']
          },
          expectedEndDate: {
            required: true,
            validator: (_rule: any, value: number | null) => {
              if (value === null) return new Error('请选择预计完工日期')
            },
            trigger: ['change', 'blur']
          }
        }"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="工地名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入工地名称" />
        </n-form-item>
        
        <n-form-item label="工地地址" path="address">
          <n-input v-model:value="formData.address" placeholder="请输入工地地址" />
        </n-form-item>
        
        <n-form-item label="负责人" path="manager">
          <n-input v-model:value="formData.manager" placeholder="请输入负责人姓名" />
        </n-form-item>
        
        <n-form-item label="联系电话" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入联系电话" />
        </n-form-item>
        
        <n-form-item label="开工日期" path="startDate">
          <n-date-picker
            v-model:value="formData.startDate"
            type="date"
            placeholder="请选择开工日期"
            style="width: 100%"
          />
        </n-form-item>
        
        <n-form-item label="预计完工日期" path="expectedEndDate">
          <n-date-picker
            v-model:value="formData.expectedEndDate"
            type="date"
            placeholder="请选择预计完工日期"
            style="width: 100%"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 工程进度弹窗 -->
    <n-modal v-model:show="showProgressModal" preset="dialog" title="工程进度管理" style="width: 800px">
      <div v-if="currentSite">
        <n-card :title="currentSite.name" class="mb-4">
          <n-descriptions :column="2">
            <n-descriptions-item label="地址">{{ currentSite.address }}</n-descriptions-item>
            <n-descriptions-item label="负责人">{{ currentSite.manager }}</n-descriptions-item>
            <n-descriptions-item label="联系电话">{{ currentSite.phone }}</n-descriptions-item>
            <n-descriptions-item label="开工日期">{{ formatDate(currentSite.startDate) }}</n-descriptions-item>
          </n-descriptions>
        </n-card>
        
        <n-card title="工程进度">
          <n-steps
            :current="getCurrentStep(currentSite.progress)"
            :status="getStepStatus(currentSite.progress)"
          >
            <n-step
              v-for="(step, index) in progressSteps"
              :key="step.key"
              :title="step.label"
              :description="getStepDescription(step.key, currentSite.progress)"
            >
              <template #icon>
                <n-icon :color="getStepIconColor(step.key, currentSite.progress)">
                  <component :is="step.icon" />
                </n-icon>
              </template>
            </n-step>
          </n-steps>
          
          <n-divider />
          
          <n-space>
            <n-button
              v-for="step in progressSteps"
              :key="step.key"
              :type="currentSite.progress[step.key as keyof typeof currentSite.progress]?.status === 'completed' ? 'success' : 'default'"
              :disabled="!canUpdateStep(step.key, currentSite.progress)"
              @click="updateProgress(step.key)"
            >
              {{ step.label }}
              {{ currentSite.progress[step.key as keyof typeof currentSite.progress]?.status === 'completed' ? '✓' : '' }}
            </n-button>
          </n-space>
        </n-card>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NSpace,
  NInput,
  NSelect,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NDatePicker,
  NDescriptions,
  NDescriptionsItem,
  NSteps,
  NStep,
  NDivider,
  NTag,
  useMessage,
  type FormInst
} from 'naive-ui'
import {
  AddOutline,
  SearchOutline,
  ConstructOutline,
  FlashOutline,
  HammerOutline,
  ColorPaletteOutline,
  BrushOutline,
  BedOutline,
  CheckmarkCircleOutline,
  EyeOutline,
  CreateOutline
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

interface ProgressStep {
  status: 'pending' | 'in_progress' | 'completed'
  startDate?: number
  endDate?: number
  notes?: string
}

interface Site {
  id: number
  name: string
  address: string
  manager: string
  phone: string
  startDate: number
  expectedEndDate: number
  status: 'planning' | 'in_progress' | 'completed' | 'suspended'
  progress: {
    start: ProgressStep
    electrical: ProgressStep
    carpentry: ProgressStep
    masonry: ProgressStep
    putty: ProgressStep
    furniture: ProgressStep
    completion: ProgressStep
  }
  createdAt: string
}

const message = useMessage()
const loading = ref(false)
const showAddModal = ref(false)
const showProgressModal = ref(false)
const formRef = ref<FormInst | null>(null)
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const currentSite = ref<Site | null>(null)

// 工程进度步骤定义
const progressSteps = [
  { key: 'start', label: '开工', icon: ConstructOutline },
  { key: 'electrical', label: '水电', icon: FlashOutline },
  { key: 'carpentry', label: '木工', icon: HammerOutline },
  { key: 'masonry', label: '泥工', icon: ColorPaletteOutline },
  { key: 'putty', label: '腻子', icon: BrushOutline },
  { key: 'furniture', label: '家具', icon: BedOutline },
  { key: 'completion', label: '完工', icon: CheckmarkCircleOutline }
]

// 状态选项
const statusOptions = [
  { label: '规划中', value: 'planning' },
  { label: '施工中', value: 'in_progress' },
  { label: '已完工', value: 'completed' },
  { label: '暂停', value: 'suspended' }
]

// 表格数据
const sites = ref<Site[]>([
  {
    id: 1,
    name: '阳光花园A栋',
    address: '北京市朝阳区阳光花园小区A栋',
    manager: '张三',
    phone: '13800138001',
    startDate: Date.now() - 30 * 24 * 60 * 60 * 1000,
    expectedEndDate: Date.now() + 60 * 24 * 60 * 60 * 1000,
    status: 'in_progress',
    progress: {
      start: { status: 'completed', startDate: Date.now() - 30 * 24 * 60 * 60 * 1000, endDate: Date.now() - 28 * 24 * 60 * 60 * 1000 },
      electrical: { status: 'completed', startDate: Date.now() - 28 * 24 * 60 * 60 * 1000, endDate: Date.now() - 20 * 24 * 60 * 60 * 1000 },
      carpentry: { status: 'in_progress', startDate: Date.now() - 20 * 24 * 60 * 60 * 1000 },
      masonry: { status: 'pending' },
      putty: { status: 'pending' },
      furniture: { status: 'pending' },
      completion: { status: 'pending' }
    },
    createdAt: '2024-01-15'
  },
  {
    id: 2,
    name: '绿城花园B栋',
    address: '上海市浦东新区绿城花园小区B栋',
    manager: '李四',
    phone: '13800138002',
    startDate: Date.now() - 10 * 24 * 60 * 60 * 1000,
    expectedEndDate: Date.now() + 80 * 24 * 60 * 60 * 1000,
    status: 'in_progress',
    progress: {
      start: { status: 'completed', startDate: Date.now() - 10 * 24 * 60 * 60 * 1000, endDate: Date.now() - 8 * 24 * 60 * 60 * 1000 },
      electrical: { status: 'in_progress', startDate: Date.now() - 8 * 24 * 60 * 60 * 1000 },
      carpentry: { status: 'pending' },
      masonry: { status: 'pending' },
      putty: { status: 'pending' },
      furniture: { status: 'pending' },
      completion: { status: 'pending' }
    },
    createdAt: '2024-01-20'
  }
])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 表格列配置
const columns: DataTableColumns<Site> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '工地名称',
    key: 'name',
    width: 150
  },
  {
    title: '地址',
    key: 'address',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '负责人',
    key: 'manager',
    width: 100
  },
  {
    title: '联系电话',
    key: 'phone',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        planning: { type: 'info', text: '规划中' },
        in_progress: { type: 'warning', text: '施工中' },
        completed: { type: 'success', text: '已完工' },
        suspended: { type: 'error', text: '暂停' }
      }
      const status = statusMap[row.status]
      return h(NTag, { type: status.type as 'info' | 'warning' | 'success' | 'error' }, { default: () => status.text })
    }
  },
  {
    title: '进度',
    key: 'progress',
    width: 120,
    render(row) {
      const completedSteps = Object.values(row.progress).filter(step => step.status === 'completed').length
      const totalSteps = Object.keys(row.progress).length
      const percentage = Math.round((completedSteps / totalSteps) * 100)
      return `${percentage}% (${completedSteps}/${totalSteps})`
    }
  },
  {
    title: '开工日期',
    key: 'startDate',
    width: 120,
    render(row) {
      return formatDate(row.startDate)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h(NSpace, null, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              secondary: true,
              onClick: () => viewProgress(row)
            },
            { default: () => '进度', icon: () => h(NIcon, null, { default: () => h(EyeOutline) }) }
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              secondary: true,
              onClick: () => handleEdit(row)
            },
            { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
          )
        ]
      })
    }
  }
]

// 表单数据
const formData = reactive({
  name: '',
  address: '',
  manager: '',
  phone: '',
  startDate: null as number | null,
  expectedEndDate: null as number | null
})

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入工地名称',
    trigger: ['input', 'blur']
  },
  address: {
    required: true,
    message: '请输入工地地址',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请输入负责人姓名',
    trigger: ['input', 'blur']
  },
  phone: {
    required: true,
    message: '请输入联系电话',
    trigger: ['input', 'blur']
  },
  startDate: {
    required: true,
    type: 'number',
    message: '请选择开工日期',
    trigger: ['change', 'blur']
  },
  expectedEndDate: {
    required: true,
    type: 'number',
    message: '请选择预计完工日期',
    trigger: ['change', 'blur']
  }
}

// 过滤后的工地列表
const filteredSites = computed(() => {
  let result = sites.value
  
  if (searchKeyword.value) {
    result = result.filter(site => 
      site.name.includes(searchKeyword.value) || 
      site.address.includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(site => site.status === statusFilter.value)
  }
  
  return result
})

// 格式化日期
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// 获取当前步骤
const getCurrentStep = (progress: Site['progress']) => {
  const steps = Object.keys(progress)
  for (let i = 0; i < steps.length; i++) {
    if (progress[steps[i] as keyof typeof progress].status !== 'completed') {
      return i
    }
  }
  return steps.length - 1
}

// 获取步骤状态
const getStepStatus = (progress: Site['progress']) => {
  const currentStep = getCurrentStep(progress)
  const steps = Object.keys(progress)
  const currentStepKey = steps[currentStep] as keyof typeof progress
  
  if (progress[currentStepKey].status === 'in_progress') {
    return 'process'
  } else if (currentStep === steps.length - 1 && progress[currentStepKey].status === 'completed') {
    return 'finish'
  }
  return 'process'
}

// 获取步骤描述
const getStepDescription = (stepKey: string, progress: Site['progress']) => {
  const step = progress[stepKey as keyof typeof progress]
  if (step.status === 'completed') {
    return `已完成 ${step.endDate ? formatDate(step.endDate) : ''}`
  } else if (step.status === 'in_progress') {
    return `进行中 ${step.startDate ? formatDate(step.startDate) : ''}`
  }
  return '待开始'
}

// 获取步骤图标颜色
const getStepIconColor = (stepKey: string, progress: Site['progress']) => {
  const step = progress[stepKey as keyof typeof progress]
  if (step.status === 'completed') {
    return '#52c41a'
  } else if (step.status === 'in_progress') {
    return '#1890ff'
  }
  return '#d9d9d9'
}

// 检查是否可以更新步骤
const canUpdateStep = (stepKey: string, progress: Site['progress']) => {
  const steps = Object.keys(progress)
  const stepIndex = steps.indexOf(stepKey)
  
  // 第一步总是可以开始
  if (stepIndex === 0) {
    return true
  }
  
  // 前一步必须完成
  const prevStepKey = steps[stepIndex - 1] as keyof typeof progress
  return progress[prevStepKey].status === 'completed'
}

// 查看进度
const viewProgress = (site: Site) => {
  currentSite.value = site
  showProgressModal.value = true
}

// 更新进度
const updateProgress = (stepKey: string) => {
  if (!currentSite.value) return
  
  const step = currentSite.value.progress[stepKey as keyof typeof currentSite.value.progress]
  
  if (step.status === 'pending') {
    step.status = 'in_progress'
    step.startDate = Date.now()
  } else if (step.status === 'in_progress') {
    step.status = 'completed'
    step.endDate = Date.now()
  }
  
  // 更新工地状态
  const allCompleted = Object.values(currentSite.value.progress).every(s => s.status === 'completed')
  if (allCompleted) {
    currentSite.value.status = 'completed'
  } else {
    currentSite.value.status = 'in_progress'
  }
  
  message.success(`${progressSteps.find(s => s.key === stepKey)?.label}状态已更新`)
}

// 处理编辑
const handleEdit = (row: Site) => {
  Object.assign(formData, {
    name: row.name,
    address: row.address,
    manager: row.manager,
    phone: row.phone,
    startDate: row.startDate,
    expectedEndDate: row.expectedEndDate
  })
  showAddModal.value = true
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    const newSite: Site = {
      id: Date.now(),
      name: formData.name,
      address: formData.address,
      manager: formData.manager,
      phone: formData.phone,
      startDate: formData.startDate!,
      expectedEndDate: formData.expectedEndDate!,
      status: 'planning',
      progress: {
        start: { status: 'pending' },
        electrical: { status: 'pending' },
        carpentry: { status: 'pending' },
        masonry: { status: 'pending' },
        putty: { status: 'pending' },
        furniture: { status: 'pending' },
        completion: { status: 'pending' }
      },
      createdAt: new Date().toLocaleDateString('zh-CN')
    }
    
    sites.value.unshift(newSite)
    message.success('工地添加成功')
    showAddModal.value = false
    
    // 重置表单
    Object.assign(formData, {
      name: '',
      address: '',
      manager: '',
      phone: '',
      startDate: null,
      expectedEndDate: null
    })
  } catch (error) {
    message.error('请检查表单信息')
  }
}
</script>

<style scoped>
.site-management {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>