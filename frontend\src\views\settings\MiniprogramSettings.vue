<template>
  <div class="miniprogram-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
          <h2>小程序导航管理</h2>
          <p class="page-description">配置和管理小程序相关设置</p>
        </div>
        <n-space>
          <n-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <n-icon><add-outline /></n-icon>
            </template>
            新增配置
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 配置选项卡 -->
    <n-tabs v-model:value="activeTab" type="line" animated>
      <!-- 基础配置 -->
      <n-tab-pane name="basic" tab="基础配置">
        <n-card title="小程序基础信息">
          <n-form
            ref="basicFormRef"
            :model="basicConfig"
            :rules="basicRules"
            label-placement="left"
            label-width="120px"
          >
            <n-grid :cols="2" :x-gap="24">
              <n-form-item-gi label="小程序名称" path="name">
                <n-input v-model:value="basicConfig.name" placeholder="请输入小程序名称" />
              </n-form-item-gi>
              <n-form-item-gi label="AppID" path="appId">
                <n-input v-model:value="basicConfig.appId" placeholder="请输入小程序AppID" />
              </n-form-item-gi>
            </n-grid>
            
            <n-grid :cols="2" :x-gap="24">
              <n-form-item-gi label="AppSecret" path="appSecret">
                <n-input
                  v-model:value="basicConfig.appSecret"
                  type="password"
                  placeholder="请输入小程序AppSecret"
                  show-password-on="click"
                />
              </n-form-item-gi>
              <n-form-item-gi label="服务器域名" path="serverDomain">
                <n-input v-model:value="basicConfig.serverDomain" placeholder="https://api.example.com" />
              </n-form-item-gi>
            </n-grid>
            
            <n-form-item label="小程序描述" path="description">
              <n-input
                v-model:value="basicConfig.description"
                type="textarea"
                placeholder="请输入小程序描述"
                :rows="3"
              />
            </n-form-item>
            
            <n-form-item label="小程序图标">
              <n-upload
                :file-list="basicConfig.iconFiles"
                list-type="image-card"
                :max="1"
                accept="image/*"
                @update:file-list="handleIconUpload"
              >
                点击上传图标
              </n-upload>
            </n-form-item>
            
            <n-form-item>
              <n-space>
                <n-button type="primary" @click="saveBasicConfig" :loading="saving">
                  保存配置
                </n-button>
                <n-button @click="resetBasicConfig">重置</n-button>
              </n-space>
            </n-form-item>
          </n-form>
        </n-card>
      </n-tab-pane>

      <!-- 导航配置 -->
      <n-tab-pane name="navigation" tab="导航配置">
        <n-card title="小程序导航菜单">
          <template #header-extra>
            <n-button type="primary" @click="addNavItem">
              <template #icon>
                <n-icon><add-outline /></n-icon>
              </template>
              添加导航
            </n-button>
          </template>
          
          <n-data-table
            :columns="navColumns"
            :data="navigationList"
            :loading="loading"
            :row-key="(row: any) => row.id"
          />
        </n-card>
      </n-tab-pane>

      <!-- 版本管理 -->
      <n-tab-pane name="version" tab="版本管理">
        <n-card title="版本历史">
          <template #header-extra>
            <n-space>
              <n-button type="primary" @click="showVersionModal = true">
                <template #icon>
                  <n-icon><cloud-upload-outline /></n-icon>
                </template>
                上传新版本
              </n-button>
              <n-button type="info" @click="checkUpdate">
                <template #icon>
                  <n-icon><refresh-outline /></n-icon>
                </template>
                检查更新
              </n-button>
            </n-space>
          </template>
          
          <n-data-table
            :columns="versionColumns"
            :data="versionList"
            :loading="loading"
            :row-key="(row: any) => row.id"
          />
        </n-card>
      </n-tab-pane>

      <!-- 发布管理 -->
      <n-tab-pane name="publish" tab="发布管理">
        <n-card title="发布配置">
          <n-form
            ref="publishFormRef"
            :model="publishConfig"
            label-placement="left"
            label-width="120px"
          >
            <n-grid :cols="2" :x-gap="24">
              <n-form-item-gi label="发布环境">
                <n-select
                  v-model:value="publishConfig.environment"
                  :options="environmentOptions"
                  placeholder="选择发布环境"
                />
              </n-form-item-gi>
              <n-form-item-gi label="自动发布">
                <n-switch v-model:value="publishConfig.autoPublish" />
              </n-form-item-gi>
            </n-grid>
            
            <n-form-item label="发布说明">
              <n-input
                v-model:value="publishConfig.releaseNotes"
                type="textarea"
                placeholder="请输入发布说明"
                :rows="4"
              />
            </n-form-item>
            
            <n-form-item>
              <n-space>
                <n-button type="primary" @click="publishVersion" :loading="publishing">
                  <template #icon>
                    <n-icon><rocket-outline /></n-icon>
                  </template>
                  发布版本
                </n-button>
                <n-button type="warning" @click="rollbackVersion">
                  <template #icon>
                    <n-icon><arrow-back-outline /></n-icon>
                  </template>
                  版本回滚
                </n-button>
              </n-space>
            </n-form-item>
          </n-form>
        </n-card>
        
        <!-- 发布历史 -->
        <n-card title="发布历史" style="margin-top: 16px">
          <n-timeline>
            <n-timeline-item
              v-for="record in publishHistory"
              :key="record.id"
              :type="getPublishType(record.status)"
              :title="record.version"
              :content="record.description"
              :time="record.publishTime"
            />
          </n-timeline>
        </n-card>
      </n-tab-pane>

      <!-- 统计分析 -->
      <n-tab-pane name="analytics" tab="统计分析">
        <n-grid :cols="2" :x-gap="16" :y-gap="16">
          <!-- 用户统计 -->
          <n-card-gi title="用户统计">
            <n-statistic label="总用户数" :value="analytics.totalUsers" />
            <n-divider />
            <n-statistic label="活跃用户" :value="analytics.activeUsers" />
            <n-divider />
            <n-statistic label="新增用户" :value="analytics.newUsers" />
          </n-card-gi>
          
          <!-- 访问统计 -->
          <n-card-gi title="访问统计">
            <n-statistic label="总访问量" :value="analytics.totalViews" />
            <n-divider />
            <n-statistic label="今日访问" :value="analytics.todayViews" />
            <n-divider />
            <n-statistic label="平均停留时间" :value="analytics.avgStayTime" suffix="分钟" />
          </n-card-gi>
          
          <!-- 功能使用统计 -->
          <n-card-gi title="功能使用统计" :span="2">
            <div ref="featureChartRef" style="height: 300px"></div>
          </n-card-gi>
        </n-grid>
      </n-tab-pane>
    </n-tabs>

    <!-- 导航配置弹窗 -->
    <n-modal v-model:show="showNavModal" preset="card" title="配置导航" style="width: 600px">
      <n-form
        ref="navFormRef"
        :model="navFormData"
        :rules="navRules"
        label-placement="top"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="导航名称" path="name">
            <n-input v-model:value="navFormData.name" placeholder="请输入导航名称" />
          </n-form-item-gi>
          <n-form-item-gi label="导航类型" path="type">
            <n-select
              v-model:value="navFormData.type"
              :options="navTypeOptions"
              placeholder="选择导航类型"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="导航路径" path="path">
          <n-input v-model:value="navFormData.path" placeholder="请输入导航路径" />
        </n-form-item>
        
        <n-form-item label="导航图标">
          <n-input v-model:value="navFormData.icon" placeholder="请输入图标名称" />
        </n-form-item>
        
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="排序">
            <n-input-number v-model:value="navFormData.sort" :min="0" />
          </n-form-item-gi>
          <n-form-item-gi label="状态">
            <n-switch v-model:value="navFormData.enabled" />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showNavModal = false">取消</n-button>
          <n-button type="primary" @click="saveNavItem" :loading="saving">
            {{ editingNavId ? '更新' : '创建' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 版本上传弹窗 -->
    <n-modal v-model:show="showVersionModal" preset="card" title="上传新版本" style="width: 600px">
      <n-form
        ref="versionFormRef"
        :model="versionFormData"
        :rules="versionRules"
        label-placement="top"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="版本号" path="version">
            <n-input v-model:value="versionFormData.version" placeholder="如: 1.0.0" />
          </n-form-item-gi>
          <n-form-item-gi label="版本类型" path="type">
            <n-select
              v-model:value="versionFormData.type"
              :options="versionTypeOptions"
              placeholder="选择版本类型"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="版本描述" path="description">
          <n-input
            v-model:value="versionFormData.description"
            type="textarea"
            placeholder="请输入版本描述"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="上传文件">
          <n-upload
            :file-list="versionFormData.files"
            accept=".zip,.tar.gz"
            :max="1"
            @update:file-list="handleVersionUpload"
          >
            <n-button>选择文件</n-button>
          </n-upload>
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showVersionModal = false">取消</n-button>
          <n-button type="primary" @click="uploadVersion" :loading="uploading">
            上传版本
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, nextTick } from 'vue'
import { NButton, NIcon, NTag, NSpace, useMessage } from 'naive-ui'
import {
  AddOutline,
  CloudUploadOutline,
  RefreshOutline,
  RocketOutline,
  ArrowBackOutline,
  CreateOutline,
  TrashOutline,
  EyeOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const message = useMessage()

// 响应式数据
const activeTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const publishing = ref(false)
const uploading = ref(false)

// 表单引用
const basicFormRef = ref()
const navFormRef = ref()
const versionFormRef = ref()
const publishFormRef = ref()

// 弹窗控制
const showCreateModal = ref(false)
const showNavModal = ref(false)
const showVersionModal = ref(false)
const editingNavId = ref(null)

// 基础配置
const basicConfig = reactive({
  name: '企业CRM小程序',
  appId: '',
  appSecret: '',
  serverDomain: '',
  description: '',
  iconFiles: [] as any[]
})

// 导航配置
const navFormData = reactive({
  name: '',
  type: '',
  path: '',
  icon: '',
  sort: 0,
  enabled: true
})

// 版本配置
const versionFormData = reactive({
  version: '',
  type: '',
  description: '',
  files: [] as any[]
})

// 发布配置
const publishConfig = reactive({
  environment: 'production',
  autoPublish: false,
  releaseNotes: ''
})

// 统计数据
const analytics = reactive({
  totalUsers: 1250,
  activeUsers: 890,
  newUsers: 45,
  totalViews: 15680,
  todayViews: 234,
  avgStayTime: 8.5
})

// 导航列表
const navigationList = ref([
  {
    id: 1,
    name: '首页',
    type: 'page',
    path: '/pages/index/index',
    icon: 'home',
    sort: 1,
    enabled: true,
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    name: '客户管理',
    type: 'page',
    path: '/pages/customer/index',
    icon: 'person',
    sort: 2,
    enabled: true,
    createTime: '2024-01-15 10:05:00'
  },
  {
    id: 3,
    name: '我的',
    type: 'page',
    path: '/pages/profile/index',
    icon: 'person-circle',
    sort: 3,
    enabled: true,
    createTime: '2024-01-15 10:10:00'
  }
])

// 版本列表
const versionList = ref([
  {
    id: 1,
    version: '1.2.0',
    type: 'major',
    description: '新增客户管理功能，优化用户体验',
    status: 'published',
    publishTime: '2024-01-20 14:30:00',
    downloadCount: 156
  },
  {
    id: 2,
    version: '1.1.5',
    type: 'patch',
    description: '修复已知问题，提升稳定性',
    status: 'published',
    publishTime: '2024-01-15 09:20:00',
    downloadCount: 89
  },
  {
    id: 3,
    version: '1.1.0',
    type: 'minor',
    description: '新增数据统计功能',
    status: 'archived',
    publishTime: '2024-01-10 16:45:00',
    downloadCount: 234
  }
])

// 发布历史
const publishHistory = ref([
  {
    id: 1,
    version: '1.2.0',
    description: '成功发布到生产环境',
    status: 'success',
    publishTime: '2024-01-20 14:30:00'
  },
  {
    id: 2,
    version: '1.1.5',
    description: '成功发布到生产环境',
    status: 'success',
    publishTime: '2024-01-15 09:20:00'
  },
  {
    id: 3,
    version: '1.1.0',
    description: '发布失败，版本回滚',
    status: 'failed',
    publishTime: '2024-01-10 16:45:00'
  }
])

// 选项数据
const environmentOptions = [
  { label: '开发环境', value: 'development' },
  { label: '测试环境', value: 'testing' },
  { label: '生产环境', value: 'production' }
]

const navTypeOptions = [
  { label: '页面', value: 'page' },
  { label: '外链', value: 'link' },
  { label: '功能', value: 'function' }
]

const versionTypeOptions = [
  { label: '主版本', value: 'major' },
  { label: '次版本', value: 'minor' },
  { label: '补丁版本', value: 'patch' }
]

// 表单验证规则
const basicRules = {
  name: [
    { required: true, message: '请输入小程序名称', trigger: 'blur' }
  ],
  appId: [
    { required: true, message: '请输入AppID', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入AppSecret', trigger: 'blur' }
  ]
}

const navRules = {
  name: [
    { required: true, message: '请输入导航名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择导航类型', trigger: 'change' }
  ],
  path: [
    { required: true, message: '请输入导航路径', trigger: 'blur' }
  ]
}

const versionRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择版本类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入版本描述', trigger: 'blur' }
  ]
}

// 表格列配置
const navColumns = [
  {
    title: '导航名称',
    key: 'name',
    width: 120
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    render(row: any) {
      const typeMap: Record<string, { type: string; text: string }> = {
        page: { type: 'info', text: '页面' },
        link: { type: 'warning', text: '外链' },
        function: { type: 'success', text: '功能' }
      }
      const config = typeMap[row.type] || { type: 'default', text: row.type }
      return h(NTag, { type: config.type as any }, { default: () => config.text })
    }
  },
  {
    title: '路径',
    key: 'path',
    ellipsis: true
  },
  {
    title: '图标',
    key: 'icon',
    width: 80
  },
  {
    title: '排序',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    key: 'enabled',
    width: 80,
    render(row: any) {
      return h(NTag, 
        { type: row.enabled ? 'success' : 'error' },
        { default: () => row.enabled ? '启用' : '禁用' }
      )
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row: any) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, 
            { 
              size: 'small', 
              type: 'primary',
              onClick: () => editNavItem(row)
            },
            { default: () => '编辑' }
          ),
          h(NButton, 
            { 
              size: 'small', 
              type: 'error',
              onClick: () => deleteNavItem(row.id)
            },
            { default: () => '删除' }
          )
        ]
      })
    }
  }
]

const versionColumns = [
  {
    title: '版本号',
    key: 'version',
    width: 100
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    render(row: any) {
      const typeMap: Record<string, { type: string; text: string }> = {
        major: { type: 'error', text: '主版本' },
        minor: { type: 'warning', text: '次版本' },
        patch: { type: 'info', text: '补丁' }
      }
      const config = typeMap[row.type] || { type: 'default', text: row.type }
      return h(NTag, { type: config.type as any }, { default: () => config.text })
    }
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: any) {
      const statusMap: Record<string, { type: string; text: string }> = {
        published: { type: 'success', text: '已发布' },
        draft: { type: 'warning', text: '草稿' },
        archived: { type: 'default', text: '已归档' }
      }
      const config = statusMap[row.status] || { type: 'default', text: row.status }
      return h(NTag, { type: config.type as any }, { default: () => config.text })
    }
  },
  {
    title: '发布时间',
    key: 'publishTime',
    width: 160
  },
  {
    title: '下载次数',
    key: 'downloadCount',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row: any) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, 
            { 
              size: 'small', 
              type: 'info',
              onClick: () => previewVersion(row)
            },
            { default: () => '预览' }
          ),
          h(NButton, 
            { 
              size: 'small', 
              type: 'primary',
              onClick: () => downloadVersion(row)
            },
            { default: () => '下载' }
          ),
          h(NButton, 
            { 
              size: 'small', 
              type: 'error',
              onClick: () => deleteVersion(row.id)
            },
            { default: () => '删除' }
          )
        ]
      })
    }
  }
]

// 图表引用
const featureChartRef = ref()

// 方法
const handleIconUpload = (fileList: any[]) => {
  basicConfig.iconFiles = fileList
}

const handleVersionUpload = (fileList: any[]) => {
  versionFormData.files = fileList
}

const saveBasicConfig = async () => {
  try {
    await basicFormRef.value?.validate()
    saving.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('基础配置保存成功')
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    saving.value = false
  }
}

const resetBasicConfig = () => {
  Object.assign(basicConfig, {
    name: '企业CRM小程序',
    appId: '',
    appSecret: '',
    serverDomain: '',
    description: '',
    iconFiles: []
  })
}

const addNavItem = () => {
  editingNavId.value = null
  Object.assign(navFormData, {
    name: '',
    type: '',
    path: '',
    icon: '',
    sort: 0,
    enabled: true
  })
  showNavModal.value = true
}

const editNavItem = (item: any) => {
  editingNavId.value = item.id
  Object.assign(navFormData, item)
  showNavModal.value = true
}

const saveNavItem = async () => {
  try {
    await navFormRef.value?.validate()
    saving.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingNavId.value) {
      // 更新
      const index = navigationList.value.findIndex(item => item.id === editingNavId.value)
      if (index !== -1) {
        navigationList.value[index] = { 
          ...navFormData, 
          id: editingNavId.value,
          createTime: navigationList.value[index].createTime
        }
      }
      message.success('导航更新成功')
    } else {
      // 新增
      const newItem = {
        ...navFormData,
        id: Date.now(),
        createTime: new Date().toLocaleString()
      }
      navigationList.value.push(newItem)
      message.success('导航创建成功')
    }
    
    showNavModal.value = false
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    saving.value = false
  }
}

const deleteNavItem = (id: number) => {
  const index = navigationList.value.findIndex(item => item.id === id)
  if (index !== -1) {
    navigationList.value.splice(index, 1)
    message.success('导航删除成功')
  }
}

const uploadVersion = async () => {
  try {
    await versionFormRef.value?.validate()
    uploading.value = true
    
    // 模拟上传
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newVersion = {
      ...versionFormData,
      id: Date.now(),
      status: 'draft',
      publishTime: new Date().toLocaleString(),
      downloadCount: 0
    }
    versionList.value.unshift(newVersion)
    
    message.success('版本上传成功')
    showVersionModal.value = false
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    uploading.value = false
  }
}

const publishVersion = async () => {
  publishing.value = true
  
  try {
    // 模拟发布
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newRecord = {
      id: Date.now(),
      version: '1.2.1',
      description: publishConfig.releaseNotes || '版本发布',
      status: 'success',
      publishTime: new Date().toLocaleString()
    }
    publishHistory.value.unshift(newRecord)
    
    message.success('版本发布成功')
  } catch (error) {
    message.error('发布失败，请重试')
  } finally {
    publishing.value = false
  }
}

const rollbackVersion = () => {
  message.info('版本回滚功能开发中')
}

const checkUpdate = () => {
  message.info('正在检查更新...')
}

const previewVersion = (version: any) => {
  message.info(`预览版本 ${version.version}`)
}

const downloadVersion = (version: any) => {
  message.success(`开始下载版本 ${version.version}`)
}

const deleteVersion = (id: number) => {
  const index = versionList.value.findIndex(item => item.id === id)
  if (index !== -1) {
    versionList.value.splice(index, 1)
    message.success('版本删除成功')
  }
}

const getPublishType = (status: string) => {
  const typeMap: Record<string, string> = {
    success: 'success',
    failed: 'error',
    pending: 'warning'
  }
  return typeMap[status] || 'info'
}

// 初始化图表
const initFeatureChart = () => {
  if (!featureChartRef.value) return
  
  const chart = echarts.init(featureChartRef.value)
  const option = {
    title: {
      text: '功能使用统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '使用次数',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '客户管理' },
          { value: 735, name: '跟进记录' },
          { value: 580, name: '数据统计' },
          { value: 484, name: '营销活动' },
          { value: 300, name: '系统设置' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initFeatureChart()
  })
})
</script>

<style scoped>
.miniprogram-settings {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.total-count {
  color: #666;
  font-size: 14px;
}

:deep(.n-card) {
  margin-bottom: 16px;
}

:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-statistic) {
  text-align: center;
}

:deep(.n-timeline-item) {
  margin-bottom: 16px;
}
</style>