-- 插入客户测试数据
INSERT INTO customers (name, gender, phone, email, company, position, source, level, region, address, tags, status, notes) VALUES
('张三', 'male', '13800138001', 'z<PERSON><PERSON>@example.com', '阿里巴巴', '产品经理', 'website', 'high', '北京市', '北京市朝阳区', '互联网,技术', 'active', '重要客户，需要重点跟进'),
('李四', 'female', '13800138002', '<EMAIL>', '腾讯科技', '技术总监', 'referral', 'medium', '深圳市', '深圳市南山区', '技术,管理', 'active', '技术背景强，对产品很感兴趣'),
('王五', 'male', '13800138003', '<EMAIL>', '百度', '市场总监', 'social_media', 'high', '北京市', '北京市海淀区', '市场,推广', 'active', '市场资源丰富'),
('赵六', 'female', '13800138004', 'zhaoli<PERSON>@example.com', '字节跳动', '运营经理', 'advertisement', 'medium', '北京市', '北京市朝阳区', '运营,数据', 'active', '数据分析能力强'),
('钱七', 'male', '13800138005', '<EMAIL>', '美团', '业务总监', 'website', 'low', '北京市', '北京市朝阳区', '业务,管理', 'potential', '初步接触，需要进一步了解需求');

-- 更新序列值
SELECT setval('customers_id_seq', (SELECT MAX(id) FROM customers));

-- 更新客户的时间信息
UPDATE customers SET last_contact_at = NOW() - INTERVAL '1 day' WHERE phone IN ('13800138001', '13800138002');
UPDATE customers SET next_follow_up_at = NOW() + INTERVAL '3 days' WHERE phone IN ('13800138001', '13800138003', '13800138004');