<template>
  <div class="wechat-customer-list">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
  
        <p>管理微信渠道获取的客户信息，跟踪客户行为和转化情况</p>
      </div>
      <div class="header-actions">
        <n-button type="primary" @click="syncCustomers" :loading="syncing">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          同步客户
        </n-button>
        <n-button type="info" @click="exportData">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出数据
        </n-button>
        <n-button @click="showAnalysis = true">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          客户分析
        </n-button>
        <n-button @click="showTrackingConfig = true">
          <template #icon>
            <n-icon><SettingsOutline /></n-icon>
          </template>
          跟踪配置
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <n-icon size="32" color="#2080f0"><PeopleOutline /></n-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.totalCustomers) }}</div>
          <div class="stat-label">总客户数</div>
          <div class="stat-change positive">+{{ stats.customerGrowth }}%</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <n-icon size="32" color="#18a058"><CheckmarkCircleOutline /></n-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.activeCustomers) }}</div>
          <div class="stat-label">活跃客户</div>
          <div class="stat-change positive">+{{ stats.activeGrowth }}%</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <n-icon size="32" color="#f0a020"><TrendingUpOutline /></n-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.conversionRate }}%</div>
          <div class="stat-label">转化率</div>
          <div class="stat-change positive">+{{ stats.conversionGrowth }}%</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <n-icon size="32" color="#d03050"><EyeOutline /></n-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.todayViews) }}</div>
          <div class="stat-label">今日浏览</div>
          <div class="stat-change positive">+{{ stats.viewGrowth }}%</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <div class="filter-form">
        <div class="filter-row">
          <div class="filter-item">
            <label>关键词搜索</label>
            <n-input
              v-model:value="filters.keyword"
              placeholder="搜索客户昵称、微信号、手机号"
              clearable
            >
              <template #prefix>
                <n-icon><SearchOutline /></n-icon>
              </template>
            </n-input>
          </div>
          <div class="filter-item">
            <label>客户状态</label>
            <n-select
              v-model:value="filters.status"
              :options="statusOptions"
              placeholder="选择客户状态"
              clearable
            />
          </div>
          <div class="filter-item">
            <label>客户来源</label>
            <n-select
              v-model:value="filters.source"
              :options="sourceOptions"
              placeholder="选择客户来源"
              clearable
            />
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-item">
            <label>注册时间</label>
            <n-date-picker
              v-model:value="filters.dateRange"
              type="daterange"
              clearable
            />
          </div>
          <div class="filter-item">
            <label>最后活跃</label>
            <n-select
              v-model:value="filters.lastActive"
              :options="activeOptions"
              placeholder="选择活跃时间"
              clearable
            />
          </div>
          <div class="filter-item">
            <label>标签筛选</label>
            <n-select
              v-model:value="filters.tags"
              :options="tagOptions"
              placeholder="选择客户标签"
              multiple
              clearable
            />
          </div>
        </div>
        <div class="filter-actions">
          <n-button type="primary" @click="searchCustomers" :loading="loading">
            <template #icon>
              <n-icon><SearchOutline /></n-icon>
            </template>
            搜索
          </n-button>
          <n-button type="default" @click="resetFilters">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            重置
          </n-button>
        </div>
      </div>
    </n-card>

    <!-- 客户列表 -->
    <n-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>客户列表 ({{ pagination.itemCount }})</span>
          <div class="table-actions">
            <n-button size="small" @click="selectAll">
              {{ selectedRows.length === customers.length ? '取消全选' : '全选' }}
            </n-button>
            <n-button size="small" type="primary" :disabled="!selectedRows.length" @click="batchOperation">
              批量操作 ({{ selectedRows.length }})
            </n-button>
          </div>
        </div>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="customers"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        v-model:checked-row-keys="selectedRows"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 客户详情抽屉 -->
    <n-drawer
      v-model:show="showDetail"
      :width="800"
      placement="right"
    >
      <n-drawer-content title="客户详情">
        <CustomerDetailPanel
          v-if="selectedCustomer"
          :customer="selectedCustomer"
          @update="refreshCustomers"
          @close="showDetail = false"
        />
      </n-drawer-content>
    </n-drawer>

    <!-- 客户分析模态框 -->
    <n-modal
      v-model:show="showAnalysis"
      preset="card"
      title="客户分析"
      style="width: 90%; max-width: 1200px;"
    >
      <CustomerAnalysis @close="showAnalysis = false" />
    </n-modal>

    <!-- 跟踪配置模态框 -->
    <n-modal
      v-model:show="showTrackingConfig"
      preset="card"
      title="跟踪配置"
      style="width: 600px;"
    >
      <TrackingConfig @close="showTrackingConfig = false" />
    </n-modal>

    <!-- 同步客户模态框 -->
    <n-modal
      v-model:show="showSyncModal"
      preset="card"
      title="同步微信客户"
      style="width: 500px;"
    >
      <div class="sync-form">
        <div class="form-item">
          <label>同步范围</label>
          <n-radio-group v-model:value="syncConfig.scope">
            <n-radio value="all">全部客户</n-radio>
            <n-radio value="recent">最近新增</n-radio>
            <n-radio value="active">活跃客户</n-radio>
          </n-radio-group>
        </div>
        <div class="form-item">
          <label>同步内容</label>
          <n-checkbox-group v-model:value="syncConfig.content">
            <n-checkbox value="basic">基本信息</n-checkbox>
            <n-checkbox value="behavior">行为数据</n-checkbox>
            <n-checkbox value="interaction">互动记录</n-checkbox>
            <n-checkbox value="tags">标签信息</n-checkbox>
          </n-checkbox-group>
        </div>
        <div class="form-item">
          <label>同步频率</label>
          <n-select
            v-model:value="syncConfig.frequency"
            :options="frequencyOptions"
            placeholder="选择同步频率"
          />
        </div>
        <div class="form-actions">
          <n-button type="primary" @click="executSync" :loading="syncing">
            开始同步
          </n-button>
          <n-button @click="showSyncModal = false">取消</n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw } from 'vue'
import {
  NCard, NButton, NIcon, NInput, NSelect, NDatePicker, NDataTable,
  NDrawer, NDrawerContent, NModal, NRadioGroup, NRadio,
  NCheckboxGroup, NCheckbox, NTag, NAvatar, useMessage
} from 'naive-ui'
import {
  RefreshOutline, DownloadOutline, AnalyticsOutline, SettingsOutline,
  PeopleOutline, CheckmarkCircleOutline, TrendingUpOutline, EyeOutline,
  SearchOutline, PhonePortraitOutline, ChatbubbleOutline, TimeOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import CustomerDetailPanel from './components/CustomerDetailPanel.vue'
import CustomerAnalysis from './components/CustomerAnalysis.vue'
import TrackingConfig from './components/TrackingConfig.vue'

interface Customer {
  id: string
  wechatId: string
  nickname: string
  avatar: string
  phone?: string

  status: string
  source: string
  tags: string[]
  registerTime: string
  lastActiveTime: string
  totalViews: number
  todayViews: number
  conversionStatus: string
  customerValue: number
}

interface Filters {
  keyword: string
  status: string
  source: string
  dateRange: [number, number] | null
  lastActive: string
  tags: string[]
}

const message = useMessage()
const wechatStore = useWechatStore()
const loading = ref(false)
const syncing = ref(false)
const showDetail = ref(false)
const showAnalysis = ref(false)
const showTrackingConfig = ref(false)
const showSyncModal = ref(false)
const selectedCustomer = ref<Customer | null>(null)
const selectedRows = ref<string[]>([])

// 筛选条件
const filters = reactive<Filters>({
  keyword: '',
  status: '',
  source: '',
  dateRange: null,
  lastActive: '',
  tags: []
})

// 同步配置
const syncConfig = reactive({
  scope: 'recent',
  content: ['basic', 'behavior'],
  frequency: 'daily'
})

// 统计数据
const stats = ref({
  totalCustomers: 15847,
  customerGrowth: 12.5,
  activeCustomers: 8923,
  activeGrowth: 8.7,
  conversionRate: 15.8,
  conversionGrowth: 3.2,
  todayViews: 2847,
  viewGrowth: 18.9
})

// 客户数据
const customers = ref<Customer[]>([
  {
    id: '1',
    wechatId: 'wx_user_001',
    nickname: '张小明',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20person%20avatar&image_size=square',
    phone: '13800138001',

    status: 'active',
    source: 'qr_code',
    tags: ['潜在客户', 'VIP'],
    registerTime: '2024-01-15 10:30:00',
    lastActiveTime: '2024-01-20 15:45:00',
    totalViews: 156,
    todayViews: 12,
    conversionStatus: 'converted',
    customerValue: 25000
  },
  {
    id: '2',
    wechatId: 'wx_user_002',
    nickname: '李美丽',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20person%20avatar&image_size=square',
    phone: '13800138002',
    status: 'potential',
    source: 'share_link',
    tags: ['新客户'],
    registerTime: '2024-01-18 14:20:00',
    lastActiveTime: '2024-01-19 09:15:00',
    totalViews: 89,
    todayViews: 8,
    conversionStatus: 'following',
    customerValue: 0
  },
  {
    id: '3',
    wechatId: 'wx_user_003',
    nickname: '王大力',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20person%20avatar&image_size=square',
    phone: '13800138003',

    status: 'inactive',
    source: 'friend_invite',
    tags: ['老客户', '高价值'],
    registerTime: '2023-12-10 16:45:00',
    lastActiveTime: '2024-01-10 11:30:00',
    totalViews: 234,
    todayViews: 0,
    conversionStatus: 'converted',
    customerValue: 58000
  }
])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 15847,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 选项数据
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '潜在', value: 'potential' },
  { label: '非活跃', value: 'inactive' },
  { label: '已流失', value: 'lost' }
]

const sourceOptions = [
  { label: '二维码', value: 'qr_code' },
  { label: '分享链接', value: 'share_link' },
  { label: '好友邀请', value: 'friend_invite' },
  { label: '群聊', value: 'group_chat' },
  { label: '搜索添加', value: 'search' }
]

const activeOptions = [
  { label: '今天', value: 'today' },
  { label: '最近3天', value: '3days' },
  { label: '最近7天', value: '7days' },
  { label: '最近30天', value: '30days' },
  { label: '30天以上', value: 'over30days' }
]

const tagOptions = [
  { label: '潜在客户', value: '潜在客户' },
  { label: 'VIP', value: 'VIP' },
  { label: '新客户', value: '新客户' },
  { label: '老客户', value: '老客户' },
  { label: '高价值', value: '高价值' },
  { label: '重点关注', value: '重点关注' }
]

const frequencyOptions = [
  { label: '实时同步', value: 'realtime' },
  { label: '每小时', value: 'hourly' },
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' }
]

// 表格列配置
const columns = [
  {
    type: 'selection' as const
  },
  {
    title: '客户信息',
    key: 'customer',
    width: 200,
    render: (row: Customer) => {
      return h('div', { class: 'customer-info' }, [
        h(NAvatar, {
          size: 40,
          src: row.avatar,
          fallbackSrc: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=default%20avatar&image_size=square'
        }),
        h('div', { class: 'customer-details' }, [
          h('div', { class: 'customer-name' }, row.nickname),
          h('div', { class: 'customer-wechat' }, row.wechatId)
        ])
      ])
    }
  },
  {
    title: '联系方式',
    key: 'contact',
    width: 150,
    render: (row: Customer) => {
      return h('div', { class: 'contact-info' }, [
        row.phone ? h('div', { class: 'contact-item' }, [
          h(NIcon, { size: 14 }, { default: () => h(markRaw(PhonePortraitOutline)) }),
          h('span', row.phone)
        ]) : null,

      ])
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: Customer) => {
      const statusMap = {
        active: { type: 'success', text: '活跃' },
        potential: { type: 'warning', text: '潜在' },
        inactive: { type: 'default', text: '非活跃' },
        lost: { type: 'error', text: '已流失' }
      }
      const status = statusMap[row.status as keyof typeof statusMap]
      return h(NTag, { type: status.type as 'success' | 'warning' | 'default' | 'error' }, { default: () => status.text })
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 120,
    render: (row: Customer) => {
      const sourceMap = {
        qr_code: '二维码',
        share_link: '分享链接',
        friend_invite: '好友邀请',
        group_chat: '群聊',
        search: '搜索添加'
      }
      return sourceMap[row.source as keyof typeof sourceMap] || row.source
    }
  },
  {
    title: '标签',
    key: 'tags',
    width: 150,
    render: (row: Customer) => {
      return h('div', { class: 'tags-container' }, 
        row.tags.map(tag => 
          h(NTag, { size: 'small', style: 'margin-right: 4px; margin-bottom: 2px;' }, 
            { default: () => tag }
          )
        )
      )
    }
  },
  {
    title: '浏览数据',
    key: 'views',
    width: 120,
    render: (row: Customer) => {
      return h('div', { class: 'views-data' }, [
        h('div', `总计：${row.totalViews}`),
        h('div', { class: 'today-views' }, `今日：${row.todayViews}`)
      ])
    }
  },
  {
    title: '转化状态',
    key: 'conversionStatus',
    width: 100,
    render: (row: Customer) => {
      const conversionMap = {
        converted: { type: 'success', text: '已转化' },
        following: { type: 'warning', text: '跟进中' },
        pending: { type: 'info', text: '待跟进' },
        failed: { type: 'error', text: '转化失败' }
      }
      const conversion = conversionMap[row.conversionStatus as keyof typeof conversionMap]
      return h(NTag, { type: conversion.type as 'success' | 'warning' | 'info' | 'error' }, { default: () => conversion.text })
    }
  },
  {
    title: '客户价值',
    key: 'customerValue',
    width: 100,
    render: (row: Customer) => {
      return row.customerValue > 0 ? `¥${formatNumber(row.customerValue)}` : '-'
    }
  },
  {
    title: '最后活跃',
    key: 'lastActiveTime',
    width: 150,
    render: (row: Customer) => {
      return h('div', { class: 'time-info' }, [
        h(NIcon, { size: 14 }, { default: () => h(markRaw(TimeOutline)) }),
        h('span', row.lastActiveTime)
      ])
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: Customer) => {
      return h('div', { class: 'action-buttons' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => viewCustomer(row)
        }, { default: () => '查看' }),
        h(NButton, {
          size: 'small',
          style: 'margin-left: 8px;',
          onClick: () => editCustomer(row)
        }, { default: () => '编辑' })
      ])
    }
  }
]

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const searchCustomers = async () => {
  loading.value = true
  try {
    // 模拟搜索
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('搜索完成')
  } catch (error) {
    message.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  Object.assign(filters, {
    keyword: '',
    status: '',
    source: '',
    dateRange: null,
    lastActive: '',
    tags: []
  })
  searchCustomers()
}

const syncCustomers = () => {
  showSyncModal.value = true
}

const executSync = async () => {
  syncing.value = true
  try {
    // 模拟同步
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('客户同步完成')
    showSyncModal.value = false
    refreshCustomers()
  } catch (error) {
    message.error('同步失败，请重试')
  } finally {
    syncing.value = false
  }
}

const exportData = () => {
  message.info('数据导出功能开发中')
}

const selectAll = () => {
  if (selectedRows.value.length === customers.value.length) {
    selectedRows.value = []
  } else {
    selectedRows.value = customers.value.map(item => item.id)
  }
}

const batchOperation = () => {
  message.info('批量操作功能开发中')
}

const viewCustomer = (customer: Customer) => {
  selectedCustomer.value = customer
  showDetail.value = true
}

const editCustomer = (customer: Customer) => {
  message.info('编辑客户功能开发中')
}

const refreshCustomers = () => {
  searchCustomers()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  searchCustomers()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  searchCustomers()
}

onMounted(() => {
  searchCustomers()
})
</script>

<style scoped>
.wechat-customer-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  align-items: center;
}

.stat-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(32, 128, 240, 0.1);
  border-radius: 12px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #18a058;
}

.stat-change.negative {
  color: #d03050;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.table-card {
  flex: 1;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.customer-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-name {
  font-weight: 500;
  color: #1a1a1a;
}

.customer-wechat {
  font-size: 12px;
  color: #666;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.views-data {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.today-views {
  color: #2080f0;
  font-weight: 500;
}

.time-info {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.sync-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>