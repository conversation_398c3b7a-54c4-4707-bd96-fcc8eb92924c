<template>
  <div class="customer-source-chart">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface SourceData {
  name: string
  value: number
}

interface Props {
  data: SourceData[]
}

const props = defineProps<Props>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 颜色配置
const colors = [
  '#667eea',
  '#764ba2', 
  '#f093fb',
  '#f5576c',
  '#4facfe',
  '#00f2fe'
]

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: 600;">${params.name}</div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="display: inline-block; width: 8px; height: 8px; background: ${params.color}; border-radius: 50%;"></span>
              <span>数量: ${params.value}</span>
            </div>
            <div style="margin-top: 4px; color: #ccc;">占比: ${percent}%</div>
          </div>
        `
      }
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 16,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = props.data.find(d => d.name === name)
        const percent = item ? ((item.value / total) * 100).toFixed(1) : '0'
        return `${name} ${percent}%`
      }
    },
    series: [
      {
        name: '客户来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: '#333',
            formatter: (params: any) => {
              const percent = ((params.value / total) * 100).toFixed(1)
              return `${params.name}\n${percent}%`
            }
          }
        },
        data: props.data.map((item, index) => ({
          ...item,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: colors[index % colors.length] },
                { offset: 1, color: adjustBrightness(colors[index % colors.length], -20) }
              ]
            }
          }
        }))
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as const
  }
  
  chartInstance.setOption(option, true)
}

// 调整颜色亮度
const adjustBrightness = (color: string, amount: number): string => {
  const usePound = color[0] === '#'
  const col = usePound ? color.slice(1) : color
  const num = parseInt(col, 16)
  let r = (num >> 16) + amount
  let g = (num >> 8 & 0x00FF) + amount
  let b = (num & 0x0000FF) + amount
  r = r > 255 ? 255 : r < 0 ? 0 : r
  g = g > 255 ? 255 : g < 0 ? 0 : g
  b = b > 255 ? 255 : b < 0 ? 0 : b
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
}

// 暴露清理方法
defineExpose({
  cleanup
})
</script>

<style scoped>
.customer-source-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>