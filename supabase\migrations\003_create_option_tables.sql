-- 创建选项数据管理表结构
-- 选项分类表
CREATE TABLE option_categories (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 选项数据表
CREATE TABLE option_items (
  id SERIAL PRIMARY KEY,
  category_id INTEGER NOT NULL REFERENCES option_categories(id) ON DELETE CASCADE,
  code VARCHAR(50) NOT NULL,
  label VARCHAR(100) NOT NULL,
  value VARCHAR(100) NOT NULL,
  color VARCHAR(20),
  icon VARCHAR(50),
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  extra_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category_id, code)
);

-- 创建索引
CREATE INDEX idx_option_categories_code ON option_categories(code);
CREATE INDEX idx_option_categories_active ON option_categories(is_active);
CREATE INDEX idx_option_items_category_id ON option_items(category_id);
CREATE INDEX idx_option_items_code ON option_items(code);
CREATE INDEX idx_option_items_active ON option_items(is_active);
CREATE INDEX idx_option_items_sort_order ON option_items(sort_order);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
CREATE TRIGGER update_option_categories_updated_at
  BEFORE UPDATE ON option_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_option_items_updated_at
  BEFORE UPDATE ON option_items
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略 (RLS)
ALTER TABLE option_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE option_items ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 选项分类表策略
CREATE POLICY "Allow read access to option_categories" ON option_categories
  FOR SELECT USING (true);

CREATE POLICY "Allow insert for authenticated users" ON option_categories
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update for authenticated users" ON option_categories
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete for authenticated users" ON option_categories
  FOR DELETE USING (auth.role() = 'authenticated');

-- 选项数据表策略
CREATE POLICY "Allow read access to option_items" ON option_items
  FOR SELECT USING (true);

CREATE POLICY "Allow insert for authenticated users" ON option_items
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update for authenticated users" ON option_items
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete for authenticated users" ON option_items
  FOR DELETE USING (auth.role() = 'authenticated');

-- 授予权限
GRANT SELECT ON option_categories TO anon;
GRANT ALL PRIVILEGES ON option_categories TO authenticated;
GRANT SELECT ON option_items TO anon;
GRANT ALL PRIVILEGES ON option_items TO authenticated;

-- 授予序列权限
GRANT USAGE, SELECT ON SEQUENCE option_categories_id_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE option_items_id_seq TO authenticated;

-- 插入初始选项分类数据
INSERT INTO option_categories (code, name, description, sort_order) VALUES
('customer_source', '客户来源', '客户获取渠道分类', 1),
('customer_level', '客户等级', '客户价值等级分类', 2),
('customer_status', '客户状态', '客户当前状态分类', 3),
('gender', '性别', '性别选项', 4),
('decoration_type', '装修类型', '装修风格类型', 5),
('house_status', '房屋状态', '房屋当前状态', 6),
('contract_package', '合同套餐', '合同套餐类型', 7),
('business_line', '业务线', '企业业务线分类', 8),
('value_dimension', '价值维度', '客户价值分析维度', 9),
('value_group', '价值分组', '客户价值分组方式', 10),
('value_method', '价值计算方法', '客户价值计算方法', 11);