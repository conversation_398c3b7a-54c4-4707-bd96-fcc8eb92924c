-- 创建客户表
CREATE TABLE IF NOT EXISTS customers (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    gender VARCHAR(10) DEFAULT 'unknown',
    phone VARCHAR(20),
    email VARCHAR(100),
    company VARCHAR(200),
    position VARCHAR(100),
    source VARCHAR(50) DEFAULT 'unknown',
    level VARCHAR(20) DEFAULT 'potential',
    region VARCHAR(100),
    address TEXT,
    tags TEXT,
    assigned_to UUID REFERENCES auth.users(id),
    status VARCHAR(20) DEFAULT 'active',
    notes TEXT,
    last_contact_at TIMESTAMPTZ,
    next_follow_up_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建客户跟进记录表
CREATE TABLE IF NOT EXISTS customer_follow_records (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    next_follow_up_at TIMESTAMPTZ,
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_assigned_to ON customers(assigned_to);
CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status);
CREATE INDEX IF NOT EXISTS idx_customers_source ON customers(source);
CREATE INDEX IF NOT EXISTS idx_customers_level ON customers(level);
CREATE INDEX IF NOT EXISTS idx_customers_created_at ON customers(created_at);
CREATE INDEX IF NOT EXISTS idx_customer_follow_records_customer_id ON customer_follow_records(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_follow_records_user_id ON customer_follow_records(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_follow_records_created_at ON customer_follow_records(created_at);

-- 启用RLS
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_follow_records ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view all customers" ON customers
    FOR SELECT USING (true);

CREATE POLICY "Users can insert customers" ON customers
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update customers" ON customers
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete customers" ON customers
    FOR DELETE USING (true);

CREATE POLICY "Users can view all follow records" ON customer_follow_records
    FOR SELECT USING (true);

CREATE POLICY "Users can insert follow records" ON customer_follow_records
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update follow records" ON customer_follow_records
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete follow records" ON customer_follow_records
    FOR DELETE USING (true);

-- 授予权限
GRANT ALL PRIVILEGES ON customers TO authenticated;
GRANT ALL PRIVILEGES ON customer_follow_records TO authenticated;
GRANT SELECT ON customers TO anon;
GRANT SELECT ON customer_follow_records TO anon;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为customers表创建更新时间触发器
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();