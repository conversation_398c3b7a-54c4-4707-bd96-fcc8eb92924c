{"migrationId": "62305179-2cee-4121-ae70-868d443ab64a", "timestamp": "2025-08-18T06:58:30.945Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 15, "failedTables": 6, "totalRecords": 165, "migratedRecords": 112, "failedRecords": 53}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T06:57:48.244Z", "errors": [], "endTime": "2025-08-18T06:57:51.782Z", "duration": 3538}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:57:51.787Z", "errors": [], "endTime": "2025-08-18T06:57:54.357Z", "duration": 2570}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T06:57:54.358Z", "errors": [], "endTime": "2025-08-18T06:57:56.301Z", "duration": 1943}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:57:56.304Z", "errors": [], "endTime": "2025-08-18T06:57:58.369Z", "duration": 2065}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:57:58.372Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:57:59.058Z", "errors": [], "endTime": "2025-08-18T06:58:01.851Z", "duration": 2793}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:58:01.854Z", "errors": ["Field 'name' doesn't have a default value"], "endTime": "2025-08-18T06:58:04.272Z", "duration": 2418}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T06:58:04.275Z", "errors": [], "endTime": "2025-08-18T06:58:06.081Z", "duration": 1806}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:06.083Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:58:06.367Z", "errors": ["Unknown column 'participant_count' in 'field list'"], "endTime": "2025-08-18T06:58:10.581Z", "duration": 4214}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:10.584Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:11.071Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:58:13.199Z", "errors": ["Unknown column 'notes' in 'field list'"], "endTime": "2025-08-18T06:58:15.784Z", "duration": 2585}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:15.787Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:58:16.270Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T06:58:18.728Z", "duration": 2458}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:18.731Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:19.226Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:19.890Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:58:21.367Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:58:23.296Z", "errors": ["Unknown column 'attachments' in 'field list'"], "endTime": "2025-08-18T06:58:28.463Z", "duration": 5167}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:58:28.465Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T06:58:30.941Z", "duration": 2476}], "logs": [{"id": "844e07bf-b296-4289-8f3d-9cdaae5d9b8a", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T06:57:48.244Z", "end_time": "2025-08-18T06:57:51.782Z", "duration_ms": 3538}, {"id": "6ffe74d7-ce72-4933-98d8-ec6b29893c2c", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:57:51.787Z", "end_time": "2025-08-18T06:57:54.357Z", "duration_ms": 2570}, {"id": "bd33dbd2-d16c-46aa-8c7d-2f916c129f17", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T06:57:54.358Z", "end_time": "2025-08-18T06:57:56.301Z", "duration_ms": 1943}, {"id": "77cf4cef-c147-4db7-91d1-8df248374160", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:57:56.304Z", "end_time": "2025-08-18T06:57:58.369Z", "duration_ms": 2065}, {"id": "d9d97af5-14bd-4a00-af7b-15ec766121e4", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:57:58.372Z", "end_time": "2025-08-18T06:57:59.058Z", "duration_ms": 686}, {"id": "dcb3f48f-6220-4235-a501-0e2d441639da", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:57:59.058Z", "end_time": "2025-08-18T06:58:01.851Z", "duration_ms": 2793}, {"id": "5a27ce18-38b7-48ae-871b-a0ff96e73a52", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:01.854Z", "end_time": "2025-08-18T06:58:04.272Z", "duration_ms": 2418}, {"id": "9017fa3d-7f8d-4f90-9c46-cb7bfb1403e8", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T06:58:04.275Z", "end_time": "2025-08-18T06:58:06.081Z", "duration_ms": 1806}, {"id": "24d0b0a3-4bf4-4edc-8e52-cd1f65ccdf6b", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:06.083Z", "end_time": "2025-08-18T06:58:06.367Z", "duration_ms": 284}, {"id": "30413321-9fe1-46b1-a822-dd8d49e8aca3", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:06.367Z", "end_time": "2025-08-18T06:58:10.581Z", "duration_ms": 4214}, {"id": "595f46e2-e543-4651-bb87-e432a942cc4e", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:10.584Z", "end_time": "2025-08-18T06:58:11.071Z", "duration_ms": 487}, {"id": "2c56b485-0116-44de-9f2d-f03cd81c5f0a", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:11.071Z", "end_time": "2025-08-18T06:58:13.199Z", "duration_ms": 2128}, {"id": "b04a9266-9950-4bad-b0de-54d20692ac4d", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:13.199Z", "end_time": "2025-08-18T06:58:15.784Z", "duration_ms": 2585}, {"id": "aca89e6b-a111-4c9b-a8e7-6eb6dbd4710d", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:15.787Z", "end_time": "2025-08-18T06:58:16.270Z", "duration_ms": 483}, {"id": "b0b08f07-1d0f-49c0-b80d-d593c8cb3885", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:16.270Z", "end_time": "2025-08-18T06:58:18.728Z", "duration_ms": 2458}, {"id": "0aef17ad-621d-47de-887d-8d9d5be61d3c", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:18.731Z", "end_time": "2025-08-18T06:58:19.226Z", "duration_ms": 495}, {"id": "59ccb735-2fac-4611-95f1-b1aca96c2972", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:19.226Z", "end_time": "2025-08-18T06:58:19.890Z", "duration_ms": 664}, {"id": "ec5cbad3-c8b0-44d0-8db5-4bfc10fdb936", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:19.890Z", "end_time": "2025-08-18T06:58:21.367Z", "duration_ms": 1477}, {"id": "6d5e6f14-86b5-4c88-bcf2-f7445c960aaf", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:21.367Z", "end_time": "2025-08-18T06:58:23.296Z", "duration_ms": 1929}, {"id": "01540121-4297-44df-9167-50e12eb5de1c", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:23.296Z", "end_time": "2025-08-18T06:58:28.463Z", "duration_ms": 5167}, {"id": "30fcd0ee-2b3d-45e3-9629-e53dcefa6cc3", "migration_id": "62305179-2cee-4121-ae70-868d443ab64a", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:58:28.465Z", "end_time": "2025-08-18T06:58:30.941Z", "duration_ms": 2476}]}