const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取跟进记录列表
router.get('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { 
      page = 1, 
      pageSize = 10, 
      customerId, 
      type, 
      stage,
      result,
      userId,
      startDate,
      endDate
    } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 客户筛选
    if (customerId) {
      whereClause += ' AND fr.customer_id = ?';
      whereParams.push(customerId);
    }

    // 跟进类型筛选
    if (type) {
      whereClause += ' AND fr.type = ?';
      whereParams.push(type);
    }

    // 跟进阶段筛选
    if (stage) {
      whereClause += ' AND fr.stage = ?';
      whereParams.push(stage);
    }

    // 跟进结果筛选
    if (result) {
      whereClause += ' AND fr.result = ?';
      whereParams.push(result);
    }

    // 跟进人筛选
    if (userId) {
      whereClause += ' AND fr.user_id = ?';
      whereParams.push(userId);
    }

    // 日期范围筛选
    if (startDate) {
      whereClause += ' AND fr.created_at >= ?';
      whereParams.push(startDate);
    }
    if (endDate) {
      whereClause += ' AND fr.created_at <= ?';
      whereParams.push(endDate + ' 23:59:59');
    }

    // 权限控制：普通销售只能看到自己的跟进记录（临时禁用）
    // if (req.user && req.user.role === 'sales') {
    //   whereClause += ' AND fr.user_id = ?';
    //   whereParams.push(req.user.userId);
    // }

    const sql = `
      SELECT 
        fr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.wechat as customer_wechat,
        c.company as customer_company,
        u.name as user_name,
        u.position as user_position
      FROM follow_records fr
      LEFT JOIN customers c ON fr.customer_id = c.id
      LEFT JOIN users u ON fr.user_id = u.id
      WHERE ${whereClause}
      ORDER BY fr.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countSql = `
      SELECT COUNT(*) as total
      FROM follow_records fr
      LEFT JOIN customers c ON fr.customer_id = c.id
      LEFT JOIN users u ON fr.user_id = u.id
      WHERE ${whereClause}
    `;

    const offset = (page - 1) * pageSize;
    const records = await db.query(sql, [...whereParams, pageSize, offset]);
    const countResult = await db.query(countSql, whereParams);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: {
        follows: records.rows,
        total: total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取跟进记录列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取跟进记录列表失败',
      data: []
    });
  }
});

// 获取跟进记录详情
router.get('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const sql = `
      SELECT 
        fr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.wechat as customer_wechat,
        c.company as customer_company,
        u.name as user_name,
        u.position as user_position
      FROM follow_records fr
      LEFT JOIN customers c ON fr.customer_id = c.id
      LEFT JOIN users u ON fr.user_id = u.id
      WHERE fr.id = ?
    `;

    const result = await db.query(sql, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '跟进记录不存在'
      });
    }

    const followRecord = result.rows[0];

    // 权限检查：普通销售只能查看自己的跟进记录（临时禁用）
    // if (req.user && req.user.role === 'sales' && followRecord.user_id !== req.user.userId) {
    //   return res.status(403).json({
    //     success: false,
    //     message: '无权限查看此跟进记录'
    //   });
    // }

    res.json({
      success: true,
      data: followRecord
    });

  } catch (error) {
    console.error('获取跟进记录详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取跟进记录详情失败'
    });
  }
});

// 创建跟进记录
router.post('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { 
      customerId, 
      type, 
      content, 
      stage,
      result,
      nextFollowTime,
      attachments,
      location,
      duration,
      participants
    } = req.body;

    // 验证必填字段
    if (!customerId || !type || !content) {
      return res.status(400).json({
        success: false,
        message: '客户ID、跟进类型和跟进内容为必填项'
      });
    }

    // 检查客户是否存在
    const customerCheck = await db.query('SELECT id FROM customers WHERE id = ?', [customerId]);
    if (customerCheck.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: '客户不存在'
      });
    }

    // 权限检查：普通销售只能为自己负责的客户创建跟进记录（临时禁用）
    // if (req.user && req.user.role === 'sales') {
    //   const customerOwnerCheck = await db.query(
    //     'SELECT owner_id FROM customers WHERE id = ?', 
    //     [customerId]
    //   );
    //   
    //   if (customerOwnerCheck.rows.length > 0 && 
    //       customerOwnerCheck.rows[0].owner_id !== req.user.userId) {
    //     return res.status(403).json({
    //       success: false,
    //       message: '无权限为此客户创建跟进记录'
    //     });
    //   }
    // }

    const followData = {
      customer_id: customerId,
      user_id: req.user ? req.user.userId : 1, // 使用当前用户ID
      type,
      content,
      stage: stage || 'initial',
      result: result || 'pending',
      next_follow_time: nextFollowTime || null,
      attachments: attachments ? JSON.stringify(attachments) : null,
      location: location || null,
      duration: duration || null,
      participants: participants || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const insertSql = `
      INSERT INTO follow_records (
        customer_id, user_id, type, content, stage, result,
        next_follow_time, attachments, location, duration, participants,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      followData.customer_id,
      followData.user_id,
      followData.type,
      followData.content,
      followData.stage,
      followData.result,
      followData.next_follow_time,
      followData.attachments,
      followData.location,
      followData.duration,
      followData.participants,
      followData.created_at,
      followData.updated_at
    ];

    const insertResult = await db.query(insertSql, insertParams);

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        req.user ? req.user.userId : 1, // 使用当前用户ID
        'create',
        'follow_record',
        insertResult.lastID,
        `创建跟进记录：${type}`,
        new Date().toISOString()
      ]
    );

    res.status(201).json({
      success: true,
      message: '跟进记录创建成功',
      data: {
        id: insertResult.lastID,
        ...followData
      }
    });

  } catch (error) {
    console.error('创建跟进记录错误:', error);
    res.status(500).json({
      success: false,
      message: '创建跟进记录失败'
    });
  }
});

// 更新跟进记录
router.put('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const {
      type,
      content,
      next_follow_time,
      status,
      attachments
    } = req.body;

    // 检查跟进记录是否存在
    const followRecord = await db.findOne('follow_records', 'id = ?', [id]);
    if (!followRecord) {
      return res.status(404).json({
        success: false,
        message: '跟进记录不存在'
      });
    }

    // 权限检查：普通员工只能修改自己的跟进记录
    if (req.user.role === 'employee' && followRecord.user_id !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 更新跟进记录
    const updateData = {
      updated_at: new Date()
    };

    if (type) updateData.type = type;
    if (content) updateData.content = content;
    if (next_follow_time !== undefined) updateData.next_follow_time = next_follow_time;
    if (status) updateData.status = status;
    if (attachments !== undefined) updateData.attachments = attachments ? JSON.stringify(attachments) : null;

    await db.update('follow_records', updateData, 'id = ?', [id]);

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'update_follow',
      target_type: 'follow_record',
      target_id: id,
      description: `更新跟进记录`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '跟进记录更新成功'
    });

  } catch (error) {
    console.error('更新跟进记录错误:', error);
    res.status(500).json({
      success: false,
      message: '更新跟进记录失败'
    });
  }
});

// 删除跟进记录
router.delete('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查跟进记录是否存在
    const followRecord = await db.findOne('follow_records', 'id = ?', [id]);
    if (!followRecord) {
      return res.status(404).json({
        success: false,
        message: '跟进记录不存在'
      });
    }

    // 权限检查：普通员工只能删除自己的跟进记录，管理员可以删除所有
    if (req.user.role === 'employee' && followRecord.user_id !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 删除跟进记录
    await db.delete('follow_records', 'id = ?', [id]);

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'delete_follow',
      target_type: 'follow_record',
      target_id: id,
      description: `删除跟进记录`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '跟进记录删除成功'
    });

  } catch (error) {
    console.error('删除跟进记录错误:', error);
    res.status(500).json({
      success: false,
      message: '删除跟进记录失败'
    });
  }
});

// 获取待跟进客户列表
router.get('/pending/customers', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, pageSize = 10 } = req.query;

    let whereClause = 'c.status = "active"';
    let whereParams = [];

    // 权限控制：普通员工只能看到自己的客户
    if (req.user.role === 'employee') {
      whereClause += ' AND c.assigned_to = ?';
      whereParams.push(req.user.userId);
    }

    const sql = `
      SELECT 
        c.*,
        u.name as assigned_name,
        DATEDIFF(NOW(), COALESCE(c.last_follow_at, c.created_at)) as days_since_follow,
        (
          SELECT next_follow_time 
          FROM follow_records 
          WHERE customer_id = c.id 
          AND next_follow_time IS NOT NULL 
          ORDER BY created_at DESC 
          LIMIT 1
        ) as next_follow_time
      FROM customers c
      LEFT JOIN users u ON c.assigned_to = u.id
      WHERE ${whereClause}
      AND (
        c.last_follow_at IS NULL 
        OR c.last_follow_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
        OR EXISTS (
          SELECT 1 FROM follow_records fr 
          WHERE fr.customer_id = c.id 
          AND fr.next_follow_time <= NOW()
          AND fr.status = 'pending'
        )
      )
      ORDER BY 
        CASE 
          WHEN c.last_follow_at IS NULL THEN 0
          ELSE DATEDIFF(NOW(), c.last_follow_at)
        END DESC
    `;

    const result = await db.paginate(sql, whereParams, page, pageSize);

    res.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('获取待跟进客户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取待跟进客户列表失败'
    });
  }
});

// 获取跟进统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const db = req.app.locals.db;

    let whereClause = '1=1';
    let whereParams = [];

    // 权限控制：普通员工只能看到自己的统计
    if (req.user.role === 'employee') {
      whereClause += ' AND user_id = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 总跟进记录数
      db.query(`SELECT COUNT(*) as total FROM follow_records WHERE ${whereClause}`, whereParams),
      // 今日跟进数
      db.query(`
        SELECT COUNT(*) as today 
        FROM follow_records 
        WHERE DATE(created_at) = CURDATE() AND ${whereClause}
      `, whereParams),
      // 本周跟进数
      db.query(`
        SELECT COUNT(*) as this_week 
        FROM follow_records 
        WHERE YEARWEEK(created_at) = YEARWEEK(NOW()) AND ${whereClause}
      `, whereParams),
      // 各类型跟进数
      db.query(`
        SELECT type, COUNT(*) as count 
        FROM follow_records 
        WHERE ${whereClause}
        GROUP BY type
      `, whereParams),
      // 待跟进客户数
      db.query(`
        SELECT COUNT(DISTINCT customer_id) as pending 
        FROM follow_records 
        WHERE next_follow_time <= NOW() AND status = 'pending' AND ${whereClause}
      `, whereParams)
    ]);

    const typeStats = {};
    stats[3].rows.forEach(item => {
      typeStats[item.type] = item.count;
    });

    res.json({
      success: true,
      data: {
        total: stats[0].rows[0].total,
        today: stats[1].rows[0].today,
        thisWeek: stats[2].rows[0].this_week,
        pending: stats[4].rows[0].pending,
        typeStats
      }
    });

  } catch (error) {
    console.error('获取跟进统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取跟进统计失败'
    });
  }
});

module.exports = router;