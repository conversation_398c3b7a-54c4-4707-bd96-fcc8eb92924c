from datetime import datetime, date, time, timedelta, timezone
from typing import Optional, Union, List
import calendar
from enum import Enum

try:
    from zoneinfo import ZoneInfo
except ImportError:
    # 如果zoneinfo不可用，使用pytz作为备选
    try:
        import pytz
        class ZoneInfo:
            def __init__(self, key):
                self.zone = pytz.timezone(key)
            
            def __str__(self):
                return str(self.zone)
    except ImportError:
        # 如果都不可用，使用简单的UTC实现
        class ZoneInfo:
            def __init__(self, key):
                if key == "UTC":
                    self.zone = timezone.utc
                else:
                    self.zone = timezone.utc  # 默认使用UTC
            
            def __str__(self):
                return str(self.zone)


class DateFormat(str, Enum):
    """日期格式枚举"""
    
    ISO = "%Y-%m-%d"
    ISO_DATETIME = "%Y-%m-%d %H:%M:%S"
    ISO_DATETIME_MS = "%Y-%m-%d %H:%M:%S.%f"
    CHINESE = "%Y年%m月%d日"
    CHINESE_DATETIME = "%Y年%m月%d日 %H:%M:%S"
    US = "%m/%d/%Y"
    US_DATETIME = "%m/%d/%Y %H:%M:%S"
    COMPACT = "%Y%m%d"
    COMPACT_DATETIME = "%Y%m%d%H%M%S"
    READABLE = "%B %d, %Y"
    READABLE_DATETIME = "%B %d, %Y %H:%M:%S"


class TimeFormat(str, Enum):
    """时间格式枚举"""
    
    HMS = "%H:%M:%S"
    HM = "%H:%M"
    HMS_12 = "%I:%M:%S %p"
    HM_12 = "%I:%M %p"


class DateTimeUtils:
    """日期时间工具类"""
    
    # 默认时区
    DEFAULT_TIMEZONE = timezone.utc  # 使用UTC作为默认时区
    UTC_TIMEZONE = timezone.utc
    
    @classmethod
    def now(cls, tz: Optional[ZoneInfo] = None) -> datetime:
        """获取当前时间"""
        if tz is None:
            tz = cls.DEFAULT_TIMEZONE
        return datetime.now(tz)
    
    @classmethod
    def utc_now(cls) -> datetime:
        """获取UTC当前时间"""
        return datetime.now(cls.UTC_TIMEZONE)
    
    @classmethod
    def today(cls) -> date:
        """获取今天日期"""
        return date.today()
    
    @classmethod
    def yesterday(cls) -> date:
        """获取昨天日期"""
        return cls.today() - timedelta(days=1)
    
    @classmethod
    def tomorrow(cls) -> date:
        """获取明天日期"""
        return cls.today() + timedelta(days=1)
    
    @classmethod
    def format_datetime(
        cls,
        dt: Union[datetime, date],
        fmt: Union[str, DateFormat, TimeFormat] = DateFormat.ISO_DATETIME
    ) -> str:
        """格式化日期时间"""
        if isinstance(fmt, (DateFormat, TimeFormat)):
            fmt = fmt.value
        
        if isinstance(dt, date) and not isinstance(dt, datetime):
            # 如果是date对象，转换为datetime
            dt = datetime.combine(dt, time.min)
        
        return dt.strftime(fmt)
    
    @classmethod
    def parse_datetime(
        cls,
        date_string: str,
        fmt: Union[str, DateFormat] = DateFormat.ISO_DATETIME,
        tz: Optional[ZoneInfo] = None
    ) -> datetime:
        """解析日期时间字符串"""
        if isinstance(fmt, DateFormat):
            fmt = fmt.value
        
        dt = datetime.strptime(date_string, fmt)
        
        if tz:
            dt = dt.replace(tzinfo=tz)
        elif dt.tzinfo is None:
            dt = dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)
        
        return dt
    
    @classmethod
    def parse_date(
        cls,
        date_string: str,
        fmt: Union[str, DateFormat] = DateFormat.ISO
    ) -> date:
        """解析日期字符串"""
        if isinstance(fmt, DateFormat):
            fmt = fmt.value
        
        return datetime.strptime(date_string, fmt).date()
    
    @classmethod
    def to_timestamp(cls, dt: datetime) -> float:
        """转换为时间戳"""
        return dt.timestamp()
    
    @classmethod
    def from_timestamp(
        cls,
        timestamp: float,
        tz: Optional[ZoneInfo] = None
    ) -> datetime:
        """从时间戳创建datetime"""
        if tz is None:
            tz = cls.DEFAULT_TIMEZONE
        return datetime.fromtimestamp(timestamp, tz)
    
    @classmethod
    def to_utc(cls, dt: datetime) -> datetime:
        """转换为UTC时间"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)
        return dt.astimezone(cls.UTC_TIMEZONE)
    
    @classmethod
    def to_local(cls, dt: datetime, tz: Optional[ZoneInfo] = None) -> datetime:
        """转换为本地时间"""
        if tz is None:
            tz = cls.DEFAULT_TIMEZONE
        
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=cls.UTC_TIMEZONE)
        
        return dt.astimezone(tz)
    
    @classmethod
    def add_days(cls, dt: Union[datetime, date], days: int) -> Union[datetime, date]:
        """增加天数"""
        return dt + timedelta(days=days)
    
    @classmethod
    def add_hours(cls, dt: datetime, hours: int) -> datetime:
        """增加小时数"""
        return dt + timedelta(hours=hours)
    
    @classmethod
    def add_minutes(cls, dt: datetime, minutes: int) -> datetime:
        """增加分钟数"""
        return dt + timedelta(minutes=minutes)
    
    @classmethod
    def add_seconds(cls, dt: datetime, seconds: int) -> datetime:
        """增加秒数"""
        return dt + timedelta(seconds=seconds)
    
    @classmethod
    def diff_days(cls, dt1: Union[datetime, date], dt2: Union[datetime, date]) -> int:
        """计算两个日期的天数差"""
        if isinstance(dt1, datetime):
            dt1 = dt1.date()
        if isinstance(dt2, datetime):
            dt2 = dt2.date()
        
        return (dt1 - dt2).days
    
    @classmethod
    def diff_seconds(cls, dt1: datetime, dt2: datetime) -> float:
        """计算两个时间的秒数差"""
        return (dt1 - dt2).total_seconds()
    
    @classmethod
    def is_same_day(cls, dt1: Union[datetime, date], dt2: Union[datetime, date]) -> bool:
        """判断是否是同一天"""
        if isinstance(dt1, datetime):
            dt1 = dt1.date()
        if isinstance(dt2, datetime):
            dt2 = dt2.date()
        
        return dt1 == dt2
    
    @classmethod
    def is_weekend(cls, dt: Union[datetime, date]) -> bool:
        """判断是否是周末"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        return dt.weekday() >= 5  # 5=Saturday, 6=Sunday
    
    @classmethod
    def is_workday(cls, dt: Union[datetime, date]) -> bool:
        """判断是否是工作日"""
        return not cls.is_weekend(dt)
    
    @classmethod
    def get_week_start(cls, dt: Union[datetime, date], start_monday: bool = True) -> date:
        """获取周开始日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        if start_monday:
            # 周一为一周开始
            days_since_monday = dt.weekday()
            return dt - timedelta(days=days_since_monday)
        else:
            # 周日为一周开始
            days_since_sunday = (dt.weekday() + 1) % 7
            return dt - timedelta(days=days_since_sunday)
    
    @classmethod
    def get_week_end(cls, dt: Union[datetime, date], start_monday: bool = True) -> date:
        """获取周结束日期"""
        week_start = cls.get_week_start(dt, start_monday)
        return week_start + timedelta(days=6)
    
    @classmethod
    def get_month_start(cls, dt: Union[datetime, date]) -> date:
        """获取月开始日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        return dt.replace(day=1)
    
    @classmethod
    def get_month_end(cls, dt: Union[datetime, date]) -> date:
        """获取月结束日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        last_day = calendar.monthrange(dt.year, dt.month)[1]
        return dt.replace(day=last_day)
    
    @classmethod
    def get_quarter_start(cls, dt: Union[datetime, date]) -> date:
        """获取季度开始日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        quarter = (dt.month - 1) // 3 + 1
        start_month = (quarter - 1) * 3 + 1
        return dt.replace(month=start_month, day=1)
    
    @classmethod
    def get_quarter_end(cls, dt: Union[datetime, date]) -> date:
        """获取季度结束日期"""
        quarter_start = cls.get_quarter_start(dt)
        end_month = quarter_start.month + 2
        
        if end_month > 12:
            end_month = 12
        
        last_day = calendar.monthrange(quarter_start.year, end_month)[1]
        return quarter_start.replace(month=end_month, day=last_day)
    
    @classmethod
    def get_year_start(cls, dt: Union[datetime, date]) -> date:
        """获取年开始日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        return dt.replace(month=1, day=1)
    
    @classmethod
    def get_year_end(cls, dt: Union[datetime, date]) -> date:
        """获取年结束日期"""
        if isinstance(dt, datetime):
            dt = dt.date()
        
        return dt.replace(month=12, day=31)
    
    @classmethod
    def get_age(cls, birth_date: date, reference_date: Optional[date] = None) -> int:
        """计算年龄"""
        if reference_date is None:
            reference_date = cls.today()
        
        age = reference_date.year - birth_date.year
        
        # 检查是否还没到生日
        if (reference_date.month, reference_date.day) < (birth_date.month, birth_date.day):
            age -= 1
        
        return age
    
    @classmethod
    def humanize_timedelta(cls, td: timedelta, locale: str = "zh") -> str:
        """人性化时间差显示"""
        total_seconds = int(td.total_seconds())
        
        if total_seconds < 0:
            total_seconds = abs(total_seconds)
            prefix = "前" if locale == "zh" else "ago"
        else:
            prefix = "后" if locale == "zh" else "later"
        
        if total_seconds < 60:
            if locale == "zh":
                return f"{total_seconds}秒{prefix}"
            else:
                return f"{total_seconds} seconds {prefix}"
        
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            if locale == "zh":
                return f"{minutes}分钟{prefix}"
            else:
                return f"{minutes} minutes {prefix}"
        
        elif total_seconds < 86400:
            hours = total_seconds // 3600
            if locale == "zh":
                return f"{hours}小时{prefix}"
            else:
                return f"{hours} hours {prefix}"
        
        else:
            days = total_seconds // 86400
            if locale == "zh":
                return f"{days}天{prefix}"
            else:
                return f"{days} days {prefix}"
    
    @classmethod
    def get_date_range(
        cls,
        start_date: date,
        end_date: date,
        step_days: int = 1
    ) -> List[date]:
        """生成日期范围列表"""
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=step_days)
        
        return dates
    
    @classmethod
    def is_valid_date_string(
        cls,
        date_string: str,
        fmt: Union[str, DateFormat] = DateFormat.ISO
    ) -> bool:
        """验证日期字符串是否有效"""
        try:
            cls.parse_date(date_string, fmt)
            return True
        except ValueError:
            return False
    
    @classmethod
    def is_valid_datetime_string(
        cls,
        datetime_string: str,
        fmt: Union[str, DateFormat] = DateFormat.ISO_DATETIME
    ) -> bool:
        """验证日期时间字符串是否有效"""
        try:
            cls.parse_datetime(datetime_string, fmt)
            return True
        except ValueError:
            return False


# 便捷函数
def now(tz: Optional[ZoneInfo] = None) -> datetime:
    """获取当前时间"""
    return DateTimeUtils.now(tz)


def utc_now() -> datetime:
    """获取UTC当前时间"""
    return DateTimeUtils.utc_now()


def today() -> date:
    """获取今天日期"""
    return DateTimeUtils.today()


def format_datetime(
    dt: Union[datetime, date],
    fmt: Union[str, DateFormat, TimeFormat] = DateFormat.ISO_DATETIME
) -> str:
    """格式化日期时间"""
    return DateTimeUtils.format_datetime(dt, fmt)


def parse_datetime(
    date_string: str,
    fmt: Union[str, DateFormat] = DateFormat.ISO_DATETIME,
    tz: Optional[ZoneInfo] = None
) -> datetime:
    """解析日期时间字符串"""
    return DateTimeUtils.parse_datetime(date_string, fmt, tz)


def parse_date(
    date_string: str,
    fmt: Union[str, DateFormat] = DateFormat.ISO
) -> date:
    """解析日期字符串"""
    return DateTimeUtils.parse_date(date_string, fmt)