<template>
  <div class="deal-stage-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
    >
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="客户" path="customerId">
          <n-select
            v-model:value="formData.customerId"
            placeholder="请选择客户"
            :options="customerOptions"
            filterable
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi label="子阶段" path="subStage">
          <n-select
            v-model:value="formData.subStage"
            placeholder="请选择子阶段"
            :options="subStageOptions"
            @update:value="handleSubStageChange"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="设计师" path="designerId">
          <n-select
            v-model:value="formData.designerId"
            placeholder="请选择设计师"
            :options="designerOptions"
            filterable
            clearable
            @update:value="handleDesignerChange"
          />
        </n-form-item-gi>
        <n-form-item-gi label="成交金额" path="amount">
          <n-input-number
            v-model:value="formData.amount"
            placeholder="请输入成交金额"
            style="width: 100%"
            :min="0"
            :precision="2"
            :show-button="false"
          >
            <template #prefix>¥</template>
          </n-input-number>
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="合同编号" path="contractNo">
          <n-input
            v-model:value="formData.contractNo"
            placeholder="请输入合同编号"
          />
        </n-form-item-gi>
        <n-form-item-gi label="付款状态" path="paymentStatus">
          <n-select
            v-model:value="formData.paymentStatus"
            placeholder="请选择付款状态"
            :options="paymentStatusOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="成交时间" path="dealDate">
          <n-date-picker
            v-model:value="formData.dealDate"
            type="datetime"
            placeholder="请选择成交时间"
            style="width: 100%"
          />
        </n-form-item-gi>
        <n-form-item-gi label="跟进时间" path="followTime">
          <n-date-picker
            v-model:value="formData.followTime"
            type="datetime"
            placeholder="请选择跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="下次跟进">
          <n-date-picker
            v-model:value="formData.nextFollowTime"
            type="datetime"
            placeholder="请选择下次跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
        <n-form-item-gi label="跟进方式" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择跟进方式"
            :options="typeOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <!-- 小定特殊字段 -->
      <div v-if="formData.subStage === 'small_deposit'" class="small-deposit-fields">
        <n-divider title-placement="left">
          <n-text type="info">小定信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="定金金额">
            <n-input-number
              v-model:value="depositAmount"
              placeholder="请输入定金金额"
              style="width: 100%"
              :min="0"
              :precision="2"
            >
              <template #prefix>¥</template>
            </n-input-number>
          </n-form-item-gi>
          <n-form-item-gi label="预计签约">
            <n-date-picker
              v-model:value="expectedSignDate"
              type="date"
              placeholder="请选择预计签约时间"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <!-- 大定特殊字段 -->
      <div v-if="formData.subStage === 'large_deposit'" class="large-deposit-fields">
        <n-divider title-placement="left">
          <n-text type="warning">大定信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="大定金额">
            <n-input-number
              v-model:value="largeDepositAmount"
              placeholder="请输入大定金额"
              style="width: 100%"
              :min="0"
              :precision="2"
            >
              <template #prefix>¥</template>
            </n-input-number>
          </n-form-item-gi>
          <n-form-item-gi label="开工时间">
            <n-date-picker
              v-model:value="constructionDate"
              type="date"
              placeholder="请选择开工时间"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="工期">
            <n-input-number
              v-model:value="constructionDays"
              placeholder="请输入工期"
              style="width: 100%"
              :min="0"
            >
              <template #suffix>天</template>
            </n-input-number>
          </n-form-item-gi>
          <n-form-item-gi label="完工时间">
            <n-date-picker
              v-model:value="completionDate"
              type="date"
              placeholder="请选择完工时间"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <!-- 预售金特殊字段 -->
      <div v-if="formData.subStage === 'presale'" class="presale-fields">
        <n-divider title-placement="left">
          <n-text type="success">预售金信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="预售金额">
            <n-input-number
              v-model:value="presaleAmount"
              placeholder="请输入预售金额"
              style="width: 100%"
              :min="0"
              :precision="2"
            >
              <template #prefix>¥</template>
            </n-input-number>
          </n-form-item-gi>
          <n-form-item-gi label="预售项目">
            <n-input
              v-model:value="presaleProject"
              placeholder="请输入预售项目名称"
            />
          </n-form-item-gi>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="交房时间">
            <n-date-picker
              v-model:value="deliveryDate"
              type="date"
              placeholder="请选择交房时间"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="优惠政策">
            <n-input
              v-model:value="discountPolicy"
              placeholder="请输入优惠政策"
            />
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <n-form-item label="跟进内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入跟进内容"
          :rows="4"
        />
      </n-form-item>
      
      <n-form-item label="备注">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

interface FormData {
  id: number | null
  customerId: number | null
  type: string
  status: string
  followTime: number | null
  content: string
  nextFollowTime: number | null
  remark: string
  stage: 'follow' | 'visit' | 'deal'
  subStage: string
  designer: string
  designerId: number | null
  amount: number | null
  contractNo: string
  paymentStatus: string
  visitDate: number | null
  measureDate: number | null
  dealDate: number | null
}

interface Props {
  formData: FormData
  customerOptions: Array<{ label: string; value: number }>
  designerOptions: Array<{ label: string; value: number }>
}

interface Emits {
  'update:formData': [value: FormData]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)

// 额外字段
const depositAmount = ref<number | null>(null)
const expectedSignDate = ref<number | null>(null)
const largeDepositAmount = ref<number | null>(null)
const constructionDate = ref<number | null>(null)
const constructionDays = ref<number | null>(null)
const completionDate = ref<number | null>(null)
const presaleAmount = ref<number | null>(null)
const presaleProject = ref('')
const deliveryDate = ref<number | null>(null)
const discountPolicy = ref('')

// 子阶段选项
const subStageOptions = [
  { label: '小定', value: 'small_deposit' },
  { label: '大定', value: 'large_deposit' },
  { label: '预售金', value: 'presale' }
]

// 跟进方式选项
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '到店洽谈', value: 'visit' },
  { label: '签约', value: 'contract' },
  { label: '付款', value: 'payment' },
  { label: '其他', value: 'other' }
]

// 付款状态选项
const paymentStatusOptions = [
  { label: '未付款', value: 'unpaid' },
  { label: '部分付款', value: 'partial' },
  { label: '已付款', value: 'paid' },
  { label: '已退款', value: 'refunded' }
]

// 表单验证规则
const formRules: FormRules = {
  customerId: {
    required: true,
    type: 'number',
    message: '请选择客户',
    trigger: 'change'
  },
  subStage: {
    required: true,
    message: '请选择子阶段',
    trigger: 'change'
  },
  designerId: {
    required: true,
    type: 'number',
    message: '请选择设计师',
    trigger: 'change'
  },
  amount: {
    required: true,
    type: 'number',
    message: '请输入成交金额',
    trigger: 'blur'
  },
  contractNo: {
    required: true,
    message: '请输入合同编号',
    trigger: 'blur'
  },
  paymentStatus: {
    required: true,
    message: '请选择付款状态',
    trigger: 'change'
  },
  dealDate: {
    required: true,
    type: 'number',
    message: '请选择成交时间',
    trigger: 'change'
  },
  followTime: {
    required: true,
    type: 'number',
    message: '请选择跟进时间',
    trigger: 'change'
  },
  type: {
    required: true,
    message: '请选择跟进方式',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入跟进内容',
    trigger: 'blur'
  }
}

// 方法
const handleSubStageChange = (value: string) => {
  // 根据子阶段自动设置跟进方式
  const typeMap: Record<string, string> = {
    small_deposit: 'contract',
    large_deposit: 'contract',
    presale: 'payment'
  }
  if (typeMap[value]) {
    props.formData.type = typeMap[value]
  }
  
  // 根据子阶段设置默认付款状态
  const statusMap: Record<string, string> = {
    small_deposit: 'partial',
    large_deposit: 'partial',
    presale: 'paid'
  }
  if (statusMap[value]) {
    props.formData.paymentStatus = statusMap[value]
  }
}

const handleDesignerChange = (value: number | null) => {
  if (value && props.designerOptions) {
    const designer = props.designerOptions.find(item => item.value === value)
    if (designer) {
      props.formData.designer = designer.label
    }
  } else {
    props.formData.designer = ''
  }
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newValue) => {
    emit('update:formData', newValue)
  },
  { deep: true }
)

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.deal-stage-form {
  padding: 16px 0;
}

.small-deposit-fields,
.large-deposit-fields,
.presale-fields {
  margin: 20px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

.small-deposit-fields {
  border-left: 4px solid #1890ff;
}

.large-deposit-fields {
  border-left: 4px solid #faad14;
}

.presale-fields {
  border-left: 4px solid #52c41a;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-date-picker) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

:deep(.n-divider .n-divider__title) {
  font-weight: 600;
}
</style>