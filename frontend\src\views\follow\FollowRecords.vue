<template>
  <div class="follow-records-container">
    <!-- 页面头部 -->

    <!-- 三阶段选项卡 -->
    <n-card class="tabs-card">
      <n-tabs 
        v-model:value="activeTab" 
        type="line" 
        animated
        @update:value="handleTabChange"
      >
        <!-- 跟进阶段 -->
        <n-tab-pane name="follow" tab="跟进阶段">
          <template #tab>
            <n-space align="center">
              <n-icon color="#1677ff"><call-outline /></n-icon>
              <span>跟进阶段</span>
              <n-badge :value="followStats.count" :max="99" />
            </n-space>
          </template>
          <FollowStageTable 
            :data="followRecords" 
            :loading="loading"
            :pagination="pagination"
            @edit="handleEdit"
            @view="handleView"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </n-tab-pane>

        <!-- 到店阶段 -->
        <n-tab-pane name="visit" tab="到店阶段">
          <template #tab>
            <n-space align="center">
              <n-icon color="#52c41a"><storefront-outline /></n-icon>
              <span>到店阶段</span>
              <n-badge :value="visitStats.count" :max="99" />
            </n-space>
          </template>
          <VisitStageTable 
            :data="visitRecords" 
            :loading="loading"
            :pagination="pagination"
            @edit="handleEdit"
            @view="handleView"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </n-tab-pane>

        <!-- 成交阶段 -->
        <n-tab-pane name="deal" tab="成交阶段">
          <template #tab>
            <n-space align="center">
              <n-icon color="#faad14"><trophy-outline /></n-icon>
              <span>成交阶段</span>
              <n-badge :value="dealStats.count" :max="99" />
            </n-space>
          </template>
          <DealStageTable 
            :data="dealRecords" 
            :loading="loading"
            :pagination="pagination"
            :customer-options="customerOptions"
            :designer-options="designerOptions"
            @edit="handleEdit"
            @view="handleView"
            @delete="handleDelete"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <!-- 跟进记录表单弹窗 -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      :title="modalTitle"
      :style="{ width: '800px' }"
      :on-positive-click="handleSubmit"
      :on-negative-click="() => showModal = false"
      positive-text="保存"
      negative-text="取消"
    >
      <component 
        :is="currentFormComponent" 
        ref="formRef"
        v-model:form-data="formData"
        :customer-options="customerOptions"
        :designer-options="designerOptions"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, markRaw, defineAsyncComponent } from 'vue'
import { useMessage } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import {
  CloudDownloadOutline,
  SearchOutline,
  RefreshOutline,
  CallOutline,
  StorefrontOutline,
  TrophyOutline,
  ArrowBackOutline
} from '@vicons/ionicons5'
import type { FollowRecord, FollowSubStage, VisitSubStage, DealSubStage } from '@/types'
import type { FormInst } from 'naive-ui'

// 导入表格和表单组件
const FollowStageTable = defineAsyncComponent(() => import('./components/FollowStageTable.vue'))
const FollowStageForm = defineAsyncComponent(() => import('./components/FollowStageForm.vue'))
const VisitStageTable = defineAsyncComponent(() => import('./components/VisitStageTable.vue'))
const VisitStageForm = defineAsyncComponent(() => import('./components/VisitStageForm.vue'))
const DealStageTable = defineAsyncComponent(() => import('./components/DealStageTable.vue'))
const DealStageForm = defineAsyncComponent(() => import('./components/DealStageForm.vue'))

const message = useMessage()
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref<'follow' | 'visit' | 'deal'>('follow')
const currentCustomerId = ref<number | null>(null)
const currentCustomerName = ref<string>('')
const allRecords = ref<FollowRecord[]>([])
const showModal = ref(false)
const formRef = ref<FormInst | null>(null)
const currentRecord = ref<FollowRecord | null>(null)
const currentStage = ref<'follow' | 'visit' | 'deal'>('follow')

// 搜索表单
const searchForm = reactive({
  customerName: '',
  type: null,
  status: null,
  dateRange: null as [number, number] | null
})

// 统计数据
const stats = reactive({
  total: 0,
  today: 0,
  week: 0,
  pending: 0
})

// 表单数据
const formData = reactive({
  id: null as number | null,
  customerId: null as number | null,
  type: '',
  status: '',
  followTime: null as number | null,
  content: '',
  nextFollowTime: null as number | null,
  remark: '',
  stage: 'follow' as 'follow' | 'visit' | 'deal',
  subStage: '',
  designer: '',
  designerId: null as number | null,
  amount: null as number | null,
  contractNo: '',
  paymentStatus: '',
  visitDate: null as number | null,
  measureDate: null as number | null,
  dealDate: null as number | null
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 选项配置
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '邮件沟通', value: 'email' },
  { label: '面谈', value: 'meeting' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '已联系', value: 'contacted' },
  { label: '有意向', value: 'interested' },
  { label: '无意向', value: 'not_interested' },
  { label: '待跟进', value: 'pending' },
  { label: '已成交', value: 'closed' }
]

const customerOptions = ref([
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 },
  { label: '赵六', value: 4 },
  { label: '陈七', value: 5 },
  { label: '刘八', value: 6 }
])

const designerOptions = ref([
  { label: '张设计师', value: 1 },
  { label: '李设计师', value: 2 },
  { label: '王设计师', value: 3 },
  { label: '赵设计师', value: 4 },
  { label: '陈设计师', value: 5 }
])

// 计算属性 - 按阶段过滤记录
const followRecords = computed(() => 
  allRecords.value.filter(record => record.stage === 'follow')
)

const visitRecords = computed(() => 
  allRecords.value.filter(record => record.stage === 'visit')
)

const dealRecords = computed(() => 
  allRecords.value.filter(record => record.stage === 'deal')
)

// 计算属性 - 各阶段统计
const followStats = computed(() => ({
  count: followRecords.value.length
}))

const visitStats = computed(() => ({
  count: visitRecords.value.length
}))

const dealStats = computed(() => ({
  count: dealRecords.value.length
}))

// 计算属性 - 模态框标题
const modalTitle = computed(() => {
  const stageNames = {
    follow: '跟进阶段',
    visit: '到店阶段', 
    deal: '成交阶段'
  }
  const action = currentRecord.value ? '编辑' : '新增'
  return `${action}${stageNames[currentStage.value]}记录`
})

// 计算属性 - 当前表单组件
const currentFormComponent = computed(() => {
  const components = {
    follow: markRaw(FollowStageForm),
    visit: markRaw(VisitStageForm),
    deal: markRaw(DealStageForm)
  }
  return components[currentStage.value]
})

// 方法
const handleTabChange = (value: string) => {
  activeTab.value = value as 'follow' | 'visit' | 'deal'
  pagination.page = 1
  fetchFollowRecords()
}



const handleEdit = (record: FollowRecord) => {
  currentRecord.value = record
  currentStage.value = record.stage
  Object.assign(formData, {
    id: record.id,
    customerId: record.customerId || record.customer_id,
    type: record.type,
    status: record.status,
    followTime: new Date(record.followTime || record.follow_time).getTime(),
    content: record.content,
    nextFollowTime: record.nextFollowTime || record.next_follow_time ? 
      new Date(record.nextFollowTime || record.next_follow_time!).getTime() : null,
    remark: record.remark || '',
    stage: record.stage,
    subStage: record.subStage || '',
    designer: record.designer || '',
    designerId: record.designerId || null,
    amount: record.amount || null,
    contractNo: record.contractNo || '',
    paymentStatus: record.paymentStatus || '',
    visitDate: record.visitDate ? new Date(record.visitDate).getTime() : null,
    measureDate: record.measureDate ? new Date(record.measureDate).getTime() : null,
    dealDate: record.dealDate ? new Date(record.dealDate).getTime() : null
  })
  showModal.value = true
}

const handleView = (record: FollowRecord) => {
  // 实现查看详情逻辑
  console.log('查看详情:', record)
  message.info('查看详情功能待实现')
}

const handleDelete = (record: FollowRecord) => {
  // 实现删除逻辑
  console.log('删除记录:', record)
  message.info('删除功能待实现')
}

const handleSubmit = async () => {
  if (!formRef.value) return false
  
  // 只允许编辑现有记录，不允许新增
  if (!currentRecord.value) {
    message.error('只能编辑现有记录，不支持新增功能')
    return false
  }
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 根据阶段设置默认值
    if (formData.stage === 'visit' && !formData.visitDate) {
      formData.visitDate = formData.followTime
    }
    if (formData.stage === 'deal' && !formData.dealDate) {
      formData.dealDate = formData.followTime
    }
    
    // TODO: 调用更新API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('更新成功')
    showModal.value = false
    await fetchFollowRecords()
    return true
  } catch (error) {
    message.error('保存失败，请检查表单数据')
    return false
  } finally {
    loading.value = false
  }
}



const handleSearch = () => {
  pagination.page = 1
  fetchFollowRecords()
}

const handleReset = () => {
  Object.assign(searchForm, {
    customerName: '',
    type: null,
    status: null,
    dateRange: null
  })
  pagination.page = 1
  fetchFollowRecords()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchFollowRecords()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchFollowRecords()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    customerId: null,
    type: '',
    status: '',
    followTime: null,
    content: '',
    nextFollowTime: null,
    remark: '',
    stage: 'follow',
    subStage: '',
    designer: '',
    designerId: null,
    amount: null,
    contractNo: '',
    paymentStatus: '',
    visitDate: null,
    measureDate: null,
    dealDate: null
  })
}

// 获取跟进记录列表
const fetchFollowRecords = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: FollowRecord[] = [
      // 跟进阶段数据
      {
        id: 1,
        customer_id: 1,
        customer_name: '张三',
        customerId: 1,
        customerName: '张三',
        type: 'phone',
        status: 'interested',
        follow_time: '2024-01-15T10:30:00Z',
        followTime: '2024-01-15T10:30:00Z',
        content: '电话联系客户，了解装修需求',
        next_follow_time: '2024-01-20T10:30:00Z',
        nextFollowTime: '2024-01-20T10:30:00Z',
        follow_by: '销售员A',
        followBy: '销售员A',
        created_by: 1,
        created_by_name: '销售员A',
        remark: '客户对现代简约风格感兴趣',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        stage: 'follow',
        subStage: 'initial'
      },
      {
        id: 2,
        customer_id: 2,
        customer_name: '李四',
        customerId: 2,
        customerName: '李四',
        type: 'wechat',
        status: 'interested',
        follow_time: '2024-01-16T14:20:00Z',
        followTime: '2024-01-16T14:20:00Z',
        content: '微信发送装修案例，客户表示满意',
        next_follow_time: '2024-01-22T14:20:00Z',
        nextFollowTime: '2024-01-22T14:20:00Z',
        follow_by: '销售员B',
        followBy: '销售员B',
        created_by: 2,
        created_by_name: '销售员B',
        remark: '预约下周到店参观',
        created_at: '2024-01-16T14:20:00Z',
        updated_at: '2024-01-16T14:20:00Z',
        createdAt: '2024-01-16T14:20:00Z',
        updatedAt: '2024-01-16T14:20:00Z',
        stage: 'follow',
        subStage: 'follow_up'
      },
      // 到店阶段数据
      {
        id: 3,
        customer_id: 3,
        customer_name: '王五',
        customerId: 3,
        customerName: '王五',
        type: 'measure',
        status: 'contacted',
        follow_time: '2024-01-17T16:00:00Z',
        followTime: '2024-01-17T16:00:00Z',
        content: '上门量房，测量房屋尺寸，了解客户具体需求',
        next_follow_time: '2024-01-24T16:00:00Z',
        nextFollowTime: '2024-01-24T16:00:00Z',
        follow_by: '张设计师',
        followBy: '张设计师',
        created_by: 3,
        created_by_name: '张设计师',
        remark: '房屋面积120平米，三室两厅',
        created_at: '2024-01-17T16:00:00Z',
        updated_at: '2024-01-17T16:00:00Z',
        createdAt: '2024-01-17T16:00:00Z',
        updatedAt: '2024-01-17T16:00:00Z',
        stage: 'visit',
        subStage: 'measure',
        designer: '张设计师',
        designerId: 1,
        visitDate: '2024-01-17T16:00:00Z',
        measureDate: '2024-01-17T16:00:00Z'
      },
      {
        id: 4,
        customer_id: 4,
        customer_name: '赵六',
        customerId: 4,
        customerName: '赵六',
        type: 'visit',
        status: 'interested',
        follow_time: '2024-01-18T10:00:00Z',
        followTime: '2024-01-18T10:00:00Z',
        content: '客户到店参观展厅，查看材料样品',
        next_follow_time: '2024-01-25T10:00:00Z',
        nextFollowTime: '2024-01-25T10:00:00Z',
        follow_by: '李设计师',
        followBy: '李设计师',
        created_by: 4,
        created_by_name: '李设计师',
        remark: '对展厅效果很满意，准备签约',
        created_at: '2024-01-18T10:00:00Z',
        updated_at: '2024-01-18T10:00:00Z',
        createdAt: '2024-01-18T10:00:00Z',
        updatedAt: '2024-01-18T10:00:00Z',
        stage: 'visit',
        subStage: 'visit',
        designer: '李设计师',
        designerId: 2,
        visitDate: '2024-01-18T10:00:00Z'
      },
      // 成交阶段数据
      {
        id: 5,
        customer_id: 5,
        customer_name: '陈七',
        customerId: 5,
        customerName: '陈七',
        type: 'contract',
        status: 'closed',
        follow_time: '2024-01-19T14:30:00Z',
        followTime: '2024-01-19T14:30:00Z',
        content: '签订小定合同，收取定金',
        next_follow_time: '2024-01-26T14:30:00Z',
        nextFollowTime: '2024-01-26T14:30:00Z',
        follow_by: '王设计师',
        followBy: '王设计师',
        created_by: 5,
        created_by_name: '王设计师',
        remark: '客户已支付小定，等待大定签约',
        created_at: '2024-01-19T14:30:00Z',
        updated_at: '2024-01-19T14:30:00Z',
        createdAt: '2024-01-19T14:30:00Z',
        updatedAt: '2024-01-19T14:30:00Z',
        stage: 'deal',
        subStage: 'small_deposit',
        designer: '王设计师',
        designerId: 3,
        amount: 5000,
        contractNo: 'XD2024001',
        paymentStatus: 'paid',
        dealDate: '2024-01-19T14:30:00Z'
      },
      {
        id: 6,
        customer_id: 6,
        customer_name: '刘八',
        customerId: 6,
        customerName: '刘八',
        type: 'contract',
        status: 'closed',
        follow_time: '2024-01-20T11:00:00Z',
        followTime: '2024-01-20T11:00:00Z',
        content: '签订大定合同，确定施工方案',
        next_follow_time: '2024-01-27T11:00:00Z',
        nextFollowTime: '2024-01-27T11:00:00Z',
        follow_by: '赵设计师',
        followBy: '赵设计师',
        created_by: 6,
        created_by_name: '赵设计师',
        remark: '合同总价15万，已支付大定',
        created_at: '2024-01-20T11:00:00Z',
        updated_at: '2024-01-20T11:00:00Z',
        createdAt: '2024-01-20T11:00:00Z',
        updatedAt: '2024-01-20T11:00:00Z',
        stage: 'deal',
        subStage: 'large_deposit',
        designer: '赵设计师',
        designerId: 4,
        amount: 150000,
        contractNo: 'DD2024001',
        paymentStatus: 'partial',
        dealDate: '2024-01-20T11:00:00Z'
      }
    ]
    
    allRecords.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('获取跟进记录失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    Object.assign(stats, {
      total: 245,
      today: 12,
      week: 68,
      pending: 23
    })
  } catch (error) {
    message.error('获取统计数据失败')
  }
}



// 根据客户状态映射到阶段
const mapCustomerStatusToStage = (status: string): 'follow' | 'visit' | 'deal' => {
  switch (status) {
    case 'potential':
    case 'contacted':
      return 'follow'
    case 'negotiating':
      return 'visit'
    case 'deal':
      return 'deal'
    default:
      return 'follow'
  }
}

// 初始化
onMounted(() => {
  // 处理路由参数
  const customerId = route.query.customerId as string
  const customerName = route.query.customerName as string
  const currentStage = route.query.currentStage as string
  
  if (customerId) {
    currentCustomerId.value = parseInt(customerId)
    currentCustomerName.value = customerName || ''
    
    // 根据客户当前阶段自动选择对应的选项卡
    if (currentStage) {
      activeTab.value = mapCustomerStatusToStage(currentStage)
    }
    
    // 预填充表单中的客户信息
    formData.customerId = currentCustomerId.value
  }
  
  fetchFollowRecords()
  fetchStats()
})
</script>

<style scoped>
.follow-records-container {
  padding: 0;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}



.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: flex-end;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.progress-section {
  margin-bottom: 24px;
}

.tabs-card {
  margin-bottom: 20px;
}

:deep(.n-tabs .n-tab-pane) {
  padding-top: 16px;
}

:deep(.n-tabs .n-tabs-tab) {
  font-weight: 500;
}

:deep(.n-badge) {
  margin-left: 8px;
}
</style>