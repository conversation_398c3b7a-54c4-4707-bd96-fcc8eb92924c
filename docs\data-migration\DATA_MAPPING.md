# 数据迁移映射规则

## 概述
本文档定义了从源数据（sample_data.sql 和前端 mock 数据）到 Supabase 数据库的映射和转换规则。

## 数据源分析

### 1. sample_data.sql 数据源
- **用户数据**: 7条用户记录，包含管理员、经理、销售、设计师等角色
- **客户数据**: 10条客户记录，包含基本信息、联系方式、状态等
- **跟进记录**: 8条跟进记录，包含电话、会议、微信、邮件等类型
- **见面记录**: 4条见面记录，关联跟进记录和设计师
- **公海池记录**: 2条公海操作记录
- **营销链接**: 4条营销链接数据
- **访问记录**: 4条链接访问日志
- **统计数据**: 9条分析统计记录
- **操作日志**: 5条系统操作日志

### 2. 前端 Mock 数据源
- **客户数据**: 8条客户记录（customerData.ts）
- **公海客户**: 5条公海客户记录
- **营销活动**: 7条营销活动记录（marketingData.ts）
- **活动参与者**: 3条参与记录
- **活动分享**: 2条分享记录

## 目标 Supabase 表结构

### 核心表
1. **customers** - 客户信息表
2. **customer_follow_records** - 客户跟进记录表
3. **marketing_campaigns** - 营销活动表
4. **campaign_participants** - 活动参与者表
5. **campaign_shares** - 活动分享表
6. **meetings** - 会议记录表
7. **meeting_participants** - 会议参与者表
8. **pool_rules** - 公海规则表
9. **customer_behaviors** - 客户行为表
10. **wechat_customer_tracking** - 微信客户跟踪表
11. **sales_funnel_stats** - 销售漏斗统计表
12. **customer_value_analysis** - 客户价值分析表

## 数据映射规则

### 1. 客户数据映射 (customers 表)

#### 从 sample_data.sql 映射
```sql
-- 源字段 -> 目标字段
id -> id
name -> name
phone -> phone
email -> email
company -> company
position -> position
wechat -> wechat_id
address -> address
source -> source
status -> status
level -> level
birthday -> birthday
gender -> gender
is_vip -> is_vip
is_high_value -> is_high_value
tags -> tags (JSON)
remark -> notes
owner_id -> assigned_to
last_follow_time -> last_contact_at
next_follow_time -> next_follow_at
follow_count -> follow_count
deal_amount -> deal_amount
```

#### 从前端 Mock 数据映射
```typescript
// 源字段 -> 目标字段
id -> id
name -> name
phone -> phone
company -> company
position -> position
source -> source
level -> level
status -> status
assigned_to -> assigned_to
region -> region (JSON)
address -> address
remark -> notes
last_follow_time -> last_contact_at
next_follow_time -> next_follow_at
deal_amount -> deal_amount
deal_probability -> deal_probability
notes -> description
tags -> tags (JSON)
created_at -> created_at
updated_at -> updated_at
```

### 2. 跟进记录映射 (customer_follow_records 表)

#### 从 sample_data.sql 映射
```sql
-- 源字段 -> 目标字段
id -> id
customer_id -> customer_id
user_id -> user_id
type -> contact_type
stage -> stage
content -> content
result -> result
result_detail -> result_detail
has_next_plan -> has_next_plan
next_time -> next_follow_at
next_content -> next_content
duration -> duration
location -> location
```

### 3. 营销活动映射 (marketing_campaigns 表)

#### 从前端 Mock 数据映射
```typescript
// 源字段 -> 目标字段
id -> id
name -> name
type -> type
description -> description
status -> status
start_time -> start_date
end_time -> end_date
target_audience -> target_audience
budget -> budget
participants_count -> participants_count
conversion_rate -> conversion_rate
created_at -> created_at
updated_at -> updated_at
config -> config (JSON)
```

### 4. 活动参与者映射 (campaign_participants 表)

```typescript
// 源字段 -> 目标字段
id -> id
campaign_id -> campaign_id
customer_id -> customer_id
customer_name -> customer_name
customer_phone -> customer_phone
customer_wechat -> customer_wechat
participation_time -> participation_time
status -> status
result -> result
reward -> reward
notes -> notes
```

### 5. 会议记录映射 (meetings 表)

#### 从 sample_data.sql 映射
```sql
-- 源字段 -> 目标字段
id -> id
follow_record_id -> follow_record_id
customer_id -> customer_id
user_id -> user_id
meeting_type -> meeting_type
meeting_time -> meeting_time
designer_id -> designer_id
designer_name -> designer_name
visit_count -> visit_count
address -> address
notes -> notes
```

## 数据转换规则

### 1. 数据类型转换
- **日期时间**: MySQL DATETIME -> PostgreSQL TIMESTAMPTZ
- **JSON字段**: MySQL JSON/TEXT -> PostgreSQL JSONB
- **布尔值**: MySQL TINYINT(1) -> PostgreSQL BOOLEAN
- **枚举值**: 保持字符串格式，确保值在允许范围内

### 2. 字段值转换

#### 客户状态映射
```
MySQL -> Supabase
'potential' -> 'potential'
'interested' -> 'contacted'
'deal' -> 'converted'
'lost' -> 'lost'
```

#### 跟进类型映射
```
MySQL -> Supabase
'phone' -> 'phone'
'meeting' -> 'meeting'
'wechat' -> 'wechat'
'email' -> 'email'
'visit' -> 'visit'
```

#### 营销活动状态映射
```
Mock -> Supabase
'active' -> 'active'
'draft' -> 'draft'
'ended' -> 'completed'
```

### 3. 默认值处理
- **created_at**: 如果源数据没有，使用当前时间
- **updated_at**: 如果源数据没有，使用 created_at 值
- **assigned_to**: 如果为 0 或 NULL，设置为 NULL（公海客户）
- **tags**: 空数组 [] 如果源数据为空
- **region**: 空数组 [] 如果源数据为空

### 4. 数据清理规则
- **手机号格式**: 确保为11位数字
- **邮箱格式**: 验证邮箱格式有效性
- **JSON字段**: 确保为有效的JSON格式
- **日期格式**: 转换为ISO 8601格式

## 数据完整性约束

### 1. 必填字段验证
- customers.name: 不能为空
- customers.phone: 不能为空且格式正确
- customer_follow_records.customer_id: 必须存在于customers表
- marketing_campaigns.name: 不能为空

### 2. 外键关系
- customer_follow_records.customer_id -> customers.id
- campaign_participants.customer_id -> customers.id
- campaign_participants.campaign_id -> marketing_campaigns.id
- meetings.customer_id -> customers.id

### 3. 数据范围验证
- customers.level: 必须在 ['A', 'B', 'C', 'D'] 范围内
- customers.status: 必须在允许的状态值范围内
- deal_probability: 0-100之间的数值

## 迁移优先级

1. **高优先级**
   - customers (客户基础数据)
   - marketing_campaigns (营销活动)

2. **中优先级**
   - customer_follow_records (跟进记录)
   - campaign_participants (活动参与)
   - meetings (会议记录)

3. **低优先级**
   - campaign_shares (活动分享)
   - 统计和分析数据

## 错误处理策略

1. **数据验证失败**: 记录错误日志，跳过该条记录
2. **外键约束失败**: 先创建依赖数据，再重试
3. **重复数据**: 根据业务规则决定更新或跳过
4. **格式转换失败**: 使用默认值或跳过该字段

## 回滚策略

1. **备份现有数据**: 迁移前创建数据快照
2. **事务处理**: 使用数据库事务确保原子性
3. **分批处理**: 分批迁移，便于定位问题
4. **验证机制**: 迁移后验证数据完整性