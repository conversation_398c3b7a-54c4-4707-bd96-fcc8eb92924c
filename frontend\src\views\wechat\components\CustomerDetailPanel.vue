<template>
  <div class="customer-detail-panel">
    <!-- 客户基本信息 -->
    <n-card title="基本信息" class="info-card">
      <div class="customer-header">
        <div class="customer-avatar">
          <n-avatar
            :size="80"
            :src="customer.avatar"
            :fallback-src="'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=default%20avatar&image_size=square'"
          />
          <div class="status-indicator" :class="customer.status"></div>
        </div>
        <div class="customer-info">
          <div class="customer-name">{{ customer.nickname }}</div>
          <div class="customer-id">微信号：{{ customer.wechatId }}</div>
          <div class="customer-tags">
            <n-tag
              v-for="tag in customer.tags"
              :key="tag"
              size="small"
              type="info"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
        <div class="customer-actions">
          <n-button type="primary" size="small" @click="startChat">
            <template #icon>
              <n-icon><ChatbubbleOutline /></n-icon>
            </template>
            发起聊天
          </n-button>
          <n-button size="small" @click="editCustomer">
            <template #icon>
              <n-icon><CreateOutline /></n-icon>
            </template>
            编辑信息
          </n-button>
        </div>
      </div>
      
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">手机号码</div>
          <div class="info-value">{{ customer.phone || '-' }}</div>
        </div>
        
        <div class="info-item">
          <div class="info-label">客户来源</div>
          <div class="info-value">{{ getSourceText(customer.source) }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">注册时间</div>
          <div class="info-value">{{ customer.registerTime }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">最后活跃</div>
          <div class="info-value">{{ customer.lastActiveTime }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">客户价值</div>
          <div class="info-value">{{ customer.customerValue > 0 ? `¥${formatNumber(customer.customerValue)}` : '-' }}</div>
        </div>
      </div>
    </n-card>

    <!-- 浏览轨迹 -->
    <n-card title="浏览轨迹" class="tracking-card">
      <div class="tracking-stats">
        <div class="stat-item">
          <div class="stat-value">{{ customer.totalViews }}</div>
          <div class="stat-label">总浏览量</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ customer.todayViews }}</div>
          <div class="stat-label">今日浏览</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ trackingData.avgDuration }}分钟</div>
          <div class="stat-label">平均停留</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ trackingData.bounceRate }}%</div>
          <div class="stat-label">跳出率</div>
        </div>
      </div>
      
      <div class="tracking-chart">
        <div ref="trackingChartRef" class="chart"></div>
      </div>
      
      <div class="tracking-list">
        <div class="list-header">
          <span>最近浏览记录</span>
          <n-button size="small" text @click="showAllTracking">
            查看全部
          </n-button>
        </div>
        <div class="tracking-items">
          <div
            v-for="item in trackingData.recentViews"
            :key="item.id"
            class="tracking-item"
          >
            <div class="tracking-icon">
              <n-icon size="16" :color="getPageTypeColor(item.pageType)">
                <component :is="getPageTypeIcon(item.pageType)" />
              </n-icon>
            </div>
            <div class="tracking-content">
              <div class="tracking-page">{{ item.pageTitle }}</div>
              <div class="tracking-url">{{ item.pageUrl }}</div>
            </div>
            <div class="tracking-meta">
              <div class="tracking-duration">{{ item.duration }}分钟</div>
              <div class="tracking-time">{{ item.viewTime }}</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 互动记录 -->
    <n-card title="互动记录" class="interaction-card">
      <div class="interaction-tabs">
        <n-tabs v-model:value="activeTab" type="line">
          <n-tab-pane name="messages" tab="消息记录">
            <div class="messages-list">
              <div
                v-for="message in interactionData.messages"
                :key="message.id"
                class="message-item"
                :class="message.type"
              >
                <div class="message-avatar">
                  <n-avatar
                    :size="32"
                    :src="message.type === 'received' ? customer.avatar : '/admin-avatar.png'"
                  />
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="message-sender">{{ message.sender }}</span>
                    <span class="message-time">{{ message.time }}</span>
                  </div>
                  <div class="message-body">
                    <div v-if="message.contentType === 'text'" class="text-message">
                      {{ message.content }}
                    </div>
                    <div v-else-if="message.contentType === 'image'" class="image-message">
                      <img :src="message.content" alt="图片" />
                    </div>
                    <div v-else-if="message.contentType === 'file'" class="file-message">
                      <n-icon><DocumentOutline /></n-icon>
                      <span>{{ message.fileName }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>
          
          <n-tab-pane name="shares" tab="分享记录">
            <div class="shares-list">
              <div
                v-for="share in interactionData.shares"
                :key="share.id"
                class="share-item"
              >
                <div class="share-icon">
                  <n-icon size="20" color="#18a058"><ShareOutline /></n-icon>
                </div>
                <div class="share-content">
                  <div class="share-title">{{ share.title }}</div>
                  <div class="share-url">{{ share.url }}</div>
                  <div class="share-stats">
                    <span>分享时间：{{ share.shareTime }}</span>
                    <span>浏览次数：{{ share.viewCount }}</span>
                    <span>转化次数：{{ share.conversionCount }}</span>
                  </div>
                </div>
                <div class="share-status">
                  <n-tag :type="share.status === 'active' ? 'success' : 'default'">
                    {{ share.status === 'active' ? '有效' : '失效' }}
                  </n-tag>
                </div>
              </div>
            </div>
          </n-tab-pane>
          
          <n-tab-pane name="activities" tab="活动参与">
            <div class="activities-list">
              <div
                v-for="activity in interactionData.activities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <n-icon size="20" :color="getActivityTypeColor(activity.type)">
                    <component :is="getActivityTypeIcon(activity.type)" />
                  </n-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-desc">{{ activity.description }}</div>
                  <div class="activity-time">参与时间：{{ activity.participateTime }}</div>
                </div>
                <div class="activity-result">
                  <n-tag :type="getActivityResultType(activity.result)">
                    {{ getActivityResultText(activity.result) }}
                  </n-tag>
                </div>
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </n-card>

    <!-- 转化分析 -->
    <n-card title="转化分析" class="conversion-card">
      <div class="conversion-funnel">
        <div class="funnel-stage" v-for="stage in conversionData.funnel" :key="stage.name">
          <div class="stage-bar" :style="{ width: stage.percentage + '%' }">
            <div class="stage-label">{{ stage.name }}</div>
            <div class="stage-value">{{ stage.count }}</div>
          </div>
          <div class="stage-percentage">{{ stage.percentage }}%</div>
        </div>
      </div>
      
      <div class="conversion-timeline">
        <div class="timeline-header">
          <span>转化时间线</span>
        </div>
        <div class="timeline-items">
          <div
            v-for="event in conversionData.timeline"
            :key="event.id"
            class="timeline-item"
          >
            <div class="timeline-dot" :class="event.type"></div>
            <div class="timeline-content">
              <div class="timeline-title">{{ event.title }}</div>
              <div class="timeline-desc">{{ event.description }}</div>
              <div class="timeline-time">{{ event.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 操作记录 -->
    <n-card title="操作记录" class="operation-card">
      <div class="operation-list">
        <div
          v-for="operation in operationData"
          :key="operation.id"
          class="operation-item"
        >
          <div class="operation-icon">
            <n-icon size="16" :color="getOperationTypeColor(operation.type)">
              <component :is="getOperationTypeIcon(operation.type)" />
            </n-icon>
          </div>
          <div class="operation-content">
            <div class="operation-title">{{ operation.title }}</div>
            <div class="operation-desc">{{ operation.description }}</div>
          </div>
          <div class="operation-meta">
            <div class="operation-operator">{{ operation.operator }}</div>
            <div class="operation-time">{{ operation.time }}</div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NAvatar, NTag, NButton, NIcon, NTabs, NTabPane, useMessage
} from 'naive-ui'
import {
  ChatbubbleOutline, CreateOutline, DocumentOutline, ShareOutline,
  GiftOutline, TrophyOutline, TicketOutline, PersonOutline,
  CreateOutline as EditOutline, EyeOutline, PhonePortraitOutline,
  MailOutline, TimeOutline, DocumentTextOutline, SettingsOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface Customer {
  id: string
  wechatId: string
  nickname: string
  avatar: string
  phone?: string

  status: string
  source: string
  tags: string[]
  registerTime: string
  lastActiveTime: string
  totalViews: number
  todayViews: number
  conversionStatus: string
  customerValue: number
}

const props = defineProps<{
  customer: Customer
}>()

const emit = defineEmits<{
  update: []
  close: []
}>()

const message = useMessage()
const activeTab = ref('messages')
const trackingChartRef = ref<HTMLElement>()

// 浏览轨迹数据
const trackingData = reactive({
  avgDuration: 8.5,
  bounceRate: 25.8,
  recentViews: [
    {
      id: '1',
      pageTitle: '产品介绍页',
      pageUrl: '/products/crm-system',
      pageType: 'product',
      duration: 12,
      viewTime: '2024-01-20 15:30:00'
    },
    {
      id: '2',
      pageTitle: '价格方案页',
      pageUrl: '/pricing',
      pageType: 'pricing',
      duration: 8,
      viewTime: '2024-01-20 15:18:00'
    },
    {
      id: '3',
      pageTitle: '案例展示页',
      pageUrl: '/cases',
      pageType: 'case',
      duration: 15,
      viewTime: '2024-01-20 14:45:00'
    },
    {
      id: '4',
      pageTitle: '联系我们页',
      pageUrl: '/contact',
      pageType: 'contact',
      duration: 5,
      viewTime: '2024-01-20 14:30:00'
    }
  ]
})

// 互动记录数据
const interactionData = reactive({
  messages: [
    {
      id: '1',
      type: 'received',
      sender: props.customer.nickname,
      content: '你好，我想了解一下你们的CRM系统',
      contentType: 'text',
      fileName: '',
      time: '2024-01-20 15:45:00'
    },
    {
      id: '2',
      type: 'sent',
      sender: '客服小王',
      content: '您好！很高兴为您介绍我们的CRM系统，请问您主要关注哪些功能呢？',
      contentType: 'text',
      fileName: '',
      time: '2024-01-20 15:46:00'
    },
    {
      id: '3',
      type: 'received',
      sender: props.customer.nickname,
      content: '主要想了解客户管理和销售跟踪功能',
      contentType: 'text',
      fileName: '',
      time: '2024-01-20 15:47:00'
    }
  ],
  shares: [
    {
      id: '1',
      title: 'CRM系统产品介绍',
      url: 'https://example.com/share/crm-intro?ref=wx001',
      shareTime: '2024-01-19 10:30:00',
      viewCount: 15,
      conversionCount: 3,
      status: 'active'
    },
    {
      id: '2',
      title: '限时优惠活动',
      url: 'https://example.com/share/promotion?ref=wx001',
      shareTime: '2024-01-18 14:20:00',
      viewCount: 8,
      conversionCount: 1,
      status: 'expired'
    }
  ],
  activities: [
    {
      id: '1',
      type: 'lottery',
      title: '新年抽奖活动',
      description: '参与新年抽奖，有机会获得免费试用资格',
      participateTime: '2024-01-15 16:20:00',
      result: 'won'
    },
    {
      id: '2',
      type: 'survey',
      title: '产品需求调研',
      description: '参与产品需求调研问卷',
      participateTime: '2024-01-12 09:15:00',
      result: 'completed'
    }
  ]
})

// 转化分析数据
const conversionData = reactive({
  funnel: [
    { name: '首次访问', count: 1, percentage: 100 },
    { name: '深度浏览', count: 1, percentage: 100 },
    { name: '留下联系方式', count: 1, percentage: 100 },
    { name: '产品咨询', count: 1, percentage: 100 },
    { name: '商务洽谈', count: 1, percentage: 100 },
    { name: '成功转化', count: 1, percentage: 100 }
  ],
  timeline: [
    {
      id: '1',
      type: 'visit',
      title: '首次访问',
      description: '通过二维码扫描首次访问网站',
      time: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      type: 'engagement',
      title: '深度浏览',
      description: '浏览了产品介绍、价格方案等多个页面',
      time: '2024-01-15 10:45:00'
    },
    {
      id: '3',
      type: 'lead',
      title: '留下联系方式',
      description: '填写了联系表单，留下手机号和邮箱',
      time: '2024-01-15 11:20:00'
    },
    {
      id: '4',
      type: 'consultation',
      title: '产品咨询',
      description: '通过微信主动咨询产品功能和价格',
      time: '2024-01-18 14:30:00'
    },
    {
      id: '5',
      type: 'negotiation',
      title: '商务洽谈',
      description: '安排了线上演示和商务洽谈',
      time: '2024-01-20 15:00:00'
    }
  ]
})

// 操作记录数据
const operationData = ref([
  {
    id: '1',
    type: 'create',
    title: '创建客户档案',
    description: '系统自动创建客户档案',
    operator: '系统',
    time: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    type: 'update',
    title: '更新客户信息',
    description: '更新了客户的手机号和邮箱信息',
    operator: '客服小王',
    time: '2024-01-15 11:25:00'
  },
  {
    id: '3',
    type: 'tag',
    title: '添加客户标签',
    description: '添加了"潜在客户"和"VIP"标签',
    operator: '销售小李',
    time: '2024-01-18 09:15:00'
  },
  {
    id: '4',
    type: 'assign',
    title: '分配销售人员',
    description: '将客户分配给销售小李跟进',
    operator: '销售经理',
    time: '2024-01-18 14:20:00'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getSourceText = (source: string) => {
  const sourceMap = {
    qr_code: '二维码',
    share_link: '分享链接',
    friend_invite: '好友邀请',
    group_chat: '群聊',
    search: '搜索添加'
  }
  return sourceMap[source as keyof typeof sourceMap] || source
}

const getPageTypeColor = (type: string) => {
  const colorMap = {
    product: '#2080f0',
    pricing: '#18a058',
    case: '#f0a020',
    contact: '#d03050'
  }
  return colorMap[type as keyof typeof colorMap] || '#666'
}

const getPageTypeIcon = (type: string) => {
  const iconMap = {
    product: DocumentTextOutline,
    pricing: TicketOutline,
    case: TrophyOutline,
    contact: PhonePortraitOutline
  }
  return iconMap[type as keyof typeof iconMap] || DocumentOutline
}

const getActivityTypeColor = (type: string) => {
  const colorMap = {
    lottery: '#f0a020',
    survey: '#2080f0',
    promotion: '#d03050'
  }
  return colorMap[type as keyof typeof colorMap] || '#666'
}

const getActivityTypeIcon = (type: string) => {
  const iconMap = {
    lottery: GiftOutline,
    survey: DocumentTextOutline,
    promotion: TicketOutline
  }
  return iconMap[type as keyof typeof iconMap] || DocumentOutline
}

const getActivityResultType = (result: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'error'> = {
    won: 'success',
    completed: 'info',
    failed: 'error'
  }
  return typeMap[result] || 'default'
}

const getActivityResultText = (result: string) => {
  const textMap = {
    won: '中奖',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[result as keyof typeof textMap] || result
}

const getOperationTypeColor = (type: string) => {
  const colorMap = {
    create: '#18a058',
    update: '#2080f0',
    tag: '#f0a020',
    assign: '#d03050'
  }
  return colorMap[type as keyof typeof colorMap] || '#666'
}

const getOperationTypeIcon = (type: string) => {
  const iconMap = {
    create: PersonOutline,
    update: EditOutline,
    tag: TicketOutline,
    assign: SettingsOutline
  }
  return iconMap[type as keyof typeof iconMap] || DocumentOutline
}

const startChat = () => {
  message.info('发起聊天功能开发中')
}

const editCustomer = () => {
  message.info('编辑客户功能开发中')
}

const showAllTracking = () => {
  message.info('查看全部浏览记录功能开发中')
}

const initTrackingChart = () => {
  if (trackingChartRef.value) {
    const chart = echarts.init(trackingChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [12, 8, 15, 20, 18, 25],
        smooth: true,
        itemStyle: { color: '#2080f0' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(32, 128, 240, 0.3)' },
            { offset: 1, color: 'rgba(32, 128, 240, 0.1)' }
          ])
        }
      }]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initTrackingChart()
  })
})
</script>

<style scoped>
.customer-detail-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card {
  margin-bottom: 16px;
}

.customer-header {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 24px;
}

.customer-avatar {
  position: relative;
}

.status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.active {
  background: #18a058;
}

.status-indicator.potential {
  background: #f0a020;
}

.status-indicator.inactive {
  background: #909399;
}

.status-indicator.lost {
  background: #d03050;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.customer-id {
  color: #666;
  margin-bottom: 8px;
}

.customer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.customer-actions {
  display: flex;
  gap: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #1a1a1a;
  font-weight: 500;
}

.tracking-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.tracking-chart {
  height: 200px;
  margin-bottom: 16px;
}

.chart {
  width: 100%;
  height: 100%;
}

.tracking-list {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.tracking-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tracking-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.tracking-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
}

.tracking-content {
  flex: 1;
}

.tracking-page {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.tracking-url {
  font-size: 12px;
  color: #666;
}

.tracking-meta {
  text-align: right;
}

.tracking-duration {
  font-weight: 500;
  color: #2080f0;
  margin-bottom: 2px;
}

.tracking-time {
  font-size: 12px;
  color: #666;
}

.interaction-tabs {
  margin-top: -16px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  gap: 8px;
}

.message-item.sent {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-sender {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.message-time {
  font-size: 10px;
  color: #999;
}

.message-body {
  background: #f0f0f0;
  padding: 8px 12px;
  border-radius: 12px;
  word-break: break-word;
}

.message-item.sent .message-body {
  background: #2080f0;
  color: white;
}

.text-message {
  line-height: 1.4;
}

.image-message img {
  max-width: 200px;
  border-radius: 6px;
}

.file-message {
  display: flex;
  gap: 8px;
  align-items: center;
}

.shares-list,
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.share-item,
.activity-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.share-icon,
.activity-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
}

.share-content,
.activity-content {
  flex: 1;
}

.share-title,
.activity-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.share-url {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.share-stats {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 16px;
}

.activity-desc,
.activity-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.conversion-funnel {
  margin-bottom: 24px;
}

.funnel-stage {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.stage-bar {
  height: 32px;
  background: linear-gradient(90deg, #2080f0, #87ceeb);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  min-width: 120px;
}

.stage-percentage {
  font-weight: 500;
  color: #2080f0;
}

.conversion-timeline {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.timeline-header {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.timeline-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 4px;
  flex-shrink: 0;
}

.timeline-dot.visit {
  background: #2080f0;
}

.timeline-dot.engagement {
  background: #18a058;
}

.timeline-dot.lead {
  background: #f0a020;
}

.timeline-dot.consultation {
  background: #d03050;
}

.timeline-dot.negotiation {
  background: #722ed1;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.timeline-desc {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 10px;
  color: #999;
}

.operation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.operation-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
}

.operation-content {
  flex: 1;
}

.operation-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.operation-desc {
  font-size: 12px;
  color: #666;
}

.operation-meta {
  text-align: right;
}

.operation-operator {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.operation-time {
  font-size: 10px;
  color: #999;
}
</style>