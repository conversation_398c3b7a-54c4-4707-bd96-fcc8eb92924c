<template>
  <div class="action-buttons">
    <n-space :size="8">
      <!-- 查看按钮 -->
      <n-button
        v-if="showView"
        type="info"
        size="small"
        secondary
        @click="$emit('view')"
        :loading="loading.view"
        class="action-btn view-btn"
      >
        <template #icon>
          <n-icon><EyeOutline /></n-icon>
        </template>
        {{ viewText }}
      </n-button>

      <!-- 编辑按钮 -->
      <n-button
        v-if="showEdit"
        type="warning"
        size="small"
        secondary
        @click="$emit('edit')"
        :loading="loading.edit"
        class="action-btn edit-btn"
      >
        <template #icon>
          <n-icon><CreateOutline /></n-icon>
        </template>
        {{ editText }}
      </n-button>

      <!-- 删除按钮 -->
      <n-button
        v-if="showDelete"
        type="error"
        size="small"
        secondary
        @click="handleDelete"
        :loading="loading.delete"
        class="action-btn delete-btn"
      >
        <template #icon>
          <n-icon><TrashOutline /></n-icon>
        </template>
        {{ deleteText }}
      </n-button>

      <!-- 自定义按钮 -->
      <n-button
        v-for="(action, index) in customActions"
        :key="index"
        :type="action.type || 'default'"
        size="small"
        :secondary="action.secondary !== false"
        @click="$emit('custom', action)"
        :loading="loading[action.key] || false"
        class="action-btn custom-btn"
      >
        <template #icon v-if="action.icon">
          <n-icon><component :is="action.icon" /></n-icon>
        </template>
        {{ action.text }}
      </n-button>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NButton, NSpace, NIcon, useDialog, useMessage } from 'naive-ui'
import { EyeOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'

interface CustomAction {
  key: string
  text: string
  type?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'
  icon?: any
  secondary?: boolean
}

interface Props {
  showView?: boolean
  showEdit?: boolean
  showDelete?: boolean
  viewText?: string
  editText?: string
  deleteText?: string
  deleteConfirmText?: string
  customActions?: CustomAction[]
  loading?: Record<string, boolean>
}

const props = withDefaults(defineProps<Props>(), {
  showView: true,
  showEdit: true,
  showDelete: true,
  viewText: '查看',
  editText: '编辑',
  deleteText: '删除',
  deleteConfirmText: '确定要删除这条记录吗？',
  customActions: () => [],
  loading: () => ({})
})

const emit = defineEmits<{
  view: []
  edit: []
  delete: []
  custom: [action: CustomAction]
}>()

const dialog = useDialog()
const message = useMessage()

const handleDelete = () => {
  dialog.warning({
    title: '确认删除',
    content: props.deleteConfirmText,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      emit('delete')
    }
  })
}
</script>

<style scoped>
.action-buttons {
  display: inline-flex;
  align-items: center;
}

.action-btn {
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  font-weight: 500;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.view-btn {
  --n-color: #f0f9ff;
  --n-color-hover: #e0f2fe;
  --n-color-pressed: #bae6fd;
  --n-text-color: #0369a1;
  --n-text-color-hover: #0284c7;
  --n-border-color: #7dd3fc;
}

.edit-btn {
  --n-color: #fffbeb;
  --n-color-hover: #fef3c7;
  --n-color-pressed: #fed7aa;
  --n-text-color: #d97706;
  --n-text-color-hover: #ea580c;
  --n-border-color: #fbbf24;
}

.delete-btn {
  --n-color: #fef2f2;
  --n-color-hover: #fee2e2;
  --n-color-pressed: #fecaca;
  --n-text-color: #dc2626;
  --n-text-color-hover: #ef4444;
  --n-border-color: #f87171;
}

.custom-btn {
  --n-color: #f8fafc;
  --n-color-hover: #f1f5f9;
  --n-color-pressed: #e2e8f0;
  --n-text-color: #475569;
  --n-text-color-hover: #334155;
  --n-border-color: #cbd5e1;
}
</style>