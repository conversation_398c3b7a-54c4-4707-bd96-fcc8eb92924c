import type { CustomerBasicInfo, CustomerFollowInfo, CustomerConstructionInfo } from '@/types/customer'
import { CustomerStage } from '@/types/customer'

export interface CustomerFullInfo {
  basicInfo: CustomerBasicInfo
  followInfo: CustomerFollowInfo[]
  constructionInfo?: CustomerConstructionInfo
}

/**
 * 客户状态管理器
 * 根据客户信息的完整度自动判断客户所处的业务阶段
 */
export class CustomerStageManager {
  /**
   * 根据客户信息自动计算客户阶段
   * @param customer 客户信息
   * @returns 客户阶段
   */
  static calculateStage(customer: CustomerFullInfo): CustomerStage {
    const { basicInfo, followInfo, constructionInfo } = customer

    // 检查基础信息完整度
    const hasBasicInfo = this.hasCompleteBasicInfo(basicInfo)
    
    // 检查跟进信息完整度
    const hasFollowInfo = this.hasFollowInfo(followInfo)
    
    // 检查是否已签约
    const isContracted = this.isContracted(followInfo)
    
    // 检查施工信息完整度
    const hasConstructionInfo = this.hasConstructionInfo(constructionInfo)
    
    // 检查是否已完工
    const isCompleted = this.isCompleted(constructionInfo)

    // 根据信息完整度判断阶段
    if (isCompleted) {
      return CustomerStage.COMPLETED
    }
    
    if (hasConstructionInfo) {
      return CustomerStage.CONSTRUCTION
    }
    
    if (isContracted) {
      return CustomerStage.CONTRACTED
    }
    
    if (hasFollowInfo) {
      return CustomerStage.FOLLOWING
    }
    
    if (hasBasicInfo) {
      return CustomerStage.POTENTIAL
    }
    
    return CustomerStage.POTENTIAL
  }

  /**
   * 检查基础信息是否完整
   * @param basicInfo 基础信息
   * @returns 是否完整
   */
  private static hasCompleteBasicInfo(basicInfo?: CustomerBasicInfo): boolean {
    if (!basicInfo) return false
    
    return !!(basicInfo.customerName && basicInfo.phone)
  }

  /**
   * 检查是否有跟进信息
   * @param followInfo 跟进信息数组
   * @returns 是否有跟进信息
   */
  private static hasFollowInfo(followInfo?: CustomerFollowInfo[]): boolean {
    return !!(followInfo && followInfo.length > 0)
  }

  /**
   * 检查是否已签约
   * @param followInfo 跟进信息数组
   * @returns 是否已签约
   */
  private static isContracted(followInfo?: CustomerFollowInfo[]): boolean {
    if (!followInfo || followInfo.length === 0) return false
    
    return followInfo.some(info => 
      info.contractTime || info.contractPackage
    )
  }

  /**
   * 检查是否有施工信息
   * @param constructionInfo 施工信息数组
   * @returns 是否有施工信息
   */
  private static hasConstructionInfo(constructionInfo?: CustomerConstructionInfo): boolean {
    return !!constructionInfo
  }

  /**
   * 检查是否已完工
   * @param constructionInfo 施工信息数组
   * @returns 是否已完工
   */
  private static isCompleted(constructionInfo?: CustomerConstructionInfo): boolean {
    if (!constructionInfo) return false
    
    return !!(constructionInfo.endTime && new Date(constructionInfo.endTime) <= new Date())
  }

  /**
   * 获取客户阶段的进度百分比
   * @param customer 客户信息
   * @returns 进度百分比 (0-100)
   */
  static getStageProgress(customer: CustomerFullInfo): number {
    const stage = this.calculateStage(customer)
    
    switch (stage) {
      case CustomerStage.POTENTIAL:
        return 20
      case CustomerStage.FOLLOWING:
        return 40
      case CustomerStage.CONTRACTED:
        return 60
      case CustomerStage.CONSTRUCTION:
        return 80
      case CustomerStage.COMPLETED:
        return 100
      default:
        return 0
    }
  }

  /**
   * 获取下一阶段的建议操作
   * @param customer 客户信息
   * @returns 建议操作列表
   */
  static getNextStageActions(customer: CustomerFullInfo): string[] {
    const stage = this.calculateStage(customer)
    
    switch (stage) {
      case CustomerStage.POTENTIAL:
        return [
          '完善客户基础信息',
          '安排首次跟进',
          '了解客户需求'
        ]
      case CustomerStage.FOLLOWING:
        return [
          '安排客户到店',
          '进行量房服务',
          '制定设计方案',
          '推进签约'
        ]
      case CustomerStage.CONTRACTED:
        return [
          '安排项目监理',
          '确定开工时间',
          '准备施工材料'
        ]
      case CustomerStage.CONSTRUCTION:
        return [
          '跟进施工进度',
          '质量检查',
          '准备验收'
        ]
      case CustomerStage.COMPLETED:
        return [
          '客户满意度调查',
          '售后服务跟进',
          '推荐新客户'
        ]
      default:
        return []
    }
  }

  /**
   * 检查客户信息的完整度
   * @param customer 客户信息
   * @returns 完整度信息
   */
  static checkCompleteness(customer: CustomerFullInfo) {
    const basicComplete = this.hasCompleteBasicInfo(customer.basicInfo)
    const hasFollow = this.hasFollowInfo(customer.followInfo)
    const hasConstruction = this.hasConstructionInfo(customer.constructionInfo)
    
    const missingFields: string[] = []
    
    if (!basicComplete) {
      if (!customer.basicInfo?.customerName) missingFields.push('客户称呼')
      if (!customer.basicInfo?.phone) missingFields.push('手机号')
    }
    
    return {
      basicComplete,
      hasFollow,
      hasConstruction,
      missingFields,
      completenessScore: this.getStageProgress(customer)
    }
  }
}

/**
 * 客户状态管理工具函数
 */
export const customerStageUtils = {
  /**
   * 计算客户阶段
   */
  calculateStage: CustomerStageManager.calculateStage.bind(CustomerStageManager),
  
  /**
   * 获取阶段进度
   */
  getProgress: CustomerStageManager.getStageProgress.bind(CustomerStageManager),
  
  /**
   * 获取下一步操作建议
   */
  getNextActions: CustomerStageManager.getNextStageActions.bind(CustomerStageManager),
  
  /**
   * 检查信息完整度
   */
  checkCompleteness: CustomerStageManager.checkCompleteness.bind(CustomerStageManager)
}