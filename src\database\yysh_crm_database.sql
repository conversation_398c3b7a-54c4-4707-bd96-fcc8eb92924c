-- ========================================
-- YYSH 客户关系管理系统 - 数据库结构
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 描述: 基于企业微信的客户管理系统数据库
-- ========================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `yysh_crm` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `yysh_crm`;

-- ========================================
-- 1. 用户管理表
-- ========================================

-- 用户表
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `work_wechat_id` varchar(100) NOT NULL COMMENT '企业微信用户ID',
  `name` varchar(50) NOT NULL COMMENT '用户姓名',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `role` enum('admin','manager','sales','designer') NOT NULL DEFAULT 'sales' COMMENT '角色：管理员/主管/销售/设计师',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常 0-禁用',
  `permissions` json DEFAULT NULL COMMENT '权限列表',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_work_wechat_id` (`work_wechat_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 部门表
CREATE TABLE `departments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '上级部门ID',
  `manager_id` bigint(20) DEFAULT NULL COMMENT '部门主管ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常 0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ========================================
-- 2. 客户管理表
-- ========================================

-- 客户表
CREATE TABLE `customers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(100) NOT NULL COMMENT '客户姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `wechat` varchar(100) DEFAULT NULL COMMENT '微信号',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `source` enum('online','referral','event','telemarketing','store','other') NOT NULL DEFAULT 'other' COMMENT '客户来源',
  `status` enum('potential','interested','deal','lost') NOT NULL DEFAULT 'potential' COMMENT '客户状态',
  `level` enum('A','B','C','D') NOT NULL DEFAULT 'C' COMMENT '客户等级',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `gender` enum('male','female','unknown') DEFAULT 'unknown' COMMENT '性别',
  `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否VIP客户',
  `is_high_value` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否高价值客户',
  `tags` json DEFAULT NULL COMMENT '客户标签',
  `remark` text DEFAULT NULL COMMENT '备注',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `team_id` bigint(20) DEFAULT NULL COMMENT '团队ID',
  `collaborators` json DEFAULT NULL COMMENT '协同跟进人员ID列表',
  `is_in_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在公海池',
  `pool_time` datetime DEFAULT NULL COMMENT '进入公海时间',
  `last_follow_time` datetime DEFAULT NULL COMMENT '最后跟进时间',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `deal_amount` decimal(10,2) DEFAULT 0.00 COMMENT '成交金额',
  `deal_time` datetime DEFAULT NULL COMMENT '成交时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_status` (`status`),
  KEY `idx_level` (`level`),
  KEY `idx_source` (`source`),
  KEY `idx_is_in_pool` (`is_in_pool`),
  KEY `idx_last_follow_time` (`last_follow_time`),
  KEY `idx_next_follow_time` (`next_follow_time`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 客户标签表
CREATE TABLE `customer_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `category` varchar(50) DEFAULT 'custom' COMMENT '标签分类',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统标签',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签表';

-- 客户标签关联表
CREATE TABLE `customer_tag_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_tag` (`customer_id`,`tag_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签关联表';

-- ========================================
-- 3. 跟进管理表
-- ========================================

-- 跟进记录表
CREATE TABLE `follow_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `user_id` bigint(20) NOT NULL COMMENT '跟进人ID',
  `type` enum('phone','wechat','email','visit','meeting','other') NOT NULL COMMENT '跟进方式',
  `stage` varchar(50) NOT NULL COMMENT '跟进阶段',
  `content` text NOT NULL COMMENT '跟进内容',
  `images` json DEFAULT NULL COMMENT '图片附件',
  `result` enum('effective','invalid','pending') NOT NULL COMMENT '跟进结果',
  `result_detail` text DEFAULT NULL COMMENT '结果详情',
  `has_next_plan` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有下次计划',
  `next_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `next_content` text DEFAULT NULL COMMENT '下次跟进内容',
  `duration` int(11) DEFAULT NULL COMMENT '跟进时长(分钟)',
  `location` varchar(200) DEFAULT NULL COMMENT '跟进地点',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_stage` (`stage`),
  KEY `idx_result` (`result`),
  KEY `idx_next_time` (`next_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟进记录表';

-- 见面记录表
CREATE TABLE `meeting_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '见面记录ID',
  `follow_record_id` bigint(20) NOT NULL COMMENT '跟进记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `user_id` bigint(20) NOT NULL COMMENT '跟进人ID',
  `meeting_type` enum('measure','visit','external') NOT NULL COMMENT '见面类型：量房/到店/外见',
  `meeting_time` datetime NOT NULL COMMENT '见面时间',
  `designer_id` bigint(20) DEFAULT NULL COMMENT '设计师ID',
  `designer_name` varchar(50) DEFAULT NULL COMMENT '设计师姓名',
  `visit_count` int(11) DEFAULT 1 COMMENT '到店次数',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_follow_record_id` (`follow_record_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_designer_id` (`designer_id`),
  KEY `idx_meeting_type` (`meeting_type`),
  KEY `idx_meeting_time` (`meeting_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='见面记录表';

-- ========================================
-- 4. 公海池管理表
-- ========================================

-- 公海池记录表
CREATE TABLE `customer_pool_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `action` enum('release','claim') NOT NULL COMMENT '操作类型：释放/领取',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `to_user_id` bigint(20) DEFAULT NULL COMMENT '新负责人ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `auto_release` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动释放',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_from_user_id` (`from_user_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公海池记录表';

-- ========================================
-- 5. 营销工具表
-- ========================================

-- 营销链接表
CREATE TABLE `marketing_links` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `title` varchar(200) NOT NULL COMMENT '链接标题',
  `description` text DEFAULT NULL COMMENT '链接描述',
  `url` varchar(1000) NOT NULL COMMENT '目标URL',
  `short_url` varchar(200) DEFAULT NULL COMMENT '短链接',
  `qr_code` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `category` varchar(50) DEFAULT 'general' COMMENT '分类',
  `tags` json DEFAULT NULL COMMENT '标签',
  `click_count` int(11) NOT NULL DEFAULT 0 COMMENT '点击次数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用 0-禁用',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_url` (`short_url`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销链接表';

-- 链接访问记录表
CREATE TABLE `link_access_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `link_id` bigint(20) NOT NULL COMMENT '链接ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `referer` varchar(1000) DEFAULT NULL COMMENT '来源页面',
  `access_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_link_id` (`link_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_access_time` (`access_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='链接访问记录表';

-- ========================================
-- 6. 数据分析表
-- ========================================

-- 数据统计表
CREATE TABLE `analytics_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '客户总数',
  `new_customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '新增客户数',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `deal_count` int(11) NOT NULL DEFAULT 0 COMMENT '成交客户数',
  `deal_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成交金额',
  `pool_claim_count` int(11) NOT NULL DEFAULT 0 COMMENT '公海领取数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据统计表';

-- ========================================
-- 7. 系统配置表
-- ========================================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统配置',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE `operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) NOT NULL COMMENT '操作用户ID',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型',
  `target_id` bigint(20) DEFAULT NULL COMMENT '目标ID',
  `content` text DEFAULT NULL COMMENT '操作内容',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- ========================================
-- 8. 外键约束
-- ========================================

-- 用户表外键
ALTER TABLE `users` ADD CONSTRAINT `fk_users_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

-- 部门表外键
ALTER TABLE `departments` ADD CONSTRAINT `fk_departments_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;
ALTER TABLE `departments` ADD CONSTRAINT `fk_departments_manager` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- 客户表外键
ALTER TABLE `customers` ADD CONSTRAINT `fk_customers_owner` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT;

-- 客户标签关联表外键
ALTER TABLE `customer_tag_relations` ADD CONSTRAINT `fk_ctr_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;
ALTER TABLE `customer_tag_relations` ADD CONSTRAINT `fk_ctr_tag` FOREIGN KEY (`tag_id`) REFERENCES `customer_tags` (`id`) ON DELETE CASCADE;

-- 跟进记录表外键
ALTER TABLE `follow_records` ADD CONSTRAINT `fk_follow_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;
ALTER TABLE `follow_records` ADD CONSTRAINT `fk_follow_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT;

-- 见面记录表外键
ALTER TABLE `meeting_records` ADD CONSTRAINT `fk_meeting_follow` FOREIGN KEY (`follow_record_id`) REFERENCES `follow_records` (`id`) ON DELETE CASCADE;
ALTER TABLE `meeting_records` ADD CONSTRAINT `fk_meeting_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;
ALTER TABLE `meeting_records` ADD CONSTRAINT `fk_meeting_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT;
ALTER TABLE `meeting_records` ADD CONSTRAINT `fk_meeting_designer` FOREIGN KEY (`designer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- 公海池记录表外键
ALTER TABLE `customer_pool_records` ADD CONSTRAINT `fk_pool_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;
ALTER TABLE `customer_pool_records` ADD CONSTRAINT `fk_pool_from_user` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `customer_pool_records` ADD CONSTRAINT `fk_pool_to_user` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- 营销链接表外键
ALTER TABLE `marketing_links` ADD CONSTRAINT `fk_links_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT;

-- 链接访问记录表外键
ALTER TABLE `link_access_logs` ADD CONSTRAINT `fk_access_link` FOREIGN KEY (`link_id`) REFERENCES `marketing_links` (`id`) ON DELETE CASCADE;
ALTER TABLE `link_access_logs` ADD CONSTRAINT `fk_access_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `link_access_logs` ADD CONSTRAINT `fk_access_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL;

-- 数据统计表外键
ALTER TABLE `analytics_stats` ADD CONSTRAINT `fk_stats_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- ========================================
-- 9. 初始数据
-- ========================================

-- 插入默认部门
INSERT INTO `departments` (`id`, `name`, `parent_id`, `sort_order`) VALUES
(1, '销售部', NULL, 1),
(2, '设计部', NULL, 2),
(3, '管理部', NULL, 3);

-- 插入系统标签
INSERT INTO `customer_tags` (`name`, `color`, `category`, `is_system`, `created_by`) VALUES
('重要客户', '#f50', 'level', 1, 1),
('高价值', '#fa8c16', 'level', 1, 1),
('决策者', '#52c41a', 'role', 1, 1),
('技术专家', '#1890ff', 'role', 1, 1),
('价格敏感', '#722ed1', 'behavior', 1, 1),
('服务导向', '#eb2f96', 'behavior', 1, 1),
('创新型', '#13c2c2', 'type', 1, 1),
('传统型', '#faad14', 'type', 1, 1),
('大客户', '#f5222d', 'size', 1, 1),
('中小企业', '#fa541c', 'size', 1, 1),
('个人用户', '#fadb14', 'size', 1, 1),
('政府机构', '#a0d911', 'size', 1, 1);

-- 插入系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('auto_pool_days', '30', 'number', '客户自动进入公海天数', 1),
('max_customer_per_user', '500', 'number', '每个销售最大客户数', 1),
('follow_reminder_hours', '24', 'number', '跟进提醒提前小时数', 1),
('high_value_amount', '100000', 'number', '高价值客户金额阈值', 1),
('work_wechat_corp_id', '', 'string', '企业微信CorpID', 1),
('work_wechat_agent_id', '', 'string', '企业微信AgentID', 1),
('work_wechat_secret', '', 'string', '企业微信Secret', 1),
('system_name', 'YYSH客户管理系统', 'string', '系统名称', 1),
('company_name', '江西有优生活装饰集团有限公司', 'string', '公司名称', 1),
('contact_phone', '18079803131', 'string', '联系电话', 1);

-- ========================================
-- 10. 存储过程和函数
-- ========================================

DELIMITER //

-- 自动释放客户到公海的存储过程
CREATE PROCEDURE `AutoReleaseCustomersToPool`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE customer_id BIGINT;
    DECLARE owner_id BIGINT;
    DECLARE auto_pool_days INT DEFAULT 30;
    
    -- 获取配置的自动进入公海天数
    SELECT CAST(config_value AS UNSIGNED) INTO auto_pool_days 
    FROM system_configs 
    WHERE config_key = 'auto_pool_days';
    
    -- 声明游标
    DECLARE customer_cursor CURSOR FOR
        SELECT id, owner_id
        FROM customers
        WHERE is_in_pool = 0
        AND last_follow_time IS NOT NULL
        AND last_follow_time < DATE_SUB(NOW(), INTERVAL auto_pool_days DAY);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开启事务
    START TRANSACTION;
    
    OPEN customer_cursor;
    
    read_loop: LOOP
        FETCH customer_cursor INTO customer_id, owner_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新客户状态为公海
        UPDATE customers 
        SET is_in_pool = 1, 
            pool_time = NOW(),
            owner_id = 0
        WHERE id = customer_id;
        
        -- 记录公海操作
        INSERT INTO customer_pool_records (customer_id, action, from_user_id, reason, auto_release)
        VALUES (customer_id, 'release', owner_id, '超过跟进期限自动释放', 1);
        
    END LOOP;
    
    CLOSE customer_cursor;
    
    -- 提交事务
    COMMIT;
    
END //

-- 计算用户统计数据的存储过程
CREATE PROCEDURE `CalculateUserStats`(IN target_user_id BIGINT, IN target_date DATE)
BEGIN
    DECLARE customer_count INT DEFAULT 0;
    DECLARE new_customer_count INT DEFAULT 0;
    DECLARE follow_count INT DEFAULT 0;
    DECLARE deal_count INT DEFAULT 0;
    DECLARE deal_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE pool_claim_count INT DEFAULT 0;
    
    -- 计算客户总数
    SELECT COUNT(*) INTO customer_count
    FROM customers
    WHERE owner_id = target_user_id AND is_in_pool = 0;
    
    -- 计算新增客户数
    SELECT COUNT(*) INTO new_customer_count
    FROM customers
    WHERE owner_id = target_user_id 
    AND DATE(created_at) = target_date;
    
    -- 计算跟进次数
    SELECT COUNT(*) INTO follow_count
    FROM follow_records
    WHERE user_id = target_user_id 
    AND DATE(created_at) = target_date;
    
    -- 计算成交客户数和金额
    SELECT COUNT(*), IFNULL(SUM(deal_amount), 0) INTO deal_count, deal_amount
    FROM customers
    WHERE owner_id = target_user_id 
    AND status = 'deal'
    AND DATE(deal_time) = target_date;
    
    -- 计算公海领取数
    SELECT COUNT(*) INTO pool_claim_count
    FROM customer_pool_records
    WHERE to_user_id = target_user_id 
    AND action = 'claim'
    AND DATE(created_at) = target_date;
    
    -- 插入或更新统计数据
    INSERT INTO analytics_stats (user_id, date, customer_count, new_customer_count, follow_count, deal_count, deal_amount, pool_claim_count)
    VALUES (target_user_id, target_date, customer_count, new_customer_count, follow_count, deal_count, deal_amount, pool_claim_count)
    ON DUPLICATE KEY UPDATE
        customer_count = VALUES(customer_count),
        new_customer_count = VALUES(new_customer_count),
        follow_count = VALUES(follow_count),
        deal_count = VALUES(deal_count),
        deal_amount = VALUES(deal_amount),
        pool_claim_count = VALUES(pool_claim_count),
        updated_at = NOW();
        
END //

DELIMITER ;

-- ========================================
-- 11. 触发器
-- ========================================

DELIMITER //

-- 客户跟进后更新最后跟进时间
CREATE TRIGGER `update_customer_follow_time` 
AFTER INSERT ON `follow_records`
FOR EACH ROW
BEGIN
    UPDATE customers 
    SET last_follow_time = NEW.created_at,
        follow_count = follow_count + 1,
        next_follow_time = NEW.next_time
    WHERE id = NEW.customer_id;
END //

-- 客户成交后更新状态
CREATE TRIGGER `update_customer_deal_status`
AFTER UPDATE ON `customers`
FOR EACH ROW
BEGIN
    IF NEW.status = 'deal' AND OLD.status != 'deal' THEN
        UPDATE customers 
        SET deal_time = NOW()
        WHERE id = NEW.id;
    END IF;
END //

DELIMITER ;

-- ========================================
-- 12. 视图
-- ========================================

-- 客户详情视图
CREATE VIEW `v_customer_details` AS
SELECT 
    c.*,
    u.name as owner_name,
    u.avatar as owner_avatar,
    d.name as department_name,
    (SELECT COUNT(*) FROM follow_records WHERE customer_id = c.id) as total_follows,
    (SELECT created_at FROM follow_records WHERE customer_id = c.id ORDER BY created_at DESC LIMIT 1) as last_follow_time_detail
FROM customers c
LEFT JOIN users u ON c.owner_id = u.id
LEFT JOIN departments d ON u.department_id = d.id;

-- 跟进统计视图
CREATE VIEW `v_follow_stats` AS
SELECT 
    user_id,
    DATE(created_at) as follow_date,
    COUNT(*) as follow_count,
    COUNT(DISTINCT customer_id) as customer_count,
    SUM(CASE WHEN result = 'effective' THEN 1 ELSE 0 END) as effective_count,
    SUM(CASE WHEN result = 'invalid' THEN 1 ELSE 0 END) as invalid_count
FROM follow_records
GROUP BY user_id, DATE(created_at);

-- 公海池视图
CREATE VIEW `v_pool_customers` AS
SELECT 
    c.*,
    pr.created_at as pool_enter_time,
    pr.reason as pool_reason,
    u.name as last_owner_name
FROM customers c
LEFT JOIN customer_pool_records pr ON c.id = pr.customer_id AND pr.action = 'release'
LEFT JOIN users u ON pr.from_user_id = u.id
WHERE c.is_in_pool = 1
ORDER BY pr.created_at DESC;

-- ========================================
-- 数据库创建完成
-- ========================================

-- 显示创建结果
SELECT 'YYSH客户管理系统数据库创建完成！' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'yysh_crm';