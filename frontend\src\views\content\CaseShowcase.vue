<template>
  <div class="case-showcase">
    <div class="page-header">
      <h1 class="page-title">案例展示</h1>
      <p class="page-description">展示优秀的家装设计案例和效果图</p>
    </div>

    <n-grid :cols="1" :y-gap="20">
      <n-card title="案例管理" class="case-card">
        <template #header-extra>
          <n-space>
            <n-button type="primary" @click="handleAddCase">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              新增案例
            </n-button>
            <n-button @click="handleRefresh">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
        </template>

        <div class="case-grid">
          <div v-for="caseItem in caseList" :key="caseItem.id" class="case-item">
            <n-card :title="caseItem.title" class="case-item-card">
              <template #header-extra>
                <n-dropdown :options="getActionOptions(caseItem)" @select="handleAction">
                  <n-button text>
                    <template #icon>
                      <n-icon><EllipsisHorizontalOutline /></n-icon>
                    </template>
                  </n-button>
                </n-dropdown>
              </template>

              <div class="case-info">
                <div class="case-basic-info">
                  <p><strong>项目类型：</strong>{{ caseItem.projectType }}</p>
                  <p><strong>面积：</strong>{{ caseItem.area }}㎡</p>
                  <p><strong>风格：</strong>{{ caseItem.style }}</p>
                  <p><strong>预算：</strong>{{ caseItem.budget }}</p>
                </div>

                <div class="case-images">
                  <n-tabs type="line" size="small">
                    <n-tab-pane name="original" tab="原始户型图">
                      <div class="image-container">
                        <img 
                          :src="caseItem.images.original" 
                          :alt="`${caseItem.title} - 原始户型图`"
                          class="case-image"
                          @click="previewImage(caseItem.images.original)"
                        />
                      </div>
                    </n-tab-pane>
                    
                    <n-tab-pane name="layout" tab="布局设计图">
                      <div class="image-container">
                        <img 
                          :src="caseItem.images.layout" 
                          :alt="`${caseItem.title} - 布局设计图`"
                          class="case-image"
                          @click="previewImage(caseItem.images.layout)"
                        />
                      </div>
                    </n-tab-pane>
                    
                    <n-tab-pane name="living" tab="客厅效果图">
                      <div class="image-container">
                        <img 
                          :src="caseItem.images.livingRoom" 
                          :alt="`${caseItem.title} - 客厅效果图`"
                          class="case-image"
                          @click="previewImage(caseItem.images.livingRoom)"
                        />
                      </div>
                    </n-tab-pane>
                    
                    <n-tab-pane name="dining" tab="餐厅效果图">
                      <div class="image-container">
                        <img 
                          :src="caseItem.images.diningRoom" 
                          :alt="`${caseItem.title} - 餐厅效果图`"
                          class="case-image"
                          @click="previewImage(caseItem.images.diningRoom)"
                        />
                      </div>
                    </n-tab-pane>
                    
                    <n-tab-pane name="bedroom" tab="卧室效果图">
                      <div class="image-container">
                        <img 
                          :src="caseItem.images.bedroom" 
                          :alt="`${caseItem.title} - 卧室效果图`"
                          class="case-image"
                          @click="previewImage(caseItem.images.bedroom)"
                        />
                      </div>
                    </n-tab-pane>
                  </n-tabs>
                </div>

                <div class="case-description">
                  <h4>案例描述</h4>
                  <p>{{ caseItem.description }}</p>
                </div>

                <div class="case-tags">
                  <n-tag v-for="tag in caseItem.tags" :key="tag" type="info" size="small">
                    {{ tag }}
                  </n-tag>
                </div>
              </div>
            </n-card>
          </div>
        </div>
      </n-card>
    </n-grid>

    <!-- 图片预览模态框 -->
    <n-modal v-model:show="showImagePreview" preset="card" style="width: 80%; max-width: 800px;">
      <template #header>
        <span>图片预览</span>
      </template>
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
      </div>
    </n-modal>

    <!-- 新增/编辑案例模态框 -->
    <n-modal v-model:show="showCaseModal" preset="card" title="案例信息" style="width: 90%; max-width: 1000px;">
      <n-form
        ref="caseFormRef"
        :model="caseForm"
        :rules="caseRules"
        label-placement="left"
        label-width="120px"
      >
        <n-grid :cols="2" :x-gap="20">
          <n-grid-item>
            <n-form-item label="案例标题" path="title">
              <n-input v-model:value="caseForm.title" placeholder="请输入案例标题" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="项目类型" path="projectType">
              <n-select v-model:value="caseForm.projectType" :options="projectTypeOptions" placeholder="请选择项目类型" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="面积" path="area">
              <n-input-number v-model:value="caseForm.area" placeholder="请输入面积" style="width: 100%" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="装修风格" path="style">
              <n-select v-model:value="caseForm.style" :options="styleOptions" placeholder="请选择装修风格" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="预算" path="budget">
              <n-input v-model:value="caseForm.budget" placeholder="请输入预算" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="标签" path="tags">
              <n-dynamic-tags v-model:value="caseForm.tags" />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        
        <n-form-item label="案例描述" path="description">
          <n-input
            v-model:value="caseForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入案例描述"
          />
        </n-form-item>

        <n-divider>效果图上传</n-divider>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16">
          <n-grid-item>
            <n-form-item label="原始户型图">
              <n-upload
                :file-list="[]"
                :max="1"
                accept="image/*"
                @change="handleImageUpload('original', $event)"
              >
                <n-button>上传原始户型图</n-button>
              </n-upload>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="布局设计图">
              <n-upload
                :file-list="[]"
                :max="1"
                accept="image/*"
                @change="handleImageUpload('layout', $event)"
              >
                <n-button>上传布局设计图</n-button>
              </n-upload>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客厅效果图">
              <n-upload
                :file-list="[]"
                :max="1"
                accept="image/*"
                @change="handleImageUpload('livingRoom', $event)"
              >
                <n-button>上传客厅效果图</n-button>
              </n-upload>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="餐厅效果图">
              <n-upload
                :file-list="[]"
                :max="1"
                accept="image/*"
                @change="handleImageUpload('diningRoom', $event)"
              >
                <n-button>上传餐厅效果图</n-button>
              </n-upload>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="卧室效果图">
              <n-upload
                :file-list="[]"
                :max="1"
                accept="image/*"
                @change="handleImageUpload('bedroom', $event)"
              >
                <n-button>上传卧室效果图</n-button>
              </n-upload>
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showCaseModal = false">取消</n-button>
          <n-button type="primary" @click="handleSaveCase">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { AddOutline, RefreshOutline, EllipsisHorizontalOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui'

interface CaseItem {
  id: number
  title: string
  projectType: string
  area: number
  style: string
  budget: string
  description: string
  tags: string[]
  images: {
    original: string
    layout: string
    livingRoom: string
    diningRoom: string
    bedroom: string
  }
  createTime: string
  status: string
}

const message = useMessage()
const dialog = useDialog()

// 表单引用
const caseFormRef = ref<FormInst | null>(null)

// 案例列表
const caseList = ref<CaseItem[]>([
  {
    id: 1,
    title: '现代简约三居室',
    projectType: '住宅',
    area: 120,
    style: '现代简约',
    budget: '15-20万',
    description: '这是一套120平米的现代简约风格三居室设计，以简洁明快的线条和舒适的色彩搭配为主，营造出温馨而不失时尚的居住环境。',
    tags: ['现代', '简约', '三居室', '温馨'],
    images: {
      original: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=original%20apartment%20floor%20plan%20blueprint%20architectural%20drawing&image_size=landscape_4_3',
      layout: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20apartment%20layout%20design%20floor%20plan%20interior%20design&image_size=landscape_4_3',
      livingRoom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20minimalist%20living%20room%20interior%20design%20clean%20lines%20neutral%20colors&image_size=landscape_4_3',
      diningRoom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20dining%20room%20interior%20design%20elegant%20table%20chairs&image_size=landscape_4_3',
      bedroom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20bedroom%20interior%20design%20comfortable%20bed%20wardrobe&image_size=landscape_4_3'
    },
    createTime: '2024-01-15',
    status: '已发布'
  },
  {
    id: 2,
    title: '欧式古典别墅',
    projectType: '别墅',
    area: 280,
    style: '欧式古典',
    budget: '50-80万',
    description: '这是一套280平米的欧式古典风格别墅设计，采用华丽的装饰元素和精致的家具搭配，展现出贵族般的优雅气质。',
    tags: ['欧式', '古典', '别墅', '奢华'],
    images: {
      original: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=villa%20original%20floor%20plan%20blueprint%20architectural%20drawing&image_size=landscape_4_3',
      layout: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=european%20classical%20villa%20layout%20design%20floor%20plan&image_size=landscape_4_3',
      livingRoom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=european%20classical%20living%20room%20luxury%20furniture%20ornate%20decorations&image_size=landscape_4_3',
      diningRoom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=european%20classical%20dining%20room%20elegant%20chandelier%20wooden%20table&image_size=landscape_4_3',
      bedroom: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=european%20classical%20bedroom%20luxury%20bed%20ornate%20furniture&image_size=landscape_4_3'
    },
    createTime: '2024-01-10',
    status: '已发布'
  }
])

// 图片预览
const showImagePreview = ref(false)
const previewImageUrl = ref('')

// 案例表单
const showCaseModal = ref(false)
const caseForm = reactive({
  title: '',
  projectType: '',
  area: null as number | null,
  style: '',
  budget: '',
  description: '',
  tags: [] as string[],
  images: {
    original: '',
    layout: '',
    livingRoom: '',
    diningRoom: '',
    bedroom: ''
  }
})

// 表单验证规则
const caseRules: FormRules = {
  title: [
    { required: true, message: '请输入案例标题', trigger: 'blur' }
  ],
  projectType: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  area: [
    { required: true, message: '请输入面积', trigger: 'blur' }
  ],
  style: [
    { required: true, message: '请选择装修风格', trigger: 'change' }
  ],
  budget: [
    { required: true, message: '请输入预算', trigger: 'blur' }
  ]
}

// 选项数据
const projectTypeOptions = [
  { label: '住宅', value: '住宅' },
  { label: '别墅', value: '别墅' },
  { label: '公寓', value: '公寓' },
  { label: '商业空间', value: '商业空间' }
]

const styleOptions = [
  { label: '现代简约', value: '现代简约' },
  { label: '欧式古典', value: '欧式古典' },
  { label: '中式传统', value: '中式传统' },
  { label: '北欧风格', value: '北欧风格' },
  { label: '美式乡村', value: '美式乡村' },
  { label: '地中海', value: '地中海' }
]

// 方法
const handleAddCase = () => {
  resetCaseForm()
  showCaseModal.value = true
}

const handleRefresh = () => {
  message.success('数据已刷新')
}

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const getActionOptions = (caseItem: CaseItem) => [
  {
    label: '编辑',
    key: `edit-${caseItem.id}`,
    icon: () => h('n-icon', null, { default: () => h(CreateOutline) })
  },
  {
    label: '删除',
    key: `delete-${caseItem.id}`,
    icon: () => h('n-icon', null, { default: () => h(TrashOutline) })
  }
]

const handleAction = (key: string) => {
  const [action, id] = key.split('-')
  const caseId = parseInt(id)
  
  if (action === 'edit') {
    const caseItem = caseList.value.find(item => item.id === caseId)
    if (caseItem) {
      Object.assign(caseForm, caseItem)
      showCaseModal.value = true
    }
  } else if (action === 'delete') {
    dialog.warning({
      title: '确认删除',
      content: '确定要删除这个案例吗？此操作不可撤销。',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        const index = caseList.value.findIndex(item => item.id === caseId)
        if (index > -1) {
          caseList.value.splice(index, 1)
          message.success('案例删除成功')
        }
      }
    })
  }
}

const handleImageUpload = (type: string, { file }: { file: UploadFileInfo }) => {
  // 这里应该实现实际的图片上传逻辑
  // 暂时使用占位符
  if (file.file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        ;(caseForm.images as any)[type] = e.target.result as string
      }
    }
    reader.readAsDataURL(file.file)
  }
}

const handleSaveCase = async () => {
  try {
    await caseFormRef.value?.validate()
    
    // 这里应该调用API保存数据
    const newCase: CaseItem = {
      id: Date.now(),
      ...caseForm,
      area: caseForm.area || 0,
      createTime: new Date().toISOString().split('T')[0],
      status: '已发布'
    }
    
    caseList.value.unshift(newCase)
    showCaseModal.value = false
    message.success('案例保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const resetCaseForm = () => {
  Object.assign(caseForm, {
    title: '',
    projectType: '',
    area: null,
    style: '',
    budget: '',
    description: '',
    tags: [],
    images: {
      original: '',
      layout: '',
      livingRoom: '',
      diningRoom: '',
      bedroom: ''
    }
  })
}

// 初始化
onMounted(() => {
  // 加载案例数据
})
</script>

<style scoped>
.case-showcase {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.case-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
}

.case-item-card {
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.case-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.case-basic-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.case-basic-info p {
  margin: 0;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.case-images {
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: var(--n-color-target);
}

.case-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: cover;
  cursor: pointer;
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.case-image:hover {
  transform: scale(1.02);
}

.case-description h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.case-description p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: var(--n-text-color-2);
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}

/* 选项卡样式优化 */
:deep(.n-tabs .n-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.n-tabs .n-tab-pane) {
  padding: 16px;
}

:deep(.n-tabs-tab) {
  padding: 8px 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-showcase {
    padding: 16px;
  }
  
  .case-grid {
    grid-template-columns: 1fr;
  }
  
  .case-basic-info {
    grid-template-columns: 1fr;
  }
}
</style>