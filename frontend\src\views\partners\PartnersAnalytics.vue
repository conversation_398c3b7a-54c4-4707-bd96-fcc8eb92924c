<template>
  <div class="partners-analytics">

    <div class="filters">
      <n-space>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          placeholder="选择时间范围"
        />
        
        <n-select
          v-model:value="partnerFilter"
          placeholder="选择合伙人"
          clearable
          style="width: 200px"
          :options="partnerOptions"
        />
        
        <n-select
          v-model:value="levelFilter"
          placeholder="等级筛选"
          clearable
          style="width: 150px"
          :options="levelOptions"
        />
        
        <n-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshIcon />
          </template>
          刷新数据
        </n-button>
      </n-space>
    </div>

    <!-- 概览统计 -->
    <div class="overview-stats">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic 
              label="总推荐客户" 
              :value="overviewStats.totalCustomers"
              :value-style="{ color: '#18a058' }"
            >
              <template #suffix>
                <span class="text-sm text-gray-500">人</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic 
              label="成交客户" 
              :value="overviewStats.convertedCustomers"
              :value-style="{ color: '#2080f0' }"
            >
              <template #suffix>
                <span class="text-sm text-gray-500">人</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic 
              label="转化率" 
              :value="overviewStats.conversionRate"
              :value-style="{ color: '#f0a020' }"
            >
              <template #suffix>
                <span class="text-sm text-gray-500">%</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic 
              label="总积分发放" 
              :value="overviewStats.totalPoints"
              :value-style="{ color: '#d03050' }"
            >
              <template #suffix>
                <span class="text-sm text-gray-500">分</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <n-grid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 推荐客户趋势图 -->
      <n-grid-item>
        <n-card title="推荐客户趋势">
          <div ref="customerTrendChart" style="height: 300px;"></div>
        </n-card>
      </n-grid-item>
      
      <!-- 转化率分析 -->
      <n-grid-item>
        <n-card title="转化率分析">
          <div ref="conversionChart" style="height: 300px;"></div>
        </n-card>
      </n-grid-item>
      
      <!-- 合伙人等级分布 -->
      <n-grid-item>
        <n-card title="合伙人等级分布">
          <div ref="levelDistributionChart" style="height: 300px;"></div>
        </n-card>
      </n-grid-item>
      
      <!-- 积分发放统计 -->
      <n-grid-item>
        <n-card title="积分发放统计">
          <div ref="pointsChart" style="height: 300px;"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 合伙人排行榜 -->
    <n-card title="合伙人业绩排行" class="ranking-card">
      <n-tabs v-model:value="rankingTab" type="line">
        <n-tab-pane name="customers" tab="推荐客户数">
          <n-data-table
            :columns="rankingColumns"
            :data="customerRanking"
            :pagination="false"
            size="small"
          />
        </n-tab-pane>
        <n-tab-pane name="conversion" tab="转化率">
          <n-data-table
            :columns="conversionRankingColumns"
            :data="conversionRanking"
            :pagination="false"
            size="small"
          />
        </n-tab-pane>
        <n-tab-pane name="points" tab="积分获得">
          <n-data-table
            :columns="pointsRankingColumns"
            :data="pointsRanking"
            :pagination="false"
            size="small"
          />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h } from 'vue'
import {
  NCard,
  NGrid,
  NGridItem,
  NStatistic,
  NDatePicker,
  NSelect,
  NButton,
  NSpace,
  NDataTable,
  NTabs,
  NTabPane,
  NTag,
  NAvatar,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import { Refresh as RefreshIcon } from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface OverviewStats {
  totalCustomers: number
  convertedCustomers: number
  conversionRate: number
  totalPoints: number
}

interface RankingItem {
  id: string
  name: string
  avatar: string
  level: string
  customers: number
  converted: number
  conversionRate: number
  points: number
  rank: number
}

const message = useMessage()
const dateRange = ref<[number, number] | null>(null)
const partnerFilter = ref<string | null>(null)
const levelFilter = ref<string | null>(null)
const rankingTab = ref('customers')

const customerTrendChart = ref<HTMLElement>()
const conversionChart = ref<HTMLElement>()
const levelDistributionChart = ref<HTMLElement>()
const pointsChart = ref<HTMLElement>()

const levelOptions = [
  { label: '初级合伙人', value: 'junior' },
  { label: '中级合伙人', value: 'intermediate' },
  { label: '高级合伙人', value: 'senior' },
  { label: '金牌合伙人', value: 'gold' }
]

const partnerOptions = [
  { label: '张三', value: '1' },
  { label: '王五', value: '2' },
  { label: '赵六', value: '3' }
]

const overviewStats = ref<OverviewStats>({
  totalCustomers: 0,
  convertedCustomers: 0,
  conversionRate: 0,
  totalPoints: 0
})

const customerRanking = ref<RankingItem[]>([])
const conversionRanking = ref<RankingItem[]>([])
const pointsRanking = ref<RankingItem[]>([])

const getLevelTag = (level: string) => {
  const levelMap = {
    junior: { type: 'default', text: '初级' },
    intermediate: { type: 'info', text: '中级' },
    senior: { type: 'warning', text: '高级' },
    gold: { type: 'success', text: '金牌' }
  }
  const config = levelMap[level as keyof typeof levelMap]
  return h(NTag, { type: config.type as any, size: 'small' }, { default: () => config.text })
}

const rankingColumns: DataTableColumns<RankingItem> = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render: (row) => {
      const colors = ['#FFD700', '#C0C0C0', '#CD7F32']
      const color = row.rank <= 3 ? colors[row.rank - 1] : '#666'
      return h('span', { style: { color, fontWeight: 'bold' } }, `#${row.rank}`)
    }
  },
  {
    title: '合伙人',
    key: 'name',
    render: (row) => h('div', { class: 'flex items-center gap-2' }, [
      h(NAvatar, { size: 'small', src: row.avatar }),
      h('span', row.name)
    ])
  },
  {
    title: '等级',
    key: 'level',
    render: (row) => getLevelTag(row.level)
  },
  {
    title: '推荐客户',
    key: 'customers',
    render: (row) => `${row.customers}人`
  }
]

const conversionRankingColumns: DataTableColumns<RankingItem> = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render: (row) => {
      const colors = ['#FFD700', '#C0C0C0', '#CD7F32']
      const color = row.rank <= 3 ? colors[row.rank - 1] : '#666'
      return h('span', { style: { color, fontWeight: 'bold' } }, `#${row.rank}`)
    }
  },
  {
    title: '合伙人',
    key: 'name',
    render: (row) => h('div', { class: 'flex items-center gap-2' }, [
      h(NAvatar, { size: 'small', src: row.avatar }),
      h('span', row.name)
    ])
  },
  {
    title: '等级',
    key: 'level',
    render: (row) => getLevelTag(row.level)
  },
  {
    title: '转化率',
    key: 'conversionRate',
    render: (row) => `${row.conversionRate}%`
  },
  {
    title: '成交客户',
    key: 'converted',
    render: (row) => `${row.converted}人`
  }
]

const pointsRankingColumns: DataTableColumns<RankingItem> = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render: (row) => {
      const colors = ['#FFD700', '#C0C0C0', '#CD7F32']
      const color = row.rank <= 3 ? colors[row.rank - 1] : '#666'
      return h('span', { style: { color, fontWeight: 'bold' } }, `#${row.rank}`)
    }
  },
  {
    title: '合伙人',
    key: 'name',
    render: (row) => h('div', { class: 'flex items-center gap-2' }, [
      h(NAvatar, { size: 'small', src: row.avatar }),
      h('span', row.name)
    ])
  },
  {
    title: '等级',
    key: 'level',
    render: (row) => getLevelTag(row.level)
  },
  {
    title: '获得积分',
    key: 'points',
    render: (row) => `${row.points}分`
  }
]

const initCharts = () => {
  nextTick(() => {
    // 推荐客户趋势图
    if (customerTrendChart.value) {
      const chart1 = echarts.init(customerTrendChart.value)
      chart1.setOption({
        title: {
          text: '近30天推荐客户趋势',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['1月1日', '1月5日', '1月10日', '1月15日', '1月20日', '1月25日', '1月30日']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [12, 18, 25, 32, 28, 35, 42],
          type: 'line',
          smooth: true,
          itemStyle: { color: '#18a058' }
        }]
      })
    }

    // 转化率分析
    if (conversionChart.value) {
      const chart2 = echarts.init(conversionChart.value)
      chart2.setOption({
        title: {
          text: '各等级转化率对比',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        xAxis: {
          type: 'category',
          data: ['初级', '中级', '高级', '金牌']
        },
        yAxis: {
          type: 'value',
          axisLabel: { formatter: '{value}%' }
        },
        series: [{
          data: [25, 35, 45, 65],
          type: 'bar',
          itemStyle: { color: '#2080f0' }
        }]
      })
    }

    // 合伙人等级分布
    if (levelDistributionChart.value) {
      const chart3 = echarts.init(levelDistributionChart.value)
      chart3.setOption({
        title: {
          text: '合伙人等级分布',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: '等级分布',
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: '初级合伙人' },
            { value: 25, name: '中级合伙人' },
            { value: 20, name: '高级合伙人' },
            { value: 15, name: '金牌合伙人' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }

    // 积分发放统计
    if (pointsChart.value) {
      const chart4 = echarts.init(pointsChart.value)
      chart4.setOption({
        title: {
          text: '月度积分发放趋势',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['10月', '11月', '12月', '1月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [8500, 12000, 15600, 18200],
          type: 'bar',
          itemStyle: { color: '#f0a020' }
        }]
      })
    }
  })
}

const loadData = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    overviewStats.value = {
      totalCustomers: 156,
      convertedCustomers: 68,
      conversionRate: 43.6,
      totalPoints: 25600
    }
    
    const mockRankingData = [
      {
        id: '1',
        name: '张三',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=张三',
        level: 'gold',
        customers: 42,
        converted: 28,
        conversionRate: 66.7,
        points: 5600,
        rank: 1
      },
      {
        id: '2',
        name: '王五',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=王五',
        level: 'senior',
        customers: 35,
        converted: 18,
        conversionRate: 51.4,
        points: 3600,
        rank: 2
      },
      {
        id: '3',
        name: '赵六',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=赵六',
        level: 'intermediate',
        customers: 28,
        converted: 12,
        conversionRate: 42.9,
        points: 2400,
        rank: 3
      }
    ]
    
    customerRanking.value = [...mockRankingData].sort((a, b) => b.customers - a.customers)
    conversionRanking.value = [...mockRankingData].sort((a, b) => b.conversionRate - a.conversionRate)
    pointsRanking.value = [...mockRankingData].sort((a, b) => b.points - a.points)
    
  } catch (error) {
    message.error('加载数据失败')
  }
}

const refreshData = () => {
  loadData()
  initCharts()
  message.success('数据已刷新')
}

onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.partners-analytics {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filters {
  margin-bottom: 24px;
}

.overview-stats {
  margin-bottom: 24px;
}

.ranking-card {
  margin-top: 24px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 8px;
}

.text-sm {
  font-size: 14px;
}

.text-gray-500 {
  color: #6b7280;
}
</style>