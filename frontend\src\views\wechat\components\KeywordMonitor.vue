<template>
  <div class="keyword-monitor">
    <!-- 监控配置 -->
    <n-card title="监控配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>监控名称</label>
            <n-input
              v-model:value="config.name"
              placeholder="请输入监控名称"
            />
          </div>
          <div class="form-item">
            <label>监控状态</label>
            <n-switch v-model:value="config.enabled">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </n-switch>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>监控范围</label>
            <n-select
              v-model:value="config.scope"
              :options="scopeOptions"
              placeholder="选择监控范围"
            />
          </div>
          <div class="form-item">
            <label>触发阈值</label>
            <n-input-number
              v-model:value="config.threshold"
              :min="1"
              :max="100"
              placeholder="次数"
            >
              <template #suffix>次/小时</template>
            </n-input-number>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <label>监控群组</label>
            <n-select
              v-model:value="config.groups"
              :options="groupOptions"
              multiple
              placeholder="选择要监控的群组"
            />
          </div>
        </div>
      </div>
    </n-card>

    <!-- 关键词管理 -->
    <n-card title="关键词管理" class="keywords-card">
      <div class="keywords-section">
        <!-- 添加关键词 -->
        <div class="add-keyword">
          <div class="add-form">
            <n-input
              v-model:value="newKeyword.word"
              placeholder="输入关键词"
              @keyup.enter="addKeyword"
            />
            <n-select
              v-model:value="newKeyword.level"
              :options="levelOptions"
              placeholder="级别"
              style="width: 120px"
            />
            <n-select
              v-model:value="newKeyword.type"
              :options="typeOptions"
              placeholder="类型"
              style="width: 120px"
            />
            <n-button type="primary" @click="addKeyword">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              添加
            </n-button>
          </div>
          <div class="batch-import">
            <n-button @click="showBatchImport = true">
              <template #icon>
                <n-icon><DocumentTextOutline /></n-icon>
              </template>
              批量导入
            </n-button>
            <n-button @click="exportKeywords">
              <template #icon>
                <n-icon><DownloadOutline /></n-icon>
              </template>
              导出关键词
            </n-button>
          </div>
        </div>

        <!-- 关键词列表 -->
        <div class="keywords-list">
          <div class="list-header">
            <div class="header-actions">
              <n-input
                v-model:value="searchKeyword"
                placeholder="搜索关键词"
                clearable
                style="width: 200px"
              >
                <template #prefix>
                  <n-icon><SearchOutline /></n-icon>
                </template>
              </n-input>
              <n-select
                v-model:value="filterLevel"
                :options="[{ label: '全部级别', value: '' }, ...levelOptions]"
                placeholder="筛选级别"
                style="width: 120px"
              />
              <n-select
                v-model:value="filterType"
                :options="[{ label: '全部类型', value: '' }, ...typeOptions]"
                placeholder="筛选类型"
                style="width: 120px"
              />
            </div>
            <div class="batch-actions">
              <n-button
                size="small"
                @click="selectAll"
                :disabled="filteredKeywords.length === 0"
              >
                全选
              </n-button>
              <n-button
                size="small"
                @click="clearSelection"
                :disabled="selectedKeywords.length === 0"
              >
                取消选择
              </n-button>
              <n-button
                size="small"
                type="error"
                @click="batchDelete"
                :disabled="selectedKeywords.length === 0"
              >
                批量删除
              </n-button>
            </div>
          </div>

          <div class="keywords-table">
            <div class="table-header">
              <div class="col-checkbox">
                <n-checkbox
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @update:checked="toggleSelectAll"
                />
              </div>
              <div class="col-keyword">关键词</div>
              <div class="col-level">级别</div>
              <div class="col-type">类型</div>
              <div class="col-hits">命中次数</div>
              <div class="col-status">状态</div>
              <div class="col-actions">操作</div>
            </div>
            <div class="table-body">
              <div
                class="table-row"
                v-for="keyword in filteredKeywords"
                :key="keyword.id"
              >
                <div class="col-checkbox">
                  <n-checkbox
                    :checked="selectedKeywords.includes(keyword.id)"
                    @update:checked="toggleKeywordSelection(keyword.id)"
                  />
                </div>
                <div class="col-keyword">
                  <span class="keyword-text">{{ keyword.word }}</span>
                </div>
                <div class="col-level">
                  <n-tag :type="getLevelColor(keyword.level)">{{ getLevelText(keyword.level) }}</n-tag>
                </div>
                <div class="col-type">
                  <n-tag :type="getTypeColor(keyword.type)">{{ getTypeText(keyword.type) }}</n-tag>
                </div>
                <div class="col-hits">
                  <span class="hits-count">{{ keyword.hits }}</span>
                </div>
                <div class="col-status">
                  <n-switch
                    v-model:value="keyword.enabled"
                    size="small"
                    @update:value="updateKeywordStatus(keyword.id, $event)"
                  />
                </div>
                <div class="col-actions">
                  <n-button size="small" @click="editKeyword(keyword)">
                    <template #icon>
                      <n-icon><CreateOutline /></n-icon>
                    </template>
                  </n-button>
                  <n-button size="small" type="error" @click="deleteKeyword(keyword.id)">
                    <template #icon>
                      <n-icon><TrashOutline /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
            </div>
          </div>

          <div class="no-data" v-if="filteredKeywords.length === 0">
            <n-empty description="暂无关键词数据">
              <template #icon>
                <n-icon size="48" color="#d0d0d0">
                  <SearchOutline />
                </n-icon>
              </template>
            </n-empty>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 监控统计 -->
    <n-card title="监控统计" class="stats-card">
      <div class="stats-overview">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon size="24" color="#2080f0"><EyeOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalKeywords }}</div>
              <div class="stat-label">监控关键词</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon size="24" color="#f0a020"><AlertCircleOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.todayHits }}</div>
              <div class="stat-label">今日命中</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon size="24" color="#d03050"><ShieldOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.highRiskHits }}</div>
              <div class="stat-label">高风险命中</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon size="24" color="#18a058"><CheckmarkCircleOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.accuracy }}%</div>
              <div class="stat-label">识别准确率</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 命中趋势图 -->
      <div class="trend-chart">
        <h4>命中趋势</h4>
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>

    <!-- 告警设置 -->
    <n-card title="告警设置" class="alert-card">
      <div class="alert-config">
        <div class="config-section">
          <h4>告警规则</h4>
          <div class="alert-rules">
            <div class="rule-item">
              <n-checkbox v-model:checked="alertConfig.enableEmail">
                邮件告警
              </n-checkbox>
              <n-input
                v-model:value="alertConfig.emailRecipients"
                placeholder="邮箱地址，多个用逗号分隔"
                :disabled="!alertConfig.enableEmail"
                style="flex: 1; margin-left: 12px"
              />
            </div>
            <div class="rule-item">
              <n-checkbox v-model:checked="alertConfig.enableSms">
                短信告警
              </n-checkbox>
              <n-input
                v-model:value="alertConfig.smsRecipients"
                placeholder="手机号码，多个用逗号分隔"
                :disabled="!alertConfig.enableSms"
                style="flex: 1; margin-left: 12px"
              />
            </div>
            <div class="rule-item">
              <n-checkbox v-model:checked="alertConfig.enableWebhook">
                Webhook告警
              </n-checkbox>
              <n-input
                v-model:value="alertConfig.webhookUrl"
                placeholder="Webhook URL"
                :disabled="!alertConfig.enableWebhook"
                style="flex: 1; margin-left: 12px"
              />
            </div>
          </div>
        </div>

        <div class="config-section">
          <h4>告警频率</h4>
          <div class="frequency-config">
            <div class="frequency-item">
              <span class="frequency-label">高风险关键词：</span>
              <n-select
                v-model:value="alertConfig.highRiskFrequency"
                :options="frequencyOptions"
                style="width: 150px"
              />
            </div>
            <div class="frequency-item">
              <span class="frequency-label">中风险关键词：</span>
              <n-select
                v-model:value="alertConfig.mediumRiskFrequency"
                :options="frequencyOptions"
                style="width: 150px"
              />
            </div>
            <div class="frequency-item">
              <span class="frequency-label">低风险关键词：</span>
              <n-select
                v-model:value="alertConfig.lowRiskFrequency"
                :options="frequencyOptions"
                style="width: 150px"
              />
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-button type="primary" @click="saveConfig" :loading="saving">
        <template #icon>
          <n-icon><SaveOutline /></n-icon>
        </template>
        保存配置
      </n-button>
      <n-button @click="testAlert">
        <template #icon>
          <n-icon><NotificationsOutline /></n-icon>
        </template>
        测试告警
      </n-button>
      <n-button @click="$emit('close')">
        关闭
      </n-button>
    </div>

    <!-- 批量导入模态框 -->
    <n-modal v-model:show="showBatchImport" title="批量导入关键词">
      <n-card style="width: 600px">
        <div class="batch-import-content">
          <div class="import-tips">
            <n-alert type="info" title="导入说明">
              <ul>
                <li>每行一个关键词，格式：关键词,级别,类型</li>
                <li>级别：high/medium/low</li>
                <li>类型：sensitive/spam/abuse/custom</li>
                <li>示例：违规内容,high,sensitive</li>
              </ul>
            </n-alert>
          </div>
          <div class="import-textarea">
            <n-input
              v-model:value="batchImportText"
              type="textarea"
              placeholder="请输入关键词数据"
              :rows="10"
            />
          </div>
          <div class="import-actions">
            <n-button @click="showBatchImport = false">取消</n-button>
            <n-button type="primary" @click="processBatchImport">
              导入
            </n-button>
          </div>
        </div>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import {
  NCard, NInput, NSwitch, NSelect, NInputNumber, NButton, NIcon,
  NTag, NCheckbox, NEmpty, NModal, NAlert, useMessage
} from 'naive-ui'
import {
  AddOutline, DocumentTextOutline, DownloadOutline, SearchOutline,
  CreateOutline, TrashOutline, EyeOutline, AlertCircleOutline,
  ShieldOutline, CheckmarkCircleOutline, SaveOutline, NotificationsOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface KeywordItem {
  id: string
  word: string
  level: string
  type: string
  hits: number
  enabled: boolean
  createdAt: string
}

interface MonitorConfig {
  name: string
  enabled: boolean
  scope: string
  threshold: number
  groups: string[]
}

interface AlertConfig {
  enableEmail: boolean
  emailRecipients: string
  enableSms: boolean
  smsRecipients: string
  enableWebhook: boolean
  webhookUrl: string
  highRiskFrequency: string
  mediumRiskFrequency: string
  lowRiskFrequency: string
}

interface Emits {
  (e: 'close'): void
}

const emit = defineEmits<Emits>()
const message = useMessage()

const saving = ref(false)
const showBatchImport = ref(false)
const batchImportText = ref('')
const searchKeyword = ref('')
const filterLevel = ref('')
const filterType = ref('')
const selectedKeywords = ref<string[]>([])

// 配置数据
const config = reactive<MonitorConfig>({
  name: '默认监控',
  enabled: true,
  scope: 'all',
  threshold: 5,
  groups: []
})

const alertConfig = reactive<AlertConfig>({
  enableEmail: true,
  emailRecipients: '',
  enableSms: false,
  smsRecipients: '',
  enableWebhook: false,
  webhookUrl: '',
  highRiskFrequency: 'immediate',
  mediumRiskFrequency: 'hourly',
  lowRiskFrequency: 'daily'
})

const newKeyword = reactive({
  word: '',
  level: 'medium',
  type: 'custom'
})

// 选项数据
const scopeOptions = [
  { label: '全部群组', value: 'all' },
  { label: '指定群组', value: 'selected' },
  { label: '公开群组', value: 'public' },
  { label: '私有群组', value: 'private' }
]

const groupOptions = [
  { label: '产品讨论群', value: 'group1' },
  { label: '技术交流群', value: 'group2' },
  { label: '客户服务群', value: 'group3' },
  { label: '市场推广群', value: 'group4' }
]

const levelOptions = [
  { label: '高风险', value: 'high' },
  { label: '中风险', value: 'medium' },
  { label: '低风险', value: 'low' }
]

const typeOptions = [
  { label: '敏感内容', value: 'sensitive' },
  { label: '垃圾信息', value: 'spam' },
  { label: '恶意内容', value: 'abuse' },
  { label: '自定义', value: 'custom' }
]

const frequencyOptions = [
  { label: '立即', value: 'immediate' },
  { label: '每小时', value: 'hourly' },
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' }
]

// 关键词数据
const keywords = ref<KeywordItem[]>([
  {
    id: '1',
    word: '违规',
    level: 'high',
    type: 'sensitive',
    hits: 23,
    enabled: true,
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    word: '广告',
    level: 'medium',
    type: 'spam',
    hits: 45,
    enabled: true,
    createdAt: '2024-01-14'
  },
  {
    id: '3',
    word: '投诉',
    level: 'medium',
    type: 'custom',
    hits: 12,
    enabled: true,
    createdAt: '2024-01-13'
  }
])

// 统计数据
const stats = ref({
  totalKeywords: 156,
  todayHits: 23,
  highRiskHits: 5,
  accuracy: 94.5
})

// 图表引用
const trendChartRef = ref<HTMLElement>()

// 计算属性
const filteredKeywords = computed(() => {
  return keywords.value.filter(keyword => {
    const matchSearch = !searchKeyword.value || 
      keyword.word.toLowerCase().includes(searchKeyword.value.toLowerCase())
    const matchLevel = !filterLevel.value || keyword.level === filterLevel.value
    const matchType = !filterType.value || keyword.type === filterType.value
    return matchSearch && matchLevel && matchType
  })
})

const isAllSelected = computed(() => {
  return filteredKeywords.value.length > 0 && 
    selectedKeywords.value.length === filteredKeywords.value.length
})

const isIndeterminate = computed(() => {
  return selectedKeywords.value.length > 0 && 
    selectedKeywords.value.length < filteredKeywords.value.length
})

// 方法
const getLevelColor = (level: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'error' | 'warning' | 'info'> = {
    high: 'error',
    medium: 'warning',
    low: 'info'
  }
  return colorMap[level] || 'default'
}

const getLevelText = (level: string) => {
  const textMap = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return textMap[level as keyof typeof textMap] || level
}

const getTypeColor = (type: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'error' | 'warning' | 'info'> = {
    sensitive: 'error',
    spam: 'warning',
    abuse: 'error',
    custom: 'info'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    sensitive: '敏感内容',
    spam: '垃圾信息',
    abuse: '恶意内容',
    custom: '自定义'
  }
  return textMap[type as keyof typeof textMap] || type
}

const addKeyword = () => {
  if (!newKeyword.word.trim()) {
    message.warning('请输入关键词')
    return
  }

  const exists = keywords.value.some(k => k.word === newKeyword.word)
  if (exists) {
    message.warning('关键词已存在')
    return
  }

  keywords.value.push({
    id: Date.now().toString(),
    word: newKeyword.word,
    level: newKeyword.level,
    type: newKeyword.type,
    hits: 0,
    enabled: true,
    createdAt: new Date().toISOString().split('T')[0]
  })

  newKeyword.word = ''
  message.success('关键词添加成功')
}

const editKeyword = (keyword: KeywordItem) => {
  message.info('编辑功能开发中')
}

const deleteKeyword = (id: string) => {
  const index = keywords.value.findIndex(k => k.id === id)
  if (index > -1) {
    keywords.value.splice(index, 1)
    selectedKeywords.value = selectedKeywords.value.filter(sid => sid !== id)
    message.success('关键词删除成功')
  }
}

const updateKeywordStatus = (id: string, enabled: boolean) => {
  const keyword = keywords.value.find(k => k.id === id)
  if (keyword) {
    keyword.enabled = enabled
    message.success(`关键词已${enabled ? '启用' : '禁用'}`)
  }
}

const toggleKeywordSelection = (id: string) => {
  const index = selectedKeywords.value.indexOf(id)
  if (index > -1) {
    selectedKeywords.value.splice(index, 1)
  } else {
    selectedKeywords.value.push(id)
  }
}

const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedKeywords.value = filteredKeywords.value.map(k => k.id)
  } else {
    selectedKeywords.value = []
  }
}

const selectAll = () => {
  selectedKeywords.value = filteredKeywords.value.map(k => k.id)
}

const clearSelection = () => {
  selectedKeywords.value = []
}

const batchDelete = () => {
  keywords.value = keywords.value.filter(k => !selectedKeywords.value.includes(k.id))
  selectedKeywords.value = []
  message.success('批量删除成功')
}

const exportKeywords = () => {
  const data = keywords.value.map(k => `${k.word},${k.level},${k.type}`).join('\n')
  const blob = new Blob([data], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'keywords.txt'
  a.click()
  URL.revokeObjectURL(url)
  message.success('关键词导出成功')
}

const processBatchImport = () => {
  if (!batchImportText.value.trim()) {
    message.warning('请输入要导入的数据')
    return
  }

  const lines = batchImportText.value.trim().split('\n')
  let successCount = 0
  let errorCount = 0

  lines.forEach(line => {
    const parts = line.split(',')
    if (parts.length >= 3) {
      const [word, level, type] = parts
      if (word && level && type) {
        const exists = keywords.value.some(k => k.word === word.trim())
        if (!exists) {
          keywords.value.push({
            id: Date.now().toString() + Math.random(),
            word: word.trim(),
            level: level.trim(),
            type: type.trim(),
            hits: 0,
            enabled: true,
            createdAt: new Date().toISOString().split('T')[0]
          })
          successCount++
        }
      } else {
        errorCount++
      }
    } else {
      errorCount++
    }
  })

  showBatchImport.value = false
  batchImportText.value = ''
  message.success(`导入完成：成功 ${successCount} 条，失败 ${errorCount} 条`)
}

const saveConfig = async () => {
  saving.value = true
  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('配置保存成功')
  } catch (error) {
    message.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const testAlert = () => {
  message.info('测试告警已发送')
}

const initChart = () => {
  if (trendChartRef.value) {
    const chart = echarts.init(trendChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['总命中', '高风险', '中风险', '低风险'] },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '总命中',
          type: 'line',
          data: [23, 18, 25, 30, 22, 15, 20],
          itemStyle: { color: '#2080f0' }
        },
        {
          name: '高风险',
          type: 'line',
          data: [5, 3, 6, 8, 4, 2, 3],
          itemStyle: { color: '#d03050' }
        },
        {
          name: '中风险',
          type: 'line',
          data: [12, 10, 13, 15, 12, 8, 11],
          itemStyle: { color: '#f0a020' }
        },
        {
          name: '低风险',
          type: 'line',
          data: [6, 5, 6, 7, 6, 5, 6],
          itemStyle: { color: '#18a058' }
        }
      ]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.keyword-monitor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-card,
.keywords-card,
.stats-card,
.alert-card {
  margin-bottom: 16px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.keywords-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.add-keyword {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.add-form {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  flex: 1;
}

.batch-import {
  display: flex;
  gap: 8px;
}

.keywords-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.keywords-table {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 50px 1fr 100px 120px 100px 80px 120px;
  gap: 16px;
  align-items: center;
  padding: 12px 16px;
}

.table-header {
  background: #f8f9fa;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.table-row {
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8f9fa;
}

.keyword-text {
  font-weight: 500;
  color: #1a1a1a;
}

.hits-count {
  font-weight: 500;
  color: #2080f0;
}

.col-actions {
  display: flex;
  gap: 8px;
}

.no-data {
  padding: 48px 0;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.trend-chart {
  margin-top: 24px;
}

.trend-chart h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.chart {
  width: 100%;
  height: 300px;
}

.alert-config {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.alert-rules {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.frequency-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.frequency-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.frequency-label {
  min-width: 120px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.batch-import-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.import-tips ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.import-tips li {
  margin-bottom: 4px;
}

.import-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>