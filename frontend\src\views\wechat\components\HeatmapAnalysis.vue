<template>
  <div class="heatmap-analysis">
    <!-- 配置区域 -->
    <div class="config-section">
      <n-form inline :model="configForm" label-placement="left">
        <n-form-item label="页面URL">
          <n-input
            v-model:value="configForm.page_url"
            placeholder="输入页面URL"
            style="width: 300px"
            clearable
          />
        </n-form-item>
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="configForm.date_range"
            type="daterange"
            clearable
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="设备类型">
          <n-select
            v-model:value="configForm.device_type"
            placeholder="选择设备类型"
            clearable
            style="width: 150px"
            :options="deviceTypeOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleGenerateHeatmap" :loading="loading">
              <template #icon>
                <n-icon><BarChartOutline /></n-icon>
              </template>
              生成热力图
            </n-button>
            <n-button @click="handleResetConfig">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </div>

    <!-- 热力图显示区域 -->
    <div class="heatmap-container" v-if="heatmapData">
      <n-card title="页面热力图">
        <template #header-extra>
          <n-space>
            <n-tag type="info">总点击: {{ heatmapData.total_clicks }}</n-tag>
            <n-tag type="success">总浏览: {{ heatmapData.total_views }}</n-tag>
            <n-button size="small" @click="handleExportHeatmap">
              <template #icon>
                <n-icon><DownloadOutline /></n-icon>
              </template>
              导出
            </n-button>
          </n-space>
        </template>

        <div class="heatmap-wrapper">
          <!-- 页面预览 -->
          <div class="page-preview" ref="pagePreviewRef">
            <div class="page-header">
              <div class="page-title">{{ heatmapData.page_title || '页面预览' }}</div>
              <div class="page-url">{{ configForm.page_url }}</div>
            </div>
            
            <!-- 热力图点位 -->
            <div class="heatmap-overlay">
              <div
                v-for="point in heatmapData.heat_points"
                :key="point.id"
                class="heat-point"
                :style="getHeatPointStyle(point)"
                @click="handlePointClick(point)"
              >
                <div class="heat-point-tooltip">
                  <div>位置: ({{ point.x }}, {{ point.y }})</div>
                  <div>点击次数: {{ point.click_count }}</div>
                  <div>停留时间: {{ point.duration }}s</div>
                </div>
              </div>
            </div>
            
            <!-- 模拟页面内容 -->
            <div class="page-content">
              <div class="content-section" v-for="section in pageSections" :key="section.id">
                <div class="section-title">{{ section.title }}</div>
                <div class="section-content">{{ section.content }}</div>
              </div>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 统计分析 -->
    <div class="stats-section" v-if="heatmapData">
      <n-grid :cols="3" :x-gap="16">
        <n-grid-item>
          <n-card title="点击热区排行">
            <n-list>
              <n-list-item v-for="(area, index) in topClickAreas" :key="area.id">
                <div class="rank-item">
                  <div class="rank-number">{{ index + 1 }}</div>
                  <div class="rank-content">
                    <div class="rank-title">{{ area.element_type }}</div>
                    <div class="rank-desc">点击 {{ area.click_count }} 次</div>
                  </div>
                  <div class="rank-percentage">
                    <n-progress
                      type="line"
                      :percentage="(area.click_count / heatmapData.total_clicks) * 100"
                      :show-indicator="false"
                      :height="6"
                    />
                  </div>
                </div>
              </n-list-item>
            </n-list>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card title="停留时间分析">
            <div class="duration-chart" ref="durationChartRef"></div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card title="用户行为模式">
            <div class="behavior-patterns">
              <div class="pattern-item" v-for="pattern in behaviorPatterns" :key="pattern.type">
                <div class="pattern-icon">
                  <n-icon size="20" :color="pattern.color">
                    <component :is="pattern.icon" />
                  </n-icon>
                </div>
                <div class="pattern-content">
                  <div class="pattern-title">{{ pattern.title }}</div>
                  <div class="pattern-desc">{{ pattern.description }}</div>
                  <div class="pattern-value">{{ pattern.value }}</div>
                </div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!heatmapData && !loading">
      <n-empty description="请配置页面URL并生成热力图">
        <template #icon>
          <n-icon size="60" color="#ccc">
            <BarChartOutline />
          </n-icon>
        </template>
        <template #extra>
          <n-button type="primary" @click="handleGenerateHeatmap">
            开始分析
          </n-button>
        </template>
      </n-empty>
    </div>

    <!-- 点击详情模态框 -->
    <n-modal
      v-model:show="showPointDetail"
      preset="dialog"
      title="热点详情"
      style="width: 500px"
    >
      <div v-if="selectedPoint" class="point-detail">
        <n-descriptions :column="2">
          <n-descriptions-item label="位置坐标">
            ({{ selectedPoint.x }}, {{ selectedPoint.y }})
          </n-descriptions-item>
          <n-descriptions-item label="元素类型">
            {{ selectedPoint.element_type }}
          </n-descriptions-item>
          <n-descriptions-item label="点击次数">
            {{ selectedPoint.click_count }}
          </n-descriptions-item>
          <n-descriptions-item label="平均停留">
            {{ selectedPoint.duration }}s
          </n-descriptions-item>
          <n-descriptions-item label="转化率">
            {{ ((selectedPoint.conversions / selectedPoint.click_count) * 100).toFixed(1) }}%
          </n-descriptions-item>
          <n-descriptions-item label="跳出率">
            {{ ((selectedPoint.bounces / selectedPoint.click_count) * 100).toFixed(1) }}%
          </n-descriptions-item>
        </n-descriptions>
        
        <n-divider />
        
        <div class="point-recommendations">
          <h4>优化建议</h4>
          <ul>
            <li v-for="suggestion in getPointSuggestions(selectedPoint)" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NDatePicker,
  NSelect,
  NButton,
  NSpace,
  NIcon,
  NTag,
  NGrid,
  NGridItem,
  NList,
  NListItem,
  NProgress,
  NEmpty,
  NModal,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  useMessage
} from 'naive-ui'
import {
  BarChartOutline,
  RefreshOutline,
  DownloadOutline,
  MoveOutline,
  TimeOutline,
  TrendingUpOutline,
  EyeOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import * as echarts from 'echarts'

interface Emits {
  (e: 'close'): void
}

const emit = defineEmits<Emits>()

const message = useMessage()
const wechatStore = useWechatStore()

// 配置表单
const configForm = reactive({
  page_url: '',
  date_range: null as [number, number] | null,
  device_type: null as string | null
})

// 设备类型选项
const deviceTypeOptions = [
  { label: '移动设备', value: 'mobile' },
  { label: '桌面设备', value: 'desktop' },
  { label: '平板设备', value: 'tablet' }
]

// 状态
const loading = ref(false)
const heatmapData = ref<any>(null)
const showPointDetail = ref(false)
const selectedPoint = ref<any>(null)

// 引用
const pagePreviewRef = ref<HTMLElement>()
const durationChartRef = ref<HTMLElement>()

// 模拟页面内容
const pageSections = [
  { id: 1, title: '产品介绍', content: '这里是产品的详细介绍内容...' },
  { id: 2, title: '功能特性', content: '产品的主要功能和特性说明...' },
  { id: 3, title: '用户评价', content: '用户对产品的评价和反馈...' },
  { id: 4, title: '购买信息', content: '价格、购买方式等信息...' }
]

// 计算属性
const topClickAreas = computed(() => {
  if (!heatmapData.value) return []
  return heatmapData.value.heat_points
    .sort((a: any, b: any) => b.click_count - a.click_count)
    .slice(0, 5)
})

const behaviorPatterns = computed(() => [
  {
    type: 'scroll',
    title: '滚动行为',
    description: '用户平均滚动深度',
    value: '75%',
    color: '#18a058',
    icon: MoveOutline
  },
  {
    type: 'attention',
    title: '注意力分布',
    description: '主要关注区域',
    value: '顶部 60%',
    color: '#2080f0',
    icon: EyeOutline
  },
  {
    type: 'engagement',
    title: '参与度',
    description: '用户交互频率',
    value: '中等',
    color: '#f0a020',
    icon: TrendingUpOutline
  },
  {
    type: 'duration',
    title: '停留时间',
    description: '平均页面停留',
    value: '2.5分钟',
    color: '#d03050',
    icon: TimeOutline
  }
])

// 方法
const handleGenerateHeatmap = async () => {
  if (!configForm.page_url) {
    message.warning('请输入页面URL')
    return
  }
  
  loading.value = true
  try {
    const params = {
      page_url: configForm.page_url,
      date_range: configForm.date_range ? [
        new Date(configForm.date_range[0]).toISOString().split('T')[0],
        new Date(configForm.date_range[1]).toISOString().split('T')[0]
      ] as [string, string] : undefined
    }
    
    const data = await wechatStore.getHeatmapData(params)
    
    // 模拟热力图数据
    heatmapData.value = {
      page_title: '产品详情页',
      page_url: configForm.page_url,
      total_clicks: 1250,
      total_views: 3200,
      heat_points: [
        {
          id: 1,
          x: 150,
          y: 200,
          click_count: 320,
          duration: 45,
          element_type: '产品图片',
          conversions: 28,
          bounces: 45
        },
        {
          id: 2,
          x: 300,
          y: 350,
          click_count: 280,
          duration: 38,
          element_type: '购买按钮',
          conversions: 85,
          bounces: 12
        },
        {
          id: 3,
          x: 200,
          y: 500,
          click_count: 195,
          duration: 25,
          element_type: '用户评价',
          conversions: 15,
          bounces: 35
        },
        {
          id: 4,
          x: 400,
          y: 150,
          click_count: 165,
          duration: 20,
          element_type: '导航菜单',
          conversions: 8,
          bounces: 85
        },
        {
          id: 5,
          x: 250,
          y: 650,
          click_count: 145,
          duration: 30,
          element_type: '相关推荐',
          conversions: 22,
          bounces: 28
        }
      ]
    }
    
    // 渲染图表
    await nextTick()
    renderDurationChart()
    
    message.success('热力图生成成功')
  } catch (error) {
    message.error('生成热力图失败')
  } finally {
    loading.value = false
  }
}

const handleResetConfig = () => {
  Object.assign(configForm, {
    page_url: '',
    date_range: null,
    device_type: null
  })
  heatmapData.value = null
}

const handleExportHeatmap = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const getHeatPointStyle = (point: any) => {
  const intensity = point.click_count / 320 // 基于最大点击数计算强度
  const size = Math.max(20, Math.min(60, intensity * 50))
  const opacity = Math.max(0.3, Math.min(0.8, intensity))
  
  return {
    position: 'absolute' as const,
    left: point.x + 'px',
    top: point.y + 'px',
    width: size + 'px',
    height: size + 'px',
    backgroundColor: `rgba(255, 87, 51, ${opacity})`,
    borderRadius: '50%',
    cursor: 'pointer' as const,
    transform: 'translate(-50%, -50%)'
  }
}

const handlePointClick = (point: any) => {
  selectedPoint.value = point
  showPointDetail.value = true
}

const getPointSuggestions = (point: any) => {
  const suggestions = []
  
  if (point.click_count > 200) {
    suggestions.push('这是一个高点击热区，可以考虑在此放置重要信息或CTA按钮')
  }
  
  if (point.conversions / point.click_count < 0.1) {
    suggestions.push('转化率较低，建议优化元素设计或位置')
  }
  
  if (point.bounces / point.click_count > 0.5) {
    suggestions.push('跳出率较高，可能需要改善用户体验或内容相关性')
  }
  
  if (point.duration < 30) {
    suggestions.push('停留时间较短，建议增加内容吸引力')
  }
  
  if (suggestions.length === 0) {
    suggestions.push('该区域表现良好，可以作为其他区域优化的参考')
  }
  
  return suggestions
}

const renderDurationChart = () => {
  if (!durationChartRef.value || !heatmapData.value) return
  
  const chart = echarts.init(durationChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}s ({d}%)'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 30, name: '0-30s' },
          { value: 45, name: '30-60s' },
          { value: 60, name: '60-120s' },
          { value: 35, name: '120s+' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化
onMounted(() => {
  // 设置默认URL
  configForm.page_url = 'https://example.com/product/123'
})
</script>

<style scoped>
.heatmap-analysis {
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.heatmap-container {
  margin-bottom: 24px;
}

.heatmap-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.page-preview {
  position: relative;
  width: 100%;
  min-height: 800px;
  background: #fff;
}

.page-header {
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.page-url {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.heatmap-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.heat-point {
  position: absolute;
  pointer-events: auto;
  transition: all 0.3s ease;
}

.heat-point:hover {
  transform: translate(-50%, -50%) scale(1.2) !important;
  z-index: 20;
}

.heat-point-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  margin-bottom: 8px;
}

.heat-point:hover .heat-point-tooltip {
  opacity: 1;
}

.page-content {
  padding: 24px;
}

.content-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.section-content {
  color: #666;
  line-height: 1.6;
}

.stats-section {
  margin-bottom: 24px;
}

.rank-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: #2080f0;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.rank-content {
  flex: 1;
}

.rank-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.rank-desc {
  font-size: 12px;
  color: #666;
}

.rank-percentage {
  width: 60px;
}

.duration-chart {
  height: 200px;
}

.behavior-patterns {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pattern-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.pattern-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
}

.pattern-content {
  flex: 1;
}

.pattern-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.pattern-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.pattern-value {
  font-size: 14px;
  font-weight: 600;
  color: #2080f0;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.point-detail {
  padding: 16px 0;
}

.point-recommendations {
  margin-top: 16px;
}

.point-recommendations h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.point-recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.point-recommendations li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}
</style>