import { createPinia } from 'pinia'
import type { App } from 'vue'

// 导出所有store
export { useAuthStore } from './modules/auth'
export { useAppStore } from './modules/app'
export { useCustomerStore } from './modules/customer'
export { useUserStore } from './modules/user'
export { useOptionsStore } from './optionsStore'

// 创建pinia实例
const pinia = createPinia()

// 安装插件的函数
export function setupStore(app: App) {
  app.use(pinia)
}

export default pinia