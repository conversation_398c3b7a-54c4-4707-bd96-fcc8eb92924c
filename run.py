#!/usr/bin/env python3
"""YYSH FastAPI应用启动脚本

提供便捷的应用启动方式和环境管理
"""

import os
import sys
import argparse
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import settings, init_settings
from app.database import init_database


def setup_environment():
    """设置运行环境"""
    try:
        # 初始化配置
        init_settings()
        
        # 初始化数据库
        if not os.getenv("SKIP_DB_INIT"):
            init_database()
        
        print("✅ 环境设置完成")
        return True
    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        return False


def run_development():
    """运行开发环境"""
    print("🚀 启动开发环境...")
    
    if not setup_environment():
        sys.exit(1)
    
    uvicorn.run(
        "main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=True,
        reload_dirs=["app", "."],
        log_level=settings.app.log_level.lower(),
        access_log=True,
        use_colors=True
    )


def run_production():
    """运行生产环境"""
    print("🏭 启动生产环境...")
    
    if not setup_environment():
        sys.exit(1)
    
    # 生产环境配置
    workers = int(os.getenv("WORKERS", "4"))
    
    uvicorn.run(
        "main:app",
        host=settings.app.host,
        port=settings.app.port,
        workers=workers,
        log_level=settings.app.log_level.lower(),
        access_log=True,
        proxy_headers=True,
        forwarded_allow_ips="*"
    )


def run_test():
    """运行测试环境"""
    print("🧪 启动测试环境...")
    
    # 设置测试环境变量
    os.environ["APP_ENVIRONMENT"] = "testing"
    os.environ["APP_DEBUG"] = "true"
    
    if not setup_environment():
        sys.exit(1)
    
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8001,
        reload=False,
        log_level="debug",
        access_log=True
    )


def check_health():
    """检查应用健康状态"""
    import httpx
    
    try:
        url = f"http://{settings.app.host}:{settings.app.port}/health"
        response = httpx.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 应用运行正常")
            print(f"📊 状态: {data.get('message', 'unknown')}")
            print(f"🕐 时间: {data.get('timestamp', 'unknown')}")
            print(f"📦 版本: {data.get('version', 'unknown')}")
            return True
        else:
            print(f"❌ 应用状态异常: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False


def show_info():
    """显示应用信息"""
    print("\n" + "="*50)
    print("🏠 YYSH FastAPI应用信息")
    print("="*50)
    print(f"📦 应用名称: {settings.app.app_name}")
    print(f"🌍 运行环境: {settings.app.environment}")
    print(f"🔧 调试模式: {settings.app.debug}")
    print(f"🌐 服务地址: http://{settings.app.host}:{settings.app.port}")
    print(f"📚 API文档: http://{settings.app.host}:{settings.app.port}{settings.app.docs_url}")
    print(f"🔍 健康检查: http://{settings.app.host}:{settings.app.port}/health")
    print(f"💾 数据库: {settings.database.host}:{settings.database.port}")
    print("="*50 + "\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YYSH FastAPI应用启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run.py dev          # 启动开发环境
  python run.py prod         # 启动生产环境
  python run.py test         # 启动测试环境
  python run.py health       # 检查应用健康状态
  python run.py info         # 显示应用信息
        """
    )
    
    parser.add_argument(
        "command",
        choices=["dev", "prod", "test", "health", "info"],
        help="运行命令"
    )
    
    parser.add_argument(
        "--host",
        default=None,
        help="服务器主机地址"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="工作进程数量（仅生产环境）"
    )
    
    args = parser.parse_args()
    
    # 覆盖配置
    if args.host:
        os.environ["APP_HOST"] = args.host
    if args.port:
        os.environ["APP_PORT"] = str(args.port)
    if args.workers:
        os.environ["WORKERS"] = str(args.workers)
    
    # 执行命令
    if args.command == "dev":
        run_development()
    elif args.command == "prod":
        run_production()
    elif args.command == "test":
        run_test()
    elif args.command == "health":
        if not check_health():
            sys.exit(1)
    elif args.command == "info":
        show_info()


if __name__ == "__main__":
    main()