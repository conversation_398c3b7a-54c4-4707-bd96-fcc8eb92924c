<template>
  <n-modal v-model:show="showModal" preset="card" style="width: 1200px; max-height: 80vh">
    <template #header>
      <n-space align="center">
        <n-icon size="20">
          <Notifications />
        </n-icon>
        <span>提醒管理</span>
        <n-badge v-if="unreadReminderCount > 0" :value="unreadReminderCount" />
      </n-space>
    </template>

    <div class="reminder-content">
      <!-- 提醒统计 -->
      <div class="reminder-stats">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-card size="small">
              <n-statistic label="今日提醒" :value="todayReminders.length">
                <template #prefix>
                  <n-icon color="#18a058">
                    <Today />
                  </n-icon>
                </template>
              </n-statistic>
            </n-card>
          </n-grid-item>
          <n-grid-item>
            <n-card size="small">
              <n-statistic label="逾期提醒" :value="overdueReminders.length">
                <template #prefix>
                  <n-icon color="#d03050">
                    <Warning />
                  </n-icon>
                </template>
              </n-statistic>
            </n-card>
          </n-grid-item>
          <n-grid-item>
            <n-card size="small">
              <n-statistic label="未读提醒" :value="unreadReminderCount">
                <template #prefix>
                  <n-icon color="#f0a020">
                    <Mail />
                  </n-icon>
                </template>
              </n-statistic>
            </n-card>
          </n-grid-item>
          <n-grid-item>
            <n-card size="small">
              <n-statistic label="总提醒" :value="reminderTotal">
                <template #prefix>
                  <n-icon color="#2080f0">
                    <List />
                  </n-icon>
                </template>
              </n-statistic>
            </n-card>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <n-space>
          <n-button type="primary" @click="showCreateReminderModal = true">
            <template #icon>
              <n-icon><Add /></n-icon>
            </template>
            新建提醒
          </n-button>
          <n-button @click="markAllAsRead" :disabled="unreadReminderCount === 0">
            <template #icon>
              <n-icon><CheckmarkDone /></n-icon>
            </template>
            全部标记已读
          </n-button>
          <n-button @click="loadReminders">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </div>

      <!-- 提醒标签页 -->
      <n-tabs v-model:value="activeTab" type="line" @update:value="handleTabChange">
        <n-tab-pane name="today" tab="今日提醒">
          <ReminderList
            :reminders="todayReminders"
            :loading="reminderLoading"
            @read="handleMarkAsRead"
            @dismiss="handleDismiss"
            @edit="handleEdit"
            @delete="handleDelete"
          />
        </n-tab-pane>
        
        <n-tab-pane name="overdue" tab="逾期提醒">
          <ReminderList
            :reminders="overdueReminders"
            :loading="reminderLoading"
            @read="handleMarkAsRead"
            @dismiss="handleDismiss"
            @edit="handleEdit"
            @delete="handleDelete"
          />
        </n-tab-pane>
        
        <n-tab-pane name="all" tab="全部提醒">
          <!-- 搜索和筛选 -->
          <div class="search-section">
            <n-space>
              <n-input
                v-model:value="searchKeyword"
                placeholder="搜索提醒标题或内容"
                style="width: 250px"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <n-icon><Search /></n-icon>
                </template>
              </n-input>
              <n-select
                v-model:value="typeFilter"
                placeholder="提醒类型"
                style="width: 140px"
                clearable
                :options="reminderTypeOptions"
              />
              <n-select
                v-model:value="statusFilter"
                placeholder="状态"
                style="width: 120px"
                clearable
                :options="statusOptions"
              />
              <n-date-picker
                v-model:value="dateRange"
                type="daterange"
                placeholder="选择日期范围"
                style="width: 240px"
                clearable
              />
              <n-button type="primary" @click="handleSearch">
                搜索
              </n-button>
              <n-button @click="handleReset">
                重置
              </n-button>
            </n-space>
          </div>
          
          <ReminderList
            :reminders="reminders"
            :loading="reminderLoading"
            :pagination="pagination"
            @read="handleMarkAsRead"
            @dismiss="handleDismiss"
            @edit="handleEdit"
            @delete="handleDelete"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 创建提醒弹窗 -->
    <CreateReminderModal
      v-model:show="showCreateReminderModal"
      @success="handleCreateSuccess"
    />

    <!-- 编辑提醒弹窗 -->
    <CreateReminderModal
      v-model:show="showEditReminderModal"
      :reminder="currentReminder"
      @success="handleEditSuccess"
    />
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  Notifications,
  Today,
  Warning,
  Mail,
  List,
  Add,
  CheckmarkDone,
  Refresh,
  Search
} from '@vicons/ionicons5'
import { useTrackingStore } from '@/stores/trackingStore'
import type { AutoReminder } from '@/api/trackingService'
import ReminderList from './ReminderList.vue'
import CreateReminderModal from './CreateReminderModal.vue'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const dialog = useDialog()
const trackingStore = useTrackingStore()

// 响应式数据
const activeTab = ref('today')
const showCreateReminderModal = ref(false)
const showEditReminderModal = ref(false)
const currentReminder = ref<AutoReminder | null>(null)
const searchKeyword = ref('')
const typeFilter = ref<string | null>(null)
const statusFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)

// 从store获取数据
const {
  reminders,
  todayReminders,
  overdueReminders,
  reminderLoading,
  reminderTotal,
  unreadReminderCount
} = trackingStore

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: computed(() => reminderTotal),
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 选项配置
const reminderTypeOptions = [
  { label: '跟进提醒', value: 'follow_up' },
  { label: '生日提醒', value: 'birthday' },
  { label: '合同续约', value: 'contract_renewal' },
  { label: '付款到期', value: 'payment_due' },
  { label: '自定义', value: 'custom' }
]

const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '已发送', value: 'sent' },
  { label: '已读', value: 'read' },
  { label: '已忽略', value: 'dismissed' }
]

// 监听器
watch(() => props.show, (newVal) => {
  if (newVal) {
    loadInitialData()
  }
})

// 方法
const loadInitialData = async () => {
  try {
    await Promise.all([
      trackingStore.fetchTodayReminders(),
      trackingStore.fetchOverdueReminders()
    ])
    
    if (activeTab.value === 'all') {
      loadReminders()
    }
  } catch (error) {
    message.error('加载提醒数据失败')
  }
}

const loadReminders = async () => {
  try {
    const params: any = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    }

    if (searchKeyword.value) {
      // 这里需要后端支持搜索功能
      // params.keyword = searchKeyword.value
    }
    if (typeFilter.value) {
      params.type = typeFilter.value
    }
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    if (dateRange.value) {
      params.start_date = new Date(dateRange.value[0]).toISOString()
      params.end_date = new Date(dateRange.value[1]).toISOString()
    }

    await trackingStore.fetchReminders(params)
  } catch (error) {
    message.error('加载提醒列表失败')
  }
}

const handleTabChange = (value: string) => {
  activeTab.value = value
  if (value === 'all') {
    loadReminders()
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  loadReminders()
}

const handleReset = () => {
  searchKeyword.value = ''
  typeFilter.value = null
  statusFilter.value = null
  dateRange.value = null
  pagination.value.page = 1
  loadReminders()
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadReminders()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  loadReminders()
}

const handleMarkAsRead = async (reminder: AutoReminder) => {
  try {
    await trackingStore.markReminderAsRead(reminder.id)
    message.success('已标记为已读')
    loadInitialData()
  } catch (error) {
    message.error('标记失败')
  }
}

const handleDismiss = async (reminder: AutoReminder) => {
  try {
    await trackingStore.dismissReminder(reminder.id)
    message.success('已忽略提醒')
    loadInitialData()
  } catch (error) {
    message.error('操作失败')
  }
}

const handleEdit = (reminder: AutoReminder) => {
  currentReminder.value = reminder
  showEditReminderModal.value = true
}

const handleDelete = (reminder: AutoReminder) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除提醒"${reminder.title}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await trackingStore.deleteReminder(reminder.id)
        message.success('删除成功')
        loadInitialData()
        if (activeTab.value === 'all') {
          loadReminders()
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const markAllAsRead = async () => {
  dialog.info({
    title: '确认操作',
    content: `确定要将所有未读提醒标记为已读吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 这里需要批量标记已读的API
        const unreadReminders = [
          ...trackingStore.todayReminders.filter((r: any) => r.status === 'pending' || r.status === 'sent'),
          ...trackingStore.overdueReminders.filter((r: any) => r.status === 'pending' || r.status === 'sent')
        ]
        
        for (const reminder of unreadReminders) {
          await trackingStore.markReminderAsRead(reminder.id)
        }
        
        message.success('全部标记已读成功')
        loadInitialData()
      } catch (error) {
        message.error('操作失败')
      }
    }
  })
}

const handleCreateSuccess = () => {
  showCreateReminderModal.value = false
  loadInitialData()
  if (activeTab.value === 'all') {
    loadReminders()
  }
}

const handleEditSuccess = () => {
  showEditReminderModal.value = false
  currentReminder.value = null
  loadInitialData()
  if (activeTab.value === 'all') {
    loadReminders()
  }
}

// 生命周期
onMounted(() => {
  if (props.show) {
    loadInitialData()
  }
})
</script>

<style scoped>
.reminder-content {
  padding: 0;
}

.reminder-stats {
  margin-bottom: 20px;
}

.quick-actions {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>