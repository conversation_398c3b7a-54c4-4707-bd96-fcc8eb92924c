{"migrationId": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "timestamp": "2025-08-18T07:01:16.095Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 15, "failedTables": 6, "totalRecords": 165, "migratedRecords": 112, "failedRecords": 53}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:00:33.457Z", "errors": [], "endTime": "2025-08-18T07:00:40.681Z", "duration": 7224}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:00:40.685Z", "errors": [], "endTime": "2025-08-18T07:00:43.177Z", "duration": 2492}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:00:43.180Z", "errors": [], "endTime": "2025-08-18T07:00:46.113Z", "duration": 2933}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:00:46.116Z", "errors": [], "endTime": "2025-08-18T07:00:47.896Z", "duration": 1780}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:00:47.899Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:00:48.178Z", "errors": [], "endTime": "2025-08-18T07:00:51.168Z", "duration": 2990}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T07:00:51.171Z", "errors": ["Field 'name' doesn't have a default value"], "endTime": "2025-08-18T07:00:54.376Z", "duration": 3205}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:00:54.378Z", "errors": [], "endTime": "2025-08-18T07:00:55.783Z", "duration": 1405}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:00:55.786Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:00:56.070Z", "errors": ["Unknown column 'conversion_count' in 'field list'"], "endTime": "2025-08-18T07:00:58.798Z", "duration": 2728}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:00:58.801Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:00:59.435Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:01:00.031Z", "errors": ["Unknown column 'action_items' in 'field list'"], "endTime": "2025-08-18T07:01:03.796Z", "duration": 3765}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:01:03.799Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:01:04.239Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:01:09.468Z", "duration": 5229}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:01:09.471Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:01:09.990Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:01:10.655Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:01:11.669Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:01:12.375Z", "errors": ["Unknown column 'tags' in 'field list'"], "endTime": "2025-08-18T07:01:14.537Z", "duration": 2162}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:01:14.539Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:01:16.092Z", "duration": 1553}], "logs": [{"id": "b4386451-c820-4f16-b61b-f21af885ba82", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:00:33.458Z", "end_time": "2025-08-18T07:00:40.681Z", "duration_ms": 7223}, {"id": "1360c7df-b41a-4bd8-a113-0d462917033e", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:00:40.685Z", "end_time": "2025-08-18T07:00:43.177Z", "duration_ms": 2492}, {"id": "a11d2cbf-c8e7-47b0-ace6-172bbbfd2a7a", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:00:43.180Z", "end_time": "2025-08-18T07:00:46.113Z", "duration_ms": 2933}, {"id": "ae81894f-c8f8-46e7-882d-46dfcc710b28", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:00:46.116Z", "end_time": "2025-08-18T07:00:47.896Z", "duration_ms": 1780}, {"id": "cdfa498a-6932-45e6-9845-9b014296b258", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:47.899Z", "end_time": "2025-08-18T07:00:48.178Z", "duration_ms": 279}, {"id": "c4588682-f964-4bdf-8217-10d05c38b242", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:00:48.178Z", "end_time": "2025-08-18T07:00:51.168Z", "duration_ms": 2990}, {"id": "1f9a42b2-d582-49ae-8b3c-8738136ae4b9", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:51.171Z", "end_time": "2025-08-18T07:00:54.376Z", "duration_ms": 3205}, {"id": "4ef8f67b-755c-44d7-9e6e-00fa3cc04489", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:00:54.378Z", "end_time": "2025-08-18T07:00:55.783Z", "duration_ms": 1405}, {"id": "ef3957e9-e895-4159-a61c-b7ed1e331675", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:55.786Z", "end_time": "2025-08-18T07:00:56.069Z", "duration_ms": 283}, {"id": "464cdfe2-032d-4e35-98ed-95113a2edb0f", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:56.070Z", "end_time": "2025-08-18T07:00:58.798Z", "duration_ms": 2728}, {"id": "42a01efb-7f15-469e-91a5-a5a753c952f9", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:58.801Z", "end_time": "2025-08-18T07:00:59.434Z", "duration_ms": 633}, {"id": "ca29e6f0-d928-4bf3-9ae2-d6c138169c13", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:00:59.435Z", "end_time": "2025-08-18T07:01:00.031Z", "duration_ms": 596}, {"id": "8ef11942-c784-4f77-9736-6a910709a534", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:00.031Z", "end_time": "2025-08-18T07:01:03.796Z", "duration_ms": 3765}, {"id": "1886c14a-d905-418e-aea9-c44baeeedfbd", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:03.799Z", "end_time": "2025-08-18T07:01:04.239Z", "duration_ms": 440}, {"id": "4f596616-bce3-410a-8426-0cf1d3faa431", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:04.239Z", "end_time": "2025-08-18T07:01:09.468Z", "duration_ms": 5229}, {"id": "2ffca2c8-3e29-4c9d-9322-229d67d63d9b", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:09.471Z", "end_time": "2025-08-18T07:01:09.990Z", "duration_ms": 519}, {"id": "e885f859-6e95-47a4-b58f-342005594aea", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:09.990Z", "end_time": "2025-08-18T07:01:10.655Z", "duration_ms": 665}, {"id": "b09c1815-1d4b-4f35-ae98-f1070fd3cc1e", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:10.655Z", "end_time": "2025-08-18T07:01:11.669Z", "duration_ms": 1014}, {"id": "11a5c6cd-dfee-4a1d-adc3-470426fe4d74", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:11.669Z", "end_time": "2025-08-18T07:01:12.375Z", "duration_ms": 706}, {"id": "3f7d2c0c-60d6-4caa-8fda-9c97697c3043", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:12.375Z", "end_time": "2025-08-18T07:01:14.537Z", "duration_ms": 2162}, {"id": "35dba6d7-d07d-4592-ae95-92899b8a7ab1", "migration_id": "d12f327f-b571-4cd9-a1e9-a0f6f3512480", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:01:14.539Z", "end_time": "2025-08-18T07:01:16.092Z", "duration_ms": 1553}]}