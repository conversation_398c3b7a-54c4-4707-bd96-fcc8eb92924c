<template>
  <div class="customer-value-analysis">
    <!-- 页面头部 -->

    <!-- 筛选条件 -->
    <n-card class="filter-card">
      <n-form inline :label-width="80">
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="filters.dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="客户等级">
          <n-select
            v-model:value="filters.customerLevel"
            placeholder="选择客户等级"
            clearable
            style="width: 150px"
            :options="levelOptions"
          />
        </n-form-item>
        <n-form-item label="价值区间">
          <n-select
            v-model:value="filters.valueRange"
            placeholder="选择价值区间"
            clearable
            style="width: 150px"
            :options="valueRangeOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button @click="handleSearch" type="primary">
              <template #icon>
                <n-icon><Search /></n-icon>
              </template>
              查询
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><Refresh /></n-icon>
              </template>
              重置
            </n-button>
            <n-button @click="handleExport">
              <template #icon>
                <n-icon><Download /></n-icon>
              </template>
              导出
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 统计概览 -->
    <n-grid :cols="4" :x-gap="20" class="stats-grid">
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="总客户数" :value="stats.totalCustomers">
            <template #suffix>
              <n-icon color="#18a058"><People /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="高价值客户" :value="stats.highValueCustomers">
            <template #suffix>
              <n-icon color="#f0a020"><Star /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="平均客户价值" :value="stats.avgCustomerValue" :precision="2">
            <template #prefix>¥</template>
            <template #suffix>
              <n-icon color="#2080f0"><TrendingUp /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="总消费金额" :value="stats.totalRevenue" :precision="2">
            <template #prefix>¥</template>
            <template #suffix>
              <n-icon color="#d03050"><Cash /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表分析 -->
    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="客户价值分布">
          <div ref="valueDistributionChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="客户等级分布">
          <div ref="levelDistributionChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="1" class="charts-grid">
      <n-grid-item>
        <n-card title="客户价值趋势">
          <div ref="valueTrendChart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 客户价值列表 -->
    <n-card title="客户价值详情" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客户姓名或手机号"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
        </n-space>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="customerValueList"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: CustomerValue) => row.id"
        size="medium"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NIcon,
  NTag,
  NStatistic,
  NGrid,
  NGridItem,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import {
  SearchOutline as Search,
  RefreshOutline as Refresh,
  DownloadOutline as Download,
  PeopleOutline as People,
  StarOutline as Star,
  TrendingUpOutline as TrendingUp,
  CashOutline as Cash
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface CustomerValue {
  id: number
  name: string
  phone: string

  level: string
  totalOrders: number
  totalAmount: number
  avgOrderAmount: number
  lastOrderDate: string
  valueScore: number
  rfmScore: {
    recency: number
    frequency: number
    monetary: number
  }
  tags: string[]
  createdAt: string
}

const message = useMessage()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const customerValueList = ref<CustomerValue[]>([])

// 图表引用
const valueDistributionChart = ref<HTMLElement>()
const levelDistributionChart = ref<HTMLElement>()
const valueTrendChart = ref<HTMLElement>()

// 图表实例
let valueDistributionInstance: echarts.ECharts | null = null
let levelDistributionInstance: echarts.ECharts | null = null
let valueTrendInstance: echarts.ECharts | null = null

// 筛选条件
const filters = reactive({
  dateRange: null as [number, number] | null,
  customerLevel: null as string | null,
  valueRange: null as string | null
})

// 统计数据
const stats = reactive({
  totalCustomers: 0,
  highValueCustomers: 0,
  avgCustomerValue: 0,
  totalRevenue: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
    loadCustomerValueList()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadCustomerValueList()
  }
})

// 选项配置
const levelOptions = [
  { label: 'VIP客户', value: 'vip' },
  { label: '重要客户', value: 'important' },
  { label: '普通客户', value: 'normal' },
  { label: '潜在客户', value: 'potential' }
]

const valueRangeOptions = [
  { label: '10万以上', value: '100000+' },
  { label: '5-10万', value: '50000-100000' },
  { label: '1-5万', value: '10000-50000' },
  { label: '1万以下', value: '0-10000' }
]

// 表格列配置
const columns: DataTableColumns<CustomerValue> = [
  {
    title: '客户姓名',
    key: 'name',
    width: 120,
    fixed: 'left'
  },
  {
    title: '手机号',
    key: 'phone',
    width: 130
  },
  {
    title: '客户等级',
    key: 'level',
    width: 100,
    render(row) {
      const levelMap: Record<string, { text: string; type: string }> = {
        vip: { text: 'VIP客户', type: 'error' },
        important: { text: '重要客户', type: 'warning' },
        normal: { text: '普通客户', type: 'info' },
        potential: { text: '潜在客户', type: 'default' }
      }
      const level = levelMap[row.level] || { text: row.level, type: 'default' }
      return h('n-tag', { type: level.type, size: 'small' }, { default: () => level.text })
    }
  },
  {
    title: '订单数量',
    key: 'totalOrders',
    width: 100,
    sorter: (a, b) => a.totalOrders - b.totalOrders
  },
  {
    title: '消费总额',
    key: 'totalAmount',
    width: 120,
    render(row) {
      return `¥${row.totalAmount.toLocaleString()}`
    },
    sorter: (a, b) => a.totalAmount - b.totalAmount
  },
  {
    title: '平均订单金额',
    key: 'avgOrderAmount',
    width: 130,
    render(row) {
      return `¥${row.avgOrderAmount.toLocaleString()}`
    },
    sorter: (a, b) => a.avgOrderAmount - b.avgOrderAmount
  },
  {
    title: '价值评分',
    key: 'valueScore',
    width: 100,
    render(row) {
      const color = row.valueScore >= 80 ? '#f0a020' : row.valueScore >= 60 ? '#18a058' : '#2080f0'
      return h('span', { style: { color, fontWeight: 'bold' } }, row.valueScore)
    },
    sorter: (a, b) => a.valueScore - b.valueScore
  },
  {
    title: 'RFM评分',
    key: 'rfmScore',
    width: 120,
    render(row) {
      return `R:${row.rfmScore.recency} F:${row.rfmScore.frequency} M:${row.rfmScore.monetary}`
    }
  },
  {
    title: '最后消费',
    key: 'lastOrderDate',
    width: 120,
    render(row) {
      return new Date(row.lastOrderDate).toLocaleDateString()
    }
  },
  {
    title: '客户标签',
    key: 'tags',
    width: 150,
    render(row) {
      return h('n-space', { size: 'small' }, {
        default: () => row.tags.map(tag => 
          h('n-tag', { size: 'small', type: 'info' }, { default: () => tag })
        )
      })
    }
  }
]

// 计算属性
const filteredCustomerValueList = computed(() => {
  if (!searchKeyword.value) return customerValueList.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return customerValueList.value.filter(customer => 
    customer.name.toLowerCase().includes(keyword) ||
    customer.phone.includes(keyword)
  )
})

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadCustomerValueList()
  loadCharts()
}

const handleReset = () => {
  Object.assign(filters, {
    dateRange: null,
    customerLevel: null,
    valueRange: null
  })
  searchKeyword.value = ''
  pagination.page = 1
  loadCustomerValueList()
  loadCharts()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

// 加载客户价值列表
const loadCustomerValueList = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: CustomerValue[] = [
      {
        id: 1,
        name: '张三',
        phone: '13800138001',
    
        level: 'vip',
        totalOrders: 15,
        totalAmount: 156800,
        avgOrderAmount: 10453,
        lastOrderDate: '2024-01-15T10:30:00Z',
        valueScore: 95,
        rfmScore: { recency: 5, frequency: 5, monetary: 5 },
        tags: ['高价值', '忠实客户', '推荐客户'],
        createdAt: '2023-06-15T10:30:00Z'
      },
      {
        id: 2,
        name: '李四',
        phone: '13800138002',
    
        level: 'important',
        totalOrders: 8,
        totalAmount: 68500,
        avgOrderAmount: 8562,
        lastOrderDate: '2024-01-10T14:20:00Z',
        valueScore: 78,
        rfmScore: { recency: 4, frequency: 4, monetary: 4 },
        tags: ['重要客户', '活跃用户'],
        createdAt: '2023-08-20T09:15:00Z'
      },
      {
        id: 3,
        name: '王五',
        phone: '13800138003',
        level: 'normal',
        totalOrders: 3,
        totalAmount: 12800,
        avgOrderAmount: 4267,
        lastOrderDate: '2023-12-25T16:45:00Z',
        valueScore: 45,
        rfmScore: { recency: 2, frequency: 2, monetary: 2 },
        tags: ['普通客户'],
        createdAt: '2023-10-10T11:30:00Z'
      }
    ]
    
    customerValueList.value = mockData
    pagination.itemCount = mockData.length
    
    // 更新统计数据
    updateStats()
  } catch (error) {
    message.error('获取客户价值数据失败')
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  const data = customerValueList.value
  
  stats.totalCustomers = data.length
  stats.highValueCustomers = data.filter(c => c.valueScore >= 80).length
  stats.avgCustomerValue = data.length > 0 ? data.reduce((sum, c) => sum + c.totalAmount, 0) / data.length : 0
  stats.totalRevenue = data.reduce((sum, c) => sum + c.totalAmount, 0)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (valueDistributionChart.value) {
      valueDistributionInstance = echarts.init(valueDistributionChart.value)
    }
    if (levelDistributionChart.value) {
      levelDistributionInstance = echarts.init(levelDistributionChart.value)
    }
    if (valueTrendChart.value) {
      valueTrendInstance = echarts.init(valueTrendChart.value)
    }
    
    loadCharts()
  })
}

// 加载图表数据
const loadCharts = () => {
  // 客户价值分布图
  if (valueDistributionInstance) {
    const option = {
      title: { text: '客户价值分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 12, name: '10万以上' },
          { value: 25, name: '5-10万' },
          { value: 45, name: '1-5万' },
          { value: 68, name: '1万以下' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    valueDistributionInstance.setOption(option)
  }
  
  // 客户等级分布图
  if (levelDistributionInstance) {
    const option = {
      title: { text: '客户等级分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 15, name: 'VIP客户' },
          { value: 35, name: '重要客户' },
          { value: 80, name: '普通客户' },
          { value: 20, name: '潜在客户' }
        ]
      }]
    }
    levelDistributionInstance.setOption(option)
  }
  
  // 客户价值趋势图
  if (valueTrendInstance) {
    const option = {
      title: { text: '客户价值趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        name: '平均客户价值',
        type: 'line',
        data: [8500, 9200, 9800, 10500, 11200, 10800],
        smooth: true
      }]
    }
    valueTrendInstance.setOption(option)
  }
}

// 初始化
onMounted(() => {
  loadCustomerValueList()
  initCharts()
})
</script>

<style scoped>
.customer-value-analysis {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.charts-grid {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}
</style>