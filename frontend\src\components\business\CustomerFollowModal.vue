<template>
  <n-modal v-model:show="visible" preset="card" title="客户跟进记录" style="width: 900px; max-height: 80vh;">
    <div class="customer-follow-modal">
      <!-- 客户基本信息 -->
      <div class="customer-info-section">
        <n-card size="small" :bordered="false" class="customer-info-card">
          <template #header>
            <div class="customer-header">
              <h3>{{ customer?.name || '客户信息' }}</h3>
              <n-tag v-if="customer?.status" :type="getStatusType(customer.status)">
                {{ getStatusLabel(customer.status) }}
              </n-tag>
            </div>
          </template>
          <n-descriptions size="small" :column="3">
            <n-descriptions-item label="联系电话">{{ customer?.phone || customer?.mobile || '-' }}</n-descriptions-item>
            <n-descriptions-item label="公司">{{ customer?.company || '-' }}</n-descriptions-item>
            <n-descriptions-item label="客户来源">{{ getSourceLabel(customer?.source) || '-' }}</n-descriptions-item>
          </n-descriptions>
        </n-card>
      </div>

      <!-- 跟进记录列表 -->
      <div class="follow-records-section">
        <div class="section-header">
          <h4>跟进记录</h4>
          <n-button type="primary" size="small" @click="handleAddFollow">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            新增跟进
          </n-button>
        </div>

        <div class="follow-records">
          <n-empty v-if="followRecords.length === 0" description="暂无跟进记录">
            <template #extra>
              <n-button size="small" @click="handleAddFollow">创建首次跟进</n-button>
            </template>
          </n-empty>

          <div v-else class="records-list">
            <n-card 
              v-for="(record, index) in followRecords" 
              :key="record.id || index"
              class="record-item"
              size="small"
            >
              <div class="record-header">
                <div class="record-meta">
                  <span class="record-time">{{ formatDate(record.follow_time) }}</span>
                  <n-tag size="small" :type="getFollowTypeColor(record.type)">{{ record.type }}</n-tag>
                </div>
                <div class="record-actions">
                  <n-button text size="small" type="primary" @click="handleEditFollow(record)">
                    <template #icon><n-icon><CreateOutline /></n-icon></template>
                    编辑
                  </n-button>
                  <n-button text size="small" type="error" @click="record.id && handleDeleteFollow(record.id)">
                    <template #icon><n-icon><TrashOutline /></n-icon></template>
                    删除
                  </n-button>
                </div>
              </div>
              <div class="record-content">
                <p class="follow-content">{{ record.content }}</p>
                <div class="record-details" v-if="record.remark || record.next_follow_time">
                  <div v-if="record.remark" class="detail-item">
                    <span class="label">备注：</span>
                    <span class="value">{{ record.remark }}</span>
                  </div>
                  <div v-if="record.next_follow_time" class="detail-item">
                    <span class="label">下次跟进：</span>
                    <span class="value">{{ formatDate(record.next_follow_time) }}</span>
                  </div>
                </div>
              </div>
            </n-card>
          </div>
        </div>
      </div>
    </div>

    <template #action>
      <n-space>
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>

    <!-- 跟进记录表单弹窗 -->
    <n-modal v-model:show="showFollowFormModal" preset="card" title="跟进记录" style="width: 600px;">
      <n-form
        ref="followFormRef"
        :model="followForm"
        :rules="followRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="跟进时间" path="follow_time">
          <n-date-picker
            v-model:value="followForm.follow_time"
            type="datetime"
            placeholder="选择跟进时间"
            style="width: 100%"
          />
        </n-form-item>

        <n-form-item label="跟进方式" path="type">
          <n-select
            v-model:value="followForm.type"
            :options="followTypeOptions"
            placeholder="请选择跟进方式"
          />
        </n-form-item>

        <n-form-item label="跟进内容" path="content">
          <n-input
            v-model:value="followForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入跟进内容"
          />
        </n-form-item>

        <n-form-item label="下次跟进时间" path="next_follow_time">
          <n-date-picker
            v-model:value="followForm.next_follow_time"
            type="datetime"
            placeholder="选择下次跟进时间"
            style="width: 100%"
          />
        </n-form-item>

        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="followForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showFollowFormModal = false">取消</n-button>
          <n-button type="primary" @click="handleSaveFollow">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NSpace,
  NIcon,
  NEmpty,
  NTag,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  useMessage
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { formatDate } from '@/utils'

interface FollowRecord {
  id?: number
  customer_id: number
  customer_name: string
  follow_time: string
  type: string
  content: string
  next_follow_time?: string
  status: string
  created_by: number
  created_by_name: string
  remark?: string
  created_at: string
  updated_at: string
  stage: 'follow'
}

interface Props {
  show: boolean
  customer?: any
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()

// 弹窗显示状态
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 跟进记录数据
const followRecords = ref<FollowRecord[]>([])
const showFollowFormModal = ref(false)
const editingRecord = ref<FollowRecord | null>(null)
const followFormRef = ref()

// 跟进表单
const followForm = reactive({
  follow_time: Date.now(),
  type: '',
  content: '',
  next_follow_time: null as number | null,
  remark: ''
})

// 表单验证规则
const followRules = {
  follow_time: [
    { required: true, message: '请选择跟进时间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择跟进方式', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' }
  ]
}

// 跟进方式选项
const followTypeOptions = [
  { label: '电话跟进', value: '电话跟进' },
  { label: '微信沟通', value: '微信沟通' },
  { label: '上门拜访', value: '上门拜访' },
  { label: '邮件联系', value: '邮件联系' },
  { label: '短信联系', value: '短信联系' },
  { label: '其他方式', value: '其他方式' }
]

// 获取客户状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'initial': '初次接触',
    'following': '跟进中',
    'appointed': '已预约',
    'met': '已见面',
    'signed': '已签约',
    'lost': '已流失'
  }
  return statusMap[status] || status
}

// 获取客户状态类型
const getStatusType = (status: string): 'primary' | 'info' | 'success' | 'warning' | 'default' | 'error' => {
  const typeMap: Record<string, 'primary' | 'info' | 'success' | 'warning' | 'default' | 'error'> = {
    'initial': 'info',
    'following': 'warning',
    'appointed': 'primary',
    'met': 'success',
    'signed': 'success',
    'lost': 'error'
  }
  return typeMap[status] || 'default'
}

// 获取客户来源标签
const getSourceLabel = (source: string) => {
  const sourceMap: Record<string, string> = {
    'wechat': '微信',
    'phone': '电话',
    'referral': '朋友介绍',
    'online': '网络推广',
    'other': '其他'
  }
  return sourceMap[source] || source
}

// 获取跟进方式颜色
const getFollowTypeColor = (type: string): 'primary' | 'info' | 'success' | 'warning' | 'default' => {
  const colorMap: Record<string, 'primary' | 'info' | 'success' | 'warning' | 'default'> = {
    '电话跟进': 'primary',
    '微信沟通': 'success',
    '上门拜访': 'warning',
    '邮件联系': 'info',
    '短信联系': 'default',
    '其他方式': 'default'
  }
  return colorMap[type] || 'default'
}

// 加载跟进记录
const loadFollowRecords = () => {
  if (!props.customer) return
  
  // 模拟数据
  followRecords.value = [
    {
      id: 1,
      customer_id: props.customer.id,
      customer_name: props.customer.name,
      follow_time: new Date(Date.now() - 86400000).toISOString(),
      type: '电话跟进',
      content: '初次联系客户，了解装修需求。客户表示对现代简约风格比较感兴趣，预算在20-30万之间。',
      next_follow_time: new Date(Date.now() + 86400000).toISOString(),
      status: 'completed',
      created_by: 1,
      created_by_name: '当前用户',
      remark: '客户对现代简约风格感兴趣，需要进一步了解具体需求',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      stage: 'follow' as const
    },
    {
      id: 2,
      customer_id: props.customer.id,
      customer_name: props.customer.name,
      follow_time: new Date(Date.now() - 172800000).toISOString(),
      type: '微信沟通',
      content: '发送了公司的装修案例图片，客户反馈很满意，约定明天上门看房。',
      next_follow_time: new Date(Date.now() + 172800000).toISOString(),
      status: 'completed',
      created_by: 1,
      created_by_name: '当前用户',
      remark: '客户对案例很满意，明天上门看房',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      stage: 'follow' as const
    }
  ]
}

// 重置跟进表单
const resetFollowForm = () => {
  Object.assign(followForm, {
    follow_time: Date.now(),
    type: '',
    content: '',
    next_follow_time: null,
    remark: ''
  })
}

// 新增跟进
const handleAddFollow = () => {
  editingRecord.value = null
  resetFollowForm()
  showFollowFormModal.value = true
}

// 编辑跟进
const handleEditFollow = (record: FollowRecord) => {
  editingRecord.value = record
  Object.assign(followForm, {
    follow_time: new Date(record.follow_time).getTime(),
    type: record.type,
    content: record.content,
    next_follow_time: record.next_follow_time ? new Date(record.next_follow_time).getTime() : null,
    remark: record.remark || ''
  })
  showFollowFormModal.value = true
}

// 删除跟进
const handleDeleteFollow = (recordId: number) => {
  followRecords.value = followRecords.value.filter(record => record.id !== recordId)
  message.success('删除成功')
}

// 保存跟进记录
const handleSaveFollow = async () => {
  try {
    await followFormRef.value?.validate()
    
    const recordData = {
      id: editingRecord.value?.id || Date.now(),
      customer_id: props.customer?.id || 0,
      customer_name: props.customer?.name || '',
      follow_time: new Date(followForm.follow_time).toISOString(),
      type: followForm.type,
      content: followForm.content,
      next_follow_time: followForm.next_follow_time ? new Date(followForm.next_follow_time).toISOString() : undefined,
      status: 'completed',
      created_by: 1,
      created_by_name: '当前用户',
      remark: followForm.remark,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      stage: 'follow' as const
    }
    
    if (editingRecord.value) {
      // 编辑
      const index = followRecords.value.findIndex(r => r.id === editingRecord.value?.id)
      if (index !== -1) {
        followRecords.value[index] = recordData
      }
      message.success('跟进记录更新成功')
    } else {
      // 新增
      followRecords.value.unshift(recordData)
      message.success('跟进记录添加成功')
    }
    
    showFollowFormModal.value = false
  } catch (error) {
    console.error('保存跟进记录失败:', error)
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.customer) {
    loadFollowRecords()
  }
})
</script>

<style scoped>
.customer-follow-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.customer-info-section {
  margin-bottom: 8px;
}

.customer-info-card {
  background: #fafafa;
}

.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.customer-header h3 {
  margin: 0;
  color: #333;
}

.follow-records-section {
  flex: 1;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.follow-records {
  max-height: 400px;
  overflow-y: auto;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  border-left: 3px solid #1677ff;
  transition: all 0.2s ease;
}

.record-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-time {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.record-content {
  padding-left: 8px;
}

.follow-content {
  margin: 0 0 8px 0;
  color: #333;
  line-height: 1.5;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.detail-item .label {
  color: #666;
  margin-right: 4px;
  min-width: 60px;
}

.detail-item .value {
  color: #333;
}

/* 滚动条样式 */
.follow-records::-webkit-scrollbar {
  width: 6px;
}

.follow-records::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.follow-records::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.follow-records::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>