#!/usr/bin/env python3
"""
修复departments表结构
"""

import pymysql
from app.config import settings
from app.models.department import Department

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=settings.database.host,
        port=settings.database.port,
        user=settings.database.username,
        password=settings.database.password,
        database=settings.database.database,
        charset='utf8mb4'
    )

def check_departments_table():
    """检查departments表结构"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("=== 检查departments表结构 ===")
        
        # 获取表结构
        cursor.execute("DESCRIBE departments")
        db_columns = cursor.fetchall()
        
        print("\n数据库中的字段:")
        db_field_names = []
        for column in db_columns:
            field_name = column[0]
            field_type = column[1]
            db_field_names.append(field_name)
            print(f"  {field_name}: {field_type}")
        
        # 获取Department模型中定义的字段
        print("\n\nDepartment模型中定义的字段:")
        model_columns = Department.__table__.columns
        model_field_names = []
        
        for column in model_columns:
            model_field_names.append(column.name)
            print(f"  {column.name}: {column.type}")
        
        # 找出缺少的字段
        missing_in_db = set(model_field_names) - set(db_field_names)
        
        print("\n\n=== 字段对比结果 ===")
        if missing_in_db:
            print(f"❌ 数据库中缺少的字段: {', '.join(missing_in_db)}")
            return missing_in_db
        else:
            print("✅ 数据库包含所有模型字段")
            return set()
        
    finally:
        cursor.close()
        conn.close()

def add_missing_departments_fields(missing_fields):
    """添加departments表缺少的字段"""
    if not missing_fields:
        return
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 字段定义映射
    field_definitions = {
        'code': 'VARCHAR(50) DEFAULT NULL COMMENT \'部门代码\'',
        'level': 'INT DEFAULT 1 COMMENT \'部门层级\'',
        'sort_order': 'INT DEFAULT 0 COMMENT \'排序\'',
        'manager_id': 'VARCHAR(36) DEFAULT NULL COMMENT \'部门经理ID\'',
        'parent_id': 'VARCHAR(36) DEFAULT NULL COMMENT \'父部门ID\''
    }
    
    try:
        print("\n=== 开始添加departments表缺少的字段 ===")
        
        for field in missing_fields:
            if field in field_definitions:
                statement = f"ALTER TABLE departments ADD COLUMN {field} {field_definitions[field]}"
                try:
                    print(f"执行: {statement}")
                    cursor.execute(statement)
                    conn.commit()
                    print("  ✅ 成功")
                except pymysql.err.OperationalError as e:
                    if "Duplicate column name" in str(e):
                        print("  ⚠️  字段已存在，跳过")
                    else:
                        print(f"  ❌ 失败: {e}")
                        raise
            else:
                print(f"⚠️  未知字段 {field}，需要手动定义")
        
        print("\n=== departments表字段添加完成 ===")
        
    except Exception as e:
        print(f"❌ 添加字段过程中出现错误: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    try:
        missing_fields = check_departments_table()
        
        if missing_fields:
            add_missing_departments_fields(missing_fields)
            print("\n🎉 departments表字段修复完成！")
        else:
            print("\n✅ departments表结构完整，无需修复")
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()