<template>
  <n-modal :show="show" @update:show="$emit('update:show', $event)">
    <n-card
      style="width: 900px; max-height: 80vh; overflow-y: auto"
      title="活动详情"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div v-if="campaign" class="campaign-detail">
        <!-- 基本信息 -->
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="活动名称">
            {{ campaign.name }}
          </n-descriptions-item>
          <n-descriptions-item label="活动类型">
            <n-tag :type="getTypeTagType(campaign.type)">
              {{ getTypeLabel(campaign.type) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="活动状态">
            <n-tag :type="getStatusTagType(campaign.status)">
              {{ getStatusLabel(campaign.status) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ formatDateTime(campaign.created_at) }}
          </n-descriptions-item>
          <n-descriptions-item label="开始时间">
            {{ formatDateTime(campaign.start_time) }}
          </n-descriptions-item>
          <n-descriptions-item label="结束时间">
            {{ formatDateTime(campaign.end_time) }}
          </n-descriptions-item>
          <n-descriptions-item label="目标受众">
            {{ campaign.target_audience || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="活动预算">
            {{ campaign.budget ? `¥${campaign.budget.toLocaleString()}` : '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="活动描述" :span="2">
            {{ campaign.description || '-' }}
          </n-descriptions-item>
        </n-descriptions>

        <!-- 统计数据 -->
        <n-divider title-placement="left">统计数据</n-divider>
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic label="参与人数" :value="campaign.participants_count || 0" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="转化率" :value="getConversionRate()" suffix="%" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="分享次数" :value="getShareCount()" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="总收益" :value="getTotalRevenue()" prefix="¥" />
          </n-grid-item>
        </n-grid>

        <!-- 活动配置详情 -->
        <n-divider title-placement="left">活动配置</n-divider>
        
        <!-- 抽奖活动配置 -->
        <template v-if="campaign.type === 'lottery'">
          <h4>奖品设置</h4>
          <n-data-table
            :columns="prizeColumns"
            :data="campaign.config?.prizes || []"
            :pagination="false"
            size="small"
          />
          
          <h4 style="margin-top: 20px">参与条件</h4>
          <n-space>
            <n-tag v-for="rule in campaign.config?.participation_rules || []" :key="rule" type="info">
              {{ getParticipationRuleLabel(rule) }}
            </n-tag>
          </n-space>
          
          <p style="margin-top: 10px">
            <strong>每人限制：</strong>{{ campaign.config?.max_attempts_per_user || 1 }}次
          </p>
        </template>

        <!-- 秒杀活动配置 -->
        <template v-if="campaign.type === 'flash_sale'">
          <n-descriptions :column="2" bordered size="small">
            <n-descriptions-item label="商品名称">
              {{ campaign.config?.product_name || '-' }}
            </n-descriptions-item>
            <n-descriptions-item label="原价">
              ¥{{ campaign.config?.original_price || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="秒杀价">
              ¥{{ campaign.config?.sale_price || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="库存数量">
              {{ campaign.config?.stock_quantity || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="每人限购">
              {{ campaign.config?.max_purchase_per_user || 1 }}件
            </n-descriptions-item>
            <n-descriptions-item label="已售数量">
              {{ getSoldQuantity() }}件
            </n-descriptions-item>
          </n-descriptions>
        </template>

        <!-- 转发分享配置 -->
        <template v-if="campaign.type === 'share'">
          <n-descriptions :column="1" bordered size="small">
            <n-descriptions-item label="分享内容">
              <div class="share-content">
                {{ campaign.config?.share_content || '-' }}
              </div>
            </n-descriptions-item>
            <n-descriptions-item label="分享奖励">
              {{ campaign.config?.reward_type || '-' }} × {{ campaign.config?.reward_amount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="目标分享次数">
              {{ campaign.config?.target_shares || 0 }}次
            </n-descriptions-item>
            <n-descriptions-item label="当前分享次数">
              {{ getCurrentShares() }}次
            </n-descriptions-item>
          </n-descriptions>
        </template>

        <!-- 优惠券配置 -->
        <template v-if="campaign.type === 'coupon'">
          <n-descriptions :column="2" bordered size="small">
            <n-descriptions-item label="优惠券类型">
              {{ getCouponTypeLabel(campaign.config?.coupon_type) }}
            </n-descriptions-item>
            <n-descriptions-item label="优惠金额">
              {{ getCouponDiscountText() }}
            </n-descriptions-item>
            <n-descriptions-item label="使用门槛">
              ¥{{ campaign.config?.min_order_amount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="发放数量">
              {{ campaign.config?.total_quantity || 0 }}张
            </n-descriptions-item>
            <n-descriptions-item label="已领取">
              {{ getClaimedCoupons() }}张
            </n-descriptions-item>
            <n-descriptions-item label="已使用">
              {{ getUsedCoupons() }}张
            </n-descriptions-item>
          </n-descriptions>
        </template>

        <!-- 积分活动配置 -->
        <template v-if="campaign.type === 'points'">
          <h4>积分规则</h4>
          <n-data-table
            :columns="pointRuleColumns"
            :data="campaign.config?.point_rules || []"
            :pagination="false"
            size="small"
          />
        </template>

        <!-- 活动数据图表 -->
        <n-divider title-placement="left">数据趋势</n-divider>
        <div class="chart-container">
          <n-empty description="图表功能开发中" />
        </div>
      </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="$emit('update:show', false)">关闭</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { DataTableColumns } from 'naive-ui'

interface Campaign {
  id: number
  name: string
  type: string
  description?: string
  status: string
  start_time: string
  end_time: string
  target_audience?: string
  budget?: number
  participants_count?: number
  conversion_rate?: number
  created_at: string
  updated_at: string
  config?: any
}

interface Props {
  show: boolean
  campaign?: Campaign | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// 奖品表格列
const prizeColumns: DataTableColumns = [
  {
    title: '奖品名称',
    key: 'name'
  },
  {
    title: '数量',
    key: 'quantity'
  },
  {
    title: '中奖率',
    key: 'probability',
    render(row: any) {
      return `${row.probability}%`
    }
  }
]

// 积分规则表格列
const pointRuleColumns: DataTableColumns = [
  {
    title: '行为',
    key: 'action'
  },
  {
    title: '获得积分',
    key: 'points'
  },
  {
    title: '每日限制',
    key: 'daily_limit'
  }
]

// 工具函数
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}

const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    lottery: '抽奖活动',
    flash_sale: '秒杀活动',
    share: '转发分享',
    coupon: '优惠券',
    points: '积分活动',
    other: '其他'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    lottery: 'info',
    flash_sale: 'error',
    share: 'success',
    coupon: 'warning',
    points: 'default',
    other: 'default'
  }
  return typeMap[type] || 'default'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '进行中',
    paused: '已暂停',
    ended: '已结束',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'default',
    active: 'success',
    paused: 'warning',
    ended: 'info',
    cancelled: 'error'
  }
  return statusMap[status] || 'default'
}

const getParticipationRuleLabel = (rule: string) => {
  const ruleMap: Record<string, string> = {
    follow: '关注账号',
    share: '分享活动',
    comment: '评论互动',
    invite: '邀请好友'
  }
  return ruleMap[rule] || rule
}

const getCouponTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    discount: '折扣券',
    cash: '现金券',
    free_shipping: '包邮券'
  }
  return typeMap[type] || type
}

const getCouponDiscountText = () => {
  if (!props.campaign?.config) return '-'
  const { coupon_type, discount_amount } = props.campaign.config
  if (coupon_type === 'discount') {
    return `${(discount_amount * 10).toFixed(1)}折`
  }
  return `¥${discount_amount}`
}

// 统计数据计算
const getConversionRate = () => {
  return props.campaign?.conversion_rate ? (props.campaign.conversion_rate * 100).toFixed(1) : '0.0'
}

const getShareCount = () => {
  // 这里应该从实际数据中获取
  return Math.floor(Math.random() * 1000)
}

const getTotalRevenue = () => {
  // 这里应该从实际数据中获取
  return Math.floor(Math.random() * 10000)
}

const getSoldQuantity = () => {
  // 这里应该从实际数据中获取
  const stockQuantity = props.campaign?.config?.stock_quantity || 0
  return Math.floor(stockQuantity * 0.3) // 模拟已售30%
}

const getCurrentShares = () => {
  // 这里应该从实际数据中获取
  const targetShares = props.campaign?.config?.target_shares || 0
  return Math.floor(targetShares * 0.6) // 模拟完成60%
}

const getClaimedCoupons = () => {
  // 这里应该从实际数据中获取
  const totalQuantity = props.campaign?.config?.total_quantity || 0
  return Math.floor(totalQuantity * 0.7) // 模拟已领取70%
}

const getUsedCoupons = () => {
  // 这里应该从实际数据中获取
  const claimedCoupons = getClaimedCoupons()
  return Math.floor(claimedCoupons * 0.4) // 模拟使用率40%
}
</script>

<style scoped>
.campaign-detail {
  padding: 0;
}

.share-content {
  max-width: 400px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

h4 {
  margin: 16px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}
</style>