<template>
  <div class="customer-table">
    <!-- 表格头部操作栏 -->
    <div class="table-header">
      <div class="table-title">
        <span>客户列表</span>
        <n-tag v-if="selectedRowKeys.length > 0" type="info" size="small">
          已选择 {{ selectedRowKeys.length }} 项
        </n-tag>
      </div>
      <div class="table-actions">
        <n-button
          v-if="selectedRowKeys.length > 0"
          type="primary"
          @click="handleBatchAssign"
        >
          <template #icon>
            <n-icon :component="UserAddOutlined" />
          </template>
          批量分配
        </n-button>
        <n-button
          v-if="selectedRowKeys.length > 0"
          type="warning"
          @click="handleMoveToPool"
        >
          <template #icon>
            <n-icon :component="SwapOutlined" />
          </template>
          移入公海
        </n-button>
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon :component="PlusOutlined" />
          </template>
          新增客户
        </n-button>
        <n-button @click="handleImport">
          <template #icon>
            <n-icon :component="ImportOutlined" />
          </template>
          导入
        </n-button>
        <n-button @click="handleExport">
          <template #icon>
            <n-icon :component="ExportOutlined" />
          </template>
          导出
        </n-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="data"
      :loading="loading"
      :pagination="paginationReactive"
      :row-key="(row: Customer) => row.id"
      :checked-row-keys="selectedRowKeys"
      @update:checked-row-keys="handleCheck"
      :scroll-x="1400"
      striped
      size="small"
      class="customer-data-table"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import {
  NDataTable,
  NButton,
  NIcon,
  NTag,
  NAvatar,
  NProgress,
  useMessage
} from 'naive-ui'
import type {
  DataTableColumns,
  DataTableRowKey,
  PaginationProps
} from 'naive-ui'
import {
  Add as PlusOutlined,
  PersonAdd as UserAddOutlined,
  SwapHorizontal as SwapOutlined,
  CloudUpload as ImportOutlined,
  CloudDownload as ExportOutlined,
  Create as EditOutlined,
  Eye as EyeOutlined,
  ArrowUp as ArrowUpOutlined
} from '@vicons/ionicons5'
import type { Customer } from '@/api/customerService'

// Props
interface Props {
  data: Customer[]
  loading?: boolean
  pagination?: PaginationProps
  selectedRowKeys?: DataTableRowKey[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  pagination: () => ({
    page: 1,
    pageSize: 20,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100]
  }),
  selectedRowKeys: () => []
})

// Emits
interface Emits {
  (e: 'update:selectedRowKeys', keys: DataTableRowKey[]): void
  (e: 'update:pagination', pagination: PaginationProps): void
  (e: 'add'): void
  (e: 'edit', customer: Customer): void
  (e: 'view', customer: Customer): void
  (e: 'follow', customer: Customer): void
  (e: 'moveToTop', customer: Customer): void
  (e: 'batchAssign', customerIds: string[]): void
  (e: 'moveToPool', customerIds: string[]): void
  (e: 'import'): void
  (e: 'export'): void
}

const emit = defineEmits<Emits>()

// Message
const message = useMessage()

// Refs
const tableRef = ref()
const selectedRowKeys = computed({
  get: () => props.selectedRowKeys,
  set: (value) => emit('update:selectedRowKeys', value)
})

// 分页配置
const paginationReactive = computed({
  get: () => props.pagination,
  set: (value) => emit('update:pagination', value)
})

// 获取客户状态标签类型
const getCustomerStatusTagType = (status: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const statusMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    potential: 'default',
    interested: 'info',
    following: 'warning',
    deal: 'success',
    invalid: 'error'
  }
  return statusMap[status] || 'default'
}

// 获取客户状态文本
const getCustomerStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    potential: '潜在客户',
    interested: '意向客户',
    following: '跟进中',
    deal: '已成交',
    invalid: '已失效'
  }
  return statusMap[status] || status
}

// 获取客户状态颜色
const getCustomerStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    potential: '#d9d9d9',
    interested: '#1890ff',
    following: '#faad14',
    deal: '#52c41a',
    invalid: '#ff4d4f'
  }
  return colorMap[status] || '#d9d9d9'
}

// 获取客户状态进度
const getCustomerStatusProgress = (status: string) => {
  const progressMap: Record<string, number> = {
    potential: 20,
    interested: 40,
    following: 60,
    deal: 100,
    invalid: 0
  }
  return progressMap[status] || 0
}

// 获取客户分类颜色
const getCustomerCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    A: '#52c41a',
    B: '#1890ff',
    C: '#faad14',
    D: '#ff4d4f'
  }
  return colorMap[category] || '#d9d9d9'
}

// 获取客户分类标签类型
const getCustomerCategoryTagType = (category: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    A: 'success',
    B: 'info',
    C: 'warning',
    D: 'error'
  }
  return typeMap[category] || 'default'
}

// 表格列配置
const columns: DataTableColumns<Customer> = [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '客户姓名',
    key: 'name',
    width: 200,
    fixed: 'left',
    render(row) {
      return h('div', { class: 'customer-name-cell' }, [
        h('div', { class: 'customer-avatar-container' }, [
          h(NAvatar, {
            size: 36,
            src: row.avatar,
            fallbackSrc: '/default-avatar.png',
            class: 'customer-avatar-img'
          }, {
            default: () => row.name?.charAt(0) || '客'
          })
        ]),
        h('div', { class: 'customer-info' }, [
          h(NButton, {
            text: true,
            type: 'primary',
            class: 'customer-name-button',
            onClick: () => emit('view', row)
          }, {
            default: () => row.name || '未知客户'
          }),
          h('div', { class: 'customer-category' }, [
            h(NTag, {
              size: 'small',
              type: getCustomerCategoryTagType(row.level || 'D'),
              style: {
                fontSize: '10px',
                padding: '0 4px',
                height: '16px',
                lineHeight: '14px'
              }
            }, {
              default: () => `${row.level || 'D'}级客户`
            })
          ])
        ])
      ])
    }
  },
  {
    title: '客户状态',
    key: 'status',
    width: 140,
    render(row) {
      const progress = getCustomerStatusProgress(row.status || 'potential')
      const color = getCustomerStatusColor(row.status || 'potential')
      
      return h('div', { class: 'stage-cell' }, [
        h(NTag, {
          type: getCustomerStatusTagType(row.status || 'potential'),
          size: 'small'
        }, {
          default: () => getCustomerStatusText(row.status || 'potential')
        }),
        h('div', { class: 'progress-bar' }, [
          h('div', {
            class: 'progress-fill',
            style: {
              width: `${progress}%`,
              backgroundColor: color,
              '--fill-color': color,
              '--fill-color-light': color + '80'
            }
          })
        ]),
        h('div', { class: 'progress-text' }, `${progress}%`)
      ])
    }
  },
  {
    title: '手机号码',
    key: 'phone',
    width: 120,
    render(row) {
      if (!row.phone) return '-'
      const phone = row.phone
      const maskedPhone = phone.length > 7 
        ? phone.slice(0, 3) + '****' + phone.slice(-4)
        : phone
      return h('span', { class: 'phone-cell' }, maskedPhone)
    }
  },
  {
    title: '性别',
    key: 'gender',
    width: 80,
    render(row) {
      const genderMap: Record<string, string> = {
        male: '男',
        female: '女'
      }
      return genderMap[row.gender || ''] || '-'
    }
  },
  {
    title: '装修类型',
    key: 'decorationType',
    width: 100,
    render(row) {
      const typeMap: Record<string, string> = {
        new: '新房装修',
        second_hand: '二手房装修',
        partial: '局部装修',
        soft: '软装设计'
      }
      return typeMap[row.decorationType || ''] || '-'
    }
  },
  {
    title: '小区名称',
    key: 'community',
    width: 150,
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.community || '-'
    }
  },
  {
    title: '客户来源',
    key: 'source',
    width: 100,
    render(row) {
      const sourceMap: Record<string, string> = {
        online: '线上推广',
        referral: '朋友介绍',
        telemarketing: '电话营销',
        store: '门店咨询',
        exhibition: '展会活动',
        other: '其他'
      }
      return sourceMap[row.source || ''] || '-'
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 120,
    render(row) {
      return row.created_at
        ? new Date(row.created_at).toLocaleDateString('zh-CN')
        : '-'
    }
  },
  {
    title: '最后跟进',
    key: 'lastFollowAt',
    width: 120,
    render(row) {
      return row.last_follow_time
        ? new Date(row.last_follow_time).toLocaleDateString('zh-CN')
        : '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row) {
      return h('div', { style: 'display: flex; gap: 4px; flex-wrap: wrap;' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => emit('edit', row)
        }, {
          icon: () => h(NIcon, { component: EditOutlined }),
          default: () => '编辑'
        }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          ghost: true,
          onClick: () => emit('follow', row)
        }, {
          icon: () => h(NIcon, { component: EyeOutlined }),
          default: () => '跟进'
        }),
        h(NButton, {
          size: 'small',
          type: 'info',
          ghost: true,
          onClick: () => emit('moveToTop', row)
        }, {
          icon: () => h(NIcon, { component: ArrowUpOutlined }),
          default: () => '置顶'
        })
      ])
    }
  }
]

// 方法
const handleCheck = (rowKeys: DataTableRowKey[]) => {
  selectedRowKeys.value = rowKeys
}

const handleAdd = () => {
  emit('add')
}

const handleBatchAssign = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要分配的客户')
    return
  }
  emit('batchAssign', selectedRowKeys.value as string[])
}

const handleMoveToPool = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要移入公海的客户')
    return
  }
  emit('moveToPool', selectedRowKeys.value as string[])
}

const handleImport = () => {
  emit('import')
}

const handleExport = () => {
  emit('export')
}
</script>

<style scoped>
.customer-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.table-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 表格现代化样式优化 */
.customer-data-table :deep(.n-data-table) {
  --n-border-color: #f0f2f5;
  --n-th-color: #fafbfc;
  --n-td-color: #ffffff;
  --n-th-text-color: #262626;
  --n-td-text-color: #595959;
  --n-font-size: 14px;
  border-radius: 0;
}

/* 表格头部样式 */
.customer-data-table :deep(.n-data-table-th) {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  font-weight: 600;
  font-size: 13px;
  color: #262626;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 12px;
}

/* 表格行样式 */
.customer-data-table :deep(.n-data-table-td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  font-size: 14px;
  line-height: 1.5;
  background-color: transparent;
}

/* 表格行悬停效果 */
.customer-data-table :deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f0f8ff;
  border-color: #91caff;
}

/* 表格行选中效果 */
.customer-data-table :deep(.n-data-table-tr--checked .n-data-table-td) {
  background-color: #e6f4ff;
  border-color: #91caff;
}

/* 客户姓名列优化 */
:deep(.customer-name-cell) {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

:deep(.customer-avatar-container) {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.9);
  background: #f5f5f5;
  flex-shrink: 0;
  position: relative;
}

:deep(.customer-name-cell .customer-avatar-container .customer-avatar-img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  display: block !important;
}

:deep(.customer-name-cell:hover .customer-avatar-container) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.customer-name-button {
  font-weight: 500;
  font-size: 14px;
  color: #1677ff;
  transition: all 0.2s ease;
  align-self: flex-start;
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.customer-name-button:hover {
  color: #4096ff;
  text-shadow: 0 1px 2px rgba(22, 119, 255, 0.1);
}

.customer-category {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 400;
  line-height: 1.2;
  margin-top: 2px;
}

/* 客户状态进度条样式优化 */
.stage-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  min-width: 120px;
  padding: 4px 0;
}

.stage-cell .n-tag {
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #f0f2f5 0%, #e8eaed 100%);
  border-radius: 3px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.progress-fill {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 3px;
  background: linear-gradient(90deg, var(--fill-color) 0%, var(--fill-color-light) 100%);
  position: relative;
  overflow: hidden;
}

.progress-text {
  font-size: 11px;
  color: #8c8c8c;
  font-weight: 500;
  align-self: flex-end;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 手机号码样式优化 */
.phone-cell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  color: #595959;
  letter-spacing: 0.5px;
}

/* 操作按钮样式优化 */
.customer-data-table :deep(.n-button) {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.customer-data-table :deep(.n-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .table-actions {
    align-self: flex-start;
  }
  
  .customer-data-table :deep(.n-data-table-td) {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .customer-avatar-container {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 768px) {
  .table-header {
    padding: 12px 16px;
  }
  
  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .customer-data-table :deep(.n-data-table-td) {
    padding: 10px 6px;
    font-size: 12px;
  }
  
  .customer-data-table :deep(.n-data-table-th) {
    padding: 12px 6px;
    font-size: 12px;
  }
  
  .customer-avatar-container {
    width: 28px;
    height: 28px;
  }
  
  .customer-name-button {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .table-header {
    padding: 8px 12px;
  }
  
  .table-title {
    font-size: 14px;
  }
  
  .customer-data-table :deep(.n-data-table-td) {
    padding: 8px 4px;
    font-size: 11px;
  }
  
  .customer-data-table :deep(.n-data-table-th) {
    padding: 10px 4px;
    font-size: 11px;
  }
  
  .customer-avatar-container {
    width: 24px;
    height: 24px;
  }
  
  .customer-name-button {
    font-size: 12px;
  }
  
  .customer-data-table :deep(.n-button) {
    font-size: 11px;
    padding: 2px 6px;
  }
}
</style>