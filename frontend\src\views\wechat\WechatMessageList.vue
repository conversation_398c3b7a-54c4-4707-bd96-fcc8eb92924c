<template>
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <n-button type="primary" @click="syncMessages">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          同步消息
        </n-button>
        <n-button @click="exportData">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出数据
        </n-button>
      </div>
      <div class="action-right">
        <n-button type="primary" @click="showMessageAnalysis = true">
          <template #icon>
            <n-icon><BarChartOutline /></n-icon>
          </template>
          消息分析
        </n-button>
        <n-button @click="showKeywordMonitor = true">
          <template #icon>
            <n-icon><EyeOutline /></n-icon>
          </template>
          关键词监控
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ messageStats.totalMessages }}</div>
          <div class="stat-label">总消息数</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#18a058"><ChatbubbleOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ messageStats.todayMessages }}</div>
          <div class="stat-label">今日消息</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#2080f0"><FlashOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ messageStats.activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#f0a020"><PersonOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ messageStats.keywordHits }}</div>
          <div class="stat-label">关键词命中</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#d03050"><AlertCircleOutline /></n-icon>
        </div>
      </n-card>
    </div>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <n-input
            v-model:value="filters.keyword"
            placeholder="搜索消息内容、发送者"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </div>
        <div class="filter-item">
          <n-select
            v-model:value="filters.groupId"
            placeholder="选择群组"
            clearable
            :options="groupOptions"
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-select
            v-model:value="filters.messageType"
            placeholder="消息类型"
            clearable
            :options="messageTypeOptions"
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-date-picker
            v-model:value="filters.sendTime"
            type="datetimerange"
            placeholder="发送时间"
            clearable
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-select
            v-model:value="filters.sentiment"
            placeholder="情感倾向"
            clearable
            :options="sentimentOptions"
            @update:value="handleFilter"
          />
        </div>
      </div>
    </n-card>

    <!-- 消息列表 -->
    <n-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>消息列表 ({{ filteredMessages.length }})</span>
          <div class="table-actions">
            <n-button
              size="small"
              :disabled="selectedMessages.length === 0"
              @click="batchOperation"
            >
              批量操作
            </n-button>
          </div>
        </div>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="paginatedMessages"
        :loading="loading"
        :row-key="(row: any) => row.id"
        v-model:checked-row-keys="selectedMessages"
        :pagination="pagination"
        @update:checked-row-keys="handleSelectionChange"
      />
    </n-card>

    <!-- 消息详情抽屉 -->
    <n-drawer
      v-model:show="showMessageDetail"
      :width="800"
      placement="right"
    >
      <n-drawer-content title="消息详情">
        <MessageDetailPanel
          v-if="selectedMessage"
          :message="selectedMessage"
          @update="handleMessageUpdate"
          @close="showMessageDetail = false"
        />
      </n-drawer-content>
    </n-drawer>

    <!-- 消息分析模态框 -->
    <n-modal
      v-model:show="showMessageAnalysis"
      preset="card"
      title="消息分析"
      style="width: 90%; max-width: 1200px;"
    >
      <MessageAnalysis @close="showMessageAnalysis = false" />
    </n-modal>

    <!-- 关键词监控模态框 -->
    <n-modal
      v-model:show="showKeywordMonitor"
      preset="card"
      title="关键词监控"
      style="width: 80%; max-width: 1000px;"
    >
      <KeywordMonitor @close="showKeywordMonitor = false" />
    </n-modal>

    <!-- 同步消息模态框 -->
    <n-modal
      v-model:show="showSyncModal"
      preset="card"
      title="同步消息"
      style="width: 500px;"
    >
      <div class="sync-content">
        <n-alert type="info" style="margin-bottom: 16px;">
          同步操作将从微信服务器获取最新的群组消息，包括文本、图片、文件等类型。
        </n-alert>
        <div class="sync-options">
          <n-form-item label="同步范围">
            <n-select
              v-model:value="syncOptions.range"
              :options="syncRangeOptions"
            />
          </n-form-item>
          <n-form-item label="消息类型">
            <n-checkbox-group v-model:value="syncOptions.types">
              <n-checkbox value="text">文本消息</n-checkbox>
              <n-checkbox value="image">图片消息</n-checkbox>
              <n-checkbox value="file">文件消息</n-checkbox>
              <n-checkbox value="voice">语音消息</n-checkbox>
            </n-checkbox-group>
          </n-form-item>
          <n-form-item label="时间范围">
            <n-date-picker
              v-model:value="syncOptions.timeRange"
              type="datetimerange"
              placeholder="选择时间范围"
            />
          </n-form-item>
        </div>
      </div>
      <template #footer>
        <div class="modal-footer">
          <n-button @click="showSyncModal = false">取消</n-button>
          <n-button type="primary" @click="confirmSync" :loading="syncing">
            开始同步
          </n-button>
        </div>
      </template>
    </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, markRaw } from 'vue'
import {
  NCard, NButton, NIcon, NInput, NSelect, NDatePicker,
  NDataTable, NDrawer, NDrawerContent, NModal, NAlert,
  NFormItem, NCheckbox, NCheckboxGroup, NTag,
  useMessage, useDialog
} from 'naive-ui'
import {
  RefreshOutline, DownloadOutline, BarChartOutline, EyeOutline,
  ChatbubbleOutline, FlashOutline, PersonOutline, AlertCircleOutline,
  SearchOutline, CreateOutline, TrashOutline, EyeOutline as ViewOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import MessageDetailPanel from './components/MessageDetailPanel.vue'
import MessageAnalysis from './components/MessageAnalysis.vue'
import KeywordMonitor from './components/KeywordMonitor.vue'
import type { WechatMessage } from '@/types'

const message = useMessage()
const dialog = useDialog()
const wechatStore = useWechatStore()

// 响应式数据
const loading = ref(false)
const showMessageDetail = ref(false)
const showMessageAnalysis = ref(false)
const showKeywordMonitor = ref(false)
const showSyncModal = ref(false)
const syncing = ref(false)
const selectedMessage = ref<WechatMessage | null>(null)
const selectedMessages = ref<string[]>([])

// 筛选条件
const filters = ref({
  keyword: '',
  groupId: null,
  messageType: null,
  sendTime: null,
  sentiment: null
})

// 同步选项
const syncOptions = ref({
  range: 'recent',
  types: ['text', 'image'],
  timeRange: null
})

// 选项数据
const groupOptions = ref([
  { label: '产品讨论群', value: 'group1' },
  { label: '技术交流群', value: 'group2' },
  { label: '客户服务群', value: 'group3' }
])

const messageTypeOptions = [
  { label: '文本消息', value: 'text' },
  { label: '图片消息', value: 'image' },
  { label: '文件消息', value: 'file' },
  { label: '语音消息', value: 'voice' },
  { label: '视频消息', value: 'video' },
  { label: '链接消息', value: 'link' }
]

const sentimentOptions = [
  { label: '正面', value: 'positive' },
  { label: '中性', value: 'neutral' },
  { label: '负面', value: 'negative' }
]

const syncRangeOptions = [
  { label: '最近消息', value: 'recent' },
  { label: '全部消息', value: 'all' },
  { label: '指定群组', value: 'group' }
]

// 计算属性
const messageStats = computed(() => ({
  totalMessages: wechatStore.wechatMessages.length,
  todayMessages: wechatStore.wechatMessages.filter((m: WechatMessage) => {
    const today = new Date().toDateString()
    return new Date(m.sendTime || '').toDateString() === today
  }).length,
  activeUsers: new Set(wechatStore.wechatMessages.map((m: WechatMessage) => m.senderId)).size,
  keywordHits: wechatStore.wechatMessages.filter((m: WechatMessage) => m.isKeywordHit).length
}))

const filteredMessages = computed(() => {
  let result = wechatStore.wechatMessages
  
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    result = result.filter((msg: WechatMessage) => 
      msg.content.toLowerCase().includes(keyword) ||
      msg.senderName?.toLowerCase().includes(keyword)
    )
  }
  
  if (filters.value.groupId) {
    result = result.filter((msg: WechatMessage) => msg.group_id === filters.value.groupId)
  }
  
  if (filters.value.messageType) {
    result = result.filter((msg: WechatMessage) => msg.type === filters.value.messageType)
  }
  
  if (filters.value.sentiment) {
    result = result.filter((msg: WechatMessage) => msg.sentiment === filters.value.sentiment)
  }
  
  return result
})

const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

const paginatedMessages = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredMessages.value.slice(start, end)
})

// 表格列配置
const columns = [
  {
    type: 'selection' as const
  },
  {
    title: '发送者',
    key: 'sender',
    render: (row: WechatMessage) => h('div', { class: 'sender-info' }, [
      h('div', { class: 'sender-name' }, row.senderName || '未知用户'),
      h('div', { class: 'sender-id' }, `ID: ${row.senderId}`)
    ])
  },
  {
    title: '群组',
    key: 'groupName',
    render: (row: WechatMessage) => row.groupName || '-'
  },
  {
    title: '消息类型',
    key: 'type',
    render: (row: WechatMessage) => {
      const typeMap = {
        text: { text: '文本', type: 'default' as const },
        image: { text: '图片', type: 'info' as const },
        file: { text: '文件', type: 'warning' as const },
        voice: { text: '语音', type: 'success' as const },
        video: { text: '视频', type: 'error' as const },
        link: { text: '链接', type: 'primary' as const }
      }
      const msgType = typeMap[row.type as keyof typeof typeMap]
      return h(NTag, { type: msgType.type }, msgType.text)
    }
  },
  {
    title: '消息内容',
    key: 'content',
    render: (row: WechatMessage) => {
      const content = row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content
      return h('div', { class: 'message-content' }, [
        h('div', { class: 'content-text' }, content),
        row.isKeywordHit && h(NTag, { type: 'error', size: 'small' }, '关键词')
      ])
    }
  },
  {
    title: '情感倾向',
    key: 'sentiment',
    render: (row: WechatMessage) => {
      if (!row.sentiment) return '-'
      const sentimentMap = {
        positive: { text: '正面', type: 'success' as const },
        negative: { text: '负面', type: 'error' as const },
        neutral: { text: '中性', type: 'default' as const }
      }
      const sentiment = sentimentMap[row.sentiment as keyof typeof sentimentMap]
      return h(NTag, { type: sentiment.type, size: 'small' }, sentiment.text)
    }
  },
  {
    title: '发送时间',
    key: 'sendTime',
    render: (row: WechatMessage) => new Date(row.sendTime || '').toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    render: (row: WechatMessage) => h('div', { class: 'action-buttons' }, [
      h(NButton, {
        size: 'small',
        type: 'primary',
        ghost: true,
        onClick: () => viewMessageDetail(row)
      }, { default: () => '查看', icon: () => h(NIcon, null, { default: () => h(markRaw(ViewOutline)) }) }),
      h(NButton, {
        size: 'small',
        type: 'error',
        ghost: true,
        onClick: () => deleteMessage(row)
      }, { default: () => '删除', icon: () => h(NIcon, null, { default: () => h(markRaw(TrashOutline)) }) })
    ])
  }
]

// 方法
const loadMessages = async () => {
  loading.value = true
  try {
    await wechatStore.fetchWechatMessages()
  } catch (error) {
    message.error('加载消息列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.page = 1
}

const handleFilter = () => {
  pagination.value.page = 1
}

const handleSelectionChange = (keys: (string | number)[]) => {
  selectedMessages.value = keys as string[]
}

const viewMessageDetail = (msg: WechatMessage) => {
  selectedMessage.value = msg
  showMessageDetail.value = true
}

const deleteMessage = (msg: WechatMessage) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这条消息吗？此操作不可恢复。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 删除消息逻辑
        message.success('消息已删除')
        loadMessages()
      } catch (error) {
        message.error('删除消息失败')
      }
    }
  })
}

const handleMessageUpdate = () => {
  loadMessages()
}

const syncMessages = () => {
  showSyncModal.value = true
}

const confirmSync = async () => {
  syncing.value = true
  try {
    // 模拟同步操作
    await new Promise(resolve => setTimeout(resolve, 3000))
    await loadMessages()
    message.success('消息同步完成')
    showSyncModal.value = false
  } catch (error) {
    message.error('消息同步失败')
  } finally {
    syncing.value = false
  }
}

const exportData = () => {
  // 导出数据逻辑
  message.info('导出功能开发中')
}

const batchOperation = () => {
  // 批量操作逻辑
  message.info('批量操作功能开发中')
}

// 生命周期
onMounted(() => {
  loadMessages()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a1a1a;
}

.page-description {
  color: #666;
  margin: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.action-left,
.action-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 0;
}

.stat-card :deep(.n-card__content) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-icon {
  opacity: 0.8;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  align-items: center;
}

.table-card {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.sender-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sender-name {
  font-weight: 500;
  color: #1a1a1a;
}

.sender-id {
  font-size: 12px;
  color: #999;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-text {
  color: #1a1a1a;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.sync-content {
  padding: 16px 0;
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>