import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest } from '@/types'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const loading = ref(false)
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || 'user')
  const permissions = computed(() => user.value?.permissions || [])
  
  // 登录
  const login = async (credentials: LoginRequest) => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      if (response && response.success && response.data) {
        token.value = response.data.access_token
        user.value = response.data.user
        localStorage.setItem('token', response.data.access_token)
        console.log('登录成功')
        return { success: true, data: response.data }
      } else {
        const errorMessage = response?.message || '登录失败'
        console.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      const errorMessage = error?.message || error?.response?.data?.message || '登录失败'
      return { success: false, message: errorMessage }
    } finally {
      loading.value = false
    }
  }
  
  // 使用token登录（企业微信登录）
  const loginWithToken = async (userToken: string) => {
    try {
      loading.value = true
      token.value = userToken
      localStorage.setItem('token', userToken)
      
      // 获取用户信息
      const success = await fetchUserInfo()
      if (success) {
        console.log('Token登录成功')
        return { success: true }
      } else {
        // 如果获取用户信息失败，清除token
        token.value = null
        localStorage.removeItem('token')
        return { success: false, message: '获取用户信息失败' }
      }
    } catch (error: any) {
      console.error('Token登录失败:', error)
      token.value = null
      localStorage.removeItem('token')
      return { success: false, message: error?.message || 'Token登录失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      if (response.success) {
        user.value = response.data
        return true
      }
      return false
    } catch {
      // 静默处理获取用户信息失败的情况
      console.log('获取用户信息失败，可能token已过期')
      return false
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      console.log('已退出登录')
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string) => {
    // 管理员拥有所有权限
    if (userRole.value === 'admin') return true
    // 检查用户是否有通配符权限
    if (permissions.value.includes('*')) return true
    // 检查具体权限
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string) => {
    return userRole.value === role
  }
  
  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        const success = await fetchUserInfo()
        if (!success) {
          // 如果获取用户信息失败，清除token
          token.value = null
          localStorage.removeItem('token')
        }
      } catch {
        // 静默处理token验证失败，清除无效token
        console.log('Token已过期，已清除')
        token.value = null
        localStorage.removeItem('token')
      }
    }
  }
  
  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    userRole,
    permissions,
    
    // 方法
    login,
    loginWithToken,
    logout,
    fetchUserInfo,
    hasPermission,
    hasRole,
    initAuth
  }
})