-- 为选项数据表设置权限
-- 授予 anon 角色基本读取权限
GRANT SELECT ON option_categories TO anon;
GRANT SELECT ON option_items TO anon;

-- 授予 authenticated 角色完整权限
GRANT ALL PRIVILEGES ON option_categories TO authenticated;
GRANT ALL PRIVILEGES ON option_items TO authenticated;

-- 授予序列权限
GRANT USAGE, SELECT ON SEQUENCE option_categories_id_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE option_items_id_seq TO authenticated;

-- 验证权限设置
SELECT 
    grantee,
    table_name,
    privilege_type
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
    AND grantee IN ('anon', 'authenticated') 
    AND table_name IN ('option_categories', 'option_items')
ORDER BY table_name, grantee, privilege_type;