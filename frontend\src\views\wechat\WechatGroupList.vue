<template>
  <div class="wechat-group-list">

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <n-button type="primary" @click="syncGroups">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          同步群组
        </n-button>
        <n-button @click="exportData">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出数据
        </n-button>
      </div>
      <div class="action-right">
        <n-button type="primary" @click="showGroupAnalysis = true">
          <template #icon>
            <n-icon><BarChartOutline /></n-icon>
          </template>
          群组分析
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ groupStats.totalGroups }}</div>
          <div class="stat-label">总群组数</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#18a058"><PeopleOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ groupStats.activeGroups }}</div>
          <div class="stat-label">活跃群组</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#2080f0"><FlashOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ groupStats.totalMembers }}</div>
          <div class="stat-label">总成员数</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#f0a020"><PersonOutline /></n-icon>
        </div>
      </n-card>
      <n-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ groupStats.todayMessages }}</div>
          <div class="stat-label">今日消息</div>
        </div>
        <div class="stat-icon">
          <n-icon size="24" color="#d03050"><ChatbubbleOutline /></n-icon>
        </div>
      </n-card>
    </div>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <n-input
            v-model:value="filters.keyword"
            placeholder="搜索群组名称、群主"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </div>
        <div class="filter-item">
          <n-select
            v-model:value="filters.status"
            placeholder="群组状态"
            clearable
            :options="statusOptions"
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-select
            v-model:value="filters.type"
            placeholder="群组类型"
            clearable
            :options="typeOptions"
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-date-picker
            v-model:value="filters.createTime"
            type="daterange"
            placeholder="创建时间"
            clearable
            @update:value="handleFilter"
          />
        </div>
        <div class="filter-item">
          <n-input-number
            v-model:value="filters.minMembers"
            placeholder="最少成员数"
            clearable
            :min="0"
            @update:value="handleFilter"
          />
        </div>
      </div>
    </n-card>

    <!-- 群组列表 -->
    <n-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>群组列表 ({{ filteredGroups.length }})</span>
          <div class="table-actions">
            <n-button
              size="small"
              :disabled="selectedGroups.length === 0"
              @click="batchOperation"
            >
              批量操作
            </n-button>
          </div>
        </div>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="paginatedGroups"
        :loading="loading"
        :row-key="(row: any) => row.id"
        v-model:checked-row-keys="selectedGroups"
        :pagination="pagination"
        @update:checked-row-keys="handleSelectionChange"
      />
    </n-card>

    <!-- 群组详情抽屉 -->
    <n-drawer
      v-model:show="showGroupDetail"
      :width="800"
      placement="right"
    >
      <n-drawer-content title="群组详情">
        <GroupDetailPanel
          v-if="selectedGroup"
          :group="selectedGroup"
          @update="handleGroupUpdate"
          @close="showGroupDetail = false"
        />
      </n-drawer-content>
    </n-drawer>

    <!-- 群组分析模态框 -->
    <n-modal
      v-model:show="showGroupAnalysis"
      preset="card"
      title="群组分析"
      style="width: 90%; max-width: 1200px;"
    >
      <GroupAnalysis @close="showGroupAnalysis = false" />
    </n-modal>

    <!-- 同步群组模态框 -->
    <n-modal
      v-model:show="showSyncModal"
      preset="card"
      title="同步群组"
      style="width: 500px;"
    >
      <div class="sync-content">
        <n-alert type="info" style="margin-bottom: 16px;">
          同步操作将从微信服务器获取最新的群组信息，包括群组成员、消息统计等数据。
        </n-alert>
        <div class="sync-options">
          <n-checkbox v-model:checked="syncOptions.members">
            同步群组成员
          </n-checkbox>
          <n-checkbox v-model:checked="syncOptions.messages">
            同步消息统计
          </n-checkbox>
          <n-checkbox v-model:checked="syncOptions.settings">
            同步群组设置
          </n-checkbox>
        </div>
      </div>
      <template #footer>
        <div class="modal-footer">
          <n-button @click="showSyncModal = false">取消</n-button>
          <n-button type="primary" @click="confirmSync" :loading="syncing">
            开始同步
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, markRaw } from 'vue'
import {
  NCard, NButton, NIcon, NInput, NSelect, NDatePicker, NInputNumber,
  NDataTable, NDrawer, NDrawerContent, NModal, NAlert, NCheckbox, NTag,
  useMessage, useDialog
} from 'naive-ui'
import {
  RefreshOutline, DownloadOutline, BarChartOutline, PeopleOutline,
  FlashOutline, PersonOutline, ChatbubbleOutline, SearchOutline,
  CreateOutline, TrashOutline, EyeOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import GroupDetailPanel from './components/GroupDetailPanel.vue'
import GroupAnalysis from './components/GroupAnalysis.vue'
import type { WechatGroup } from '@/types'

const message = useMessage()
const dialog = useDialog()
const wechatStore = useWechatStore()

// 响应式数据
const loading = ref(false)
const showGroupDetail = ref(false)
const showGroupAnalysis = ref(false)
const showSyncModal = ref(false)
const syncing = ref(false)
const selectedGroup = ref<WechatGroup | null>(null)
const selectedGroups = ref<string[]>([])

// 筛选条件
const filters = ref({
  keyword: '',
  status: null,
  type: null,
  createTime: null,
  minMembers: null
})

// 同步选项
const syncOptions = ref({
  members: true,
  messages: true,
  settings: false
})

// 选项数据
const statusOptions = [
  { label: '正常', value: 'normal' },
  { label: '已解散', value: 'dissolved' },
  { label: '已退出', value: 'left' }
]

const typeOptions = [
  { label: '普通群', value: 'normal' },
  { label: '企业群', value: 'enterprise' },
  { label: '客服群', value: 'service' }
]

// 计算属性
const groupStats = computed(() => ({
  totalGroups: wechatStore.wechatGroups.length,
  activeGroups: wechatStore.wechatGroups.filter((g: WechatGroup) => g.status === 'active').length,
  totalMembers: wechatStore.wechatGroups.reduce((sum: number, g: WechatGroup) => sum + g.memberCount, 0),
  todayMessages: wechatStore.wechatGroups.reduce((sum: number, g: WechatGroup) => sum + (g.todayMessageCount || 0), 0)
}))

const filteredGroups = computed(() => {
  let result = wechatStore.wechatGroups
  
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    result = result.filter((group: WechatGroup) => 
      group.group_name.toLowerCase().includes(keyword) ||
      group.ownerName?.toLowerCase().includes(keyword)
    )
  }
  
  if (filters.value.status) {
    result = result.filter((group: WechatGroup) => group.status === filters.value.status)
  }
  
  if (filters.value.type) {
    result = result.filter((group: WechatGroup) => group.type === filters.value.type)
  }
  
  if (filters.value.minMembers) {
    result = result.filter((group: WechatGroup) => group.memberCount >= filters.value.minMembers!)
  }
  
  return result
})

const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

const paginatedGroups = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredGroups.value.slice(start, end)
})

// 表格列配置
const columns = [
  {
    type: 'selection' as const
  },
  {
    title: '群组信息',
    key: 'info',
    render: (row: WechatGroup) => h('div', { class: 'group-info' }, [
      h('div', { class: 'group-name' }, row.name),
      h('div', { class: 'group-id' }, `ID: ${row.groupId}`)
    ])
  },
  {
    title: '群主',
    key: 'ownerName',
    render: (row: WechatGroup) => row.ownerName || '-'
  },
  {
    title: '成员数',
    key: 'memberCount',
    sorter: (a: WechatGroup, b: WechatGroup) => a.memberCount - b.memberCount
  },
  {
    title: '状态',
    key: 'status',
    render: (row: WechatGroup) => {
      const statusMap = {
        normal: { text: '正常', type: 'success' },
        dissolved: { text: '已解散', type: 'error' },
        left: { text: '已退出', type: 'warning' }
      }
      const status = statusMap[row.status as keyof typeof statusMap]
      return h(NTag, { type: status.type as 'success' | 'error' | 'warning' }, { default: () => status.text })
    }
  },
  {
    title: '类型',
    key: 'type',
    render: (row: WechatGroup) => {
      const typeMap = {
        normal: '普通群',
        enterprise: '企业群',
        service: '客服群'
      }
      return typeMap[row.type as keyof typeof typeMap] || row.type
    }
  },
  {
    title: '今日消息',
    key: 'todayMessageCount',
    sorter: (a: WechatGroup, b: WechatGroup) => (a.todayMessageCount || 0) - (b.todayMessageCount || 0)
  },
  {
    title: '创建时间',
    key: 'createTime',
    render: (row: WechatGroup) => new Date(row.createTime).toLocaleDateString()
  },
  {
    title: '操作',
    key: 'actions',
    render: (row: WechatGroup) => h('div', { class: 'action-buttons' }, [
      h(NButton, {
        size: 'small',
        type: 'primary',
        ghost: true,
        onClick: () => viewGroupDetail(row)
      }, { default: () => '查看', icon: () => h(NIcon, null, { default: () => h(markRaw(EyeOutline)) }) }),
      h(NButton, {
        size: 'small',
        type: 'warning',
        ghost: true,
        onClick: () => editGroup(row)
      }, { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(markRaw(CreateOutline)) }) })
    ])
  }
]

// 方法
const loadGroups = async () => {
  loading.value = true
  try {
    await wechatStore.fetchWechatGroups()
  } catch (error) {
    message.error('加载群组列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.page = 1
}

const handleFilter = () => {
  pagination.value.page = 1
}

const handleSelectionChange = (keys: (string | number)[]) => {
  selectedGroups.value = keys.map(key => String(key))
}

const viewGroupDetail = (group: WechatGroup) => {
  selectedGroup.value = group
  showGroupDetail.value = true
}

const editGroup = (group: WechatGroup) => {
  // 编辑群组逻辑
  message.info('编辑群组功能开发中')
}

const handleGroupUpdate = () => {
  loadGroups()
}

const syncGroups = () => {
  showSyncModal.value = true
}

const confirmSync = async () => {
  syncing.value = true
  try {
    // 模拟同步操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    await loadGroups()
    message.success('群组同步完成')
    showSyncModal.value = false
  } catch (error) {
    message.error('群组同步失败')
  } finally {
    syncing.value = false
  }
}

const exportData = () => {
  // 导出数据逻辑
  message.info('导出功能开发中')
}

const batchOperation = () => {
  // 批量操作逻辑
  message.info('批量操作功能开发中')
}

// 生命周期
onMounted(() => {
  loadGroups()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
}

.page-description {
  color: #666;
  margin: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.action-left,
.action-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 0;
}

.stat-card :deep(.n-card__content) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-icon {
  opacity: 0.8;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  align-items: center;
}

.table-card {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.group-name {
  font-weight: 500;
  color: #1a1a1a;
}

.group-id {
  font-size: 12px;
  color: #999;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.sync-content {
  padding: 16px 0;
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>