import { http } from './index'
import type { FollowRecord, PaginationResponse, ApiResponse } from '@/types'

// 创建跟进记录请求类型
export interface CreateFollowRequest {
  customer_id: number
  type: string
  content: string
  follow_time: string
  next_follow_time?: string
  stage: 'follow' | 'visit' | 'deal'
  subStage?: string
  designer?: string
  designerId?: number
  amount?: number
  contractNo?: string
  paymentStatus?: string
  visitDate?: string
  measureDate?: string
  dealDate?: string
  remark?: string
}

export interface FollowListParams {
  page?: number
  limit?: number
  customer_id?: number
  user_id?: number
  type?: string
  start_date?: string
  end_date?: string
}

export const followApi = {
  // 获取跟进记录列表
  getFollowList: (params: FollowListParams): Promise<PaginationResponse<FollowRecord>> => {
    return http.get('/follow', { params })
  },

  // 创建跟进记录
  createFollow: (data: CreateFollowRequest): Promise<ApiResponse<FollowRecord>> => {
    return http.post('/follow', data)
  },

  // 更新跟进记录
  updateFollow: (id: string, data: Partial<CreateFollowRequest>): Promise<ApiResponse<FollowRecord>> => {
    return http.put(`/follow/${id}`, data)
  },

  // 删除跟进记录
  deleteFollow: (id: string): Promise<ApiResponse<void>> => {
    return http.delete(`/follow/${id}`)
  },

  // 获取跟进类型
  getFollowTypes: (): Promise<ApiResponse<string[]>> => {
    return http.get('/follow/types')
  },

  // 获取客户跟进记录
  getCustomerFollows: (customerId: string): Promise<ApiResponse<FollowRecord[]>> => {
    return http.get(`/follow/customer/${customerId}`)
  }
}