# 数据迁移指南

## 概述

本指南将帮助您将项目中的所有测试数据迁移到 Supabase 数据库中，包括：
- 前端 Mock 数据（客户、营销活动等）
- SQL 示例数据
- 确保数据格式与 Supabase 表结构匹配

## 前置条件

### 1. Supabase 项目配置
确保您已经：
- ✅ 创建了 Supabase 项目
- ✅ 获取了项目的 URL 和 API 密钥
- ✅ 数据库表结构已创建完成

### 2. 环境依赖
- Node.js >= 18.0.0
- npm 或 pnpm
- TypeScript 支持

## 快速开始

### 步骤 1: 配置环境变量

1. 复制环境变量模板：
```bash
cp scripts/.env.example scripts/.env
```

2. 编辑 `scripts/.env` 文件，填入您的 Supabase 配置：
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### 步骤 2: 安装依赖

进入 scripts 目录并安装依赖：
```bash
cd scripts
npm install
```

### 步骤 3: 执行数据迁移

运行迁移脚本：
```bash
npm run migrate
```

或者使用监听模式（开发时使用）：
```bash
npm run migrate:dev
```

## 迁移内容详情

### 1. 客户数据迁移
- **源数据**: `src/mock/customerData.ts`
- **目标表**: `customers`
- **数据量**: 约 13 条记录（8条普通客户 + 5条公海客户）
- **包含字段**: 姓名、电话、公司、职位、来源、等级、状态等

### 2. 营销活动数据迁移
- **源数据**: `src/mock/marketingData.ts`
- **目标表**: `marketing_campaigns`
- **数据量**: 7 条营销活动记录
- **包含字段**: 活动名称、类型、状态、时间、预算、转化率等

### 3. 活动参与者数据迁移
- **源数据**: `src/mock/marketingData.ts`
- **目标表**: `campaign_participants`
- **数据量**: 3 条参与记录
- **包含字段**: 客户信息、参与时间、状态、奖励等

### 4. 活动分享数据迁移
- **源数据**: `src/mock/marketingData.ts`
- **目标表**: `campaign_shares`
- **数据量**: 2 条分享记录
- **包含字段**: 分享渠道、时间、效果等

### 5. SQL 示例数据
- **源数据**: `docs/sample_data.sql`
- **处理方式**: 脚本会检测文件存在性，建议手动导入或使用数据库工具

## 数据转换规则

### 字段映射
详细的字段映射规则请参考 [DATA_MAPPING.md](./DATA_MAPPING.md)

### 数据验证
迁移过程中会进行以下验证：
- ✅ 必填字段检查（姓名、电话等）
- ✅ 数据格式验证（手机号、邮箱等）
- ✅ 外键关系验证
- ✅ 数据类型转换

### 错误处理
- 🔄 自动重试机制
- 📝 详细错误日志
- ⚠️ 数据验证失败时跳过记录
- 📊 完整的迁移报告

## 迁移结果验证

### 1. 控制台输出
迁移完成后，您将看到类似以下的输出：
```
📊 数据迁移报告
==================================================
✅ 成功迁移: 25 条记录
❌ 失败记录: 0 条记录

🎉 数据迁移完成！
```

### 2. 数据库验证
您可以在 Supabase 控制台中验证数据：
1. 登录 [Supabase Dashboard](https://app.supabase.com)
2. 选择您的项目
3. 进入 "Table Editor"
4. 检查各表的数据是否正确导入

### 3. 前端验证
启动前端应用，检查数据是否能正常显示：
```bash
npm run dev
```

## 常见问题

### Q1: 迁移失败，提示缺少环境变量
**A**: 请确保 `scripts/.env` 文件存在且包含正确的 Supabase 配置信息。

### Q2: 数据验证失败
**A**: 检查源数据格式，特别是手机号格式（必须为11位数字）和必填字段。

### Q3: 外键约束错误
**A**: 确保 Supabase 数据库表结构正确，且外键关系已正确设置。

### Q4: 权限错误
**A**: 确保使用的是 Service Role Key，而不是 Anon Key。

### Q5: 重复数据问题
**A**: 脚本使用 `upsert` 操作，会自动处理重复数据。如果需要完全重新导入，请先清空相关表。

## 高级配置

### 自定义迁移
如果需要自定义迁移逻辑，可以修改 `scripts/migrate-data.ts` 文件：

```typescript
// 自定义数据转换
static transformCustomer(customer: any, source: 'sql' | 'mock') {
  // 添加您的自定义逻辑
  return transformedCustomer
}
```

### 分批迁移
对于大量数据，可以修改脚本实现分批处理：

```typescript
// 分批处理示例
const batchSize = 100
for (let i = 0; i < data.length; i += batchSize) {
  const batch = data.slice(i, i + batchSize)
  await processBatch(batch)
}
```

## 回滚策略

### 数据备份
迁移前建议备份现有数据：
```sql
-- 在 Supabase SQL Editor 中执行
CREATE TABLE customers_backup AS SELECT * FROM customers;
CREATE TABLE marketing_campaigns_backup AS SELECT * FROM marketing_campaigns;
```

### 数据清理
如需重新迁移，可以清空相关表：
```sql
-- 注意：这将删除所有数据，请谨慎操作
TRUNCATE TABLE campaign_shares CASCADE;
TRUNCATE TABLE campaign_participants CASCADE;
TRUNCATE TABLE marketing_campaigns CASCADE;
TRUNCATE TABLE customer_follow_records CASCADE;
TRUNCATE TABLE customers CASCADE;
```

## 性能优化

### 1. 批量操作
脚本已优化为使用 Supabase 的批量操作 API，提高迁移效率。

### 2. 并发控制
避免同时运行多个迁移脚本，可能导致数据冲突。

### 3. 网络优化
确保网络连接稳定，避免在网络不稳定时执行大量数据迁移。

## 监控和日志

### 迁移日志
所有迁移操作都会输出详细日志，包括：
- ✅ 成功记录
- ❌ 失败记录
- ⚠️ 警告信息
- 📊 统计报告

### 错误追踪
如遇到问题，请查看控制台输出的错误信息，并参考本文档的常见问题部分。

## 技术支持

如果您在迁移过程中遇到问题，请：
1. 检查本文档的常见问题部分
2. 查看控制台错误日志
3. 验证 Supabase 配置和网络连接
4. 联系技术支持团队

---

**注意**: 数据迁移是一个重要操作，建议在生产环境执行前先在测试环境中验证。