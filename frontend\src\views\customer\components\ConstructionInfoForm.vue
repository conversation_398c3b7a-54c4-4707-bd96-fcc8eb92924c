<template>
  <div class="construction-info-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <div class="header-info">
        <n-icon size="18" color="#1677ff">
          <construct-outline />
        </n-icon>
        <span class="header-title">{{ isEdit ? '编辑施工信息' : '新增施工信息' }}</span>
      </div>
      <div class="header-tips">
        <n-alert type="info" :show-icon="false" size="small">
          记录施工项目的详细信息，包括时间安排、进度跟踪和质量管理
        </n-alert>
      </div>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
    >
      <!-- 基础信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#1677ff">
            <home-outline />
          </n-icon>
          <span class="section-title">基础信息</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="门牌号" path="houseNumber">
            <n-input
              v-model:value="formData.houseNumber"
              placeholder="请输入门牌号"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#1677ff">
                  <location-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="项目监理" path="projectSupervisor">
            <n-input
              v-model:value="formData.projectSupervisor"
              placeholder="请输入项目监理姓名"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#52c41a">
                  <person-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="设备号" path="equipmentNumber">
            <n-input
              v-model:value="formData.equipmentNumber"
              placeholder="请输入设备号"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#faad14">
                  <hardware-chip-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="计划周期" path="plannedDuration">
            <n-input-number
              v-model:value="formData.plannedDuration"
              placeholder="请输入计划周期"
              :min="1"
              :max="365"
              :step="1"
              style="width: 100%"
              :show-button="false"
            >
              <template #prefix>
                <n-icon size="16" color="#722ed1">
                  <time-outline />
                </n-icon>
              </template>
              <template #suffix>天</template>
            </n-input-number>
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 时间安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#52c41a">
            <calendar-outline />
          </n-icon>
          <span class="section-title">时间安排</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="开工时间" path="startTime">
            <n-date-picker
              v-model:value="formData.startTime"
              type="date"
              placeholder="请选择开工时间"
              style="width: 100%"
              clearable
              :input-readonly="false"
            >
              <template #date-icon>
                <n-icon size="16" color="#52c41a">
                  <play-outline />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item-gi>
          
          <n-form-item-gi label="完工时间" path="endTime">
            <n-date-picker
              v-model:value="formData.endTime"
              type="date"
              placeholder="请选择完工时间"
              style="width: 100%"
              :disabled="!formData.startTime"
              :is-date-disabled="(date: number) => formData.startTime ? date < formData.startTime : false"
              clearable
              :input-readonly="false"
            >
              <template #date-icon>
                <n-icon size="16" color="#fa541c">
                  <stop-outline />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item-gi>
        </n-grid>
        
        <!-- 时间计算提示 -->
        <div class="time-calculation" v-if="formData.startTime && formData.endTime">
          <n-alert type="success" :show-icon="false" size="small">
            <template #header>
              <div class="calculation-header">
                <n-icon size="16" color="#52c41a">
                  <calculator-outline />
                </n-icon>
                <span>时间计算</span>
              </div>
            </template>
            实际工期：{{ calculateDuration() }}天
            {{ formData.plannedDuration && calculateDuration() > formData.plannedDuration ? '（超出计划）' : '（符合计划）' }}
          </n-alert>
        </div>
      </div>

      <!-- 施工进度区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#722ed1">
            <trending-up-outline />
          </n-icon>
          <span class="section-title">施工进度</span>
        </div>
        
        <n-form-item label="当前进度" path="constructionProgress">
          <div class="progress-container">
            <div class="progress-slider">
              <n-slider
                v-model:value="formData.constructionProgress"
                :step="5"
                :min="0"
                :max="100"
                :marks="progressMarks"
                :tooltip="false"
              />
            </div>
            <div class="progress-info">
              <div class="progress-display">
                <n-icon size="18" :color="getProgressColor(formData.constructionProgress)">
                  <component :is="getProgressIcon(formData.constructionProgress)" />
                </n-icon>
                <span class="progress-text">{{ formData.constructionProgress }}%</span>
                <n-tag :type="getProgressType(formData.constructionProgress)" size="small">
                  {{ getProgressStatus(formData.constructionProgress) }}
                </n-tag>
              </div>
              <div class="progress-description">
                {{ getProgressDescription(formData.constructionProgress) }}
              </div>
            </div>
          </div>
        </n-form-item>
      </div>

      <!-- 备注信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#13c2c2">
            <document-text-outline />
          </n-icon>
          <span class="section-title">备注信息</span>
        </div>
        
        <n-form-item label="施工备注" path="constructionNotes">
          <n-input
            v-model:value="formData.constructionNotes"
            type="textarea"
            placeholder="请输入施工相关备注信息，如特殊要求、注意事项、质量标准等..."
            :rows="4"
            clearable
            :maxlength="500"
            show-count
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 快速操作区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#fa8c16">
            <flash-outline />
          </n-icon>
          <span class="section-title">快速操作</span>
        </div>
        
        <div class="quick-actions">
          <n-space>
            <n-button size="small" @click="fillQuickTemplate('start')">
              <template #icon>
                <n-icon><play-outline /></n-icon>
              </template>
              开工模板
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('progress')">
              <template #icon>
                <n-icon><trending-up-outline /></n-icon>
              </template>
              进度更新
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('complete')">
              <template #icon>
                <n-icon><checkmark-circle-outline /></n-icon>
              </template>
              完工模板
            </n-button>
            <n-button size="small" @click="calculateOptimalDuration">
              <template #icon>
                <n-icon><calculator-outline /></n-icon>
              </template>
              智能计算
            </n-button>
          </n-space>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <n-space>
          <n-button @click="handleReset" size="medium">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="loading" size="medium">
            <template #icon>
              <n-icon><save-outline /></n-icon>
            </template>
            {{ isEdit ? '更新' : '保存' }}
          </n-button>
        </n-space>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import {
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NDatePicker,
  NSlider,
  NTag,
  NButton,
  NSpace,
  NIcon,
  NAlert,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  ConstructOutline,
  HomeOutline,
  LocationOutline,
  PersonOutline,
  HardwareChipOutline,
  TimeOutline,
  CalendarOutline,
  PlayOutline,
  StopOutline,
  CalculatorOutline,
  TrendingUpOutline,
  DocumentTextOutline,
  FlashOutline,
  RefreshOutline,
  SaveOutline,
  CheckmarkCircleOutline,
  HourglassOutline,
  BuildOutline,
  WarningOutline,
  CheckmarkOutline
} from '@vicons/ionicons5'

// 定义接口
interface CustomerConstructionInfo {
  id?: string
  customerId: string
  houseNumber: string
  projectSupervisor: string
  startTime: number | null
  endTime: number | null
  plannedDuration: number
  equipmentNumber: string
  constructionProgress: number
  constructionNotes: string
  createdAt: string
}

// Props定义
interface Props {
  modelValue?: CustomerConstructionInfo
  isEdit?: boolean
  loading?: boolean
}

// Emits定义
interface Emits {
  (e: 'update:modelValue', value: CustomerConstructionInfo): void
  (e: 'submit', value: CustomerConstructionInfo): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const message = useMessage()
const formRef = ref<FormInst>()

// 表单数据
const formData = reactive<CustomerConstructionInfo>({
  customerId: '',
  houseNumber: '',
  projectSupervisor: '',
  startTime: null,
  endTime: null,
  plannedDuration: 30,
  equipmentNumber: '',
  constructionProgress: 0,
  constructionNotes: '',
  createdAt: new Date().toISOString()
})

// 进度标记
const progressMarks = {
  0: '未开始',
  25: '施工中',
  50: '过半',
  75: '接近完工',
  100: '已完工'
}

// 获取进度类型
const getProgressType = (progress: number) => {
  if (progress === 0) return 'default'
  if (progress < 30) return 'warning'
  if (progress < 70) return 'info'
  if (progress < 100) return 'success'
  return 'success'
}

// 获取进度状态文本
const getProgressStatus = (progress: number) => {
  if (progress === 0) return '未开始'
  if (progress < 30) return '刚开始'
  if (progress < 70) return '进行中'
  if (progress < 100) return '接近完工'
  return '已完工'
}

// 获取进度颜色
const getProgressColor = (progress: number) => {
  if (progress === 0) return '#8c8c8c'
  if (progress < 30) return '#faad14'
  if (progress < 70) return '#1677ff'
  if (progress < 100) return '#52c41a'
  return '#52c41a'
}

// 获取进度图标
const getProgressIcon = (progress: number) => {
  if (progress === 0) return HourglassOutline
  if (progress < 30) return PlayOutline
  if (progress < 70) return BuildOutline
  if (progress < 100) return TrendingUpOutline
  return CheckmarkOutline
}

// 获取进度描述
const getProgressDescription = (progress: number) => {
  if (progress === 0) return '项目尚未开始，请安排开工时间'
  if (progress < 30) return '项目刚刚开始，需要密切关注初期进展'
  if (progress < 70) return '项目进展顺利，继续保持当前节奏'
  if (progress < 100) return '项目即将完工，注意质量验收'
  return '项目已完工，可以进行最终验收'
}

// 计算工期
const calculateDuration = () => {
  if (!formData.startTime || !formData.endTime) return 0
  const start = new Date(formData.startTime)
  const end = new Date(formData.endTime)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const now = new Date()
  
  const templates = {
    start: {
      startTime: now.getTime(),
      endTime: new Date(now.getTime() + formData.plannedDuration * 24 * 60 * 60 * 1000).getTime(),
      constructionProgress: 5,
      constructionNotes: '项目正式开工，已完成现场勘查和准备工作。'
    },
    progress: {
      constructionProgress: 50,
      constructionNotes: '项目进展顺利，已完成主体结构施工，正在进行装修阶段。'
    },
    complete: {
      constructionProgress: 100,
      endTime: now.getTime(),
      constructionNotes: '项目已完工，通过质量验收，可以交付使用。'
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
    message.success(`已应用${type === 'start' ? '开工' : type === 'progress' ? '进度' : '完工'}模板`)
  }
}

// 智能计算最优工期
const calculateOptimalDuration = () => {
  // 根据房屋面积和装修类型智能计算工期
  const baseArea = 100 // 基准面积
  const baseDuration = 60 // 基准工期（天）
  
  // 这里可以根据实际业务逻辑调整计算方式
  const calculatedDuration = Math.max(30, Math.min(180, baseDuration))
  
  formData.plannedDuration = calculatedDuration
  
  if (formData.startTime) {
    const endDate = new Date(formData.startTime)
    endDate.setDate(endDate.getDate() + calculatedDuration)
    formData.endTime = endDate.getTime()
  }
  
  message.success(`已智能计算最优工期：${calculatedDuration}天`)
}

// 表单验证规则
const rules: FormRules = {
  houseNumber: {
    required: true,
    message: '请输入门牌号',
    trigger: ['blur', 'input']
  },
  projectSupervisor: {
    required: true,
    message: '请输入项目监理姓名',
    trigger: ['blur', 'input']
  },
  plannedDuration: {
    required: true,
    type: 'number',
    min: 1,
    max: 365,
    message: '请输入正确的计划周期（1-365天）',
    trigger: ['blur', 'change']
  },
  equipmentNumber: {
    required: true,
    message: '请输入设备号',
    trigger: ['blur', 'input']
  },
  endTime: [
    {
      validator: (rule, value) => {
        if (value && formData.startTime && value < formData.startTime) {
          return new Error('完工时间不能早于开工时间')
        }
        return true
      },
      trigger: ['blur', 'change']
    }
  ]
}

// 监听props变化，更新表单数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, newValue)
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，向上传递
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 监听开工时间变化，自动计算完工时间
watch(
  () => formData.startTime,
  (newStartTime) => {
    if (newStartTime && formData.plannedDuration) {
      const endDate = new Date(newStartTime)
      endDate.setDate(endDate.getDate() + formData.plannedDuration)
      formData.endTime = endDate.getTime()
    }
  }
)

// 监听计划周期变化，自动计算完工时间
watch(
  () => formData.plannedDuration,
  (newDuration) => {
    if (formData.startTime && newDuration) {
      const endDate = new Date(formData.startTime)
      endDate.setDate(endDate.getDate() + newDuration)
      formData.endTime = endDate.getTime()
    }
  }
)

// 事件处理函数
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    message.error('请检查表单填写是否正确')
  }
}

const handleReset = () => {
  formRef.value?.restoreValidation()
  
  // 重置为初始值
  Object.assign(formData, {
    customerId: '',
    houseNumber: '',
    projectSupervisor: '',
    startTime: null,
    endTime: null,
    plannedDuration: 30,
    equipmentNumber: '',
    constructionProgress: 0,
    constructionNotes: '',
    createdAt: new Date().toISOString()
  })
  
  emit('reset')
}

// 暴露验证方法
const validate = async () => {
  return await formRef.value?.validate()
}

// 暴露重置方法
const resetForm = () => {
  handleReset()
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.construction-info-form {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-tips {
  margin-top: 8px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.time-calculation {
  margin-top: 16px;
}

.calculation-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-container {
  width: 100%;
}

.progress-slider {
  margin: 16px 0 24px 0;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  min-width: 50px;
}

.progress-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

.quick-actions {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

:deep(.n-date-picker) {
  border-radius: 6px;
}

/* 滑块样式 */
:deep(.n-slider) {
  margin: 16px 0;
}

:deep(.n-slider-rail) {
  background-color: #f0f0f0;
  height: 8px;
}

:deep(.n-slider-fill) {
  background: linear-gradient(90deg, #1677ff 0%, #52c41a 100%);
  height: 8px;
}

:deep(.n-slider-handle) {
  border-color: #1677ff;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

:deep(.n-slider-mark) {
  font-size: 12px;
  color: #8c8c8c;
}

/* 文本域样式 */
:deep(.n-input--textarea) {
  border-radius: 6px;
}

:deep(.n-input--textarea .n-input__textarea) {
  line-height: 1.6;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-input-number:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 禁用状态样式 */
:deep(.n-date-picker--disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

:deep(.n-date-picker--disabled .n-input) {
  background-color: #f5f5f5;
}

/* 标签样式 */
:deep(.n-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 输入框前缀图标样式 */
:deep(.n-input .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__suffix) {
  color: #8c8c8c;
  font-weight: 500;
}

/* 日期选择器图标样式 */
:deep(.n-date-picker .n-input__suffix) {
  color: #8c8c8c;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 滚动条样式 */
.construction-info-form::-webkit-scrollbar {
  width: 6px;
}

.construction-info-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.construction-info-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.construction-info-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .construction-info-form {
    padding: 0;
    max-height: 60vh;
  }
  
  .form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  .progress-display {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .progress-text {
    font-size: 14px;
    min-width: auto;
  }
  
  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .quick-actions {
    padding: 8px;
  }
  
  :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .progress-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .calculation-header {
    gap: 4px;
  }
}
</style>