#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import pymysql

try:
    # 连接数据库
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='root',
        database='workchat_admin',
        charset='utf8mb4'
    )
    
    cursor = conn.cursor()
    
    # 检查users表结构
    print("=== Users表结构 ===")
    cursor.execute('DESCRIBE users')
    for row in cursor.fetchall():
        print(f"字段: {row[0]}, 类型: {row[1]}, 空值: {row[2]}, 键: {row[3]}, 默认值: {row[4]}, 额外: {row[5]}")
    
    print("\n=== 检查admin用户 ===")
    cursor.execute('SELECT * FROM users WHERE username = %s', ('admin',))
    admin_user = cursor.fetchone()
    if admin_user:
        print(f"找到admin用户: {admin_user}")
    else:
        print("未找到admin用户")
    
    conn.close()
    print("\n✅ 数据库连接成功")
    
except Exception as e:
    print(f"❌ 错误: {e}")