import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from fastapi import status
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder

from typing import Any, Optional, Generic, TypeVar

# 定义泛型类型变量
T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """统一API响应格式"""
    
    code: int
    message: str
    data: Optional[T] = Field(default=None, description="响应数据")
    timestamp: datetime
    success: bool
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PaginationInfo(BaseModel):
    """分页信息"""
    
    page: int
    page_size: int
    total: int
    total_pages: int
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseModel):
    """分页响应格式"""
    
    items: List[Any]
    pagination: PaginationInfo


class ResponseFormatter:
    """响应格式化工具"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        code: int = status.HTTP_200_OK
    ) -> JSONResponse:
        """成功响应"""
        response_data = ApiResponse(
            code=code,
            message=message,
            data=data,
            timestamp=datetime.now(),
            success=True
        )
        return JSONResponse(
            status_code=code,
            content=jsonable_encoder(response_data.model_dump())
        )
    
    @staticmethod
    def error(
        message: str = "操作失败",
        code: int = status.HTTP_400_BAD_REQUEST,
        data: Any = None
    ) -> JSONResponse:
        """错误响应"""
        response_data = ApiResponse(
            code=code,
            message=message,
            data=data,
            timestamp=datetime.now(),
            success=False
        )
        return JSONResponse(
            status_code=code,
            content=jsonable_encoder(response_data.model_dump())
        )
    
    @staticmethod
    def paginated(
        items: List[Any],
        page: int,
        page_size: int,
        total: int,
        message: str = "查询成功"
    ) -> JSONResponse:
        """分页响应"""
        total_pages = (total + page_size - 1) // page_size
        
        pagination_info = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        paginated_data = PaginatedResponse(
            items=items,
            pagination=pagination_info
        )
        
        return ResponseFormatter.success(
            data=paginated_data.dict(),
            message=message
        )
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "创建成功"
    ) -> JSONResponse:
        """创建成功响应"""
        return ResponseFormatter.success(
            data=data,
            message=message,
            code=status.HTTP_201_CREATED
        )
    
    @staticmethod
    def updated(
        data: Any = None,
        message: str = "更新成功"
    ) -> JSONResponse:
        """更新成功响应"""
        return ResponseFormatter.success(
            data=data,
            message=message
        )
    
    @staticmethod
    def deleted(
        message: str = "删除成功"
    ) -> JSONResponse:
        """删除成功响应"""
        return ResponseFormatter.success(
            message=message
        )
    
    @staticmethod
    def not_found(
        message: str = "资源不存在"
    ) -> JSONResponse:
        """资源不存在响应"""
        return ResponseFormatter.error(
            message=message,
            code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def forbidden(
        message: str = "权限不足"
    ) -> JSONResponse:
        """权限不足响应"""
        return ResponseFormatter.error(
            message=message,
            code=status.HTTP_403_FORBIDDEN
        )
    
    @staticmethod
    def unauthorized(
        message: str = "未授权访问"
    ) -> JSONResponse:
        """未授权响应"""
        return ResponseFormatter.error(
            message=message,
            code=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def validation_error(
        errors: Union[str, Dict, List],
        message: str = "参数验证失败"
    ) -> JSONResponse:
        """参数验证错误响应"""
        return ResponseFormatter.error(
            message=message,
            code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            data=errors
        )
    
    @staticmethod
    def server_error(
        message: str = "服务器内部错误"
    ) -> JSONResponse:
        """服务器错误响应"""
        return ResponseFormatter.error(
            message=message,
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 便捷函数
def success_response(
    data: Any = None,
    message: str = "操作成功"
) -> JSONResponse:
    """成功响应便捷函数"""
    return ResponseFormatter.success(data=data, message=message)


def error_response(
    message: str = "操作失败",
    code: int = status.HTTP_400_BAD_REQUEST
) -> JSONResponse:
    """错误响应便捷函数"""
    return ResponseFormatter.error(message=message, code=code)


def paginated_response(
    items: List[Any],
    page: int,
    page_size: int,
    total: int
) -> JSONResponse:
    """分页响应便捷函数"""
    return ResponseFormatter.paginated(
        items=items,
        page=page,
        page_size=page_size,
        total=total
    )