<template>
  <n-modal v-model:show="showModal" preset="dialog" style="width: 600px">
    <template #header>
      <span>{{ isEdit ? '编辑提醒' : '新建提醒' }}</span>
    </template>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="客户" path="customer_id">
        <n-select
          v-model:value="formData.customer_id"
          placeholder="选择客户"
          filterable
          remote
          :options="customerOptions"
          :loading="customerLoading"
          @search="handleCustomerSearch"
        />
      </n-form-item>

      <n-form-item label="提醒类型" path="type">
        <n-select
          v-model:value="formData.type"
          placeholder="选择提醒类型"
          :options="typeOptions"
          @update:value="handleTypeChange"
        />
      </n-form-item>

      <n-form-item label="提醒标题" path="title">
        <n-input
          v-model:value="formData.title"
          placeholder="请输入提醒标题"
          maxlength="100"
          show-count
        />
      </n-form-item>

      <n-form-item label="提醒内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入提醒内容"
          :rows="4"
          maxlength="500"
          show-count
        />
      </n-form-item>

      <n-form-item label="提醒时间" path="remind_time">
        <n-date-picker
          v-model:value="remindTime"
          type="datetime"
          placeholder="选择提醒时间"
          style="width: 100%"
          :is-date-disabled="isDateDisabled"
        />
      </n-form-item>

      <!-- 快捷时间设置 -->
      <n-form-item label="快捷设置">
        <n-space>
          <n-button size="small" @click="setQuickTime(1, 'hour')">
            1小时后
          </n-button>
          <n-button size="small" @click="setQuickTime(2, 'hour')">
            2小时后
          </n-button>
          <n-button size="small" @click="setQuickTime(1, 'day')">
            明天
          </n-button>
          <n-button size="small" @click="setQuickTime(3, 'day')">
            3天后
          </n-button>
          <n-button size="small" @click="setQuickTime(1, 'week')">
            1周后
          </n-button>
        </n-space>
      </n-form-item>

      <!-- 重复设置 -->
      <n-form-item label="重复提醒">
        <n-checkbox v-model:checked="enableRepeat">
          启用重复提醒
        </n-checkbox>
      </n-form-item>

      <template v-if="enableRepeat">
        <n-form-item label="重复频率">
          <n-select
            v-model:value="repeatFrequency"
            placeholder="选择重复频率"
            :options="repeatOptions"
          />
        </n-form-item>

        <n-form-item label="重复次数">
          <n-input-number
            v-model:value="repeatCount"
            placeholder="重复次数"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </n-form-item>
      </template>

      <!-- 高级设置 -->
      <n-divider>高级设置</n-divider>

      <n-form-item label="提醒方式">
        <n-checkbox-group v-model:value="notificationMethods">
          <n-space>
            <n-checkbox value="system" label="系统通知" />
            <n-checkbox value="email" label="邮件通知" />
            <n-checkbox value="sms" label="短信通知" />
            <n-checkbox value="wechat" label="微信通知" />
          </n-space>
        </n-checkbox-group>
      </n-form-item>

      <n-form-item label="优先级">
        <n-radio-group v-model:value="priority">
          <n-space>
            <n-radio value="low">低</n-radio>
            <n-radio value="normal">普通</n-radio>
            <n-radio value="high">高</n-radio>
            <n-radio value="urgent">紧急</n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import { useTrackingStore } from '@/stores/trackingStore'
import { useCustomerStore } from '@/stores/modules/customer'
import type { AutoReminder } from '@/api/trackingService'
import type { FormInst, FormRules } from 'naive-ui'

interface Props {
  show: boolean
  reminder?: AutoReminder | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const trackingStore = useTrackingStore()
const customerStore = useCustomerStore()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const customerLoading = ref(false)
const customerOptions = ref<Array<{ label: string; value: number }>>([])
const remindTime = ref<number | null>(null)
const enableRepeat = ref(false)
const repeatFrequency = ref('daily')
const repeatCount = ref(1)
const notificationMethods = ref(['system'])
const priority = ref('normal')

// 表单数据
const formData = ref({
  customer_id: null as number | null,
  user_id: 1, // 当前用户ID，实际应该从用户状态获取
  title: '',
  content: '',
  remind_time: '',
  type: 'follow_up' as 'follow_up' | 'birthday' | 'contract_renewal' | 'payment_due' | 'custom',
  status: 'pending' as 'pending' | 'sent' | 'read' | 'dismissed'
})

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.reminder)

// 选项配置
const typeOptions = [
  { label: '跟进提醒', value: 'follow_up' },
  { label: '生日提醒', value: 'birthday' },
  { label: '合同续约', value: 'contract_renewal' },
  { label: '付款到期', value: 'payment_due' },
  { label: '自定义', value: 'custom' }
]

const repeatOptions = [
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '每年', value: 'yearly' }
]

// 表单验证规则
const rules: FormRules = {
  customer_id: {
    required: true,
    type: 'number',
    message: '请选择客户',
    trigger: ['blur', 'change']
  },
  type: {
    required: true,
    message: '请选择提醒类型',
    trigger: ['blur', 'change']
  },
  title: {
    required: true,
    message: '请输入提醒标题',
    trigger: ['blur', 'input']
  },
  content: {
    required: true,
    message: '请输入提醒内容',
    trigger: ['blur', 'input']
  },
  remind_time: {
    required: true,
    message: '请选择提醒时间',
    trigger: ['blur', 'change']
  }
}

// 监听器
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.reminder) {
      loadReminder()
    }
    loadCustomers()
  }
})

watch(remindTime, (newVal) => {
  if (newVal) {
    formData.value.remind_time = new Date(newVal).toISOString()
  } else {
    formData.value.remind_time = ''
  }
})

// 方法
const resetForm = () => {
  formData.value = {
    customer_id: null,
    user_id: 1,
    title: '',
    content: '',
    remind_time: '',
    type: 'follow_up',
    status: 'pending'
  }
  remindTime.value = null
  enableRepeat.value = false
  repeatFrequency.value = 'daily'
  repeatCount.value = 1
  notificationMethods.value = ['system']
  priority.value = 'normal'
  
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

const loadReminder = () => {
  if (!props.reminder) return
  
  const reminder = props.reminder
  formData.value = {
    customer_id: reminder.customer_id,
    user_id: reminder.user_id,
    title: reminder.title,
    content: reminder.content,
    remind_time: reminder.remind_time,
    type: reminder.type,
    status: reminder.status
  }
  
  remindTime.value = new Date(reminder.remind_time).getTime()
  
  // 如果有客户信息，添加到选项中
  if (reminder.customer_name) {
    customerOptions.value = [{
      label: reminder.customer_name,
      value: reminder.customer_id
    }]
  }
}

const loadCustomers = async (keyword = '') => {
  customerLoading.value = true
  try {
    const response = await customerStore.fetchCustomers({
      page: 1,
      page_size: 50,
      search: keyword
    })
    
    const customers = response.data?.data || []
    customerOptions.value = customers.map((customer: any) => ({
      label: `${customer.name} (${customer.phone})`,
      value: customer.id
    }))
  } catch (error) {
    console.error('Failed to load customers:', error)
  } finally {
    customerLoading.value = false
  }
}

const handleCustomerSearch = (query: string) => {
  if (query) {
    loadCustomers(query)
  }
}

const handleTypeChange = (value: string) => {
  // 根据类型自动设置标题
  const titleMap: Record<string, string> = {
    follow_up: '跟进提醒',
    birthday: '生日提醒',
    contract_renewal: '合同续约提醒',
    payment_due: '付款到期提醒',
    custom: '自定义提醒'
  }
  
  if (!formData.value.title || Object.values(titleMap).includes(formData.value.title)) {
    formData.value.title = titleMap[value] || ''
  }
}

const isDateDisabled = (ts: number) => {
  // 不能选择过去的时间
  return ts < Date.now() - 24 * 60 * 60 * 1000
}

const setQuickTime = (amount: number, unit: 'hour' | 'day' | 'week') => {
  const now = new Date()
  let targetTime: Date
  
  switch (unit) {
    case 'hour':
      targetTime = new Date(now.getTime() + amount * 60 * 60 * 1000)
      break
    case 'day':
      targetTime = new Date(now.getTime() + amount * 24 * 60 * 60 * 1000)
      break
    case 'week':
      targetTime = new Date(now.getTime() + amount * 7 * 24 * 60 * 60 * 1000)
      break
    default:
      targetTime = now
  }
  
  remindTime.value = targetTime.getTime()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.value.customer_id === null) {
      message.error('请选择客户')
      return
    }
    
    loading.value = true
    
    const submitData = {
      ...formData.value,
      customer_id: formData.value.customer_id as number
    }
    
    if (isEdit.value && props.reminder) {
      // 更新提醒
      await trackingStore.updateReminder(props.reminder.id, submitData)
      message.success('提醒更新成功')
    } else {
      // 创建提醒
      await trackingStore.createReminder(submitData)
      message.success('提醒创建成功')
      
      // 如果启用了重复提醒，创建重复提醒
      if (enableRepeat.value && repeatCount.value > 1) {
        await createRepeatReminders()
      }
    }
    
    emit('success')
  } catch (error) {
    console.error('Submit error:', error)
    if (error instanceof Error) {
      message.error(error.message)
    } else {
      message.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

const createRepeatReminders = async () => {
  try {
    const baseTime = new Date(formData.value.remind_time)
    
    for (let i = 1; i < repeatCount.value; i++) {
      let nextTime: Date
      
      switch (repeatFrequency.value) {
        case 'daily':
          nextTime = new Date(baseTime.getTime() + i * 24 * 60 * 60 * 1000)
          break
        case 'weekly':
          nextTime = new Date(baseTime.getTime() + i * 7 * 24 * 60 * 60 * 1000)
          break
        case 'monthly':
          nextTime = new Date(baseTime)
          nextTime.setMonth(nextTime.getMonth() + i)
          break
        case 'yearly':
          nextTime = new Date(baseTime)
          nextTime.setFullYear(nextTime.getFullYear() + i)
          break
        default:
          continue
      }
      
      const repeatReminderData = {
        ...formData.value,
        customer_id: formData.value.customer_id as number,
        title: `${formData.value.title} (第${i + 1}次)`,
        remind_time: nextTime.toISOString()
      }
      
      await trackingStore.createReminder(repeatReminderData)
    }
    
    message.success(`成功创建 ${repeatCount.value} 个重复提醒`)
  } catch (error) {
    message.warning('部分重复提醒创建失败')
  }
}

const handleCancel = () => {
  showModal.value = false
}
</script>

<style scoped>
.n-form {
  padding: 20px 0;
}
</style>