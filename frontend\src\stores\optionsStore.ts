import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { optionCategoriesApi, optionItemsApi } from '@/api/options'
import type {
  OptionCategory,
  OptionItem,
  OptionCategoryWithItems
} from '@/types/options'

export const useOptionsStore = defineStore('options', () => {
  // 状态
  const categories = ref<OptionCategory[]>([])
  const items = ref<OptionItem[]>([])
  const categoriesWithItems = ref<OptionCategoryWithItems[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const categoryCount = computed(() => categories.value.length)
  const itemCount = computed(() => items.value.length)
  
  // 根据分类名称获取选项项
  const getItemsByCategory = computed(() => {
    return (categoryName: string) => {
      const category = categories.value.find(cat => cat.name === categoryName)
      if (!category) return []
      return items.value.filter(item => item.category_id === category.id)
    }
  })
  
  // 根据分类名称获取选项项（格式化为前端使用的格式）
  const getFormattedOptions = computed(() => {
    return (categoryName: string) => {
      const categoryItems = getItemsByCategory.value(categoryName)
      return categoryItems.map(item => ({
        value: item.value,
        label: item.label,
        color: item.color,
        icon: item.icon,
        desc: item.description
      }))
    }
  })
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  // 设置加载状态
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }
  
  // 获取所有选项分类
  const fetchCategories = async () => {
    setLoading(true)
    clearError()
    try {
      const response = await optionCategoriesApi.getList()
      if (response.data) {
        categories.value = response.data
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '获取选项分类失败'
      console.error('获取选项分类失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  // 根据分类ID获取选项项
  const fetchItemsByCategory = async (categoryId: number) => {
    try {
      loading.value = true
      const response = await optionItemsApi.getList({ 
        category_code: categoryId.toString(),
        page: 1,
        page_size: 1000
      })
      const categoryItems = response.data || []
      
      // 更新items数组，替换该分类的数据
      items.value = items.value.filter(item => item.category_id !== categoryId.toString()).concat(categoryItems)
      
      return categoryItems
    } catch (error) {
      console.error('获取分类选项项失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 根据分类代码获取选项项
  const fetchItemsByCategoryCode = async (categoryCode: string) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionItemsApi.getByCategoryCode(categoryCode)
      const categoryItems = response
      // 找到对应的分类ID
      const category = categories.value.find(cat => cat.code === categoryCode)
      if (category) {
        // 更新对应分类的选项项
        items.value = items.value.filter(item => item.category_id !== category.id).concat(categoryItems)
      }
      return categoryItems
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '获取选项项失败'
      console.error('获取选项项失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  // 获取所有选项数据（包含分类和选项项）
  const fetchAllOptions = async () => {
    setLoading(true)
    clearError()
    try {
      // 先获取所有分类
      await fetchCategories()
      
      // 然后获取所有选项项
      const itemsResponse = await optionItemsApi.getList()
      if (itemsResponse.data) {
        items.value = itemsResponse.data
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '获取选项数据失败'
      console.error('获取选项数据失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  // 初始化选项数据（获取常用的选项分类）
  const initializeOptions = async () => {
    try {
      // 首先获取所有分类
      await fetchCategories()
      
      // 获取常用的选项分类数据
      const commonCategories = ['customer_source', 'customer_level', 'gender', 'decoration_type', 'house_status', 'follow_status']
      
      const promises = commonCategories.map(async (categoryCode) => {
        try {
          await fetchItemsByCategoryCode(categoryCode)
        } catch (err) {
          console.warn(`获取分类 ${categoryCode} 的选项项失败:`, err)
        }
      })
      
      await Promise.all(promises)
    } catch (err) {
      console.error('初始化选项数据失败:', err)
      throw err
    }
  }
  
  // 刷新选项数据
  const refreshOptions = async () => {
    await fetchAllOptions()
  }
  
  // 重置状态
  const resetState = () => {
    categories.value = []
    items.value = []
    categoriesWithItems.value = []
    loading.value = false
    error.value = null
  }
  
  return {
    // 状态
    categories,
    items,
    categoriesWithItems,
    loading,
    error,
    
    // 计算属性
    categoryCount,
    itemCount,
    getItemsByCategory,
    getFormattedOptions,
    
    // 方法
    clearError,
    setLoading,
    fetchCategories,
    fetchItemsByCategory,
    fetchItemsByCategoryCode,
    fetchAllOptions,
    initializeOptions,
    refreshOptions,
    resetState
  }
})