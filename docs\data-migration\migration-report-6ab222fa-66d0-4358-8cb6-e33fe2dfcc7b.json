{"migrationId": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "timestamp": "2025-08-18T06:54:33.473Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 15, "failedTables": 6, "totalRecords": 165, "migratedRecords": 112, "failedRecords": 53}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T06:53:45.370Z", "errors": [], "endTime": "2025-08-18T06:53:49.390Z", "duration": 4020}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:53:49.394Z", "errors": [], "endTime": "2025-08-18T06:53:51.638Z", "duration": 2244}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T06:53:51.640Z", "errors": [], "endTime": "2025-08-18T06:53:53.249Z", "duration": 1609}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T06:53:53.252Z", "errors": [], "endTime": "2025-08-18T06:53:55.658Z", "duration": 2406}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:53:55.662Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:53:56.181Z", "errors": [], "endTime": "2025-08-18T06:53:58.749Z", "duration": 2568}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:53:58.752Z", "errors": ["Field 'name' doesn't have a default value"], "endTime": "2025-08-18T06:54:01.812Z", "duration": 3060}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T06:54:01.815Z", "errors": [], "endTime": "2025-08-18T06:54:04.520Z", "duration": 2705}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:04.523Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:54:09.284Z", "errors": ["Unknown column 'spent' in 'field list'"], "endTime": "2025-08-18T06:54:12.808Z", "duration": 3524}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:12.810Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:13.403Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:54:16.333Z", "errors": ["Unknown column 'recording_url' in 'field list'"], "endTime": "2025-08-18T06:54:19.337Z", "duration": 3004}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:19.339Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:54:19.617Z", "errors": ["Field 'actions' doesn't have a default value"], "endTime": "2025-08-18T06:54:21.055Z", "duration": 1438}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:21.057Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:21.360Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:22.939Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:54:23.853Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:54:25.268Z", "errors": ["Unknown column 'next_follow_up_at' in 'field list'"], "endTime": "2025-08-18T06:54:28.572Z", "duration": 3304}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:54:28.576Z", "errors": ["Unknown column 'notes' in 'field list'"], "endTime": "2025-08-18T06:54:33.470Z", "duration": 4894}], "logs": [{"id": "5bf2710c-5b73-4542-8df9-eaffaa8ddc31", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T06:53:45.370Z", "end_time": "2025-08-18T06:53:49.390Z", "duration_ms": 4020}, {"id": "834754bd-25fb-466f-9aba-d0f8b4db0be4", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:53:49.394Z", "end_time": "2025-08-18T06:53:51.638Z", "duration_ms": 2244}, {"id": "2967732e-f45e-4463-8adc-057a0c0f9376", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T06:53:51.640Z", "end_time": "2025-08-18T06:53:53.249Z", "duration_ms": 1609}, {"id": "3201bc8e-38b3-44b9-9581-02de679389fc", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T06:53:53.252Z", "end_time": "2025-08-18T06:53:55.658Z", "duration_ms": 2406}, {"id": "df9a67ad-5fbf-4bf5-b11f-ee26b8ce3db4", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:53:55.662Z", "end_time": "2025-08-18T06:53:56.180Z", "duration_ms": 518}, {"id": "336d6eb3-154f-4b94-90c2-8a5cbb203ddd", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:53:56.181Z", "end_time": "2025-08-18T06:53:58.749Z", "duration_ms": 2568}, {"id": "7d75195c-805b-4206-b978-1bca276cd0d0", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:53:58.752Z", "end_time": "2025-08-18T06:54:01.812Z", "duration_ms": 3060}, {"id": "50c22acd-7f1e-4489-9db8-b30529555efc", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T06:54:01.815Z", "end_time": "2025-08-18T06:54:04.520Z", "duration_ms": 2705}, {"id": "a37b0d02-566d-4166-ae7f-c96e97f94876", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:04.523Z", "end_time": "2025-08-18T06:54:09.284Z", "duration_ms": 4761}, {"id": "73ccc792-6da0-4a67-b1be-9d1513837b28", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:09.284Z", "end_time": "2025-08-18T06:54:12.808Z", "duration_ms": 3524}, {"id": "2b48fc54-6fcd-45ab-b2c4-3d4d37b900ca", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:12.810Z", "end_time": "2025-08-18T06:54:13.403Z", "duration_ms": 593}, {"id": "109fc16c-9424-4fbe-ba9d-58b150a97572", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:13.403Z", "end_time": "2025-08-18T06:54:16.333Z", "duration_ms": 2930}, {"id": "813b62fd-2c5c-4f62-aded-6fa87c56de26", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:16.333Z", "end_time": "2025-08-18T06:54:19.337Z", "duration_ms": 3004}, {"id": "bcc99f47-cf97-42e3-bfa7-77affa5dd950", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:19.339Z", "end_time": "2025-08-18T06:54:19.617Z", "duration_ms": 278}, {"id": "ca141f12-cbe9-42dd-aaa0-1b420a1801cc", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:19.617Z", "end_time": "2025-08-18T06:54:21.055Z", "duration_ms": 1438}, {"id": "910533cb-0c6a-4c79-8cc1-fa376fe5adc1", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:21.057Z", "end_time": "2025-08-18T06:54:21.360Z", "duration_ms": 303}, {"id": "a928986f-f7c3-41d7-bd1a-16b222770d81", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:21.360Z", "end_time": "2025-08-18T06:54:22.939Z", "duration_ms": 1579}, {"id": "cf9741bc-2c1e-4a4f-817a-0cef8626098d", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:22.939Z", "end_time": "2025-08-18T06:54:23.853Z", "duration_ms": 914}, {"id": "3ef78a96-1930-44e8-bf4d-99ac3fb8fbfe", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:23.853Z", "end_time": "2025-08-18T06:54:25.268Z", "duration_ms": 1415}, {"id": "34bad722-02e5-46a0-a5e5-68ff8284b9e9", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:25.268Z", "end_time": "2025-08-18T06:54:28.572Z", "duration_ms": 3304}, {"id": "e037897c-8602-4cce-b1ef-0ba1af7dbe8c", "migration_id": "6ab222fa-66d0-4358-8cb6-e33fe2dfcc7b", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:54:28.576Z", "end_time": "2025-08-18T06:54:33.470Z", "duration_ms": 4894}]}