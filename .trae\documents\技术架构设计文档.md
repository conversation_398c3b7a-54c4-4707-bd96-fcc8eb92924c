# YYSH客户管理系统技术架构设计文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[Vue 3 前端应用]
    B --> C[Vite 构建工具]
    B --> D[Vue Router 4]
    B --> E[Pinia 状态管理]
    B --> F[Naive UI 组件库]
    B --> G[Axios HTTP客户端]
    G --> H[Express.js 后端服务]
    H --> I[JWT认证中间件]
    H --> J[业务路由层]
    J --> K[MySQL数据库]
    
    subgraph "前端层"
        B
        C
        D
        E
        F
    end
    
    subgraph "后端层"
        H
        I
        J
    end
    
    subgraph "数据层"
        K
    end
```

## 2. 技术描述

### 前端技术栈

* **Vue 3**: 采用Composition API，提供更好的TypeScript支持和逻辑复用

* **TypeScript**: 提供类型安全，减少运行时错误

* **Naive UI**: 现代化Vue 3 UI组件库，提供丰富的组件和主题定制

* **Pinia**: Vue 3官方推荐的状态管理库

* **Vue Router 4**: Vue 3配套的路由管理

* **Vite**: 现代化构建工具，提供快速的开发体验

* **Axios**: HTTP客户端，用于API请求

### 后端技术栈（保持现有）

* **Node.js + Express.js**: 后端服务框架

* **MySQL**: 关系型数据库

* **JWT**: 用户认证和授权

* **Multer**: 文件上传处理

<br />

**Python（Flask/FastAPI）+ Node.js 分层架构**。

## 3. 路由定义

### 前端路由结构

| 路由                  | 组件                 | 功能描述   | 权限要求             |
| ------------------- | ------------------ | ------ | ---------------- |
| /login              | Login.vue          | 用户登录页面 | 无                |
| /                   | AppLayout.vue      | 应用主布局  | 需要认证             |
| /dashboard          | Dashboard.vue      | 仪表板首页  | dashboard.view   |
| /users              | UserList.vue       | 员工管理列表 | employee.view    |
| /users/create       | UserForm.vue       | 创建员工   | employee.create  |
| /users/:id/edit     | UserForm.vue       | 编辑员工   | employee.edit    |
| /customers          | CustomerList.vue   | 客户管理列表 | customer.view    |
| /customers/create   | CustomerForm.vue   | 创建客户   | customer.create  |
| /customers/:id      | CustomerDetail.vue | 客户详情   | customer.view    |
| /customers/:id/edit | CustomerForm.vue   | 编辑客户   | customer.edit    |
| /follows            | FollowList.vue     | 跟进记录列表 | follow\.view     |
| /follows/create     | FollowForm.vue     | 创建跟进记录 | follow\.create   |
| /meetings           | MeetingList.vue    | 会议记录列表 | meeting.view     |
| /meetings/create    | MeetingForm.vue    | 创建会议记录 | meeting.create   |
| /marketing          | MarketingList.vue  | 营销活动列表 | marketing.view   |
| /marketing/create   | MarketingForm.vue  | 创建营销活动 | marketing.create |
| /analytics          | Analytics.vue      | 数据分析页面 | analytics.view   |
| /settings           | Settings.vue       | 系统设置   | config.view      |

## 4. API定义

### 4.1 核心API接口

#### 用户认证相关

**用户登录**

```
POST /api/auth/login
```

请求参数:

| 参数名      | 参数类型   | 是否必需 | 描述  |
| -------- | ------ | ---- | --- |
| username | string | true | 用户名 |
| password | string | true | 密码  |

响应数据:

| 参数名        | 参数类型    | 描述      |
| ---------- | ------- | ------- |
| success    | boolean | 请求是否成功  |
| message    | string  | 响应消息    |
| data.token | string  | JWT访问令牌 |
| data.user  | object  | 用户信息    |

示例:

```json
{
  "username": "admin",
  "password": "password"
}
```

**获取当前用户信息**

```
GET /api/auth/me
```

请求头:

| 参数名           | 参数类型   | 是否必需 | 描述             |
| ------------- | ------ | ---- | -------------- |
| Authorization | string | true | Bearer {token} |

响应数据:

| 参数名           | 参数类型    | 描述     |
| ------------- | ------- | ------ |
| success       | boolean | 请求是否成功 |
| data.id       | number  | 用户ID   |
| data.username | string  | 用户名    |
| data.name     | string  | 姓名     |
| data.role     | string  | 角色     |

#### 客户管理相关

**获取客户列表**

```
GET /api/customers
```

查询参数:

| 参数名    | 参数类型   | 是否必需  | 描述        |
| ------ | ------ | ----- | --------- |
| page   | number | false | 页码，默认1    |
| limit  | number | false | 每页数量，默认20 |
| search | string | false | 搜索关键词     |
| status | string | false | 客户状态筛选    |

**创建客户**

```
POST /api/customers
```

请求参数:

| 参数名     | 参数类型   | 是否必需  | 描述   |
| ------- | ------ | ----- | ---- |
| name    | string | true  | 客户姓名 |
| mobile  | string | true  | 手机号  |
| company | string | false | 公司名称 |
| source  | string | false | 客户来源 |
| status  | string | false | 客户状态 |

#### 跟进记录相关

**获取跟进记录列表**

```
GET /api/follows
```

**创建跟进记录**

```
POST /api/follows
```

请求参数:

| 参数名                | 参数类型   | 是否必需  | 描述     |
| ------------------ | ------ | ----- | ------ |
| customer\_id       | number | true  | 客户ID   |
| type               | string | true  | 跟进类型   |
| content            | string | true  | 跟进内容   |
| result             | string | false | 跟进结果   |
| next\_follow\_time | string | false | 下次跟进时间 |

### 4.2 TypeScript类型定义

```typescript
// 用户相关类型
interface User {
  id: number
  username: string
  name: string
  role: string
  avatar?: string
  department_id?: number
  position?: string
  mobile?: string
  email?: string
  last_login_time?: string
}

interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  success: boolean
  message: string
  data: {
    token: string
    user: User
  }
}

// 客户相关类型
interface Customer {
  id: number
  name: string
  mobile: string
  company?: string
  position?: string
  source: string
  status: string
  owner_id: number
  owner_name?: string
  tags?: CustomerTag[]
  follow_count: number
  last_follow_time?: string
  created_at: string
  updated_at: string
}

interface CustomerTag {
  id: number
  name: string
  color: string
  category: string
}

interface CreateCustomerRequest {
  name: string
  mobile: string
  company?: string
  position?: string
  source: string
  status?: string
  remark?: string
}

// 跟进记录相关类型
interface FollowRecord {
  id: number
  customer_id: number
  customer_name?: string
  user_id: number
  user_name?: string
  type: string
  content: string
  result?: string
  is_effective: boolean
  next_follow_time?: string
  created_at: string
}

interface CreateFollowRequest {
  customer_id: number
  type: string
  content: string
  result?: string
  is_effective?: boolean
  next_follow_time?: string
}

// API响应类型
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: number
}

interface PaginatedResponse<T> {
  success: boolean
  message: string
  data: {
    items: T[]
    total: number
    page: number
    limit: number
    totalPages: number
  }
}
```

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端请求] --> B[Express.js 服务器]
    B --> C[CORS中间件]
    C --> D[Body Parser中间件]
    D --> E[JWT认证中间件]
    E --> F[路由控制器层]
    F --> G[业务逻辑层]
    G --> H[数据访问层]
    H --> I[MySQL数据库]
    
    subgraph "中间件层"
        C
        D
        E
    end
    
    subgraph "应用层"
        F
        G
    end
    
    subgraph "数据层"
        H
        I
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USERS ||--o{ CUSTOMERS : owns
    USERS ||--o{ FOLLOW_RECORDS : creates
    USERS ||--o{ MEETING_RECORDS : creates
    CUSTOMERS ||--o{ FOLLOW_RECORDS : has
    CUSTOMERS ||--o{ MEETING_RECORDS : has
    CUSTOMERS ||--o{ CUSTOMER_TAG_RELATIONS : has
    CUSTOMER_TAGS ||--o{ CUSTOMER_TAG_RELATIONS : belongs_to
    USERS ||--o{ ANALYTICS_STATS : generates
    
    USERS {
        bigint id PK
        varchar username UK
        varchar name
        varchar password
        varchar role
        varchar mobile
        varchar email
        bigint department_id
        varchar position
        varchar avatar
        datetime last_login_time
        datetime created_at
        datetime updated_at
    }
    
    CUSTOMERS {
        bigint id PK
        varchar name
        varchar mobile
        varchar company
        varchar position
        varchar source
        varchar status
        bigint owner_id FK
        boolean is_in_pool
        int follow_count
        datetime last_follow_time
        decimal deal_amount
        datetime deal_time
        datetime created_at
        datetime updated_at
    }
    
    FOLLOW_RECORDS {
        bigint id PK
        bigint customer_id FK
        bigint user_id FK
        varchar type
        text content
        varchar result
        boolean is_effective
        datetime next_follow_time
        datetime created_at
    }
    
    MEETING_RECORDS {
        bigint id PK
        bigint follow_record_id FK
        bigint customer_id FK
        bigint user_id FK
        enum meeting_type
        datetime meeting_time
        bigint designer_id
        varchar designer_name
        int visit_count
        varchar address
        text notes
        datetime created_at
    }
    
    CUSTOMER_TAGS {
        bigint id PK
        varchar name UK
        varchar color
        varchar category
        int sort_order
        bigint created_by FK
        datetime created_at
        datetime updated_at
    }
    
    CUSTOMER_TAG_RELATIONS {
        bigint id PK
        bigint customer_id FK
        bigint tag_id FK
        datetime created_at
    }
    
    ANALYTICS_STATS {
        bigint id PK
        bigint user_id FK
        date date
        int customer_count
        int new_customer_count
        int follow_count
        int deal_count
        decimal deal_amount
        int pool_claim_count
        datetime created_at
        datetime updated_at
    }
```

### 6.2 数据定义语言

#### 用户表 (users)

```sql
-- 创建用户表
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名/工号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `password` varchar(255) DEFAULT NULL COMMENT '密码哈希',
  `role` varchar(20) NOT NULL DEFAULT 'sales' COMMENT '角色',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `work_wechat_id` varchar(100) DEFAULT NULL COMMENT '企业微信ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_work_wechat_id` (`work_wechat_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建索引
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_department ON users(department_id);

-- 初始化管理员用户
INSERT INTO users (username, name, password, role, status) VALUES 
('admin', '系统管理员', '$2a$10$rQ8QqQqQqQqQqQqQqQqQqO', 'admin', 1);
```

#### 客户表 (customers)

```sql
-- 创建客户表
CREATE TABLE `customers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(50) NOT NULL COMMENT '客户姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `company` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `source` varchar(50) NOT NULL COMMENT '客户来源',
  `status` varchar(20) NOT NULL DEFAULT 'potential' COMMENT '客户状态',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `is_in_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在公海',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `last_follow_time` datetime DEFAULT NULL COMMENT '最后跟进时间',
  `deal_amount` decimal(10,2) DEFAULT 0.00 COMMENT '成交金额',
  `deal_time` datetime DEFAULT NULL COMMENT '成交时间',
  `remark` text COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_source` (`source`),
  KEY `idx_status` (`status`),
  KEY `idx_is_in_pool` (`is_in_pool`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 创建索引
CREATE INDEX idx_customers_owner ON customers(owner_id);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_source ON customers(source);
CREATE INDEX idx_customers_pool ON customers(is_in_pool);
```

#### 跟进记录表 (follow\_records)

```sql
-- 创建跟进记录表
CREATE TABLE `follow_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `user_id` bigint(20) NOT NULL COMMENT '跟进人ID',
  `type` varchar(20) NOT NULL COMMENT '跟进类型',
  `content` text NOT NULL COMMENT '跟进内容',
  `result` varchar(20) DEFAULT NULL COMMENT '跟进结果',
  `is_effective` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效跟进',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `remark` text COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟进记录表';

-- 创建索引
CREATE INDEX idx_follow_customer ON follow_records(customer_id);
CREATE INDEX idx_follow_user ON follow_records(user_id);
CREATE INDEX idx_follow_type ON follow_records(type);
CREATE INDEX idx_follow_time ON follow_records(created_at);
```

#### 客户标签表 (customer\_tags)

```sql
-- 创建客户标签表
CREATE TABLE `customer_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `category` varchar(50) DEFAULT 'custom' COMMENT '标签分类',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签表';

-- 初始化标签数据
INSERT INTO customer_tags (name, color, category, created_by) VALUES
('重点客户', '#f50', 'priority', 1),
('潜在客户', '#2db7f5', 'priority', 1),
('成交客户', '#87d068', 'status', 1),
('流失客户', '#f50', 'status', 1);
```

#### 数据分析统计表 (analytics\_stats)

```sql
-- 创建数据分析统计表
CREATE TABLE `analytics_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '客户总数',
  `new_customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '新增客户数',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '跟进次数',
  `deal_count` int(11) NOT NULL DEFAULT 0 COMMENT '成交客户数',
  `deal_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成交金额',
  `pool_claim_count` int(11) NOT NULL DEFAULT 0 COMMENT '公海领取数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`),
  KEY `idx_date` (`date`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据统计表';
```

## 7. 部署配置

### 7.1 开发环境配置

#### 前端开发环境 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
```

#### TypeScript配置 (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 7.2 生产环境部署

#### Docker配置

```dockerfile
# 前端构建
FROM node:18-alpine as frontend-build
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ ./
RUN npm run build

# 后端运行环境
FROM node:18-alpine as production
WORKDIR /app

# 安装后端依赖
COPY backend/package*.json ./
RUN npm ci --only=production

# 复制后端代码
COPY backend/ ./

# 复制前端构建结果
COPY --from=frontend-build /app/frontend/dist ./public

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["node", "server.js"]
```

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        root /var/www/html;
        index index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传
    location /uploads/ {
        proxy_pass http://localhost:3000;
        client_max_body_size 10M;
    }
}
```

## 8. 性能优化

### 8.1 前端优化

* **代码分割**: 使用Vue Router的懒加载

* **组件缓存**: 使用keep-alive缓存组件

* **图片优化**: 使用WebP格式和懒加载

* **打包优化**: 使用Vite的构建优化

### 8.2 后端优化

* **数据库索引**: 优化查询性能

* **缓存策略**: 使用Redis缓存热点数据

* **连接池**: 优化数据库连接管理

* **压缩**: 启用Gzip压缩

这个技术架构设计为YYSH客户管理系统提供了完整的现代化升级方案，确保系统的可扩展性、可维护性和高性能。
