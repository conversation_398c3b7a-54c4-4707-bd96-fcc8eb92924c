{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/scripts/*": ["./scripts/*"]}, "types": ["node", "jest"]}, "include": ["src/**/*", "scripts/**/*", "api/**/*", "**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "frontend", "**/*.test.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}