<template>
  <div class="ticket-reminders">
    <n-card title="倒计时提醒" :bordered="false">
      <!-- 统计概览 -->
      <div class="stats-overview mb-6">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic label="即将到期" :value="urgentCount" class="stat-urgent">
              <template #suffix>
                <n-icon :component="AlertCircle" class="text-red-500" />
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="今日到期" :value="todayCount" class="stat-today">
              <template #suffix>
                <n-icon :component="Time" class="text-orange-500" />
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="已超期" :value="overdueCount" class="stat-overdue">
              <template #suffix>
                <n-icon :component="Warning" class="text-red-600" />
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="正常处理" :value="normalCount" class="stat-normal">
              <template #suffix>
                <n-icon :component="CheckmarkCircle" class="text-green-500" />
              </template>
            </n-statistic>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 筛选和操作区域 -->
      <div class="filter-section mb-6">
        <n-space>
          <n-select
            v-model:value="reminderFilter"
            placeholder="提醒类型"
            style="width: 150px"
            :options="reminderOptions"
            @update:value="filterReminders"
          />
          <n-select
            v-model:value="priorityFilter"
            placeholder="优先级"
            clearable
            style="width: 120px"
            :options="priorityOptions"
          />
          <n-button type="primary" @click="refreshReminders">
            <template #icon>
              <n-icon :component="Refresh" />
            </template>
            刷新
          </n-button>
          <n-button type="warning" @click="markAllAsRead">
            <template #icon>
              <n-icon :component="CheckmarkDone" />
            </template>
            全部已读
          </n-button>
          <n-button type="info" @click="showSettingsModal = true">
            <template #icon>
              <n-icon :component="Settings" />
            </template>
            提醒设置
          </n-button>
        </n-space>
      </div>

      <!-- 提醒列表 -->
      <div class="reminders-list">
        <n-list>
          <n-list-item
            v-for="reminder in filteredReminders"
            :key="reminder.id"
            class="reminder-item"
            :class="{
              'reminder-urgent': reminder.urgency === 'urgent',
              'reminder-warning': reminder.urgency === 'warning',
              'reminder-overdue': reminder.urgency === 'overdue',
              'reminder-read': reminder.isRead
            }"
          >
            <div class="reminder-content">
              <div class="reminder-header">
                <div class="reminder-title">
                  <n-icon :component="getReminderIcon(reminder.type)" class="mr-2" />
                  <span class="title-text">{{ reminder.title }}</span>
                  <n-tag
                    :type="getUrgencyType(reminder.urgency)"
                    size="small"
                    class="ml-2"
                  >
                    {{ getUrgencyText(reminder.urgency) }}
                  </n-tag>
                  <n-badge
                    v-if="!reminder.isRead"
                    dot
                    type="error"
                    class="ml-2"
                  />
                </div>
                <div class="reminder-time">
                  <countdown-timer
                    :target-time="reminder.targetTime"
                    :warning-hours="24"
                    :urgent-hours="6"
                  />
                </div>
              </div>
              
              <div class="reminder-details">
                <div class="reminder-info">
                  <span class="info-item">
                    <n-icon :component="Person" class="mr-1" />
                    {{ reminder.customerName }}
                  </span>
                  <span class="info-item ml-4">
                    <n-icon :component="Home" class="mr-1" />
                    {{ reminder.propertyInfo }}
                  </span>
                  <span class="info-item ml-4">
                    <n-icon :component="Document" class="mr-1" />
                    {{ reminder.ticketNo }}
                  </span>
                </div>
                <div class="reminder-description">{{ reminder.description }}</div>
              </div>

              <div class="reminder-actions">
                <n-space>
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleReminder(reminder)"
                  >
                    立即处理
                  </n-button>
                  <n-button
                    size="small"
                    @click="postponeReminder(reminder)"
                  >
                    延期
                  </n-button>
                  <n-button
                    size="small"
                    @click="markAsRead(reminder)"
                    v-if="!reminder.isRead"
                  >
                    标记已读
                  </n-button>
                  <n-button
                    size="small"
                    type="error"
                    @click="dismissReminder(reminder)"
                  >
                    忽略
                  </n-button>
                </n-space>
              </div>
            </div>
          </n-list-item>
        </n-list>
      </div>

      <!-- 空状态 -->
      <n-empty v-if="filteredReminders.length === 0" description="暂无提醒事项" />
    </n-card>

    <!-- 提醒设置模态框 -->
    <n-modal v-model:show="showSettingsModal" preset="dialog" title="提醒设置">
      <n-form
        ref="settingsFormRef"
        :model="reminderSettings"
        label-placement="left"
        label-width="120px"
      >
        <n-form-item label="提前提醒时间">
          <n-input-number
            v-model:value="reminderSettings.warningHours"
            :min="1"
            :max="72"
            suffix="小时"
            style="width: 150px"
          />
        </n-form-item>
        <n-form-item label="紧急提醒时间">
          <n-input-number
            v-model:value="reminderSettings.urgentHours"
            :min="1"
            :max="24"
            suffix="小时"
            style="width: 150px"
          />
        </n-form-item>
        <n-form-item label="邮件提醒">
          <n-switch v-model:value="reminderSettings.emailNotification" />
        </n-form-item>
        <n-form-item label="短信提醒">
          <n-switch v-model:value="reminderSettings.smsNotification" />
        </n-form-item>
        <n-form-item label="微信提醒">
          <n-switch v-model:value="reminderSettings.wechatNotification" />
        </n-form-item>
        <n-form-item label="声音提醒">
          <n-switch v-model:value="reminderSettings.soundNotification" />
        </n-form-item>
        <n-form-item label="提醒频率">
          <n-select
            v-model:value="reminderSettings.frequency"
            :options="frequencyOptions"
            style="width: 200px"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showSettingsModal = false">取消</n-button>
          <n-button type="primary" @click="saveSettings">保存设置</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 延期模态框 -->
    <n-modal v-model:show="showPostponeModal" preset="dialog" title="延期处理">
      <n-form
        ref="postponeFormRef"
        :model="postponeForm"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="延期时间" path="postponeTime">
          <n-date-picker
            v-model:value="postponeForm.postponeTime"
            type="datetime"
            placeholder="选择新的截止时间"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="延期原因" path="reason">
          <n-input
            v-model:value="postponeForm.reason"
            type="textarea"
            placeholder="请输入延期原因"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showPostponeModal = false">取消</n-button>
          <n-button type="primary" @click="confirmPostpone">确认延期</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useRouter } from 'vue-router'
import {
  AlertCircle,
  Time,
  Warning,
  CheckmarkCircle,
  Refresh,
  CheckmarkDone,
  Settings,
  Person,
  Home,
  Document,
  Alarm,
  Mail,
  Call
} from '@vicons/ionicons5'
import CountdownTimer from '@/components/CountdownTimer.vue'

interface Reminder {
  id: string
  type: 'deadline' | 'followup' | 'inspection' | 'maintenance'
  title: string
  description: string
  ticketNo: string
  customerId: string
  customerName: string
  propertyInfo: string
  targetTime: string
  urgency: 'normal' | 'warning' | 'urgent' | 'overdue'
  isRead: boolean
  createdAt: string
}

const message = useMessage()
const router = useRouter()
const showSettingsModal = ref(false)
const showPostponeModal = ref(false)
const selectedReminder = ref<Reminder | null>(null)
const settingsFormRef = ref()
const postponeFormRef = ref()

// 筛选条件
const reminderFilter = ref<string | null>(null)
const priorityFilter = ref<string | null>(null)

// 提醒设置
const reminderSettings = reactive({
  warningHours: 24,
  urgentHours: 6,
  emailNotification: true,
  smsNotification: false,
  wechatNotification: true,
  soundNotification: true,
  frequency: 'every_hour'
})

// 延期表单
const postponeForm = reactive({
  postponeTime: null as number | null,
  reason: ''
})

// 模拟数据
const reminders = ref<Reminder[]>([
  {
    id: '1',
    type: 'deadline',
    title: '工单即将到期',
    description: '卫生间水龙头漏水维修工单将在2小时后到期',
    ticketNo: 'T202401001',
    customerId: 'C001',
    customerName: '张三',
    propertyInfo: '阳光花园 A栋 1201室',
    targetTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后
    urgency: 'urgent',
    isRead: false,
    createdAt: '2024-01-15 16:00:00'
  },
  {
    id: '2',
    type: 'followup',
    title: '客户回访提醒',
    description: '空调维修完成后需要进行客户满意度回访',
    ticketNo: 'T202401002',
    customerId: 'C002',
    customerName: '李四',
    propertyInfo: '绿城花园 B栋 2305室',
    targetTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6小时后
    urgency: 'warning',
    isRead: false,
    createdAt: '2024-01-15 12:00:00'
  },
  {
    id: '3',
    type: 'deadline',
    title: '工单已超期',
    description: '电路维修工单已超期1小时，请立即处理',
    ticketNo: 'T202401003',
    customerId: 'C003',
    customerName: '王五',
    propertyInfo: '海景豪庭 C栋 1508室',
    targetTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1小时前
    urgency: 'overdue',
    isRead: false,
    createdAt: '2024-01-15 08:00:00'
  },
  {
    id: '4',
    type: 'inspection',
    title: '定期检查提醒',
    description: '消防设备定期检查时间到了',
    ticketNo: 'T202401004',
    customerId: 'C004',
    customerName: '赵六',
    propertyInfo: '蓝天公寓 D栋 808室',
    targetTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后
    urgency: 'normal',
    isRead: true,
    createdAt: '2024-01-14 10:00:00'
  }
])

// 选项数据
const reminderOptions = [
  { label: '全部', value: null },
  { label: '截止提醒', value: 'deadline' },
  { label: '跟进提醒', value: 'followup' },
  { label: '检查提醒', value: 'inspection' },
  { label: '维护提醒', value: 'maintenance' }
]

const priorityOptions = [
  { label: '正常', value: 'normal' },
  { label: '警告', value: 'warning' },
  { label: '紧急', value: 'urgent' },
  { label: '已超期', value: 'overdue' }
]

const frequencyOptions = [
  { label: '每15分钟', value: 'every_15min' },
  { label: '每30分钟', value: 'every_30min' },
  { label: '每小时', value: 'every_hour' },
  { label: '每2小时', value: 'every_2hours' },
  { label: '每天', value: 'daily' }
]

// 计算属性
const filteredReminders = computed(() => {
  let result = reminders.value
  
  if (reminderFilter.value) {
    result = result.filter(reminder => reminder.type === reminderFilter.value)
  }
  
  if (priorityFilter.value) {
    result = result.filter(reminder => reminder.urgency === priorityFilter.value)
  }
  
  return result.sort((a, b) => {
    // 按紧急程度排序
    const urgencyOrder = { overdue: 0, urgent: 1, warning: 2, normal: 3 }
    return urgencyOrder[a.urgency] - urgencyOrder[b.urgency]
  })
})

const urgentCount = computed(() => 
  reminders.value.filter(r => r.urgency === 'urgent').length
)

const todayCount = computed(() => {
  const today = new Date()
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
  const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)
  
  return reminders.value.filter(r => {
    const targetTime = new Date(r.targetTime)
    return targetTime >= todayStart && targetTime < todayEnd
  }).length
})

const overdueCount = computed(() => 
  reminders.value.filter(r => r.urgency === 'overdue').length
)

const normalCount = computed(() => 
  reminders.value.filter(r => r.urgency === 'normal').length
)

// 工具函数
const getReminderIcon = (type: string) => {
  const icons: Record<string, any> = {
    deadline: Alarm,
    followup: Call,
    inspection: CheckmarkCircle,
    maintenance: Settings
  }
  return icons[type] || Alarm
}

const getUrgencyType = (urgency: string) => {
  const types: Record<string, string> = {
    normal: 'default',
    warning: 'warning',
    urgent: 'error',
    overdue: 'error'
  }
  return types[urgency] || 'default'
}

const getUrgencyText = (urgency: string) => {
  const texts: Record<string, string> = {
    normal: '正常',
    warning: '警告',
    urgent: '紧急',
    overdue: '已超期'
  }
  return texts[urgency] || urgency
}

// 事件处理函数
const filterReminders = () => {
  // 筛选逻辑已在computed中实现
}

const refreshReminders = () => {
  // 更新提醒的紧急程度
  reminders.value.forEach(reminder => {
    const now = Date.now()
    const targetTime = new Date(reminder.targetTime).getTime()
    const timeDiff = targetTime - now
    
    if (timeDiff <= 0) {
      reminder.urgency = 'overdue'
    } else if (timeDiff <= reminderSettings.urgentHours * 60 * 60 * 1000) {
      reminder.urgency = 'urgent'
    } else if (timeDiff <= reminderSettings.warningHours * 60 * 60 * 1000) {
      reminder.urgency = 'warning'
    } else {
      reminder.urgency = 'normal'
    }
  })
  
  message.success('提醒列表已刷新')
}

const markAllAsRead = () => {
  reminders.value.forEach(reminder => {
    reminder.isRead = true
  })
  message.success('所有提醒已标记为已读')
}

const handleReminder = (reminder: Reminder) => {
  // 跳转到工单处理页面
  router.push({
    name: 'TicketProcess',
    params: { id: reminder.ticketNo },
    query: { customerId: reminder.customerId }
  })
}

const postponeReminder = (reminder: Reminder) => {
  selectedReminder.value = reminder
  postponeForm.postponeTime = new Date(reminder.targetTime).getTime()
  showPostponeModal.value = true
}

const markAsRead = (reminder: Reminder) => {
  reminder.isRead = true
  message.success('提醒已标记为已读')
}

const dismissReminder = (reminder: Reminder) => {
  const index = reminders.value.findIndex(r => r.id === reminder.id)
  if (index > -1) {
    reminders.value.splice(index, 1)
    message.success('提醒已忽略')
  }
}

const saveSettings = () => {
  // 保存提醒设置
  localStorage.setItem('reminderSettings', JSON.stringify(reminderSettings))
  showSettingsModal.value = false
  message.success('提醒设置已保存')
  
  // 重新计算提醒紧急程度
  refreshReminders()
}

const confirmPostpone = () => {
  if (!selectedReminder.value || !postponeForm.postponeTime) {
    message.error('请选择延期时间')
    return
  }
  
  selectedReminder.value.targetTime = new Date(postponeForm.postponeTime).toISOString()
  
  // 重新计算紧急程度
  const now = Date.now()
  const targetTime = postponeForm.postponeTime
  const timeDiff = targetTime - now
  
  if (timeDiff <= reminderSettings.urgentHours * 60 * 60 * 1000) {
    selectedReminder.value.urgency = 'urgent'
  } else if (timeDiff <= reminderSettings.warningHours * 60 * 60 * 1000) {
    selectedReminder.value.urgency = 'warning'
  } else {
    selectedReminder.value.urgency = 'normal'
  }
  
  showPostponeModal.value = false
  postponeForm.postponeTime = null
  postponeForm.reason = ''
  
  message.success('提醒已延期')
}

// 定时刷新
let refreshTimer: NodeJS.Timeout | null = null

onMounted(() => {
  // 加载保存的设置
  const savedSettings = localStorage.getItem('reminderSettings')
  if (savedSettings) {
    Object.assign(reminderSettings, JSON.parse(savedSettings))
  }
  
  // 初始刷新
  refreshReminders()
  
  // 设置定时刷新（每分钟）
  refreshTimer = setInterval(refreshReminders, 60 * 1000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.ticket-reminders {
  padding: 16px;
}

.stats-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
}

.stats-overview :deep(.n-statistic) {
  color: white;
}

.stats-overview :deep(.n-statistic-value) {
  color: white;
  font-size: 28px;
  font-weight: bold;
}

.stats-overview :deep(.n-statistic-label) {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.filter-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.reminder-item {
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

.reminder-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reminder-urgent {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.reminder-warning {
  border-left-color: #f97316;
  background: #fff7ed;
}

.reminder-overdue {
  border-left-color: #dc2626;
  background: #fef2f2;
  animation: pulse 2s infinite;
}

.reminder-read {
  opacity: 0.7;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.reminder-content {
  width: 100%;
  padding: 16px;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reminder-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.title-text {
  margin-right: 8px;
}

.reminder-time {
  font-size: 14px;
}

.reminder-details {
  margin-bottom: 16px;
}

.reminder-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.info-item {
  display: flex;
  align-items: center;
}

.reminder-description {
  color: #333;
  line-height: 1.5;
}

.reminder-actions {
  display: flex;
  justify-content: flex-end;
}

.text-red-500 {
  color: #ef4444;
}

.text-orange-500 {
  color: #f97316;
}

.text-red-600 {
  color: #dc2626;
}

.text-green-500 {
  color: #10b981;
}
</style>