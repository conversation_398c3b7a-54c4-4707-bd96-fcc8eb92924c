<template>
  <div class="user-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">用户管理</h1>
          <p class="page-description">系统用户账号管理与权限配置</p>
        </div>
        <div class="page-actions">
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon><person-add-outline /></n-icon>
            </template>
            新增用户
          </n-button>
          <n-button type="default" @click="handleBatchImport">
            <template #icon>
              <n-icon><cloud-upload-outline /></n-icon>
            </template>
            批量导入
          </n-button>
          <n-button type="info" @click="handleExport">
            <template #icon>
              <n-icon><cloud-download-outline /></n-icon>
            </template>
            导出用户
          </n-button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchForm.name"
          placeholder="搜索用户名称、手机号"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="searchForm.status"
          placeholder="用户状态"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
        
        <n-select
          v-model:value="searchForm.roleId"
          placeholder="用户角色"
          clearable
          style="width: 150px"
          :options="roleOptions"
        />
        
        <n-button type="default" @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">搜索</n-button>
      </n-space>
    </div>

    <!-- 统计卡片 -->
    <n-grid :cols="4" :x-gap="16" class="stats-grid">
      <n-card class="stat-card">
        <n-statistic label="总用户数" :value="stats.total" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="活跃用户" :value="stats.active" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="今日登录" :value="stats.todayLogin" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="新增用户" :value="stats.newUsers" />
      </n-card>
    </n-grid>

    <!-- 用户列表 -->
    <n-card class="table-card">
      <template #header-extra>
        <n-space>
          <n-button
            type="error"
            :disabled="!selectedRowKeys.length"
            @click="handleBatchDelete"
          >
            <template #icon>
              <n-icon><trash-outline /></n-icon>
            </template>
            批量删除
          </n-button>
          <n-button
            type="warning"
            :disabled="!selectedRowKeys.length"
            @click="handleBatchDisable"
          >
            <template #icon>
              <n-icon><ban-outline /></n-icon>
            </template>
            批量禁用
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="users"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: User) => row.id"
        :checked-row-keys="selectedRowKeys"
        remote
        @update:checked-row-keys="handleCheck"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 用户表单弹窗 -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      :title="currentUser ? '编辑用户' : '新增用户'"
      :style="{ width: '600px' }"
      :on-positive-click="handleSubmit"
      :on-negative-click="() => showModal = false"
      positive-text="保存"
      negative-text="取消"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="用户名" path="username">
          <n-input
            v-model:value="formData.username"
            placeholder="请输入用户名"
            :disabled="!!currentUser"
          />
        </n-form-item>
        
        <n-form-item label="真实姓名" path="realName">
          <n-input
            v-model:value="formData.realName"
            placeholder="请输入真实姓名"
          />
        </n-form-item>
        
        <n-form-item label="手机号" path="phone">
          <n-input
            v-model:value="formData.phone"
            placeholder="请输入手机号"
          />
        </n-form-item>
        

        
        <n-form-item label="用户角色" path="roleId">
          <n-select
            v-model:value="formData.roleId"
            placeholder="请选择用户角色"
            :options="roleOptions"
          />
        </n-form-item>
        
        <n-form-item label="所属部门" path="departmentId">
          <n-select
            v-model:value="formData.departmentId"
            placeholder="请选择所属部门"
            :options="departmentOptions"
          />
        </n-form-item>
        
        <n-form-item v-if="!currentUser" label="初始密码" path="password">
          <n-input
            v-model:value="formData.password"
            type="password"
            placeholder="请输入初始密码"
            show-password-on="click"
          />
        </n-form-item>
        
        <n-form-item label="用户状态" path="status">
          <n-radio-group v-model:value="formData.status">
            <n-radio value="active">启用</n-radio>
            <n-radio value="disabled">禁用</n-radio>
          </n-radio-group>
        </n-form-item>
        
        <n-form-item label="备注">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </n-modal>

    <!-- 重置密码弹窗 -->
    <n-modal
      v-model:show="showPasswordModal"
      preset="dialog"
      title="重置密码"
      :style="{ width: '400px' }"
      :on-positive-click="handleResetPassword"
      :on-negative-click="() => showPasswordModal = false"
      positive-text="确认重置"
      negative-text="取消"
    >
      <n-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="新密码" path="newPassword">
          <n-input
            v-model:value="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password-on="click"
          />
        </n-form-item>
        
        <n-form-item label="确认密码" path="confirmPassword">
          <n-input
            v-model:value="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password-on="click"
          />
        </n-form-item>
      </n-form>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  PersonAddOutline,
  CloudUploadOutline,
  DownloadOutline,
  SearchOutline as SearchIcon,
  RefreshOutline,
  TrashOutline,
  BanOutline,
  CreateOutline,
  KeyOutline,
  EyeOutline
} from '@vicons/ionicons5'
import type { User } from '@/types'
import type { DataTableColumns, FormInst, FormRules } from 'naive-ui'
import { useUserStore } from '@/stores'

const message = useMessage()
const dialog = useDialog()
const userStore = useUserStore()

// 预创建图标组件，避免在render函数中重复创建
const ViewIcon = () => h('n-icon', null, { default: () => h(markRaw(EyeOutline)) })
const EditIcon = () => h('n-icon', null, { default: () => h(markRaw(CreateOutline)) })
const ResetPasswordIcon = () => h('n-icon', null, { default: () => h(markRaw(KeyOutline)) })
const DeleteIcon = () => h('n-icon', null, { default: () => h(markRaw(TrashOutline)) })

// 响应式数据
const loading = ref(false)
const users = ref<User[]>([])
const showModal = ref(false)
const showPasswordModal = ref(false)
const formRef = ref<FormInst | null>(null)
const passwordFormRef = ref<FormInst | null>(null)
const currentUser = ref<User | null>(null)
const selectedRowKeys = ref<number[]>([])
const currentPasswordUser = ref<User | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  phone: '',
  status: null,
  roleId: null
})

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  todayLogin: 0,
  newUsers: 0
})

// 表单数据
const formData = reactive({
  id: null as number | null,
  username: '',
  realName: '',
  phone: '',

  roleId: null as number | null,
  departmentId: null as number | null,
  password: '',
  status: 'active',
  remark: ''
})

// 密码表单数据
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 选项配置
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'disabled' }
]

const roleOptions = ref([
  { label: '超级管理员', value: 1 },
  { label: '管理员', value: 2 },
  { label: '销售经理', value: 3 },
  { label: '销售员', value: 4 },
  { label: '客服', value: 5 }
])

const departmentOptions = ref([
  { label: '管理部', value: 1 },
  { label: '销售部', value: 2 },
  { label: '市场部', value: 3 },
  { label: '客服部', value: 4 },
  { label: '技术部', value: 5 }
])

// 表单验证规则
const formRules: FormRules = {
  username: {
    required: true,
    message: '请输入用户名',
    trigger: 'blur'
  },
  realName: {
    required: true,
    message: '请输入真实姓名',
    trigger: 'blur'
  },
  phone: {
    required: true,
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号',
    trigger: 'blur'
  },

  roleId: {
    required: true,
    type: 'number',
    message: '请选择用户角色',
    trigger: 'change'
  },
  departmentId: {
    required: true,
    type: 'number',
    message: '请选择所属部门',
    trigger: 'change'
  },
  password: {
    required: true,
    min: 6,
    message: '密码长度不能少于6位',
    trigger: 'blur'
  }
}

const passwordRules: FormRules = {
  newPassword: {
    required: true,
    min: 6,
    message: '密码长度不能少于6位',
    trigger: 'blur'
  },
  confirmPassword: {
    required: true,
    validator: (rule, value) => {
      if (value !== passwordForm.newPassword) {
        return new Error('两次输入的密码不一致')
      }
      return true
    },
    trigger: 'blur'
  }
}

// 表格列配置
const columns: DataTableColumns<User> = [
  {
    type: 'selection'
  },
  {
    title: '用户名',
    key: 'username',
    width: 120
  },
  {
    title: '真实姓名',
    key: 'realName',
    width: 100
  },
  {
    title: '手机号',
    key: 'phone',
    width: 130
  },

  {
    title: '角色',
    key: 'roleName',
    width: 100
  },
  {
    title: '部门',
    key: 'departmentName',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => {
      return h(
        'n-tag',
        { 
          type: row.status === 'active' ? 'success' : 'error',
          size: 'small'
        },
        { default: () => row.status === 'active' ? '启用' : '禁用' }
      )
    }
  },
  {
    title: '最后登录',
    key: 'lastLoginAt',
    width: 150,
    render: (row) => row.lastLoginAt ? new Date(row.lastLoginAt).toLocaleString() : '-'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => new Date(row.createdAt).toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row) => [
      h(
        'n-button',
        {
          size: 'small',
          type: 'info',
          ghost: true,
          onClick: () => handleView(row)
        },
        { default: () => '查看', icon: ViewIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleEdit(row)
        },
        { default: () => '编辑', icon: EditIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'warning',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleResetPasswordModal(row)
        },
        { default: () => '重置密码', icon: ResetPasswordIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'error',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleDelete(row)
        },
        { default: () => '删除', icon: DeleteIcon }
      )
    ]
  }
]

// 方法
const handleAdd = () => {
  currentUser.value = null
  resetForm()
  showModal.value = true
}

const handleEdit = (user: User) => {
  currentUser.value = user
  Object.assign(formData, {
    id: user.id,
    username: user.username,
    realName: user.realName,
    phone: user.phone,
    roleId: user.roleId,
    departmentId: user.departmentId,
    password: '',
    status: user.status,
    remark: user.remark || ''
  })
  showModal.value = true
}

const handleView = (user: User) => {
  // TODO: 实现查看详情功能
  message.info('查看功能开发中')
}

const handleDelete = (user: User) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除用户 "${user.realName}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用删除API
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('删除成功')
        await fetchUsers()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleResetPasswordModal = (user: User) => {
  currentPasswordUser.value = user
  Object.assign(passwordForm, {
    newPassword: '',
    confirmPassword: ''
  })
  showPasswordModal.value = true
}

const handleResetPassword = async () => {
  if (!passwordFormRef.value) return false
  
  try {
    await passwordFormRef.value.validate()
    
    // TODO: 调用重置密码API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('密码重置成功')
    showPasswordModal.value = false
    return true
  } catch (error) {
    message.error('密码重置失败')
    return false
  }
}

const handleBatchImport = () => {
  // TODO: 实现批量导入功能
  message.info('批量导入功能开发中')
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const handleBatchDelete = () => {
  dialog.warning({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个用户吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用批量删除API
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('批量删除成功')
        selectedRowKeys.value = []
        await fetchUsers()
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

const handleBatchDisable = () => {
  dialog.warning({
    title: '确认批量禁用',
    content: `确定要禁用选中的 ${selectedRowKeys.value.length} 个用户吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用批量禁用API
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('批量禁用成功')
        selectedRowKeys.value = []
        await fetchUsers()
      } catch (error) {
        message.error('批量禁用失败')
      }
    }
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // TODO: 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(currentUser.value ? '更新成功' : '创建成功')
    showModal.value = false
    await fetchUsers()
    return true
  } catch (error) {
    message.error('保存失败')
    return false
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchUsers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    status: null,
    roleId: null
  })
  pagination.page = 1
  fetchUsers()
}

const handleCheck = (rowKeys: number[]) => {
  selectedRowKeys.value = rowKeys
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchUsers()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchUsers()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    username: '',
    realName: '',
    phone: '',
    roleId: null,
    departmentId: null,
    password: '',
    status: 'active',
    remark: ''
  })
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: User[] = [
      {
        id: 1,
        username: 'admin',
        realName: '系统管理员',
        phone: '13800138001',
        roleId: 1,
        roleName: '超级管理员',
        departmentId: 1,
        departmentName: '管理部',
        status: 'active',
        lastLoginAt: '2024-03-01T09:30:00Z',
        remark: '系统管理员账号',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2024-03-01T09:30:00Z'
      },
      {
        id: 2,
        username: 'sales001',
        realName: '张销售',
        phone: '13800138002',
        roleId: 4,
        roleName: '销售员',
        departmentId: 2,
        departmentName: '销售部',
        status: 'active',
        lastLoginAt: '2024-02-28T18:20:00Z',
        remark: '销售部员工',
        createdAt: '2023-06-15T10:00:00Z',
        updatedAt: '2024-02-28T18:20:00Z'
      }
    ]
    
    users.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    Object.assign(stats, {
      total: 156,
      active: 142,
      todayLogin: 38,
      newUsers: 12
    })
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

// 初始化
onMounted(() => {
  fetchUsers()
  fetchStats()
})
</script>

<style scoped>
.user-list {
  padding: 24px;
  background: white;
  min-height: 100%;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 16px;
  color: var(--n-text-color-disabled);
  margin: 0;
  line-height: 1.4;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.table-card {
  margin-bottom: 20px;
}
</style>