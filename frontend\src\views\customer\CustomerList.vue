<template>
  <div class="customer-list">
    <!-- 页面头部 -->
    <PageHeader title="客户列表" >
      <template #actions>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <AddIcon />
          </template>
          新增客户
        </n-button>
        <n-button @click="handleImport">
          <template #icon>
            <ImportIcon />
          </template>
          导入客户
        </n-button>
        <n-button @click="handleExport">
          <template #icon>
            <ExportIcon />
          </template>
          导出客户
        </n-button>
      </template>
    </PageHeader>

    <!-- 筛选器 -->
    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchForm.name"
          placeholder="搜索客户手机号"
          clearable
          style="width: 200px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="searchForm.source"
          placeholder="客户来源"
          clearable
          style="width: 150px"
          :options="customerOptions.channelSource"
        />
        
        <n-select
          v-model:value="searchForm.status"
          placeholder="客户状态"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
        
        <n-select
          v-model:value="searchForm.level"
          placeholder="客户分类"
          clearable
          style="width: 150px"
          :options="customerOptions.customerCategory"
        />
        
        <n-select
          v-model:value="searchForm.isImportant"
          placeholder="重点客户筛选"
          clearable
          style="width: 150px"
          :options="importantOptions"
        />
        
        <n-date-picker
          v-model:value="searchForm.dateRange"
          type="daterange"
          placeholder="创建时间"
          clearable
        />
        
        <n-button type="default" @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">搜索</n-button>
      </n-space>
    </div>

    <!-- 客户表格 -->
    <n-card class="table-card" :bordered="false">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <n-icon size="18" color="#1677ff">
              <list-outline />
            </n-icon>
            <span>客户列表</span>
            <n-tag v-if="checkedRowKeys.length > 0" type="info" size="small">
              已选择 {{ checkedRowKeys.length }} 项
            </n-tag>
          </div>
          <div class="table-actions">
            <n-space size="small">
              <n-button
                v-if="checkedRowKeys.length > 0"
                type="primary"
                size="small"
                @click="handleBatchAssign"
              >
                批量分配
              </n-button>
              <n-button
                v-if="checkedRowKeys.length > 0"
                type="warning"
                size="small"
                @click="handleMoveToPool"
              >
                移入公海
              </n-button>

            </n-space>
          </div>
        </div>
      </template>
      
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="customerStore.customers"
        :loading="customerStore.loading"
        :pagination="pagination"
        :row-key="(row: Customer) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheck"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        :scroll-x="1400"
      />
    </n-card>

    <!-- 客户表单弹窗 -->
    <basic-info-form
      v-model:show="showCreateModal"
      :model-value="currentCustomer || undefined"
      :is-edit="!!currentCustomer"
      @submit="handleModalSuccess"
      @cancel="handleModalCancel"
    />
    
    <!-- 批量分配组织架构选择弹窗 -->
    <OrganizationSelectModal
      v-model:show="showBatchAssignModal"
      :customers="customerStore.customers.filter(c => checkedRowKeys.includes(c.id))"
      @confirm="handleBatchAssignConfirm"
      title="批量分配客户"
      :show-customer-list="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h, markRaw } from 'vue'
import { useMessage, useDialog, NTag, NAvatar } from 'naive-ui'
import {
  AddOutline as AddIcon,
  CloudUploadOutline as ImportIcon,
  CloudDownloadOutline as ExportIcon,
  SearchOutline as SearchIcon,
  RefreshOutline,
  CreateOutline,
  PersonAddOutline,
  ChevronDownOutline,
  ChevronUpOutline,
  PeopleOutline,
  TrendingUpOutline,
  DocumentTextOutline,
  HammerOutline,
  CheckmarkCircleOutline,
  ListOutline,
  CallOutline
} from '@vicons/ionicons5'
import { useCustomerStore } from '@/stores/modules/customer'
import BasicInfoForm from './components/BasicInfoForm.vue'
import FollowProcessActions from '@/components/business/FollowProcessActions.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import OrganizationSelectModal from '@/components/business/OrganizationSelectModal.vue'
import type { Customer } from '@/api/customerService'
import type { DataTableColumns } from 'naive-ui'
import { customerOptions } from '@/types/customer'
import { useRouter } from 'vue-router'

const message = useMessage()
const dialog = useDialog()
const customerStore = useCustomerStore()
const router = useRouter()

// 图标组件已移至FollowProcessActions组件中

// 搜索表单
const searchForm = reactive({
  name: '',
  phone: '',
  source: null,
  level: null,
  status: null,
  dateRange: null,
  isImportant: null,
  houseStatus: null,
  decorationType: null,
  areaMin: null,
  areaMax: null
})

// 高级搜索控制
const showAdvanced = ref(false)



// 表格选中
const checkedRowKeys = ref<number[]>([])

// 弹窗控制
const showCreateModal = ref(false)
const showBatchAssignModal = ref(false)
const currentCustomer = ref<Customer | null>(null)

// 分页配置
const pagination = computed(() => ({
  page: customerStore.pagination.page,
  pageSize: customerStore.pagination.pageSize,
  itemCount: customerStore.pagination.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}))

// 选项配置
const statusOptions = [
  { label: '潜在客户', value: 'potential' },
  { label: '已联系', value: 'contacted' },
  { label: '洽谈中', value: 'negotiating' },
  { label: '已成交', value: 'deal' },
  { label: '已流失', value: 'lost' }
]

const importantOptions = [
  { label: '重点客户', value: true },
  { label: '普通客户', value: false }
]

// 获取客户状态标签类型
const getStatusTagType = (status: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' => {
  switch (status) {
    case 'potential':
      return 'default'
    case 'contacted':
      return 'info'
    case 'negotiating':
      return 'warning'
    case 'deal':
      return 'success'
    case 'lost':
      return 'error'
    default:
      return 'default'
  }
}

// 获取客户状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'potential':
      return '潜在客户'
    case 'contacted':
      return '已联系'
    case 'negotiating':
      return '洽谈中'
    case 'deal':
      return '已成交'
    case 'lost':
      return '已流失'
    default:
      return '未知'
  }
}

// 获取状态颜色
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'potential':
      return '#d9d9d9'
    case 'contacted':
      return '#1890ff'
    case 'negotiating':
      return '#faad14'
    case 'deal':
      return '#52c41a'
    case 'lost':
      return '#ff4d4f'
    default:
      return '#d9d9d9'
  }
}

// 获取状态进度
const getStatusProgress = (status: string): number => {
  switch (status) {
    case 'potential':
      return 20
    case 'contacted':
      return 40
    case 'negotiating':
      return 70
    case 'deal':
      return 100
    case 'lost':
      return 0
    default:
      return 0
  }
}

// 获取客户分类颜色
const getCategoryColor = (category: string): string => {
  switch (category) {
    case 'A':
      return '#ff4d4f'
    case 'B':
      return '#fa8c16'
    case 'C':
      return '#1890ff'
    case 'D':
      return '#d9d9d9'
    default:
      return '#d9d9d9'
  }
}

// 获取客户分类标签类型
const getCategoryTagType = (category: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' => {
  switch (category) {
    case 'A':
      return 'error'
    case 'B':
      return 'warning'
    case 'C':
      return 'primary'
    case 'D':
      return 'default'
    default:
      return 'default'
  }
}

// 表格列配置
const columns: DataTableColumns<Customer> = [
  {
    type: 'selection'
  },
  {
    title: '客户姓名',
    key: 'name',
    width: 160,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      const categoryOption = customerOptions.customerCategory.find(item => item.value === row.level)
      const categoryLabel = categoryOption?.label || row.level || '未知'
      
      return h('div', { class: 'customer-name-cell' }, [
        h('div', { class: 'customer-avatar-container' }, [
          h('img', {
            class: 'customer-avatar-img',
            src: row.avatar || 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20business%20person%20headshot%20portrait%20clean%20background&image_size=square',
            alt: row.name || '客户头像',
            onError: (e: Event) => {
              // 如果图片加载失败，显示默认头像
              const target = e.target as HTMLImageElement
              if (target) {
                target.src = 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20business%20person%20headshot%20portrait%20clean%20background&image_size=square'
              }
            }
          })
        ]),
        h('div', { class: 'customer-info' }, [
          h(
            'n-button',
            {
              text: true,
              type: 'primary',
              class: 'customer-name-button',
              onClick: () => router.push(`/customer/detail/${row.id}`)
            },
            { default: () => row.name }
          ),
          h('div', { class: 'customer-category' }, categoryLabel)
        ])
      ])
    }
  },
  {
    title: '客户状态',
    key: 'status',
    width: 150,
    render: (row) => {
      const status = row.status || 'potential'
      const progress = getStatusProgress(status)
      return h('div', { class: 'stage-cell' }, [
        h(NTag, {
          type: getStatusTagType(status),
          size: 'small'
        }, {
          default: () => getStatusText(status)
        }),
        h('div', { class: 'progress-bar' }, [
          h('div', {
            class: 'progress-fill',
            style: {
              width: `${progress}%`,
              backgroundColor: getStatusColor(status)
            }
          })
        ]),
        h('span', { class: 'progress-text' }, `${progress}%`)
      ])
    }
  },
  {
    title: '手机号码',
    key: 'phone',
    width: 130,
    render: (row) => {
      const phone = row.phone || ''
      const maskedPhone = phone.length >= 7 
        ? phone.substring(0, 3) + '****' + phone.substring(phone.length - 4)
        : phone
      return h('span', { class: 'phone-cell' }, maskedPhone)
    }
  },
  {
    title: '性别',
    key: 'gender',
    width: 80,
    render: (row) => {
      const genderOption = customerOptions.gender.find(item => item.value === row.gender)
      return genderOption?.label || '未知'
    }
  },
  {
    title: '装修类型',
    key: 'decorationType',
    width: 100,
    render: (row) => {
      const decorationTypeMap = {
        'rough': '毛坯',
        'fine': '精装',
        'renovation': '旧改',
        'partial': '局改',
        'private': '私房'
      }
      return decorationTypeMap[row.decorationType as keyof typeof decorationTypeMap] || '未知'
    }
  },
  {
    title: '小区名称',
    key: 'address',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '客户来源',
    key: 'source',
    width: 100,
    render: (row) => {
      const channelOption = customerOptions.channelSource.find(item => item.value === row.source)
      return channelOption?.label || row.source || '未知'
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render: (row) => {
      if (row.created_at) {
        return new Date(row.created_at).toLocaleString()
      }
      return '-'
    }
  },
  {
    title: '最后跟进时间',
    key: 'last_follow_time',
    width: 160,
    render: (row) => {
      if (row.last_follow_time) {
        return new Date(row.last_follow_time).toLocaleString()
      }
      return '暂无跟进'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row) => h(FollowProcessActions, {
      customer: row,
      onEdit: () => handleEdit(row),
      onFollow: () => handleFollow(row),
      onMoveToTop: () => handleMoveToTop(row),
      onRefresh: () => fetchCustomers()
    })
  }
]

// 方法
const handleAdd = () => {
  currentCustomer.value = null
  showCreateModal.value = true
}

const handleEdit = (customer: Customer) => {
  currentCustomer.value = customer
  showCreateModal.value = true
}

const handleFollow = (customer: Customer) => {
  // 跳转到客户跟进页面，传递客户ID和当前阶段
  router.push({
    path: '/follow/records',
    query: {
      customerId: customer.id,
      customerName: customer.name,
      currentStage: customer.status || 'potential'
    }
  })
}

const handleMoveToTop = (customer: Customer) => {
  // 将客户移到列表顶部
  const index = customerStore.customers.findIndex((c: any) => c.id === customer.id)
  if (index > 0) {
    const [movedCustomer] = customerStore.customers.splice(index, 1)
    customerStore.customers.unshift(movedCustomer)
    message.success(`客户「${customer.name}」已置顶显示`)
  } else {
    message.info('该客户已在顶部')
  }
}

const handleImport = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls,.csv'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return
    
    try {
      await customerStore.importCustomers(file)
      message.success('客户数据导入成功')
      fetchCustomers() // 刷新列表
    } catch (error) {
      message.error('导入失败，请检查文件格式')
    }
  }
  input.click()
}

const handleExport = async () => {
  try {
    const exportFilters = {
      search: searchForm.name || searchForm.phone || undefined,
      source: searchForm.source ? [searchForm.source] : undefined,
      level: searchForm.level ? [searchForm.level] : undefined,
      status: searchForm.status ? [searchForm.status] : undefined
    }
    await customerStore.exportCustomers(exportFilters)
    message.success('客户数据导出成功')
  } catch (error) {
    message.error('导出失败，请重试')
  }
}

const handleSearch = () => {
  customerStore.setPagination(1)
  fetchCustomers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    source: null,
    level: null,
    status: null,
    dateRange: null,
    isImportant: null,
    houseStatus: null,
    decorationType: null,
    areaMin: null,
    areaMax: null
  })
  customerStore.setPagination(1)
  fetchCustomers()
}

// 切换高级搜索
const toggleAdvancedSearch = () => {
  showAdvanced.value = !showAdvanced.value
}

const handleCheck = (keys: number[]) => {
  checkedRowKeys.value = keys
}

const handlePageChange = (page: number) => {
  customerStore.setPagination(page)
  fetchCustomers()
}

const handlePageSizeChange = (pageSize: number) => {
  customerStore.setPagination(1, pageSize)
  fetchCustomers()
}

const handleModalSuccess = () => {
  showCreateModal.value = false
  currentCustomer.value = null
  fetchCustomers()
}

const handleModalCancel = () => {
  showCreateModal.value = false
  currentCustomer.value = null
}

// 批量操作方法
const handleBatchAssign = () => {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请选择要分配的客户')
    return
  }
  showBatchAssignModal.value = true
}

// 批量分配确认处理
const handleBatchAssignConfirm = (data: { employees: any[] }) => {
  const { employees } = data
  const selectedCustomers = customerStore.customers.filter(customer => 
    checkedRowKeys.value.includes(customer.id)
  )
  
  message.success(`已将 ${selectedCustomers.length} 个客户分配给 ${employees.length} 名员工`)
  
  // 清空选中状态
  checkedRowKeys.value = []
  
  // 刷新列表
  fetchCustomers()
}

const handleMoveToPool = () => {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请选择要移入公海的客户')
    return
  }
  
  dialog.warning({
    title: '确认移入公海',
    content: `确定要将选中的 ${checkedRowKeys.value.length} 个客户移入公海吗？移入后客户将变为公海客户，可被其他销售人员领取。`,
    positiveText: '确认移入',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用移入公海的API
        // await customerStore.moveToPool(checkedRowKeys.value)
        message.success(`成功将 ${checkedRowKeys.value.length} 个客户移入公海`)
        checkedRowKeys.value = []
        fetchCustomers()
      } catch (error) {
        message.error('移入公海失败，请重试')
      }
    }
  })
}





// 获取客户列表
const fetchCustomers = async () => {
  const params = {
    page: customerStore.pagination.page,
    page_size: customerStore.pagination.pageSize,
    search: searchForm.name || searchForm.phone || undefined,
    source: searchForm.source ? [searchForm.source] : undefined,
    level: searchForm.level ? [searchForm.level] : undefined,
    status: searchForm.status ? [searchForm.status] : undefined
  }
  
  try {
    await customerStore.fetchCustomers(params)
  } catch (error) {
    message.error('获取客户列表失败')
  }
}

// 初始化
onMounted(async () => {
  await fetchCustomers()

})
</script>

<style scoped>
.customer-list {
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.filters {
  margin-bottom: 10px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}



.table-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.n-card-header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table-card :deep(.n-card__content) {
  padding: 0;
  flex: 1;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  /* 表单项优化 */
  .search-form :deep(.n-form-item) {
    margin-bottom: 0;
  }

  .search-form :deep(.n-form-item-label) {
    font-size: 12px;
    font-weight: 500;
  }

  /* 按钮尺寸优化 */
  .search-header-actions :deep(.n-button) {
    height: 28px;
    font-size: 12px;
    padding: 0 12px;
  }

  .search-actions :deep(.n-button) {
    height: 28px;
    font-size: 12px;
    padding: 0 12px;
  }

  /* 输入框尺寸优化 */
  .search-form :deep(.n-input),
  .search-form :deep(.n-select),
  .search-form :deep(.n-date-picker) {
    height: 28px;
  }

  .search-form :deep(.n-input__input-el),
  .search-form :deep(.n-base-selection-input__content) {
    font-size: 12px;
  }

  /* 客户姓名列样式 */
  :deep(.customer-name-cell) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  :deep(.customer-name-cell .n-tag) {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
    padding: 0 6px;
    border-radius: 9px;
    font-weight: 500;
  }

  /* 客户状态进度条样式 */
  .stage-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .progress-bar {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 11px;
    color: #666;
    align-self: flex-end;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .header-actions {
      margin-left: 0;
      align-self: flex-end;
    }

    .search-header {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .search-header-actions {
      align-self: flex-end;
    }

    .search-card {
      max-height: 120px;
    }

    .search-form :deep(.n-grid) {
      grid-template-columns: repeat(3, 1fr);
    }

    .search-form :deep(.n-form-item-gi[data-span="4"]) {
      grid-column: span 8;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      padding: 12px 16px;
    }

    .search-card {
      max-height: 150px;
    }

    .search-card :deep(.n-card-header),
    .search-card :deep(.n-card__content) {
      padding: 8px 12px;
    }

    .search-header {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .search-header-actions {
      align-self: stretch;
    }

    .search-form :deep(.n-form-item-gi) {
      grid-column: span 24 !important;
    }

    .stats-grid {
      --n-cols: 2;
    }
  }

/* 表格现代化样式优化 */
.table-card :deep(.n-data-table) {
  --n-border-color: #f0f2f5;
  --n-th-color: #fafbfc;
  --n-td-color: #ffffff;
  --n-th-text-color: #262626;
  --n-td-text-color: #595959;
  --n-font-size: 14px;
  border-radius: 8px;
  overflow: hidden;
}

/* 表格头部样式 */
.table-card :deep(.n-data-table-th) {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  font-weight: 600;
  font-size: 13px;
  color: #262626;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 12px;
  position: relative;
}

/* 表格行样式 - 去掉默认背景色 */
.table-card :deep(.n-data-table-td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  font-size: 14px;
  line-height: 1.5;
  background-color: transparent;
}

/* 表格行悬停效果 - 加强视觉反馈 */
.table-card :deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 119, 255, 0.2);
  border-color: #91caff;
}

/* 表格行选中效果 */
.table-card :deep(.n-data-table-tr--checked .n-data-table-td) {
  background-color: #e6f4ff;
  border-color: #91caff;
}

/* 客户姓名列优化 */
:deep(.customer-name-cell) {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

:deep(.customer-avatar-container) {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.9);
  background: #f5f5f5;
  flex-shrink: 0; /* 防止容器被压缩 */
  position: relative; /* 为绝对定位的子元素提供定位上下文 */
}

:deep(.customer-name-cell .customer-avatar-container .customer-avatar-img) {
  width: 100% !important; /* 填满容器宽度 */
  height: 100% !important; /* 填满容器高度 */
  object-fit: cover !important; /* 保持图片比例并填满容器 */
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  display: block !important;
}

:deep(.customer-name-cell:hover .customer-avatar-container) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:deep(.customer-name-cell:hover .customer-avatar-img) {
  transform: scale(1.1);
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.customer-name-button {
  font-weight: 500;
  font-size: 14px;
  color: #1677ff;
  transition: all 0.2s ease;
  align-self: flex-start;
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.customer-name-button:hover {
  color: #4096ff;
  text-shadow: 0 1px 2px rgba(22, 119, 255, 0.1);
}

.customer-category {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 400;
  line-height: 1.2;
  margin-top: 2px;
}

/* 客户状态进度条样式优化 */
.stage-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  min-width: 140px;
  padding: 4px 0;
}

.stage-cell .n-tag {
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.stage-cell .n-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #f0f2f5 0%, #e8eaed 100%);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
}

.progress-fill {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
  background: linear-gradient(90deg, var(--fill-color) 0%, var(--fill-color-light) 100%);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-text {
  font-size: 11px;
  color: #8c8c8c;
  font-weight: 500;
  align-self: flex-end;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式优化 */
.table-card :deep(.n-button) {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.n-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 手机号码样式优化 */
.phone-cell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  color: #595959;
  letter-spacing: 0.5px;
}

/* 表格加载状态优化 */
.table-card :deep(.n-data-table--loading) {
  position: relative;
}

.table-card :deep(.n-data-table--loading::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    margin-left: 0;
    align-self: flex-start;
  }
  

  
  /* 表格响应式优化 */
  .table-card :deep(.n-data-table-td) {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .customer-avatar-container {
    width: 36px;
    height: 36px;
  }
  
  .customer-avatar-img {
    font-size: 14px;
  }
  
  .stage-cell {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  

  
  .search-form :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 移动端表格优化 */
  .table-card :deep(.n-data-table-td) {
    padding: 10px 6px;
    font-size: 12px;
  }
  
  .table-card :deep(.n-data-table-th) {
    padding: 12px 6px;
    font-size: 12px;
  }
  
  :deep(.customer-name-cell) {
    gap: 8px;
  }
  
  .customer-avatar-container {
    width: 32px;
    height: 32px;
  }
  
  .customer-avatar-img {
    font-size: 13px;
  }
  
  .customer-name-button {
    font-size: 13px;
  }
  
  .stage-cell {
    min-width: 100px;
    gap: 6px;
  }
  
  .progress-bar {
    height: 6px;
  }
  
  .phone-cell {
    font-size: 12px;
  }
  
  /* 操作按钮移动端优化 */
  .table-card :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {

  
  .search-form :deep(.n-grid) {
    grid-template-columns: 1fr;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  /* 小屏幕表格优化 */
  .table-card :deep(.n-data-table) {
    font-size: 11px;
  }
  
  .table-card :deep(.n-data-table-td) {
    padding: 8px 4px;
  }
  
  .table-card :deep(.n-data-table-th) {
    padding: 10px 4px;
    font-size: 11px;
  }
  
  :deep(.customer-name-cell) {
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
  }
  
  .customer-avatar-container {
    width: 28px;
    height: 28px;
  }
  
  .customer-avatar-img {
    font-size: 12px;
  }
  
  .customer-name-button {
    font-size: 12px;
  }
  
  .stage-cell {
    min-width: 80px;
    align-items: center;
  }
  
  .progress-bar {
    height: 4px;
  }
  
  .progress-text {
    font-size: 10px;
  }
  
  .phone-cell {
    font-size: 11px;
  }
  
  /* 操作按钮小屏优化 */
  .table-card :deep(.n-button) {
    font-size: 11px;
    padding: 2px 6px;
    margin: 1px;
  }
}

/* 操作列样式同步 - 确保在所有情况下都有背景 */
.table-card :deep(.n-data-table-td[data-col-key="actions"]) {
  background-color: #ffffff !important;
}

.table-card :deep(.n-data-table-tr:hover .n-data-table-td[data-col-key="actions"]) {
  background-color: #fafafa !important;
}

/* 去除表格行悬浮时的阴影 */
.table-card :deep(.n-data-table-tr:hover .n-data-table-td) {
  box-shadow: none !important;
}

/* 操作按钮样式区分 */
.table-card :deep(.n-data-table-td[data-col-key="actions"] .n-button) {
  transition: all 0.2s ease;
}

/* 操作按钮样式已移至FollowProcessActions组件中 */

</style>