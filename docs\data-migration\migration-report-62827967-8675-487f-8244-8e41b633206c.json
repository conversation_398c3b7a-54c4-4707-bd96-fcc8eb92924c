{"migrationId": "62827967-8675-487f-8244-8e41b633206c", "timestamp": "2025-08-18T07:12:27.757Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:11:46.710Z", "errors": [], "endTime": "2025-08-18T07:11:49.380Z", "duration": 2670}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:11:49.386Z", "errors": [], "endTime": "2025-08-18T07:11:53.247Z", "duration": 3861}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:11:53.250Z", "errors": [], "endTime": "2025-08-18T07:11:56.888Z", "duration": 3638}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:11:56.892Z", "errors": [], "endTime": "2025-08-18T07:11:59.620Z", "duration": 2728}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:11:59.623Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:12:00.160Z", "errors": [], "endTime": "2025-08-18T07:12:02.994Z", "duration": 2834}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:12:02.996Z", "errors": [], "endTime": "2025-08-18T07:12:05.637Z", "duration": 2641}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:12:05.640Z", "errors": [], "endTime": "2025-08-18T07:12:07.545Z", "duration": 1905}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:07.548Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:12:07.892Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:12:11.062Z", "duration": 3170}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:11.064Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:11.585Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:12:13.689Z", "errors": ["Unknown column 'duration' in 'field list'"], "endTime": "2025-08-18T07:12:17.353Z", "duration": 3664}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:17.356Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:12:17.759Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:12:19.383Z", "duration": 1624}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:19.385Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:19.690Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:20.395Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:12:20.998Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:12:23.084Z", "errors": ["Unknown column 'created_by' in 'field list'"], "endTime": "2025-08-18T07:12:26.371Z", "duration": 3287}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:12:26.374Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:12:27.754Z", "duration": 1380}], "logs": [{"id": "0ed16d46-48e3-4535-8dc6-c06b7896f284", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:11:46.710Z", "end_time": "2025-08-18T07:11:49.380Z", "duration_ms": 2670}, {"id": "663248b2-e62b-4ffb-8610-62d43b7b0406", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:11:49.386Z", "end_time": "2025-08-18T07:11:53.247Z", "duration_ms": 3861}, {"id": "764c510e-9ec3-4066-b331-bb4358aedbf8", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:11:53.250Z", "end_time": "2025-08-18T07:11:56.888Z", "duration_ms": 3638}, {"id": "6926758d-b592-4c88-82aa-df453eeaf050", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:11:56.892Z", "end_time": "2025-08-18T07:11:59.620Z", "duration_ms": 2728}, {"id": "192a2129-de28-402b-a271-d44a3d56f808", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:11:59.623Z", "end_time": "2025-08-18T07:12:00.159Z", "duration_ms": 536}, {"id": "2d72b03d-eec8-4605-8163-d2f48e785ad0", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:12:00.160Z", "end_time": "2025-08-18T07:12:02.994Z", "duration_ms": 2834}, {"id": "74838071-616a-46d4-bdab-ea64ccccc941", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:12:02.996Z", "end_time": "2025-08-18T07:12:05.637Z", "duration_ms": 2641}, {"id": "e09586bf-db4d-469e-87cc-2beac22c10c8", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:12:05.640Z", "end_time": "2025-08-18T07:12:07.545Z", "duration_ms": 1905}, {"id": "3a9dd414-9e72-48c4-a542-b71cd933fa36", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:07.548Z", "end_time": "2025-08-18T07:12:07.892Z", "duration_ms": 344}, {"id": "e1d4caa3-e4c3-4cbd-89a8-5b25313e5a1f", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:07.892Z", "end_time": "2025-08-18T07:12:11.062Z", "duration_ms": 3170}, {"id": "741c73f2-514c-42cc-81c7-06aae0a47af2", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:11.064Z", "end_time": "2025-08-18T07:12:11.585Z", "duration_ms": 521}, {"id": "9a83b1f3-82b7-4a0f-ace1-38fcd5587ea1", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:11.585Z", "end_time": "2025-08-18T07:12:13.689Z", "duration_ms": 2104}, {"id": "02f10fc0-5774-4a29-9711-e14ff8916b5f", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:13.689Z", "end_time": "2025-08-18T07:12:17.353Z", "duration_ms": 3664}, {"id": "fd17f737-2970-421c-b83b-d791a3a4f1d0", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:17.356Z", "end_time": "2025-08-18T07:12:17.759Z", "duration_ms": 403}, {"id": "ec5a6ccc-b804-4fc9-869d-0114696ec948", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:17.759Z", "end_time": "2025-08-18T07:12:19.383Z", "duration_ms": 1624}, {"id": "96108d0d-64d6-498b-88ec-59acbe09e1c9", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:19.385Z", "end_time": "2025-08-18T07:12:19.690Z", "duration_ms": 305}, {"id": "676e8fdb-7e59-45d4-ae60-f14588954302", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:19.690Z", "end_time": "2025-08-18T07:12:20.395Z", "duration_ms": 705}, {"id": "ef0e8a2b-0324-487f-b356-2d36996870f3", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:20.395Z", "end_time": "2025-08-18T07:12:20.998Z", "duration_ms": 603}, {"id": "f2d9058f-9469-42f9-8807-cd4d8f660ac8", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:20.998Z", "end_time": "2025-08-18T07:12:23.084Z", "duration_ms": 2086}, {"id": "0a1029a7-d382-4d16-aa24-ed883e96d407", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:23.084Z", "end_time": "2025-08-18T07:12:26.371Z", "duration_ms": 3287}, {"id": "e1f71200-1381-41bf-895d-43e2a119700f", "migration_id": "62827967-8675-487f-8244-8e41b633206c", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:12:26.374Z", "end_time": "2025-08-18T07:12:27.754Z", "duration_ms": 1380}]}