const mysql = require('mysql2/promise');

async function addDepartmentField() {
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'workchat_admin'
    });
    
    console.log('Connected to MySQL server');
    
    // 检查department_id字段是否已存在
    try {
      const [columns] = await connection.execute(
        "SHOW COLUMNS FROM users LIKE 'department_id'"
      );
      
      if (columns.length > 0) {
        console.log('✓ department_id field already exists in users table');
      } else {
        console.log('Adding department_id field to users table...');
        await connection.execute(
          'ALTER TABLE users ADD COLUMN department_id varchar(36) DEFAULT NULL COMMENT "部门ID"'
        );
        console.log('✓ department_id field added successfully');
      }
    } catch (error) {
      console.error('Error checking/adding department_id field:', error.message);
    }
    
    // 检查departments表是否存在
    try {
      const [tables] = await connection.execute(
        "SHOW TABLES LIKE 'departments'"
      );
      
      if (tables.length > 0) {
        console.log('✓ departments table already exists');
      } else {
        console.log('Creating departments table...');
        await connection.execute(`
          CREATE TABLE departments (
            id varchar(36) NOT NULL PRIMARY KEY,
            name varchar(100) NOT NULL COMMENT '部门名称',
            description text COMMENT '部门描述',
            parent_id varchar(36) DEFAULT NULL COMMENT '上级部门ID',
            sort_order int DEFAULT 0 COMMENT '排序',
            status enum('active','inactive') DEFAULT 'active' COMMENT '状态',
            created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表'
        `);
        console.log('✓ departments table created successfully');
        
        // 插入一些示例部门数据
        console.log('Inserting sample department data...');
        await connection.execute(`
          INSERT INTO departments (id, name, description, sort_order) VALUES
          ('dept-001', '销售部', '负责产品销售和客户关系维护', 1),
          ('dept-002', '市场部', '负责市场推广和品牌建设', 2),
          ('dept-003', '技术部', '负责产品研发和技术支持', 3),
          ('dept-004', '客服部', '负责客户服务和售后支持', 4)
        `);
        console.log('✓ Sample department data inserted');
      }
    } catch (error) {
      console.error('Error checking/creating departments table:', error.message);
    }
    
    await connection.end();
    console.log('\n✓ Database schema update complete!');
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

addDepartmentField();