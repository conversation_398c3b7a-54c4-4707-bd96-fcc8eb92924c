/**
 * 统一错误处理中间件
 * Unified Error Handling Middleware
 */

import { Request, Response, NextFunction } from 'express';
import { DatabaseError, DatabaseErrorType } from '../../src/database/MySQLManager';

// 错误类型枚举
export enum ApiErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  CONFLICT = 'CONFLICT',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  BAD_REQUEST = 'BAD_REQUEST'
}

// API错误类
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly type: ApiErrorType;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    type: ApiErrorType = ApiErrorType.INTERNAL_ERROR,
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.type = type;
    this.details = details;
  }

  // 静态方法创建常见错误
  static badRequest(message: string, details?: any): ApiError {
    return new ApiError(message, 400, ApiErrorType.BAD_REQUEST, details);
  }

  static unauthorized(message: string = '未授权访问'): ApiError {
    return new ApiError(message, 401, ApiErrorType.UNAUTHORIZED);
  }

  static forbidden(message: string = '禁止访问'): ApiError {
    return new ApiError(message, 403, ApiErrorType.FORBIDDEN);
  }

  static notFound(message: string = '资源不存在'): ApiError {
    return new ApiError(message, 404, ApiErrorType.NOT_FOUND);
  }

  static conflict(message: string, details?: any): ApiError {
    return new ApiError(message, 409, ApiErrorType.CONFLICT, details);
  }

  static validation(message: string, details?: any): ApiError {
    return new ApiError(message, 422, ApiErrorType.VALIDATION_ERROR, details);
  }

  static database(message: string, originalError?: DatabaseError): ApiError {
    return new ApiError(message, 500, ApiErrorType.DATABASE_ERROR, {
      originalError: originalError?.message,
      type: originalError?.type
    });
  }

  static internal(message: string = '服务器内部错误', details?: any): ApiError {
    return new ApiError(message, 500, ApiErrorType.INTERNAL_ERROR, details);
  }
}

// 标准API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    type: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
  };
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 响应工具类
export class ResponseUtil {
  // 成功响应
  static success<T>(
    res: Response,
    data?: T,
    message?: string,
    statusCode?: number,
    pagination?: ApiResponse['pagination']
  ): void {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      pagination
    };
    res.status(statusCode || 200).json(response);
  }

  // 错误响应
  static error(
    res: Response,
    error: ApiError,
    req: Request
  ): void {
    const response: ApiResponse = {
      success: false,
      error: {
        type: error.type,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        path: req.path
      }
    };
    res.status(error.statusCode).json(response);
  }

  // 分页响应
  static paginated<T>(
    res: Response,
    data: T[],
    pagination: {
      page: number;
      pageSize: number;
      total: number;
    },
    message?: string
  ): void {
    const response: ApiResponse<T[]> = {
      success: true,
      data,
      message,
      pagination: {
        ...pagination,
        totalPages: Math.ceil(pagination.total / pagination.pageSize)
      }
    };
    res.json(response);
  }
}

// 错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // 如果是自定义API错误
  if (error instanceof ApiError) {
    ResponseUtil.error(res, error, req);
    return;
  }

  // 如果是数据库错误
  if (error instanceof DatabaseError) {
    const apiError = ApiError.database('数据库操作失败', error);
    ResponseUtil.error(res, apiError, req);
    return;
  }

  // 处理其他已知错误类型
  if (error.name === 'ValidationError') {
    const apiError = ApiError.validation('数据验证失败', error.message);
    ResponseUtil.error(res, apiError, req);
    return;
  }

  if (error.name === 'CastError') {
    const apiError = ApiError.badRequest('无效的数据格式');
    ResponseUtil.error(res, apiError, req);
    return;
  }

  // 默认内部服务器错误
  const apiError = ApiError.internal('服务器内部错误');
  ResponseUtil.error(res, apiError, req);
};

// 404处理中间件
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = ApiError.notFound(`路由 ${req.path} 不存在`);
  ResponseUtil.error(res, error, req);
};

// 异步错误捕获装饰器
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};