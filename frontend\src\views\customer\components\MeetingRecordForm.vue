<template>
  <div class="meeting-record-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <div class="header-info">
        <n-icon size="18" color="#1677ff">
          <people-outline />
        </n-icon>
        <span class="header-title">{{ isEdit ? '编辑见面记录' : '新增见面记录' }}</span>
      </div>
      <div class="header-tips">
        <n-alert type="info" :show-icon="false" size="small">
          详细记录与客户的见面情况，包括沟通内容、客户需求和后续安排
        </n-alert>
      </div>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
    >
      <!-- 基础信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#1677ff">
            <calendar-outline />
          </n-icon>
          <span class="section-title">见面安排</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="见面时间" path="meetingTime">
            <n-date-picker
              v-model:value="formData.meetingTime"
              type="datetime"
              placeholder="选择见面时间"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts > Date.now()"
              format="yyyy-MM-dd HH:mm"
              value-format="timestamp"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="见面类型" path="meetingType">
            <n-select
              v-model:value="formData.meetingType"
              placeholder="选择见面类型"
              :options="meetingTypeOptions"
              :render-label="renderTypeLabel"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="见面地点" path="location">
            <n-input
              v-model:value="formData.location"
              placeholder="请输入见面地点"
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#8c8c8c">
                  <location-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="见面结果" path="result">
            <n-select
              v-model:value="formData.result"
              placeholder="选择见面结果"
              :options="resultOptions"
              :render-label="renderResultLabel"
            />
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 人员安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#52c41a">
            <people-circle-outline />
          </n-icon>
          <span class="section-title">人员安排</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">

          
          <n-form-item-gi label="预算范围" path="budgetRange">
            <n-select
              v-model:value="formData.budgetRange"
              placeholder="选择预算范围（可选）"
              :options="budgetOptions"
              :render-label="renderBudgetLabel"
              clearable
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="参与人员" path="participants">
          <n-space vertical style="width: 100%">
            <n-dynamic-tags
              v-model:value="formData.participants"
              placeholder="添加参与人员"
              :max="10"
            />
            <n-text depth="3" style="font-size: 12px;">
              请添加本次见面的所有参与人员，包括客户方和公司方人员
            </n-text>
          </n-space>
        </n-form-item>
      </div>

      <!-- 量房安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#faad14">
            <home-outline />
          </n-icon>
          <span class="section-title">量房安排</span>
        </div>
        
        <n-form-item label="量房时间" path="measureTime">
          <n-space vertical style="width: 100%">
            <n-date-picker
              v-model:value="formData.measureTime"
              type="datetime"
              placeholder="选择量房时间（可选）"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts < Date.now()"
              format="yyyy-MM-dd HH:mm"
              value-format="timestamp"
              clearable
            />
            <n-text depth="3" style="font-size: 12px;">
              如果客户同意量房，请安排具体的量房时间
            </n-text>
          </n-space>
        </n-form-item>
      </div>

      <!-- 见面内容区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#722ed1">
            <document-text-outline />
          </n-icon>
          <span class="section-title">见面内容</span>
        </div>
        
        <n-form-item label="详细内容" path="meetingContent">
          <n-input
            v-model:value="formData.meetingContent"
            type="textarea"
            placeholder="请详细描述见面过程：&#10;• 客户的具体需求和期望&#10;• 沟通的主要内容和要点&#10;• 客户的反应和态度&#10;• 达成的共识和约定&#10;• 遇到的问题和解决方案"
            :rows="5"
            show-count
            maxlength="800"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
        
        <n-form-item label="客户需求">
          <n-input
            v-model:value="formData.customerRequirements"
            type="textarea"
            placeholder="记录客户的具体需求和期望：&#10;• 装修风格偏好&#10;• 功能性要求&#10;• 特殊需求和注意事项&#10;• 时间安排要求"
            :rows="4"
            show-count
            maxlength="500"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 相关附件区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#13c2c2">
            <cloud-upload-outline />
          </n-icon>
          <span class="section-title">相关附件</span>
        </div>
        
        <n-form-item label="附件上传">
          <div class="attachment-section">
            <n-upload
              ref="uploadRef"
              v-model:file-list="fileList"
              multiple
              :max="5"
              list-type="image-card"
              accept="image/*,application/pdf,.doc,.docx"
              :custom-request="handleUpload"
              @remove="handleRemove"
            >
              <n-button size="medium" type="primary" ghost>
                <template #icon>
                  <n-icon><cloud-upload-outline /></n-icon>
                </template>
                上传附件
              </n-button>
            </n-upload>
            <div class="upload-tip">
              <n-text depth="3" style="font-size: 12px">
                支持图片、PDF、Word文档，最多5个文件，单个文件不超过10MB
              </n-text>
            </div>
          </div>
        </n-form-item>
      </div>

      <!-- 备注信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#eb2f96">
            <chatbubble-ellipses-outline />
          </n-icon>
          <span class="section-title">备注信息</span>
        </div>
        
        <n-form-item label="其他备注">
          <n-input
            v-model:value="formData.remarks"
            type="textarea"
            placeholder="其他需要记录的信息：&#10;• 客户特殊情况说明&#10;• 注意事项和提醒&#10;• 后续跟进要点"
            :rows="3"
            show-count
            maxlength="200"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 快速操作区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#fa8c16">
            <flash-outline />
          </n-icon>
          <span class="section-title">快速操作</span>
        </div>
        
        <div class="quick-actions">
          <n-space>
            <n-button size="small" @click="fillQuickTemplate('successful')">
              见面成功
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('interested')">
              客户感兴趣
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('need_time')">
              需要考虑
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('price_concern')">
              价格顾虑
            </n-button>
          </n-space>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, h } from 'vue'
import {
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NSelect,
  NDatePicker,
  NDynamicTags,
  NUpload,
  NButton,
  NIcon,
  NText,
  NAlert,
  NSpace,
  NTag,
  useMessage,
  type FormInst,
  type FormRules,
  type UploadFileInfo,
  type UploadCustomRequestOptions,
  type SelectRenderLabel
} from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { MeetingRecord } from '@/types'
import {
  PeopleOutline,
  CalendarOutline,
  LocationOutline,
  PeopleCircleOutline,
  HomeOutline,
  DocumentTextOutline,
  CloudUploadOutline,
  ChatbubbleEllipsesOutline,
  FlashOutline,
  PersonOutline,
  BusinessOutline,
  CheckmarkCircleOutline,
  AlertCircleOutline,
  CloseCircleOutline,
  TimeOutline,
  CashOutline,
  StarOutline
} from '@vicons/ionicons5'

// Props
interface Props {
  modelValue: MeetingRecord | null
  isEdit?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  loading: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: MeetingRecord): void
  (e: 'submit'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInst>()
const uploadRef = ref()
const message = useMessage()

// 表单数据
const formData = reactive<Partial<MeetingRecord> & {
  customerId: number | string
  meetingType: string
  measureTime: number | null
  budgetRange: string
  meetingContent: string
  customerRequirements: string
  result: string
  remarks: string
}>({
  customerId: 0,
  meetingType: 'first_meeting',
  meetingTime: Date.now(),
  location: '',
  participants: [],

  measureTime: null,
  budgetRange: '',
  meetingContent: '',
  customerRequirements: '',
  result: 'pending',
  attachments: [],
  remarks: ''
})

// 文件列表
const fileList = ref<UploadFileInfo[]>([])

// 选项数据
const meetingTypeOptions = [
  { label: '初次见面', value: 'first_meeting', icon: PersonOutline, color: '#1677ff', type: 'info' },
  { label: '深度沟通', value: 'deep_communication', icon: PeopleOutline, color: '#52c41a', type: 'success' },
  { label: '方案讲解', value: 'proposal_presentation', icon: DocumentTextOutline, color: '#faad14', type: 'warning' },
  { label: '合同洽谈', value: 'contract_negotiation', icon: BusinessOutline, color: '#722ed1', type: 'default' },
  { label: '签约见面', value: 'contract_signing', icon: CheckmarkCircleOutline, color: '#eb2f96', type: 'success' },
  { label: '其他见面', value: 'other_meeting', icon: AlertCircleOutline, color: '#8c8c8c', type: 'default' }
]

const resultOptions = [
  { label: '非常成功', value: 'very_successful', icon: CheckmarkCircleOutline, color: '#52c41a', type: 'success' },
  { label: '比较成功', value: 'successful', icon: CheckmarkCircleOutline, color: '#1677ff', type: 'info' },
  { label: '效果一般', value: 'average', icon: AlertCircleOutline, color: '#faad14', type: 'warning' },
  { label: '效果不佳', value: 'poor', icon: CloseCircleOutline, color: '#ff4d4f', type: 'error' },
  { label: '待定跟进', value: 'pending', icon: TimeOutline, color: '#722ed1', type: 'default' }
]



const budgetOptions = [
  { label: '5万以下', value: 'under_50k', icon: CashOutline, color: '#8c8c8c' },
  { label: '5-10万', value: '50k_100k', icon: CashOutline, color: '#1677ff' },
  { label: '10-15万', value: '100k_150k', icon: CashOutline, color: '#52c41a' },
  { label: '15-20万', value: '150k_200k', icon: CashOutline, color: '#faad14' },
  { label: '20-30万', value: '200k_300k', icon: CashOutline, color: '#722ed1' },
  { label: '30万以上', value: 'over_300k', icon: StarOutline, color: '#eb2f96' }
]

// 渲染标签函数
const renderTypeLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = meetingTypeOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label),
    h(NTag, { type: opt.type as any, size: 'small' }, { default: () => opt.label })
  ])
}

const renderResultLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = resultOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label),
    h(NTag, { type: opt.type as any, size: 'small' }, { default: () => opt.label })
  ])
}



const renderBudgetLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = budgetOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label)
  ])
}

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const templates = {
    successful: {
      result: 'very_successful',
      meetingContent: '本次见面非常成功，客户对我们的方案表现出浓厚兴趣。详细讨论了装修需求和预算安排，客户对我们的专业性和服务质量非常认可。',
      customerRequirements: '客户希望现代简约风格，注重实用性和美观性的结合。对厨房和卫生间的功能性要求较高，希望增加储物空间。',
      measureTime: Date.now() + 3 * 24 * 60 * 60 * 1000 // 3天后
    },
    interested: {
      result: 'successful',
      meetingContent: '客户对我们的设计理念和服务流程表现出兴趣，详细询问了装修流程、材料选择和价格构成。客户比较关注性价比和施工质量。',
      customerRequirements: '客户偏好温馨舒适的家居环境，注重环保材料的使用。对儿童房的设计有特殊要求，希望安全环保。',
      measureTime: Date.now() + 5 * 24 * 60 * 60 * 1000 // 5天后
    },
    need_time: {
      result: 'pending',
      meetingContent: '客户表示需要时间考虑，主要顾虑是预算和时间安排。已详细介绍了我们的优势和服务保障，客户表示会认真考虑。',
      customerRequirements: '客户对装修有一定想法，但还需要与家人商量具体方案。比较关注装修周期和对日常生活的影响。'
    },
    price_concern: {
      result: 'average',
      meetingContent: '客户对价格比较敏感，希望能够获得更优惠的报价。已向客户介绍了不同档次的方案选择，建议客户考虑性价比。',
      customerRequirements: '客户希望在有限预算内实现最佳效果，对材料等级和施工标准有一定要求，但更注重整体性价比。'
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
  }
}

// 表单验证规则
const rules: FormRules = {
  meetingTime: {
    required: true,
    type: 'number',
    message: '请选择见面时间',
    trigger: ['blur', 'change']
  },
  meetingType: {
    required: true,
    message: '请选择见面类型',
    trigger: ['blur', 'change']
  },
  location: {
    required: true,
    message: '请输入见面地点',
    trigger: ['blur', 'input']
  },
  meetingContent: {
    required: true,
    message: '请输入见面内容',
    trigger: ['blur', 'input'],
    min: 20,
    max: 800
  },
  result: {
    required: true,
    message: '请选择见面结果',
    trigger: ['blur', 'change']
  },
  participants: {
    required: true,
    type: 'array',
    min: 1,
    message: '请添加至少一个参与人员',
    trigger: ['blur', 'change']
  }
}

// 文件上传处理
const handleUpload = ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  // 模拟文件上传
  setTimeout(() => {
    if (file.file) {
      const url = URL.createObjectURL(file.file)
      const attachmentUrl = url || file.name
      if (!formData.attachments) {
        formData.attachments = []
      }
      formData.attachments.push(attachmentUrl)
      
      onFinish()
      message.success(`${file.name} 上传成功`)
    } else {
      onError()
      message.error(`${file.name} 上传失败`)
    }
  }, 1000)
}

// 文件删除处理
const handleRemove = (options: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = options
  if (formData.attachments) {
    const index = formData.attachments.findIndex((att: string) => att.includes(file.name || ''))
    if (index > -1) {
      formData.attachments.splice(index, 1)
    }
  }
  message.info(`${file.name} 已删除`)
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, {
        ...newValue,
        meetingTime: newValue.meetingTime ? (typeof newValue.meetingTime === 'string' ? new Date(newValue.meetingTime).getTime() : newValue.meetingTime) : Date.now(),
        participants: newValue.participants || [],
        attachments: newValue.attachments || []
      })
      
      // 更新文件列表
      if (newValue.attachments) {
        fileList.value = newValue.attachments.map((att: string, index: number) => ({
          id: `${index}`,
          name: att.split('/').pop() || att,
          status: 'finished' as const,
          url: att
        }))
      }
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，同步到父组件
watch(
  formData,
  (newValue) => {
    const meetingRecord: MeetingRecord = {
      id: newValue.id || 0,
      title: newValue.title || '',
      customer_id: newValue.customer_id || (typeof newValue.customerId === 'number' ? newValue.customerId : 0),
      customer_name: newValue.customer_name || '',
      meeting_time: newValue.meeting_time || (typeof newValue.meetingTime === 'number' ? newValue.meetingTime : Date.now()),
      duration: newValue.duration || 0,
      location: newValue.location || '',
      participants: newValue.participants || [],
      content: newValue.content || newValue.meetingContent || '',
      attachments: newValue.attachments || [],
      status: newValue.status || 'pending',
      created_by: newValue.created_by || 0,
      created_by_name: newValue.created_by_name || '',
      created_at: newValue.created_at || new Date().toISOString(),
      updated_at: newValue.updated_at || new Date().toISOString()
    }
    emit('update:modelValue', meetingRecord)
  },
  { deep: true }
)

// 表单验证方法
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    customerId: '',
    meetingType: 'first_meeting',
    meetingTime: Date.now(),
    location: '',
    participants: [],
    designer: '',
    measureTime: null,
    budgetRange: '',
    meetingContent: '',
    customerRequirements: '',
    result: 'pending',
    attachments: [],
    remarks: ''
  })
  fileList.value = []
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.meeting-record-form {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-tips {
  margin-top: 8px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.attachment-section {
  width: 100%;
}

.upload-tip {
  margin-top: 8px;
}

.quick-actions {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input__textarea) {
  min-height: 100px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

:deep(.n-date-picker) {
  width: 100%;
}

:deep(.n-select) {
  width: 100%;
}

:deep(.n-dynamic-tags) {
  width: 100%;
}

:deep(.n-upload) {
  width: 100%;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-select:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 选择器选项样式 */
:deep(.n-base-selection-label) {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 上传组件样式调整 */
:deep(.n-upload-file-list) {
  margin-top: 8px;
}

:deep(.n-upload-trigger) {
  width: auto;
}

/* 动态标签样式 */
:deep(.n-dynamic-tags .n-tag) {
  margin: 2px 4px 2px 0;
}

/* 滚动条样式 */
.meeting-record-form::-webkit-scrollbar {
  width: 6px;
}

.meeting-record-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.meeting-record-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.meeting-record-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meeting-record-form {
    padding: 0;
    max-height: 60vh;
  }
  
  .form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .quick-actions {
    padding: 8px;
  }
  
  :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .upload-tip {
    margin-top: 4px;
  }
}
</style>