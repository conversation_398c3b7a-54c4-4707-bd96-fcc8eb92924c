import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';

// 数据库错误类型枚举
export enum DatabaseErrorType {
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 数据库错误类
export class DatabaseError extends Error {
  public readonly type: DatabaseErrorType;
  public readonly originalError?: Error;

  constructor(message: string, type: DatabaseErrorType = DatabaseErrorType.UNKNOWN_ERROR, originalError?: Error) {
    super(message);
    this.name = 'DatabaseError';
    this.type = type;
    this.originalError = originalError;
  }
}

// MySQL连接配置接口
export interface MySQLConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit?: number;
  acquireTimeout?: number;
  timeout?: number;
}

// 查询结果接口
export interface QueryResult {
  success: boolean;
  data?: any;
  error?: string;
  affectedRows?: number;
  insertId?: string | number;
}

// 事务接口
export interface Transaction {
  query(sql: string, params?: any[]): Promise<QueryResult>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

/**
 * MySQL数据库管理器
 * 提供连接池管理、基础CRUD操作、事务处理和错误处理
 */
export class MySQLManager {
  private pool: mysql.Pool | null = null;
  private config: MySQLConfig;

  constructor(config: MySQLConfig) {
    this.config = {
      connectionLimit: 10,
      ...config
    };
    console.log('MySQLManager构造函数接收到的配置:', {
      host: this.config.host,
      port: this.config.port,
      user: this.config.user,
      password: this.config.password ? '***' : 'undefined',
      database: this.config.database
    });
  }

  /**
   * 初始化连接池
   */
  async initialize(): Promise<void> {
    try {
      this.pool = mysql.createPool({
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        connectionLimit: this.config.connectionLimit,
        charset: 'utf8mb4',
        timezone: '+08:00',
        waitForConnections: true,
        queueLimit: 0
      });

      // 测试连接
      await this.testConnection();
      console.log('MySQL连接池初始化成功');
    } catch (error) {
      console.error('MySQL连接池初始化失败:', error);
      throw error;
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.pool) {
      throw new Error('连接池未初始化');
    }

    try {
      console.log('测试连接配置:', {
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password ? '***' : 'undefined',
        database: this.config.database
      });
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      console.error('数据库连接测试失败:', error);
      return false;
    }
  }

  /**
   * 执行查询
   */
  async query(sql: string, params: any[] = []): Promise<QueryResult> {
    if (!this.pool) {
      return {
        success: false,
        error: '连接池未初始化'
      };
    }

    try {
      const [rows, fields] = await this.pool.execute(sql, params);
      
      return {
        success: true,
        data: rows,
        affectedRows: (rows as any).affectedRows,
        insertId: (rows as any).insertId
      };
    } catch (error: any) {
      console.error('查询执行失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 插入数据
   */
  async insert(table: string, data: Record<string, any>): Promise<QueryResult> {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    return await this.query(sql, values);
  }

  /**
   * 批量插入数据
   */
  async batchInsert(table: string, dataArray: Record<string, any>[]): Promise<QueryResult> {
    if (dataArray.length === 0) {
      return { success: true, data: [], affectedRows: 0 };
    }

    const fields = Object.keys(dataArray[0]);
    const placeholders = fields.map(() => '?').join(', ');
    const valuesSql = dataArray.map(() => `(${placeholders})`).join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES ${valuesSql}`;
    const values = dataArray.flatMap(item => Object.values(item));
    
    return await this.query(sql, values);
  }

  /**
   * 更新数据
   */
  async update(table: string, data: Record<string, any>, where: Record<string, any>): Promise<QueryResult> {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const values = [...Object.values(data), ...Object.values(where)];
    
    return await this.query(sql, values);
  }

  /**
   * 删除数据
   */
  async delete(table: string, where: Record<string, any>): Promise<QueryResult> {
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    
    const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
    const values = Object.values(where);
    
    return await this.query(sql, values);
  }

  /**
   * 查询数据
   */
  async select(table: string, options: {
    fields?: string[];
    where?: Record<string, any>;
    orderBy?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<QueryResult> {
    const {
      fields = ['*'],
      where = {},
      orderBy,
      limit,
      offset
    } = options;

    let sql = `SELECT ${fields.join(', ')} FROM ${table}`;
    const values: any[] = [];

    // WHERE子句
    if (Object.keys(where).length > 0) {
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
      sql += ` WHERE ${whereClause}`;
      values.push(...Object.values(where));
    }

    // ORDER BY子句
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }

    // LIMIT子句
    if (limit) {
      sql += ` LIMIT ${limit}`;
      if (offset) {
        sql += ` OFFSET ${offset}`;
      }
    }

    return await this.query(sql, values);
  }

  /**
   * 执行事务
   */
  async transaction<T>(callback: (connection: any) => Promise<T>): Promise<T> {
    if (!this.pool) {
      throw new Error('连接池未初始化');
    }

    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 开始事务
   */
  async beginTransaction(): Promise<Transaction> {
    if (!this.pool) {
      throw new Error('连接池未初始化');
    }

    const connection = await this.pool.getConnection();
    await connection.beginTransaction();

    return {
      query: async (sql: string, params: any[] = []): Promise<QueryResult> => {
        try {
          const [rows] = await connection.execute(sql, params);
          return {
            success: true,
            data: rows,
            affectedRows: (rows as any).affectedRows,
            insertId: (rows as any).insertId
          };
        } catch (error: any) {
          return {
            success: false,
            error: error.message
          };
        }
      },
      commit: async (): Promise<void> => {
        await connection.commit();
        connection.release();
      },
      rollback: async (): Promise<void> => {
        await connection.rollback();
        connection.release();
      }
    };
  }

  /**
   * 执行SQL文件
   */
  async executeSqlFile(sqlContent: string): Promise<QueryResult[]> {
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    const results: QueryResult[] = [];

    for (const statement of statements) {
      if (statement.toLowerCase().includes('select')) {
        // 对于SELECT语句，直接执行
        const result = await this.query(statement);
        results.push(result);
      } else {
        // 对于其他语句，执行并记录结果
        const result = await this.query(statement);
        results.push(result);
      }
    }

    return results;
  }

  /**
   * 获取表信息
   */
  async getTableInfo(tableName: string): Promise<QueryResult> {
    const sql = `DESCRIBE ${tableName}`;
    return await this.query(sql);
  }

  /**
   * 检查表是否存在
   */
  async tableExists(tableName: string): Promise<boolean> {
    const sql = `SHOW TABLES LIKE ?`;
    const result = await this.query(sql, [tableName]);
    return result.success && Array.isArray(result.data) && result.data.length > 0;
  }

  /**
   * 获取表记录数
   */
  async getTableCount(tableName: string): Promise<number> {
    const sql = `SELECT COUNT(*) as count FROM ${tableName}`;
    const result = await this.query(sql);
    if (result.success && Array.isArray(result.data) && result.data.length > 0) {
      return (result.data[0] as any).count;
    }
    return 0;
  }

  /**
   * 关闭连接池
   */
  async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log('MySQL连接池已关闭');
    }
  }

  /**
   * 断开连接（close方法的别名）
   */
  async disconnect(): Promise<void> {
    await this.close();
  }

  /**
   * 生成UUID
   */
  generateId(): string {
    return uuidv4();
  }
}

// 导出单例实例
let mysqlManager: MySQLManager | null = null;

export const createMySQLManager = (config: MySQLConfig): MySQLManager => {
  if (!mysqlManager) {
    mysqlManager = new MySQLManager(config);
  }
  return mysqlManager;
};

export const getMySQLManager = (): MySQLManager => {
  if (!mysqlManager) {
    throw new Error('MySQL管理器未初始化，请先调用createMySQLManager');
  }
  return mysqlManager;
};