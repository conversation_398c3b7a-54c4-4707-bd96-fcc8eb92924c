const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('./auth');
const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
const avatarDir = path.join(uploadDir, 'avatars');
const attachmentDir = path.join(uploadDir, 'attachments');
const qrCodeDir = path.join(uploadDir, 'qrcodes');

[uploadDir, avatarDir, attachmentDir, qrCodeDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'text/plain': ['.txt']
  };

  if (allowedTypes[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 头像上传配置
const avatarStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, avatarDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `avatar-${uniqueSuffix}${ext}`);
  }
});

const avatarUpload = multer({
  storage: avatarStorage,
  fileFilter: (req, file, cb) => {
    // 头像只允许图片
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('头像只能上传图片文件'), false);
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB
  }
});

// 附件上传配置
const attachmentStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, attachmentDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, ext);
    cb(null, `${baseName}-${uniqueSuffix}${ext}`);
  }
});

const attachmentUpload = multer({
  storage: attachmentStorage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 上传头像
router.post('/avatar', avatarUpload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的头像文件'
      });
    }

    const db = req.app.locals.db;
    const filePath = `/uploads/avatars/${req.file.filename}`;

    // 更新用户头像
    await db.update(
      'users',
      { 
        avatar: filePath,
        updated_at: new Date()
      },
      'id = ?',
      [req.user.userId]
    );

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'upload_avatar',
      target_type: 'user',
      target_id: req.user.userId,
      description: '上传头像',
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        filename: req.file.filename,
        path: filePath,
        size: req.file.size
      }
    });

  } catch (error) {
    console.error('上传头像错误:', error);
    
    // 删除已上传的文件
    if (req.file) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('删除文件失败:', err);
      });
    }

    res.status(500).json({
      success: false,
      message: '头像上传失败'
    });
  }
});

// 上传附件
router.post('/attachment', attachmentUpload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const db = req.app.locals.db;
    const filePath = `/uploads/attachments/${req.file.filename}`;

    // 保存文件信息到数据库
    const fileData = {
      original_name: req.file.originalname,
      filename: req.file.filename,
      path: filePath,
      size: req.file.size,
      mimetype: req.file.mimetype,
      uploaded_by: req.user.userId,
      created_at: new Date()
    };

    const result = await db.insert('file_uploads', fileData);

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'upload_file',
      target_type: 'file',
      target_id: result.insertId,
      description: `上传文件：${req.file.originalname}`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '文件上传成功',
      data: {
        id: result.insertId,
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: filePath,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('上传文件错误:', error);
    
    // 删除已上传的文件
    if (req.file) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('删除文件失败:', err);
      });
    }

    res.status(500).json({
      success: false,
      message: '文件上传失败'
    });
  }
});

// 批量上传附件
router.post('/attachments', attachmentUpload.array('files', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const db = req.app.locals.db;
    const uploadedFiles = [];

    // 批量保存文件信息
    for (const file of req.files) {
      const filePath = `/uploads/attachments/${file.filename}`;
      
      const fileData = {
        original_name: file.originalname,
        filename: file.filename,
        path: filePath,
        size: file.size,
        mimetype: file.mimetype,
        uploaded_by: req.user.userId,
        created_at: new Date()
      };

      const result = await db.insert('file_uploads', fileData);

      uploadedFiles.push({
        id: result.insertId,
        originalName: file.originalname,
        filename: file.filename,
        path: filePath,
        size: file.size,
        mimetype: file.mimetype
      });
    }

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'upload_files',
      target_type: 'file',
      description: `批量上传 ${req.files.length} 个文件`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: `成功上传 ${req.files.length} 个文件`,
      data: uploadedFiles
    });

  } catch (error) {
    console.error('批量上传文件错误:', error);
    
    // 删除已上传的文件
    if (req.files) {
      req.files.forEach(file => {
        fs.unlink(file.path, (err) => {
          if (err) console.error('删除文件失败:', err);
        });
      });
    }

    res.status(500).json({
      success: false,
      message: '批量上传文件失败'
    });
  }
});

// 获取文件列表
router.get('/files', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, pageSize = 10, type } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 文件类型筛选
    if (type) {
      if (type === 'image') {
        whereClause += ' AND mimetype LIKE "image/%"';
      } else if (type === 'document') {
        whereClause += ' AND (mimetype LIKE "application/%" OR mimetype = "text/plain")';
      }
    }

    const sql = `
      SELECT 
        fu.*,
        u.name as uploaded_by_name
      FROM file_uploads fu
      LEFT JOIN users u ON fu.uploaded_by = u.id
      WHERE ${whereClause}
      ORDER BY fu.created_at DESC
    `;

    const result = await db.paginate(sql, whereParams, page, pageSize);

    res.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('获取文件列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取文件列表失败'
    });
  }
});

// 删除文件
router.delete('/files/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 查找文件信息
    const file = await db.findOne('file_uploads', 'id = ?', [id]);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 权限检查：只能删除自己上传的文件或管理员可以删除所有文件
    if (file.uploaded_by !== req.user.userId && !['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 删除物理文件
    const filePath = path.join(__dirname, '../..', file.path);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // 删除数据库记录
    await db.delete('file_uploads', 'id = ?', [id]);

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'delete_file',
      target_type: 'file',
      target_id: id,
      description: `删除文件：${file.original_name}`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '文件删除成功'
    });

  } catch (error) {
    console.error('删除文件错误:', error);
    res.status(500).json({
      success: false,
      message: '删除文件失败'
    });
  }
});

// 下载文件
router.get('/download/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 查找文件信息
    const file = await db.findOne('file_uploads', 'id = ?', [id]);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    const filePath = path.join(__dirname, '../..', file.path);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.original_name)}"`);
    res.setHeader('Content-Type', file.mimetype);

    // 发送文件
    res.sendFile(filePath);

  } catch (error) {
    console.error('下载文件错误:', error);
    res.status(500).json({
      success: false,
      message: '下载文件失败'
    });
  }
});

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超出限制'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超出限制'
      });
    }
  }

  res.status(400).json({
    success: false,
    message: error.message || '文件上传失败'
  });
});

module.exports = router;