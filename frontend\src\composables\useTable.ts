import { ref, reactive, computed } from 'vue'
import type { Ref } from 'vue'

export interface TableColumn {
  key: string
  title: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (row: any, index: number) => any
}

export interface TablePagination {
  page: number
  pageSize: number
  total: number
  showSizePicker?: boolean
  pageSizes?: number[]
}

export interface UseTableOptions {
  immediate?: boolean
  defaultPageSize?: number
}

export function useTable<T = any>(
  fetchData: (params: any) => Promise<{ items: T[]; total: number }>,
  options: UseTableOptions = {}
) {
  const { immediate = true, defaultPageSize = 20 } = options
  
  // 表格数据
  const data = ref<T[]>([]) as Ref<T[]>
  const loading = ref(false)
  
  // 分页配置
  const pagination = reactive<TablePagination>({
    page: 1,
    pageSize: defaultPageSize,
    total: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100]
  })
  
  // 筛选和排序
  const filters = ref<Record<string, any>>({})
  const sorter = ref<{ field: string; order: 'asc' | 'desc' } | null>(null)
  
  // 选中的行
  const selectedRowKeys = ref<string[]>([])
  const selectedRows = ref<T[]>([])
  
  // 计算属性
  const hasData = computed(() => data.value.length > 0)
  const isEmpty = computed(() => !loading.value && data.value.length === 0)
  
  // 加载数据
  const loadData = async (params: any = {}) => {
    try {
      loading.value = true
      
      const queryParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...filters.value,
        ...params
      }
      
      if (sorter.value) {
        queryParams.sortField = sorter.value.field
        queryParams.sortOrder = sorter.value.order
      }
      
      const result = await fetchData(queryParams)
      
      data.value = result.items
      pagination.total = result.total
      
      return result
    } catch (error) {
      console.error('加载表格数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 刷新数据
  const refresh = () => {
    return loadData()
  }
  
  // 重置并刷新
  const reset = () => {
    pagination.page = 1
    filters.value = {}
    sorter.value = null
    selectedRowKeys.value = []
    selectedRows.value = []
    return loadData()
  }
  
  // 分页变化
  const handlePageChange = (page: number) => {
    pagination.page = page
    return loadData()
  }
  
  // 页面大小变化
  const handlePageSizeChange = (pageSize: number) => {
    pagination.page = 1
    pagination.pageSize = pageSize
    return loadData()
  }
  
  // 排序变化
  const handleSorterChange = (field: string, order: 'asc' | 'desc' | null) => {
    if (order) {
      sorter.value = { field, order }
    } else {
      sorter.value = null
    }
    pagination.page = 1
    return loadData()
  }
  
  // 筛选变化
  const handleFilterChange = (newFilters: Record<string, any>) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.page = 1
    return loadData()
  }
  
  // 选择变化
  const handleSelectionChange = (keys: string[], rows: T[]) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
  }
  
  // 清空选择
  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }
  
  // 初始化
  if (immediate) {
    loadData()
  }
  
  return {
    // 数据
    data,
    loading,
    pagination,
    filters,
    sorter,
    selectedRowKeys,
    selectedRows,
    
    // 计算属性
    hasData,
    isEmpty,
    
    // 方法
    loadData,
    refresh,
    reset,
    handlePageChange,
    handlePageSizeChange,
    handleSorterChange,
    handleFilterChange,
    handleSelectionChange,
    clearSelection
  }
}