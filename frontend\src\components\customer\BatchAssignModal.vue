<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    title="批量分配客户"
    class="batch-assign-modal"
    style="width: 600px;"
  >
    <template #header>
      <div class="modal-header">
        <n-icon :component="PersonAdd" />
        <span>批量分配客户</span>
      </div>
    </template>

    <div class="modal-content">
      <!-- 选中客户信息 -->
      <div class="selected-customers">
        <div class="section-title">
          <n-icon :component="People" />
          <span>选中客户 ({{ customerIds.length }}个)</span>
        </div>
        <div class="customer-list">
          <n-tag
            v-for="customer in selectedCustomers"
            :key="customer.id"
            type="info"
            size="small"
            class="customer-tag"
          >
            {{ customer.name }}
          </n-tag>
          <n-tag
            v-if="customerIds.length > displayLimit"
            type="default"
            size="small"
            class="more-tag"
          >
            +{{ customerIds.length - displayLimit }}个
          </n-tag>
        </div>
      </div>

      <!-- 分配表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
        size="medium"
        class="assign-form"
      >
        <div class="section-title">
          <n-icon :component="Person" />
          <span>分配信息</span>
        </div>

        <n-form-item label="分配给" path="assigneeId">
          <n-select
            v-model:value="formData.assigneeId"
            placeholder="请选择销售人员"
            :options="salesOptions"
            :loading="loadingSales"
            filterable
            clearable
            @focus="loadSalesOptions"
          >
            <template #empty>
              <div class="empty-state">
                <n-icon :component="Person" size="24" />
                <span>暂无销售人员</span>
              </div>
            </template>
          </n-select>
        </n-form-item>

        <n-form-item label="分配原因" path="reason">
          <n-select
            v-model:value="formData.reason"
            placeholder="请选择分配原因"
            :options="reasonOptions"
            clearable
          />
        </n-form-item>

        <n-form-item label="备注说明" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注说明（可选）"
            :rows="3"
            clearable
          />
        </n-form-item>

        <n-form-item label="通知方式" path="notifyMethods">
          <n-checkbox-group v-model:value="formData.notifyMethods">
            <n-space>
              <n-checkbox value="email" label="邮件通知" />
              <n-checkbox value="sms" label="短信通知" />
              <n-checkbox value="system" label="系统消息" />
            </n-space>
          </n-checkbox-group>
        </n-form-item>
      </n-form>

      <!-- 分配预览 -->
      <div class="assign-preview">
        <div class="section-title">
          <n-icon :component="Eye" />
          <span>分配预览</span>
        </div>
        <div class="preview-content">
          <n-alert
            v-if="formData.assigneeId && selectedAssignee"
            type="info"
            :show-icon="false"
            class="preview-alert"
          >
            <template #header>
              <div class="preview-header">
                <n-avatar
                  :size="32"
                  :src="selectedAssignee.avatar"
                  :fallback-src="'/default-avatar.png'"
                >
                  {{ selectedAssignee.name?.charAt(0) || 'S' }}
                </n-avatar>
                <div class="assignee-info">
                  <div class="assignee-name">{{ selectedAssignee.name }}</div>
                  <div class="assignee-role">{{ selectedAssignee.role || '销售顾问' }}</div>
                </div>
              </div>
            </template>
            <div class="preview-details">
              <div class="detail-item">
                <span class="label">分配客户数量：</span>
                <span class="value">{{ customerIds.length }}个</span>
              </div>
              <div class="detail-item">
                <span class="label">当前客户数量：</span>
                <span class="value">{{ selectedAssignee.customerCount || 0 }}个</span>
              </div>
              <div class="detail-item">
                <span class="label">分配后总数：</span>
                <span class="value">{{ (selectedAssignee.customerCount || 0) + customerIds.length }}个</span>
              </div>
            </div>
          </n-alert>
          <n-empty
            v-else
            description="请选择销售人员查看分配预览"
            size="small"
          />
        </div>
      </div>
    </div>

    <template #action>
      <div class="modal-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="loading"
          :disabled="!formData.assigneeId"
          @click="handleSubmit"
        >
          确认分配
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal,
  NCard,
  NForm,
  NFormItem,
  NSelect,
  NButton,
  NSpace,
  NAlert,
  NIcon,
  useMessage
} from 'naive-ui'
import type {
  FormInst,
  FormRules,
  SelectOption
} from 'naive-ui'
import {
  PersonAdd,
  People,
  Person,
  Eye
} from '@vicons/ionicons5'
import type { Customer } from '@/api/customerService'

// 销售人员接口
interface SalesUser {
  id: string
  name: string
  avatar?: string
  role?: string
  customerCount?: number
  email?: string
  phone?: string
}

// 批量分配数据接口
interface BatchAssignData {
  customerIds: number[]
  assigneeId: string
  reason: string
  remark?: string
  notifyMethods: string[]
}

// Props
interface Props {
  show: boolean
  customerIds: number[]
  customers: Customer[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  customerIds: () => [],
  customers: () => [],
  loading: false
})

// Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'submit', data: BatchAssignData): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

const message = useMessage()

// Refs
const formRef = ref<FormInst>()
const loadingSales = ref(false)
const salesUsers = ref<SalesUser[]>([])

// Computed
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const loading = computed(() => props.loading)
const customerIds = computed(() => props.customerIds)

// 显示的客户数量限制
const displayLimit = 10

// 选中的客户（用于显示）
const selectedCustomers = computed(() => {
  return props.customers
    .filter(customer => props.customerIds.includes(Number(customer.id!)))
    .slice(0, displayLimit)
})

// 销售人员选项
const salesOptions = computed((): SelectOption[] => {
  return salesUsers.value.map(user => ({
    label: `${user.name} (${user.customerCount || 0}个客户)`,
    value: user.id,
    disabled: false
  }))
})

// 选中的销售人员
const selectedAssignee = computed(() => {
  return salesUsers.value.find(user => user.id === formData.assigneeId)
})

// 表单数据
const defaultFormData = (): Omit<BatchAssignData, 'customerIds'> => ({
  assigneeId: '',
  reason: '',
  remark: '',
  notifyMethods: ['system']
})

const formData = reactive<Omit<BatchAssignData, 'customerIds'>>(defaultFormData())

// 分配原因选项
const reasonOptions: SelectOption[] = [
  { label: '新客户分配', value: 'new_customer' },
  { label: '工作量平衡', value: 'workload_balance' },
  { label: '专业匹配', value: 'expertise_match' },
  { label: '地区调整', value: 'region_adjustment' },
  { label: '客户要求', value: 'customer_request' },
  { label: '其他原因', value: 'other' }
]

// 表单验证规则
const rules: FormRules = {
  assigneeId: [
    { required: true, message: '请选择销售人员', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请选择分配原因', trigger: 'change' }
  ],
  notifyMethods: [
    { type: 'array', min: 1, message: '请至少选择一种通知方式', trigger: 'change' }
  ]
}

// 加载销售人员选项
const loadSalesOptions = async () => {
  if (salesUsers.value.length > 0) return
  
  try {
    loadingSales.value = true
    // TODO: 调用API获取销售人员列表
    // const response = await salesService.getSalesList()
    // salesUsers.value = response.data
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    salesUsers.value = [
      {
        id: '1',
        name: '张三',
        role: '高级销售顾问',
        customerCount: 25,
        email: '<EMAIL>',
        phone: '13800138001'
      },
      {
        id: '2',
        name: '李四',
        role: '销售顾问',
        customerCount: 18,
        email: '<EMAIL>',
        phone: '13800138002'
      },
      {
        id: '3',
        name: '王五',
        role: '销售经理',
        customerCount: 32,
        email: '<EMAIL>',
        phone: '13800138003'
      },
      {
        id: '4',
        name: '赵六',
        role: '销售顾问',
        customerCount: 12,
        email: '<EMAIL>',
        phone: '13800138004'
      }
    ]
  } catch (error) {
    console.error('加载销售人员失败:', error)
    message.error('加载销售人员失败')
  } finally {
    loadingSales.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (show) => {
    if (show) {
      // 重置表单
      Object.assign(formData, defaultFormData())
      formRef.value?.restoreValidation()
      // 预加载销售人员
      loadSalesOptions()
    }
  }
)

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    const submitData: BatchAssignData = {
      customerIds: props.customerIds,
      assigneeId: formData.assigneeId,
      reason: formData.reason,
      remark: formData.remark,
      notifyMethods: formData.notifyMethods
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
    message.error('请检查表单填写是否正确')
  }
}

const handleCancel = () => {
  emit('cancel')
  showModal.value = false
}
</script>

<style scoped>
.batch-assign-modal {
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.modal-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f2f5;
}

.selected-customers {
  margin-bottom: 24px;
}

.customer-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f0f2f5;
}

.customer-tag {
  font-size: 12px;
  border-radius: 4px;
}

.more-tag {
  font-size: 12px;
  border-radius: 4px;
  background: #f0f2f5;
  color: #8c8c8c;
}

.assign-form {
  margin-bottom: 24px;
}

.assign-form :deep(.n-form-item) {
  margin-bottom: 16px;
}

.assign-form :deep(.n-form-item-label) {
  font-weight: 500;
  color: #262626;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  color: #8c8c8c;
}

.assign-preview {
  margin-bottom: 16px;
}

.preview-content {
  min-height: 100px;
}

.preview-alert {
  border-radius: 6px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assignee-info {
  flex: 1;
}

.assignee-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

.assignee-role {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

.preview-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: rgba(255, 255, 255, 0.8);
}

.detail-item .value {
  font-weight: 600;
  color: #fff;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.modal-actions .n-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0 24px;
  height: 36px;
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar,
.customer-list::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track,
.customer-list::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb,
.customer-list::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.customer-list::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-assign-modal {
    width: 95vw !important;
    max-width: 95vw !important;
  }
  
  .customer-list {
    max-height: 80px;
  }
  
  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .modal-actions .n-button {
    width: 100%;
  }
}
</style>