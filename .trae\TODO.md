# TODO:

- [x] 4: 为到店阶段创建表格和表单，包含设计师字段和子阶段（量房、到店、拜访） (priority: High)
- [x] 1: 更新 FollowRecord 类型定义，添加 stage、subStage、designer、amount 等字段 (priority: High)
- [x] 2: 重构 FollowRecords.vue 组件，使用 n-tabs 实现三个主要阶段选项卡 (priority: High)
- [x] 3: 为跟进阶段创建独立的表格和表单配置 (priority: High)
- [x] 5: 为成交阶段创建表格和表单，包含子阶段（小定、大定、预售金）和金额字段 (priority: High)
- [x] 6: 实现数据关联和状态管理，确保不同阶段数据正确关联 (priority: Medium)
- [x] 7: 添加进度指示器，显示客户在各阶段的进展情况 (priority: Medium)
- [x] 8: 更新模拟数据，为三个阶段提供测试数据 (priority: Low)
