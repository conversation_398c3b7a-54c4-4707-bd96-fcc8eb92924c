{"migrationId": "979cc855-efb3-4791-bdbb-dceffd5405f1", "timestamp": "2025-08-18T07:39:50.684Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 21, "failedTables": 0, "totalRecords": 165, "migratedRecords": 165, "failedRecords": 0}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:39:03.468Z", "errors": [], "endTime": "2025-08-18T07:39:09.814Z", "duration": 6346}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:39:09.819Z", "errors": [], "endTime": "2025-08-18T07:39:12.533Z", "duration": 2714}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:39:12.535Z", "errors": [], "endTime": "2025-08-18T07:39:16.317Z", "duration": 3782}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:39:16.319Z", "errors": [], "endTime": "2025-08-18T07:39:18.727Z", "duration": 2408}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:18.730Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:39:19.126Z", "errors": [], "endTime": "2025-08-18T07:39:21.126Z", "duration": 2000}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:39:21.128Z", "errors": [], "endTime": "2025-08-18T07:39:26.017Z", "duration": 4889}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:39:26.020Z", "errors": [], "endTime": "2025-08-18T07:39:28.049Z", "duration": 2029}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:28.051Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:39:28.454Z", "errors": [], "endTime": "2025-08-18T07:39:32.543Z", "duration": 4089}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:32.545Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:33.066Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:39:33.744Z", "errors": [], "endTime": "2025-08-18T07:39:37.337Z", "duration": 3593}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:37.340Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:39:37.859Z", "errors": [], "endTime": "2025-08-18T07:39:39.258Z", "duration": 1399}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:39.261Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:39.705Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:40.377Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:39:41.977Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:39:43.525Z", "errors": [], "endTime": "2025-08-18T07:39:48.713Z", "duration": 5188}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 1, "failedRecords": 0, "startTime": "2025-08-18T07:39:48.715Z", "errors": [], "endTime": "2025-08-18T07:39:50.682Z", "duration": 1967}], "logs": [{"id": "cee7099e-6c87-460d-bf17-885b51985e7e", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:39:03.468Z", "end_time": "2025-08-18T07:39:09.814Z", "duration_ms": 6346}, {"id": "84b974e9-3112-4d2a-aeae-7330f1667ae8", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:39:09.819Z", "end_time": "2025-08-18T07:39:12.533Z", "duration_ms": 2714}, {"id": "e4efc47b-b03a-4b5b-81f6-a176197e7a7a", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:39:12.535Z", "end_time": "2025-08-18T07:39:16.317Z", "duration_ms": 3782}, {"id": "c53a17f6-bb5c-478a-8aa6-577c201f96ef", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:39:16.319Z", "end_time": "2025-08-18T07:39:18.727Z", "duration_ms": 2408}, {"id": "4727d8ef-26a3-4706-9441-b8439bc14489", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:18.730Z", "end_time": "2025-08-18T07:39:19.126Z", "duration_ms": 396}, {"id": "5b657e86-b620-4d48-8180-af6d0e23d588", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:39:19.126Z", "end_time": "2025-08-18T07:39:21.126Z", "duration_ms": 2000}, {"id": "50c70591-e3b7-4117-818e-c22ac161cc1a", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:39:21.128Z", "end_time": "2025-08-18T07:39:26.017Z", "duration_ms": 4889}, {"id": "680e935d-20fb-44dc-b0c9-967e4c0112f2", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:39:26.020Z", "end_time": "2025-08-18T07:39:28.049Z", "duration_ms": 2029}, {"id": "901e4a6e-7e0f-4635-9f3f-0067c33f8f9b", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:28.052Z", "end_time": "2025-08-18T07:39:28.454Z", "duration_ms": 402}, {"id": "dbe9ff5e-b20e-4fc6-bbbc-9facaa5c5e2c", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:39:28.454Z", "end_time": "2025-08-18T07:39:32.543Z", "duration_ms": 4089}, {"id": "8614e310-aa6a-4df7-8382-deab3bf179c6", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:32.545Z", "end_time": "2025-08-18T07:39:33.066Z", "duration_ms": 521}, {"id": "10b76154-5ef3-4386-965b-ed8c780f068a", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:33.066Z", "end_time": "2025-08-18T07:39:33.744Z", "duration_ms": 678}, {"id": "615f531f-3011-410b-b571-3035702d4d07", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:39:33.744Z", "end_time": "2025-08-18T07:39:37.337Z", "duration_ms": 3593}, {"id": "d268b0d3-60cb-4783-9ef2-21d5fc1c3101", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:37.340Z", "end_time": "2025-08-18T07:39:37.859Z", "duration_ms": 519}, {"id": "778eec22-ca35-404e-89d7-5dfcd5930891", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:39:37.859Z", "end_time": "2025-08-18T07:39:39.258Z", "duration_ms": 1399}, {"id": "d2232aaf-b302-4551-9089-8f83a8150399", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:39.261Z", "end_time": "2025-08-18T07:39:39.705Z", "duration_ms": 444}, {"id": "ced05ae3-31ea-438a-8004-340171c3b901", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:39.705Z", "end_time": "2025-08-18T07:39:40.377Z", "duration_ms": 672}, {"id": "a315639f-996e-47cd-8ab1-d7e69c1deef1", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:40.377Z", "end_time": "2025-08-18T07:39:41.977Z", "duration_ms": 1600}, {"id": "9652919f-9ded-405d-8df0-7eb50a5daa30", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:39:41.977Z", "end_time": "2025-08-18T07:39:43.525Z", "duration_ms": 1548}, {"id": "ba482c78-69c6-4091-94c1-7ce59487e8e0", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:39:43.525Z", "end_time": "2025-08-18T07:39:48.713Z", "duration_ms": 5188}, {"id": "84a938ee-bee5-4295-a81d-6ee8f045b8a4", "migration_id": "979cc855-efb3-4791-bdbb-dceffd5405f1", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 1, "start_time": "2025-08-18T07:39:48.715Z", "end_time": "2025-08-18T07:39:50.682Z", "duration_ms": 1967}]}