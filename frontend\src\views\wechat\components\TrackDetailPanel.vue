<template>
  <div class="track-detail-panel">
    <!-- 基本信息 -->
    <n-card title="浏览详情" class="detail-card">
      <n-descriptions :column="2">
        <n-descriptions-item label="页面标题">
          {{ track.page_title || '无标题' }}
        </n-descriptions-item>
        <n-descriptions-item label="页面URL">
          <n-ellipsis style="max-width: 200px">
            {{ track.page_url }}
          </n-ellipsis>
        </n-descriptions-item>
        <n-descriptions-item label="页面类型">
          <n-tag :type="getPageTypeColor(track.page_type)" size="small">
            {{ getPageTypeText(track.page_type) }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="停留时间">
          <n-tag :type="getDurationColor(track.duration)" size="small">
            {{ formatDuration(track.duration) }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="访问时间">
          {{ new Date(track.created_at).toLocaleString() }}
        </n-descriptions-item>
        <n-descriptions-item label="IP地址">
          {{ track.ip_address || '-' }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>

    <!-- 客户信息 -->
    <n-card title="客户信息" class="customer-card">
      <div class="customer-info">
        <n-avatar
          :size="60"
          :src="customerInfo?.avatar_url"
          fallback-src="/default-avatar.png"
        />
        <div class="customer-details">
          <h4>{{ customerInfo?.nickname || '未知用户' }}</h4>
          <p class="customer-meta">
            <span>ID: {{ track.customer_id }}</span>
            <span v-if="customerInfo?.city">{{ customerInfo.city }}</span>
          </p>
          <div class="customer-tags" v-if="customerInfo?.tags?.length">
            <n-tag
              v-for="tag in customerInfo.tags"
              :key="tag"
              size="small"
              style="margin-right: 4px"
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 技术信息 -->
    <n-card title="技术信息" class="tech-card">
      <n-descriptions :column="1">
        <n-descriptions-item label="来源页面">
          <div v-if="track.referrer">
            <n-ellipsis style="max-width: 300px">
              {{ track.referrer }}
            </n-ellipsis>
            <n-button
              text
              size="small"
              @click="openUrl(track.referrer)"
              style="margin-left: 8px"
            >
              <template #icon>
                <n-icon><OpenOutline /></n-icon>
              </template>
              打开
            </n-button>
          </div>
          <span v-else>直接访问</span>
        </n-descriptions-item>
        <n-descriptions-item label="用户代理">
          <div class="user-agent-detail">
            <div class="ua-summary">
              <n-tag size="small" :type="getDeviceType(track.user_agent).color">
                {{ getDeviceType(track.user_agent).text }}
              </n-tag>
              <n-tag size="small" style="margin-left: 8px">
                {{ getBrowserInfo(track.user_agent) }}
              </n-tag>
            </div>
            <n-collapse style="margin-top: 8px">
              <n-collapse-item title="完整用户代理" name="ua">
                <code class="user-agent-full">{{ track.user_agent || '未知' }}</code>
              </n-collapse-item>
            </n-collapse>
          </div>
        </n-descriptions-item>
      </n-descriptions>
    </n-card>

    <!-- 行为分析 -->
    <n-card title="行为分析" class="behavior-card">
      <div class="behavior-metrics">
        <div class="metric-item">
          <div class="metric-label">页面深度</div>
          <div class="metric-value">
            <n-progress
              type="line"
              :percentage="getPageDepthPercentage()"
              :color="getPageDepthColor()"
              :show-indicator="false"
            />
            <span class="metric-text">{{ getPageDepthText() }}</span>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-label">参与度</div>
          <div class="metric-value">
            <n-progress
              type="line"
              :percentage="getEngagementPercentage()"
              :color="getEngagementColor()"
              :show-indicator="false"
            />
            <span class="metric-text">{{ getEngagementText() }}</span>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-label">跳出风险</div>
          <div class="metric-value">
            <n-progress
              type="line"
              :percentage="getBounceRiskPercentage()"
              :color="getBounceRiskColor()"
              :show-indicator="false"
            />
            <span class="metric-text">{{ getBounceRiskText() }}</span>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 相关推荐 -->
    <n-card title="相关推荐" class="recommendation-card">
      <div class="recommendations">
        <div class="recommendation-item">
          <n-icon size="20" color="#18a058">
            <TrendingUpOutline />
          </n-icon>
          <div class="recommendation-content">
            <div class="recommendation-title">优化建议</div>
            <div class="recommendation-desc">
              {{ getOptimizationSuggestion() }}
            </div>
          </div>
        </div>
        
        <div class="recommendation-item">
          <n-icon size="20" color="#2080f0">
            <BulbOutline />
          </n-icon>
          <div class="recommendation-content">
            <div class="recommendation-title">内容推荐</div>
            <div class="recommendation-desc">
              {{ getContentRecommendation() }}
            </div>
          </div>
        </div>
        
        <div class="recommendation-item">
          <n-icon size="20" color="#f0a020">
            <StarOutline />
          </n-icon>
          <div class="recommendation-content">
            <div class="recommendation-title">营销机会</div>
            <div class="recommendation-desc">
              {{ getMarketingOpportunity() }}
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-space>
        <n-button type="primary" @click="handleCreateReminder">
          <template #icon>
            <n-icon><AlarmOutline /></n-icon>
          </template>
          创建提醒
        </n-button>
        <n-button @click="handleViewCustomer">
          <template #icon>
            <n-icon><PersonOutline /></n-icon>
          </template>
          查看客户
        </n-button>
        <n-button @click="handleAnalyzeBehavior">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          行为分析
        </n-button>
        <n-button @click="$emit('close')">
          关闭
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NTag,
  NEllipsis,
  NAvatar,
  NButton,
  NIcon,
  NCollapse,
  NCollapseItem,
  NProgress,
  NSpace,
  useMessage
} from 'naive-ui'
import {
  OpenOutline,
  TrendingUpOutline,
  BulbOutline,
  StarOutline,
  AlarmOutline,
  PersonOutline,
  AnalyticsOutline
} from '@vicons/ionicons5'
import { useWechatStore } from '@/stores/wechatStore'
import type { BrowsingTrack, WechatCustomer } from '@/types'

interface Props {
  track: BrowsingTrack
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const wechatStore = useWechatStore()

// 客户信息
const customerInfo = ref<WechatCustomer | null>(null)

// 获取页面类型颜色
const getPageTypeColor = (pageType: string | undefined): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  if (!pageType) return 'default'
  const colorMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    home: 'info',
    product: 'success',
    article: 'warning',
    activity: 'error',
    about: 'default',
    contact: 'default',
    other: 'default'
  }
  return colorMap[pageType] || 'default'
}

// 获取页面类型文本
const getPageTypeText = (pageType: string | undefined) => {
  if (!pageType) return '未知'
  const textMap: Record<string, string> = {
    home: '首页',
    product: '产品页',
    article: '文章页',
    activity: '活动页',
    about: '关于我们',
    contact: '联系我们',
    other: '其他'
  }
  return textMap[pageType] || '未知'
}

// 获取停留时间颜色
const getDurationColor = (duration: number): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  if (duration < 10) return 'error'
  if (duration < 60) return 'warning'
  return 'success'
}

// 格式化停留时间
const formatDuration = (duration: number) => {
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}分${seconds}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 获取设备类型
const getDeviceType = (userAgent: string): { text: string; color: 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' } => {
  if (!userAgent) return { text: '未知', color: 'default' }
  
  const ua = userAgent.toLowerCase()
  if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
    return { text: '移动设备', color: 'success' }
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    return { text: '平板设备', color: 'info' }
  } else {
    return { text: '桌面设备', color: 'warning' }
  }
}

// 获取浏览器信息
const getBrowserInfo = (userAgent: string) => {
  if (!userAgent) return '未知浏览器'
  
  const ua = userAgent.toLowerCase()
  if (ua.includes('chrome')) return 'Chrome'
  if (ua.includes('firefox')) return 'Firefox'
  if (ua.includes('safari')) return 'Safari'
  if (ua.includes('edge')) return 'Edge'
  if (ua.includes('opera')) return 'Opera'
  return '其他浏览器'
}

// 获取页面深度百分比
const getPageDepthPercentage = () => {
  // 基于停留时间计算页面深度
  const duration = props.track.duration
  if (duration < 10) return 20
  if (duration < 30) return 40
  if (duration < 60) return 60
  if (duration < 180) return 80
  return 100
}

// 获取页面深度颜色
const getPageDepthColor = () => {
  const percentage = getPageDepthPercentage()
  if (percentage < 40) return '#d03050'
  if (percentage < 70) return '#f0a020'
  return '#18a058'
}

// 获取页面深度文本
const getPageDepthText = () => {
  const percentage = getPageDepthPercentage()
  if (percentage < 40) return '浅度浏览'
  if (percentage < 70) return '中度浏览'
  return '深度浏览'
}

// 获取参与度百分比
const getEngagementPercentage = () => {
  const duration = props.track.duration
  const hasReferrer = !!props.track.referrer
  
  let score = 0
  if (duration > 30) score += 30
  if (duration > 60) score += 30
  if (duration > 180) score += 20
  if (hasReferrer) score += 20
  
  return Math.min(score, 100)
}

// 获取参与度颜色
const getEngagementColor = () => {
  const percentage = getEngagementPercentage()
  if (percentage < 40) return '#d03050'
  if (percentage < 70) return '#f0a020'
  return '#18a058'
}

// 获取参与度文本
const getEngagementText = () => {
  const percentage = getEngagementPercentage()
  if (percentage < 40) return '低参与度'
  if (percentage < 70) return '中参与度'
  return '高参与度'
}

// 获取跳出风险百分比
const getBounceRiskPercentage = () => {
  const duration = props.track.duration
  if (duration < 5) return 90
  if (duration < 10) return 70
  if (duration < 30) return 50
  if (duration < 60) return 30
  return 10
}

// 获取跳出风险颜色
const getBounceRiskColor = () => {
  const percentage = getBounceRiskPercentage()
  if (percentage > 70) return '#d03050'
  if (percentage > 40) return '#f0a020'
  return '#18a058'
}

// 获取跳出风险文本
const getBounceRiskText = () => {
  const percentage = getBounceRiskPercentage()
  if (percentage > 70) return '高风险'
  if (percentage > 40) return '中风险'
  return '低风险'
}

// 获取优化建议
const getOptimizationSuggestion = () => {
  const duration = props.track.duration
  const pageType = props.track.page_type
  
  if (duration < 10) {
    return '页面停留时间较短，建议优化页面加载速度和内容吸引力'
  } else if (duration < 30) {
    return '用户有一定兴趣，可以增加相关内容推荐和互动元素'
  } else if (pageType === 'product') {
    return '产品页浏览时间较长，可以适时推送优惠信息或客服咨询'
  } else {
    return '用户参与度较高，可以引导关注公众号或加入会员'
  }
}

// 获取内容推荐
const getContentRecommendation = () => {
  const pageType = props.track.page_type
  
  switch (pageType) {
    case 'product':
      return '推荐相关产品、用户评价或使用教程'
    case 'article':
      return '推荐相关文章、专题内容或作者其他作品'
    case 'activity':
      return '推荐类似活动、优惠信息或会员权益'
    case 'home':
      return '推荐热门产品、最新文章或精选活动'
    default:
      return '根据用户兴趣推荐个性化内容'
  }
}

// 获取营销机会
const getMarketingOpportunity = () => {
  const duration = props.track.duration
  const pageType = props.track.page_type
  
  if (duration > 180 && pageType === 'product') {
    return '高意向客户，建议主动联系或推送专属优惠'
  } else if (duration > 60) {
    return '有购买意向，可以推送相关优惠或新品信息'
  } else if (pageType === 'activity') {
    return '对活动感兴趣，可以推送类似活动或会员邀请'
  } else {
    return '潜在客户，建议通过内容营销培养兴趣'
  }
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 创建提醒
const handleCreateReminder = () => {
  // TODO: 实现创建提醒功能
  message.info('创建提醒功能开发中')
}

// 查看客户
const handleViewCustomer = () => {
  // TODO: 跳转到客户详情页
  message.info('跳转到客户详情页')
}

// 行为分析
const handleAnalyzeBehavior = () => {
  // TODO: 打开行为分析模态框
  message.info('行为分析功能开发中')
}

// 加载客户信息
const loadCustomerInfo = async () => {
  try {
    customerInfo.value = await wechatStore.fetchWechatCustomer(props.track.customer_id)
  } catch (error) {
    console.error('加载客户信息失败:', error)
  }
}

// 初始化
onMounted(() => {
  loadCustomerInfo()
})
</script>

<style scoped>
.track-detail-panel {
  padding: 16px;
}

.detail-card,
.customer-card,
.tech-card,
.behavior-card,
.recommendation-card {
  margin-bottom: 16px;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.customer-details h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.customer-meta {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
}

.customer-meta span {
  margin-right: 12px;
}

.customer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.user-agent-detail {
  display: flex;
  flex-direction: column;
}

.ua-summary {
  display: flex;
  align-items: center;
}

.user-agent-full {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  display: block;
}

.behavior-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-label {
  font-weight: 500;
  font-size: 14px;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.recommendation-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}
</style>