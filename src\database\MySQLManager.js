const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

class MySQLManager {
  constructor(config) {
    this.config = {
      host: config.host || 'localhost',
      port: config.port || 3306,
      user: config.user || 'root',
      password: config.password || '',
      database: config.database || 'yysh_miniprogram',
      charset: config.charset || 'utf8mb4',
      timezone: config.timezone || '+08:00',
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000
    };
    this.pool = null;
  }

  async connect() {
    try {
      console.log('正在连接MySQL数据库...');
      console.log(`数据库配置: ${this.config.host}:${this.config.port}/${this.config.database}`);
      
      // 创建连接池
      this.pool = mysql.createPool(this.config);
      
      // 测试连接
      const connection = await this.pool.getConnection();
      console.log('MySQL数据库连接成功');
      connection.release();
      
      // 初始化数据库表
      await this.initDatabase();
      
      return true;
    } catch (error) {
      console.error('MySQL数据库连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log('MySQL数据库连接已关闭');
    }
  }

  async query(sql, params = []) {
    try {
      // 对于带LIMIT/OFFSET的查询，使用query方法而不是execute
      if (sql.includes('LIMIT') && sql.includes('OFFSET')) {
        const [rows] = await this.pool.query(sql, params);
        return { rows };
      } else {
        const [rows] = await this.pool.execute(sql, params);
        return { rows };
      }
    } catch (error) {
      console.error('SQL查询错误:', error.message);
      console.error('SQL语句:', sql);
      console.error('参数:', params);
      throw error;
    }
  }

  async transaction(callback) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  async initDatabase() {
    try {
      console.log('正在初始化数据库表...');
      
      // 读取SQL初始化脚本
      const sqlPath = path.join(__dirname, 'init.sql');
      const initSQL = await fs.readFile(sqlPath, 'utf8');
      
      // 分割SQL语句并执行
      const statements = initSQL.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));
      
      for (const statement of statements) {
        const trimmedStatement = statement.trim();
        if (trimmedStatement && !trimmedStatement.startsWith('INSERT')) {
          try {
            await this.pool.execute(trimmedStatement);
            console.log(`✅ 执行SQL: ${trimmedStatement.substring(0, 50)}...`);
          } catch (error) {
            if (error.code !== 'ER_TABLE_EXISTS_ERROR') {
              console.error(`❌ SQL执行失败: ${trimmedStatement.substring(0, 50)}...`);
              console.error(`错误: ${error.message}`);
            }
          }
        }
      }
      
      // 执行INSERT语句
      for (const statement of statements) {
        const trimmedStatement = statement.trim();
        if (trimmedStatement && trimmedStatement.startsWith('INSERT')) {
          try {
            await this.pool.execute(trimmedStatement);
            console.log(`✅ 插入默认数据: ${trimmedStatement.substring(0, 50)}...`);
          } catch (error) {
            if (error.code !== 'ER_DUP_ENTRY') {
              console.error(`❌ 插入数据失败: ${trimmedStatement.substring(0, 50)}...`);
              console.error(`错误: ${error.message}`);
            }
          }
        }
      }
      
      // 创建额外的默认数据
      await this.createDefaultData();
      
      console.log('数据库表初始化完成');
    } catch (error) {
      console.error('数据库初始化失败:', error.message);
      throw error;
    }
  }

  async createDefaultData() {
    try {
      // 检查表是否存在
      const [tables] = await this.pool.execute('SHOW TABLES');
      const tableNames = tables.map(table => Object.values(table)[0]);
      
      // 创建部门表数据（如果表存在）
      if (tableNames.includes('departments')) {
        try {
          const [deptRows] = await this.pool.execute('SELECT id FROM departments WHERE id = ?', [1]);
          if (deptRows.length === 0) {
            await this.pool.execute('INSERT INTO departments (id, name, sort_order) VALUES (?, ?, ?)', [1, '销售部', 1]);
            await this.pool.execute('INSERT INTO departments (id, name, sort_order) VALUES (?, ?, ?)', [2, '设计部', 2]);
            await this.pool.execute('INSERT INTO departments (id, name, sort_order) VALUES (?, ?, ?)', [3, '管理部', 3]);
            console.log('✅ 默认部门创建成功');
          }
        } catch (error) {
          console.log('⚠️  部门表不存在，跳过部门数据创建');
        }
      }

      // 创建默认管理员（如果用户表存在）
      if (tableNames.includes('users')) {
        try {
          const [adminRows] = await this.pool.execute('SELECT id FROM users WHERE username = ?', ['admin']);
          if (adminRows.length === 0) {
            const bcrypt = require('bcryptjs');
            const hashedPassword = await bcrypt.hash('admin123', 10);
            
            await this.pool.execute(`
              INSERT INTO users (username, password, real_name, role, department, status) 
              VALUES (?, ?, ?, ?, ?, ?)
            `, ['admin', hashedPassword, '系统管理员', 'admin', '管理部', 1]);
            
            console.log('✅ 默认管理员创建成功 - 用户名: admin, 密码: admin123');
          }
        } catch (error) {
          console.log('⚠️  用户表不存在，跳过管理员创建');
        }
      }

      // 创建默认客户标签（如果标签表存在）
      if (tableNames.includes('customer_tags')) {
        try {
          const [tagRows] = await this.pool.execute('SELECT id FROM customer_tags WHERE name = ?', ['潜在客户']);
          if (tagRows.length === 0) {
            const defaultTags = [
              ['潜在客户', '#409EFF', 'status', 1],
              ['意向客户', '#67C23A', 'status', 1],
              ['成交客户', '#E6A23C', 'status', 1],
              ['流失客户', '#F56C6C', 'status', 1],
              ['VIP客户', '#9C27B0', 'level', 1]
            ];

            for (const [name, color, category, isSystem] of defaultTags) {
              await this.pool.execute(`
                INSERT INTO customer_tags (name, color, category, is_system, created_by)
                VALUES (?, ?, ?, ?, ?)
              `, [name, color, category, isSystem, 1]);
            }
            console.log('✅ 默认客户标签创建成功');
          }
        } catch (error) {
          console.log('⚠️  客户标签表不存在，跳过标签创建');
        }
      }

    } catch (error) {
      console.error('创建默认数据失败:', error.message);
      // 不抛出错误，允许应用继续启动
    }
  }

  // 兼容性方法 - 保持与原有代码的接口一致
  async insert(table, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    const result = await this.query(sql, values);
    return result;
  }

  async update(table, data, where) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const whereFields = Object.keys(where);
    const whereValues = Object.values(where);
    const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const result = await this.query(sql, [...values, ...whereValues]);
    return result;
  }

  async delete(table, where) {
    const whereFields = Object.keys(where);
    const whereValues = Object.values(where);
    const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');
    
    const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
    const result = await this.query(sql, whereValues);
    return result;
  }

  async findOne(table, where = {}) {
    const whereFields = Object.keys(where);
    const whereValues = Object.values(where);
    
    let sql = `SELECT * FROM ${table}`;
    if (whereFields.length > 0) {
      const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');
      sql += ` WHERE ${whereClause}`;
    }
    sql += ' LIMIT 1';
    
    const result = await this.query(sql, whereValues);
    return result.rows[0] || null;
  }

  async findAll(table, where = {}, options = {}) {
    const whereFields = Object.keys(where);
    const whereValues = Object.values(where);
    
    let sql = `SELECT * FROM ${table}`;
    if (whereFields.length > 0) {
      const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');
      sql += ` WHERE ${whereClause}`;
    }
    
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`;
    }
    
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
    }
    
    const result = await this.query(sql, whereValues);
    return result.rows;
  }
}

module.exports = MySQLManager;