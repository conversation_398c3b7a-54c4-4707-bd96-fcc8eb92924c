"""基础模型类

定义所有模型的基础类和通用字段
"""

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, DateTime, String, func, text
from datetime import datetime
from typing import Any, Dict
import uuid

# 创建基础模型类
Base = declarative_base()


class TimestampMixin:
    """时间戳混入类
    
    为模型添加创建时间和更新时间字段
    """
    created_at = Column(
        DateTime, 
        server_default=func.current_timestamp(), 
        nullable=False, 
        comment="创建时间"
    )
    updated_at = Column(
        DateTime, 
        server_default=func.current_timestamp(), 
        onupdate=func.current_timestamp(),
        nullable=False, 
        comment="更新时间"
    )


class UUIDMixin:
    """UUID主键混入类
    
    为模型添加UUID主键
    """
    id = Column(
        String(36), 
        primary_key=True, 
        default=lambda: str(uuid.uuid4()), 
        comment="主键ID"
    )


class BaseModel(Base, UUIDMixin, TimestampMixin):
    """基础模型类
    
    所有模型的基础类，包含通用字段和方法
    """
    __abstract__ = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={getattr(self, 'id', 'None')})>"