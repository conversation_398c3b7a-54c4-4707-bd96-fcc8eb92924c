<template>
  <div class="deal-stage-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <n-button @click="handleFilter">
          <template #icon>
            <n-icon><FilterOutline /></n-icon>
          </template>
          筛选
        </n-button>
      </div>
      <div class="toolbar-right">
        <n-select
          v-model:value="selectedSubStage"
          placeholder="子阶段筛选"
          :options="subStageOptions"
          clearable
          style="width: 120px; margin-right: 12px"
          @update:value="handleSubStageFilter"
        />
        <n-select
          v-model:value="selectedDesigner"
          placeholder="设计师筛选"
          :options="designerOptions"
          clearable
          style="width: 120px; margin-right: 12px"
          @update:value="handleDesignerFilter"
        />
        <n-select
          v-model:value="selectedPaymentStatus"
          placeholder="付款状态"
          :options="paymentStatusOptions"
          clearable
          style="width: 120px"
          @update:value="handlePaymentStatusFilter"
        />
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="filteredData"
      :pagination="pagination"
      :loading="loading"
      :row-key="(row: any) => row.id"
      striped
      size="small"
      class="deal-table"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { NButton, NTag, NIcon, NTooltip, type DataTableColumns } from 'naive-ui'
import { FilterOutline, CreateOutline, TrashOutline, EyeOutline } from '@vicons/ionicons5'
import type { FollowRecord } from '@/types'

interface Props {
  data: FollowRecord[]
  loading?: boolean
  customerOptions: Array<{ label: string; value: number }>
  designerOptions: Array<{ label: string; value: number }>
}

interface Emits {
  edit: [record: FollowRecord]
  delete: [record: FollowRecord]
  view: [record: FollowRecord]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 筛选状态
const selectedSubStage = ref<string | null>(null)
const selectedDesigner = ref<number | null>(null)
const selectedPaymentStatus = ref<string | null>(null)

// 子阶段选项
const subStageOptions = [
  { label: '小定', value: 'small_deposit' },
  { label: '大定', value: 'large_deposit' },
  { label: '预售金', value: 'presale' }
]

// 付款状态选项
const paymentStatusOptions = [
  { label: '未付款', value: 'unpaid' },
  { label: '部分付款', value: 'partial' },
  { label: '已付款', value: 'paid' },
  { label: '已退款', value: 'refunded' }
]

// 分页配置
const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}

// 过滤数据
const filteredData = computed(() => {
  let result = props.data.filter(item => item.stage === 'deal')
  
  if (selectedSubStage.value) {
    result = result.filter(item => item.subStage === selectedSubStage.value)
  }
  
  if (selectedDesigner.value) {
    result = result.filter(item => item.designerId === selectedDesigner.value)
  }
  
  if (selectedPaymentStatus.value) {
    result = result.filter(item => item.paymentStatus === selectedPaymentStatus.value)
  }
  
  return result
})

// 表格列配置
const columns: DataTableColumns<FollowRecord> = [
  {
    title: '客户姓名',
    key: 'customerName',
    width: 120,
    render: (row) => {
      const customer = props.customerOptions.find(c => c.value === row.customerId)
      return customer?.label || '-'
    }
  },
  {
    title: '子阶段',
    key: 'subStage',
    width: 100,
    render: (row) => {
      const subStageMap: Record<string, { label: string; type: any }> = {
        small_deposit: { label: '小定', type: 'info' },
        large_deposit: { label: '大定', type: 'warning' },
        presale: { label: '预售金', type: 'success' }
      }
      const subStage = subStageMap[row.subStage || '']
      return subStage ? h(NTag, { type: subStage.type, size: 'small' }, { default: () => subStage.label }) : '-'
    }
  },
  {
    title: '设计师',
    key: 'designer',
    width: 100,
    render: (row) => row.designer || '-'
  },
  {
    title: '成交金额',
    key: 'amount',
    width: 120,
    render: (row) => {
      if (row.amount) {
        return h('span', { style: 'color: #f56c6c; font-weight: 600;' }, `¥${row.amount.toLocaleString()}`)
      }
      return '-'
    }
  },
  {
    title: '合同编号',
    key: 'contractNo',
    width: 140,
    render: (row) => row.contractNo || '-'
  },
  {
    title: '付款状态',
    key: 'paymentStatus',
    width: 100,
    render: (row) => {
      const statusMap: Record<string, { label: string; type: any }> = {
        unpaid: { label: '未付款', type: 'error' },
        partial: { label: '部分付款', type: 'warning' },
        paid: { label: '已付款', type: 'success' },
        refunded: { label: '已退款', type: 'default' }
      }
      const status = statusMap[row.paymentStatus || '']
      return status ? h(NTag, { type: status.type, size: 'small' }, { default: () => status.label }) : '-'
    }
  },
  {
    title: '成交时间',
    key: 'dealDate',
    width: 160,
    render: (row) => {
      if (row.dealDate) {
        return new Date(row.dealDate).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return '-'
    }
  },
  {
    title: '跟进时间',
    key: 'followTime',
    width: 160,
    render: (row) => {
      if (row.followTime) {
        return new Date(row.followTime).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return '-'
    }
  },
  {
    title: '跟进内容',
    key: 'content',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    render: (row) => row.content || '-'
  },
  {
    title: '下次跟进',
    key: 'nextFollowTime',
    width: 160,
    render: (row) => {
      if (row.nextFollowTime) {
        const nextTime = new Date(row.nextFollowTime)
        const now = new Date()
        const isOverdue = nextTime < now
        return h(
          'span',
          { 
            style: isOverdue ? 'color: #f56c6c; font-weight: 600;' : 'color: #67c23a;'
          },
          nextTime.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })
        )
      }
      return '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 140,
    fixed: 'right',
    render: (row) => {
      return h('div', { class: 'action-buttons' }, [
        h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'info',
                quaternary: true,
                onClick: () => emit('view', row)
              },
              { icon: () => h(NIcon, null, { default: () => h(EyeOutline) }) }
            ),
            default: () => '查看详情'
          }
        ),
        h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'primary',
                quaternary: true,
                onClick: () => emit('edit', row)
              },
              { icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
            ),
            default: () => '编辑'
          }
        ),
        h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'error',
                quaternary: true,
                onClick: () => emit('delete', row)
              },
              { icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
            ),
            default: () => '删除'
          }
        )
      ])
    }
  }
]

// 事件处理
const handleFilter = () => {
  // 实现筛选逻辑
  console.log('筛选功能')
}

const handleSubStageFilter = () => {
  // 子阶段筛选已通过计算属性实现
}

const handleDesignerFilter = () => {
  // 设计师筛选已通过计算属性实现
}

const handlePaymentStatusFilter = () => {
  // 付款状态筛选已通过计算属性实现
}
</script>

<style scoped>
.deal-stage-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.deal-table {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.n-data-table-th) {
  background-color: #f8f9fa;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.3s ease;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f8f9fa;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

:deep(.n-button) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-tag) {
  border-radius: 4px;
  font-weight: 500;
}
</style>