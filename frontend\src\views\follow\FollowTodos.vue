<template>
  <div class="follow-todos">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
  
          <p class="page-description">管理跟进待办事项和任务提醒</p>
        </div>
        <n-space>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon><add-outline /></n-icon>
            </template>
            新增待办
          </n-button>
          <n-button @click="handleMarkAllRead">
            <template #icon>
              <n-icon><checkmark-done-outline /></n-icon>
            </template>
            全部已读
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 筛选标签 -->
    <n-card class="filter-card">
      <n-space>
        <n-tag
          :type="activeFilter === 'all' ? 'primary' : 'default'"
          :bordered="false"
          checkable
          :checked="activeFilter === 'all'"
          @click="handleFilterChange('all')"
        >
          全部 ({{ stats.total }})
        </n-tag>
        <n-tag
          :type="activeFilter === 'pending' ? 'warning' : 'default'"
          :bordered="false"
          checkable
          :checked="activeFilter === 'pending'"
          @click="handleFilterChange('pending')"
        >
          待处理 ({{ stats.pending }})
        </n-tag>
        <n-tag
          :type="activeFilter === 'overdue' ? 'error' : 'default'"
          :bordered="false"
          checkable
          :checked="activeFilter === 'overdue'"
          @click="handleFilterChange('overdue')"
        >
          已逾期 ({{ stats.overdue }})
        </n-tag>
        <n-tag
          :type="activeFilter === 'completed' ? 'success' : 'default'"
          :bordered="false"
          checkable
          :checked="activeFilter === 'completed'"
          @click="handleFilterChange('completed')"
        >
          已完成 ({{ stats.completed }})
        </n-tag>
        <n-tag
          :type="activeFilter === 'today' ? 'info' : 'default'"
          :bordered="false"
          checkable
          :checked="activeFilter === 'today'"
          @click="handleFilterChange('today')"
        >
          今日待办 ({{ stats.today }})
        </n-tag>
      </n-space>
    </n-card>

    <!-- 待办事项列表 -->
    <n-card class="todos-card">
      <n-list>
        <n-list-item
          v-for="todo in filteredTodos"
          :key="todo.id"
          class="todo-item"
          :class="{
            'todo-completed': todo.status === 'completed',
            'todo-overdue': isOverdue(todo)
          }"
        >
          <template #prefix>
            <n-checkbox
              :checked="todo.status === 'completed'"
              @update:checked="(checked: boolean) => handleToggleStatus(todo, checked)"
            />
          </template>
          
          <n-thing class="todo-content">
            <template #header>
              <n-space align="center">
                <span class="todo-title" :class="{ 'completed': todo.status === 'completed' }">
                  {{ todo.title }}
                </span>
                <n-tag
                  v-if="todo.priority === 'high'"
                  type="error"
                  size="small"
                >
                  高优先级
                </n-tag>
                <n-tag
                  v-else-if="todo.priority === 'medium'"
                  type="warning"
                  size="small"
                >
                  中优先级
                </n-tag>
                <n-tag
                  v-if="isOverdue(todo)"
                  type="error"
                  size="small"
                >
                  已逾期
                </n-tag>
              </n-space>
            </template>
            
            <template #description>
              <div class="todo-meta">
                <n-space>
                  <span v-if="todo.customerName">
                    <n-icon><person-outline /></n-icon>
                    {{ todo.customerName }}
                  </span>
                  <span>
                    <n-icon><time-outline /></n-icon>
                    {{ formatDate(todo.dueTime) }}
                  </span>
                  <span v-if="todo.type">
                    <n-icon><bookmark-outline /></n-icon>
                    {{ getTypeLabel(todo.type) }}
                  </span>
                </n-space>
              </div>
              <p v-if="todo.description" class="todo-description">
                {{ todo.description }}
              </p>
            </template>
            
            <template #action>
              <n-space>
                <n-button
                  size="small"
                  type="primary"
                  ghost
                  @click="handleEdit(todo)"
                >
                  <template #icon>
                    <n-icon><create-outline /></n-icon>
                  </template>
                  编辑
                </n-button>
                <n-button
                  size="small"
                  type="error"
                  ghost
                  @click="handleDelete(todo)"
                >
                  <template #icon>
                    <n-icon><trash-outline /></n-icon>
                  </template>
                  删除
                </n-button>
              </n-space>
            </template>
          </n-thing>
        </n-list-item>
      </n-list>
      
      <n-empty v-if="filteredTodos.length === 0" description="暂无待办事项" />
    </n-card>

    <!-- 待办事项表单弹窗 -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      title="待办事项"
      :style="{ width: '500px' }"
      :on-positive-click="handleSubmit"
      :on-negative-click="() => showModal = false"
      positive-text="保存"
      negative-text="取消"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item label="标题" path="title">
          <n-input
            v-model:value="formData.title"
            placeholder="请输入待办事项标题"
          />
        </n-form-item>
        <n-form-item label="类型" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择类型"
            :options="typeOptions"
          />
        </n-form-item>
        <n-form-item label="优先级" path="priority">
          <n-select
            v-model:value="formData.priority"
            placeholder="请选择优先级"
            :options="priorityOptions"
          />
        </n-form-item>
        <n-form-item label="关联客户">
          <n-select
            v-model:value="formData.customerId"
            placeholder="请选择客户（可选）"
            :options="customerOptions"
            filterable
            clearable
          />
        </n-form-item>
        <n-form-item label="截止时间" path="dueTime">
          <n-date-picker
            v-model:value="formData.dueTime"
            type="datetime"
            placeholder="请选择截止时间"
          />
        </n-form-item>
        <n-form-item label="描述">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入描述（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  AddOutline,
  CheckmarkDoneOutline,
  PersonOutline,
  TimeOutline,
  BookmarkOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import type { TodoItem } from '@/types'
import type { FormInst, FormRules } from 'naive-ui'

const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const todos = ref<TodoItem[]>([])
const showModal = ref(false)
const formRef = ref<FormInst | null>(null)
const currentTodo = ref<TodoItem | null>(null)
const activeFilter = ref('all')

// 统计数据
const stats = reactive({
  total: 0,
  pending: 0,
  overdue: 0,
  completed: 0,
  today: 0
})

// 表单数据
const formData = reactive({
  id: null as number | null,
  title: '',
  type: '',
  priority: 'medium' as 'high' | 'medium' | 'low',
  customerId: undefined as number | undefined,
  dueTime: null as number | null,
  description: ''
})

// 选项配置
const typeOptions = [
  { label: '客户跟进', value: 'follow' },
  { label: '会议安排', value: 'meeting' },
  { label: '电话回访', value: 'callback' },
  { label: '资料整理', value: 'document' },
  { label: '其他', value: 'other' }
]

const priorityOptions = [
  { label: '高优先级', value: 'high' },
  { label: '中优先级', value: 'medium' },
  { label: '低优先级', value: 'low' }
]

const customerOptions = ref([
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 }
])

// 表单验证规则
const formRules: FormRules = {
  title: {
    required: true,
    message: '请输入待办事项标题',
    trigger: 'blur'
  },
  type: {
    required: true,
    message: '请选择类型',
    trigger: 'change'
  },
  priority: {
    required: true,
    message: '请选择优先级',
    trigger: 'change'
  },
  dueTime: {
    required: true,
    type: 'number',
    message: '请选择截止时间',
    trigger: 'change'
  }
}

// 计算属性
const filteredTodos = computed(() => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
  
  return todos.value.filter(todo => {
    switch (activeFilter.value) {
      case 'pending':
        return todo.status === 'pending'
      case 'overdue':
        return todo.status === 'pending' && new Date(todo.dueTime) < now
      case 'completed':
        return todo.status === 'completed'
      case 'today':
        const dueDate = new Date(todo.dueTime)
        return dueDate >= today && dueDate < tomorrow
      default:
        return true
    }
  })
})

// 方法
const isOverdue = (todo: TodoItem) => {
  return todo.status === 'pending' && new Date(todo.dueTime) < new Date()
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = date.getTime() - now.getTime()
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days === 1) {
    return '明天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days === -1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days > 0) {
    return `${days}天后 ` + date.toLocaleString('zh-CN')
  } else {
    return `${Math.abs(days)}天前 ` + date.toLocaleString('zh-CN')
  }
}

const getTypeLabel = (type: string) => {
  const option = typeOptions.find(item => item.value === type)
  return option?.label || type
}

const handleFilterChange = (filter: string) => {
  activeFilter.value = filter
}

const handleAdd = () => {
  currentTodo.value = null
  resetForm()
  showModal.value = true
}

const handleEdit = (todo: TodoItem) => {
  currentTodo.value = todo
  Object.assign(formData, {
    id: todo.id,
    title: todo.title,
    type: todo.type,
    priority: todo.priority,
    customerId: todo.customerId,
    dueTime: new Date(todo.dueTime).getTime(),
    description: todo.description || ''
  })
  showModal.value = true
}

const handleDelete = (todo: TodoItem) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除待办事项「${todo.title}」吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        loading.value = true
        // TODO: 调用删除API
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const index = todos.value.findIndex(item => item.id === todo.id)
        if (index > -1) {
          todos.value.splice(index, 1)
        }
        
        message.success('删除成功')
        updateStats()
      } catch (error) {
        message.error('删除失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const handleToggleStatus = async (todo: TodoItem, checked: boolean) => {
  try {
    loading.value = true
    // TODO: 调用更新状态API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    todo.status = checked ? 'completed' : 'pending'
    if (checked) {
      todo.completedAt = new Date().toISOString()
    } else {
      todo.completedAt = undefined
    }
    
    message.success(checked ? '已标记为完成' : '已标记为待处理')
    updateStats()
  } catch (error) {
    message.error('更新状态失败')
  } finally {
    loading.value = false
  }
}

const handleMarkAllRead = () => {
  dialog.info({
    title: '确认操作',
    content: '确定要将所有待办事项标记为已读吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        loading.value = true
        // TODO: 调用批量更新API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        todos.value.forEach(todo => {
          if (todo.status === 'pending') {
            todo.status = 'completed'
            todo.completedAt = new Date().toISOString()
          }
        })
        
        message.success('已全部标记为完成')
        updateStats()
      } catch (error) {
        message.error('操作失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // TODO: 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (currentTodo.value) {
      // 更新
      const index = todos.value.findIndex(item => item.id === currentTodo.value!.id)
      if (index > -1) {
        Object.assign(todos.value[index], {
          title: formData.title,
          type: formData.type,
          priority: formData.priority,
          customerId: formData.customerId,
          customerName: formData.customerId ? customerOptions.value.find(c => c.value === formData.customerId)?.label : undefined,
          dueTime: new Date(formData.dueTime!).toISOString(),
          description: formData.description,
          updatedAt: new Date().toISOString()
        })
      }
    } else {
      // 新增
      const newTodo: TodoItem = {
        id: String(Date.now()),
        title: formData.title,
        type: formData.type,
        priority: formData.priority,
        status: 'pending',
        customerId: formData.customerId,
        customerName: formData.customerId ? customerOptions.value.find(c => c.value === formData.customerId)?.label : undefined,
        dueTime: new Date(formData.dueTime!).toISOString(),
        description: formData.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      todos.value.unshift(newTodo)
    }
    
    message.success(currentTodo.value ? '更新成功' : '创建成功')
    showModal.value = false
    updateStats()
    return true
  } catch (error) {
    message.error('保存失败')
    return false
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    title: '',
    type: '',
    priority: 'medium',
    customerId: undefined,
    dueTime: null,
    description: ''
  })
}

const updateStats = () => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
  
  stats.total = todos.value.length
  stats.pending = todos.value.filter(todo => todo.status === 'pending').length
  stats.overdue = todos.value.filter(todo => todo.status === 'pending' && new Date(todo.dueTime) < now).length
  stats.completed = todos.value.filter(todo => todo.status === 'completed').length
  stats.today = todos.value.filter(todo => {
    const dueDate = new Date(todo.dueTime)
    return dueDate >= today && dueDate < tomorrow
  }).length
}

// 获取待办事项列表
const fetchTodos = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: TodoItem[] = [
      {
        id: '1',
        title: '跟进张三的项目需求',
        type: 'follow',
        priority: 'high',
        status: 'pending',
        customerId: 1,
        customerName: '张三',
        dueTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后
        description: '了解具体需求和预算',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        title: '准备明天的产品演示',
        type: 'meeting',
        priority: 'medium',
        status: 'pending',
        customerId: undefined,
        dueTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 明天
        description: '准备PPT和演示环境',
        createdAt: '2024-01-16T09:00:00Z',
        updatedAt: '2024-01-16T09:00:00Z'
      },
      {
        id: '3',
        title: '整理客户资料',
        type: 'document',
        priority: 'low',
        status: 'completed',
        customerId: undefined,
        dueTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天
        description: '整理本周新增客户的资料',
        createdAt: '2024-01-14T14:00:00Z',
        updatedAt: '2024-01-15T16:30:00Z',
        completedAt: '2024-01-15T16:30:00Z'
      }
    ]
    
    todos.value = mockData
    updateStats()
  } catch (error) {
    message.error('获取待办事项失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchTodos()
})
</script>

<style scoped>
.follow-todos {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.todos-card {
  margin-bottom: 20px;
}

.todo-item {
  border-bottom: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
}

.todo-item:hover {
  background-color: var(--n-color-hover);
}

.todo-item.todo-completed {
  opacity: 0.6;
}

.todo-item.todo-overdue {
  border-left: 4px solid var(--n-color-error);
}

.todo-content {
  width: 100%;
}

.todo-title {
  font-weight: 500;
  font-size: 16px;
}

.todo-title.completed {
  text-decoration: line-through;
  color: var(--n-text-color-3);
}

.todo-meta {
  margin: 8px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.todo-meta .n-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.todo-description {
  margin: 8px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
  line-height: 1.5;
}
</style>