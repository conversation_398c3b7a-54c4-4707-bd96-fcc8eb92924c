"""营销活动和统计分析模型

定义营销活动、统计分析等相关的数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Enum, Integer, Numeric, Table
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class CampaignStatus(enum.Enum):
    """活动状态枚举"""
    DRAFT = "draft"  # 草稿
    ACTIVE = "active"  # 进行中
    PAUSED = "paused"  # 暂停
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消


class CampaignType(enum.Enum):
    """活动类型枚举"""
    EMAIL = "email"  # 邮件营销
    SMS = "sms"  # 短信营销
    WECHAT = "wechat"  # 微信营销
    PHONE = "phone"  # 电话营销
    EVENT = "event"  # 活动营销
    SOCIAL = "social"  # 社交媒体
    OTHER = "other"  # 其他


class ParticipantStatus(enum.Enum):
    """参与者状态枚举"""
    INVITED = "invited"  # 已邀请
    CONFIRMED = "confirmed"  # 已确认
    ATTENDED = "attended"  # 已参加
    NO_SHOW = "no_show"  # 未出席
    CANCELLED = "cancelled"  # 已取消


class ShareStatus(enum.Enum):
    """分享状态枚举"""
    PENDING = "pending"  # 待分享
    SHARED = "shared"  # 已分享
    CLICKED = "clicked"  # 已点击
    CONVERTED = "converted"  # 已转化


class MarketingCampaign(BaseModel):
    """营销活动模型
    
    对应数据库中的marketing_campaigns表
    """
    __tablename__ = "marketing_campaigns"
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="活动名称")
    description = Column(Text, nullable=True, comment="活动描述")
    campaign_type = Column(Enum(CampaignType), nullable=False, comment="活动类型")
    
    # 状态信息
    status = Column(Enum(CampaignStatus), default=CampaignStatus.DRAFT, nullable=False, comment="活动状态")
    
    # 时间信息
    start_date = Column(DateTime, nullable=False, comment="开始时间")
    end_date = Column(DateTime, nullable=False, comment="结束时间")
    
    # 预算和目标
    budget = Column(Numeric(10, 2), nullable=True, comment="预算")
    target_audience = Column(Text, nullable=True, comment="目标受众")
    expected_participants = Column(Integer, nullable=True, comment="预期参与人数")
    
    # 内容信息
    content = Column(Text, nullable=True, comment="活动内容")
    materials = Column(Text, nullable=True, comment="活动素材（JSON格式）")
    
    # 管理信息
    created_by = Column(String(36), ForeignKey('users.id'), nullable=False, comment="创建人ID")
    manager_id = Column(String(36), ForeignKey('users.id'), nullable=True, comment="负责人ID")
    
    # 统计信息
    total_participants = Column(Integer, default=0, nullable=False, comment="总参与人数")
    total_shares = Column(Integer, default=0, nullable=False, comment="总分享次数")
    total_clicks = Column(Integer, default=0, nullable=False, comment="总点击次数")
    conversion_rate = Column(Numeric(5, 2), default=0, nullable=False, comment="转化率")
    
    # 关联关系
    creator = relationship("User", foreign_keys=[created_by], lazy="select")
    manager = relationship("User", foreign_keys=[manager_id], lazy="select")
    participants = relationship("CampaignParticipant", back_populates="campaign", lazy="select")
    shares = relationship("CampaignShare", back_populates="campaign", lazy="select")
    
    def __repr__(self):
        return f"<MarketingCampaign(id={self.id}, name={self.name})>"
    
    @property
    def status_display(self):
        """状态显示名称"""
        status_map = {
            CampaignStatus.DRAFT: "草稿",
            CampaignStatus.ACTIVE: "进行中",
            CampaignStatus.PAUSED: "暂停",
            CampaignStatus.COMPLETED: "已完成",
            CampaignStatus.CANCELLED: "已取消"
        }
        return status_map.get(self.status, self.status.value)
    
    @property
    def type_display(self):
        """类型显示名称"""
        type_map = {
            CampaignType.EMAIL: "邮件营销",
            CampaignType.SMS: "短信营销",
            CampaignType.WECHAT: "微信营销",
            CampaignType.PHONE: "电话营销",
            CampaignType.EVENT: "活动营销",
            CampaignType.SOCIAL: "社交媒体",
            CampaignType.OTHER: "其他"
        }
        return type_map.get(self.campaign_type, self.campaign_type.value)


class CampaignParticipant(BaseModel):
    """活动参与者模型
    
    对应数据库中的campaign_participants表
    """
    __tablename__ = "campaign_participants"
    
    # 基本信息
    campaign_id = Column(String(36), ForeignKey('marketing_campaigns.id'), nullable=False, comment="活动ID")
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    
    # 参与信息
    status = Column(Enum(ParticipantStatus), default=ParticipantStatus.INVITED, nullable=False, comment="参与状态")
    invited_at = Column(DateTime, nullable=False, comment="邀请时间")
    responded_at = Column(DateTime, nullable=True, comment="响应时间")
    attended_at = Column(DateTime, nullable=True, comment="参加时间")
    
    # 反馈信息
    feedback = Column(Text, nullable=True, comment="反馈内容")
    rating = Column(Integer, nullable=True, comment="评分（1-5）")
    
    # 统计信息
    engagement_score = Column(Numeric(5, 2), nullable=True, comment="参与度评分")
    
    # 关联关系
    campaign = relationship("MarketingCampaign", back_populates="participants", lazy="select")
    customer = relationship("Customer", lazy="select")
    
    def __repr__(self):
        return f"<CampaignParticipant(id={self.id}, campaign_id={self.campaign_id}, customer_id={self.customer_id})>"
    
    @property
    def status_display(self):
        """状态显示名称"""
        status_map = {
            ParticipantStatus.INVITED: "已邀请",
            ParticipantStatus.CONFIRMED: "已确认",
            ParticipantStatus.ATTENDED: "已参加",
            ParticipantStatus.NO_SHOW: "未出席",
            ParticipantStatus.CANCELLED: "已取消"
        }
        return status_map.get(self.status, self.status.value)


class CampaignShare(BaseModel):
    """活动分享模型
    
    对应数据库中的campaign_shares表
    """
    __tablename__ = "campaign_shares"
    
    # 基本信息
    campaign_id = Column(String(36), ForeignKey('marketing_campaigns.id'), nullable=False, comment="活动ID")
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="分享客户ID")
    
    # 分享信息
    share_platform = Column(String(50), nullable=False, comment="分享平台")
    share_url = Column(String(500), nullable=True, comment="分享链接")
    share_content = Column(Text, nullable=True, comment="分享内容")
    
    # 状态和时间
    status = Column(Enum(ShareStatus), default=ShareStatus.PENDING, nullable=False, comment="分享状态")
    shared_at = Column(DateTime, nullable=True, comment="分享时间")
    
    # 统计信息
    click_count = Column(Integer, default=0, nullable=False, comment="点击次数")
    conversion_count = Column(Integer, default=0, nullable=False, comment="转化次数")
    
    # 关联关系
    campaign = relationship("MarketingCampaign", back_populates="shares", lazy="select")
    customer = relationship("Customer", lazy="select")
    
    def __repr__(self):
        return f"<CampaignShare(id={self.id}, campaign_id={self.campaign_id})>"
    
    @property
    def status_display(self):
        """状态显示名称"""
        status_map = {
            ShareStatus.PENDING: "待分享",
            ShareStatus.SHARED: "已分享",
            ShareStatus.CLICKED: "已点击",
            ShareStatus.CONVERTED: "已转化"
        }
        return status_map.get(self.status, self.status.value)


class SalesFunnelStats(BaseModel):
    """销售漏斗统计模型
    
    对应数据库中的sales_funnel_stats表
    """
    __tablename__ = "sales_funnel_stats"
    
    # 统计维度
    date = Column(DateTime, nullable=False, comment="统计日期")
    user_id = Column(String(36), ForeignKey('users.id'), nullable=True, comment="用户ID")
    department_id = Column(String(36), ForeignKey('departments.id'), nullable=True, comment="部门ID")
    
    # 漏斗数据
    leads_count = Column(Integer, default=0, nullable=False, comment="线索数量")
    prospects_count = Column(Integer, default=0, nullable=False, comment="潜在客户数量")
    opportunities_count = Column(Integer, default=0, nullable=False, comment="机会数量")
    customers_count = Column(Integer, default=0, nullable=False, comment="成交客户数量")
    
    # 转化率
    lead_to_prospect_rate = Column(Numeric(5, 2), default=0, nullable=False, comment="线索到潜客转化率")
    prospect_to_opportunity_rate = Column(Numeric(5, 2), default=0, nullable=False, comment="潜客到机会转化率")
    opportunity_to_customer_rate = Column(Numeric(5, 2), default=0, nullable=False, comment="机会到成交转化率")
    overall_conversion_rate = Column(Numeric(5, 2), default=0, nullable=False, comment="整体转化率")
    
    # 关联关系
    user = relationship("User", lazy="select")
    department = relationship("Department", lazy="select")
    
    def __repr__(self):
        return f"<SalesFunnelStats(id={self.id}, date={self.date})>"


class CustomerValueAnalysis(BaseModel):
    """客户价值分析模型
    
    对应数据库中的customer_value_analysis表
    """
    __tablename__ = "customer_value_analysis"
    
    # 基本信息
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    analysis_date = Column(DateTime, nullable=False, comment="分析日期")
    
    # 价值指标
    total_revenue = Column(Numeric(12, 2), default=0, nullable=False, comment="总收入")
    average_order_value = Column(Numeric(10, 2), default=0, nullable=False, comment="平均订单价值")
    purchase_frequency = Column(Numeric(5, 2), default=0, nullable=False, comment="购买频率")
    customer_lifetime_value = Column(Numeric(12, 2), default=0, nullable=False, comment="客户生命周期价值")
    
    # 行为指标
    engagement_score = Column(Numeric(5, 2), default=0, nullable=False, comment="参与度评分")
    loyalty_score = Column(Numeric(5, 2), default=0, nullable=False, comment="忠诚度评分")
    satisfaction_score = Column(Numeric(5, 2), default=0, nullable=False, comment="满意度评分")
    
    # 风险指标
    churn_risk_score = Column(Numeric(5, 2), default=0, nullable=False, comment="流失风险评分")
    payment_risk_score = Column(Numeric(5, 2), default=0, nullable=False, comment="付款风险评分")
    
    # 分类标签
    value_segment = Column(String(50), nullable=True, comment="价值分段")
    risk_level = Column(String(50), nullable=True, comment="风险等级")
    
    # 关联关系
    customer = relationship("Customer", lazy="select")
    
    def __repr__(self):
        return f"<CustomerValueAnalysis(id={self.id}, customer_id={self.customer_id})>"


class FollowUp(BaseModel):
    """跟进任务模型
    
    对应数据库中的follow_ups表
    """
    __tablename__ = "follow_ups"
    
    # 基本信息
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    assigned_to = Column(String(36), ForeignKey('users.id'), nullable=False, comment="负责人ID")
    created_by = Column(String(36), ForeignKey('users.id'), nullable=False, comment="创建人ID")
    
    # 任务信息
    title = Column(String(200), nullable=False, comment="任务标题")
    description = Column(Text, nullable=True, comment="任务描述")
    priority = Column(String(20), default='medium', nullable=False, comment="优先级")
    
    # 时间信息
    due_date = Column(DateTime, nullable=False, comment="截止时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    
    # 状态信息
    is_completed = Column(Boolean, default=False, nullable=False, comment="是否完成")
    is_overdue = Column(Boolean, default=False, nullable=False, comment="是否逾期")
    
    # 结果信息
    result = Column(Text, nullable=True, comment="跟进结果")
    next_action = Column(Text, nullable=True, comment="下一步行动")
    
    # 关联关系
    customer = relationship("Customer", lazy="select")
    assigned_user = relationship("User", foreign_keys=[assigned_to], lazy="select")
    creator = relationship("User", foreign_keys=[created_by], lazy="select")
    
    def __repr__(self):
        return f"<FollowUp(id={self.id}, title={self.title})>"
    
    @property
    def priority_display(self):
        """优先级显示名称"""
        priority_map = {
            'low': '低',
            'medium': '中',
            'high': '高',
            'urgent': '紧急'
        }
        return priority_map.get(self.priority, self.priority)