# 数据库迁移与客户管理重构 - 设计文档

## 1. 整体架构图

### 1.1 迁移前架构

```mermaid
graph TD
    A[Vue 3 Frontend] --> B[Express.js Backend]
    B --> C[Supabase PostgreSQL]
    B --> D[SQLite Local DB]
    
    subgraph "Frontend Layer"
        A
        A1[CustomerList.vue]
        A2[OptionsManagement.vue]
    end
    
    subgraph "Backend Layer"
        B
        B1[auth.ts]
        B2[options.ts]
        B3[optionsManagement.ts]
    end
    
    subgraph "Data Layer"
        C
        D
    end
```

### 1.2 迁移后架构

```mermaid
graph TD
    A[Vue 3 Frontend] --> B[Express.js Backend]
    B --> E[MySQL Local DB]
    
    subgraph "Frontend Layer"
        A
        A1[OptionsManagement.vue]
        A2[CustomerManagement.vue - 新增标签页]
        A3[OptionCategoriesManagement.vue]
        A4[OptionItemsManagement.vue]
    end
    
    subgraph "Backend Layer"
        B
        B1[auth.ts]
        B2[options.ts]
        B3[optionsManagement.ts]
        B4[customer.ts - 新增]
    end
    
    subgraph "Data Layer"
        E
        E1[option_categories]
        E2[option_items]
        E3[customers]
        E4[users]
        E5[follow_records]
    end
```

## 2. 分层设计和核心组件

### 2.1 数据库层设计

#### 2.1.1 MySQL 数据库管理器

```typescript
// src/database/MySQLManager.ts
import mysql from 'mysql2/promise'

interface MySQLConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
  charset: string
  timezone: string
}

class MySQLManager {
  private pool: mysql.Pool
  private config: MySQLConfig

  constructor(config: MySQLConfig)
  async connect(): Promise<void>
  async disconnect(): Promise<void>
  async query<T>(sql: string, params?: any[]): Promise<T[]>
  async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T>
  async initTables(): Promise<void>
}
```

#### 2.1.2 数据表结构设计

```sql
-- 客户表 (MySQL 版本)
CREATE TABLE customers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(100),
  company VARCHAR(200),
  position VARCHAR(100),
  wechat VARCHAR(100),
  address VARCHAR(500),
  source ENUM('online', 'referral', 'event', 'telemarketing', 'store', 'other') DEFAULT 'other',
  status ENUM('potential', 'interested', 'deal', 'lost') DEFAULT 'potential',
  level ENUM('A', 'B', 'C', 'D') DEFAULT 'C',
  birthday DATE,
  gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown',
  is_vip TINYINT(1) DEFAULT 0,
  is_high_value TINYINT(1) DEFAULT 0,
  tags JSON,
  remark TEXT,
  owner_id BIGINT NOT NULL,
  team_id BIGINT,
  collaborators JSON,
  is_in_pool TINYINT(1) DEFAULT 0,
  pool_time DATETIME,
  last_follow_time DATETIME,
  next_follow_time DATETIME,
  follow_count INT DEFAULT 0,
  deal_amount DECIMAL(10,2) DEFAULT 0.00,
  deal_time DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_owner_id (owner_id),
  INDEX idx_team_id (team_id),
  INDEX idx_source (source),
  INDEX idx_status (status),
  INDEX idx_level (level),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (owner_id) REFERENCES users(id),
  FOREIGN KEY (team_id) REFERENCES departments(id)
);
```

### 2.2 API 层设计

#### 2.2.1 客户管理 API 接口

```typescript
// api/routes/customer.ts
import express from 'express'
import { MySQLManager } from '../database/MySQLManager'
import { ApiResponse, PaginationParams } from '../types'

interface CustomerFilters extends PaginationParams {
  name?: string
  phone?: string
  source?: string
  status?: string
  level?: string
  isImportant?: boolean
  dateRange?: [string, string]
}

interface CustomerData {
  name: string
  phone?: string
  email?: string
  company?: string
  source?: string
  status?: string
  level?: string
  // ... 其他字段
}

// API 端点定义
GET    /api/customers              // 获取客户列表
POST   /api/customers              // 创建客户
GET    /api/customers/:id          // 获取客户详情
PUT    /api/customers/:id          // 更新客户
DELETE /api/customers/:id          // 删除客户
POST   /api/customers/batch-delete // 批量删除客户
POST   /api/customers/batch-update // 批量更新客户
GET    /api/customers/export       // 导出客户
POST   /api/customers/import       // 导入客户
```

#### 2.2.2 API 响应格式

```typescript
// 统一响应格式
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  total?: number
  message?: string
  error?: string
}

// 分页响应格式
interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}
```

### 2.3 前端层设计

#### 2.3.1 选项管理页面重构

```vue
<!-- views/settings/OptionsManagement.vue -->
<template>
  <div class="options-management">
    <div class="page-header">
      <h1 class="page-title">选项管理</h1>
      <p class="page-description">管理系统选项分类、选项数据和客户信息配置</p>
    </div>

    <n-card class="options-card">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="categories" tab="选项分类管理">
          <OptionCategoriesManagement />
        </n-tab-pane>
        <n-tab-pane name="items" tab="选项数据管理">
          <OptionItemsManagement />
        </n-tab-pane>
        <n-tab-pane name="customers" tab="客户管理">
          <CustomerManagement />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const activeTab = ref('categories')

// 支持 URL 参数切换标签页
onMounted(() => {
  if (route.query.tab) {
    activeTab.value = route.query.tab as string
  }
})

// 监听标签页切换，更新 URL
watch(activeTab, (newTab) => {
  router.replace({ query: { ...route.query, tab: newTab } })
})
</script>
```

#### 2.3.2 客户管理组件设计

```vue
<!-- views/settings/components/CustomerManagement.vue -->
<template>
  <div class="customer-management">
    <!-- 工具栏 -->
    <div class="toolbar">
      <n-space>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon><AddIcon /></template>
          新增客户
        </n-button>
        <n-button @click="handleImport">
          <template #icon><ImportIcon /></template>
          导入客户
        </n-button>
        <n-button @click="handleExport">
          <template #icon><ExportIcon /></template>
          导出客户
        </n-button>
      </n-space>
    </div>

    <!-- 筛选器 -->
    <CustomerFilters 
      v-model:filters="filters"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 客户表格 -->
    <CustomerTable
      :data="customerStore.customers"
      :loading="customerStore.loading"
      :pagination="pagination"
      :checked-row-keys="checkedRowKeys"
      @update:checked-row-keys="handleCheck"
      @update:page="handlePageChange"
      @edit="handleEdit"
      @delete="handleDelete"
    />

    <!-- 批量操作 -->
    <BatchActions
      v-if="checkedRowKeys.length > 0"
      :selected-count="checkedRowKeys.length"
      @batch-assign="handleBatchAssign"
      @batch-delete="handleBatchDelete"
      @move-to-pool="handleMoveToPool"
    />

    <!-- 客户表单弹窗 -->
    <CustomerFormModal
      v-model:show="showCreateModal"
      :customer="currentCustomer"
      :is-edit="!!currentCustomer"
      @submit="handleModalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useCustomerStore } from '@/stores/customerStore'
import CustomerFilters from './CustomerFilters.vue'
import CustomerTable from './CustomerTable.vue'
import BatchActions from './BatchActions.vue'
import CustomerFormModal from './CustomerFormModal.vue'

// 组件逻辑复用现有的 CustomerList.vue 逻辑
// 但移除页面头部和导航相关代码
</script>
```

#### 2.3.3 组件拆分设计

```typescript
// 组件结构
CustomerManagement.vue              // 主容器组件
├── CustomerFilters.vue             // 筛选器组件
├── CustomerTable.vue               // 表格组件
├── BatchActions.vue                // 批量操作组件
├── CustomerFormModal.vue           // 客户表单弹窗
└── components/
    ├── CustomerStatusTag.vue       // 客户状态标签
    ├── CustomerLevelTag.vue        // 客户等级标签
    └── CustomerActions.vue         // 行操作按钮
```

## 3. 模块依赖关系图

```mermaid
graph TD
    A[OptionsManagement.vue] --> B[CustomerManagement.vue]
    A --> C[OptionCategoriesManagement.vue]
    A --> D[OptionItemsManagement.vue]
    
    B --> E[CustomerFilters.vue]
    B --> F[CustomerTable.vue]
    B --> G[BatchActions.vue]
    B --> H[CustomerFormModal.vue]
    
    B --> I[customerStore.ts]
    I --> J[customerService.ts]
    J --> K[customer.ts API]
    K --> L[MySQLManager.ts]
    
    F --> M[CustomerStatusTag.vue]
    F --> N[CustomerLevelTag.vue]
    F --> O[CustomerActions.vue]
    
    subgraph "Shared Components"
        P[PageHeader.vue]
        Q[OrganizationSelectModal.vue]
        R[FollowProcessActions.vue]
    end
    
    B --> P
    G --> Q
    O --> R
```

## 4. 接口契约定义

### 4.1 数据库接口

```typescript
// 数据库操作接口
interface ICustomerRepository {
  findAll(filters: CustomerFilters): Promise<PaginationResponse<Customer>>
  findById(id: number): Promise<Customer | null>
  create(data: CreateCustomerData): Promise<Customer>
  update(id: number, data: UpdateCustomerData): Promise<Customer>
  delete(id: number): Promise<void>
  batchDelete(ids: number[]): Promise<void>
  batchUpdate(ids: number[], data: Partial<UpdateCustomerData>): Promise<void>
}
```

### 4.2 API 接口

```typescript
// API 服务接口
interface ICustomerService {
  getCustomers(filters: CustomerFilters): Promise<ApiResponse<PaginationResponse<Customer>>>
  getCustomer(id: number): Promise<ApiResponse<Customer>>
  createCustomer(data: CreateCustomerData): Promise<ApiResponse<Customer>>
  updateCustomer(id: number, data: UpdateCustomerData): Promise<ApiResponse<Customer>>
  deleteCustomer(id: number): Promise<ApiResponse<void>>
  batchDeleteCustomers(ids: number[]): Promise<ApiResponse<void>>
  exportCustomers(filters: CustomerFilters): Promise<Blob>
  importCustomers(file: File): Promise<ApiResponse<ImportResult>>
}
```

### 4.3 前端组件接口

```typescript
// 组件 Props 接口
interface CustomerTableProps {
  data: Customer[]
  loading: boolean
  pagination: PaginationConfig
  checkedRowKeys: number[]
  embeddedMode?: boolean  // 嵌入模式，隐藏某些功能
}

interface CustomerFiltersProps {
  filters: CustomerFilters
  options: {
    sources: SelectOption[]
    statuses: SelectOption[]
    levels: SelectOption[]
  }
}

// 组件 Emits 接口
interface CustomerTableEmits {
  'update:checked-row-keys': [keys: number[]]
  'update:page': [page: number]
  'edit': [customer: Customer]
  'delete': [customer: Customer]
}
```

## 5. 数据流向图

```mermaid
sequenceDiagram
    participant U as User
    participant C as CustomerManagement.vue
    participant S as customerStore
    participant A as customerService
    participant API as customer.ts
    participant DB as MySQLManager
    
    U->>C: 访问客户管理标签页
    C->>S: 调用 fetchCustomers()
    S->>A: 调用 getCustomers()
    A->>API: HTTP GET /api/customers
    API->>DB: 执行 SQL 查询
    DB-->>API: 返回查询结果
    API-->>A: 返回 ApiResponse
    A-->>S: 返回客户数据
    S-->>C: 更新 customers 状态
    C-->>U: 渲染客户列表
    
    U->>C: 创建新客户
    C->>S: 调用 createCustomer()
    S->>A: 调用 createCustomer()
    A->>API: HTTP POST /api/customers
    API->>DB: 执行 INSERT 语句
    DB-->>API: 返回插入结果
    API-->>A: 返回新客户数据
    A-->>S: 返回客户数据
    S-->>C: 更新客户列表
    C-->>U: 显示成功提示
```

## 6. 异常处理策略

### 6.1 数据库异常处理

```typescript
// 数据库连接异常
try {
  await mysqlManager.connect()
} catch (error) {
  logger.error('MySQL 连接失败:', error)
  throw new DatabaseConnectionError('数据库连接失败')
}

// 查询异常处理
try {
  const result = await mysqlManager.query(sql, params)
  return result
} catch (error) {
  logger.error('数据库查询失败:', error)
  if (error.code === 'ER_DUP_ENTRY') {
    throw new DuplicateEntryError('数据已存在')
  }
  throw new DatabaseQueryError('查询失败')
}
```

### 6.2 API 异常处理

```typescript
// API 路由异常处理
app.use('/api/customers', (req, res, next) => {
  try {
    // 路由处理逻辑
  } catch (error) {
    logger.error('API 处理异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      message: error.message
    })
  }
})
```

### 6.3 前端异常处理

```typescript
// 前端异常处理
const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    message.error('登录已过期，请重新登录')
    router.push('/login')
  } else if (error.response?.status === 403) {
    message.error('权限不足')
  } else if (error.response?.status >= 500) {
    message.error('服务器错误，请稍后重试')
  } else {
    message.error(error.response?.data?.message || '操作失败')
  }
}
```

## 7. 性能优化设计

### 7.1 数据库优化

* **索引优化**: 为常用查询字段添加索引

* **连接池**: 配置合适的连接池大小

* **查询优化**: 使用分页查询，避免全表扫描

* **缓存策略**: 对不经常变化的数据进行缓存

### 7.2 前端优化

* **组件懒加载**: 标签页组件按需加载

* **虚拟滚动**: 大数据量表格使用虚拟滚动

* **防抖搜索**: 搜索输入使用防抖处理

* **状态缓存**: 合理使用 Pinia 状态缓存

### 7.3 API 优化

* **响应压缩**: 启用 gzip 压缩

* **请求合并**: 批量操作接口减少请求次数

* **错误重试**: 网络错误自动重试机制

* **超时控制**: 设置合理的请求超时时间

