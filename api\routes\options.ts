import express, { type Request, type Response } from 'express';
import { MySQLManager } from '../../src/database/MySQLManager';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(process.cwd(), 'api', '.env') });

const router = express.Router();

// 调试环境变量
console.log('数据库配置调试:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
console.log('DB_NAME:', process.env.DB_NAME);

// 创建MySQL管理器实例
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'workchat_admin'
};

console.log('MySQL配置调试:');
console.log('host:', mysqlConfig.host);
console.log('port:', mysqlConfig.port);
console.log('user:', mysqlConfig.user);
console.log('password:', mysqlConfig.password ? '***' : 'undefined');
console.log('database:', mysqlConfig.database);

const mysqlManager = new MySQLManager(mysqlConfig);

// 初始化MySQL连接
mysqlManager.initialize().catch(error => {
  console.error('MySQL初始化失败:', error);
});

// 定义接口类型
interface OptionCategory {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

interface OptionItem {
  id: string;
  category_id: string;
  value: string;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * 获取所有选项分类
 * GET /api/options/categories
 */
router.get('/categories', async (req: Request, res: Response): Promise<void> => {
  try {
    const query = `
      SELECT * FROM option_categories 
      WHERE is_active = true 
      ORDER BY sort_order ASC
    `;
    
    const data = await mysqlManager.query(query);

    res.json({
      success: true,
      data: data || []
    } as ApiResponse<OptionCategory[]>);
  } catch (error) {
    console.error('获取选项分类失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    } as ApiResponse);
  }
});

/**
 * 根据分类获取选项项
 * GET /api/options/items/:categoryId
 */
router.get('/items/:categoryId', async (req: Request, res: Response): Promise<void> => {
  try {
    const { categoryId } = req.params;
    
    const query = `
      SELECT * FROM option_items 
      WHERE category_id = ? AND is_active = true 
      ORDER BY sort_order ASC
    `;
    
    const data = await mysqlManager.query(query, [categoryId]);

    res.json({
      success: true,
      data: data || []
    } as ApiResponse<OptionItem[]>);
  } catch (error) {
    console.error('获取选项项失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    } as ApiResponse);
  }
});

/**
 * 获取所有选项数据（包含分类和选项项）
 * GET /api/options/all
 */
router.get('/all', async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取所有分类
    const categoriesQuery = `
      SELECT * FROM option_categories 
      WHERE is_active = true 
      ORDER BY sort_order ASC
    `;
    const categories = await mysqlManager.query(categoriesQuery);

    // 获取所有选项项
    const itemsQuery = `
      SELECT * FROM option_items 
      WHERE is_active = true 
      ORDER BY sort_order ASC
    `;
    const items = await mysqlManager.query(itemsQuery);

    // 组织数据结构
    const result = (categories || []).map(category => ({
      ...category,
      items: (items || []).filter(item => item.category_id === category.id)
    }));

    res.json({
      success: true,
      data: result
    } as ApiResponse);
  } catch (error) {
    console.error('获取所有选项数据失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    } as ApiResponse);
  }
});

/**
 * 根据分类名称获取选项项
 * GET /api/options/by-category/:categoryName
 */
router.get('/by-category/:categoryName', async (req: Request, res: Response): Promise<void> => {
  try {
    const { categoryName } = req.params;
    
    // 首先根据分类名称获取分类ID
    const categoryQuery = `
      SELECT id FROM option_categories 
      WHERE code = ? AND is_active = true 
      LIMIT 1
    `;
    const categoryResult = await mysqlManager.query(categoryQuery, [categoryName]);

    if (!categoryResult || categoryResult.length === 0) {
      res.status(404).json({
        success: false,
        error: `分类 '${categoryName}' 不存在`
      } as ApiResponse);
      return;
    }

    const categoryId = categoryResult[0].id;

    // 根据分类ID获取选项项
    const itemsQuery = `
      SELECT * FROM option_items 
      WHERE category_id = ? AND is_active = true 
      ORDER BY sort_order ASC
    `;
    const items = await mysqlManager.query(itemsQuery, [categoryId]);

    res.json({
      success: true,
      data: items || []
    } as ApiResponse<OptionItem[]>);
  } catch (error) {
    console.error('根据分类名称获取选项项失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    } as ApiResponse);
  }
});

export default router;