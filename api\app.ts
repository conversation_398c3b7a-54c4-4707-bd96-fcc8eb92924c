/**
 * This is a API server
 */

import express, { type Request, type Response, type NextFunction }  from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import authRoutes from './routes/auth.js';
import optionsRoutes from './routes/options.js';
import optionsManagementRoutes from './routes/optionsManagement.js';
import customerRoutes from './routes/customer.js';

// for esm mode
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// load env
dotenv.config();


const app: express.Application = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * API Routes
 */
app.use('/api/auth', authRoutes);
app.use('/api/options', optionsRoutes);
app.use('/api/options-management', optionsManagementRoutes);
app.use('/api/customer', customerRoutes);

/**
 * health
 */
app.use('/api/health', (req: Request, res: Response, next: NextFunction): void => {
  res.status(200).json({
    success: true,
    message: 'ok'
  });
});

/**
 * Root path handler - redirect to Vue 3 frontend
 */
app.get('/', (req: Request, res: Response): void => {
  res.redirect('http://localhost:8080');
});

/**
 * Handle other non-API routes - redirect to Vue 3 frontend
 */
app.get('*', (req: Request, res: Response, next: NextFunction): void => {
  // Only redirect non-API routes
  if (!req.path.startsWith('/api/')) {
    res.redirect('http://localhost:8080');
  } else {
    next();
  }
});

/**
 * error handler middleware
 */
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  res.status(500).json({
    success: false,
    error: 'Server internal error'
  });
});

/**
 * 404 handler
 */
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'API not found'
  });
});

export default app;