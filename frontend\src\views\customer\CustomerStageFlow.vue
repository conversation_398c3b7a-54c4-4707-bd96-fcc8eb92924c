<template>
  <div class="customer-stage-flow">
    <!-- 阶段进度条 -->
    <div class="stage-progress">
      <div class="progress-header">
        <h3 class="progress-title">客户跟进流程</h3>
        <p class="progress-description">跟踪客户从初次接触到成功签约的完整流程</p>
      </div>
      <n-steps :current="currentStage" :status="stageStatus" size="large">
        <n-step title="跟进阶段" description="初步接触，了解需求">
          <template #icon>
            <n-icon size="24" color="#1677ff">
              <phone-portrait-outline />
            </n-icon>
          </template>
        </n-step>
        <n-step title="见面阶段" description="深入沟通，量房设计">
          <template #icon>
            <n-icon size="24" color="#52c41a">
              <people-outline />
            </n-icon>
          </template>
        </n-step>
        <n-step title="成交阶段" description="签约合作，项目启动">
          <template #icon>
            <n-icon size="24" color="#faad14">
              <checkmark-circle-outline />
            </n-icon>
          </template>
        </n-step>
      </n-steps>
      
      <!-- 阶段统计 -->
      <div class="stage-stats">
        <div class="stat-item">
          <div class="stat-number">{{ followRecords.length }}</div>
          <div class="stat-label">跟进记录</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ meetingRecords.length }}</div>
          <div class="stat-label">见面记录</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ dealRecords.length }}</div>
          <div class="stat-label">成交记录</div>
        </div>
      </div>
    </div>

    <!-- 阶段内容区域 -->
    <div class="stage-content">
      <!-- 跟进阶段 -->
      <n-card class="stage-card follow-stage" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <n-icon size="20" color="#1677ff">
                <phone-portrait-outline />
              </n-icon>
              <span class="card-title">跟进记录</span>
              <n-tag v-if="followRecords.length > 0" type="info" size="small">
                {{ followRecords.length }} 条记录
              </n-tag>
            </div>
            <n-button type="primary" size="medium" @click="handleAddFollow">
              <template #icon>
                <n-icon><add-outline /></n-icon>
              </template>
              新增跟进
            </n-button>
          </div>
        </template>
        
        <div class="records-container">
          <div v-if="followRecords.length === 0" class="empty-state">
            <n-empty description="暂无跟进记录，开始第一次客户跟进吧！">
              <template #icon>
                <n-icon size="48" color="#d9d9d9">
                  <phone-portrait-outline />
                </n-icon>
              </template>
              <template #extra>
                <n-button type="primary" @click="handleAddFollow">
                  立即添加
                </n-button>
              </template>
            </n-empty>
          </div>
          <div v-else class="records-list">
            <div v-for="(record, index) in followRecords" :key="record.id" class="record-item">
              <div class="record-timeline">
                <div class="timeline-dot" :class="getFollowStatusClass(record.status)"></div>
                <div v-if="index < followRecords.length - 1" class="timeline-line"></div>
              </div>
              <div class="record-card">
                <div class="record-header">
                  <div class="record-info">
                    <span class="record-time">{{ record.follow_time || '未设置' }}</span>
                    <n-tag :type="getFollowStatusType(record.status)" size="small">
                      {{ getFollowStatusText(record.status) }}
                    </n-tag>
                    <n-tag type="default" size="small">
                      {{ record.type || '跟进' }}
                    </n-tag>
                  </div>
                  <div class="record-actions">
                    <n-button text size="small" @click="handleEditFollow(record)">
                      <template #icon><n-icon><edit-outline /></n-icon></template>
                      编辑
                    </n-button>
                    <n-button text size="small" type="error" @click="handleDeleteFollow(record.id)">
                      <template #icon><n-icon><trash-outline /></n-icon></template>
                      删除
                    </n-button>
                  </div>
                </div>
                <div class="record-content">
                  <div class="content-row">
                    <span class="content-label">客户反馈：</span>
                    <span class="content-value">{{ record.content || '暂无' }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">跟进内容：</span>
                    <span class="content-value">{{ record.content || '暂无' }}</span>
                  </div>
                  <div v-if="record.next_follow_time" class="content-row">
                    <span class="content-label">下次跟进：</span>
                    <span class="content-value next-follow">{{ record.next_follow_time }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 见面阶段 -->
      <n-card class="stage-card meeting-stage" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <n-icon size="20" color="#52c41a">
                <people-outline />
              </n-icon>
              <span class="card-title">见面记录</span>
              <n-tag v-if="meetingRecords.length > 0" type="success" size="small">
                {{ meetingRecords.length }} 条记录
              </n-tag>
            </div>
            <n-button type="primary" size="medium" @click="handleAddMeeting">
              <template #icon>
                <n-icon><add-outline /></n-icon>
              </template>
              新增见面
            </n-button>
          </div>
        </template>
        
        <div class="records-container">
          <div v-if="meetingRecords.length === 0" class="empty-state">
            <n-empty description="暂无见面记录，安排与客户的第一次见面吧！">
              <template #icon>
                <n-icon size="48" color="#d9d9d9">
                  <people-outline />
                </n-icon>
              </template>
              <template #extra>
                <n-button type="primary" @click="handleAddMeeting">
                  立即添加
                </n-button>
              </template>
            </n-empty>
          </div>
          <div v-else class="records-list">
            <div v-for="(record, index) in meetingRecords" :key="record.id" class="record-item">
              <div class="record-timeline">
                <div class="timeline-dot" :class="record.status === 'completed' ? 'success' : 'default'"></div>
                <div v-if="index < meetingRecords.length - 1" class="timeline-line"></div>
              </div>
              <div class="record-card">
                <div class="record-header">
                  <div class="record-info">
                    <span class="record-time">{{ record.meetingTime ? formatDate(record.meetingTime) : '未设置' }}</span>
                    <n-tag :type="record.status === 'completed' ? 'success' : 'default'" size="small">
                      {{ record.status === 'completed' ? '已完成' : '进行中' }}
                    </n-tag>
                    <n-tag type="default" size="small">
                      {{ record.type || '会议' }}
                    </n-tag>
                  </div>
                  <div class="record-actions">
                    <n-button text size="small" @click="handleEditMeeting(record)">
                      <template #icon><n-icon><edit-outline /></n-icon></template>
                      编辑
                    </n-button>
                    <n-button text size="small" type="error" @click="handleDeleteMeeting(record.id)">
                      <template #icon><n-icon><trash-outline /></n-icon></template>
                      删除
                    </n-button>
                  </div>
                </div>
                <div class="record-content">
                  <div class="content-row">
                    <span class="content-label">见面地点：</span>
                    <span class="content-value">{{ record.location || '暂无' }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">参与人员：</span>
                    <span class="content-value">{{ record.participants?.join(', ') || '暂无' }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">见面内容：</span>
                    <span class="content-value">{{ record.content || '暂无' }}</span>
                  </div>
                  <div v-if="record.duration" class="content-row">
                    <span class="content-label">会议时长：</span>
                    <span class="content-value measure-time">{{ record.duration }}分钟</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 成交阶段 -->
      <n-card class="stage-card deal-stage" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <n-icon size="20" color="#faad14">
                <checkmark-circle-outline />
              </n-icon>
              <span class="card-title">成交记录</span>
              <n-tag v-if="dealRecords.length > 0" type="warning" size="small">
                {{ dealRecords.length }} 条记录
              </n-tag>
            </div>
            <n-button type="primary" size="medium" @click="handleAddDeal">
              <template #icon>
                <n-icon><add-outline /></n-icon>
              </template>
              新增成交
            </n-button>
          </div>
        </template>
        
        <div class="records-container">
          <div v-if="dealRecords.length === 0" class="empty-state">
            <n-empty description="暂无成交记录，记录客户的成功签约信息！">
              <template #icon>
                <n-icon size="48" color="#d9d9d9">
                  <checkmark-circle-outline />
                </n-icon>
              </template>
              <template #extra>
                <n-button type="primary" @click="handleAddDeal">
                  立即添加
                </n-button>
              </template>
            </n-empty>
          </div>
          <div v-else class="records-list">
            <div v-for="(record, index) in dealRecords" :key="record.id" class="record-item">
              <div class="record-timeline">
                <div class="timeline-dot success"></div>
                <div v-if="index < dealRecords.length - 1" class="timeline-line"></div>
              </div>
              <div class="record-card deal-card">
                <div class="record-header">
                  <div class="record-info">
                    <span class="record-time">{{ record.contractTime ? formatDate(record.contractTime) : '未设置' }}</span>
                    <n-tag type="success" size="small">已成交</n-tag>
                    <n-tag type="warning" size="small">
                      ¥{{ record.contractAmount?.toLocaleString() }}
                    </n-tag>
                  </div>
                  <div class="record-actions">
                    <n-button text size="small" @click="handleEditDeal(record)">
                      <template #icon><n-icon><edit-outline /></n-icon></template>
                      编辑
                    </n-button>
                    <n-button text size="small" type="error" @click="handleDeleteDeal(record.id)">
                      <template #icon><n-icon><trash-outline /></n-icon></template>
                      删除
                    </n-button>
                  </div>
                </div>
                <div class="record-content">
                  <div class="content-row">
                    <span class="content-label">签单套餐：</span>
                    <span class="content-value">{{ record.packageType || '暂无' }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">付款方式：</span>
                    <span class="content-value">{{ getPaymentMethodText(record.paymentMethod) }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">设计师：</span>
                    <span class="content-value">{{ record.designer || '暂无' }}</span>
                  </div>
                  <div class="content-row">
                    <span class="content-label">销售顾问：</span>
                    <span class="content-value">{{ record.sales || '暂无' }}</span>
                  </div>
                  <div v-if="record.contractTime" class="content-row">
              <span class="content-label">合同时间：</span>
              <span class="content-value start-time">{{ formatDate(record.contractTime) }}</span>
            </div>
            <div v-if="record.projectDuration" class="content-row">
              <span class="content-label">项目工期：</span>
              <span class="content-value end-time">{{ record.projectDuration }}天</span>
            </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 跟进记录表单模态框 -->
    <n-modal v-model:show="showFollowModal" preset="card" style="width: 600px">
      <template #header>
        <div class="modal-header">
          <n-icon size="18" color="#1677ff">
            <phone-portrait-outline />
          </n-icon>
          <span>{{ editFollowRecord?.id ? '编辑' : '新增' }}跟进记录</span>
        </div>
      </template>
      <FollowRecordForm
        ref="followFormRef"
        :model-value="editFollowRecord"
        :is-edit="!!editFollowRecord?.id"
        @update:model-value="editFollowRecord = $event"
      />
      <template #footer>
        <div class="modal-footer">
          <n-button @click="showFollowModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleSubmitFollow">
            {{ editFollowRecord?.id ? '更新' : '保存' }}
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 见面记录表单模态框 -->
    <n-modal v-model:show="showMeetingModal" preset="card" style="width: 700px">
      <template #header>
        <div class="modal-header">
          <n-icon size="18" color="#52c41a">
            <people-outline />
          </n-icon>
          <span>{{ editMeetingRecord?.id ? '编辑' : '新增' }}见面记录</span>
        </div>
      </template>
      <MeetingRecordForm
        ref="meetingFormRef"
        :model-value="editMeetingRecord"
        :is-edit="!!editMeetingRecord?.id"
        @update:model-value="editMeetingRecord = $event"
      />
      <template #footer>
        <div class="modal-footer">
          <n-button @click="showMeetingModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleSubmitMeeting">
            {{ editMeetingRecord?.id ? '更新' : '保存' }}
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 成交记录表单模态框 -->
    <n-modal v-model:show="showDealModal" preset="card" style="width: 700px">
      <template #header>
        <div class="modal-header">
          <n-icon size="18" color="#faad14">
            <checkmark-circle-outline />
          </n-icon>
          <span>{{ editDealRecord?.id ? '编辑' : '新增' }}成交记录</span>
        </div>
      </template>
      <DealRecordForm
        ref="dealFormRef"
        :model-value="editDealRecord"
        :is-edit="!!editDealRecord?.id"
        @update:model-value="editDealRecord = $event"
      />
      <template #footer>
        <div class="modal-footer">
          <n-button @click="showDealModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleSubmitDeal">
            {{ editDealRecord?.id ? '更新' : '保存' }}
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  AddOutline,
  CreateOutline as EditOutline,
  TrashOutline,
  PhonePortraitOutline,
  PeopleOutline,
  CheckmarkCircleOutline
} from '@vicons/ionicons5'
import FollowRecordForm from './components/FollowRecordForm.vue'
import MeetingRecordForm from './components/MeetingRecordForm.vue'
import DealRecordForm from './components/DealRecordForm.vue'
import type { DealRecord, FollowRecord, MeetingRecord } from '@/types'

interface Props {
  customerId: string
}

const props = defineProps<Props>()
const message = useMessage()

// 响应式数据
const followRecords = ref<FollowRecord[]>([])
const meetingRecords = ref<MeetingRecord[]>([])
const dealRecords = ref<DealRecord[]>([])

// 模态框状态
const showFollowModal = ref(false)
const showMeetingModal = ref(false)
const showDealModal = ref(false)
const formLoading = ref(false)

// 编辑数据
const editFollowRecord = ref<FollowRecord | null>(null)
const editMeetingRecord = ref<MeetingRecord | null>(null)
const editDealRecord = ref<DealRecord | null>(null)

// 表单引用
const followFormRef = ref()
const meetingFormRef = ref()
const dealFormRef = ref()

// 计算当前阶段
const currentStage = computed(() => {
  if (dealRecords.value.length > 0) return 3
  if (meetingRecords.value.length > 0) return 2
  if (followRecords.value.length > 0) return 1
  return 1
})

const stageStatus = computed(() => {
  if (dealRecords.value.length > 0) return 'finish'
  return 'process'
})

// 跟进记录处理
const handleAddFollow = () => {
  editFollowRecord.value = {
    id: 0,
    customer_id: parseInt(props.customerId),
    customer_name: '',
    type: 'phone',
    follow_time: new Date().toISOString(),
    content: '',
    status: 'pending',
    created_by: 0,
    created_by_name: '当前用户',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    stage: 'follow'
  }
  showFollowModal.value = true
}

const handleEditFollow = (record: FollowRecord) => {
  editFollowRecord.value = { ...record }
  showFollowModal.value = true
}

const handleDeleteFollow = (id?: number) => {
  if (!id) return
  const index = followRecords.value.findIndex(r => r.id === id)
  if (index > -1) {
    followRecords.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleSubmitFollow = async () => {
  try {
    formLoading.value = true
    await followFormRef.value?.validate()
    
    if (editFollowRecord.value?.id) {
      // 更新
      const index = followRecords.value.findIndex(r => r.id === editFollowRecord.value?.id)
      if (index > -1) {
        followRecords.value[index] = { ...editFollowRecord.value }
      }
      message.success('更新成功')
    } else {
      // 新增
      const newRecord = {
        ...editFollowRecord.value!,
        id: Date.now()
      }
      followRecords.value.push(newRecord)
      message.success('添加成功')
    }
    
    showFollowModal.value = false
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    formLoading.value = false
  }
}

// 见面记录处理
const handleAddMeeting = () => {
  editMeetingRecord.value = {
    id: 0,
    title: '',
    customer_id: parseInt(props.customerId),
    customer_name: '',
    meeting_time: Date.now(),
    duration: 0,
    location: '',
    participants: [],
    content: '',
    status: 'pending',
    created_by: 0,
    created_by_name: '当前用户',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showMeetingModal.value = true
}

const handleEditMeeting = (record: MeetingRecord) => {
  editMeetingRecord.value = { ...record }
  showMeetingModal.value = true
}

const handleDeleteMeeting = (id?: number) => {
  if (!id) return
  const index = meetingRecords.value.findIndex(r => r.id === id)
  if (index > -1) {
    meetingRecords.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleSubmitMeeting = async () => {
  try {
    formLoading.value = true
    await meetingFormRef.value?.validate()
    
    if (editMeetingRecord.value?.id) {
      // 更新
      const index = meetingRecords.value.findIndex(r => r.id === editMeetingRecord.value?.id)
      if (index > -1) {
        meetingRecords.value[index] = { ...editMeetingRecord.value }
      }
      message.success('更新成功')
    } else {
      // 新增
      const newRecord = {
        ...editMeetingRecord.value!,
        id: Date.now()
      }
      meetingRecords.value.push(newRecord)
      message.success('添加成功')
    }
    
    showMeetingModal.value = false
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    formLoading.value = false
  }
}

// 成交记录处理
const handleAddDeal = () => {
  editDealRecord.value = {
    id: 0,
    customer_id: parseInt(props.customerId),
    customer_name: '',
    packageType: '',
    contractAmount: 0,
    paidAmount: 0,
    remainingAmount: 0,
    paymentMethod: 'installment',
    contractTime: Date.now(),
    projectDuration: 0,
    designer: '',
    designerId: 0,
    sales: '',
    salesId: 0,
    contractNo: '',
    status: 'pending',
    remark: '',
    attachments: [],
    created_by: 0,
    created_by_name: '当前用户',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showDealModal.value = true
}

const handleEditDeal = (record: DealRecord) => {
  editDealRecord.value = { ...record }
  showDealModal.value = true
}

const handleDeleteDeal = (id?: number) => {
  if (!id) return
  const index = dealRecords.value.findIndex(r => r.id === id)
  if (index > -1) {
    dealRecords.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleSubmitDeal = async () => {
  try {
    formLoading.value = true
    await dealFormRef.value?.validate()
    
    if (editDealRecord.value?.id) {
      // 更新
      const index = dealRecords.value.findIndex(r => r.id === editDealRecord.value?.id)
      if (index > -1) {
        dealRecords.value[index] = { ...editDealRecord.value }
      }
      message.success('更新成功')
    } else {
      // 新增
      const newRecord = {
        ...editDealRecord.value!,
        id: Date.now()
      }
      dealRecords.value.push(newRecord)
      message.success('添加成功')
    }
    
    showDealModal.value = false
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    formLoading.value = false
  }
}

// 工具函数
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getFollowStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    completed: 'success',
    failed: 'error'
  }
  return types[status] || 'default'
}

const getFollowStatusClass = (status: string) => {
  const classes: Record<string, string> = {
    pending: 'warning',
    completed: 'success',
    failed: 'error'
  }
  return classes[status] || 'default'
}

const getFollowStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待跟进',
    completed: '已完成',
    failed: '跟进失败'
  }
  return texts[status] || status
}



const getPaymentMethodText = (method: string) => {
  const texts: Record<string, string> = {
    full_payment: '全款',
    installment: '分期付款',
    loan: '贷款'
  }
  return texts[method] || method
}

// 加载数据
const loadData = async () => {
  // TODO: 从API加载数据
  // 这里使用模拟数据
  followRecords.value = []
  meetingRecords.value = []
  dealRecords.value = []
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.customer-stage-flow {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.stage-progress {
  background: white;
  padding: 32px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-header {
  text-align: center;
  margin-bottom: 32px;
}

.progress-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.progress-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stage-stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #1677ff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stage-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stage-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.follow-stage {
  border-left: 4px solid #1677ff;
}

.meeting-stage {
  border-left: 4px solid #52c41a;
}

.deal-stage {
  border-left: 4px solid #faad14;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.records-container {
  min-height: 200px;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.records-list {
  max-height: 600px;
  overflow-y: auto;
  padding: 8px 0;
}

.record-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.record-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  position: relative;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #d9d9d9;
  background: #d9d9d9;
  z-index: 2;
}

.timeline-dot.success {
  background: #52c41a;
  box-shadow: 0 0 0 2px #52c41a;
}

.timeline-dot.warning {
  background: #faad14;
  box-shadow: 0 0 0 2px #faad14;
}

.timeline-dot.error {
  background: #ff4d4f;
  box-shadow: 0 0 0 2px #ff4d4f;
}

.timeline-line {
  width: 2px;
  height: 40px;
  background: #e8e8e8;
  margin-top: 8px;
}

.record-card {
  flex: 1;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.record-card:hover {
  border-color: #1677ff;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);
}

.deal-card {
  background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
  border-color: #faad14;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.record-time {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.content-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  line-height: 1.6;
}

.content-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
  font-size: 13px;
}

.content-value {
  color: #1a1a1a;
  flex: 1;
  font-size: 13px;
}

.content-value.next-follow {
  color: #1677ff;
  font-weight: 500;
}

.content-value.budget {
  color: #faad14;
  font-weight: 500;
}

.content-value.measure-time,
.content-value.start-time,
.content-value.end-time {
  color: #52c41a;
  font-weight: 500;
}

/* 模态框样式 */
.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1a1a1a;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stage-stats {
    gap: 32px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .customer-stage-flow {
    padding: 0;
  }
  
  .stage-progress {
    padding: 24px 16px;
    margin-bottom: 16px;
  }
  
  .progress-title {
    font-size: 20px;
  }
  
  .stage-stats {
    gap: 24px;
    flex-wrap: wrap;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stage-content {
    gap: 16px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .header-left {
    width: 100%;
  }
  
  .record-card {
    padding: 16px;
  }
  
  .record-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .record-info {
    width: 100%;
  }
  
  .content-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .content-label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stage-progress {
    padding: 16px;
  }
  
  .progress-title {
    font-size: 18px;
  }
  
  .stage-stats {
    gap: 16px;
  }
  
  .stat-number {
    font-size: 18px;
  }
  
  .record-item {
    margin-bottom: 16px;
  }
  
  .record-card {
    padding: 12px;
  }
  
  .record-timeline {
    margin-right: 12px;
  }
  
  .timeline-dot {
    width: 10px;
    height: 10px;
  }
  
  .timeline-line {
    height: 30px;
  }
}
</style>