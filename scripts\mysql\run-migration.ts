#!/usr/bin/env ts-node

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { DataMigrationManager } from '../migrate-to-mysql';
import { DataCleanupManager } from '../cleanup-migration';
import { DataRollbackManager } from '../rollback-migration';
import { DataIntegrityValidator } from '../data-integrity-validator';
import { MigrationLogger, LogLevel } from './migration-logger';
import { configManager, environmentManager, initializeConfig } from './migration-config';
import { MySQLManager } from '../../src/database/MySQLManager';
import fs from 'fs/promises';
import path from 'path';

/**
 * 迁移主程序类
 */
class MigrationRunner {
  private logger: MigrationLogger;
  private mysqlManager: MySQLManager;

  constructor() {
    this.logger = new MigrationLogger();
    this.mysqlManager = new MySQLManager({
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'yysh_crm',
      connectionLimit: 10
    });
  }

  /**
   * 初始化迁移环境
   */
  async initialize(): Promise<void> {
    console.log(chalk.blue('🚀 初始化数据库迁移环境...'));
    
    try {
      // 初始化日志系统
      await this.logger.initialize();
      
      // 初始化配置
      await initializeConfig();
      
      // 创建必要的目录
      await this.createDirectories();
      
      console.log(chalk.green('✅ 迁移环境初始化完成'));
    } catch (error) {
      console.error(chalk.red('❌ 初始化失败:'), error);
      throw error;
    }
  }

  /**
   * 执行完整迁移
   */
  async runFullMigration(): Promise<void> {
    console.log(chalk.blue('🔄 开始执行完整数据迁移...'));
    
    try {
      const config = configManager.getConfig();
      const migrationManager = new DataMigrationManager(config, this.logger);
      
      // 执行迁移前检查
      await this.preflightCheck();
      
      // 创建备份（如果启用）
      if (config.backup.enabled && config.backup.backup_before_migration) {
        await this.createBackup();
      }
      
      // 执行迁移
      const result = await migrationManager.migrateAllTables();
      
      // 验证数据完整性
      if (config.enable_validation) {
        await this.validateMigration();
      }
      
      // 生成迁移报告
      await this.generateMigrationReport(result);
      
      console.log(chalk.green('✅ 完整迁移执行成功'));
    } catch (error) {
      console.error(chalk.red('❌ 迁移失败:'), error);
      await this.logger.critical('MIGRATION', '完整迁移失败', { error: error.message });
      
      // 自动回滚（如果启用）
      const config = configManager.getConfig();
      if (config.error_handling.auto_rollback) {
        await this.rollbackMigration();
      }
      
      throw error;
    }
  }

  /**
   * 执行增量迁移
   */
  async runIncrementalMigration(tables?: string[]): Promise<void> {
    console.log(chalk.blue('🔄 开始执行增量迁移...'));
    
    try {
      const config = configManager.getConfig();
      const migrationManager = new DataMigrationManager(config, this.logger);
      
      const tablesToMigrate = tables || await this.selectTables();
      
      for (const table of tablesToMigrate) {
        console.log(chalk.yellow(`📊 迁移表: ${table}`));
        await migrationManager.migrateTable(table);
      }
      
      console.log(chalk.green('✅ 增量迁移执行成功'));
    } catch (error) {
      console.error(chalk.red('❌ 增量迁移失败:'), error);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration(): Promise<void> {
    console.log(chalk.blue('🔍 开始验证迁移数据完整性...'));
    
    try {
      const config = configManager.getConfig();
      const validator = new DataIntegrityValidator(config, this.logger);
      
      const result = await validator.validateAllTables();
      
      if (result.overall_status === 'PASSED') {
        console.log(chalk.green('✅ 数据完整性验证通过'));
      } else {
        console.log(chalk.yellow('⚠️ 数据完整性验证发现问题'));
        console.log(chalk.yellow(`总计问题: ${result.total_issues}`));
        
        // 显示详细问题
        result.table_results.forEach(tableResult => {
          if (tableResult.issues.length > 0) {
            console.log(chalk.red(`❌ ${tableResult.table_name}: ${tableResult.issues.length} 个问题`));
          }
        });
      }
    } catch (error) {
      console.error(chalk.red('❌ 验证失败:'), error);
      throw error;
    }
  }

  /**
   * 回滚迁移
   */
  async rollbackMigration(): Promise<void> {
    console.log(chalk.blue('🔙 开始回滚迁移...'));
    
    try {
      const config = configManager.getConfig();
      const rollbackManager = new DataRollbackManager(config, this.logger);
      
      const result = await rollbackManager.rollbackAllTables();
      
      console.log(chalk.green('✅ 迁移回滚成功'));
      console.log(chalk.blue(`回滚了 ${result.total_tables} 个表，共 ${result.total_records} 条记录`));
    } catch (error) {
      console.error(chalk.red('❌ 回滚失败:'), error);
      throw error;
    }
  }

  /**
   * 清理迁移数据
   */
  async cleanupMigration(mode: 'all' | 'failed' | 'partial' | 'logs' = 'failed'): Promise<void> {
    console.log(chalk.blue(`🧹 开始清理迁移数据 (模式: ${mode})...`));
    
    try {
      const config = configManager.getConfig();
      const cleanupManager = new DataCleanupManager(config, this.logger);
      
      const result = await cleanupManager.cleanup(mode);
      
      console.log(chalk.green('✅ 数据清理完成'));
      console.log(chalk.blue(`清理了 ${result.cleaned_records} 条记录`));
    } catch (error) {
      console.error(chalk.red('❌ 清理失败:'), error);
      throw error;
    }
  }

  /**
   * 显示迁移状态
   */
  async showStatus(): Promise<void> {
    console.log(chalk.blue('📊 获取迁移状态...'));
    
    try {
      const config = configManager.getConfig();
      const enabledTables = configManager.getEnabledTables();
      
      console.log(chalk.green('\n=== 迁移配置状态 ==='));
      console.log(`启用的表: ${enabledTables.length} 个`);
      console.log(`批处理大小: ${config.batch_size}`);
      console.log(`并行表数: ${config.parallel_tables}`);
      console.log(`验证启用: ${config.enable_validation ? '是' : '否'}`);
      
      // 显示表状态
      console.log(chalk.green('\n=== 表迁移状态 ==='));
      for (const table of enabledTables) {
        const count = await this.mysqlManager.getTableCount(table);
        const status = count > 0 ? '已迁移' : '未迁移';
        const statusColor = count > 0 ? chalk.green : chalk.yellow;
        console.log(`${statusColor(status)} ${table}: ${count} 条记录`);
      }
      
      // 显示日志统计
      const logStats = await this.logger.getStatistics();
      console.log(chalk.green('\n=== 日志统计 ==='));
      console.log(`总日志条目: ${logStats.total_entries}`);
      console.log(`错误率: ${logStats.error_rate.toFixed(2)}%`);
      console.log(`平均执行时间: ${logStats.average_execution_time.toFixed(2)}ms`);
      
    } catch (error) {
      console.error(chalk.red('❌ 获取状态失败:'), error);
    }
  }

  /**
   * 生成迁移报告
   */
  async generateReport(): Promise<void> {
    console.log(chalk.blue('📋 生成迁移报告...'));
    
    try {
      const logStats = await this.logger.getStatistics();
      const config = configManager.getConfig();
      
      const report = {
        generated_at: new Date().toISOString(),
        migration_config: {
          batch_size: config.batch_size,
          parallel_tables: config.parallel_tables,
          validation_enabled: config.enable_validation
        },
        statistics: logStats,
        table_status: {} as Record<string, number>
      };
      
      // 获取表状态
      const enabledTables = configManager.getEnabledTables();
      for (const table of enabledTables) {
        report.table_status[table] = await this.mysqlManager.getTableCount(table);
      }
      
      // 保存报告
      const reportPath = `./reports/migration-report-${Date.now()}.json`;
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      
      console.log(chalk.green(`✅ 报告已生成: ${reportPath}`));
    } catch (error) {
      console.error(chalk.red('❌ 生成报告失败:'), error);
    }
  }

  // 私有方法

  private async createDirectories(): Promise<void> {
    const dirs = ['./logs', './backups', './reports'];
    
    for (const dir of dirs) {
      await fs.mkdir(dir, { recursive: true });
    }
  }

  private async preflightCheck(): Promise<void> {
    console.log(chalk.blue('🔍 执行迁移前检查...'));
    
    // 检查环境变量
    const envCheck = await environmentManager.checkRequiredEnvVars();
    if (envCheck.missing.length > 0 || envCheck.invalid.length > 0) {
      throw new Error('环境变量配置不完整或无效');
    }
    
    // 检查数据库连接
    const config = configManager.getConfig();
    const mysqlValid = await environmentManager.validateDatabaseConnection(config.mysql);
    const supabaseValid = await environmentManager.validateSupabaseConnection(config.supabase);
    
    if (!mysqlValid || !supabaseValid) {
      throw new Error('数据库连接验证失败');
    }
    
    console.log(chalk.green('✅ 迁移前检查通过'));
  }

  private async createBackup(): Promise<void> {
    console.log(chalk.blue('💾 创建数据备份...'));
    // 这里可以实现备份逻辑
    console.log(chalk.green('✅ 备份创建完成'));
  }

  private async generateMigrationReport(result: any): Promise<void> {
    const reportPath = `./reports/migration-${Date.now()}.json`;
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(result, null, 2));
    console.log(chalk.green(`📋 迁移报告已生成: ${reportPath}`));
  }

  private async selectTables(): Promise<string[]> {
    const enabledTables = configManager.getEnabledTables();
    
    const { selectedTables } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selectedTables',
        message: '选择要迁移的表:',
        choices: enabledTables.map(table => ({ name: table, value: table })),
        validate: (input) => {
          if (input.length === 0) {
            return '请至少选择一个表';
          }
          return true;
        }
      }
    ]);
    
    return selectedTables;
  }

  async close(): Promise<void> {
    await this.logger.close();
    await this.mysqlManager.close();
  }
}

// CLI程序
const program = new Command();
const runner = new MigrationRunner();

program
  .name('migration-runner')
  .description('数据库迁移工具')
  .version('1.0.0');

program
  .command('init')
  .description('初始化迁移环境')
  .action(async () => {
    try {
      await runner.initialize();
    } catch (error) {
      process.exit(1);
    }
  });

program
  .command('migrate')
  .description('执行完整迁移')
  .action(async () => {
    try {
      await runner.initialize();
      await runner.runFullMigration();
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('incremental')
  .description('执行增量迁移')
  .option('-t, --tables <tables>', '指定要迁移的表（逗号分隔）')
  .action(async (options) => {
    try {
      await runner.initialize();
      const tables = options.tables ? options.tables.split(',') : undefined;
      await runner.runIncrementalMigration(tables);
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('validate')
  .description('验证迁移数据完整性')
  .action(async () => {
    try {
      await runner.initialize();
      await runner.validateMigration();
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('rollback')
  .description('回滚迁移')
  .action(async () => {
    try {
      await runner.initialize();
      
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: '确定要回滚迁移吗？这将删除MySQL中的所有数据并恢复到Supabase。',
          default: false
        }
      ]);
      
      if (confirm) {
        await runner.rollbackMigration();
      } else {
        console.log(chalk.yellow('回滚操作已取消'));
      }
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('cleanup')
  .description('清理迁移数据')
  .option('-m, --mode <mode>', '清理模式 (all|failed|partial|logs)', 'failed')
  .action(async (options) => {
    try {
      await runner.initialize();
      await runner.cleanupMigration(options.mode);
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('status')
  .description('显示迁移状态')
  .action(async () => {
    try {
      await runner.initialize();
      await runner.showStatus();
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

program
  .command('report')
  .description('生成迁移报告')
  .action(async () => {
    try {
      await runner.initialize();
      await runner.generateReport();
    } catch (error) {
      process.exit(1);
    } finally {
      await runner.close();
    }
  });

// 错误处理
process.on('uncaughtException', async (error) => {
  console.error(chalk.red('未捕获的异常:'), error);
  await runner.close();
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  console.error(chalk.red('未处理的Promise拒绝:'), reason);
  await runner.close();
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', async () => {
  console.log(chalk.yellow('\n收到中断信号，正在优雅退出...'));
  await runner.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log(chalk.yellow('\n收到终止信号，正在优雅退出...'));
  await runner.close();
  process.exit(0);
});

// 启动CLI
if (require.main === module) {
  program.parse();
}

export { MigrationRunner };