import { MySQLManager, MySQLConfig } from './MySQLManager';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: '../../.env' });

/**
 * 简化的MySQL测试
 */
async function simpleTest() {
  console.log('🚀 开始MySQL数据库管理器简化测试...');
  
  // 从环境变量读取MySQL配置
  const config: MySQLConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'test',
    connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10'),
    acquireTimeout: parseInt(process.env.MYSQL_ACQUIRE_TIMEOUT || '60000'),
    timeout: parseInt(process.env.MYSQL_TIMEOUT || '60000')
  };

  console.log('📋 MySQL配置:', {
    host: config.host,
    port: config.port,
    user: config.user,
    database: config.database,
    connectionLimit: config.connectionLimit
  });

  const mysqlManager = new MySQLManager(config);

  try {
    console.log('\n=== 测试数据库连接 ===');
    await mysqlManager.initialize();
    console.log('✅ 数据库连接成功');
    
    const isConnected = await mysqlManager.testConnection();
    console.log(`✅ 连接状态: ${isConnected}`);
    
    if (isConnected) {
      try {
        const versionResult = await mysqlManager.query('SELECT VERSION() as version');
        const version = versionResult.success ? versionResult.data[0]?.version : 'Unknown';
        console.log(`✅ 数据库版本: ${version}`);
        
        const checkResult = await mysqlManager.testConnection();
        console.log(`✅ 连接检查: ${checkResult}`);
        
        // 测试简单查询
        console.log('\n=== 测试基础查询 ===');
        const result = await mysqlManager.query('SELECT 1 as test_value');
        console.log('✅ 基础查询成功:', result.data);
        console.log(`✅ 查询结果数量: ${(result.data as any[])?.length || 0}`);
        
      } catch (queryError) {
        console.error('❌ 查询测试失败:', queryError);
      }
    }
    
  } catch (error: unknown) {
    console.error('❌ 数据库连接失败:', error);
  } finally {
    try {
      await mysqlManager.disconnect();
      console.log('\n🔌 数据库连接已关闭');
    } catch (disconnectError) {
      console.error('断开连接时出错:', disconnectError);
    }
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
simpleTest().catch(console.error);