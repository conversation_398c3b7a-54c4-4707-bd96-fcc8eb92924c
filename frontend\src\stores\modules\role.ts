import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createDiscreteApi } from 'naive-ui'
import { roleService } from '@/api/roleService'
import type {
  // Role,
  Permission,
  RoleWithPermissions,
  CreateRoleData,
  UpdateRoleData,
  RoleQueryParams,
  AssignRoleData
} from '@/api/roleService'

// 创建独立的message API，用于在store中使用
const { message } = createDiscreteApi(['message'])

export const useRoleStore = defineStore('role', () => {

  // 状态
  const roles = ref<RoleWithPermissions[]>([])
  const permissions = ref<Permission[]>([])
  const currentRole = ref<RoleWithPermissions | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const pagination = ref({
    page: 1,
    pageSize: 10
  })
  const filters = ref<RoleQueryParams>({
    search: '',
    status: undefined
  })

  // 计算属性
  const activeRoles = computed(() => roles.value.filter(role => role.status))
  const systemRoles = computed(() => roles.value.filter(role => role.is_system))
  const customRoles = computed(() => roles.value.filter(role => !role.is_system))

  // 权限按模块分组
  const permissionsByModule = computed(() => {
    const grouped: Record<string, Permission[]> = {}
    permissions.value.forEach(permission => {
      if (!grouped[permission.module]) {
        grouped[permission.module] = []
      }
      grouped[permission.module].push(permission)
    })
    return grouped
  })

  // 获取角色列表
  const fetchRoles = async (params?: RoleQueryParams) => {
    try {
      loading.value = true
      const queryParams = {
        ...filters.value,
        ...params,
        page: pagination.value.page,
        page_size: pagination.value.pageSize
      }

      const result = await roleService.getRoles(queryParams)
      
      // 检查是否是分页响应
      if (result && typeof result === 'object' && 'data' in result && 'total' in result) {
        roles.value = result.data
        total.value = result.total
      } else {
        // 直接返回数组的情况
        roles.value = Array.isArray(result) ? result : []
        total.value = roles.value.length
      }
      
      if (params) {
        Object.assign(filters.value, params)
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取角色详情
  const fetchRoleDetail = async (id: number) => {
    try {
      loading.value = true
      const result = await roleService.getRoleDetail(id)
      
      if (result && typeof result === 'object' && 'success' in result && result.success && result.data) {
        currentRole.value = result.data
        return result.data
      } else {
        message.error(result?.message || '获取角色详情失败')
        return null
      }
    } catch (error) {
      console.error('获取角色详情失败:', error)
      message.error('获取角色详情失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建角色
  const createRole = async (roleData: CreateRoleData) => {
    try {
      loading.value = true
      const result = await roleService.createRole(roleData)
      
      if (result && typeof result === 'object' && 'success' in result && result.success) {
        message.success('角色创建成功')
        await fetchRoles()
        return result
      } else {
        message.error(result?.message || '角色创建失败')
        return result
      }
    } catch (error) {
      console.error('创建角色失败:', error)
      message.error('创建角色失败')
      return { success: false, message: '创建角色失败', data: undefined as any }
    } finally {
      loading.value = false
    }
  }

  // 更新角色
  const updateRole = async (id: number, roleData: UpdateRoleData) => {
    try {
      loading.value = true
      const result = await roleService.updateRole(id, roleData)
      
      if (result && typeof result === 'object' && 'success' in result && result.success) {
        message.success('角色更新成功')
        await fetchRoles()
        if (currentRole.value?.id === id) {
          await fetchRoleDetail(id)
        }
        return result
      } else {
        message.error(result?.message || '角色更新失败')
        return result
      }
    } catch (error) {
      console.error('更新角色失败:', error)
      message.error('更新角色失败')
      return { success: false, message: '更新角色失败', data: undefined as any }
    } finally {
      loading.value = false
    }
  }

  // 删除角色
  const deleteRole = async (id: number) => {
    try {
      loading.value = true
      const result = await roleService.deleteRole(id)
      
      if (result && typeof result === 'object' && 'success' in result && result.success) {
        message.success('角色删除成功')
        await fetchRoles()
        if (currentRole.value?.id === id) {
          currentRole.value = null
        }
        return result
      } else {
        message.error(result?.message || '角色删除失败')
        return result
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      message.error('删除角色失败')
      return { success: false, message: '删除角色失败', data: undefined as any }
    } finally {
      loading.value = false
    }
  }

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const result = await roleService.getPermissions()
      
      if (result && typeof result === 'object' && 'success' in result && result.success && result.data) {
        permissions.value = result.data
      } else {
        message.error(result?.message || '获取权限列表失败')
      }
    } catch (error) {
      console.error('获取权限列表失败:', error)
      message.error('获取权限列表失败')
    }
  }

  // 分配角色
  const assignRoles = async (assignData: AssignRoleData) => {
    try {
      loading.value = true
      const result = await roleService.assignRoles(assignData)
      
      if (result && typeof result === 'object' && 'success' in result && result.success) {
        message.success('角色分配成功')
        return result
      } else {
        message.error(result?.message || '角色分配失败')
        return result
      }
    } catch (error) {
      console.error('分配角色失败:', error)
      message.error('分配角色失败')
      return { success: false, message: '分配角色失败', data: undefined as any }
    } finally {
      loading.value = false
    }
  }

  // 获取用户角色
  const fetchUserRoles = async (userId: string) => {
    try {
      const result = await roleService.getUserRoles(userId)
      
      if (result && typeof result === 'object' && 'success' in result && result.success && result.data) {
        return result.data
      } else {
        message.error(result?.message || '获取用户角色失败')
        return []
      }
    } catch (error) {
      console.error('获取用户角色失败:', error)
      message.error('获取用户角色失败')
      return []
    }
  }

  // 设置筛选条件
  const setFilters = (newFilters: Partial<RoleQueryParams>) => {
    Object.assign(filters.value, newFilters)
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      search: '',
      status: undefined
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize?: number) => {
    pagination.value.page = page
    if (pageSize) {
      pagination.value.pageSize = pageSize
    }
  }

  // 重置当前角色
  const resetCurrentRole = () => {
    currentRole.value = null
  }

  return {
    // 状态
    roles,
    permissions,
    currentRole,
    loading,
    total,
    pagination,
    filters,
    
    // 计算属性
    activeRoles,
    systemRoles,
    customRoles,
    permissionsByModule,
    
    // 方法
    fetchRoles,
    fetchRoleDetail,
    createRole,
    updateRole,
    deleteRole,
    fetchPermissions,
    assignRoles,
    fetchUserRoles,
    setFilters,
    resetFilters,
    setPagination,
    resetCurrentRole
  }
})