from pydantic_settings import BaseSettings
from typing import Optional
import os


class SecuritySettings(BaseSettings):
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_reset_token_expire_hours: int = 1
    
    class Config:
        extra = "ignore"
        env_prefix = "SECURITY_"


class DatabaseSettings(BaseSettings):
    """数据库相关配置"""
    database_url: str = "sqlite+aiosqlite:///./app.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    
    class Config:
        extra = "ignore"
        env_prefix = "DATABASE_"


class Settings(BaseSettings):
    """应用主配置"""
    app_name: str = "YYSH Customer Management System"
    version: str = "1.0.0"
    debug: bool = True
    
    # CORS配置
    cors_origins: list = ["*"]
    cors_methods: list = ["*"]
    cors_headers: list = ["*"]
    
    # 嵌套配置
    security: SecuritySettings = SecuritySettings()
    database: DatabaseSettings = DatabaseSettings()
    
    class Config:
        extra = "ignore"
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局设置实例
settings = Settings()