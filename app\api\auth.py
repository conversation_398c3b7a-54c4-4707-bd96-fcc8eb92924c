"""认证API路由

处理用户认证相关的API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
import secrets
import random
import string
from typing import Any, Dict
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import base64

from app.database import get_db
from app.services.crud import CRUDUser
from app.models.user import User
from app.utils.security import verify_password, get_password_hash, create_access_token, create_refresh_token, verify_token
from app.schemas.auth import (
    LoginRequest, LoginResponse, RegisterRequest, RegisterResponse,
    RefreshTokenRequest, RefreshTokenResponse, CaptchaResponse,
    VerifyCaptchaRequest, UserInfoResponse, ChangePasswordRequest,
    ResetPasswordRequest, LogoutResponse
)
from app.utils.captcha import generate_captcha, verify_captcha
from app.utils.response import ApiResponse, success_response, error_response
from app.core.config import settings
from app.dependencies import get_current_user

router = APIRouter(prefix="/auth", tags=["认证"])
crud_user = CRUDUser(User)

# 验证码存储（生产环境应使用Redis）
captcha_store = {}


@router.post("/refresh", response_model=ApiResponse[RefreshTokenResponse])
async def refresh_token(
    refresh_data: RefreshTokenRequest
):
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = verify_token(refresh_data.refresh_token)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 生成新的访问令牌
        new_access_token = create_access_token(data={"sub": user_id})
        
        refresh_response = RefreshTokenResponse(
            access_token=new_access_token,
            token_type="bearer",
            expires_in=settings.security.access_token_expire_minutes * 60
        )
        
        return ApiResponse(
            code=200,
            message="令牌刷新成功",
            data=refresh_response,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"令牌刷新失败: {str(e)}"
        )


# 移除重复的类定义，使用从schemas导入的类


@router.post("/login", response_model=ApiResponse[LoginResponse])
async def login(
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    try:
        # 验证验证码（如果提供）
        if login_data.captcha_code and login_data.captcha_key:
            if not verify_captcha(login_data.captcha_key, login_data.captcha_code):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="验证码错误"
                )
        
        # 获取用户
        user = await crud_user.get_by_username(db, username=login_data.username)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账号已被禁用"
            )
        
        # 验证密码
        if not verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        current_login_count = int(user.login_count) if user.login_count else 0
        user.login_count = current_login_count + 1
        await crud_user.update(db, db_obj=user, obj_in={})
        
        # 生成令牌
        access_token = create_access_token(data={"sub": str(user.id)})
        refresh_token = create_refresh_token(data={"sub": str(user.id)})
        
        # 构建用户信息
        user_info: Dict[str, Any] = {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "real_name": user.real_name,
            "nickname": user.nickname,
            "avatar": user.avatar,
            "phone": user.phone,
            "department_id": str(user.department_id) if user.department_id is not None else None,
            "position": user.position,
            "is_superuser": user.is_superuser,
            "is_active": user.is_active
        }
        
        # 确保expires_in是整数
        expires_in_minutes = int(settings.security.access_token_expire_minutes)
        expires_in_seconds = expires_in_minutes * 60
        
        login_response = LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=expires_in_seconds,
            user=user_info
        )
        
        return ApiResponse(
            code=200,
            message="登录成功",
            data=login_response,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"登录异常详细信息: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/register", response_model=ApiResponse[RegisterResponse])
async def register(
    register_data: RegisterRequest,
    db: AsyncSession = Depends(get_db)
):
    """用户注册"""
    try:
        # 验证验证码
        if not verify_captcha(register_data.captcha_key, register_data.captcha_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码错误"
            )
        
        # 检查用户名是否已存在
        existing_user = await crud_user.get_by_username(db, username=register_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        existing_email = await crud_user.get_by_email(db, email=register_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 创建新用户
        password_hash = get_password_hash(register_data.password)
        user_data = {
            "username": register_data.username,
            "email": register_data.email,
            "password_hash": password_hash,
            "real_name": register_data.real_name,
            "phone": register_data.phone,
            "is_active": True,
            "created_at": datetime.utcnow()
        }
        
        new_user = await crud_user.create(db, obj_in=user_data)
        
        register_response = RegisterResponse(
            id=str(new_user.id),
            username=new_user.username,
            email=new_user.email,
            real_name=new_user.real_name,
            created_at=new_user.created_at.isoformat()
        )
        
        return ApiResponse(
            code=200,
            message="注册成功",
            data=register_response,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )


@router.post("/logout", response_model=ApiResponse[LogoutResponse])
async def logout(
    current_user: dict = Depends(get_current_user)
):
    """用户注销"""
    try:
        # 在实际应用中，这里应该将令牌加入黑名单
        # 或者在Redis中标记令牌为无效
        
        logout_response = LogoutResponse(message="注销成功")
        
        return ApiResponse(
            code=200,
            message="注销成功",
            data=logout_response,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注销失败: {str(e)}"
        )


@router.get("/captcha", response_model=ApiResponse[CaptchaResponse])
async def get_captcha():
    """获取验证码"""
    try:
        captcha_key, captcha_image = generate_captcha()
        
        captcha_response = CaptchaResponse(
            captcha_key=captcha_key,
            captcha_image=captcha_image
        )
        
        return ApiResponse(
            code=200,
            message="验证码生成成功",
            data=captcha_response,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证码生成失败: {str(e)}"
        )


@router.post("/verify-captcha", response_model=ApiResponse[dict])
async def verify_captcha_endpoint(
    verify_data: VerifyCaptchaRequest
):
    """验证验证码"""
    try:
        is_valid = verify_captcha(verify_data.captcha_key, verify_data.captcha_code)
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码错误或已过期"
            )
        
        return ApiResponse(
            code=200,
            message="验证码验证成功",
            data={"valid": True},
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证码验证失败: {str(e)}"
        )


@router.get("/me", response_model=ApiResponse[UserInfoResponse])
async def get_current_user_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户信息"""
    try:
        # 从数据库获取完整的用户信息
        user = await crud_user.get(db, id=current_user["id"])
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取部门信息
        department_name = None
        if user.department_id and user.department:
            department_name = user.department.name
        
        user_info = UserInfoResponse(
            id=str(user.id),
            username=user.username,
            email=user.email,
            real_name=user.real_name,
            nickname=user.nickname,
            avatar=user.avatar,
            phone=user.phone,
            gender=user.gender,
            department_id=str(user.department_id) if user.department_id else None,
            department_name=department_name,
            position=user.position,
            is_superuser=user.is_superuser,
            is_active=user.is_active,
            last_login=user.last_login.isoformat() if user.last_login else None,
            created_at=user.created_at.isoformat()
        )
        
        return ApiResponse(
            code=200,
            message="获取用户信息成功",
            data=user_info,
            timestamp=datetime.utcnow(),
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息失败: {str(e)}"
        )