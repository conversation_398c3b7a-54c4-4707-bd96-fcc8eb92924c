# DESIGN - Python+FastAPI架构迁移设计文档

## 1. 整体架构设计

### 1.1 系统架构图

```mermaid
graph TD
    A[Vue 3 Frontend<br/>Port: 8080] --> B[FastAPI Backend<br/>Port: 3001]
    B --> C[SQLAlchemy ORM]
    C --> D[MySQL Database]
    B --> E[File Storage]
    B --> F[Pydantic Validation]
    
    subgraph "Frontend Layer"
        A
    end
    
    subgraph "API Layer"
        B
        F
    end
    
    subgraph "Data Access Layer"
        C
    end
    
    subgraph "Storage Layer"
        D
        E
    end
    
    style A fill:#42b883
    style B fill:#009688
    style C fill:#ff9800
    style D fill:#2196f3
```

### 1.2 技术栈对比

```mermaid
graph LR
    subgraph "现有架构"
        A1[Node.js + Express]
        A2[TypeScript]
        A3[自定义MySQLManager]
        A4[自定义验证中间件]
    end
    
    subgraph "目标架构"
        B1[Python + FastAPI]
        B2[Type Hints]
        B3[SQLAlchemy + aiomysql]
        B4[Pydantic]
    end
    
    A1 -.->|迁移| B1
    A2 -.->|对应| B2
    A3 -.->|替换| B3
    A4 -.->|替换| B4
```

## 2. 分层设计和核心组件

### 2.1 应用分层架构

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[FastAPI Routers]
        B[Pydantic Schemas]
        C[Response Models]
    end
    
    subgraph "Business Logic Layer"
        D[Service Classes]
        E[Business Rules]
        F[Data Validation]
    end
    
    subgraph "Data Access Layer"
        G[SQLAlchemy Models]
        H[Repository Pattern]
        I[Database Sessions]
    end
    
    subgraph "Infrastructure Layer"
        J[Database Connection]
        K[File Handlers]
        L[Configuration]
    end
    
    A --> D
    B --> F
    D --> G
    G --> J
```

### 2.2 核心组件设计

#### 2.2.1 FastAPI应用结构
```python
# app/main.py - 应用入口
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routers import auth, customer, options, options_management
from app.middleware.error_handler import ErrorHandlerMiddleware

app = FastAPI(
    title="YYSH CRM API",
    description="客户关系管理系统API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 中间件配置
app.add_middleware(CORSMiddleware, allow_origins=["*"])
app.add_middleware(ErrorHandlerMiddleware)

# 路由注册
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(customer.router, prefix="/api/customer", tags=["客户管理"])
app.include_router(options.router, prefix="/api/options", tags=["选项查询"])
app.include_router(options_management.router, prefix="/api/options-management", tags=["选项管理"])
```

#### 2.2.2 数据库连接管理
```python
# app/database.py - 数据库连接
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.config import settings

engine = create_async_engine(
    settings.DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=3600,
    echo=settings.DEBUG
)

AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
```

#### 2.2.3 模型定义
```python
# app/models/customer.py - 客户模型
from sqlalchemy import Column, BigInteger, String, DateTime, Enum, Text, JSON, Boolean, DECIMAL
from sqlalchemy.sql import func
from app.models.base import Base

class Customer(Base):
    __tablename__ = "customers"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="客户姓名")
    phone = Column(String(20), comment="手机号")
    email = Column(String(100), comment="邮箱")
    company = Column(String(200), comment="公司名称")
    # ... 其他字段保持与现有数据库一致
    
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
```

#### 2.2.4 Pydantic模式
```python
# app/schemas/customer.py - 客户数据模式
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from datetime import datetime
from enum import Enum

class CustomerStatus(str, Enum):
    POTENTIAL = "potential"
    INTERESTED = "interested"
    DEAL = "deal"
    LOST = "lost"

class CustomerCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="客户姓名")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    company: Optional[str] = Field(None, max_length=200, description="公司名称")
    # ... 其他字段

class CustomerResponse(BaseModel):
    id: int
    name: str
    phone: Optional[str]
    email: Optional[str]
    # ... 其他字段
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

## 3. 模块依赖关系图

```mermaid
graph TD
    A[main.py] --> B[routers/]
    A --> C[middleware/]
    A --> D[config.py]
    
    B --> E[schemas/]
    B --> F[models/]
    B --> G[utils/]
    B --> H[dependencies.py]
    
    F --> I[database.py]
    H --> I
    
    C --> J[error_handler.py]
    C --> K[validation.py]
    
    G --> L[security.py]
    G --> M[file_handler.py]
    G --> N[response.py]
    
    subgraph "Core Modules"
        D
        I
        H
    end
    
    subgraph "API Layer"
        B
        E
        C
    end
    
    subgraph "Data Layer"
        F
    end
    
    subgraph "Utilities"
        G
    end
```

## 4. 接口契约定义

### 4.1 认证接口

#### 4.1.1 用户登录
```python
# POST /api/auth/login
class LoginRequest(BaseModel):
    username: str = Field(..., min_length=1, description="用户名")
    password: str = Field(..., min_length=1, description="密码")
    captcha: Optional[str] = Field(None, description="验证码")

class LoginResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
    
# 响应示例
{
    "success": true,
    "message": "登录成功",
    "data": {
        "token": "admin_token_1703123456",
        "user": {
            "id": 1,
            "username": "admin",
            "name": "管理员",
            "role": "admin",
            "permissions": ["*"]
        }
    }
}
```

#### 4.1.2 验证码获取
```python
# GET /api/auth/captcha
# 返回SVG格式的验证码图片
Response(
    content=svg_content,
    media_type="image/svg+xml"
)
```

### 4.2 客户管理接口

#### 4.2.1 客户列表查询
```python
# GET /api/customer
class CustomerListParams(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(10, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, max_length=100, description="搜索关键词")
    status: Optional[CustomerStatus] = Field(None, description="客户状态")
    source: Optional[str] = Field(None, max_length=50, description="客户来源")
    level: Optional[str] = Field(None, regex="^[ABCD]$", description="客户等级")
    ownerId: Optional[int] = Field(None, ge=1, description="负责人ID")
    isInPool: Optional[bool] = Field(None, description="是否在公海池")
    startDate: Optional[str] = Field(None, regex="^\d{4}-\d{2}-\d{2}$", description="开始日期")
    endDate: Optional[str] = Field(None, regex="^\d{4}-\d{2}-\d{2}$", description="结束日期")

class CustomerListResponse(BaseModel):
    success: bool
    message: Optional[str]
    data: List[CustomerResponse]
    pagination: PaginationInfo

class PaginationInfo(BaseModel):
    page: int
    pageSize: int
    total: int
    totalPages: int
```

#### 4.2.2 客户创建
```python
# POST /api/customer
class CustomerCreateRequest(CustomerCreate):
    pass

class CustomerCreateResponse(BaseModel):
    success: bool
    message: str
    data: Optional[CustomerResponse] = None
```

#### 4.2.3 文件上传
```python
# POST /api/customer/import
from fastapi import UploadFile, File

async def import_customers(
    file: UploadFile = File(..., description="Excel文件")
) -> dict:
    # 处理Excel文件导入
    pass

class ImportResponse(BaseModel):
    success: bool
    message: str
    data: dict = Field(..., description="导入结果统计")
```

### 4.3 选项管理接口

#### 4.3.1 选项查询
```python
# GET /api/options/{option_type}
class OptionResponse(BaseModel):
    success: bool
    data: List[dict]
    
# 响应示例
{
    "success": true,
    "data": [
        {"value": "online", "label": "线上"},
        {"value": "referral", "label": "推荐"}
    ]
}
```

## 5. 数据流向图

### 5.1 客户查询流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant R as Router
    participant V as Validator
    participant S as Service
    participant M as Model
    participant D as Database
    
    F->>R: GET /api/customer?page=1&pageSize=10
    R->>V: 验证查询参数
    V->>S: 调用客户查询服务
    S->>M: 构建查询条件
    M->>D: 执行SQL查询
    D-->>M: 返回查询结果
    M-->>S: 返回模型对象
    S-->>R: 返回业务数据
    R-->>F: 返回JSON响应
```

### 5.2 客户创建流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant R as Router
    participant V as Validator
    participant S as Service
    participant M as Model
    participant D as Database
    
    F->>R: POST /api/customer {data}
    R->>V: 验证请求数据
    V->>S: 调用客户创建服务
    S->>S: 业务逻辑验证
    S->>M: 创建客户模型
    M->>D: 插入数据库记录
    D-->>M: 返回插入结果
    M-->>S: 返回创建的对象
    S-->>R: 返回业务结果
    R-->>F: 返回创建响应
```

### 5.3 文件上传流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant R as Router
    participant FH as FileHandler
    participant P as Pandas
    participant S as Service
    participant D as Database
    
    F->>R: POST /api/customer/import {file}
    R->>FH: 处理上传文件
    FH->>P: 解析Excel文件
    P-->>FH: 返回数据框
    FH->>S: 批量创建客户
    S->>D: 批量插入数据
    D-->>S: 返回插入结果
    S-->>R: 返回导入统计
    R-->>F: 返回导入结果
```

## 6. 异常处理策略

### 6.1 异常处理层次

```mermaid
graph TD
    A[HTTP请求] --> B[路由层异常处理]
    B --> C[业务层异常处理]
    C --> D[数据层异常处理]
    D --> E[全局异常处理器]
    
    B --> F[参数验证异常]
    C --> G[业务逻辑异常]
    D --> H[数据库异常]
    E --> I[未知异常]
    
    F --> J[400 Bad Request]
    G --> K[422 Unprocessable Entity]
    H --> L[500 Internal Server Error]
    I --> L
```

### 6.2 异常处理实现

```python
# app/middleware/error_handler.py
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError
import logging

class ErrorHandlerMiddleware:
    async def __call__(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "success": False,
                    "message": e.detail,
                    "error": "HTTP异常"
                }
            )
        except SQLAlchemyError as e:
            logging.error(f"数据库错误: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": "数据库操作失败",
                    "error": "数据库异常"
                }
            )
        except Exception as e:
            logging.error(f"未知错误: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": "服务器内部错误",
                    "error": "系统异常"
                }
            )
```

### 6.3 数据验证异常

```python
# 自定义验证异常处理
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors = []
    for error in exc.errors():
        field = ".".join(str(x) for x in error["loc"][1:])
        message = error["msg"]
        errors.append(f"{field}: {message}")
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "数据验证失败",
            "error": "; ".join(errors)
        }
    )
```

## 7. 安全设计

### 7.1 认证安全

```python
# app/utils/security.py
from fastapi import HTTPException, Depends, Header
from typing import Optional

async def verify_token(authorization: Optional[str] = Header(None)):
    if not authorization:
        raise HTTPException(status_code=401, detail="缺少认证头")
    
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="认证格式错误")
    
    token = authorization.split(" ")[1]
    
    # 验证token（保持与现有系统一致的简单验证）
    if not token.startswith("admin_token_"):
        raise HTTPException(status_code=401, detail="无效的token")
    
    return {"id": 1, "username": "admin", "role": "admin"}
```

### 7.2 输入安全

```python
# SQL注入防护（SQLAlchemy自动处理）
# XSS防护（Pydantic数据验证）
# 文件上传安全
from fastapi import UploadFile

ALLOWED_EXTENSIONS = {".xlsx", ".xls"}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

async def validate_upload_file(file: UploadFile):
    # 检查文件扩展名
    if not any(file.filename.lower().endswith(ext) for ext in ALLOWED_EXTENSIONS):
        raise HTTPException(status_code=400, detail="不支持的文件类型")
    
    # 检查文件大小
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="文件大小超过限制")
    
    await file.seek(0)  # 重置文件指针
    return file
```

## 8. 性能优化设计

### 8.1 数据库优化

```python
# 连接池配置
engine = create_async_engine(
    DATABASE_URL,
    pool_size=10,          # 连接池大小
    max_overflow=20,       # 最大溢出连接
    pool_timeout=30,       # 获取连接超时
    pool_recycle=3600,     # 连接回收时间
    pool_pre_ping=True,    # 连接预检查
    echo=False             # 生产环境关闭SQL日志
)

# 查询优化
from sqlalchemy.orm import selectinload, joinedload

# 预加载关联数据
query = select(Customer).options(
    selectinload(Customer.tags),
    joinedload(Customer.owner)
)
```

### 8.2 响应优化

```python
# 分页查询优化
from sqlalchemy import func, select

async def get_customers_with_pagination(
    db: AsyncSession,
    page: int,
    page_size: int,
    filters: dict
):
    # 计算总数（优化的count查询）
    count_query = select(func.count(Customer.id)).filter_by(**filters)
    total = await db.scalar(count_query)
    
    # 分页查询
    offset = (page - 1) * page_size
    query = select(Customer).filter_by(**filters).offset(offset).limit(page_size)
    result = await db.execute(query)
    customers = result.scalars().all()
    
    return customers, total
```

### 8.3 缓存策略

```python
# 选项数据缓存（内存缓存）
from functools import lru_cache
from typing import List, Dict

@lru_cache(maxsize=128)
async def get_cached_options(option_type: str) -> List[Dict]:
    # 缓存系统选项数据
    pass

# 清除缓存
def clear_options_cache():
    get_cached_options.cache_clear()
```

## 9. 监控和日志设计

### 9.1 日志配置

```python
# app/utils/logging.py
import logging
from app.config import settings

logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

### 9.2 健康检查

```python
# 健康检查接口
@app.get("/api/health")
async def health_check(db: AsyncSession = Depends(get_db)):
    try:
        # 检查数据库连接
        await db.execute(text("SELECT 1"))
        return {
            "success": True,
            "message": "ok",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail="服务不可用")
```

## 10. 部署和配置设计

### 10.1 配置管理

```python
# app/config.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = ""
    DB_NAME: str = "workchat_admin"
    
    # 应用配置
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key"
    
    @property
    def DATABASE_URL(self) -> str:
        return f"mysql+aiomysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 10.2 启动脚本

```python
# run.py
import uvicorn
from app.main import app
from app.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
```

这个设计文档提供了完整的Python+FastAPI迁移架构设计，确保与现有Node.js系统的完全兼容性，同时利用Python生态系统的优势提升系统的可维护性和扩展性。