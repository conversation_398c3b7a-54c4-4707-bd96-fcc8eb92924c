<template>
  <div class="visit-stage-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <n-space>
          <n-button type="info" ghost>
            <template #icon>
              <n-icon><filter-outline /></n-icon>
            </template>
            筛选
          </n-button>
        </n-space>
      </div>
      <div class="toolbar-right">
        <n-space>
          <n-select
            v-model:value="subStageFilter"
            placeholder="子阶段筛选"
            clearable
            style="width: 150px"
            :options="subStageOptions"
            @update:value="handleSubStageFilter"
          />
          <n-select
            v-model:value="designerFilter"
            placeholder="设计师筛选"
            clearable
            style="width: 150px"
            :options="designerOptions"
            @update:value="handleDesignerFilter"
          />
        </n-space>
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: FollowRecord) => row.id"
      remote
      @update:page="$emit('page-change', $event)"
      @update:page-size="$emit('page-size-change', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, markRaw } from 'vue'
import {
  FilterOutline,
  CreateOutline,
  EyeOutline,
  PersonOutline
} from '@vicons/ionicons5'
import type { FollowRecord } from '@/types'
import type { DataTableColumns } from 'naive-ui'

interface Props {
  data: FollowRecord[]
  loading: boolean
  pagination: any
}

interface Emits {
  edit: [record: FollowRecord]
  view: [record: FollowRecord]
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
}

// 获取props和emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 筛选条件
const subStageFilter = ref<string | null>(null)
const designerFilter = ref<string | null>(null)

// 子阶段选项
const subStageOptions = [
  { label: '量房', value: 'measure' },
  { label: '到店', value: 'visit' },
  { label: '拜访', value: 'home_visit' }
]

// 设计师选项
const designerOptions = [
  { label: '设计师A', value: '设计师A' },
  { label: '设计师B', value: '设计师B' },
  { label: '设计师C', value: '设计师C' }
]

// 跟进方式选项
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '到店参观', value: 'visit' },
  { label: '上门量房', value: 'measure' },
  { label: '家访', value: 'home_visit' },
  { label: '其他', value: 'other' }
]

// 跟进状态选项
const statusOptions = [
  { label: '已联系', value: 'contacted' },
  { label: '有意向', value: 'interested' },
  { label: '无意向', value: 'not_interested' },
  { label: '待跟进', value: 'pending' },
  { label: '已成交', value: 'closed' }
]

// 过滤后的数据
const filteredData = computed(() => {
  let result = props.data
  
  if (subStageFilter.value) {
    result = result.filter(record => record.subStage === subStageFilter.value)
  }
  
  if (designerFilter.value) {
    result = result.filter(record => record.designer === designerFilter.value)
  }
  
  return result
})

// 表格列配置
const columns: DataTableColumns<FollowRecord> = [
  {
    title: '客户姓名',
    key: 'customerName',
    width: 120,
    render: (row) => row.customerName || row.customer_name
  },
  {
    title: '子阶段',
    key: 'subStage',
    width: 100,
    render: (row) => {
      const subStage = subStageOptions.find(item => item.value === row.subStage)
      const colorMap: Record<string, string> = {
        measure: 'warning',
        visit: 'info',
        home_visit: 'success'
      }
      return h(
        'n-tag',
        { type: colorMap[row.subStage || ''] || 'default', size: 'small' },
        { default: () => subStage?.label || row.subStage || '未设置' }
      )
    }
  },
  {
    title: '设计师',
    key: 'designer',
    width: 120,
    render: (row) => {
      if (row.designer) {
        return h(
          'n-space',
          { align: 'center', size: 'small' },
          {
            default: () => [
              h('n-icon', { color: '#52c41a' }, { default: () => h(markRaw(PersonOutline)) }),
              h('span', null, row.designer)
            ]
          }
        )
      }
      return '-'
    }
  },
  {
    title: '跟进方式',
    key: 'type',
    width: 100,
    render: (row) => {
      const type = typeOptions.find(item => item.value === row.type)
      return type?.label || row.type
    }
  },
  {
    title: '跟进状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const status = statusOptions.find(item => item.value === row.status)
      const statusMap: Record<string, string> = {
        contacted: 'info',
        interested: 'success',
        not_interested: 'warning',
        pending: 'default',
        closed: 'success'
      }
      return h(
        'n-tag',
        { type: statusMap[row.status] || 'default', size: 'small' },
        { default: () => status?.label || row.status }
      )
    }
  },
  {
    title: '到店时间',
    key: 'visitDate',
    width: 160,
    render: (row) => {
      if (row.visitDate) {
        return new Date(row.visitDate).toLocaleString()
      }
      return row.followTime ? new Date(row.followTime || row.follow_time).toLocaleString() : '-'
    }
  },
  {
    title: '跟进内容',
    key: 'content',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '下次跟进',
    key: 'nextFollowTime',
    width: 160,
    render: (row) => row.nextFollowTime || row.next_follow_time ? 
      new Date(row.nextFollowTime || row.next_follow_time!).toLocaleString() : '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: (row) => {
      return [
        h(
          'n-button',
          {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => emit('view', row)
          },
          { 
            default: () => '查看', 
            icon: () => h('n-icon', null, { default: () => h(markRaw(EyeOutline)) }) 
          }
        ),
        h(
          'n-button',
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            style: { marginLeft: '8px' },
            onClick: () => emit('edit', row)
          },
          { 
            default: () => '编辑', 
            icon: () => h('n-icon', null, { default: () => h(markRaw(CreateOutline)) }) 
          }
        )
      ]
    }
  }
]

// 方法
const handleSubStageFilter = () => {
  // 触发父组件重新获取数据
}

const handleDesignerFilter = () => {
  // 触发父组件重新获取数据
}
</script>

<style scoped>
.visit-stage-table {
  background: white;
  border-radius: 8px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

:deep(.n-data-table) {
  border-radius: 8px;
}

:deep(.n-data-table-th) {
  background-color: #f0f9ff;
  font-weight: 600;
  border-bottom: 1px solid #e0f2fe;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
  transition: all 0.3s ease;
}

:deep(.n-tag) {
  font-weight: 500;
}
</style>