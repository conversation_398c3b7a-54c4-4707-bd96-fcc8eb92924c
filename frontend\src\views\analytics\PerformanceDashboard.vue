<template>
  <div class="performance-dashboard">

    <!-- 筛选条件 -->
    <n-card class="filter-card">
      <n-form inline :label-width="80">
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="filters.dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="销售团队">
          <n-select
            v-model:value="filters.team"
            placeholder="选择销售团队"
            clearable
            style="width: 150px"
            :options="teamOptions"
          />
        </n-form-item>
        <n-form-item label="产品线">
          <n-select
            v-model:value="filters.productLine"
            placeholder="选择产品线"
            clearable
            style="width: 150px"
            :options="productLineOptions"
          />
        </n-form-item>
        <n-form-item label="对比维度">
          <n-select
            v-model:value="filters.compareBy"
            placeholder="选择对比维度"
            style="width: 150px"
            :options="compareByOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button @click="handleSearch" type="primary">
              <template #icon>
                <n-icon><Search /></n-icon>
              </template>
              查询
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><Refresh /></n-icon>
              </template>
              重置
            </n-button>
            <n-button @click="handleExport">
              <template #icon>
                <n-icon><Download /></n-icon>
              </template>
              导出报表
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 核心指标概览 -->
    <n-grid :cols="4" :x-gap="20" class="stats-grid">
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="总销售额" :value="stats.totalRevenue" :precision="2">
            <template #prefix>¥</template>
            <template #suffix>
              <n-icon color="#18a058"><TrendingUp /></n-icon>
            </template>
          </n-statistic>
          <div class="stat-trend">
            <span class="trend-text positive">+12.5%</span>
            <span class="trend-desc">较上月</span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="订单数量" :value="stats.totalOrders">
            <template #suffix>
              <n-icon color="#2080f0"><Receipt /></n-icon>
            </template>
          </n-statistic>
          <div class="stat-trend">
            <span class="trend-text positive">+8.3%</span>
            <span class="trend-desc">较上月</span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="平均客单价" :value="stats.avgOrderValue" :precision="2">
            <template #prefix>¥</template>
            <template #suffix>
              <n-icon color="#f0a020"><Cash /></n-icon>
            </template>
          </n-statistic>
          <div class="stat-trend">
            <span class="trend-text positive">+3.7%</span>
            <span class="trend-desc">较上月</span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="新客户数" :value="stats.newCustomers">
            <template #suffix>
              <n-icon color="#d03050"><PersonAdd /></n-icon>
            </template>
          </n-statistic>
          <div class="stat-trend">
            <span class="trend-text negative">-2.1%</span>
            <span class="trend-desc">较上月</span>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表分析 -->
    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="销售趋势分析">
          <template #header-extra>
            <n-radio-group v-model:value="salesTrendPeriod" size="small">
              <n-radio-button value="week">周</n-radio-button>
              <n-radio-button value="month">月</n-radio-button>
              <n-radio-button value="quarter">季</n-radio-button>
            </n-radio-group>
          </template>
          <div ref="salesTrendChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="团队业绩对比">
          <template #header-extra>
            <n-radio-group v-model:value="teamCompareMetric" size="small">
              <n-radio-button value="revenue">销售额</n-radio-button>
              <n-radio-button value="orders">订单数</n-radio-button>
              <n-radio-button value="customers">客户数</n-radio-button>
            </n-radio-group>
          </template>
          <div ref="teamCompareChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="产品销售分布">
          <div ref="productDistributionChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="销售目标达成">
          <div ref="targetAchievementChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 团队排行榜 -->
    <n-grid :cols="2" :x-gap="20" class="ranking-grid">
      <n-grid-item>
        <n-card title="销售人员排行榜" class="ranking-card">
          <template #header-extra>
            <n-select
              v-model:value="salesRankingMetric"
              size="small"
              style="width: 120px"
              :options="rankingMetricOptions"
            />
          </template>
          <div class="ranking-list">
            <div 
              v-for="(item, index) in salesRanking" 
              :key="item.id" 
              class="ranking-item"
            >
              <div class="rank-number" :class="getRankClass(index)">
                {{ index + 1 }}
              </div>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-team">{{ item.team }}</div>
              </div>
              <div class="rank-value">
                <div class="value">{{ formatRankValue(item.value, salesRankingMetric) }}</div>
                <div class="progress">
                  <n-progress 
                    :percentage="item.percentage" 
                    :show-indicator="false" 
                    :height="6"
                    :color="getRankColor(index)"
                  />
                </div>
              </div>
            </div>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="团队排行榜" class="ranking-card">
          <template #header-extra>
            <n-select
              v-model:value="teamRankingMetric"
              size="small"
              style="width: 120px"
              :options="rankingMetricOptions"
            />
          </template>
          <div class="ranking-list">
            <div 
              v-for="(item, index) in teamRanking" 
              :key="item.id" 
              class="ranking-item"
            >
              <div class="rank-number" :class="getRankClass(index)">
                {{ index + 1 }}
              </div>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-team">{{ item.memberCount }}人</div>
              </div>
              <div class="rank-value">
                <div class="value">{{ formatRankValue(item.value, teamRankingMetric) }}</div>
                <div class="progress">
                  <n-progress 
                    :percentage="item.percentage" 
                    :show-indicator="false" 
                    :height="6"
                    :color="getRankColor(index)"
                  />
                </div>
              </div>
            </div>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 详细数据表格 -->
    <n-card title="详细业绩数据" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索销售人员"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="performanceColumns"
        :data="filteredPerformanceData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: PerformanceRecord) => row.id"
        size="medium"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NIcon,
  NTag,
  NStatistic,
  NGrid,
  NGridItem,
  NProgress,
  NRadioGroup,
  NRadioButton,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import {
  SearchOutline as Search,
  RefreshOutline as Refresh,
  DownloadOutline as Download,
  TrendingUpOutline as TrendingUp,
  ReceiptOutline as Receipt,
  CashOutline as Cash,
  PersonAddOutline as PersonAdd
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface PerformanceRecord {
  id: number
  name: string
  team: string
  position: string
  revenue: number
  orders: number
  customers: number
  avgOrderValue: number
  conversionRate: number
  targetCompletion: number
  lastMonthGrowth: number
  status: string
}

interface RankingItem {
  id: number
  name: string
  team?: string
  memberCount?: number
  value: number
  percentage: number
}

const message = useMessage()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const performanceData = ref<PerformanceRecord[]>([])
const salesRanking = ref<RankingItem[]>([])
const teamRanking = ref<RankingItem[]>([])

// 图表引用
const salesTrendChart = ref<HTMLElement>()
const teamCompareChart = ref<HTMLElement>()
const productDistributionChart = ref<HTMLElement>()
const targetAchievementChart = ref<HTMLElement>()

// 图表实例
let salesTrendInstance: echarts.ECharts | null = null
let teamCompareInstance: echarts.ECharts | null = null
let productDistributionInstance: echarts.ECharts | null = null
let targetAchievementInstance: echarts.ECharts | null = null

// 筛选条件
const filters = reactive({
  dateRange: null as [number, number] | null,
  team: null as string | null,
  productLine: null as string | null,
  compareBy: 'month' as string
})

// 图表控制
const salesTrendPeriod = ref('month')
const teamCompareMetric = ref('revenue')
const salesRankingMetric = ref('revenue')
const teamRankingMetric = ref('revenue')

// 统计数据
const stats = reactive({
  totalRevenue: 2580000,
  totalOrders: 1250,
  avgOrderValue: 2064,
  newCustomers: 186
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 选项配置
const teamOptions = [
  { label: '销售一部', value: 'sales_1' },
  { label: '销售二部', value: 'sales_2' },
  { label: '销售三部', value: 'sales_3' },
  { label: '大客户部', value: 'enterprise' }
]

const productLineOptions = [
  { label: '产品A系列', value: 'product_a' },
  { label: '产品B系列', value: 'product_b' },
  { label: '产品C系列', value: 'product_c' },
  { label: '服务套餐', value: 'service' }
]

const compareByOptions = [
  { label: '按月对比', value: 'month' },
  { label: '按季度对比', value: 'quarter' },
  { label: '按年对比', value: 'year' }
]

const rankingMetricOptions = [
  { label: '销售额', value: 'revenue' },
  { label: '订单数', value: 'orders' },
  { label: '客户数', value: 'customers' },
  { label: '转化率', value: 'conversion' }
]

// 业绩数据表格列
const performanceColumns: DataTableColumns<PerformanceRecord> = [
  {
    title: '姓名',
    key: 'name',
    width: 100,
    fixed: 'left'
  },
  {
    title: '团队',
    key: 'team',
    width: 100
  },
  {
    title: '职位',
    key: 'position',
    width: 100
  },
  {
    title: '销售额',
    key: 'revenue',
    width: 120,
    render(row) {
      return `¥${row.revenue.toLocaleString()}`
    },
    sorter: (a, b) => a.revenue - b.revenue
  },
  {
    title: '订单数',
    key: 'orders',
    width: 100,
    sorter: (a, b) => a.orders - b.orders
  },
  {
    title: '客户数',
    key: 'customers',
    width: 100,
    sorter: (a, b) => a.customers - b.customers
  },
  {
    title: '客单价',
    key: 'avgOrderValue',
    width: 120,
    render(row) {
      return `¥${row.avgOrderValue.toLocaleString()}`
    },
    sorter: (a, b) => a.avgOrderValue - b.avgOrderValue
  },
  {
    title: '转化率',
    key: 'conversionRate',
    width: 100,
    render(row) {
      const color = row.conversionRate >= 15 ? '#18a058' : row.conversionRate >= 10 ? '#f0a020' : '#d03050'
      return h('span', { style: { color, fontWeight: 'bold' } }, `${row.conversionRate}%`)
    },
    sorter: (a, b) => a.conversionRate - b.conversionRate
  },
  {
    title: '目标完成率',
    key: 'targetCompletion',
    width: 130,
    render(row) {
      return h(NProgress, {
        type: 'line',
        percentage: row.targetCompletion,
        showIndicator: true,
        status: row.targetCompletion >= 100 ? 'success' : row.targetCompletion >= 80 ? 'warning' : 'error'
      })
    },
    sorter: (a, b) => a.targetCompletion - b.targetCompletion
  },
  {
    title: '环比增长',
    key: 'lastMonthGrowth',
    width: 100,
    render(row) {
      const isPositive = row.lastMonthGrowth >= 0
      const color = isPositive ? '#18a058' : '#d03050'
      const prefix = isPositive ? '+' : ''
      return h('span', { style: { color, fontWeight: 'bold' } }, `${prefix}${row.lastMonthGrowth}%`)
    },
    sorter: (a, b) => a.lastMonthGrowth - b.lastMonthGrowth
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        active: { text: '在职', type: 'success' as const },
        leave: { text: '请假', type: 'warning' as const },
        inactive: { text: '离职', type: 'error' as const }
      }
      const status = statusMap[row.status as keyof typeof statusMap] || { text: row.status, type: 'default' as const }
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    }
  }
]

// 计算属性
const filteredPerformanceData = computed(() => {
  if (!searchKeyword.value) return performanceData.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return performanceData.value.filter(record => 
    record.name.toLowerCase().includes(keyword)
  )
})

// 方法
const handleSearch = () => {
  loadData()
  loadCharts()
}

const handleReset = () => {
  Object.assign(filters, {
    dateRange: null,
    team: null,
    productLine: null,
    compareBy: 'month'
  })
  searchKeyword.value = ''
  loadData()
  loadCharts()
}

const handleRefresh = () => {
  loadData()
  loadCharts()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const getRankClass = (index: number) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

const getRankColor = (index: number) => {
  const colors = ['#f0a020', '#c0c0c0', '#cd7f32', '#2080f0']
  return colors[Math.min(index, 3)]
}

const formatRankValue = (value: number, metric: string) => {
  switch (metric) {
    case 'revenue':
      return `¥${value.toLocaleString()}`
    case 'conversion':
      return `${value}%`
    default:
      return value.toString()
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟业绩数据
    performanceData.value = [
      {
        id: 1,
        name: '张三',
        team: '销售一部',
        position: '高级销售',
        revenue: 580000,
        orders: 145,
        customers: 89,
        avgOrderValue: 4000,
        conversionRate: 18.5,
        targetCompletion: 116,
        lastMonthGrowth: 12.5,
        status: 'active'
      },
      {
        id: 2,
        name: '李四',
        team: '销售一部',
        position: '销售经理',
        revenue: 520000,
        orders: 130,
        customers: 76,
        avgOrderValue: 4000,
        conversionRate: 16.2,
        targetCompletion: 104,
        lastMonthGrowth: 8.3,
        status: 'active'
      },
      {
        id: 3,
        name: '王五',
        team: '销售二部',
        position: '销售代表',
        revenue: 380000,
        orders: 95,
        customers: 58,
        avgOrderValue: 4000,
        conversionRate: 12.8,
        targetCompletion: 76,
        lastMonthGrowth: -2.1,
        status: 'active'
      }
    ]
    
    // 模拟排行榜数据
    salesRanking.value = [
      { id: 1, name: '张三', team: '销售一部', value: 580000, percentage: 100 },
      { id: 2, name: '李四', team: '销售一部', value: 520000, percentage: 90 },
      { id: 3, name: '王五', team: '销售二部', value: 380000, percentage: 65 },
      { id: 4, name: '赵六', team: '销售三部', value: 320000, percentage: 55 },
      { id: 5, name: '钱七', team: '大客户部', value: 280000, percentage: 48 }
    ]
    
    teamRanking.value = [
      { id: 1, name: '销售一部', memberCount: 8, value: 1580000, percentage: 100 },
      { id: 2, name: '大客户部', memberCount: 5, value: 1200000, percentage: 76 },
      { id: 3, name: '销售二部', memberCount: 6, value: 980000, percentage: 62 },
      { id: 4, name: '销售三部', memberCount: 7, value: 820000, percentage: 52 }
    ]
    
    pagination.itemCount = performanceData.value.length
  } catch (error) {
    message.error('获取业绩数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (salesTrendChart.value) {
      salesTrendInstance = echarts.init(salesTrendChart.value)
    }
    if (teamCompareChart.value) {
      teamCompareInstance = echarts.init(teamCompareChart.value)
    }
    if (productDistributionChart.value) {
      productDistributionInstance = echarts.init(productDistributionChart.value)
    }
    if (targetAchievementChart.value) {
      targetAchievementInstance = echarts.init(targetAchievementChart.value)
    }
    
    loadCharts()
  })
}

// 加载图表数据
const loadCharts = () => {
  // 销售趋势分析图
  if (salesTrendInstance) {
    const option = {
      title: { text: '销售趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['销售额', '订单数', '新客户数'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: [
        {
          type: 'value',
          name: '金额(万元)',
          position: 'left'
        },
        {
          type: 'value',
          name: '数量',
          position: 'right'
        }
      ],
      series: [
        {
          name: '销售额',
          type: 'line',
          data: [180, 220, 250, 280, 320, 258],
          smooth: true,
          itemStyle: { color: '#5470c6' }
        },
        {
          name: '订单数',
          type: 'bar',
          yAxisIndex: 1,
          data: [850, 1020, 1150, 1280, 1450, 1250],
          itemStyle: { color: '#91cc75' }
        },
        {
          name: '新客户数',
          type: 'line',
          yAxisIndex: 1,
          data: [120, 145, 168, 185, 210, 186],
          smooth: true,
          itemStyle: { color: '#fac858' }
        }
      ]
    }
    salesTrendInstance.setOption(option)
  }
  
  // 团队业绩对比图
  if (teamCompareInstance) {
    const option = {
      title: { text: '团队业绩对比', left: 'center' },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['销售一部', '销售二部', '销售三部', '大客户部']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}万'
        }
      },
      series: [{
        name: '销售额',
        type: 'bar',
        data: [158, 98, 82, 120],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    }
    teamCompareInstance.setOption(option)
  }
  
  // 产品销售分布图
  if (productDistributionInstance) {
    const option = {
      title: { text: '产品销售分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 680, name: '产品A系列' },
          { value: 520, name: '产品B系列' },
          { value: 380, name: '产品C系列' },
          { value: 420, name: '服务套餐' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    productDistributionInstance.setOption(option)
  }
  
  // 销售目标达成图
  if (targetAchievementInstance) {
    const option = {
      title: { text: '销售目标达成情况', left: 'center' },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['实际完成', '目标'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: ['销售一部', '销售二部', '销售三部', '大客户部']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}万'
        }
      },
      series: [
        {
          name: '实际完成',
          type: 'bar',
          data: [158, 98, 82, 120],
          itemStyle: { color: '#5470c6' }
        },
        {
          name: '目标',
          type: 'bar',
          data: [150, 120, 110, 100],
          itemStyle: { color: '#91cc75' }
        }
      ]
    }
    targetAchievementInstance.setOption(option)
  }
}

// 初始化
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.performance-dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  position: relative;
}

.stat-trend {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.trend-text {
  font-size: 12px;
  font-weight: bold;
}

.trend-text.positive {
  color: #18a058;
}

.trend-text.negative {
  color: #d03050;
}

.trend-desc {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.charts-grid {
  margin-bottom: 20px;
}

.ranking-grid {
  margin-bottom: 20px;
}

.ranking-card {
  height: 100%;
}

.ranking-list {
  padding: 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 12px;
}

.rank-first {
  background: linear-gradient(135deg, #f0a020, #f5b942);
  color: white;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #d4d4d4);
  color: white;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #d4935a);
  color: white;
}

.rank-normal {
  background: var(--n-color-target);
  color: var(--n-text-color);
  border: 1px solid var(--n-border-color);
}

.rank-info {
  flex: 1;
  margin-right: 12px;
}

.rank-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--n-text-color);
}

.rank-team {
  font-size: 12px;
  color: var(--n-text-color-2);
  margin-top: 2px;
}

.rank-value {
  text-align: right;
  min-width: 100px;
}

.rank-value .value {
  font-weight: bold;
  font-size: 14px;
  color: var(--n-text-color);
  margin-bottom: 4px;
}

.rank-value .progress {
  width: 100px;
}

.table-card {
  margin-bottom: 20px;
}
</style>