{"migrationId": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "timestamp": "2025-08-18T06:29:40.133Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 10, "failedTables": 11, "totalRecords": 165, "migratedRecords": 15, "failedRecords": 150}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:29:01.370Z", "errors": ["Unknown column 'avatar' in 'field list'"], "endTime": "2025-08-18T06:29:07.742Z", "duration": 6372}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:29:07.747Z", "errors": ["Unknown column 'is_system' in 'field list'"], "endTime": "2025-08-18T06:29:11.523Z", "duration": 3776}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T06:29:11.526Z", "errors": ["Unknown column 'is_system' in 'field list'"], "endTime": "2025-08-18T06:29:13.640Z", "duration": 2114}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:29:13.642Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`role_permissions`, CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T06:29:15.364Z", "duration": 1722}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:15.367Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:29:15.667Z", "errors": [], "endTime": "2025-08-18T06:29:17.108Z", "duration": 1441}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:29:17.110Z", "errors": ["Unknown column 'color' in 'field list'"], "endTime": "2025-08-18T06:29:20.308Z", "duration": 3198}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:29:20.311Z", "errors": ["Unknown column 'tags' in 'field list'"], "endTime": "2025-08-18T06:29:22.279Z", "duration": 1968}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:22.282Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:29:22.860Z", "errors": ["Unknown column 'end_time' in 'field list'"], "endTime": "2025-08-18T06:29:24.340Z", "duration": 1480}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:24.343Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:24.631Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:29:25.282Z", "errors": ["Unknown column 'organizer_id' in 'field list'"], "endTime": "2025-08-18T06:29:29.098Z", "duration": 3816}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:29.101Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:29:29.404Z", "errors": ["Unknown column 'max_claims_per_day' in 'field list'"], "endTime": "2025-08-18T06:29:31.092Z", "duration": 1688}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:31.094Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:31.411Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:32.161Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:29:33.112Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:29:33.866Z", "errors": ["Unknown column 'content' in 'field list'"], "endTime": "2025-08-18T06:29:38.087Z", "duration": 4221}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:29:38.089Z", "errors": ["Unknown column 'moved_at' in 'field list'"], "endTime": "2025-08-18T06:29:40.129Z", "duration": 2040}], "logs": [{"id": "5b59a7f1-a145-443d-94dc-e8cac95696ec", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:01.370Z", "end_time": "2025-08-18T06:29:07.742Z", "duration_ms": 6372}, {"id": "6dcdec6f-f1a2-48be-a028-29925e4a5cbe", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:07.747Z", "end_time": "2025-08-18T06:29:11.523Z", "duration_ms": 3776}, {"id": "a42d3846-c2a3-493c-a5b2-a235b7dc5af4", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:11.526Z", "end_time": "2025-08-18T06:29:13.640Z", "duration_ms": 2114}, {"id": "4c2a0503-a34d-4923-b36c-17fcb267b2c7", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:13.642Z", "end_time": "2025-08-18T06:29:15.364Z", "duration_ms": 1722}, {"id": "500b0b5f-9e9d-4d23-bb44-39d66c7fb405", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:15.367Z", "end_time": "2025-08-18T06:29:15.667Z", "duration_ms": 300}, {"id": "c6f55d83-10aa-46dc-8cda-0df4027956e9", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:29:15.667Z", "end_time": "2025-08-18T06:29:17.108Z", "duration_ms": 1441}, {"id": "6880961d-b191-49ad-9f8d-065db88996e1", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:17.110Z", "end_time": "2025-08-18T06:29:20.308Z", "duration_ms": 3198}, {"id": "d78d2647-10f7-4885-bdf1-a229332b0f6d", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:20.311Z", "end_time": "2025-08-18T06:29:22.279Z", "duration_ms": 1968}, {"id": "e8902b35-12ae-41c8-969d-eeca6531d5cb", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:22.282Z", "end_time": "2025-08-18T06:29:22.860Z", "duration_ms": 578}, {"id": "5367c42b-3414-492f-8018-5425aed2b660", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:22.860Z", "end_time": "2025-08-18T06:29:24.340Z", "duration_ms": 1480}, {"id": "e7322302-263e-4615-912a-15ad0e4149f3", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:24.343Z", "end_time": "2025-08-18T06:29:24.630Z", "duration_ms": 287}, {"id": "b53c1cc9-6195-4092-8972-638ad199b975", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:24.631Z", "end_time": "2025-08-18T06:29:25.282Z", "duration_ms": 651}, {"id": "15ffdae8-401b-4d27-8c3d-241901707592", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:25.282Z", "end_time": "2025-08-18T06:29:29.098Z", "duration_ms": 3816}, {"id": "13f92c9f-7ed6-4a26-861f-d89e07f6e15e", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:29.101Z", "end_time": "2025-08-18T06:29:29.403Z", "duration_ms": 302}, {"id": "54e0ae9c-a8d1-4828-882c-93e89bdf8df8", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:29.404Z", "end_time": "2025-08-18T06:29:31.092Z", "duration_ms": 1688}, {"id": "75ae57be-dc37-44b0-831f-d1a6fb9fd4c3", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:31.094Z", "end_time": "2025-08-18T06:29:31.411Z", "duration_ms": 317}, {"id": "718819d2-6069-4d41-8cbf-8b539ba2cdf3", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:31.411Z", "end_time": "2025-08-18T06:29:32.161Z", "duration_ms": 750}, {"id": "f38a2a6e-d32a-4c44-baf6-dcfa56b11642", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:32.161Z", "end_time": "2025-08-18T06:29:33.112Z", "duration_ms": 951}, {"id": "7c9395dc-9c39-4abf-905e-41e82c44bb34", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:33.112Z", "end_time": "2025-08-18T06:29:33.866Z", "duration_ms": 754}, {"id": "ea23f015-2e27-4004-b1c0-b07267b15935", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:33.866Z", "end_time": "2025-08-18T06:29:38.087Z", "duration_ms": 4221}, {"id": "395810f8-5df5-4f15-a97f-4087148c5a7c", "migration_id": "af7ab7c2-118a-4dc3-9b00-5842d42b13a3", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:29:38.089Z", "end_time": "2025-08-18T06:29:40.129Z", "duration_ms": 2040}]}