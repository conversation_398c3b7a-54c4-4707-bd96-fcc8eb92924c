<template>
  <div class="conversion-rate-stats">
    <!-- 统计配置 -->
    <n-card title="转化率统计配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>统计维度</label>
            <n-select v-model:value="statsConfig.dimension" :options="dimensionOptions" placeholder="选择统计维度" />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="statsConfig.dateRange" type="daterange" clearable />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>转化类型</label>
            <n-select v-model:value="statsConfig.conversionType" :options="conversionTypeOptions" multiple placeholder="选择转化类型" />
          </div>
          <div class="form-item">
            <label>客户分群</label>
            <n-select v-model:value="statsConfig.segment" :options="segmentOptions" multiple placeholder="选择客户分群" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化率概览 -->
    <n-card title="转化率概览" class="overview-card">
      <div class="conversion-overview">
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#2080f0"><TrendingUpOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.overallRate }}%</div>
              <div class="stat-label">总体转化率</div>
              <div class="stat-change" :class="conversionOverview.overallChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.overallChange >= 0 ? '+' : '' }}{{ conversionOverview.overallChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#18a058"><PersonAddOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.leadRate }}%</div>
              <div class="stat-label">潜客转化率</div>
              <div class="stat-change" :class="conversionOverview.leadChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.leadChange >= 0 ? '+' : '' }}{{ conversionOverview.leadChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#f0a020"><CartOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.salesRate }}%</div>
              <div class="stat-label">销售转化率</div>
              <div class="stat-change" :class="conversionOverview.salesChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.salesChange >= 0 ? '+' : '' }}{{ conversionOverview.salesChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#d03050"><RepeatOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.retentionRate }}%</div>
              <div class="stat-label">客户留存率</div>
              <div class="stat-change" :class="conversionOverview.retentionChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.retentionChange >= 0 ? '+' : '' }}{{ conversionOverview.retentionChange }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化率趋势图 -->
    <n-card title="转化率趋势" class="trend-card">
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 渠道转化率对比 -->
    <n-card title="渠道转化率对比" class="channel-card">
      <div class="chart-container">
        <div ref="channelChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 转化率详细数据 -->
    <n-card title="转化率详细数据" class="details-card">
      <div class="details-table">
        <n-data-table
          :columns="tableColumns"
          :data="conversionDetails"
          :pagination="{
            pageSize: 10,
            showSizePicker: true,
            pageSizes: [10, 20, 50]
          }"
          :row-key="(row: ConversionDetail) => row.id"
        />
      </div>
    </n-card>
    
    <!-- 转化路径分析 -->
    <n-card title="转化路径分析" class="path-card">
      <div class="path-analysis">
        <div class="path-item" v-for="path in conversionPaths" :key="path.id">
          <div class="path-header">
            <div class="path-name">{{ path.name }}</div>
            <div class="path-stats">
              <span class="path-count">{{ path.count }}次</span>
              <span class="path-rate">{{ path.conversionRate }}%</span>
            </div>
          </div>
          
          <div class="path-steps">
            <div class="path-step" v-for="(step, index) in path.steps" :key="index">
              <div class="step-name">{{ step.name }}</div>
              <div class="step-rate">{{ step.rate }}%</div>
              <div v-if="index < path.steps.length - 1" class="step-arrow">→</div>
            </div>
          </div>
          
          <div class="path-insights">
            <div class="insight-item" v-for="insight in path.insights" :key="insight.type">
              <n-tag :type="insight.type === 'bottleneck' ? 'error' : insight.type === 'opportunity' ? 'warning' : 'success'">
                {{ insight.label }}
              </n-tag>
              <span class="insight-text">{{ insight.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化优化建议 -->
    <n-card title="转化优化建议" class="optimization-card">
      <div class="optimization-list">
        <div class="optimization-item" v-for="optimization in optimizations" :key="optimization.id">
          <div class="optimization-header">
            <div class="optimization-icon">
              <n-icon :color="optimization.priority === 'high' ? '#d03050' : optimization.priority === 'medium' ? '#f0a020' : '#18a058'">
                <BulbOutline />
              </n-icon>
            </div>
            <div class="optimization-info">
              <div class="optimization-title">{{ optimization.title }}</div>
              <div class="optimization-description">{{ optimization.description }}</div>
            </div>
            <div class="optimization-priority">
              <n-tag :type="optimization.priority === 'high' ? 'error' : optimization.priority === 'medium' ? 'warning' : 'success'">
                {{ optimization.priority === 'high' ? '高优先级' : optimization.priority === 'medium' ? '中优先级' : '低优先级' }}
              </n-tag>
            </div>
          </div>
          
          <div class="optimization-details">
            <div class="detail-section">
              <h4>当前问题</h4>
              <p>{{ optimization.currentIssue }}</p>
            </div>
            
            <div class="detail-section">
              <h4>解决方案</h4>
              <ul>
                <li v-for="solution in optimization.solutions" :key="solution">{{ solution }}</li>
              </ul>
            </div>
            
            <div class="detail-section">
              <h4>预期效果</h4>
              <div class="expected-results">
                <div class="result-item">
                  <span class="result-label">转化率提升:</span>
                  <span class="result-value">{{ optimization.expectedImprovement.conversionRate }}</span>
                </div>
                <div class="result-item">
                  <span class="result-label">实施周期:</span>
                  <span class="result-value">{{ optimization.expectedImprovement.timeline }}</span>
                </div>
                <div class="result-item">
                  <span class="result-label">投入成本:</span>
                  <span class="result-value">{{ optimization.expectedImprovement.cost }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NTag, NDataTable
} from 'naive-ui'
import {
  TrendingUpOutline, PersonAddOutline, CartOutline, RepeatOutline, BulbOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface ConversionDetail {
  id: string
  channel: string
  source: string
  visitors: number
  leads: number
  customers: number
  conversionRate: number
  avgOrderValue: number
  revenue: number
}

const trendChartRef = ref<HTMLElement>()
const channelChartRef = ref<HTMLElement>()

// 统计配置
const statsConfig = reactive({
  dimension: 'channel',
  dateRange: null,
  conversionType: ['lead', 'sale'],
  segment: []
})

// 转化率概览数据
const conversionOverview = ref({
  overallRate: 6.8,
  overallChange: 1.2,
  leadRate: 25.8,
  leadChange: 3.5,
  salesRate: 6.8,
  salesChange: -0.8,
  retentionRate: 78.5,
  retentionChange: 2.1
})

// 转化路径数据
const conversionPaths = ref([
  {
    id: 1,
    name: '搜索引擎 → 产品页 → 购买',
    count: 1250,
    conversionRate: 8.5,
    steps: [
      { name: '搜索引擎', rate: 100 },
      { name: '产品页面', rate: 65 },
      { name: '加入购物车', rate: 35 },
      { name: '完成购买', rate: 8.5 }
    ],
    insights: [
      { type: 'bottleneck', label: '瓶颈', text: '产品页到购物车转化率偏低' },
      { type: 'opportunity', label: '机会', text: '可优化产品页面设计' }
    ]
  },
  {
    id: 2,
    name: '社交媒体 → 活动页 → 注册',
    count: 890,
    conversionRate: 12.3,
    steps: [
      { name: '社交媒体', rate: 100 },
      { name: '活动页面', rate: 78 },
      { name: '填写表单', rate: 45 },
      { name: '完成注册', rate: 12.3 }
    ],
    insights: [
      { type: 'success', label: '优势', text: '社交媒体引流效果良好' },
      { type: 'bottleneck', label: '瓶颈', text: '表单填写环节流失较多' }
    ]
  },
  {
    id: 3,
    name: '邮件营销 → 落地页 → 咨询',
    count: 650,
    conversionRate: 15.8,
    steps: [
      { name: '邮件营销', rate: 100 },
      { name: '落地页面', rate: 85 },
      { name: '在线咨询', rate: 28 },
      { name: '留下联系方式', rate: 15.8 }
    ],
    insights: [
      { type: 'success', label: '优势', text: '邮件打开率和点击率较高' },
      { type: 'opportunity', label: '机会', text: '可增加咨询引导元素' }
    ]
  }
])

// 转化率详细数据
const conversionDetails = ref<ConversionDetail[]>([
  {
    id: '1',
    channel: '搜索引擎',
    source: 'Google',
    visitors: 15680,
    leads: 4050,
    customers: 1340,
    conversionRate: 8.5,
    avgOrderValue: 285,
    revenue: 381900
  },
  {
    id: '2',
    channel: '社交媒体',
    source: '微信',
    visitors: 8920,
    leads: 2680,
    customers: 1100,
    conversionRate: 12.3,
    avgOrderValue: 220,
    revenue: 242000
  },
  {
    id: '3',
    channel: '直接访问',
    source: '直接输入',
    visitors: 6540,
    leads: 1960,
    customers: 850,
    conversionRate: 13.0,
    avgOrderValue: 310,
    revenue: 263500
  },
  {
    id: '4',
    channel: '邮件营销',
    source: 'EDM',
    visitors: 4120,
    leads: 1650,
    customers: 650,
    conversionRate: 15.8,
    avgOrderValue: 195,
    revenue: 126750
  },
  {
    id: '5',
    channel: '广告投放',
    source: '百度推广',
    visitors: 12350,
    leads: 2470,
    customers: 740,
    conversionRate: 6.0,
    avgOrderValue: 265,
    revenue: 196100
  }
])

// 优化建议
const optimizations = ref([
  {
    id: 1,
    title: '优化搜索引擎转化路径',
    description: '搜索引擎流量大但转化率相对较低，需要优化转化路径。',
    priority: 'high',
    currentIssue: '搜索引擎带来的流量虽然最大，但转化率只有8.5%，低于平均水平。主要问题在于产品页面到购物车的转化环节。',
    solutions: [
      '优化产品页面的用户体验和视觉设计',
      '增加产品评价和用户推荐内容',
      '简化加入购物车的操作流程',
      '添加限时优惠和紧迫感元素'
    ],
    expectedImprovement: {
      conversionRate: '提升至10-12%',
      timeline: '2-3周',
      cost: '中等'
    }
  },
  {
    id: 2,
    title: '扩大邮件营销规模',
    description: '邮件营销转化率最高，应该扩大投入规模。',
    priority: 'medium',
    currentIssue: '邮件营销虽然转化率高达15.8%，但流量规模相对较小，存在扩大空间。',
    solutions: [
      '扩大邮件订阅用户基数',
      '优化邮件内容和发送时机',
      '实施个性化邮件营销策略',
      '增加邮件营销预算投入'
    ],
    expectedImprovement: {
      conversionRate: '保持15%以上',
      timeline: '1-2个月',
      cost: '较低'
    }
  },
  {
    id: 3,
    title: '改善广告投放效果',
    description: '广告投放转化率偏低，需要优化投放策略和落地页。',
    priority: 'high',
    currentIssue: '广告投放的转化率只有6.0%，投入产出比不理想，需要优化广告质量和落地页体验。',
    solutions: [
      '优化广告创意和投放关键词',
      '改善广告落地页的相关性',
      '实施A/B测试优化转化元素',
      '调整投放时间和目标人群'
    ],
    expectedImprovement: {
      conversionRate: '提升至8-9%',
      timeline: '3-4周',
      cost: '较高'
    }
  },
  {
    id: 4,
    title: '提升社交媒体互动',
    description: '社交媒体转化率良好，可进一步提升用户互动和粘性。',
    priority: 'low',
    currentIssue: '社交媒体转化率为12.3%，表现良好，但可以通过增强互动来进一步提升。',
    solutions: [
      '增加社交媒体内容的互动性',
      '开展用户生成内容活动',
      '优化社交媒体客服响应',
      '建立社群运营体系'
    ],
    expectedImprovement: {
      conversionRate: '提升至14-15%',
      timeline: '4-6周',
      cost: '较低'
    }
  }
])

// 表格列配置
const tableColumns = [
  {
    title: '渠道',
    key: 'channel',
    width: 120
  },
  {
    title: '来源',
    key: 'source',
    width: 120
  },
  {
    title: '访问量',
    key: 'visitors',
    width: 100,
    render: (row: ConversionDetail) => row.visitors.toLocaleString()
  },
  {
    title: '潜在客户',
    key: 'leads',
    width: 100,
    render: (row: ConversionDetail) => row.leads.toLocaleString()
  },
  {
    title: '成交客户',
    key: 'customers',
    width: 100,
    render: (row: ConversionDetail) => row.customers.toLocaleString()
  },
  {
    title: '转化率',
    key: 'conversionRate',
    width: 100,
    render: (row: ConversionDetail) => `${row.conversionRate}%`
  },
  {
    title: '客单价',
    key: 'avgOrderValue',
    width: 100,
    render: (row: ConversionDetail) => `¥${row.avgOrderValue}`
  },
  {
    title: '总收入',
    key: 'revenue',
    width: 120,
    render: (row: ConversionDetail) => `¥${row.revenue.toLocaleString()}`
  }
]

// 选项数据
const dimensionOptions = [
  { label: '渠道维度', value: 'channel' },
  { label: '时间维度', value: 'time' },
  { label: '产品维度', value: 'product' },
  { label: '地域维度', value: 'region' }
]

const conversionTypeOptions = [
  { label: '潜客转化', value: 'lead' },
  { label: '销售转化', value: 'sale' },
  { label: '复购转化', value: 'repeat' },
  { label: '推荐转化', value: 'referral' }
]

const segmentOptions = [
  { label: '新客户', value: 'new' },
  { label: '老客户', value: 'existing' },
  { label: '高价值客户', value: 'high_value' },
  { label: '活跃客户', value: 'active' }
]

// 图表渲染方法
const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const overallRate = [5.2, 5.8, 6.1, 5.9, 6.5, 6.8, 6.3, 7.2, 6.9, 7.5, 7.1, 6.8]
  const leadRate = [22.5, 24.1, 25.8, 24.2, 26.5, 27.2, 25.8, 28.5, 27.1, 29.2, 28.0, 25.8]
  const salesRate = [5.2, 5.8, 6.1, 5.9, 6.5, 6.8, 6.3, 7.2, 6.9, 7.5, 7.1, 6.8]
  
  const option = {
    title: {
      text: '转化率趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['总体转化率', '潜客转化率', '销售转化率'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '总体转化率',
        type: 'line',
        data: overallRate,
        lineStyle: { color: '#2080f0', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '潜客转化率',
        type: 'line',
        data: leadRate,
        lineStyle: { color: '#18a058', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '销售转化率',
        type: 'line',
        data: salesRate,
        lineStyle: { color: '#f0a020', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }
  
  chart.setOption(option)
}

const renderChannelChart = () => {
  if (!channelChartRef.value) return
  
  const chart = echarts.init(channelChartRef.value)
  
  const channelData = conversionDetails.value.map(item => ({
    name: item.channel,
    value: item.conversionRate,
    visitors: item.visitors,
    customers: item.customers
  }))
  
  const option = {
    title: {
      text: '各渠道转化率对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        const item = channelData[data.dataIndex]
        return `${data.axisValue}<br/>
                转化率: ${data.value}%<br/>
                访问量: ${item.visitors.toLocaleString()}<br/>
                成交客户: ${item.customers.toLocaleString()}`
      }
    },
    xAxis: {
      type: 'category',
      data: channelData.map(item => item.name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '转化率',
        type: 'bar',
        data: channelData.map(item => item.value),
        itemStyle: {
          color: (params: any) => {
            const colors = ['#2080f0', '#18a058', '#f0a020', '#d03050', '#722ed1']
            return colors[params.dataIndex % colors.length]
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  renderTrendChart()
  renderChannelChart()
})
</script>

<style scoped>
.conversion-rate-stats {
  padding: 24px;
}

.config-card,
.overview-card,
.trend-card,
.channel-card,
.details-card,
.path-card,
.optimization-card {
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #18a058;
}

.stat-change.negative {
  color: #d03050;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.path-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.path-item {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.path-name {
  font-weight: 600;
  font-size: 16px;
  color: #1a1a1a;
}

.path-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.path-count {
  color: #666;
  font-size: 14px;
}

.path-rate {
  font-weight: 600;
  color: #2080f0;
  font-size: 16px;
}

.path-steps {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.path-step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.step-name {
  font-size: 14px;
  color: #333;
}

.step-rate {
  font-size: 12px;
  color: #2080f0;
  font-weight: 500;
}

.step-arrow {
  color: #999;
  font-weight: bold;
}

.path-insights {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.insight-text {
  font-size: 14px;
  color: #666;
}

.optimization-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.optimization-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
}

.optimization-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.optimization-icon {
  font-size: 24px;
}

.optimization-info {
  flex: 1;
}

.optimization-title {
  font-weight: 600;
  font-size: 16px;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.optimization-description {
  color: #666;
  font-size: 14px;
}

.optimization-details {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-section p {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.detail-section ul {
  color: #666;
  line-height: 1.5;
  padding-left: 20px;
}

.detail-section li {
  margin-bottom: 4px;
}

.expected-results {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.result-label {
  color: #666;
  font-size: 14px;
}

.result-value {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}
</style>