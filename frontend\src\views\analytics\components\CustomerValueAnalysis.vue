<template>
  <div class="customer-value-analysis">
    <!-- 分析配置 -->
    <n-card title="分析配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>分析维度</label>
            <n-select v-model:value="analysisConfig.dimension" :options="dimensionOptions" placeholder="选择分析维度" />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="analysisConfig.dateRange" type="daterange" clearable />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>客户分组</label>
            <n-select v-model:value="analysisConfig.groupBy" :options="groupOptions" placeholder="选择分组方式" />
          </div>
          <div class="form-item">
            <label>价值计算方式</label>
            <n-select v-model:value="analysisConfig.valueMethod" :options="valueMethodOptions" placeholder="选择计算方式" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 价值概览 -->
    <n-card title="客户价值概览" class="overview-card">
      <div class="overview-stats">
        <div class="stat-item">
          <div class="stat-icon">
            <n-icon color="#2080f0"><PeopleOutline /></n-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ valueOverview.totalCustomers }}</div>
            <div class="stat-label">总客户数</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <n-icon color="#18a058"><TrendingUpOutline /></n-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ valueOverview.avgValue }}</div>
            <div class="stat-label">平均客户价值</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <n-icon color="#f0a020"><StarOutline /></n-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ valueOverview.highValueCount }}</div>
            <div class="stat-label">高价值客户</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <n-icon color="#d03050"><DiamondOutline /></n-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ valueOverview.totalValue }}</div>
            <div class="stat-label">总客户价值</div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 价值分布分析 -->
    <n-card title="客户价值分布" class="distribution-card">
      <div class="chart-container">
        <div ref="distributionChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 价值趋势分析 -->
    <n-card title="价值趋势分析" class="trend-card">
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 客户分层分析 -->
    <n-card title="客户分层分析" class="segmentation-card">
      <div class="segmentation-content">
        <div class="chart-section">
          <div ref="segmentationChartRef" class="chart"></div>
        </div>
        
        <div class="segment-details">
          <div class="segment-item" v-for="segment in customerSegments" :key="segment.name">
            <div class="segment-header">
              <div class="segment-name">{{ segment.name }}</div>
              <div class="segment-count">{{ segment.count }}人</div>
            </div>
            <div class="segment-stats">
              <div class="segment-stat">
                <span class="stat-label">平均价值:</span>
                <span class="stat-value">¥{{ segment.avgValue }}</span>
              </div>
              <div class="segment-stat">
                <span class="stat-label">价值范围:</span>
                <span class="stat-value">¥{{ segment.minValue }} - ¥{{ segment.maxValue }}</span>
              </div>
              <div class="segment-stat">
                <span class="stat-label">占比:</span>
                <span class="stat-value">{{ segment.percentage }}%</span>
              </div>
            </div>
            <div class="segment-description">{{ segment.description }}</div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 价值提升建议 -->
    <n-card title="价值提升建议" class="suggestions-card">
      <div class="suggestions-list">
        <div class="suggestion-item" v-for="suggestion in valueSuggestions" :key="suggestion.id">
          <div class="suggestion-icon">
            <n-icon :color="suggestion.priority === 'high' ? '#d03050' : suggestion.priority === 'medium' ? '#f0a020' : '#18a058'">
              <BulbOutline />
            </n-icon>
          </div>
          <div class="suggestion-content">
            <div class="suggestion-title">{{ suggestion.title }}</div>
            <div class="suggestion-description">{{ suggestion.description }}</div>
            <div class="suggestion-impact">预期提升: {{ suggestion.expectedImprovement }}</div>
          </div>
          <div class="suggestion-priority">
            <n-tag :type="suggestion.priority === 'high' ? 'error' : suggestion.priority === 'medium' ? 'warning' : 'success'">
              {{ suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级' }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NTag
} from 'naive-ui'
import {
  PeopleOutline, TrendingUpOutline, StarOutline, DiamondOutline, BulbOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const distributionChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
const segmentationChartRef = ref<HTMLElement>()

// 分析配置
const analysisConfig = reactive({
  dimension: 'monetary',
  dateRange: null,
  groupBy: 'value_tier',
  valueMethod: 'total_spent'
})

// 价值概览数据
const valueOverview = ref({
  totalCustomers: 1250,
  avgValue: '12,580',
  highValueCount: 156,
  totalValue: '15,725,000'
})

// 客户分层数据
const customerSegments = ref([
  {
    name: '钻石客户',
    count: 45,
    avgValue: '85,600',
    minValue: '50,000',
    maxValue: '200,000',
    percentage: 3.6,
    description: '最高价值客户群体，贡献了40%的总收入，需要提供VIP服务和个性化体验。'
  },
  {
    name: '黄金客户',
    count: 111,
    avgValue: '28,900',
    minValue: '15,000',
    maxValue: '49,999',
    percentage: 8.9,
    description: '高价值客户群体，具有较强的消费能力和忠诚度，是重点维护对象。'
  },
  {
    name: '白银客户',
    count: 423,
    avgValue: '8,750',
    minValue: '3,000',
    maxValue: '14,999',
    percentage: 33.8,
    description: '中等价值客户群体，有提升潜力，需要通过营销活动激发消费。'
  },
  {
    name: '青铜客户',
    count: 671,
    avgValue: '1,250',
    minValue: '0',
    maxValue: '2,999',
    percentage: 53.7,
    description: '基础客户群体，消费频次较低，需要通过优惠活动提升活跃度。'
  }
])

// 价值提升建议
const valueSuggestions = ref([
  {
    id: 1,
    title: '针对钻石客户推出专属服务',
    description: '为钻石客户提供专属客服、优先配送、定制化产品等VIP服务，提升客户满意度和忠诚度。',
    expectedImprovement: '客户价值提升15-20%',
    priority: 'high'
  },
  {
    id: 2,
    title: '白银客户升级计划',
    description: '通过个性化推荐、限时优惠、会员积分等方式，引导白银客户向黄金客户转化。',
    expectedImprovement: '转化率提升8-12%',
    priority: 'high'
  },
  {
    id: 3,
    title: '青铜客户激活策略',
    description: '设计新手礼包、首购优惠、社交分享奖励等活动，提升青铜客户的购买频次。',
    expectedImprovement: '活跃度提升25-30%',
    priority: 'medium'
  },
  {
    id: 4,
    title: '客户生命周期管理',
    description: '建立客户生命周期模型，在关键节点进行干预，防止客户流失。',
    expectedImprovement: '客户留存率提升10-15%',
    priority: 'medium'
  },
  {
    id: 5,
    title: '交叉销售机会挖掘',
    description: '基于客户购买历史和偏好，推荐相关产品，提升客户单次购买价值。',
    expectedImprovement: '客单价提升5-8%',
    priority: 'low'
  }
])

// 选项数据
const dimensionOptions = [
  { label: '消费金额', value: 'monetary' },
  { label: '购买频次', value: 'frequency' },
  { label: '最近购买', value: 'recency' },
  { label: 'RFM综合', value: 'rfm' }
]

const groupOptions = [
  { label: '价值层级', value: 'value_tier' },
  { label: '行业类型', value: 'industry' },
  { label: '地理区域', value: 'region' },
  { label: '客户来源', value: 'source' }
]

const valueMethodOptions = [
  { label: '总消费金额', value: 'total_spent' },
  { label: '平均订单价值', value: 'avg_order_value' },
  { label: '生命周期价值', value: 'lifetime_value' },
  { label: '预测价值', value: 'predicted_value' }
]

// 图表渲染方法
const renderDistributionChart = () => {
  if (!distributionChartRef.value) return
  
  const chart = echarts.init(distributionChartRef.value)
  
  const option = {
    title: {
      text: '客户价值分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '客户价值分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: [
          { value: 45, name: '钻石客户 (>5万)' },
          { value: 111, name: '黄金客户 (1.5-5万)' },
          { value: 423, name: '白银客户 (0.3-1.5万)' },
          { value: 671, name: '青铜客户 (<0.3万)' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const diamondData = [42, 45, 48, 52, 49, 51, 54, 58, 55, 59, 62, 65]
  const goldData = [98, 105, 112, 108, 115, 122, 118, 125, 132, 128, 135, 142]
  const silverData = [380, 395, 410, 425, 415, 430, 445, 435, 450, 465, 455, 470]
  const bronzeData = [620, 635, 650, 665, 655, 670, 685, 675, 690, 705, 695, 710]
  
  const option = {
    title: {
      text: '客户价值趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['钻石客户', '黄金客户', '白银客户', '青铜客户'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      name: '客户数量'
    },
    series: [
      {
        name: '钻石客户',
        type: 'line',
        data: diamondData,
        smooth: true,
        lineStyle: { color: '#722ed1' }
      },
      {
        name: '黄金客户',
        type: 'line',
        data: goldData,
        smooth: true,
        lineStyle: { color: '#faad14' }
      },
      {
        name: '白银客户',
        type: 'line',
        data: silverData,
        smooth: true,
        lineStyle: { color: '#13c2c2' }
      },
      {
        name: '青铜客户',
        type: 'line',
        data: bronzeData,
        smooth: true,
        lineStyle: { color: '#fa8c16' }
      }
    ]
  }
  
  chart.setOption(option)
}

const renderSegmentationChart = () => {
  if (!segmentationChartRef.value) return
  
  const chart = echarts.init(segmentationChartRef.value)
  
  const option = {
    title: {
      text: '客户分层占比',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}人 ({d}%)'
    },
    series: [
      {
        name: '客户分层',
        type: 'pie',
        radius: '60%',
        data: customerSegments.value.map(segment => ({
          value: segment.count,
          name: segment.name
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  renderDistributionChart()
  renderTrendChart()
  renderSegmentationChart()
})
</script>

<style scoped>
.customer-value-analysis {
  padding: 24px;
}

.config-card,
.overview-card,
.distribution-card,
.trend-card,
.segmentation-card,
.suggestions-card {
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.segmentation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-section {
  height: 400px;
}

.segment-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.segment-name {
  font-weight: 600;
  font-size: 16px;
  color: #1a1a1a;
}

.segment-count {
  font-weight: 500;
  color: #2080f0;
}

.segment-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.segment-stat {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.segment-stat .stat-label {
  color: #666;
}

.segment-stat .stat-value {
  font-weight: 500;
  color: #1a1a1a;
}

.segment-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.suggestion-icon {
  font-size: 24px;
  margin-top: 4px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  font-size: 16px;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.suggestion-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.suggestion-impact {
  font-size: 14px;
  color: #18a058;
  font-weight: 500;
}

.suggestion-priority {
  margin-top: 4px;
}
</style>