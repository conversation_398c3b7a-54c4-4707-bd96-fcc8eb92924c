import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, List, Dict, Any, Union, BinaryIO, TextIO
from datetime import datetime
import tempfile
import zipfile
import json
import csv
from io import StringIO, BytesIO
from enum import Enum


class FileType(str, Enum):
    """文件类型枚举"""
    
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    ARCHIVE = "archive"
    TEXT = "text"
    BINARY = "binary"
    UNKNOWN = "unknown"


class FileInfo:
    """文件信息类"""
    
    def __init__(self, file_path: Union[str, Path]):
        self.path = Path(file_path)
        self._stat = None
    
    @property
    def name(self) -> str:
        """文件名"""
        return self.path.name
    
    @property
    def stem(self) -> str:
        """文件名（不含扩展名）"""
        return self.path.stem
    
    @property
    def suffix(self) -> str:
        """文件扩展名"""
        return self.path.suffix.lower()
    
    @property
    def parent(self) -> Path:
        """父目录"""
        return self.path.parent
    
    @property
    def size(self) -> int:
        """文件大小（字节）"""
        if self._stat is None:
            self._stat = self.path.stat()
        return self._stat.st_size
    
    @property
    def size_human(self) -> str:
        """人性化文件大小"""
        return FileUtils.format_file_size(self.size)
    
    @property
    def created_time(self) -> datetime:
        """创建时间"""
        if self._stat is None:
            self._stat = self.path.stat()
        return datetime.fromtimestamp(self._stat.st_ctime)
    
    @property
    def modified_time(self) -> datetime:
        """修改时间"""
        if self._stat is None:
            self._stat = self.path.stat()
        return datetime.fromtimestamp(self._stat.st_mtime)
    
    @property
    def accessed_time(self) -> datetime:
        """访问时间"""
        if self._stat is None:
            self._stat = self.path.stat()
        return datetime.fromtimestamp(self._stat.st_atime)
    
    @property
    def mime_type(self) -> Optional[str]:
        """MIME类型"""
        return mimetypes.guess_type(str(self.path))[0]
    
    @property
    def file_type(self) -> FileType:
        """文件类型"""
        return FileUtils.get_file_type(self.suffix)
    
    @property
    def is_image(self) -> bool:
        """是否是图片"""
        return self.file_type == FileType.IMAGE
    
    @property
    def is_video(self) -> bool:
        """是否是视频"""
        return self.file_type == FileType.VIDEO
    
    @property
    def is_audio(self) -> bool:
        """是否是音频"""
        return self.file_type == FileType.AUDIO
    
    @property
    def is_document(self) -> bool:
        """是否是文档"""
        return self.file_type == FileType.DOCUMENT
    
    @property
    def is_archive(self) -> bool:
        """是否是压缩包"""
        return self.file_type == FileType.ARCHIVE
    
    @property
    def is_text(self) -> bool:
        """是否是文本文件"""
        return self.file_type == FileType.TEXT
    
    def exists(self) -> bool:
        """文件是否存在"""
        return self.path.exists()
    
    def is_file(self) -> bool:
        """是否是文件"""
        return self.path.is_file()
    
    def is_dir(self) -> bool:
        """是否是目录"""
        return self.path.is_dir()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "stem": self.stem,
            "suffix": self.suffix,
            "path": str(self.path),
            "parent": str(self.parent),
            "size": self.size,
            "size_human": self.size_human,
            "created_time": self.created_time.isoformat(),
            "modified_time": self.modified_time.isoformat(),
            "accessed_time": self.accessed_time.isoformat(),
            "mime_type": self.mime_type,
            "file_type": self.file_type.value,
            "is_file": self.is_file(),
            "is_dir": self.is_dir()
        }


class FileUtils:
    """文件工具类"""
    
    # 文件类型映射
    FILE_TYPE_MAPPING = {
        # 图片
        ".jpg": FileType.IMAGE, ".jpeg": FileType.IMAGE, ".png": FileType.IMAGE,
        ".gif": FileType.IMAGE, ".bmp": FileType.IMAGE, ".svg": FileType.IMAGE,
        ".webp": FileType.IMAGE, ".ico": FileType.IMAGE, ".tiff": FileType.IMAGE,
        
        # 视频
        ".mp4": FileType.VIDEO, ".avi": FileType.VIDEO, ".mov": FileType.VIDEO,
        ".wmv": FileType.VIDEO, ".flv": FileType.VIDEO, ".webm": FileType.VIDEO,
        ".mkv": FileType.VIDEO, ".m4v": FileType.VIDEO, ".3gp": FileType.VIDEO,
        
        # 音频
        ".mp3": FileType.AUDIO, ".wav": FileType.AUDIO, ".flac": FileType.AUDIO,
        ".aac": FileType.AUDIO, ".ogg": FileType.AUDIO, ".wma": FileType.AUDIO,
        ".m4a": FileType.AUDIO, ".opus": FileType.AUDIO,
        
        # 文档
        ".pdf": FileType.DOCUMENT, ".doc": FileType.DOCUMENT, ".docx": FileType.DOCUMENT,
        ".xls": FileType.DOCUMENT, ".xlsx": FileType.DOCUMENT, ".ppt": FileType.DOCUMENT,
        ".pptx": FileType.DOCUMENT, ".odt": FileType.DOCUMENT, ".ods": FileType.DOCUMENT,
        ".odp": FileType.DOCUMENT, ".rtf": FileType.DOCUMENT,
        
        # 压缩包
        ".zip": FileType.ARCHIVE, ".rar": FileType.ARCHIVE, ".7z": FileType.ARCHIVE,
        ".tar": FileType.ARCHIVE, ".gz": FileType.ARCHIVE, ".bz2": FileType.ARCHIVE,
        ".xz": FileType.ARCHIVE, ".tar.gz": FileType.ARCHIVE, ".tar.bz2": FileType.ARCHIVE,
        
        # 文本
        ".txt": FileType.TEXT, ".md": FileType.TEXT, ".csv": FileType.TEXT,
        ".json": FileType.TEXT, ".xml": FileType.TEXT, ".yaml": FileType.TEXT,
        ".yml": FileType.TEXT, ".ini": FileType.TEXT, ".cfg": FileType.TEXT,
        ".log": FileType.TEXT, ".py": FileType.TEXT, ".js": FileType.TEXT,
        ".html": FileType.TEXT, ".css": FileType.TEXT, ".sql": FileType.TEXT,
    }
    
    @classmethod
    def get_file_type(cls, extension: str) -> FileType:
        """根据扩展名获取文件类型"""
        return cls.FILE_TYPE_MAPPING.get(extension.lower(), FileType.UNKNOWN)
    
    @classmethod
    def format_file_size(cls, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @classmethod
    def format_size(cls, size_bytes: int) -> str:
        """格式化文件大小（别名方法）"""
        return cls.format_file_size(size_bytes)
    
    @classmethod
    def get_file_info(cls, file_path: Union[str, Path]) -> FileInfo:
        """获取文件信息"""
        return FileInfo(file_path)
    
    @staticmethod
    def ensure_dir(dir_path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(dir_path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def ensure_dir_exists(dir_path: Union[str, Path]) -> None:
        """确保目录存在（无返回值版本）"""
        path = Path(dir_path)
        path.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def copy_file(
        cls,
        src: Union[str, Path],
        dst: Union[str, Path],
        overwrite: bool = False
    ) -> Path:
        """复制文件"""
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            raise FileNotFoundError(f"Source file not found: {src_path}")
        
        if dst_path.exists() and not overwrite:
            raise FileExistsError(f"Destination file already exists: {dst_path}")
        
        # 确保目标目录存在
        cls.ensure_dir(dst_path.parent)
        
        shutil.copy2(src_path, dst_path)
        return dst_path
    
    @classmethod
    def move_file(
        cls,
        src: Union[str, Path],
        dst: Union[str, Path],
        overwrite: bool = False
    ) -> Path:
        """移动文件"""
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            raise FileNotFoundError(f"Source file not found: {src_path}")
        
        if dst_path.exists() and not overwrite:
            raise FileExistsError(f"Destination file already exists: {dst_path}")
        
        # 确保目标目录存在
        cls.ensure_dir(dst_path.parent)
        
        shutil.move(str(src_path), str(dst_path))
        return dst_path
    
    @classmethod
    def delete_file(cls, file_path: Union[str, Path]) -> bool:
        """删除文件"""
        path = Path(file_path)
        
        if not path.exists():
            return False
        
        if path.is_file():
            path.unlink()
        elif path.is_dir():
            shutil.rmtree(path)
        
        return True
    
    @classmethod
    def read_text(
        cls,
        file_path: Union[str, Path],
        encoding: str = "utf-8"
    ) -> str:
        """读取文本文件"""
        return Path(file_path).read_text(encoding=encoding)
    
    @classmethod
    def write_text(
        cls,
        file_path: Union[str, Path],
        content: str,
        encoding: str = "utf-8",
        create_dirs: bool = True
    ) -> Path:
        """写入文本文件"""
        path = Path(file_path)
        
        if create_dirs:
            cls.ensure_dir(path.parent)
        
        path.write_text(content, encoding=encoding)
        return path
    
    @classmethod
    def read_bytes(cls, file_path: Union[str, Path]) -> bytes:
        """读取二进制文件"""
        return Path(file_path).read_bytes()
    
    @classmethod
    def write_bytes(
        cls,
        file_path: Union[str, Path],
        content: bytes,
        create_dirs: bool = True
    ) -> Path:
        """写入二进制文件"""
        path = Path(file_path)
        
        if create_dirs:
            cls.ensure_dir(path.parent)
        
        path.write_bytes(content)
        return path
    
    @classmethod
    def read_json(
        cls,
        file_path: Union[str, Path],
        encoding: str = "utf-8"
    ) -> Any:
        """读取JSON文件"""
        content = cls.read_text(file_path, encoding)
        return json.loads(content)
    
    @classmethod
    def write_json(
        cls,
        file_path: Union[str, Path],
        data: Any,
        encoding: str = "utf-8",
        indent: int = 2,
        ensure_ascii: bool = False,
        create_dirs: bool = True
    ) -> Path:
        """写入JSON文件"""
        content = json.dumps(
            data,
            indent=indent,
            ensure_ascii=ensure_ascii,
            default=str
        )
        return cls.write_text(file_path, content, encoding, create_dirs)
    
    @classmethod
    def read_csv(
        cls,
        file_path: Union[str, Path],
        encoding: str = "utf-8",
        delimiter: str = ","
    ) -> List[Dict[str, str]]:
        """读取CSV文件"""
        content = cls.read_text(file_path, encoding)
        reader = csv.DictReader(StringIO(content), delimiter=delimiter)
        return list(reader)
    
    @classmethod
    def write_csv(
        cls,
        file_path: Union[str, Path],
        data: List[Dict[str, Any]],
        encoding: str = "utf-8",
        delimiter: str = ",",
        create_dirs: bool = True
    ) -> Path:
        """写入CSV文件"""
        if not data:
            return cls.write_text(file_path, "", encoding, create_dirs)
        
        output = StringIO()
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=delimiter)
        
        writer.writeheader()
        writer.writerows(data)
        
        content = output.getvalue()
        return cls.write_text(file_path, content, encoding, create_dirs)
    
    @classmethod
    def calculate_hash(
        cls,
        file_path: Union[str, Path],
        algorithm: str = "md5"
    ) -> str:
        """计算文件哈希值"""
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    @classmethod
    def list_files(
        cls,
        dir_path: Union[str, Path],
        pattern: str = "*",
        recursive: bool = False,
        include_dirs: bool = False
    ) -> List[Path]:
        """列出目录中的文件"""
        path = Path(dir_path)
        
        if not path.exists() or not path.is_dir():
            return []
        
        if recursive:
            files = path.rglob(pattern)
        else:
            files = path.glob(pattern)
        
        result = []
        for file in files:
            if file.is_file() or (include_dirs and file.is_dir()):
                result.append(file)
        
        return sorted(result)
    
    @classmethod
    def get_temp_file(
        cls,
        suffix: str = "",
        prefix: str = "tmp",
        dir: Optional[Union[str, Path]] = None
    ) -> Path:
        """创建临时文件"""
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
        os.close(fd)  # 关闭文件描述符
        return Path(path)
    
    @classmethod
    def get_temp_dir(
        cls,
        prefix: str = "tmp",
        dir: Optional[Union[str, Path]] = None
    ) -> Path:
        """创建临时目录"""
        return Path(tempfile.mkdtemp(prefix=prefix, dir=dir))
    
    @classmethod
    def create_zip(
        cls,
        zip_path: Union[str, Path],
        files: List[Union[str, Path]],
        base_dir: Optional[Union[str, Path]] = None
    ) -> Path:
        """创建ZIP压缩包"""
        zip_path = Path(zip_path)
        cls.ensure_dir(zip_path.parent)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in files:
                file_path = Path(file_path)
                
                if file_path.exists():
                    if base_dir:
                        arcname = file_path.relative_to(base_dir)
                    else:
                        arcname = file_path.name
                    
                    zipf.write(file_path, arcname)
        
        return zip_path
    
    @classmethod
    def extract_zip(
        cls,
        zip_path: Union[str, Path],
        extract_dir: Union[str, Path]
    ) -> Path:
        """解压ZIP文件"""
        zip_path = Path(zip_path)
        extract_dir = Path(extract_dir)
        
        cls.ensure_dir(extract_dir)
        
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            zipf.extractall(extract_dir)
        
        return extract_dir
    
    @classmethod
    def is_safe_path(cls, path: Union[str, Path], base_dir: Union[str, Path]) -> bool:
        """检查路径是否安全（防止路径遍历攻击）"""
        try:
            path = Path(path).resolve()
            base_dir = Path(base_dir).resolve()
            return str(path).startswith(str(base_dir))
        except (OSError, ValueError):
            return False
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        # 移除或替换不安全字符
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # 移除控制字符
        filename = ''.join(char for char in filename if ord(char) >= 32)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename.strip()


# 便捷函数
def ensure_dir_exists(path: str) -> None:
    """确保目录存在"""
    FileUtils.ensure_dir_exists(path)

def safe_join(*paths: str) -> str:
    """安全路径拼接"""
    return FileUtils.safe_join(*paths)

def clean_filename(filename: str) -> str:
    """清理文件名"""
    return FileUtils.clean_filename(filename)

def format_file_size(size: int) -> str:
    """格式化文件大小"""
    return FileUtils.format_size(size)

def get_file_info(file_path: str) -> FileInfo:
    """获取文件信息"""
    return FileInfo(file_path)