# 数据库迁移与客户管理重构 - 共识文档

## 1. 明确的需求描述

### 1.1 核心需求

1. **数据库迁移**: 将项目从 Supabase 迁移到本地 MySQL 数据库
2. **数据清理**: 删除所有测试数据，保留基础配置数据
3. **界面重构**: 在选项管理页面新增客户管理标签页
4. **功能迁移**: 将客户表的增删改查功能移动到新的标签页中

### 1.2 具体要求

* 保持现有客户管理功能的完整性

* 保持现有的用户体验和操作流程

* 确保数据的完整性和一致性

* 维护现有的权限控制机制

## 2. 技术实现方案

### 2.1 数据库迁移方案

#### 2.1.1 MySQL 数据库配置

```javascript
// 更新 DatabaseManager.js 支持 MySQL
const mysql = require('mysql2/promise')

class MySQLManager {
  constructor(config) {
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'yysh_miniprogram',
      charset: process.env.DB_CHARSET || 'utf8mb4',
      timezone: process.env.DB_TIMEZONE || '+08:00'
    }
  }
}
```

#### 2.1.2 数据表迁移

* 将 Supabase PostgreSQL 表结构转换为 MySQL DDL

* 数据类型映射: UUID -> VARCHAR(36), TIMESTAMP WITH TIME ZONE -> DATETIME

* 保持外键约束和索引结构

#### 2.1.3 数据迁移策略

* 保留基础数据: option\_categories, option\_items, 系统用户

* 清理测试数据: 测试客户、测试跟进记录、测试营销数据

* 创建数据迁移脚本: `scripts/migrate-to-mysql.ts`

### 2.2 API 接口调整方案

#### 2.2.1 新建客户管理 API 路由

```typescript
// api/routes/customer.ts
import express from 'express'
import { MySQLManager } from '../database/MySQLManager'

const router = express.Router()

// 客户管理 CRUD 接口
router.get('/customers', getCustomers)
router.post('/customers', createCustomer)
router.put('/customers/:id', updateCustomer)
router.delete('/customers/:id', deleteCustomer)
router.post('/customers/batch-delete', batchDeleteCustomers)

export default router
```

#### 2.2.2 数据库连接更新

* 更新所有 API 路由使用 MySQL 连接

* 统一错误处理和响应格式

* 保持现有的 ApiResponse 接口规范

### 2.3 前端界面重构方案

#### 2.3.1 选项管理页面结构调整

```vue
<!-- OptionsManagement.vue -->
<template>
  <div class="options-management">
    <n-card class="options-card">
      <n-tabs type="line" animated>
        <n-tab-pane name="categories" tab="选项分类管理">
          <OptionCategoriesManagement />
        </n-tab-pane>
        <n-tab-pane name="items" tab="选项数据管理">
          <OptionItemsManagement />
        </n-tab-pane>
        <n-tab-pane name="customers" tab="客户管理">
          <CustomerManagement />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>
```

#### 2.3.2 客户管理组件设计

```vue
<!-- components/CustomerManagement.vue -->
<template>
  <div class="customer-management">
    <!-- 复用现有的客户列表组件逻辑 -->
    <CustomerListTable 
      :show-page-header="false"
      :embedded-mode="true"
    />
  </div>
</template>
```

#### 2.3.3 组件复用策略

* 提取 CustomerList.vue 的核心逻辑为可复用组件

* 支持嵌入模式和独立页面模式

* 保持现有的搜索、筛选、批量操作功能

### 2.4 路由和导航调整

#### 2.4.1 路由配置更新

```typescript
// router/index.ts
const routes = [
  {
    path: '/settings/options',
    name: 'OptionsManagement',
    component: () => import('@/views/settings/OptionsManagement.vue'),
    meta: { 
      title: '选项管理',
      requiresAuth: true,
      permissions: ['settings:options']
    }
  },
  // 保留原有客户管理路由作为重定向
  {
    path: '/customer/list',
    redirect: '/settings/options?tab=customers'
  }
]
```

#### 2.4.2 菜单配置调整

* 将客户管理菜单项指向选项管理页面的客户标签页

* 或者移除独立的客户管理菜单项

* 更新面包屑导航

## 3. 技术约束和集成方案

### 3.1 技术约束

* **数据库**: 使用 MySQL 8.0+ 版本

* **连接池**: 使用 mysql2/promise 连接池管理

* **事务支持**: 保持现有的事务处理机制

* **类型安全**: 保持 TypeScript 类型定义的完整性

### 3.2 集成方案

* **渐进式迁移**: 先迁移数据库，再调整前端界面

* **向后兼容**: 保持现有 API 接口的兼容性

* **错误处理**: 统一的错误处理和用户提示机制

### 3.3 性能考虑

* **连接池配置**: 合理配置 MySQL 连接池大小

* **查询优化**: 保持现有的分页和索引优化

* **缓存策略**: 保持现有的前端状态管理缓存

## 4. 任务边界限制

### 4.1 包含的工作范围

1. **数据库层**:

   * 创建 MySQL 数据库管理器

   * 编写数据库迁移脚本

   * 更新所有数据库连接配置

2. **API 层**:

   * 新建客户管理 API 路由

   * 更新现有 API 使用 MySQL 连接

   * 保持 API 响应格式一致性

3. **前端层**:

   * 重构选项管理页面添加客户管理标签页

   * 创建可复用的客户管理组件

   * 更新路由和导航配置

4. **配置层**:

   * 更新环境变量配置

   * 更新项目依赖

   * 更新部署配置

### 4.2 不包含的工作范围

* 不修改客户管理的核心业务逻辑

* 不改变现有的权限控制机制

* 不修改其他业务模块(跟进记录、营销管理等)

* 不改变现有的数据模型结构

## 5. 验收标准

### 5.1 功能验收标准

1. **数据库迁移**:

   * [ ] MySQL 数据库连接正常

   * [ ] 所有表结构正确创建

   * [ ] 基础数据正确迁移

   * [ ] 测试数据已清理

2. **API 接口**:

   * [ ] 客户管理 API 正常工作

   * [ ] 所有 CRUD 操作正常

   * [ ] 分页和搜索功能正常

   * [ ] 批量操作功能正常

3. **前端界面**:

   * [ ] 选项管理页面新增客户管理标签页

   * [ ] 客户列表显示正常

   * [ ] 搜索和筛选功能正常

   * [ ] 新增、编辑、删除功能正常

   * [ ] 批量操作功能正常

4. **整体集成**:

   * [ ] 项目编译通过

   * [ ] TypeScript 类型检查通过

   * [ ] 所有功能测试通过

   * [ ] 性能无明显下降

### 5.2 质量验收标准

* **代码质量**: 通过 ESLint 和 Prettier 检查

* **类型安全**: 通过 TypeScript 严格模式检查

* **测试覆盖**: 核心功能有对应的测试用例

* **文档完整**: 更新相关的技术文档

### 5.3 用户体验验收标准

* **操作流畅**: 页面切换和数据加载流畅

* **界面一致**: 保持与现有界面风格一致

* **错误提示**: 友好的错误提示和处理

* **响应速度**: 数据库操作响应时间在可接受范围内

## 6. 风险评估和缓解措施

### 6.1 技术风险

* **数据迁移风险**: 制定详细的数据备份和回滚方案

* **兼容性风险**: 充分测试 MySQL 与现有代码的兼容性

* **性能风险**: 监控数据库性能，及时优化查询

### 6.2 业务风险

* **功能缺失风险**: 详细对比迁移前后的功能完整性

* **用户体验风险**: 保持界面操作的一致性和直观性

* **数据安全风险**: 确保数据迁移过程的安全性

### 6.3 缓解措施

* **分阶段实施**: 先完成数据库迁移，再进行界面重构

* **充分测试**: 每个阶段都进行充分的功能和集成测试

* **备份机制**: 建立完善的数据备份和恢复机制

* **回滚方案**: 准备快速回滚到原有方案的应急预案

