<template>
  <div class="customer-stage-flow">
    <!-- 阶段进度条 -->
    <div class="stage-progress-bar">
      <div class="progress-container">
        <div 
          v-for="(stage, index) in stages" 
          :key="stage.key"
          class="stage-item"
          :class="{
            'active': currentStage >= index,
            'current': currentStage === index,
            'completed': currentStage > index
          }"
        >
          <div class="stage-circle">
            <n-icon v-if="currentStage > index" size="16">
              <CheckmarkOutline />
            </n-icon>
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div class="stage-label">{{ stage.label }}</div>
        </div>
        <div class="progress-line" :style="{ width: progressWidth }"></div>
      </div>
    </div>

    <!-- 阶段内容区域 -->
    <div class="stage-content">
      <n-tabs v-model:value="activeStageTab" type="card" animated>
        <!-- 跟进阶段 -->
        <n-tab-pane name="follow" tab="跟进阶段">
          <div class="stage-panel">
            <div class="panel-header">
              <h3>客户跟进记录</h3>
              <n-button type="primary" size="small" @click="handleAddFollowRecord">
                <template #icon>
                  <n-icon><Add /></n-icon>
                </template>
                新增跟进
              </n-button>
            </div>
            
            <div class="follow-records">
              <n-empty v-if="followRecords.length === 0" description="暂无跟进记录">
                <template #extra>
                  <n-button size="small" @click="handleAddFollowRecord">创建首次跟进</n-button>
                </template>
              </n-empty>
              
              <div v-else class="records-list">
                <n-card 
                  v-for="(record, index) in followRecords" 
                  :key="record.id || index"
                  class="record-item"
                  :class="{ 'latest': index === 0 }"
                >
                  <template #header>
                    <div class="record-header">
                      <span class="record-title">第{{ index + 1 }}次跟进</span>
                      <n-tag size="small" :type="getRecordTagType(record.status)">{{ record.status }}</n-tag>
                    </div>
                  </template>
                  <template #header-extra>
                    <n-button-group size="small">
                      <n-button @click="handleEditRecord(record)">编辑</n-button>
                      <n-button type="error" @click="handleDeleteRecord(record.id)">删除</n-button>
                    </n-button-group>
                  </template>
                  
                  <div class="record-content">
                    <n-descriptions :column="2" label-placement="left" size="small">
                      <n-descriptions-item label="跟进时间">{{ formatDateTime(record.follow_time) }}</n-descriptions-item>
                      <n-descriptions-item label="跟进方式">{{ record.type }}</n-descriptions-item>
                      <n-descriptions-item label="备注">{{ record.remark || '-' }}</n-descriptions-item>
                        <n-descriptions-item label="下次跟进">{{ record.next_follow_time ? formatDateTime(record.next_follow_time) : '-' }}</n-descriptions-item>
                    </n-descriptions>
                    <div class="follow-content">
                      <strong>跟进内容：</strong>
                      <p>{{ record.content }}</p>
                    </div>
                  </div>
                </n-card>
              </div>
            </div>
          </div>
        </n-tab-pane>

        <!-- 见面阶段 -->
        <n-tab-pane name="meeting" tab="见面阶段">
          <div class="stage-panel">
            <div class="panel-header">
              <h3>客户见面记录</h3>
              <n-button type="primary" size="small" @click="handleAddMeetingRecord">
                <template #icon>
                  <n-icon><Add /></n-icon>
                </template>
                新增见面
              </n-button>
            </div>
            
            <div class="meeting-records">
              <n-empty v-if="meetingRecords.length === 0" description="暂无见面记录">
                <template #extra>
                  <n-button size="small" @click="handleAddMeetingRecord">记录首次见面</n-button>
                </template>
              </n-empty>
              
              <div v-else class="records-list">
                <n-card 
                  v-for="(record, index) in meetingRecords" 
                  :key="record.id || index"
                  class="record-item"
                  :class="{ 'latest': index === 0 }"
                >
                  <template #header>
                    <div class="record-header">
                      <span class="record-title">{{ record.type }}见面</span>
                      <n-tag size="small" :type="getMeetingTagType(record.status)">{{ record.status }}</n-tag>
                    </div>
                  </template>
                  <template #header-extra>
                    <n-button-group size="small">
                      <n-button @click="handleEditMeeting(record)">编辑</n-button>
                      <n-button type="error" @click="handleDeleteMeeting(record.id)">删除</n-button>
                    </n-button-group>
                  </template>
                  
                  <div class="record-content">
                    <n-descriptions :column="2" label-placement="left" size="small">
                      <n-descriptions-item label="见面时间">{{ record.meeting_time ? formatDateTime(record.meeting_time) : '-' }}</n-descriptions-item>
                      <n-descriptions-item label="见面地点">{{ record.location }}</n-descriptions-item>
                      <n-descriptions-item label="参与人员">{{ record.participants.join('、') }}</n-descriptions-item>
                      <n-descriptions-item label="会议时长">{{ record.duration }}分钟</n-descriptions-item>
                      <n-descriptions-item label="备注">{{ record.remark || '-' }}</n-descriptions-item>
                    </n-descriptions>
                    <div class="meeting-content">
                      <strong>见面内容：</strong>
                      <p>{{ record.content }}</p>
                    </div>
                    <div v-if="record.attachments && record.attachments.length > 0" class="attachments">
                      <strong>相关附件：</strong>
                      <div class="attachment-list">
                        <n-image 
                          v-for="(attachment, idx) in record.attachments" 
                          :key="idx"
                          :src="attachment" 
                          width="100" 
                          height="100"
                          object-fit="cover"
                          class="attachment-image"
                        />
                      </div>
                    </div>
                  </div>
                </n-card>
              </div>
            </div>
          </div>
        </n-tab-pane>

        <!-- 成交阶段 -->
        <n-tab-pane name="deal" tab="成交阶段">
          <div class="stage-panel">
            <div class="panel-header">
              <h3>成交记录</h3>
              <n-button 
                v-if="!dealRecord" 
                type="primary" 
                size="small" 
                @click="handleAddDealRecord"
              >
                <template #icon>
                  <n-icon><Add /></n-icon>
                </template>
                记录成交
              </n-button>
              <n-button 
                v-else
                size="small" 
                @click="handleEditDeal"
              >
                <template #icon>
                  <n-icon><Edit /></n-icon>
                </template>
                编辑成交信息
              </n-button>
            </div>
            
            <div class="deal-record">
              <n-empty v-if="!dealRecord" description="暂无成交记录">
                <template #extra>
                  <n-button size="small" @click="handleAddDealRecord">记录成交信息</n-button>
                </template>
              </n-empty>
              
              <n-card v-else class="deal-card">
                <template #header>
                  <div class="deal-header">
                    <span class="deal-title">成交信息</span>
                    <n-tag size="large" type="success">已成交</n-tag>
                  </div>
                </template>
                
                <div class="deal-content">
                  <n-descriptions :column="2" label-placement="left">
                    <n-descriptions-item label="签单时间">{{ dealRecord.contractTime ? formatDateTime(dealRecord.contractTime) : '-' }}</n-descriptions-item>
                    <n-descriptions-item label="签单金额">{{ dealRecord.contractAmount ? `¥${dealRecord.contractAmount.toLocaleString()}` : '-' }}</n-descriptions-item>
                    <n-descriptions-item label="签单套餐">{{ dealRecord.packageType }}</n-descriptions-item>
                    <n-descriptions-item label="付款方式">{{ dealRecord.paymentMethod }}</n-descriptions-item>
                    <n-descriptions-item label="设计师">{{ dealRecord.designer }}</n-descriptions-item>
                    <n-descriptions-item label="销售顾问">{{ dealRecord.sales }}</n-descriptions-item>
                    <n-descriptions-item label="合同时间">{{ dealRecord.contractTime ? formatDateTime(dealRecord.contractTime) : '-' }}</n-descriptions-item>
              <n-descriptions-item label="项目工期">{{ dealRecord.projectDuration ? dealRecord.projectDuration + '天' : '-' }}</n-descriptions-item>
                  </n-descriptions>
                  
                  <div v-if="dealRecord.remark" class="contract-notes">
                    <strong>合同备注：</strong>
                    <p>{{ dealRecord.remark }}</p>
                  </div>
                  
                  <div v-if="dealRecord.attachments && dealRecord.attachments.length > 0" class="transaction-screenshots">
                    <strong>相关附件：</strong>
                    <div class="screenshot-list">
                      <n-image 
                        v-for="(attachment, idx) in dealRecord.attachments" 
                        :key="idx"
                        :src="attachment" 
                        width="150" 
                        height="150"
                        object-fit="cover"
                        class="screenshot-image"
                      />
                    </div>
                  </div>
                </div>
              </n-card>
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 跟进记录编辑模态框 -->
    <n-modal
      v-model:show="showFollowModal"
      title="跟进记录"
      preset="card"
      style="width: 600px; max-width: 90vw"
      :bordered="false"
      size="huge"
    >
      <FollowRecordForm
        ref="followFormRef"
        v-model="editingFollowRecord"
        :is-edit="!!editingFollowRecord?.id"
        :loading="formLoading"
        @submit="handleFollowSubmit"
      />
      <template #action>
        <n-space>
          <n-button @click="showFollowModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleFollowSubmit">
            {{ editingFollowRecord?.id ? '更新' : '保存' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 见面记录编辑模态框 -->
    <n-modal
      v-model:show="showMeetingModal"
      title="见面记录"
      preset="card"
      style="width: 700px; max-width: 90vw"
      :bordered="false"
      size="huge"
    >
      <MeetingRecordForm
        ref="meetingFormRef"
        v-model="editingMeetingRecord"
        :is-edit="!!editingMeetingRecord?.id"
        :loading="formLoading"
        @submit="handleMeetingSubmit"
      />
      <template #action>
        <n-space>
          <n-button @click="showMeetingModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleMeetingSubmit">
            {{ editingMeetingRecord?.id ? '更新' : '保存' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 成交记录编辑模态框 -->
    <n-modal
      v-model:show="showDealModal"
      title="成交记录"
      preset="card"
      style="width: 700px; max-width: 90vw"
      :bordered="false"
      size="huge"
    >
      <DealRecordForm
        ref="dealFormRef"
        v-model="editingDealRecord"
        :is-edit="!!dealRecord"
        :loading="formLoading"
        @submit="handleDealSubmit"
      />
      <template #action>
        <n-space>
          <n-button @click="showDealModal = false">取消</n-button>
          <n-button type="primary" :loading="formLoading" @click="handleDealSubmit">
            {{ dealRecord ? '更新' : '保存' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NTabs,
  NTabPane,
  NCard,
  NButton,
  NButtonGroup,
  NIcon,
  NTag,
  NEmpty,
  NDescriptions,
  NDescriptionsItem,
  NImage,
  NModal,
  NSpace,
  useMessage
} from 'naive-ui'
import { Add, CheckmarkOutline, CreateOutline as Edit } from '@vicons/ionicons5'
import FollowRecordForm from './FollowRecordForm.vue'
import MeetingRecordForm from './MeetingRecordForm.vue'
import DealRecordForm from './DealRecordForm.vue'
import type { DealRecord, FollowRecord, MeetingRecord } from '@/types'

// Props
interface Props {
  customerId: number
}

const props = defineProps<Props>()

// 响应式数据
const message = useMessage()
const activeStageTab = ref('follow')
const currentStage = ref(0) // 0: 跟进, 1: 见面, 2: 成交

// 阶段定义
const stages = [
  { key: 'follow', label: '跟进阶段' },
  { key: 'meeting', label: '见面阶段' },
  { key: 'deal', label: '成交阶段' }
]

// 数据
const followRecords = ref<FollowRecord[]>([])
const meetingRecords = ref<MeetingRecord[]>([])
const dealRecord = ref<DealRecord | null>(null)

// 模态框状态
const showFollowModal = ref(false)
const showMeetingModal = ref(false)
const showDealModal = ref(false)
const formLoading = ref(false)

// 编辑数据
const editingFollowRecord = ref<FollowRecord | null>(null)
const editingMeetingRecord = ref<MeetingRecord | null>(null)
const editingDealRecord = ref<DealRecord | null>(null)

// 表单引用
const followFormRef = ref()
const meetingFormRef = ref()
const dealFormRef = ref()

// 计算属性
const progressWidth = computed(() => {
  return `${(currentStage.value / (stages.length - 1)) * 100}%`
})

// 工具函数
const formatDateTime = (dateTime: string | number) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getRecordTagType = (status: string) => {
  const typeMap: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
    '待跟进': 'warning',
    '跟进中': 'info',
    '已完成': 'success',
    '已转化': 'primary'
  }
  return typeMap[status] || 'default'
}

const getMeetingTagType = (result: string) => {
  const typeMap: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
    '成功': 'success',
    '一般': 'info',
    '失败': 'error',
    '待定': 'warning'
  }
  return typeMap[result] || 'default'
}

// 事件处理
const handleAddFollowRecord = () => {
  editingFollowRecord.value = {
    id: Date.now(),
    customer_id: props.customerId,
    customer_name: '',
    customerId: props.customerId,
    follow_time: new Date().toISOString(),
    type: '电话',
    content: '',
    status: '跟进中',
    stage: 'follow',
    created_by: 1,
    created_by_name: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showFollowModal.value = true
}

const handleEditRecord = (record: FollowRecord) => {
  editingFollowRecord.value = { ...record }
  showFollowModal.value = true
}

const handleDeleteRecord = (id?: number) => {
  if (!id) return
  const index = followRecords.value.findIndex(r => r.id === id)
  if (index > -1) {
    followRecords.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleFollowSubmit = async () => {
  if (!editingFollowRecord.value) return
  
  formLoading.value = true
  try {
    if (editingFollowRecord.value.id) {
      // 更新
      const index = followRecords.value.findIndex(r => r.id === editingFollowRecord.value!.id)
      if (index > -1) {
        followRecords.value[index] = { ...editingFollowRecord.value }
      }
      message.success('更新成功')
    } else {
      // 新增
      editingFollowRecord.value.id = Date.now()
      followRecords.value.unshift({ ...editingFollowRecord.value })
      message.success('保存成功')
    }
    showFollowModal.value = false
    updateCurrentStage()
  } catch (error) {
    message.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleAddMeetingRecord = () => {
  editingMeetingRecord.value = {
    id: Date.now(),
    title: '客户见面',
    customer_id: props.customerId,
    customer_name: '',
    customerId: props.customerId,
    type: '初次',
    meeting_time: Date.now(),
    duration: 60,
    location: '',
    participants: [],
    content: '',
    status: '待定',
    created_by: 1,
    created_by_name: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showMeetingModal.value = true
}

const handleEditMeeting = (record: MeetingRecord) => {
  editingMeetingRecord.value = { ...record }
  showMeetingModal.value = true
}

const handleDeleteMeeting = (id?: number) => {
  if (!id) return
  const index = meetingRecords.value.findIndex(r => r.id === id)
  if (index > -1) {
    meetingRecords.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleMeetingSubmit = async () => {
  if (!editingMeetingRecord.value) return
  
  formLoading.value = true
  try {
    if (editingMeetingRecord.value.id) {
      // 更新
      const index = meetingRecords.value.findIndex(r => r.id === editingMeetingRecord.value!.id)
      if (index > -1) {
        meetingRecords.value[index] = { ...editingMeetingRecord.value }
      }
      message.success('更新成功')
    } else {
      // 新增
      editingMeetingRecord.value.id = Date.now()
      meetingRecords.value.unshift({ ...editingMeetingRecord.value })
      message.success('保存成功')
    }
    showMeetingModal.value = false
    updateCurrentStage()
  } catch (error) {
    message.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleAddDealRecord = () => {
  editingDealRecord.value = {
    id: Date.now(),
    customer_id: props.customerId,
    customer_name: '',
    customerId: props.customerId,
    packageType: '',
    contractAmount: 0,
    paidAmount: 0,
    remainingAmount: 0,
    paymentMethod: '',
    contractTime: Date.now(),
    projectDuration: 0,
    designer: '',
    sales: '',
    contractNo: '',
    status: '待签约',
    created_by: 1,
    created_by_name: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showDealModal.value = true
}

const handleEditDeal = () => {
  if (dealRecord.value) {
    editingDealRecord.value = { ...dealRecord.value }
    showDealModal.value = true
  }
}

const handleDealSubmit = async () => {
  if (!editingDealRecord.value) return
  
  formLoading.value = true
  try {
    dealRecord.value = { ...editingDealRecord.value }
    if (!dealRecord.value.id) {
      dealRecord.value.id = Date.now()
    }
    message.success(dealRecord.value.id ? '更新成功' : '保存成功')
    showDealModal.value = false
    updateCurrentStage()
  } catch (error) {
    message.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

// 更新当前阶段
const updateCurrentStage = () => {
  if (dealRecord.value) {
    currentStage.value = 2
    activeStageTab.value = 'deal'
  } else if (meetingRecords.value.length > 0) {
    currentStage.value = 1
    if (activeStageTab.value === 'follow') {
      activeStageTab.value = 'meeting'
    }
  } else if (followRecords.value.length > 0) {
    currentStage.value = 0
  }
}

// 初始化
onMounted(() => {
  // 加载数据
  loadData()
})

const loadData = async () => {
  // 这里应该从API加载数据
  // 暂时使用模拟数据
  followRecords.value = []
  meetingRecords.value = []
  dealRecord.value = null
  updateCurrentStage()
}
</script>

<style scoped>
.customer-stage-flow {
  padding: 20px;
}

/* 阶段进度条样式 */
.stage-progress-bar {
  margin-bottom: 30px;
}

.progress-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.stage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  position: relative;
}

.stage-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border: 2px solid #d9d9d9;
  color: #999;
  font-weight: bold;
  transition: all 0.3s ease;
}

.stage-item.active .stage-circle {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.stage-item.current .stage-circle {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
}

.stage-item.completed .stage-circle {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.stage-label {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stage-item.active .stage-label {
  color: #1890ff;
  font-weight: 600;
}

.progress-line {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: #f0f0f0;
  z-index: 1;
}

.progress-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
  width: var(--progress-width, 0%);
}

/* 阶段内容样式 */
.stage-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stage-panel {
  padding: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

/* 记录列表样式 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.record-item {
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.record-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.record-item.latest {
  border-color: #1890ff;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.record-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-title {
  font-weight: 600;
  color: #333;
}

.record-content {
  margin-top: 16px;
}

.follow-content,
.meeting-content,
.contract-notes {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.follow-content p,
.meeting-content p,
.contract-notes p {
  margin: 8px 0 0 0;
  color: #666;
  line-height: 1.6;
}

/* 附件样式 */
.attachments,
.transaction-screenshots {
  margin-top: 16px;
}

.attachment-list,
.screenshot-list {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.attachment-image,
.screenshot-image {
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

/* 成交卡片样式 */
.deal-card {
  border: 2px solid #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #fcffe6 100%);
}

.deal-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.deal-title {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.deal-content {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-stage-flow {
    padding: 16px;
  }
  
  .progress-container {
    padding: 0 10px;
  }
  
  .stage-circle {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
  
  .stage-label {
    font-size: 12px;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .panel-header h3 {
    font-size: 16px;
  }
}
</style>