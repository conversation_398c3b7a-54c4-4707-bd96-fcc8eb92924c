const fs = require('fs');
const mysql = require('mysql2/promise');
const path = require('path');

async function runSQLFile() {
  try {
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'mysql', 'create-mysql-tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'workchat_admin',
      multipleStatements: true
    });
    
    console.log('Connected to MySQL server');
    
    // 分割SQL语句（按分号分割，但忽略注释中的分号）
    const statements = sqlContent
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim() !== '')
      .join('\n')
      .split(';')
      .filter(stmt => stmt.trim() !== '');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          await connection.execute(statement);
          console.log(`✓ Statement ${i + 1} executed successfully`);
        } catch (error) {
          console.error(`✗ Error executing statement ${i + 1}:`, error.message);
          console.error('Statement:', statement.substring(0, 100) + '...');
        }
      }
    }
    
    await connection.end();
    console.log('\n✓ All SQL statements executed. Database setup complete!');
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

runSQLFile();