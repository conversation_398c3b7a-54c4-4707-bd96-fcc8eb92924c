const express = require('express');
const QRCode = require('qrcode');
const router = express.Router();

// 生成企业微信登录二维码
router.get('/qrcode', async (req, res) => {
  try {
    // 企业微信应用配置
    const corpId = process.env.WECHAT_CORP_ID || 'your_corp_id';
    const agentId = process.env.WECHAT_AGENT_ID || 'your_agent_id';
    const redirectUri = encodeURIComponent(process.env.WECHAT_REDIRECT_URI || 'http://localhost:3000/api/auth/wechat/callback');
    const state = Math.random().toString(36).substring(2, 15);
    
    // 构建企业微信授权URL
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&agentid=${agentId}&state=${state}#wechat_redirect`;
    
    // 生成二维码
    const qrcodeDataUrl = await QRCode.toDataURL(authUrl, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    res.json({
      success: true,
      data: {
        qrcode: qrcodeDataUrl,
        authUrl: authUrl,
        state: state,
        expiresIn: 300 // 5分钟
      }
    });
    
  } catch (error) {
    console.error('生成二维码失败:', error);
    res.status(500).json({
      success: false,
      message: '生成二维码失败'
    });
  }
});

// 企业微信登录状态检查
router.get('/login-status/:state', async (req, res) => {
  try {
    const { state } = req.params;
    
    // 这里应该检查state对应的登录状态
    // 实际项目中需要使用Redis或数据库存储状态
    
    res.json({
      success: true,
      data: {
        status: 'pending', // pending, success, failed
        token: null,
        user: null
      }
    });
    
  } catch (error) {
    console.error('检查登录状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查登录状态失败'
    });
  }
});

module.exports = router;