<template>
  <div class="script-management">
    <div class="header">
      <n-space justify="space-between">
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索话术标题"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <n-icon><search-outline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="typeFilter"
            placeholder="话术类型"
            clearable
            style="width: 120px"
            :options="typeOptions"
          />
        </n-space>
        <n-button type="primary" @click="showModal = true">
          <template #icon>
            <n-icon><add-outline /></n-icon>
          </template>
          新增话术
        </n-button>
      </n-space>
    </div>

    <n-data-table
      :columns="columns"
      :data="filteredScripts"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: Script) => row.id"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="card" style="width: 600px" title="话术管理">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item label="话术标题" path="title">
          <n-input v-model:value="formData.title" placeholder="请输入话术标题" />
        </n-form-item>
        <n-form-item label="话术类型" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择话术类型"
            :options="typeOptions"
          />
        </n-form-item>
        <n-form-item label="话术内容" path="content">
          <n-input
            v-model:value="formData.content"
            type="textarea"
            placeholder="请输入话术内容"
            :rows="5"
          />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-switch
            v-model:value="formData.status"
            :checked-value="'active'"
            :unchecked-value="'inactive'"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  SearchOutline,
  AddOutline
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

interface Script {
  id: number
  title: string
  type: string
  content: string
  status: 'active' | 'inactive'
  createTime: string
}

const message = useMessage()
const dialog = useDialog()
const loading = ref(false)
const showModal = ref(false)
const searchKeyword = ref('')
const typeFilter = ref('')
const formRef = ref()

const formData = ref({
  id: 0,
  title: '',
  type: '',
  content: '',
  status: 'active' as 'active' | 'inactive'
})

const rules = {
  title: {
    required: true,
    message: '请输入话术标题',
    trigger: 'blur'
  },
  type: {
    required: true,
    message: '请选择话术类型',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入话术内容',
    trigger: 'blur'
  }
}

const typeOptions = [
  { label: '开场白', value: 'opening' },
  { label: '产品介绍', value: 'product' },
  { label: '异议处理', value: 'objection' },
  { label: '成交话术', value: 'closing' },
  { label: '售后服务', value: 'service' }
]

const scripts = ref<Script[]>([
  {
    id: 1,
    title: '开场白话术',
    type: 'opening',
    content: '您好，我是YYSH的客户顾问...',
    status: 'active',
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    title: '产品介绍话术',
    type: 'product',
    content: '我们的产品具有以下特点...',
    status: 'active',
    createTime: '2024-01-15 10:05:00'
  }
])

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
}

const filteredScripts = computed(() => {
  let result = scripts.value
  
  if (searchKeyword.value) {
    result = result.filter(item =>
      item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  if (typeFilter.value) {
    result = result.filter(item => item.type === typeFilter.value)
  }
  
  return result
})

const getTypeLabel = (type: string) => {
  const option = typeOptions.find(item => item.value === type)
  return option ? option.label : type
}

const columns: DataTableColumns<Script> = [
  {
    title: '话术标题',
    key: 'title'
  },
  {
    title: '类型',
    key: 'type',
    width: 120,
    render: (row) => {
      return h('span', {
        class: 'px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm'
      }, getTypeLabel(row.type))
    }
  },
  {
    title: '内容预览',
    key: 'content',
    render: (row) => {
      const preview = row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content
      return h('span', { class: 'text-gray-600' }, preview)
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h('span', {
        class: row.status === 'active' ? 'text-green-600' : 'text-red-600'
      }, row.status === 'active' ? '启用' : '禁用')
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return h('div', { class: 'flex gap-2' }, [
        h('button', {
          class: 'px-2 py-1 text-blue-600 hover:bg-blue-50 rounded',
          onClick: () => handleEdit(row)
        }, '编辑'),
        h('button', {
          class: 'px-2 py-1 text-red-600 hover:bg-red-50 rounded',
          onClick: () => handleDelete(row)
        }, '删除')
      ])
    }
  }
]

const handleEdit = (row: Script) => {
  formData.value = { ...row }
  showModal.value = true
}

const handleDelete = (row: Script) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除话术"${row.title}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = scripts.value.findIndex(item => item.id === row.id)
      if (index > -1) {
        scripts.value.splice(index, 1)
        message.success('删除成功')
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.value.id === 0) {
      // 新增
      const newId = Math.max(...scripts.value.map(item => item.id)) + 1
      scripts.value.push({
        ...formData.value,
        id: newId,
        createTime: new Date().toLocaleString()
      })
      message.success('新增成功')
    } else {
      // 编辑
      const index = scripts.value.findIndex(item => item.id === formData.value.id)
      if (index > -1) {
        scripts.value[index] = { ...scripts.value[index], ...formData.value }
        message.success('编辑成功')
      }
    }
    
    showModal.value = false
    resetForm()
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const resetForm = () => {
  formData.value = {
    id: 0,
    title: '',
    type: '',
    content: '',
    status: 'active'
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.script-management {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}
</style>