-- 为meetings表添加缺失的列
ALTER TABLE meetings ADD COLUMN customer_id BIGINT;
ALTER TABLE meetings ADD COLUMN meeting_time TIMESTAMPTZ;
ALTER TABLE meetings ADD COLUMN duration INTEGER;
ALTER TABLE meetings ADD COLUMN attendees JSONB DEFAULT '[]'::jsonb;
ALTER TABLE meetings ADD COLUMN summary TEXT;

-- 添加注释
COMMENT ON COLUMN meetings.customer_id IS '关联的客户ID';
COMMENT ON COLUMN meetings.meeting_time IS '会议时间';
COMMENT ON COLUMN meetings.duration IS '会议时长（分钟）';
COMMENT ON COLUMN meetings.attendees IS '参会人员列表，JSON格式存储';
COMMENT ON COLUMN meetings.summary IS '会议总结';

-- 添加外键约束
ALTER TABLE meetings ADD CONSTRAINT fk_meetings_customer_id 
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL;

-- 创建索引
CREATE INDEX idx_meetings_customer_id ON meetings(customer_id);
CREATE INDEX idx_meetings_meeting_time ON meetings(meeting_time);
CREATE INDEX idx_meetings_attendees ON meetings USING GIN (attendees);