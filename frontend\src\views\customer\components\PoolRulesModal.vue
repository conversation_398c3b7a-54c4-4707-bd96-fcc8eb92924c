<template>
  <n-modal :show="show" @update:show="$emit('update:show', $event)" class="pool-rules-modal">
    <n-card
      style="width: 900px; max-height: 80vh; overflow: hidden;"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <!-- 模态框头部 -->
      <template #header>
        <div class="modal-header">
          <div class="header-info">
            <n-icon size="20" color="#1677ff">
              <settings-outline />
            </n-icon>
            <span class="header-title">公海规则配置</span>
          </div>
          <div class="header-description">
            <n-text depth="3" style="font-size: 13px;">
              配置客户自动进入公海的规则，提高客户资源利用效率
            </n-text>
          </div>
        </div>
      </template>

      <div class="rules-content">
        <!-- 统计信息卡片 -->
        <div class="stats-cards">
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-grid-item>
              <div class="stat-card">
                <div class="stat-icon">
                  <n-icon size="24" color="#1677ff">
                    <list-outline />
                  </n-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ rules.length }}</div>
                  <div class="stat-label">总规则数</div>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="stat-card">
                <div class="stat-icon">
                  <n-icon size="24" color="#52c41a">
                    <checkmark-circle-outline />
                  </n-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ activeRulesCount }}</div>
                  <div class="stat-label">启用规则</div>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="stat-card">
                <div class="stat-icon">
                  <n-icon size="24" color="#fa8c16">
                    <sync-outline />
                  </n-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ autoAssignRulesCount }}</div>
                  <div class="stat-label">自动分配</div>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="stat-card">
                <div class="stat-icon">
                  <n-icon size="24" color="#722ed1">
                    <time-outline />
                  </n-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ avgDays }}</div>
                  <div class="stat-label">平均天数</div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 规则列表 -->
        <div class="rules-list">
          <div class="list-header">
            <div class="header-left">
              <n-icon size="18" color="#1677ff">
                <list-circle-outline />
              </n-icon>
              <h3>规则列表</h3>
            </div>
            <div class="header-actions">
              <n-space>
                <n-button size="small" @click="loadRules" :loading="loading">
                  <template #icon>
                    <n-icon><refresh-outline /></n-icon>
                  </template>
                  刷新
                </n-button>
                <n-button type="primary" size="small" @click="showAddRule">
                  <template #icon>
                    <n-icon><add-outline /></n-icon>
                  </template>
                  添加规则
                </n-button>
              </n-space>
            </div>
          </div>
          
          <div class="table-container">
            <n-data-table
              :columns="columns"
              :data="rules"
              :loading="loading"
              :pagination="false"
              :bordered="false"
              :single-line="false"
              size="small"
              flex-height
              style="height: 300px;"
            />
          </div>
        </div>

        <!-- 规则表单模态框 -->
        <n-modal v-model:show="showRuleForm" class="rule-form-modal">
          <n-card
            style="width: 700px; max-height: 80vh; overflow: hidden;"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <!-- 表单头部 -->
            <template #header>
              <div class="form-header">
                <div class="form-header-info">
                  <n-icon size="18" color="#1677ff">
                    <document-text-outline />
                  </n-icon>
                  <span class="form-title">{{ editingRule ? '编辑规则' : '添加规则' }}</span>
                </div>
                <div class="form-description">
                  <n-text depth="3" style="font-size: 13px;">
                    {{ editingRule ? '修改现有公海规则配置' : '创建新的公海规则配置' }}
                  </n-text>
                </div>
              </div>
            </template>

            <div class="form-content">
              <n-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-placement="left"
                label-width="100px"
                require-mark-placement="right-hanging"
                size="medium"
              >
                <!-- 基础信息区域 -->
                <div class="form-section">
                  <div class="section-header">
                    <n-icon size="16" color="#1677ff">
                      <information-circle-outline />
                    </n-icon>
                    <span class="section-title">基础信息</span>
                  </div>
                  
                  <n-grid :cols="2" :x-gap="20" :y-gap="16">
                    <n-form-item-gi label="规则名称" path="name">
                      <n-input
                        v-model:value="formData.name"
                        placeholder="请输入规则名称"
                        clearable
                        :input-props="{ spellcheck: false }"
                      >
                        <template #prefix>
                          <n-icon size="16" color="#1677ff">
                            <text-outline />
                          </n-icon>
                        </template>
                      </n-input>
                    </n-form-item-gi>
                    
                    <n-form-item-gi label="触发条件" path="condition_type">
                      <n-select
                        v-model:value="formData.condition_type"
                        :options="conditionOptions"
                        placeholder="请选择触发条件"
                        clearable
                      >
                        <template #arrow>
                          <n-icon size="16" color="#8c8c8c">
                            <chevron-down-outline />
                          </n-icon>
                        </template>
                      </n-select>
                    </n-form-item-gi>
                  </n-grid>
                  
                  <n-form-item
                    label="天数设置"
                    path="days"
                    v-if="formData.condition_type === 'no_contact'"
                  >
                    <n-input-number
                      v-model:value="formData.days"
                      :min="1"
                      :max="365"
                      placeholder="请输入天数"
                      style="width: 100%"
                      :show-button="false"
                    >
                      <template #prefix>
                        <n-icon size="16" color="#fa8c16">
                          <calendar-outline />
                        </n-icon>
                      </template>
                      <template #suffix>天</template>
                    </n-input-number>
                  </n-form-item>
                </div>

                <!-- 适用范围区域 -->
                <div class="form-section">
                  <div class="section-header">
                    <n-icon size="16" color="#52c41a">
                      <people-outline />
                    </n-icon>
                    <span class="section-title">适用范围</span>
                  </div>
                  
                  <n-form-item label="客户等级" path="customer_levels">
                    <n-select
                      v-model:value="formData.customer_levels"
                      :options="levelOptions"
                      multiple
                      placeholder="选择适用的客户等级"
                      clearable
                      max-tag-count="responsive"
                    >
                      <template #arrow>
                        <n-icon size="16" color="#8c8c8c">
                          <chevron-down-outline />
                        </n-icon>
                      </template>
                    </n-select>
                  </n-form-item>
                  
                  <n-form-item label="排除条件">
                    <div class="checkbox-group">
                      <n-checkbox-group v-model:value="formData.exclude_conditions">
                        <n-space :size="16">
                          <n-checkbox value="has_deal">
                            <div class="checkbox-item">
                              <n-icon size="14" color="#52c41a">
                                <checkmark-circle-outline />
                              </n-icon>
                              <span>有成交记录</span>
                            </div>
                          </n-checkbox>
                          <n-checkbox value="high_value">
                            <div class="checkbox-item">
                              <n-icon size="14" color="#fa8c16">
                                <star-outline />
                              </n-icon>
                              <span>高价值客户</span>
                            </div>
                          </n-checkbox>
                          <n-checkbox value="recent_activity">
                            <div class="checkbox-item">
                              <n-icon size="14" color="#722ed1">
                                <pulse-outline />
                              </n-icon>
                              <span>近期有活动</span>
                            </div>
                          </n-checkbox>
                        </n-space>
                      </n-checkbox-group>
                    </div>
                  </n-form-item>
                </div>

                <!-- 执行设置区域 -->
                <div class="form-section">
                  <div class="section-header">
                    <n-icon size="16" color="#722ed1">
                      <settings-outline />
                    </n-icon>
                    <span class="section-title">执行设置</span>
                  </div>
                  
                  <n-grid :cols="2" :x-gap="20" :y-gap="16">
                    <n-form-item-gi label="自动分配">
                      <div class="switch-container">
                        <n-switch
                          v-model:value="formData.auto_assign"
                          size="medium"
                        >
                          <template #checked>
                            <n-icon size="14"><checkmark-outline /></n-icon>
                          </template>
                          <template #unchecked>
                            <n-icon size="14"><close-outline /></n-icon>
                          </template>
                        </n-switch>
                        <span class="switch-text">
                          {{ formData.auto_assign ? '开启自动分配' : '关闭自动分配' }}
                        </span>
                      </div>
                      <div class="switch-hint">
                        <n-text depth="3" style="font-size: 12px;">
                          开启后将自动分配给在线销售人员
                        </n-text>
                      </div>
                    </n-form-item-gi>
                    
                    <n-form-item-gi label="规则状态">
                      <div class="switch-container">
                        <n-switch
                          v-model:value="formData.is_active"
                          size="medium"
                        >
                          <template #checked>
                            <n-icon size="14"><checkmark-outline /></n-icon>
                          </template>
                          <template #unchecked>
                            <n-icon size="14"><close-outline /></n-icon>
                          </template>
                        </n-switch>
                        <span class="switch-text">
                          {{ formData.is_active ? '规则启用' : '规则禁用' }}
                        </span>
                      </div>
                      <div class="switch-hint">
                        <n-text depth="3" style="font-size: 12px;">
                          关闭后规则将不会执行
                        </n-text>
                      </div>
                    </n-form-item-gi>
                  </n-grid>
                </div>

                <!-- 规则描述区域 -->
                <div class="form-section">
                  <div class="section-header">
                    <n-icon size="16" color="#fa8c16">
                      <document-outline />
                    </n-icon>
                    <span class="section-title">规则描述</span>
                  </div>
                  
                  <n-form-item label="详细描述">
                    <n-input
                      v-model:value="formData.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入规则的详细描述，包括执行逻辑和注意事项..."
                      clearable
                      :maxlength="500"
                      show-count
                      :input-props="{ spellcheck: false }"
                    />
                  </n-form-item>
                </div>
              </n-form>
            </div>
            
            <template #footer>
              <div class="form-footer">
                <n-space justify="end">
                  <n-button @click="cancelEdit" size="medium">
                    <template #icon>
                      <n-icon><close-outline /></n-icon>
                    </template>
                    取消
                  </n-button>
                  <n-button type="primary" @click="saveRule" :loading="saving" size="medium">
                    <template #icon>
                      <n-icon><save-outline /></n-icon>
                    </template>
                    {{ editingRule ? '更新规则' : '创建规则' }}
                  </n-button>
                </n-space>
              </div>
            </template>
          </n-card>
        </n-modal>
      </div>
      
      <template #footer>
        <div class="modal-footer">
          <n-space justify="end">
            <n-button @click="$emit('update:show', false)" size="medium">
              <template #icon>
                <n-icon><close-outline /></n-icon>
              </template>
              关闭
            </n-button>
          </n-space>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw, computed } from 'vue'
import {
  NModal,
  NCard,
  NDataTable,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NGridItem,
  NInput,
  NInputNumber,
  NSelect,
  NCheckbox,
  NCheckboxGroup,
  NSwitch,
  NIcon,
  NText,
  NTag,
  useMessage,
  type DataTableColumns,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  SettingsOutline,
  ListOutline,
  CheckmarkCircleOutline,
  SyncOutline,
  TimeOutline,
  ListCircleOutline,
  RefreshOutline,
  AddOutline,
  DocumentTextOutline,
  InformationCircleOutline,
  TextOutline,
  ChevronDownOutline,
  CalendarOutline,
  PeopleOutline,
  StarOutline,
  PulseOutline,
  CheckmarkOutline,
  CloseOutline,
  DocumentOutline,
  SaveOutline,
  CreateOutline as EditIcon,
  TrashOutline as DeleteIcon
} from '@vicons/ionicons5'

interface PoolRule {
  id?: number
  name: string
  condition_type: string
  days?: number
  customer_levels: string[]
  exclude_conditions: string[]
  auto_assign: boolean
  is_active: boolean
  description?: string
  created_at?: string
  updated_at?: string
}

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'refresh'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 预创建图标组件，使用markRaw避免响应式处理
const EditIconComponent = markRaw(EditIcon)
const DeleteIconComponent = markRaw(DeleteIcon)
const formRef = ref<FormInst | null>(null)

const loading = ref(false)
const saving = ref(false)
const showRuleForm = ref(false)
const editingRule = ref<PoolRule | null>(null)
const rules = ref<PoolRule[]>([])

const formData = reactive<PoolRule>({
  name: '',
  condition_type: '',
  days: 30,
  customer_levels: [],
  exclude_conditions: [],
  auto_assign: false,
  is_active: true,
  description: ''
})

// 计算属性
const activeRulesCount = computed(() => {
  return rules.value.filter(rule => rule.is_active).length
})

const autoAssignRulesCount = computed(() => {
  return rules.value.filter(rule => rule.auto_assign).length
})

const avgDays = computed(() => {
  const daysRules = rules.value.filter(rule => rule.days)
  if (daysRules.length === 0) return 0
  const total = daysRules.reduce((sum, rule) => sum + (rule.days || 0), 0)
  return Math.round(total / daysRules.length)
})

const formRules: FormRules = {
  name: {
    required: true,
    message: '请输入规则名称',
    trigger: 'blur'
  },
  condition_type: {
    required: true,
    message: '请选择触发条件',
    trigger: 'change'
  },
  days: {
    required: true,
    type: 'number' as const,
    message: '请输入有效天数',
    trigger: 'blur'
  },
  customer_levels: {
    required: true,
    type: 'array' as const,
    min: 1,
    message: '请选择至少一个客户等级',
    trigger: 'change'
  }
}

const conditionOptions = [
  { label: '超过指定天数未联系', value: 'no_contact' },
  { label: '负责人离职', value: 'owner_leave' },
  { label: '客户主动要求更换', value: 'customer_request' },
  { label: '长期无响应', value: 'no_response' }
]

const levelOptions = [
  { label: '潜在客户', value: 'potential' },
  { label: '意向客户', value: 'interested' },
  { label: '重点客户', value: 'important' },
  { label: '成交客户', value: 'deal' }
]

const columns: DataTableColumns<PoolRule> = [
  {
    title: '规则名称',
    key: 'name',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '触发条件',
    key: 'condition_type',
    width: 140,
    render(row) {
      const condition = conditionOptions.find(opt => opt.value === row.condition_type)
      return condition?.label || row.condition_type
    }
  },
  {
    title: '天数',
    key: 'days',
    width: 70,
    align: 'center',
    render(row) {
      return row.days ? `${row.days}天` : '-'
    }
  },
  {
    title: '适用等级',
    key: 'customer_levels',
    width: 120,
    render(row) {
      const levels = row.customer_levels.map(level => {
        const levelOption = levelOptions.find(opt => opt.value === level)
        return levelOption?.label || level
      })
      
      if (levels.length <= 2) {
        return levels.join(', ')
      } else {
        return h('span', {}, [
          levels.slice(0, 2).join(', '),
          h('n-text', { depth: 3 }, ` 等${levels.length}项`)
        ])
      }
    }
  },
  {
    title: '自动分配',
    key: 'auto_assign',
    width: 80,
    align: 'center',
    render(row) {
      return h(NTag, { 
        type: row.auto_assign ? 'success' : 'default',
        size: 'small'
      }, { 
        default: () => row.auto_assign ? '是' : '否' 
      })
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 70,
    align: 'center',
    render(row) {
      return h(NTag, { 
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      }, { 
        default: () => row.is_active ? '启用' : '禁用' 
      })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center',
    render(row) {
      return h(NSpace, { size: 8 }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => editRule(row)
          }, {
            default: () => '编辑',
            icon: () => h(EditIconComponent)
          }),
          h(NButton, {
            size: 'small',
            type: 'error',
            secondary: true,
            onClick: () => deleteRule(row.id!)
          }, {
            default: () => '删除',
            icon: () => h(DeleteIconComponent)
          })
        ]
      })
    }
  }
]

const loadRules = async () => {
  loading.value = true
  try {
    // TODO: 调用获取公海规则API
    // const response = await poolRuleService.getRules()
    // rules.value = response.data
    
    // 模拟数据
    rules.value = [
      {
        id: 1,
        name: '长期未联系规则',
        condition_type: 'no_contact',
        days: 30,
        customer_levels: ['potential', 'interested'],
        exclude_conditions: ['has_deal'],
        auto_assign: true,
        is_active: true,
        description: '超过30天未联系的潜在和意向客户自动进入公海',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '负责人离职规则',
        condition_type: 'owner_leave',
        customer_levels: ['potential', 'interested', 'important'],
        exclude_conditions: ['high_value'],
        auto_assign: false,
        is_active: true,
        description: '负责人离职时客户自动进入公海',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 3,
        name: '客户要求更换规则',
        condition_type: 'customer_request',
        customer_levels: ['potential', 'interested', 'important', 'deal'],
        exclude_conditions: [],
        auto_assign: true,
        is_active: false,
        description: '客户主动要求更换负责人时的处理规则',
        created_at: '2024-01-01T00:00:00Z'
      }
    ]
  } catch (error) {
    message.error('加载公海规则失败')
    console.error('Load pool rules error:', error)
  } finally {
    loading.value = false
  }
}

const showAddRule = () => {
  editingRule.value = null
  resetForm()
  showRuleForm.value = true
}

const editRule = (rule: PoolRule) => {
  editingRule.value = rule
  Object.assign(formData, rule)
  showRuleForm.value = true
}

const deleteRule = async (id: number) => {
  try {
    // TODO: 调用删除公海规则API
    // await poolRuleService.deleteRule(id)
    
    message.success('删除规则成功')
    loadRules()
  } catch (error) {
    message.error('删除规则失败')
    console.error('Delete pool rule error:', error)
  }
}

const saveRule = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    if (editingRule.value) {
      // TODO: 调用更新公海规则API
      // await poolRuleService.updateRule(editingRule.value.id!, formData)
      message.success('更新规则成功')
    } else {
      // TODO: 调用创建公海规则API
      // await poolRuleService.createRule(formData)
      message.success('创建规则成功')
    }
    
    showRuleForm.value = false
    loadRules()
    emit('refresh')
  } catch (error) {
    if (error instanceof Error) {
      message.error(editingRule.value ? '更新规则失败' : '创建规则失败')
      console.error('Save pool rule error:', error)
    }
  } finally {
    saving.value = false
  }
}

const cancelEdit = () => {
  showRuleForm.value = false
  editingRule.value = null
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    condition_type: '',
    days: 30,
    customer_levels: [],
    exclude_conditions: [],
    auto_assign: false,
    is_active: true,
    description: ''
  })
}

onMounted(() => {
  loadRules()
})
</script>

<style scoped>
.pool-rules-modal {
  --n-color-modal: rgba(0, 0, 0, 0.6);
}

.modal-header {
  padding-bottom: 8px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-description {
  margin-left: 28px;
}

.rules-content {
  min-height: 500px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 0;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(22, 119, 255, 0.1);
  border-radius: 8px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1;
}

.rules-list {
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.table-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.rule-form-modal {
  --n-color-modal: rgba(0, 0, 0, 0.6);
}

.form-header {
  padding-bottom: 8px;
}

.form-header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.form-description {
  margin-left: 26px;
}

.form-content {
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 4px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.checkbox-group {
  width: 100%;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.switch-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.switch-hint {
  margin-top: 4px;
}

.form-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

/* 开关样式 */
:deep(.n-switch) {
  --n-rail-color-active: #1677ff;
  --n-rail-height: 20px;
  --n-button-width: 18px;
  --n-button-height: 18px;
}

:deep(.n-switch .n-switch__button) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 复选框样式 */
:deep(.n-checkbox) {
  --n-color-checked: #1677ff;
  --n-border-checked: #1677ff;
}

:deep(.n-checkbox .n-checkbox__label) {
  font-size: 14px;
  color: #333;
}

/* 数据表格样式 */
:deep(.n-data-table) {
  --n-th-color: #fafafa;
  --n-td-color: #ffffff;
  --n-border-color: #f0f0f0;
}

:deep(.n-data-table .n-data-table-th) {
  font-weight: 600;
  color: #333;
  font-size: 13px;
}

:deep(.n-data-table .n-data-table-td) {
  font-size: 13px;
  color: #666;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-input-number:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-select:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 输入框前缀图标样式 */
:deep(.n-input .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__suffix) {
  color: #8c8c8c;
  font-weight: 500;
}

/* 选择器箭头样式 */
:deep(.n-select .n-base-suffix) {
  color: #8c8c8c;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 滚动条样式 */
.rules-content::-webkit-scrollbar,
.form-content::-webkit-scrollbar {
  width: 6px;
}

.rules-content::-webkit-scrollbar-track,
.form-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.rules-content::-webkit-scrollbar-thumb,
.form-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.rules-content::-webkit-scrollbar-thumb:hover,
.form-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .pool-rules-modal :deep(.n-card) {
    width: 95vw !important;
    max-height: 90vh !important;
  }
  
  .rule-form-modal :deep(.n-card) {
    width: 95vw !important;
    max-height: 90vh !important;
  }
  
  .header-title {
    font-size: 16px;
  }
  
  .form-title {
    font-size: 14px;
  }
  
  .stats-cards :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .rules-list {
    padding: 16px;
  }
  
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  .switch-container {
    gap: 8px;
  }
  
  .switch-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .stats-cards :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  .stat-card {
    padding: 10px;
  }
  
  .stat-icon {
    width: 32px;
    height: 32px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .header-actions :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .checkbox-item {
    gap: 4px;
  }
  
  .switch-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>