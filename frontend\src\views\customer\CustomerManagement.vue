<template>
  <div class="customer-management" :class="{ 'embedded-mode': embedded }">
    <!-- 页面头部 - 仅在独立模式显示 -->
    <PageHeader v-if="!embedded" title="客户管理">
      <template #actions>
        <n-button type="primary" @click="handleCreate">
          <template #icon>
            <n-icon><AddIcon /></n-icon>
          </template>
          新增客户
        </n-button>
        <n-button @click="handleImport">
          <template #icon>
            <n-icon><ImportIcon /></n-icon>
          </template>
          导入客户
        </n-button>
        <n-button @click="handleExport">
          <template #icon>
            <n-icon><ExportIcon /></n-icon>
          </template>
          导出客户
        </n-button>
      </template>
    </PageHeader>

    <!-- 筛选器组件 -->
    <CustomerFilters
      v-model:filters="filters"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
      @export="handleExport"
      @import="handleImport"
      :show-actions="embedded"
    />

    <!-- 客户表格组件 -->
    <CustomerTable
      :data="customers"
      :loading="loading"
      :pagination="pagination"
      :selected-keys="selectedKeys"
      @update:selected-keys="handleSelectionChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
      @follow="handleFollow"
      @assign="handleAssign"
      @batch-assign="handleBatchAssign"
      @batch-delete="handleBatchDelete"
      @move-to-pool="handleMoveToPool"
    />

    <!-- 客户表单弹窗 -->
    <CustomerForm
      v-model:show="showForm"
      :customer="currentCustomer"
      :is-edit="isEdit"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    />

    <!-- 批量分配弹窗 -->
    <BatchAssignModal
      v-model:show="showBatchAssign"
      :customer-ids="selectedKeys"
      :customers="customers"
      @confirm="handleBatchAssignConfirm"
    />

    <!-- 导入弹窗 -->
    <ImportModal
      v-model:show="showImport"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  AddOutline as AddIcon,
  CloudUploadOutline as ImportIcon,
  CloudDownloadOutline as ExportIcon
} from '@vicons/ionicons5'
import { useCustomerStore } from '@/stores/modules/customer'
import { CustomerService } from '@/api/customerService'
import type { Customer, CustomerFilters as CustomerFiltersType } from '@/api/customerService'
import type { PaginationResponse } from '@/types'

// 组件导入
import PageHeader from '@/components/common/PageHeader.vue'
import CustomerFilters from '@/components/customer/CustomerFilters.vue'
import CustomerTable from '@/components/customer/CustomerTable.vue'
import CustomerForm from '@/components/customer/CustomerForm.vue'
import BatchAssignModal from '@/components/customer/BatchAssignModal.vue'
import ImportModal from '@/components/customer/ImportModal.vue'

// Props定义
interface Props {
  embedded?: boolean // 是否为嵌入模式
  height?: string | number // 容器高度
  showActions?: boolean // 是否显示操作按钮
  defaultFilters?: Partial<CustomerFiltersType> // 默认筛选条件
}

const props = withDefaults(defineProps<Props>(), {
  embedded: false,
  height: 'auto',
  showActions: true,
  defaultFilters: () => ({})
})

// Emits定义
interface Emits {
  (e: 'customer-selected', customer: Customer): void
  (e: 'customers-updated'): void
  (e: 'selection-change', keys: number[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const message = useMessage()
const dialog = useDialog()
const customerStore = useCustomerStore()

// 数据状态
const loading = ref(false)
const customers = ref<Customer[]>([])
const selectedKeys = ref<number[]>([])
const currentCustomer = ref<Customer | null>(null)

// 弹窗状态
const showForm = ref(false)
const showBatchAssign = ref(false)
const showImport = ref(false)
const isEdit = ref(false)

// 筛选条件
const filters = reactive<CustomerFiltersType>({
  page: 1,
  page_size: 20,
  ...props.defaultFilters
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount} 条`
})

// 计算属性
const containerStyle = computed(() => {
  if (props.embedded && props.height !== 'auto') {
    return {
      height: typeof props.height === 'number' ? `${props.height}px` : props.height,
      overflow: 'auto'
    }
  }
  return {}
})

// 监听筛选条件变化
watch(
  () => filters,
  () => {
    if (filters.page === 1) {
      fetchCustomers()
    } else {
      filters.page = 1
    }
  },
  { deep: true }
)

// 监听页码变化
watch(
  () => filters.page,
  () => {
    fetchCustomers()
  }
)

// 监听选中状态变化
watch(
  () => selectedKeys.value,
  (keys) => {
    emit('selection-change', keys)
  }
)

// 方法定义

// 获取客户列表
const fetchCustomers = async () => {
  try {
    loading.value = true
    const response: PaginationResponse<Customer> = await CustomerService.getCustomers(filters)
    
    customers.value = response.data
    pagination.total = response.total
    pagination.page = response.page
    pagination.pageSize = response.page_size
    pagination.totalPages = response.total_pages
    
    // 清空选中状态
    selectedKeys.value = []
  } catch (error: any) {
    message.error(error.message || '获取客户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  filters.page = 1
  fetchCustomers()
}

// 重置处理
const handleReset = () => {
  Object.assign(filters, {
    search: '',
    level: undefined,
    status: undefined,
    source: undefined,
    assigned_to: undefined,
    date_range: undefined,
    tags: undefined,
    page: 1,
    page_size: 20
  })
  fetchCustomers()
}

// 分页处理
const handlePageChange = (page: number) => {
  filters.page = page
}

const handlePageSizeChange = (pageSize: number) => {
  filters.page_size = pageSize
  filters.page = 1
}

// 选择处理
const handleSelectionChange = (keys: number[]) => {
  selectedKeys.value = keys
}

// CRUD操作
const handleCreate = () => {
  currentCustomer.value = null
  isEdit.value = false
  showForm.value = true
}

const handleEdit = (customer: Customer) => {
  currentCustomer.value = customer
  isEdit.value = true
  showForm.value = true
}

const handleView = (customer: Customer) => {
  emit('customer-selected', customer)
  // 如果是独立模式，可以跳转到详情页
  if (!props.embedded) {
    // router.push(`/customer/${customer.id}`)
  }
}

const handleDelete = (customer: Customer) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除客户 "${customer.name}" 吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await CustomerService.deleteCustomer(customer.id)
        message.success('删除成功')
        fetchCustomers()
        emit('customers-updated')
      } catch (error: any) {
        message.error(error.message || '删除失败')
      }
    }
  })
}

// 表单处理
const handleFormSubmit = async (customerData: any) => {
  try {
    if (isEdit.value && currentCustomer.value) {
      await CustomerService.updateCustomer(currentCustomer.value.id, customerData)
      message.success('更新成功')
    } else {
      await CustomerService.createCustomer(customerData)
      message.success('创建成功')
    }
    
    showForm.value = false
    fetchCustomers()
    emit('customers-updated')
  } catch (error: any) {
    message.error(error.message || '操作失败')
  }
}

const handleFormCancel = () => {
  showForm.value = false
  currentCustomer.value = null
}

// 批量操作
const handleBatchAssign = () => {
  if (selectedKeys.value.length === 0) {
    message.warning('请先选择要分配的客户')
    return
  }
  showBatchAssign.value = true
}

const handleBatchAssignConfirm = async (userId: string) => {
  try {
    await CustomerService.batchAssignCustomers(selectedKeys.value, userId)
    message.success('批量分配成功')
    showBatchAssign.value = false
    selectedKeys.value = []
    fetchCustomers()
    emit('customers-updated')
  } catch (error: any) {
    message.error(error.message || '批量分配失败')
  }
}

const handleBatchDelete = () => {
  if (selectedKeys.value.length === 0) {
    message.warning('请先选择要删除的客户')
    return
  }
  
  dialog.warning({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedKeys.value.length} 个客户吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await CustomerService.batchDeleteCustomers(selectedKeys.value)
        message.success('批量删除成功')
        selectedKeys.value = []
        fetchCustomers()
        emit('customers-updated')
      } catch (error: any) {
        message.error(error.message || '批量删除失败')
      }
    }
  })
}

// 其他操作
const handleFollow = (customer: Customer) => {
  // 跟进操作
  console.log('跟进客户:', customer)
}

const handleAssign = (customer: Customer) => {
  // 分配操作
  console.log('分配客户:', customer)
}

const handleMoveToPool = () => {
  if (selectedKeys.value.length === 0) {
    message.warning('请先选择要移入公海的客户')
    return
  }
  
  dialog.warning({
    title: '确认移入公海',
    content: `确定要将选中的 ${selectedKeys.value.length} 个客户移入公海吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 调用移入公海API
        message.success('移入公海成功')
        selectedKeys.value = []
        fetchCustomers()
        emit('customers-updated')
      } catch (error: any) {
        message.error(error.message || '移入公海失败')
      }
    }
  })
}

// 导入导出
const handleImport = () => {
  showImport.value = true
}

const handleImportSuccess = () => {
  showImport.value = false
  fetchCustomers()
  emit('customers-updated')
}

const handleExport = async () => {
  try {
    await CustomerService.exportCustomers(filters)
    message.success('导出成功')
  } catch (error: any) {
    message.error(error.message || '导出失败')
  }
}

// 生命周期
onMounted(() => {
  fetchCustomers()
})

// 暴露方法给父组件
defineExpose({
  refresh: fetchCustomers,
  getSelectedCustomers: () => customers.value.filter(c => selectedKeys.value.includes(c.id)),
  clearSelection: () => { selectedKeys.value = [] }
})
</script>

<style scoped lang="scss">
.customer-management {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  &.embedded-mode {
    .page-header {
      display: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .customer-management {
    gap: 12px;
  }
}
</style>