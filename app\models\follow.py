"""跟进管理模型

定义客户跟进、会议、公海等相关的数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Enum, Integer, Table
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class FollowType(enum.Enum):
    """跟进类型枚举"""
    PHONE = "phone"  # 电话
    EMAIL = "email"  # 邮件
    WECHAT = "wechat"  # 微信
    VISIT = "visit"  # 拜访
    MEETING = "meeting"  # 会议
    OTHER = "other"  # 其他


class FollowResult(enum.Enum):
    """跟进结果枚举"""
    SUCCESS = "success"  # 成功
    FAILED = "failed"  # 失败
    NO_ANSWER = "no_answer"  # 无人接听
    BUSY = "busy"  # 忙线
    INTERESTED = "interested"  # 有兴趣
    NOT_INTERESTED = "not_interested"  # 无兴趣
    NEED_FOLLOW = "need_follow"  # 需要跟进


class MeetingStatus(enum.Enum):
    """会议状态枚举"""
    SCHEDULED = "scheduled"  # 已安排
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消
    POSTPONED = "postponed"  # 已延期


class PoolReason(enum.Enum):
    """进入公海原因枚举"""
    TIMEOUT = "timeout"  # 超时未跟进
    INACTIVE = "inactive"  # 客户不活跃
    MANUAL = "manual"  # 手动放入
    REASSIGN = "reassign"  # 重新分配
    LOST = "lost"  # 客户流失


# 会议参与者关联表
meeting_participants = Table(
    'meeting_participants',
    BaseModel.metadata,
    Column('meeting_id', String(36), ForeignKey('meetings.id'), primary_key=True),
    Column('customer_id', String(36), ForeignKey('customers.id'), primary_key=True)
)


class CustomerFollowRecord(BaseModel):
    """客户跟进记录模型
    
    对应数据库中的customer_follow_records表
    """
    __tablename__ = "customer_follow_records"
    
    # 基本信息
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    created_by = Column(String(36), ForeignKey('users.id'), nullable=False, comment="创建人ID")
    
    # 跟进信息
    follow_type = Column(Enum(FollowType), nullable=False, comment="跟进方式")
    follow_result = Column(Enum(FollowResult), nullable=True, comment="跟进结果")
    content = Column(Text, nullable=False, comment="跟进内容")
    
    # 时间信息
    follow_time = Column(DateTime, nullable=False, comment="跟进时间")
    next_follow_time = Column(DateTime, nullable=True, comment="下次跟进时间")
    duration = Column(Integer, nullable=True, comment="跟进时长（分钟）")
    
    # 附加信息
    attachments = Column(Text, nullable=True, comment="附件（JSON格式）")
    tags = Column(Text, nullable=True, comment="标签")
    is_important = Column(Boolean, default=False, nullable=False, comment="是否重要")
    
    # 关联关系
    # TODO: 暂时注释掉客户关系，避免循环引用问题
    # customer = relationship("Customer", back_populates="follow_records", lazy="select")
    # TODO: 暂时注释掉用户关系，避免循环引用问题
    # created_by_user = relationship("User", back_populates="follow_records", lazy="select")
    
    def __repr__(self):
        return f"<CustomerFollowRecord(id={self.id}, customer_id={self.customer_id})>"
    
    @property
    def follow_type_display(self):
        """跟进方式显示名称"""
        type_map = {
            FollowType.PHONE: "电话",
            FollowType.EMAIL: "邮件",
            FollowType.WECHAT: "微信",
            FollowType.VISIT: "拜访",
            FollowType.MEETING: "会议",
            FollowType.OTHER: "其他"
        }
        return type_map.get(self.follow_type, self.follow_type.value)
    
    @property
    def follow_result_display(self):
        """跟进结果显示名称"""
        if not self.follow_result:
            return None
        result_map = {
            FollowResult.SUCCESS: "成功",
            FollowResult.FAILED: "失败",
            FollowResult.NO_ANSWER: "无人接听",
            FollowResult.BUSY: "忙线",
            FollowResult.INTERESTED: "有兴趣",
            FollowResult.NOT_INTERESTED: "无兴趣",
            FollowResult.NEED_FOLLOW: "需要跟进"
        }
        return result_map.get(self.follow_result, self.follow_result.value)


class Meeting(BaseModel):
    """会议模型
    
    对应数据库中的meetings表
    """
    __tablename__ = "meetings"
    
    # 基本信息
    title = Column(String(200), nullable=False, comment="会议标题")
    description = Column(Text, nullable=True, comment="会议描述")
    location = Column(String(200), nullable=True, comment="会议地点")
    
    # 时间信息
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=False, comment="结束时间")
    duration = Column(Integer, nullable=True, comment="会议时长（分钟）")
    
    # 状态信息
    status = Column(Enum(MeetingStatus), default=MeetingStatus.SCHEDULED, nullable=False, comment="会议状态")
    
    # 组织信息
    organizer_id = Column(String(36), ForeignKey('users.id'), nullable=False, comment="组织者ID")
    created_by = Column(String(36), ForeignKey('users.id'), nullable=False, comment="创建人ID")
    
    # 会议内容
    agenda = Column(Text, nullable=True, comment="会议议程")
    notes = Column(Text, nullable=True, comment="会议纪要")
    attachments = Column(Text, nullable=True, comment="附件（JSON格式）")
    
    # 提醒设置
    reminder_time = Column(DateTime, nullable=True, comment="提醒时间")
    is_reminded = Column(Boolean, default=False, nullable=False, comment="是否已提醒")
    
    # 关联关系
    organizer = relationship("User", foreign_keys=[organizer_id], lazy="select")
    creator = relationship("User", foreign_keys=[created_by], lazy="select")
    customers = relationship("Customer", secondary=meeting_participants, back_populates="meetings", lazy="select")
    
    def __repr__(self):
        return f"<Meeting(id={self.id}, title={self.title})>"
    
    @property
    def status_display(self):
        """状态显示名称"""
        status_map = {
            MeetingStatus.SCHEDULED: "已安排",
            MeetingStatus.IN_PROGRESS: "进行中",
            MeetingStatus.COMPLETED: "已完成",
            MeetingStatus.CANCELLED: "已取消",
            MeetingStatus.POSTPONED: "已延期"
        }
        return status_map.get(self.status, self.status.value)


class PublicPool(BaseModel):
    """公海客户模型
    
    对应数据库中的public_pool表
    """
    __tablename__ = "public_pool"
    
    # 基本信息
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    previous_owner_id = Column(String(36), ForeignKey('users.id'), nullable=True, comment="原负责人ID")
    
    # 进入公海信息
    reason = Column(Enum(PoolReason), nullable=False, comment="进入公海原因")
    reason_description = Column(Text, nullable=True, comment="原因描述")
    entered_by = Column(String(36), ForeignKey('users.id'), nullable=False, comment="操作人ID")
    entered_at = Column(DateTime, nullable=False, comment="进入时间")
    
    # 领取信息
    claimed_by = Column(String(36), ForeignKey('users.id'), nullable=True, comment="领取人ID")
    claimed_at = Column(DateTime, nullable=True, comment="领取时间")
    is_claimed = Column(Boolean, default=False, nullable=False, comment="是否已领取")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否活跃")
    priority = Column(Integer, default=0, nullable=False, comment="优先级")
    
    # 备注
    notes = Column(Text, nullable=True, comment="备注")
    
    # 关联关系
    customer = relationship("Customer", back_populates="pool_records", lazy="select")
    previous_owner = relationship("User", foreign_keys=[previous_owner_id], lazy="select")
    entered_by_user = relationship("User", foreign_keys=[entered_by], lazy="select")
    claimed_by_user = relationship("User", foreign_keys=[claimed_by], lazy="select")
    
    def __repr__(self):
        return f"<PublicPool(id={self.id}, customer_id={self.customer_id})>"
    
    @property
    def reason_display(self):
        """原因显示名称"""
        reason_map = {
            PoolReason.TIMEOUT: "超时未跟进",
            PoolReason.INACTIVE: "客户不活跃",
            PoolReason.MANUAL: "手动放入",
            PoolReason.REASSIGN: "重新分配",
            PoolReason.LOST: "客户流失"
        }
        return reason_map.get(self.reason, self.reason.value)


class CustomerBehavior(BaseModel):
    """客户行为记录模型
    
    对应数据库中的customer_behaviors表
    """
    __tablename__ = "customer_behaviors"
    
    # 基本信息
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False, comment="客户ID")
    behavior_type = Column(String(50), nullable=False, comment="行为类型")
    behavior_data = Column(Text, nullable=True, comment="行为数据（JSON格式）")
    
    # 时间和来源
    occurred_at = Column(DateTime, nullable=False, comment="发生时间")
    source = Column(String(100), nullable=True, comment="数据来源")
    
    # 统计信息
    duration = Column(Integer, nullable=True, comment="持续时间（秒）")
    page_views = Column(Integer, nullable=True, comment="页面浏览数")
    
    # 关联关系
    customer = relationship("Customer", back_populates="behaviors", lazy="select")
    
    def __repr__(self):
        return f"<CustomerBehavior(id={self.id}, customer_id={self.customer_id}, type={self.behavior_type})>"