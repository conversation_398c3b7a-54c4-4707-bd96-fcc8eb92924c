<template>
  <div class="meeting-list">
    <!-- 页面头部 -->
    <PageHeader title="会议管理" description="管理客户会议安排和记录">
      <template #actions>
        <n-space>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            新增会议
          </n-button>
          <n-button type="default" @click="handleCalendarView">
            <template #icon>
              <n-icon><calendar-outline /></n-icon>
            </template>
            日历视图
          </n-button>
        </n-space>
      </template>
    </PageHeader>

    <!-- 筛选器 -->
    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchForm.title"
          placeholder="搜索会议主题、客户姓名"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="searchForm.status"
          placeholder="会议状态"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
        
        <n-select
          v-model:value="searchForm.type"
          placeholder="会议类型"
          clearable
          style="width: 150px"
          :options="typeOptions"
        />
        
        <n-date-picker
          v-model:value="searchForm.dateRange"
          type="daterange"
          placeholder="会议时间"
          clearable
        />
        
        <n-button type="default" @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">搜索</n-button>
      </n-space>
    </div>

    <!-- 统计卡片 -->
    <n-grid :cols="4" :x-gap="16" class="stats-grid">
      <n-card class="stat-card">
        <n-statistic label="总会议数" :value="stats.total" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="今日会议" :value="stats.today" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="本周会议" :value="stats.week" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="待开会议" :value="stats.upcoming" />
      </n-card>
    </n-grid>

    <!-- 会议列表 -->
    <n-card class="table-card">
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="meetings"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: MeetingRecord) => row.id"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 会议表单弹窗 -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      title="会议信息"
      :style="{ width: '700px' }"
      :on-positive-click="handleSubmit"
      :on-negative-click="() => { showModal = false; return true }"
      positive-text="保存"
      negative-text="取消"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="会议主题" path="title">
            <n-input
              v-model:value="formData.title"
              placeholder="请输入会议主题"
            />
          </n-form-item-gi>
          <n-form-item-gi label="会议类型" path="type">
            <n-select
              v-model:value="formData.type"
              placeholder="请选择会议类型"
              :options="typeOptions"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="开始时间" path="startTime">
            <n-date-picker
              v-model:value="formData.startTime"
              type="datetime"
              placeholder="请选择开始时间"
            />
          </n-form-item-gi>
          <n-form-item-gi label="结束时间" path="endTime">
            <n-date-picker
              v-model:value="formData.endTime"
              type="datetime"
              placeholder="请选择结束时间"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="会议地点">
            <n-input
              v-model:value="formData.location"
              placeholder="请输入会议地点"
            />
          </n-form-item-gi>
          <n-form-item-gi label="会议状态" path="status">
            <n-select
              v-model:value="formData.status"
              placeholder="请选择会议状态"
              :options="statusOptions"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="参与客户" path="customerIds">
          <n-select
            v-model:value="formData.customerIds"
            placeholder="请选择参与客户"
            :options="customerOptions"
            multiple
            filterable
          />
        </n-form-item>
        
        <n-form-item label="参与人员">
          <n-space>
            <n-select
              v-model:value="formData.attendeeIds"
              placeholder="请选择参与人员"
              :options="userOptions"
              multiple
              filterable
              style="flex: 1"
            />
            <n-button type="primary" @click="showEmployeeModal = true">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              添加员工
            </n-button>
          </n-space>
        </n-form-item>
        
        <n-form-item label="会议议程">
          <n-input
            v-model:value="formData.agenda"
            type="textarea"
            placeholder="请输入会议议程"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="会议记录">
          <n-input
            v-model:value="formData.notes"
            type="textarea"
            placeholder="请输入会议记录"
            :rows="4"
          />
        </n-form-item>
        
        <n-form-item label="备注">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
          />
        </n-form-item>
      </n-form>
    </n-modal>

    <!-- 添加员工模态框 -->
    <n-modal
      v-model:show="showEmployeeModal"
      preset="dialog"
      title="添加企业微信员工"
      :style="{ width: '600px' }"
      :on-positive-click="handleAddEmployees"
      :on-negative-click="() => { showEmployeeModal = false; return true }"
      positive-text="确认添加"
      negative-text="取消"
    >
      <div class="employee-selection">
        <n-space vertical>
          <n-alert type="info" title="提示">
            选择要添加到会议的企业微信内部员工，系统将自动发送会议通知
          </n-alert>
          
          <n-checkbox-group v-model:value="selectedEmployees">
            <n-space vertical>
              <div v-for="user in userOptions" :key="user.value" class="employee-item">
                <n-checkbox :value="user.value">
                  <div class="employee-info">
                    <div class="employee-name">{{ user.label }}</div>
                    <div class="employee-dept">{{ user.department }}</div>
                    <div class="employee-wechat">企业微信ID: {{ user.wechatId }}</div>
                  </div>
                </n-checkbox>
              </div>
            </n-space>
          </n-checkbox-group>
        </n-space>
      </div>
    </n-modal>

    <!-- 企业微信通知模态框 -->
    <n-modal
      v-model:show="showNotificationModal"
      preset="dialog"
      title="发送企业微信通知"
      :style="{ width: '700px' }"
      :on-positive-click="handleSendNotification"
      :on-negative-click="() => { showNotificationModal = false; return true }"
      positive-text="发送通知"
      negative-text="取消"
    >
      <div class="notification-content">
        <n-space vertical>
          <n-alert type="warning" title="会议通知">
            即将向选中的企业微信员工发送会议通知
          </n-alert>
          
          <n-descriptions title="会议信息" :column="2" bordered>
            <n-descriptions-item label="会议主题">{{ currentMeetingForNotification?.title }}</n-descriptions-item>
            <n-descriptions-item label="会议时间">
              {{ currentMeetingForNotification?.startTime ? new Date(currentMeetingForNotification.startTime).toLocaleString() : '-' }}
            </n-descriptions-item>
            <n-descriptions-item label="会议地点">{{ currentMeetingForNotification?.location || '线上会议' }}</n-descriptions-item>
            <n-descriptions-item label="参与人数">{{ formData.attendeeIds.length }}人</n-descriptions-item>
          </n-descriptions>
          
          <n-form-item label="通知内容">
            <n-input
              v-model:value="notificationContent"
              type="textarea"
              placeholder="请输入要发送的通知内容（可选，系统会自动生成基础通知内容）"
              :rows="4"
            />
          </n-form-item>
          
          <n-form-item label="通知对象">
            <n-space>
              <n-tag
                v-for="employeeId in formData.attendeeIds"
                :key="employeeId"
                type="info"
              >
                {{ userOptions.find(u => u.value === employeeId)?.label }}
              </n-tag>
            </n-space>
          </n-form-item>
        </n-space>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw } from 'vue'
import { useMessage } from 'naive-ui'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  AddOutline,
  CalendarOutline,
  SearchOutline as SearchIcon,
  RefreshOutline,
  CreateOutline,
  TrashOutline,
  EyeOutline,
  VideocamOutline
} from '@vicons/ionicons5'
import type { MeetingRecord } from '@/types'
import type { DataTableColumns, FormInst, FormRules } from 'naive-ui'

const message = useMessage()

// 预创建图标组件，避免在render函数中重复创建
const ViewIcon = () => h('n-icon', null, { default: () => h(markRaw(EyeOutline)) })
const EditIcon = () => h('n-icon', null, { default: () => h(markRaw(CreateOutline)) })
const JoinIcon = () => h('n-icon', null, { default: () => h(markRaw(VideocamOutline)) })

// 响应式数据
const loading = ref(false)
const meetings = ref<MeetingRecord[]>([])
const showModal = ref(false)
const formRef = ref<FormInst | null>(null)
const currentMeeting = ref<MeetingRecord | null>(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  customerName: '',
  status: null,
  type: null,
  dateRange: null as [number, number] | null
})

// 统计数据
const stats = reactive({
  total: 0,
  today: 0,
  week: 0,
  upcoming: 0
})

// 表单数据
const formData = reactive({
  id: null as number | null,
  title: '',
  type: '',
  startTime: null as number | null,
  endTime: null as number | null,
  location: '',
  status: 'scheduled',
  customerIds: [] as number[],
  attendeeIds: [] as number[],
  agenda: '',
  notes: '',
  remark: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 选项配置
const typeOptions = [
  { label: '产品演示', value: 'demo' },
  { label: '需求沟通', value: 'requirement' },
  { label: '商务洽谈', value: 'business' },
  { label: '技术交流', value: 'technical' },
  { label: '项目评审', value: 'review' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '已安排', value: 'scheduled' },
  { label: '进行中', value: 'ongoing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
  { label: '已延期', value: 'postponed' }
]

const customerOptions = ref([
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 }
])

const userOptions = ref([
  { label: '销售员A', value: 1, department: '销售部', wechatId: 'sales_a' },
  { label: '销售员B', value: 2, department: '销售部', wechatId: 'sales_b' },
  { label: '技术经理', value: 3, department: '技术部', wechatId: 'tech_manager' },
  { label: '产品经理', value: 4, department: '产品部', wechatId: 'product_manager' },
  { label: '客服主管', value: 5, department: '客服部', wechatId: 'service_manager' }
])

// 企业微信相关状态
const showEmployeeModal = ref(false)
const showNotificationModal = ref(false)
const selectedEmployees = ref<number[]>([])
const notificationContent = ref('')
const currentMeetingForNotification = ref<MeetingRecord | null>(null)

// 表单验证规则
const formRules: FormRules = {
  title: {
    required: true,
    message: '请输入会议主题',
    trigger: 'blur'
  },
  type: {
    required: true,
    message: '请选择会议类型',
    trigger: 'change'
  },
  startTime: {
    required: true,
    type: 'number',
    message: '请选择开始时间',
    trigger: 'change'
  },
  endTime: {
    required: true,
    type: 'number',
    message: '请选择结束时间',
    trigger: 'change'
  },
  status: {
    required: true,
    message: '请选择会议状态',
    trigger: 'change'
  },
  customerIds: {
    required: true,
    type: 'array',
    min: 1,
    message: '请选择至少一个参与客户',
    trigger: 'change'
  }
}

// 表格列配置
const columns: DataTableColumns<MeetingRecord> = [
  {
    title: '会议主题',
    key: 'title',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '会议类型',
    key: 'type',
    width: 100,
    render: (row) => {
      const type = typeOptions.find(item => item.value === row.type)
      return type?.label || row.type
    }
  },
  {
    title: '开始时间',
    key: 'startTime',
    width: 160,
    render: (row) => row.startTime ? new Date(row.startTime).toLocaleString() : '-'
  },
  {
    title: '结束时间',
    key: 'endTime',
    width: 160,
    render: (row) => row.endTime ? new Date(row.endTime).toLocaleString() : '-'
  },
  {
    title: '会议地点',
    key: 'location',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '参与客户',
    key: 'customerNames',
    width: 150,
    render: (row) => row.customerNames?.join(', ') || '-'
  },
  {
    title: '会议状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const status = statusOptions.find(item => item.value === row.status)
      const statusMap: Record<string, string> = {
        scheduled: 'info',
        ongoing: 'warning',
        completed: 'success',
        cancelled: 'error',
        postponed: 'default'
      }
      return h(
        'n-tag',
        { type: statusMap[row.status] || 'default', size: 'small' },
        { default: () => status?.label || row.status }
      )
    }
  },
  {
    title: '创建人',
    key: 'created_by',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row) => [
      h(
        'n-button',
        {
          size: 'small',
          type: 'info',
          ghost: true,
          onClick: () => handleView(row)
        },
        { default: () => '查看', icon: ViewIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleEdit(row)
        },
        { default: () => '编辑', icon: EditIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'warning',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleJoin(row)
        },
        { default: () => '加入', icon: JoinIcon }
      ),
      h(
        'n-button',
        {
          size: 'small',
          type: 'success',
          ghost: true,
          style: { marginLeft: '8px' },
          onClick: () => handleSendWechatNotification(row)
        },
        { default: () => '通知' }
      )
    ]
  }
]

// 方法
const handleAdd = () => {
  currentMeeting.value = null
  resetForm()
  showModal.value = true
}

const handleEdit = (meeting: MeetingRecord) => {
  currentMeeting.value = meeting
  Object.assign(formData, {
    id: meeting.id,
    title: meeting.title,
    type: meeting.type,
    startTime: meeting.startTime ? new Date(meeting.startTime).getTime() : null,
    endTime: meeting.endTime ? new Date(meeting.endTime).getTime() : null,
    location: meeting.location || '',
    status: meeting.status,
    customerIds: meeting.customerIds || [],
    attendeeIds: meeting.attendeeIds || [],
    agenda: meeting.agenda || '',
    notes: meeting.notes || '',
    remark: meeting.remark || ''
  })
  showModal.value = true
}

const handleView = (meeting: MeetingRecord) => {
  // TODO: 实现查看详情功能
  message.info('查看功能开发中')
}

const handleJoin = (meeting: MeetingRecord) => {
  // TODO: 实现加入会议功能
  message.info('加入会议功能开发中')
}

const handleCalendarView = () => {
  // TODO: 实现日历视图
  message.info('日历视图开发中')
}

const handleSubmit = async () => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // TODO: 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(currentMeeting.value ? '更新成功' : '创建成功')
    showModal.value = false
    
    // 如果是新建会议且有参与人员，询问是否发送企业微信通知
    if (!currentMeeting.value && formData.attendeeIds.length > 0) {
      currentMeetingForNotification.value = {
        id: Date.now(), // 模拟新建会议的ID
        title: formData.title,
        type: formData.type,
        customer_id: formData.customerIds[0] || 0,
        customer_name: '',
        customerIds: formData.customerIds,
        customerNames: [],
        meeting_time: formData.startTime ? new Date(formData.startTime).getTime() : null,
        startTime: formData.startTime ? new Date(formData.startTime).toISOString() : '',
        endTime: formData.endTime ? new Date(formData.endTime).toISOString() : '',
        duration: 0,
        location: formData.location,
        participants: [],
        attendeeIds: formData.attendeeIds,
        attendeeNames: [],
        content: formData.agenda,
        agenda: formData.agenda,
        notes: formData.notes,
        status: formData.status,
        created_by: 1,
        created_by_name: '当前用户',
        remark: formData.remark,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as MeetingRecord
      
      // 自动生成通知内容
      notificationContent.value = `【会议通知】\n\n会议主题：${formData.title}\n会议时间：${formData.startTime ? new Date(formData.startTime).toLocaleString() : '待定'}\n会议地点：${formData.location || '线上会议'}\n\n请准时参加会议，谢谢！`
      
      showNotificationModal.value = true
    }
    
    await fetchMeetings()
    return true
  } catch (error) {
    message.error('保存失败')
    return false
  } finally {
    loading.value = false
  }
}

// 添加员工处理
const handleAddEmployees = () => {
  if (selectedEmployees.value.length === 0) {
    message.warning('请选择要添加的员工')
    return false
  }
  
  // 将选中的员工添加到参与人员列表
  const newAttendees = [...new Set([...formData.attendeeIds, ...selectedEmployees.value])]
  formData.attendeeIds = newAttendees
  
  message.success(`成功添加 ${selectedEmployees.value.length} 名员工`)
  showEmployeeModal.value = false
  selectedEmployees.value = []
  return true
}

// 发送企业微信通知
const handleSendWechatNotification = (meeting: MeetingRecord) => {
  currentMeetingForNotification.value = meeting
  
  // 自动生成通知内容
  notificationContent.value = `【会议提醒】\n\n会议主题：${meeting.title}\n会议时间：${meeting.startTime ? new Date(meeting.startTime).toLocaleString() : '待定'}\n会议地点：${meeting.location || '线上会议'}\n\n请准时参加会议，谢谢！`
  
  showNotificationModal.value = true
}

// 发送通知处理
const handleSendNotification = async () => {
  if (!currentMeetingForNotification.value) return false
  
  try {
    loading.value = true
    
    // 获取要通知的员工信息
    const notifyEmployees = userOptions.value.filter(user => 
      formData.attendeeIds.includes(user.value)
    )
    
    if (notifyEmployees.length === 0) {
      message.warning('没有选择要通知的员工')
      return false
    }
    
    // TODO: 调用企业微信API发送通知
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    message.success(`成功向 ${notifyEmployees.length} 名员工发送企业微信通知`)
    showNotificationModal.value = false
    notificationContent.value = ''
    currentMeetingForNotification.value = null
    
    return true
  } catch (error) {
    message.error('发送通知失败，请重试')
    return false
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchMeetings()
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    customerName: '',
    status: null,
    dateRange: null
  })
  pagination.page = 1
  fetchMeetings()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchMeetings()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchMeetings()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    title: '',
    type: '',
    startTime: null,
    endTime: null,
    location: '',
    status: 'scheduled',
    customerIds: [],
    attendeeIds: [],
    agenda: '',
    notes: '',
    remark: ''
  })
}

// 获取会议列表
const fetchMeetings = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: MeetingRecord[] = [
      {
        id: 1,
        customer_id: 1,
        customer_name: '张三',
        title: '产品演示会议',
        type: 'demo',
        meeting_time: new Date('2024-01-20T10:00:00Z').getTime(),
        startTime: '2024-01-20T10:00:00Z',
        endTime: '2024-01-20T11:30:00Z',
        duration: 90,
        location: '会议室A',
        participants: ['销售员A', '销售员B'],
        customerIds: [1, 2],
        customerNames: ['张三', '李四'],
        attendeeIds: [1, 2],
        attendeeNames: ['销售员A', '销售员B'],
        content: '产品演示会议内容',
        agenda: '1. 产品功能介绍\n2. 演示操作流程\n3. 答疑解惑',
        notes: '',
        status: 'scheduled',
        created_by: 1,
        created_by_name: '管理员',
        remark: '',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        customer_id: 1,
        customer_name: '张三',
        title: '需求沟通会议',
        type: 'requirement',
        meeting_time: new Date('2024-01-18T14:00:00Z').getTime(),
        startTime: '2024-01-18T14:00:00Z',
        endTime: '2024-01-18T16:00:00Z',
        duration: 120,
        location: '线上会议',
        participants: ['销售员A', '技术经理'],
        customerIds: [1],
        customerNames: ['张三'],
        attendeeIds: [1, 3],
        attendeeNames: ['销售员A', '技术经理'],
        content: '需求沟通会议内容',
        agenda: '1. 了解客户需求\n2. 技术方案讨论\n3. 项目时间安排',
        notes: '客户需求明确，技术方案可行，预计下周开始项目实施',
        status: 'completed',
        created_by: 1,
        created_by_name: '管理员',
        remark: '重要客户',
        created_at: '2024-01-16T09:00:00Z',
        updated_at: '2024-01-18T16:30:00Z'
      }
    ]
    
    meetings.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('获取会议列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    Object.assign(stats, {
      total: 45,
      today: 3,
      week: 12,
      upcoming: 8
    })
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

// 初始化
onMounted(() => {
  fetchMeetings()
  fetchStats()
})
</script>

<style scoped>
.meeting-list {
  padding: 0;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.table-card {
  margin-bottom: 20px;
}

/* 员工选择相关样式 */
.employee-selection {
  max-height: 400px;
  overflow-y: auto;
}

.employee-item {
  padding: 12px;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.employee-item:hover {
  background-color: var(--n-color-hover);
  border-color: var(--n-border-color-hover);
}

.employee-info {
  margin-left: 8px;
}

.employee-name {
  font-weight: 600;
  color: var(--n-text-color);
  margin-bottom: 4px;
}

.employee-dept {
  font-size: 12px;
  color: var(--n-text-color-2);
  margin-bottom: 2px;
}

.employee-wechat {
  font-size: 11px;
  color: var(--n-text-color-3);
  font-family: monospace;
}

/* 通知内容样式 */
.notification-content {
  max-height: 500px;
  overflow-y: auto;
}
</style>