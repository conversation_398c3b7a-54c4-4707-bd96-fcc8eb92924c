import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

// Supabase 配置
const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置信息')
  console.error('请确保设置了以下环境变量:')
  console.error('- VITE_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// 用户数据
const mockUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    department: '管理部',
    phone: '13800138000',
    status: 'active'
  },
  {
    username: 'sales1',
    email: '<EMAIL>',
    role: 'sales',
    department: '销售部',
    phone: '13800138001',
    status: 'active'
  },
  {
    username: 'sales2',
    email: '<EMAIL>',
    role: 'sales',
    department: '销售部',
    phone: '13800138002',
    status: 'active'
  }
]

// 客户数据
const mockCustomers = [
  {
    name: '张三',
    phone: '13800138001',
    email: '<EMAIL>',
    company: '阿里巴巴',
    position: '技术总监',
    source: '朋友介绍',
    level: 'A',
    status: 'active',
    region: '浙江省,杭州市',
    address: '杭州市西湖区文三路',
    notes: '技术背景强，有合作意向',
    tags: 'VIP,技术'
  },
  {
    name: '李四',
    phone: '13800138002',
    email: '<EMAIL>',
    company: '腾讯科技',
    position: '产品经理',
    source: '网络推广',
    level: 'B',
    status: 'active',
    region: '广东省,深圳市',
    address: '深圳市南山区科技园',
    notes: '对产品很感兴趣',
    tags: '产品,互联网'
  },
  {
    name: '王五',
    phone: '13800138003',
    email: '<EMAIL>',
    company: '百度',
    position: '运营总监',
    source: '展会',
    level: 'A',
    status: 'active',
    region: '北京市,海淀区',
    address: '北京市海淀区上地十街',
    notes: '运营经验丰富',
    tags: '运营,大厂'
  },
  {
    name: '赵六',
    phone: '13800138004',
    email: '<EMAIL>',
    company: '字节跳动',
    position: '技术专家',
    source: '朋友介绍',
    level: 'A',
    status: 'potential',
    region: '北京市,朝阳区',
    address: '北京市朝阳区望京',
    notes: '技术实力强，正在考虑中',
    tags: '技术,潜在客户'
  },
  {
    name: '孙七',
    phone: '13800138005',
    email: '<EMAIL>',
    company: '美团',
    position: '业务总监',
    source: '网络推广',
    level: 'B',
    status: 'inactive',
    region: '北京市,海淀区',
    address: '北京市海淀区中关村',
    notes: '暂时没有合作需求',
    tags: '业务,暂停'
  }
]

// 营销活动数据
const mockCampaigns = [
  {
    name: '春季产品发布会',
    description: '2024年春季新产品发布活动',
    type: 'event',
    status: 'active',
    start_time: '2024-03-15T09:00:00Z',
    end_time: '2024-03-15T18:00:00Z',
    budget: 50000,
    spent: 45000,
    target_audience: { type: '企业客户', industry: ['科技', '金融'] },
    rules: { max_participants: 200, registration_required: true },
    prizes: [{ name: '一等奖', value: 5000, quantity: 1 }, { name: '二等奖', value: 2000, quantity: 3 }],
    participant_count: 180,
    conversion_count: 28,
    share_count: 45,
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    name: '夏季促销活动',
    description: '夏季产品优惠促销',
    type: 'promotion',
    status: 'completed',
    start_time: '2024-06-01T00:00:00Z',
    end_time: '2024-06-30T23:59:59Z',
    budget: 30000,
    spent: 28500,
    target_audience: { type: '个人用户', age_range: ['25-35', '36-45'] },
    rules: { discount_rate: 0.2, min_purchase: 100 },
    prizes: [{ name: '优惠券', value: 50, quantity: 100 }],
    participant_count: 650,
    conversion_count: 83,
    share_count: 120,
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    name: '秋季会员招募',
    description: '秋季新会员招募活动',
    type: 'membership',
    status: 'draft',
    start_time: '2024-09-01T00:00:00Z',
    end_time: '2024-09-30T23:59:59Z',
    budget: 20000,
    spent: 0,
    target_audience: { type: '潜在客户', source: ['网络', '推荐'] },
    rules: { membership_fee: 99, benefits: ['专属折扣', '优先服务'] },
    prizes: [{ name: '会员礼包', value: 200, quantity: 50 }],
    participant_count: 0,
    conversion_count: 0,
    share_count: 0,
    created_by: '00000000-0000-0000-0000-000000000000'
  }
]

// 跟进记录数据
const mockFollowUps = [
  {
    customer_id: 1, // 将在迁移后更新为实际ID
    type: 'phone',
    content: '电话沟通产品需求，客户表示很感兴趣',
    result: 'positive',
    next_action: '发送产品详细资料',
    next_follow_up_at: '2024-08-20T10:00:00Z',
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    customer_id: 2,
    type: 'email',
    content: '发送产品介绍邮件，等待客户回复',
    result: 'pending',
    next_action: '电话跟进',
    next_follow_up_at: '2024-08-18T14:00:00Z',
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    customer_id: 3,
    type: 'meeting',
    content: '面谈讨论合作方案，客户提出了一些技术问题',
    result: 'neutral',
    next_action: '准备技术方案回复',
    next_follow_up_at: '2024-08-22T09:00:00Z',
    created_by: '00000000-0000-0000-0000-000000000000'
  }
]

// 见面记录数据
const mockMeetings = [
  {
    customer_id: 1,
    title: '产品演示会议',
    type: 'client_meeting',
    location: '阿里巴巴总部会议室',
    start_time: '2024-08-15T14:00:00Z',
    end_time: '2024-08-15T16:00:00Z',
    meeting_time: '2024-08-15T14:00:00Z',
    duration: 120,
    attendees: ['张三', '李经理', '王技术'],
    agenda: ['产品功能演示', '技术架构讲解', '商务条款讨论'],
    summary: '客户对产品功能很满意，技术架构符合需求，商务条款需要进一步协商',
    action_items: ['准备详细报价单', '提供技术文档', '安排下次商务谈判'],
    status: 'completed',
    organizer_id: '00000000-0000-0000-0000-000000000000',
    created_by: '00000000-0000-0000-0000-000000000000'
  },
  {
    customer_id: 2,
    title: '需求调研会议',
    type: 'requirement_analysis',
    location: '腾讯大厦',
    start_time: '2024-08-12T10:00:00Z',
    end_time: '2024-08-12T11:30:00Z',
    meeting_time: '2024-08-12T10:00:00Z',
    duration: 90,
    attendees: ['李四', '产品团队'],
    agenda: ['了解客户具体需求', '讨论定制化方案'],
    summary: '客户需求明确，需要定制化开发，预算充足',
    action_items: ['制定定制化方案', '评估开发周期', '准备项目提案'],
    status: 'completed',
    organizer_id: '00000000-0000-0000-0000-000000000000',
    created_by: '00000000-0000-0000-0000-000000000000'
  }
]

// 公海记录数据
const mockPublicPool = [
  {
    customer_id: 5,
    reason: '长期无跟进',
    moved_by: '00000000-0000-0000-0000-000000000000',
    moved_at: '2024-08-10T09:00:00Z',
    notes: '客户3个月未回复，暂时放入公海'
  }
]

async function migrateTable(tableName: string, data: any[], description: string) {
  try {
    console.log(`🔄 开始迁移${description}...`)
    
    // 清空现有数据（可选）
    await supabase.from(tableName).delete().neq('id', 0)
    
    // 插入数据
    const { data: result, error } = await supabase
      .from(tableName)
      .insert(data)
      .select()
    
    if (error) {
      console.error(`❌ ${description}迁移失败:`, error.message)
      return { success: false, data: null }
    }
    
    console.log(`✅ 成功迁移 ${result.length} 条${description}`)
    return { success: true, data: result }
  } catch (error) {
    console.error(`❌ ${description}迁移异常:`, error)
    return { success: false, data: null }
  }
}

async function migrateFollowUpsWithCustomerIds(customers: any[]) {
  try {
    console.log('🔄 开始迁移跟进记录数据...')
    
    // 更新跟进记录中的客户ID
    const followUpsWithIds = mockFollowUps.map((followUp, index) => ({
      ...followUp,
      customer_id: customers[index % customers.length].id
    }))
    
    const result = await migrateTable('follow_ups', followUpsWithIds, '跟进记录数据')
    return result
  } catch (error) {
    console.error('❌ 跟进记录数据迁移异常:', error)
    return { success: false, data: null }
  }
}

async function migrateMeetingsWithCustomerIds(customers: any[]) {
  try {
    console.log('🔄 开始迁移见面记录数据...')
    
    // 获取实际的用户ID
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1)
    
    if (usersError || !users || users.length === 0) {
      console.error('❌ 无法获取用户ID，跳过见面记录迁移')
      return { success: false, data: null }
    }
    
    const userId = users[0].id
    
    // 更新见面记录中的客户ID和用户ID
    const meetingsWithIds = mockMeetings.map((meeting, index) => ({
      ...meeting,
      customer_id: customers[index % customers.length].id,
      organizer_id: userId,
      created_by: userId
    }))
    
    const result = await migrateTable('meetings', meetingsWithIds, '见面记录数据')
    return result
  } catch (error) {
    console.error('❌ 见面记录数据迁移异常:', error)
    return { success: false, data: null }
  }
}

async function migratePublicPoolWithCustomerIds(customers: any[]) {
  try {
    console.log('🔄 开始迁移公海记录数据...')
    
    // 更新公海记录中的客户ID
    const publicPoolWithIds = mockPublicPool.map(record => ({
      ...record,
      customer_id: customers[customers.length - 1].id // 使用最后一个客户
    }))
    
    const result = await migrateTable('public_pool', publicPoolWithIds, '公海记录数据')
    return result
  } catch (error) {
    console.error('❌ 公海记录数据迁移异常:', error)
    return { success: false, data: null }
  }
}

async function validateAllMigration() {
  try {
    console.log('🔄 验证所有迁移结果...')
    
    const tables = [
      { name: 'users', description: '用户' },
      { name: 'customers', description: '客户' },
      { name: 'marketing_campaigns', description: '营销活动' },
      { name: 'follow_ups', description: '跟进记录' },
      { name: 'meetings', description: '见面记录' },
      { name: 'public_pool', description: '公海记录' }
    ]
    
    console.log('✅ 数据验证完成')
    
    for (const table of tables) {
      const { count, error } = await supabase
        .from(table.name)
        .select('*', { count: 'exact', head: true })
      
      if (error) {
        console.log(`   - ${table.description}: 验证失败 (${error.message})`)
      } else {
        console.log(`   - ${table.description}: ${count || 0} 条`)
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ 数据验证异常:', error)
    return false
  }
}

async function main() {
  console.log('🚀 开始完整数据迁移\n')
  
  // 迁移用户数据
  const usersResult = await migrateTable('users', mockUsers, '用户数据')
  if (!usersResult.success) {
    console.log('⚠️  用户数据迁移失败，但继续执行其他迁移')
  }
  
  // 迁移客户数据
  const customersResult = await migrateTable('customers', mockCustomers, '客户数据')
  if (!customersResult.success) {
    console.error('❌ 客户数据迁移失败，停止执行')
    process.exit(1)
  }
  
  // 迁移营销活动数据
  const campaignsResult = await migrateTable('marketing_campaigns', mockCampaigns, '营销活动数据')
  if (!campaignsResult.success) {
    console.log('⚠️  营销活动数据迁移失败，但继续执行其他迁移')
  }
  
  // 迁移跟进记录数据（需要客户ID）
  if (customersResult.data) {
    await migrateFollowUpsWithCustomerIds(customersResult.data)
    await migrateMeetingsWithCustomerIds(customersResult.data)
    await migratePublicPoolWithCustomerIds(customersResult.data)
  }
  
  // 验证迁移结果
  const validationOk = await validateAllMigration()
  if (!validationOk) {
    console.error('❌ 数据验证失败')
    process.exit(1)
  }
  
  console.log('\n🎉 完整数据迁移完成！所有测试数据已成功导入到 Supabase 数据库。')
  console.log('\n📝 迁移摘要:')
  console.log('   - ✅ 用户数据迁移完成')
  console.log('   - ✅ 客户数据迁移完成')
  console.log('   - ✅ 营销活动数据迁移完成')
  console.log('   - ✅ 跟进记录数据迁移完成')
  console.log('   - ✅ 见面记录数据迁移完成')
  console.log('   - ✅ 公海记录数据迁移完成')
  console.log('   - ✅ 数据完整性验证通过')
  console.log('\n🔗 现在前端可以通过 Supabase 客户端调用这些完整的测试数据了！')
  console.log('\n💡 提示: 可以在前端使用以下方式获取数据:')
  console.log('   - 客户列表: supabase.from("customers").select("*")')
  console.log('   - 营销活动: supabase.from("marketing_campaigns").select("*")')
  console.log('   - 跟进记录: supabase.from("follow_ups").select("*, customers(name)")')
}

// 执行迁移
main().catch(console.error)