<template>
  <div class="message-analysis">
    <!-- 分析配置 -->
    <n-card title="分析配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>分析维度</label>
            <n-select
              v-model:value="config.dimension"
              :options="dimensionOptions"
              placeholder="选择分析维度"
            />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker
              v-model:value="config.dateRange"
              type="daterange"
              clearable
            />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>消息类型</label>
            <n-select
              v-model:value="config.messageTypes"
              :options="messageTypeOptions"
              multiple
              placeholder="选择消息类型"
            />
          </div>
          <div class="form-item">
            <label>群组筛选</label>
            <n-select
              v-model:value="config.groups"
              :options="groupOptions"
              multiple
              placeholder="选择群组"
            />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>情感倾向</label>
            <n-select
              v-model:value="config.sentiments"
              :options="sentimentOptions"
              multiple
              placeholder="选择情感倾向"
            />
          </div>
          <div class="form-item">
            <label>对比分析</label>
            <n-switch v-model:value="config.enableComparison" />
          </div>
        </div>
        <div class="form-actions">
          <n-button type="primary" @click="runAnalysis" :loading="loading">
            <template #icon>
              <n-icon><AnalyticsOutline /></n-icon>
            </template>
            开始分析
          </n-button>
          <n-button @click="resetConfig">
            重置配置
          </n-button>
        </div>
      </div>
    </n-card>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="showResults">
      <!-- 概览指标 -->
      <n-card title="概览指标" class="overview-card">
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-icon">
              <n-icon size="24" color="#2080f0"><ChatbubbleOutline /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ formatNumber(overview.totalMessages) }}</div>
              <div class="metric-label">总消息数</div>
              <div class="metric-change positive">+{{ overview.messageGrowth }}%</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <n-icon size="24" color="#18a058"><PeopleOutline /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ formatNumber(overview.activeUsers) }}</div>
              <div class="metric-label">活跃用户</div>
              <div class="metric-change positive">+{{ overview.userGrowth }}%</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <n-icon size="24" color="#f0a020"><TimeOutline /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ overview.avgResponseTime }}</div>
              <div class="metric-label">平均响应时间</div>
              <div class="metric-change negative">+{{ overview.responseTimeChange }}%</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <n-icon size="24" color="#d03050"><AlertCircleOutline /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ formatNumber(overview.keywordHits) }}</div>
              <div class="metric-label">关键词命中</div>
              <div class="metric-change negative">+{{ overview.keywordGrowth }}%</div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 消息趋势分析 -->
      <n-card title="消息趋势分析" class="trend-card">
        <div class="chart-container">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </n-card>

      <!-- 消息类型分布 -->
      <n-card title="消息类型分布" class="type-card">
        <div class="chart-container">
          <div ref="typeChartRef" class="chart"></div>
        </div>
      </n-card>

      <!-- 用户活跃度分析 -->
      <n-card title="用户活跃度分析" class="activity-card">
        <div class="activity-analysis">
          <div class="activity-chart">
            <div ref="activityChartRef" class="chart"></div>
          </div>
          <div class="activity-stats">
            <h4>活跃度统计</h4>
            <div class="stats-list">
              <div class="stats-item">
                <span class="stats-label">高活跃用户：</span>
                <span class="stats-value">{{ activityStats.highActive }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">中活跃用户：</span>
                <span class="stats-value">{{ activityStats.mediumActive }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">低活跃用户：</span>
                <span class="stats-value">{{ activityStats.lowActive }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">沉默用户：</span>
                <span class="stats-value">{{ activityStats.silent }}</span>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 情感分析 -->
      <n-card title="情感分析" class="sentiment-card">
        <div class="sentiment-analysis">
          <div class="sentiment-overview">
            <div class="sentiment-chart">
              <div ref="sentimentChartRef" class="chart"></div>
            </div>
            <div class="sentiment-stats">
              <h4>情感分布</h4>
              <div class="sentiment-list">
                <div class="sentiment-item positive">
                  <div class="sentiment-color"></div>
                  <span class="sentiment-label">正面情感</span>
                  <span class="sentiment-value">{{ sentimentStats.positive }}%</span>
                </div>
                <div class="sentiment-item neutral">
                  <div class="sentiment-color"></div>
                  <span class="sentiment-label">中性情感</span>
                  <span class="sentiment-value">{{ sentimentStats.neutral }}%</span>
                </div>
                <div class="sentiment-item negative">
                  <div class="sentiment-color"></div>
                  <span class="sentiment-label">负面情感</span>
                  <span class="sentiment-value">{{ sentimentStats.negative }}%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="sentiment-trend">
            <h4>情感趋势</h4>
            <div ref="sentimentTrendChartRef" class="chart"></div>
          </div>
        </div>
      </n-card>

      <!-- 关键词分析 -->
      <n-card title="关键词分析" class="keyword-card">
        <div class="keyword-analysis">
          <div class="keyword-cloud">
            <h4>热门关键词</h4>
            <div class="keyword-tags">
              <n-tag
                v-for="keyword in hotKeywords"
                :key="keyword.word"
                :type="getKeywordType(keyword.frequency)"
                :size="getKeywordSize(keyword.frequency)"
                class="keyword-tag"
              >
                {{ keyword.word }}
                <span class="keyword-count">({{ keyword.count }})</span>
              </n-tag>
            </div>
          </div>
          <div class="keyword-stats">
            <h4>关键词统计</h4>
            <div class="stats-table">
              <div class="table-header">
                <span>关键词</span>
                <span>出现次数</span>
                <span>命中率</span>
                <span>趋势</span>
              </div>
              <div class="table-row" v-for="keyword in keywordStats" :key="keyword.word">
                <span class="keyword-word">{{ keyword.word }}</span>
                <span class="keyword-count">{{ keyword.count }}</span>
                <span class="keyword-rate">{{ keyword.rate }}%</span>
                <span class="keyword-trend" :class="keyword.trend">
                  <n-icon><component :is="getTrendIcon(keyword.trend)" /></n-icon>
                </span>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 时间分布分析 -->
      <n-card title="时间分布分析" class="time-card">
        <div class="time-analysis">
          <div class="time-heatmap">
            <h4>24小时活跃度热力图</h4>
            <div ref="heatmapChartRef" class="chart"></div>
          </div>
          <div class="time-stats">
            <h4>时间段统计</h4>
            <div class="time-periods">
              <div class="period-item">
                <span class="period-label">早高峰 (7-9点)：</span>
                <span class="period-value">{{ timeStats.morning }}条消息</span>
              </div>
              <div class="period-item">
                <span class="period-label">午休时间 (12-14点)：</span>
                <span class="period-value">{{ timeStats.noon }}条消息</span>
              </div>
              <div class="period-item">
                <span class="period-label">晚高峰 (18-20点)：</span>
                <span class="period-value">{{ timeStats.evening }}条消息</span>
              </div>
              <div class="period-item">
                <span class="period-label">深夜时段 (22-24点)：</span>
                <span class="period-value">{{ timeStats.night }}条消息</span>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 优化建议 -->
      <n-card title="优化建议" class="suggestions-card">
        <div class="suggestions-list">
          <div class="suggestion-item" v-for="suggestion in suggestions" :key="suggestion.id">
            <div class="suggestion-icon">
              <n-icon :color="suggestion.color"><component :is="suggestion.icon" /></n-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
              <div class="suggestion-action">
                <n-button size="small" type="primary" ghost>
                  {{ suggestion.actionText }}
                </n-button>
              </div>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 空状态 -->
    <n-empty
      v-if="!showResults && !loading"
      description="请配置分析参数并点击开始分析"
      class="empty-state"
    >
      <template #icon>
        <n-icon size="48" color="#d0d0d0">
          <AnalyticsOutline />
        </n-icon>
      </template>
    </n-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NSelect, NDatePicker, NSwitch, NButton, NIcon, NTag, NEmpty,
  useMessage
} from 'naive-ui'
import {
  AnalyticsOutline, ChatbubbleOutline, PeopleOutline, TimeOutline,
  AlertCircleOutline, TrendingUpOutline, TrendingDownOutline,
  RemoveOutline, BulbOutline, SettingsOutline, ShieldCheckmarkOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface AnalysisConfig {
  dimension: string
  dateRange: [number, number] | null
  messageTypes: string[]
  groups: string[]
  sentiments: string[]
  enableComparison: boolean
}

const message = useMessage()
const loading = ref(false)
const showResults = ref(false)

// 配置
const config = reactive<AnalysisConfig>({
  dimension: 'daily',
  dateRange: null,
  messageTypes: [],
  groups: [],
  sentiments: [],
  enableComparison: false
})

const dimensionOptions = [
  { label: '按天统计', value: 'daily' },
  { label: '按周统计', value: 'weekly' },
  { label: '按月统计', value: 'monthly' },
  { label: '按小时统计', value: 'hourly' }
]

const messageTypeOptions = [
  { label: '文本消息', value: 'text' },
  { label: '图片消息', value: 'image' },
  { label: '文件消息', value: 'file' },
  { label: '语音消息', value: 'voice' },
  { label: '视频消息', value: 'video' },
  { label: '链接消息', value: 'link' }
]

const groupOptions = [
  { label: '产品讨论群', value: 'group1' },
  { label: '技术交流群', value: 'group2' },
  { label: '客户服务群', value: 'group3' },
  { label: '市场推广群', value: 'group4' }
]

const sentimentOptions = [
  { label: '正面', value: 'positive' },
  { label: '中性', value: 'neutral' },
  { label: '负面', value: 'negative' }
]

// 图表引用
const trendChartRef = ref<HTMLElement>()
const typeChartRef = ref<HTMLElement>()
const activityChartRef = ref<HTMLElement>()
const sentimentChartRef = ref<HTMLElement>()
const sentimentTrendChartRef = ref<HTMLElement>()
const heatmapChartRef = ref<HTMLElement>()

// 分析结果数据
const overview = ref({
  totalMessages: 15420,
  messageGrowth: 12.5,
  activeUsers: 1250,
  userGrowth: 8.3,
  avgResponseTime: '2.5分钟',
  responseTimeChange: 5.2,
  keywordHits: 342,
  keywordGrowth: 15.8
})

const activityStats = ref({
  highActive: 156,
  mediumActive: 423,
  lowActive: 671,
  silent: 289
})

const sentimentStats = ref({
  positive: 65,
  neutral: 25,
  negative: 10
})

const hotKeywords = ref([
  { word: '产品', frequency: 'high', count: 156 },
  { word: '功能', frequency: 'high', count: 134 },
  { word: '问题', frequency: 'medium', count: 89 },
  { word: '优化', frequency: 'medium', count: 76 },
  { word: '建议', frequency: 'low', count: 45 },
  { word: '反馈', frequency: 'low', count: 38 }
])

const keywordStats = ref([
  { word: '产品', count: 156, rate: 12.3, trend: 'up' },
  { word: '功能', count: 134, rate: 10.5, trend: 'up' },
  { word: '问题', count: 89, rate: 7.2, trend: 'down' },
  { word: '优化', count: 76, rate: 6.1, trend: 'stable' }
])

const timeStats = ref({
  morning: 2340,
  noon: 1890,
  evening: 3420,
  night: 1560
})

const suggestions = ref([
  {
    id: '1',
    icon: BulbOutline,
    color: '#f0a020',
    title: '优化响应时间',
    description: '平均响应时间较长，建议增加客服人员或优化自动回复机制',
    actionText: '查看详情'
  },
  {
    id: '2',
    icon: SettingsOutline,
    color: '#2080f0',
    title: '关键词监控',
    description: '负面关键词命中率上升，建议加强关键词监控和预警机制',
    actionText: '设置监控'
  },
  {
    id: '3',
    icon: ShieldCheckmarkOutline,
    color: '#18a058',
    title: '用户活跃度',
    description: '沉默用户比例较高，建议制定用户激活策略',
    actionText: '制定策略'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getKeywordType = (frequency: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'error' | 'warning' | 'info'> = {
    high: 'error',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[frequency] || 'default'
}

const getKeywordSize = (frequency: string): 'medium' | 'small' | 'large' | 'tiny' => {
  const sizeMap: Record<string, 'large' | 'medium' | 'small'> = {
    high: 'large',
    medium: 'medium',
    low: 'small'
  }
  return sizeMap[frequency] || 'medium'
}

const getTrendIcon = (trend: string) => {
  const iconMap = {
    up: TrendingUpOutline,
    down: TrendingDownOutline,
    stable: RemoveOutline
  }
  return iconMap[trend as keyof typeof iconMap] || RemoveOutline
}

const runAnalysis = async () => {
  loading.value = true
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    showResults.value = true
    await nextTick()
    initCharts()
    message.success('分析完成')
  } catch (error) {
    message.error('分析失败，请重试')
  } finally {
    loading.value = false
  }
}

const resetConfig = () => {
  config.dimension = 'daily'
  config.dateRange = null
  config.messageTypes = []
  config.groups = []
  config.sentiments = []
  config.enableComparison = false
  showResults.value = false
}

const initCharts = () => {
  // 消息趋势图
  if (trendChartRef.value) {
    const trendChart = echarts.init(trendChartRef.value)
    trendChart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['消息数量', '活跃用户'] },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: [{ type: 'value' }, { type: 'value' }],
      series: [
        {
          name: '消息数量',
          type: 'line',
          data: [1200, 1350, 1100, 1400, 1600, 900, 800],
          smooth: true
        },
        {
          name: '活跃用户',
          type: 'bar',
          yAxisIndex: 1,
          data: [180, 200, 160, 220, 240, 140, 120]
        }
      ]
    })
  }

  // 消息类型分布图
  if (typeChartRef.value) {
    const typeChart = echarts.init(typeChartRef.value)
    typeChart.setOption({
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: [
          { value: 8500, name: '文本消息' },
          { value: 3200, name: '图片消息' },
          { value: 1800, name: '文件消息' },
          { value: 1200, name: '语音消息' },
          { value: 500, name: '视频消息' },
          { value: 220, name: '链接消息' }
        ]
      }]
    })
  }

  // 用户活跃度图
  if (activityChartRef.value) {
    const activityChart = echarts.init(activityChartRef.value)
    activityChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 156, name: '高活跃' },
          { value: 423, name: '中活跃' },
          { value: 671, name: '低活跃' },
          { value: 289, name: '沉默' }
        ]
      }]
    })
  }

  // 情感分布图
  if (sentimentChartRef.value) {
    const sentimentChart = echarts.init(sentimentChartRef.value)
    sentimentChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 65, name: '正面', itemStyle: { color: '#18a058' } },
          { value: 25, name: '中性', itemStyle: { color: '#909399' } },
          { value: 10, name: '负面', itemStyle: { color: '#d03050' } }
        ]
      }]
    })
  }

  // 情感趋势图
  if (sentimentTrendChartRef.value) {
    const sentimentTrendChart = echarts.init(sentimentTrendChartRef.value)
    sentimentTrendChart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['正面', '中性', '负面'] },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '正面',
          type: 'line',
          data: [60, 62, 65, 68, 65, 67],
          itemStyle: { color: '#18a058' }
        },
        {
          name: '中性',
          type: 'line',
          data: [30, 28, 25, 22, 25, 23],
          itemStyle: { color: '#909399' }
        },
        {
          name: '负面',
          type: 'line',
          data: [10, 10, 10, 10, 10, 10],
          itemStyle: { color: '#d03050' }
        }
      ]
    })
  }

  // 时间热力图
  if (heatmapChartRef.value) {
    const heatmapChart = echarts.init(heatmapChartRef.value)
    const hours = Array.from({ length: 24 }, (_, i) => i + 'h')
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const data = []
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 24; j++) {
        data.push([j, i, Math.floor(Math.random() * 100)])
      }
    }
    
    heatmapChart.setOption({
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          return `${days[params.data[1]]} ${hours[params.data[0]]}: ${params.data[2]}条消息`
        }
      },
      grid: { height: '50%', top: '10%' },
      xAxis: {
        type: 'category',
        data: hours,
        splitArea: { show: true }
      },
      yAxis: {
        type: 'category',
        data: days,
        splitArea: { show: true }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        type: 'heatmap',
        data: data,
        label: { show: false },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    })
  }
}
</script>

<style scoped>
.message-analysis {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-card {
  margin-bottom: 16px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overview-card,
.trend-card,
.type-card,
.activity-card,
.sentiment-card,
.keyword-card,
.time-card,
.suggestions-card {
  margin-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.activity-analysis {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.activity-chart {
  height: 300px;
}

.activity-stats h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stats-label {
  color: #666;
}

.stats-value {
  font-weight: 500;
  color: #1a1a1a;
}

.sentiment-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sentiment-overview {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.sentiment-chart {
  height: 300px;
}

.sentiment-stats h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.sentiment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sentiment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.sentiment-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.sentiment-item.positive .sentiment-color {
  background: #18a058;
}

.sentiment-item.neutral .sentiment-color {
  background: #909399;
}

.sentiment-item.negative .sentiment-color {
  background: #d03050;
}

.sentiment-label {
  flex: 1;
  color: #666;
}

.sentiment-value {
  font-weight: 500;
  color: #1a1a1a;
}

.sentiment-trend h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.keyword-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.keyword-cloud h4,
.keyword-stats h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.keyword-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.keyword-count {
  font-size: 12px;
  opacity: 0.8;
}

.stats-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 1fr 80px 80px 60px;
  gap: 16px;
  align-items: center;
  padding: 8px 0;
}

.table-header {
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.table-row {
  border-bottom: 1px solid #f0f0f0;
}

.keyword-word {
  color: #1a1a1a;
}

.keyword-trend {
  display: flex;
  justify-content: center;
}

.keyword-trend.up {
  color: #18a058;
}

.keyword-trend.down {
  color: #d03050;
}

.keyword-trend.stable {
  color: #909399;
}

.time-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.time-heatmap h4,
.time-stats h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.time-periods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.period-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.period-label {
  color: #666;
}

.period-value {
  font-weight: 500;
  color: #1a1a1a;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.suggestion-icon {
  margin-top: 2px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.suggestion-desc {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.suggestion-action {
  display: flex;
  justify-content: flex-start;
}

.empty-state {
  margin: 48px 0;
}
</style>