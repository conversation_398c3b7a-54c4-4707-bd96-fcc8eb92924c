import { createClient } from '@supabase/supabase-js';
import { MySQLManager } from '../src/database/MySQLManager';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 迁移配置接口
interface MigrationConfig {
  batchSize: number;
  enableLogging: boolean;
  validateData: boolean;
  incrementalMode: boolean;
  lastMigrationTime?: string;
}

// 迁移日志接口
interface MigrationLog {
  id: string;
  migration_id: string;
  table_name: string;
  operation: string;
  status: 'started' | 'completed' | 'failed';
  records_count: number;
  error_message?: string;
  start_time: Date;
  end_time?: Date;
  duration_ms?: number;
}

// 表迁移统计
interface TableMigrationStats {
  tableName: string;
  totalRecords: number;
  migratedRecords: number;
  failedRecords: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  errors: string[];
}

/**
 * 数据迁移管理器
 */
class DataMigrationManager {
  private supabase: any;
  private mysql: MySQLManager;
  private config: MigrationConfig;
  private migrationId: string;
  private logs: MigrationLog[] = [];
  private stats: TableMigrationStats[] = [];

  constructor(config: MigrationConfig) {
    this.config = config;
    this.migrationId = uuidv4();
    
    // 初始化Supabase客户端
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase配置缺失，请检查环境变量');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseKey);
    
    // 初始化MySQL管理器
    const mysqlConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'workchat_admin',
      connectionLimit: 10
    };
    
    this.mysql = new MySQLManager(mysqlConfig);
  }

  /**
   * 初始化迁移环境
   */
  async initialize(): Promise<void> {
    console.log('🚀 初始化数据迁移环境...');
    
    try {
      // 初始化MySQL连接
      await this.mysql.initialize();
      
      // 创建MySQL表结构
      await this.createMySQLTables();
      
      console.log('✅ 迁移环境初始化完成');
    } catch (error) {
      console.error('❌ 迁移环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建MySQL表结构
   */
  private async createMySQLTables(): Promise<void> {
    console.log('📋 创建MySQL表结构...');
    
    try {
      const sqlFilePath = path.join(__dirname, 'mysql', 'create-mysql-tables.sql');
      const sqlContent = await fs.readFile(sqlFilePath, 'utf-8');
      
      // 分割SQL语句并逐个执行
      const sqlStatements = sqlContent.split(';').filter(stmt => stmt.trim());
      const results = [];
      
      for (const statement of sqlStatements) {
        const result = await this.mysql.query(statement.trim());
        results.push(result);
      }
      
      const failedResults = results.filter(r => !r.success);
      if (failedResults.length > 0) {
        console.warn('⚠️ 部分表创建失败:', failedResults);
      } else {
        console.log('✅ MySQL表结构创建完成');
      }
    } catch (error) {
      console.error('❌ MySQL表结构创建失败:', error);
      throw error;
    }
  }

  /**
   * 执行完整数据迁移
   */
  async migrate(): Promise<void> {
    console.log(`🔄 开始数据迁移 (ID: ${this.migrationId})`);
    
    const startTime = new Date();
    
    try {
      // 定义迁移表的顺序（考虑外键依赖）
      const migrationOrder = [
        'users',
        'roles',
        'permissions',
        'role_permissions',
        'user_roles',
        'option_categories',
        'option_items',
        'customers',
        'customer_follow_records',
        'marketing_campaigns',
        'campaign_participants',
        'campaign_shares',
        'meetings',
        'meeting_participants',
        'pool_rules',
        'customer_behaviors',
        'wechat_customer_tracking',
        'sales_funnel_stats',
        'customer_value_analysis',
        'follow_ups',
        'public_pool'
      ];

      // 按顺序迁移每个表
      for (const tableName of migrationOrder) {
        await this.migrateTable(tableName);
      }

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      console.log('🎉 数据迁移完成!');
      console.log(`⏱️ 总耗时: ${duration}ms`);
      
      // 生成迁移报告
      await this.generateMigrationReport();
      
    } catch (error) {
      console.error('❌ 数据迁移失败:', error);
      throw error;
    }
  }

  /**
   * 迁移单个表
   */
  private async migrateTable(tableName: string): Promise<void> {
    console.log(`📊 开始迁移表: ${tableName}`);
    
    const tableStats: TableMigrationStats = {
      tableName,
      totalRecords: 0,
      migratedRecords: 0,
      failedRecords: 0,
      startTime: new Date(),
      errors: []
    };
    
    this.stats.push(tableStats);
    
    const log = this.createLog(tableName, 'migrate', 'started');
    
    try {
      // 获取Supabase表数据总数
      const { count: totalCount } = await this.supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      tableStats.totalRecords = totalCount || 0;
      
      if (totalCount === 0) {
        console.log(`⚠️ 表 ${tableName} 无数据，跳过迁移`);
        this.completeLog(log, 0);
        return;
      }
      
      // 检查是否为增量迁移
      let whereClause = '';
      if (this.config.incrementalMode && this.config.lastMigrationTime) {
        whereClause = `updated_at.gt.${this.config.lastMigrationTime}`;
      }
      
      // 分批迁移数据
      let offset = 0;
      let hasMore = true;
      
      while (hasMore) {
        const { data, error } = await this.supabase
          .from(tableName)
          .select('*')
          .range(offset, offset + this.config.batchSize - 1)
          .order('created_at', { ascending: true });
        
        if (error) {
          throw new Error(`获取 ${tableName} 数据失败: ${error.message}`);
        }
        
        if (!data || data.length === 0) {
          hasMore = false;
          break;
        }
        
        // 转换数据格式并插入MySQL
        const convertedData = this.convertDataForMySQL(data, tableName);
        
        try {
          const result = await this.mysql.batchInsert(tableName, convertedData);
          
          if (result.success) {
            tableStats.migratedRecords += convertedData.length;
            console.log(`✅ ${tableName}: 已迁移 ${tableStats.migratedRecords}/${tableStats.totalRecords} 条记录`);
          } else {
            tableStats.failedRecords += convertedData.length;
            tableStats.errors.push(result.error || '未知错误');
            console.error(`❌ ${tableName}: 批量插入失败 - ${result.error}`);
          }
        } catch (batchError: any) {
          // 如果批量插入失败，尝试逐条插入
          console.warn(`⚠️ ${tableName}: 批量插入失败，尝试逐条插入...`);
          
          for (const record of convertedData) {
            try {
              const singleResult = await this.mysql.insert(tableName, record);
              if (singleResult.success) {
                tableStats.migratedRecords++;
              } else {
                tableStats.failedRecords++;
                tableStats.errors.push(`记录 ${record.id}: ${singleResult.error}`);
              }
            } catch (singleError: any) {
              tableStats.failedRecords++;
              tableStats.errors.push(`记录 ${record.id}: ${singleError.message}`);
            }
          }
        }
        
        offset += this.config.batchSize;
        
        // 避免过快请求
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      tableStats.endTime = new Date();
      tableStats.duration = tableStats.endTime.getTime() - tableStats.startTime.getTime();
      
      this.completeLog(log, tableStats.migratedRecords);
      
      console.log(`✅ 表 ${tableName} 迁移完成: ${tableStats.migratedRecords}/${tableStats.totalRecords} 条记录`);
      
      // 数据验证
      if (this.config.validateData) {
        await this.validateTableData(tableName, tableStats);
      }
      
    } catch (error: any) {
      tableStats.endTime = new Date();
      tableStats.errors.push(error.message);
      
      this.failLog(log, error.message);
      
      console.error(`❌ 表 ${tableName} 迁移失败:`, error);
      throw error;
    }
  }

  /**
   * 转换数据格式以适配MySQL
   */
  private convertDataForMySQL(data: any[], tableName: string): any[] {
    return data.map(record => {
      const converted = { ...record };
      
      // 处理外键约束问题 - 将所有用户相关字段设置为null以避免外键约束失败
      if (tableName === 'pool_rules') {
        // 暂时将created_by设为null以避免外键约束问题
        converted.created_by = null;
      }
      
      if (tableName === 'marketing_campaigns') {
        // 暂时将created_by设为null以避免外键约束问题
        converted.created_by = null;
      }
      
      if (tableName === 'follow_ups') {
        // 暂时将created_by和assigned_to设为null以避免外键约束问题
        converted.created_by = null;
        converted.assigned_to = null;
      }
      
      if (tableName === 'public_pool') {
        // 暂时将所有用户相关字段设为null以避免外键约束问题
        converted.moved_by = null;
        converted.last_assigned_to = null;
        converted.claimed_by = null;
      }
      
      // 处理特定表的字段映射
      if (tableName === 'users') {
        // 将username映射到name字段（MySQL表需要name字段）
        if (converted.username && !converted.name) {
          converted.name = converted.username;
        }
        // 如果没有username但有email，使用email的用户名部分作为name
        if (!converted.name && converted.email) {
          converted.name = converted.email.split('@')[0];
        }
        // 确保name字段不为空
        if (!converted.name) {
          converted.name = 'Unknown User';
        }
      }
      
      // 处理customers表的特殊字段
      if (tableName === 'customers') {
        // 处理tags字段，确保是有效的JSON格式
        if (converted.tags) {
          if (typeof converted.tags === 'string') {
            try {
              // 尝试解析JSON字符串
              JSON.parse(converted.tags);
            } catch (e) {
              // 如果不是有效JSON，将其转换为JSON数组
              converted.tags = JSON.stringify([converted.tags]);
            }
          } else if (Array.isArray(converted.tags)) {
            converted.tags = JSON.stringify(converted.tags);
          } else if (typeof converted.tags === 'object') {
            converted.tags = JSON.stringify(converted.tags);
          } else {
            // 其他类型转换为字符串数组
            converted.tags = JSON.stringify([String(converted.tags)]);
          }
        } else {
          // 如果tags为空，设置为空数组
          converted.tags = JSON.stringify([]);
        }
      }
      
      // 处理时间字段
      ['created_at', 'updated_at', 'last_login_at', 'start_time', 'end_time', 
       'due_date', 'completed_at', 'next_follow_time', 'participation_date',
       'share_time', 'entered_at', 'last_interaction', 'last_calculated'].forEach(field => {
        if (converted[field]) {
          converted[field] = new Date(converted[field]);
        }
      });
      
      // 处理JSON字段
      ['conditions', 'actions', 'behavior_data', 'tags'].forEach(field => {
        if (converted[field] && typeof converted[field] === 'object') {
          converted[field] = JSON.stringify(converted[field]);
        }
      });
      
      // 处理布尔字段
      ['is_active', 'is_admin'].forEach(field => {
        if (typeof converted[field] === 'boolean') {
          converted[field] = converted[field] ? 1 : 0;
        }
      });
      
      return converted;
    });
  }

  /**
   * 验证表数据
   */
  private async validateTableData(tableName: string, stats: TableMigrationStats): Promise<void> {
    console.log(`🔍 验证表 ${tableName} 数据...`);
    
    try {
      // 检查记录数
      const mysqlCount = await this.mysql.getTableCount(tableName);
      
      if (mysqlCount !== stats.migratedRecords) {
        const error = `数据验证失败: MySQL记录数(${mysqlCount}) != 迁移记录数(${stats.migratedRecords})`;
        stats.errors.push(error);
        console.warn(`⚠️ ${error}`);
      } else {
        console.log(`✅ 表 ${tableName} 数据验证通过`);
      }
    } catch (error: any) {
      stats.errors.push(`数据验证失败: ${error.message}`);
      console.error(`❌ 表 ${tableName} 数据验证失败:`, error);
    }
  }

  /**
   * 创建迁移日志
   */
  private createLog(tableName: string, operation: string, status: 'started' | 'completed' | 'failed'): MigrationLog {
    const log: MigrationLog = {
      id: uuidv4(),
      migration_id: this.migrationId,
      table_name: tableName,
      operation,
      status,
      records_count: 0,
      start_time: new Date()
    };
    
    this.logs.push(log);
    
    if (this.config.enableLogging) {
      // 异步保存日志到MySQL
      this.saveLogToMySQL(log).catch(console.error);
    }
    
    return log;
  }

  /**
   * 完成迁移日志
   */
  private completeLog(log: MigrationLog, recordsCount: number): void {
    log.status = 'completed';
    log.records_count = recordsCount;
    log.end_time = new Date();
    log.duration_ms = log.end_time.getTime() - log.start_time.getTime();
    
    if (this.config.enableLogging) {
      this.saveLogToMySQL(log).catch(console.error);
    }
  }

  /**
   * 失败迁移日志
   */
  private failLog(log: MigrationLog, errorMessage: string): void {
    log.status = 'failed';
    log.error_message = errorMessage;
    log.end_time = new Date();
    log.duration_ms = log.end_time.getTime() - log.start_time.getTime();
    
    if (this.config.enableLogging) {
      this.saveLogToMySQL(log).catch(console.error);
    }
  }

  /**
   * 保存日志到MySQL
   */
  private async saveLogToMySQL(log: MigrationLog): Promise<void> {
    try {
      // 检查日志是否已存在
      const existingLog = await this.mysql.query(
        'SELECT id FROM migration_logs WHERE id = ?',
        [log.id]
      );
      
      if (existingLog.length > 0) {
        // 更新现有日志
        await this.mysql.query(
          `UPDATE migration_logs SET 
           status = ?, records_count = ?, error_message = ?, 
           end_time = ?, duration_ms = ?
           WHERE id = ?`,
          [
            log.status,
            log.records_count,
            log.error_message || null,
            log.end_time || null,
            log.duration_ms || null,
            log.id
          ]
        );
      } else {
        // 插入新日志
        await this.mysql.insert('migration_logs', log);
      }
    } catch (error) {
      console.error('保存迁移日志失败:', error);
    }
  }

  /**
   * 生成迁移报告
   */
  private async generateMigrationReport(): Promise<void> {
    console.log('📋 生成迁移报告...');
    
    const report = {
      migrationId: this.migrationId,
      timestamp: new Date().toISOString(),
      config: this.config,
      summary: {
        totalTables: this.stats.length,
        successfulTables: this.stats.filter(s => s.errors.length === 0).length,
        failedTables: this.stats.filter(s => s.errors.length > 0).length,
        totalRecords: this.stats.reduce((sum, s) => sum + s.totalRecords, 0),
        migratedRecords: this.stats.reduce((sum, s) => sum + s.migratedRecords, 0),
        failedRecords: this.stats.reduce((sum, s) => sum + s.failedRecords, 0)
      },
      tableStats: this.stats,
      logs: this.logs
    };
    
    // 保存报告到文件
    const reportPath = path.join(__dirname, '..', 'docs', 'data-migration', `migration-report-${this.migrationId}.json`);
    
    try {
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 迁移报告已保存: ${reportPath}`);
    } catch (error) {
      console.error('保存迁移报告失败:', error);
    }
    
    // 打印摘要
    console.log('\n📊 迁移摘要:');
    console.log(`   总表数: ${report.summary.totalTables}`);
    console.log(`   成功表数: ${report.summary.successfulTables}`);
    console.log(`   失败表数: ${report.summary.failedTables}`);
    console.log(`   总记录数: ${report.summary.totalRecords}`);
    console.log(`   迁移记录数: ${report.summary.migratedRecords}`);
    console.log(`   失败记录数: ${report.summary.failedRecords}`);
    
    if (report.summary.failedTables > 0) {
      console.log('\n❌ 失败的表:');
      this.stats.filter(s => s.errors.length > 0).forEach(s => {
        console.log(`   ${s.tableName}: ${s.errors.join(', ')}`);
      });
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('🧹 清理迁移资源...');
    
    try {
      await this.mysql.close();
      console.log('✅ 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error);
    }
  }
}

/**
 * 主迁移函数
 */
async function main() {
  const config: MigrationConfig = {
    batchSize: parseInt(process.env.MIGRATION_BATCH_SIZE || '100'),
    enableLogging: process.env.MIGRATION_ENABLE_LOGGING !== 'false',
    validateData: process.env.MIGRATION_VALIDATE_DATA !== 'false',
    incrementalMode: process.env.MIGRATION_INCREMENTAL_MODE === 'true',
    lastMigrationTime: process.env.MIGRATION_LAST_TIME
  };
  
  console.log('🚀 启动数据迁移程序');
  console.log('配置:', config);
  
  const migrationManager = new DataMigrationManager(config);
  
  try {
    await migrationManager.initialize();
    await migrationManager.migrate();
    console.log('🎉 数据迁移成功完成!');
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
    process.exit(1);
  } finally {
    await migrationManager.cleanup();
  }
}

// 如果直接运行此脚本
if (import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
  main().catch(console.error);
}

export { DataMigrationManager, MigrationConfig };