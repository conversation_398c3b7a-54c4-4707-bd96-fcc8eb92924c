import request from '@/utils/request'
import type {
  OptionCategory,
  OptionItem,
  CreateOptionCategoryRequest,
  UpdateOptionCategoryRequest,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  GetOptionCategoriesParams,
  GetOptionItemsParams
} from '@/types/options'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  return response.data
}

// 选项分类 API
export const optionCategoriesApi = {
  // 获取选项分类列表
  async getList(params?: GetOptionCategoriesParams) {
    const response = await request.get('/api/options-management/categories', { params })
    return unwrapResponse<{
      data: OptionCategory[]
      total: number
      page: number
      page_size: number
      total_pages: number
    }>(response)
  },

  // 获取单个分类
  async getById(id: number) {
    const response = await request.get(`/api/options-management/categories/${id}`)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 创建分类
  async create(data: CreateOptionCategoryRequest) {
    const response = await request.post('/api/options-management/categories', data)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 更新分类
  async update(id: number, data: UpdateOptionCategoryRequest) {
    const response = await request.put(`/api/options-management/categories/${id}`, data)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 删除选项分类
  async delete(id: number) {
    const response = await request.delete(`/api/options-management/categories/${id}`)
    return unwrapResponse(response)
  },

  // 获取所有启用的分类（用于下拉选择，使用公共API）
  async getActive() {
    const response = await request.get('/api/options/categories?is_active=true')
    return unwrapResponse<OptionCategory[]>(response)
  }
}

// 选项数据 API
export const optionItemsApi = {
  // 只读：按分类ID获取（保留公共API）
  async getByCategory(categoryId: number) {
    const response = await request.get(`/api/options/items/${categoryId}`)
    return unwrapResponse<OptionItem[]>(response)
  },

  // 只读：按分类代码获取选项（用于前端组件）
  async getByCategoryCode(categoryCode: string) {
    const response = await request.get(`/api/options/by-category/${categoryCode}`)
    return unwrapResponse<OptionItem[]>(response)
  },

  // 管理端：获取列表（修正路径，带分页）
  async getList(params?: GetOptionItemsParams) {
    const response = await request.get(`/api/options-management/items`, { params })
    return unwrapResponse<{
      data: OptionItem[]
      total: number
      page: number
      page_size: number
      total_pages: number
    }>(response)
  },

  // 管理端：根据ID获取
  async getById(id: number) {
    const response = await request.get(`/api/options-management/items/${id}`)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：创建
  async create(data: CreateOptionItemRequest) {
    const response = await request.post(`/api/options-management/items`, data)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：更新
  async update(id: number, data: UpdateOptionItemRequest) {
    const response = await request.put(`/api/options-management/items/${id}`, data)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：删除
  async delete(id: number) {
    const response = await request.delete(`/api/options-management/items/${id}`)
    return unwrapResponse(response)
  },

  // 管理端：批量删除
  async batchDelete(ids: number[]) {
    const response = await request.delete(`/api/options-management/items/batch`, { data: { ids } })
    return unwrapResponse(response)
  },

  // 管理端：批量更新排序
  async batchUpdateSort(items: { id: number; sort_order: number }[]) {
    const response = await request.put(`/api/options-management/items/batch-sort`, { items })
    return unwrapResponse(response)
  },

  // 管理端：批量更新状态
  async batchUpdateStatus(ids: number[], is_active: boolean) {
    const response = await request.put(`/api/options-management/items/batch-status`, { ids, is_active })
    return unwrapResponse(response)
  }
}

// 选项数据获取工具函数（用于前端组件）
export const optionsUtils = {
  // 获取客户来源选项
  async getCustomerSources(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('customer_source')
      return response
    } catch (error) {
      console.error('获取客户来源选项失败:', error)
      return []
    }
  },

  // 获取客户等级选项
  async getCustomerLevels(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('customer_level')
      return response
    } catch (error) {
      console.error('获取客户等级选项失败:', error)
      return []
    }
  },

  // 获取性别选项
  async getGenders(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('gender')
      return response
    } catch (error) {
      console.error('获取性别选项失败:', error)
      return []
    }
  },

  // 获取装修类型选项
  async getDecorationTypes(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('decoration_type')
      return response
    } catch (error) {
      console.error('获取装修类型选项失败:', error)
      return []
    }
  },

  // 获取房屋状态选项
  async getHouseStatuses(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('house_status')
      return response
    } catch (error) {
      console.error('获取房屋状态选项失败:', error)
      return []
    }
  },

  // 通用获取选项方法
  async getOptionsByCategory(categoryCode: string): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode(categoryCode)
      return response
    } catch (error) {
      console.error(`获取分类 ${categoryCode} 选项失败:`, error)
      return []
    }
  },

  // 将选项数据转换为 Naive UI 的 SelectOption 格式
  toSelectOptions(items: OptionItem[]): Array<{ label: string; value: string; disabled?: boolean }> {
    return items
      .filter(item => item.is_active)
      .sort((a, b) => a.sort_order - b.sort_order)
      .map(item => ({
        label: item.label,
        value: item.value,
        disabled: !item.is_active
      }))
  },

  // 根据值获取标签
  getLabelByValue(items: OptionItem[], value: string): string {
    const item = items.find(item => item.value === value)
    return item ? item.label : value
  }
}