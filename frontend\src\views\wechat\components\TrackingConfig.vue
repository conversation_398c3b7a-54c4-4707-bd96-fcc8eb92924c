<template>
  <div class="tracking-config">
    <!-- 配置标题 -->
    <div class="config-header">
      <h3>客户跟踪配置</h3>
      <div class="header-actions">
        <n-button @click="resetConfig">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          重置配置
        </n-button>
        <n-button type="primary" @click="saveConfig" :loading="saving">
          <template #icon>
            <n-icon><SaveOutline /></n-icon>
          </template>
          保存配置
        </n-button>
      </div>
    </div>

    <!-- 基础配置 -->
    <div class="config-section">
      <div class="section-header">
        <h4>基础配置</h4>
        <n-switch v-model:value="config.enabled" size="large">
          <template #checked>启用跟踪</template>
          <template #unchecked>禁用跟踪</template>
        </n-switch>
      </div>
      <div class="config-grid">
        <div class="config-item">
          <label>跟踪模式</label>
          <n-select
            v-model:value="config.trackingMode"
            :options="trackingModeOptions"
            placeholder="选择跟踪模式"
          />
          <div class="config-desc">选择客户行为跟踪的详细程度</div>
        </div>
        <div class="config-item">
          <label>数据保留期</label>
          <n-input-number
            v-model:value="config.dataRetentionDays"
            :min="1"
            :max="365"
            placeholder="天数"
          >
            <template #suffix>天</template>
          </n-input-number>
          <div class="config-desc">跟踪数据的保留时间</div>
        </div>
        <div class="config-item">
          <label>采样率</label>
          <n-slider
            v-model:value="config.samplingRate"
            :min="1"
            :max="100"
            :step="1"
            :format-tooltip="(value) => value + '%'"
          />
          <div class="config-desc">数据采集的比例，100%为全量采集</div>
        </div>
      </div>
    </div>

    <!-- 页面跟踪配置 -->
    <div class="config-section">
      <div class="section-header">
        <h4>页面跟踪配置</h4>
        <n-switch v-model:value="config.pageTracking.enabled">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </div>
      <div class="tracking-options">
        <div class="option-group">
          <div class="group-title">跟踪事件</div>
          <div class="checkbox-grid">
            <n-checkbox v-model:checked="config.pageTracking.events.pageView">
              页面浏览
            </n-checkbox>
            <n-checkbox v-model:checked="config.pageTracking.events.pageStay">
              停留时间
            </n-checkbox>
            <n-checkbox v-model:checked="config.pageTracking.events.scroll">
              滚动行为
            </n-checkbox>
            <n-checkbox v-model:checked="config.pageTracking.events.click">
              点击事件
            </n-checkbox>
            <n-checkbox v-model:checked="config.pageTracking.events.form">
              表单交互
            </n-checkbox>
            <n-checkbox v-model:checked="config.pageTracking.events.download">
              文件下载
            </n-checkbox>
          </div>
        </div>
        <div class="option-group">
          <div class="group-title">跟踪页面</div>
          <div class="page-list">
            <div class="page-item" v-for="page in config.pageTracking.pages" :key="page.id">
              <div class="page-info">
                <n-checkbox v-model:checked="page.enabled">
                  {{ page.name }}
                </n-checkbox>
                <div class="page-url">{{ page.url }}</div>
              </div>
              <div class="page-actions">
                <n-button size="small" @click="editPage(page)">
                  <template #icon>
                    <n-icon><CreateOutline /></n-icon>
                  </template>
                </n-button>
                <n-button size="small" type="error" @click="removePage(page.id)">
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>
            <div class="add-page">
              <n-button dashed block @click="addPage">
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                添加页面
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户行为跟踪 -->
    <div class="config-section">
      <div class="section-header">
        <h4>用户行为跟踪</h4>
        <n-switch v-model:value="config.behaviorTracking.enabled">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </div>
      <div class="behavior-config">
        <div class="config-row">
          <div class="config-item">
            <label>会话超时</label>
            <n-input-number
              v-model:value="config.behaviorTracking.sessionTimeout"
              :min="5"
              :max="120"
              placeholder="分钟"
            >
              <template #suffix>分钟</template>
            </n-input-number>
            <div class="config-desc">用户无操作多久后结束会话</div>
          </div>
          <div class="config-item">
            <label>热力图采样</label>
            <n-input-number
              v-model:value="config.behaviorTracking.heatmapSampling"
              :min="1"
              :max="100"
              placeholder="百分比"
            >
              <template #suffix>%</template>
            </n-input-number>
            <div class="config-desc">热力图数据采集比例</div>
          </div>
        </div>
        <div class="behavior-events">
          <div class="events-title">跟踪行为</div>
          <div class="events-grid">
            <n-checkbox v-model:checked="config.behaviorTracking.events.mouse">
              鼠标轨迹
            </n-checkbox>
            <n-checkbox v-model:checked="config.behaviorTracking.events.keyboard">
              键盘输入
            </n-checkbox>
            <n-checkbox v-model:checked="config.behaviorTracking.events.touch">
              触摸操作
            </n-checkbox>
            <n-checkbox v-model:checked="config.behaviorTracking.events.resize">
              窗口变化
            </n-checkbox>
            <n-checkbox v-model:checked="config.behaviorTracking.events.error">
              错误事件
            </n-checkbox>
            <n-checkbox v-model:checked="config.behaviorTracking.events.performance">
              性能指标
            </n-checkbox>
          </div>
        </div>
      </div>
    </div>

    <!-- 转化跟踪配置 -->
    <div class="config-section">
      <div class="section-header">
        <h4>转化跟踪配置</h4>
        <n-switch v-model:value="config.conversionTracking.enabled">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </div>
      <div class="conversion-config">
        <div class="conversion-goals">
          <div class="goals-header">
            <div class="goals-title">转化目标</div>
            <n-button size="small" @click="addGoal">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              添加目标
            </n-button>
          </div>
          <div class="goals-list">
            <div class="goal-item" v-for="goal in config.conversionTracking.goals" :key="goal.id">
              <div class="goal-info">
                <div class="goal-header">
                  <n-checkbox v-model:checked="goal.enabled">
                    {{ goal.name }}
                  </n-checkbox>
                  <n-tag :type="getGoalTypeColor(goal.type)" size="small">
                    {{ getGoalTypeText(goal.type) }}
                  </n-tag>
                </div>
                <div class="goal-details">
                  <div class="goal-desc">{{ goal.description }}</div>
                  <div class="goal-value">目标值: {{ goal.value }}</div>
                </div>
              </div>
              <div class="goal-actions">
                <n-button size="small" @click="editGoal(goal)">
                  <template #icon>
                    <n-icon><CreateOutline /></n-icon>
                  </template>
                </n-button>
                <n-button size="small" type="error" @click="removeGoal(goal.id)">
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>
          </div>
        </div>
        <div class="attribution-config">
          <div class="attribution-title">归因模型</div>
          <n-radio-group v-model:value="config.conversionTracking.attributionModel">
            <div class="attribution-options">
              <n-radio value="first_touch">
                <div class="radio-content">
                  <div class="radio-title">首次接触</div>
                  <div class="radio-desc">将转化归因于首次接触点</div>
                </div>
              </n-radio>
              <n-radio value="last_touch">
                <div class="radio-content">
                  <div class="radio-title">最后接触</div>
                  <div class="radio-desc">将转化归因于最后接触点</div>
                </div>
              </n-radio>
              <n-radio value="linear">
                <div class="radio-content">
                  <div class="radio-title">线性归因</div>
                  <div class="radio-desc">平均分配给所有接触点</div>
                </div>
              </n-radio>
              <n-radio value="time_decay">
                <div class="radio-content">
                  <div class="radio-title">时间衰减</div>
                  <div class="radio-desc">越接近转化时间权重越高</div>
                </div>
              </n-radio>
            </div>
          </n-radio-group>
        </div>
      </div>
    </div>

    <!-- 隐私设置 -->
    <div class="config-section">
      <div class="section-header">
        <h4>隐私设置</h4>
      </div>
      <div class="privacy-config">
        <div class="privacy-options">
          <n-checkbox v-model:checked="config.privacy.anonymizeIp">
            IP地址匿名化
          </n-checkbox>
          <n-checkbox v-model:checked="config.privacy.respectDnt">
            尊重Do Not Track设置
          </n-checkbox>
          <n-checkbox v-model:checked="config.privacy.cookieConsent">
            需要Cookie同意
          </n-checkbox>
          <n-checkbox v-model:checked="config.privacy.dataMinimization">
            数据最小化收集
          </n-checkbox>
        </div>
        <div class="privacy-notice">
          <div class="notice-title">隐私声明</div>
          <n-input
            v-model:value="config.privacy.privacyNotice"
            type="textarea"
            :rows="3"
            placeholder="输入隐私声明内容"
          />
        </div>
      </div>
    </div>

    <!-- 页面编辑模态框 -->
    <n-modal v-model:show="pageModalVisible" preset="card" title="页面配置" style="width: 500px">
      <n-form :model="editingPage" label-placement="top">
        <n-form-item label="页面名称">
          <n-input v-model:value="editingPage.name" placeholder="输入页面名称" />
        </n-form-item>
        <n-form-item label="页面URL">
          <n-input v-model:value="editingPage.url" placeholder="输入页面URL" />
        </n-form-item>
        <n-form-item label="页面描述">
          <n-input
            v-model:value="editingPage.description"
            type="textarea"
            :rows="2"
            placeholder="输入页面描述"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="modal-actions">
          <n-button @click="pageModalVisible = false">取消</n-button>
          <n-button type="primary" @click="savePage">保存</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 目标编辑模态框 -->
    <n-modal v-model:show="goalModalVisible" preset="card" title="转化目标配置" style="width: 500px">
      <n-form :model="editingGoal" label-placement="top">
        <n-form-item label="目标名称">
          <n-input v-model:value="editingGoal.name" placeholder="输入目标名称" />
        </n-form-item>
        <n-form-item label="目标类型">
          <n-select
            v-model:value="editingGoal.type"
            :options="goalTypeOptions"
            placeholder="选择目标类型"
          />
        </n-form-item>
        <n-form-item label="目标值">
          <n-input v-model:value="editingGoal.value" placeholder="输入目标值" />
        </n-form-item>
        <n-form-item label="目标描述">
          <n-input
            v-model:value="editingGoal.description"
            type="textarea"
            :rows="2"
            placeholder="输入目标描述"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="modal-actions">
          <n-button @click="goalModalVisible = false">取消</n-button>
          <n-button type="primary" @click="saveGoal">保存</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  NButton, NIcon, NSwitch, NSelect, NInputNumber, NSlider, NCheckbox,
  NTag, NRadioGroup, NRadio, NInput, NModal, NForm, NFormItem, useMessage
} from 'naive-ui'
import {
  RefreshOutline, SaveOutline, CreateOutline, TrashOutline, AddOutline
} from '@vicons/ionicons5'

interface TrackingConfig {
  enabled: boolean
  trackingMode: string
  dataRetentionDays: number
  samplingRate: number
  pageTracking: {
    enabled: boolean
    events: {
      pageView: boolean
      pageStay: boolean
      scroll: boolean
      click: boolean
      form: boolean
      download: boolean
    }
    pages: Array<{
      id: string
      name: string
      url: string
      description: string
      enabled: boolean
    }>
  }
  behaviorTracking: {
    enabled: boolean
    sessionTimeout: number
    heatmapSampling: number
    events: {
      mouse: boolean
      keyboard: boolean
      touch: boolean
      resize: boolean
      error: boolean
      performance: boolean
    }
  }
  conversionTracking: {
    enabled: boolean
    attributionModel: string
    goals: Array<{
      id: string
      name: string
      type: string
      value: string
      description: string
      enabled: boolean
    }>
  }
  privacy: {
    anonymizeIp: boolean
    respectDnt: boolean
    cookieConsent: boolean
    dataMinimization: boolean
    privacyNotice: string
  }
}

const emit = defineEmits<{
  close: []
}>()

const message = useMessage()
const saving = ref(false)
const pageModalVisible = ref(false)
const goalModalVisible = ref(false)

// 配置数据
const config = reactive<TrackingConfig>({
  enabled: true,
  trackingMode: 'standard',
  dataRetentionDays: 90,
  samplingRate: 100,
  pageTracking: {
    enabled: true,
    events: {
      pageView: true,
      pageStay: true,
      scroll: true,
      click: true,
      form: true,
      download: false
    },
    pages: [
      {
        id: '1',
        name: '首页',
        url: '/',
        description: '网站首页',
        enabled: true
      },
      {
        id: '2',
        name: '产品页',
        url: '/products',
        description: '产品展示页面',
        enabled: true
      },
      {
        id: '3',
        name: '联系页',
        url: '/contact',
        description: '联系我们页面',
        enabled: true
      }
    ]
  },
  behaviorTracking: {
    enabled: true,
    sessionTimeout: 30,
    heatmapSampling: 10,
    events: {
      mouse: true,
      keyboard: false,
      touch: true,
      resize: true,
      error: true,
      performance: true
    }
  },
  conversionTracking: {
    enabled: true,
    attributionModel: 'last_touch',
    goals: [
      {
        id: '1',
        name: '注册转化',
        type: 'registration',
        value: '完成注册',
        description: '用户完成注册流程',
        enabled: true
      },
      {
        id: '2',
        name: '购买转化',
        type: 'purchase',
        value: '完成购买',
        description: '用户完成购买流程',
        enabled: true
      }
    ]
  },
  privacy: {
    anonymizeIp: true,
    respectDnt: true,
    cookieConsent: true,
    dataMinimization: true,
    privacyNotice: '我们会收集您的浏览行为数据以改善服务质量，您的隐私将得到充分保护。'
  }
})

// 编辑数据
const editingPage = ref({
  id: '',
  name: '',
  url: '',
  description: '',
  enabled: true
})

const editingGoal = ref({
  id: '',
  name: '',
  type: '',
  value: '',
  description: '',
  enabled: true
})

// 选项数据
const trackingModeOptions = [
  { label: '基础模式', value: 'basic' },
  { label: '标准模式', value: 'standard' },
  { label: '高级模式', value: 'advanced' },
  { label: '自定义模式', value: 'custom' }
]

const goalTypeOptions = [
  { label: '页面访问', value: 'page_view' },
  { label: '用户注册', value: 'registration' },
  { label: '产品购买', value: 'purchase' },
  { label: '表单提交', value: 'form_submit' },
  { label: '文件下载', value: 'download' },
  { label: '视频播放', value: 'video_play' }
]

// 方法
const getGoalTypeColor = (type: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    page_view: 'info',
    registration: 'success',
    purchase: 'warning',
    form_submit: 'info',
    download: 'default',
    video_play: 'error'
  }
  return colorMap[type] || 'default'
}

const getGoalTypeText = (type: string) => {
  const textMap = {
    page_view: '页面访问',
    registration: '用户注册',
    purchase: '产品购买',
    form_submit: '表单提交',
    download: '文件下载',
    video_play: '视频播放'
  }
  return textMap[type as keyof typeof textMap] || type
}

const resetConfig = () => {
  // 重置配置到默认值
  message.info('配置已重置')
}

const saveConfig = async () => {
  saving.value = true
  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('配置保存成功')
  } catch (error) {
    message.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const addPage = () => {
  editingPage.value = {
    id: '',
    name: '',
    url: '',
    description: '',
    enabled: true
  }
  pageModalVisible.value = true
}

const editPage = (page: any) => {
  editingPage.value = { ...page }
  pageModalVisible.value = true
}

const savePage = () => {
  if (editingPage.value.id) {
    // 编辑现有页面
    const index = config.pageTracking.pages.findIndex(p => p.id === editingPage.value.id)
    if (index !== -1) {
      config.pageTracking.pages[index] = { ...editingPage.value }
    }
  } else {
    // 添加新页面
    config.pageTracking.pages.push({
      ...editingPage.value,
      id: Date.now().toString()
    })
  }
  pageModalVisible.value = false
  message.success('页面配置保存成功')
}

const removePage = (id: string) => {
  const index = config.pageTracking.pages.findIndex(p => p.id === id)
  if (index !== -1) {
    config.pageTracking.pages.splice(index, 1)
    message.success('页面已删除')
  }
}

const addGoal = () => {
  editingGoal.value = {
    id: '',
    name: '',
    type: '',
    value: '',
    description: '',
    enabled: true
  }
  goalModalVisible.value = true
}

const editGoal = (goal: any) => {
  editingGoal.value = { ...goal }
  goalModalVisible.value = true
}

const saveGoal = () => {
  if (editingGoal.value.id) {
    // 编辑现有目标
    const index = config.conversionTracking.goals.findIndex(g => g.id === editingGoal.value.id)
    if (index !== -1) {
      config.conversionTracking.goals[index] = { ...editingGoal.value }
    }
  } else {
    // 添加新目标
    config.conversionTracking.goals.push({
      ...editingGoal.value,
      id: Date.now().toString()
    })
  }
  goalModalVisible.value = false
  message.success('转化目标保存成功')
}

const removeGoal = (id: string) => {
  const index = config.conversionTracking.goals.findIndex(g => g.id === id)
  if (index !== -1) {
    config.conversionTracking.goals.splice(index, 1)
    message.success('转化目标已删除')
  }
}
</script>

<style scoped>
.tracking-config {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.config-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.config-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.tracking-options {
  padding: 20px;
}

.option-group {
  margin-bottom: 24px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.page-info {
  flex: 1;
}

.page-url {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  margin-left: 24px;
}

.page-actions {
  display: flex;
  gap: 8px;
}

.add-page {
  margin-top: 12px;
}

.behavior-config {
  padding: 20px;
}

.behavior-events {
  margin-top: 20px;
}

.events-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.conversion-config {
  padding: 20px;
}

.conversion-goals {
  margin-bottom: 24px;
}

.goals-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.goals-title {
  font-weight: 500;
  color: #1a1a1a;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.goal-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.goal-info {
  flex: 1;
}

.goal-header {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.goal-details {
  margin-left: 24px;
}

.goal-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.goal-value {
  font-size: 12px;
  color: #2080f0;
  font-weight: 500;
}

.goal-actions {
  display: flex;
  gap: 8px;
}

.attribution-config {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.attribution-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.attribution-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.radio-content {
  margin-left: 8px;
}

.radio-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.radio-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

.privacy-config {
  padding: 20px;
}

.privacy-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.privacy-notice {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.notice-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>