<template>
  <div class="option-categories-management">
    <!-- 操作栏 -->
    <div class="action-bar">
      <n-space>
        <n-button v-if="canCreate" type="primary" @click="showAddModal = true">
          <template #icon>
            <n-icon><add-outline /></n-icon>
          </template>
          添加分类
        </n-button>
        <n-button @click="refreshData">
          <template #icon>
            <n-icon><refresh-outline /></n-icon>
          </template>
          刷新
        </n-button>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="categories"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: OptionCategory) => row.id"
      size="medium"
    />

    <!-- 添加/编辑分类弹窗 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="添加分类">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="分类代码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入分类代码（英文）"
            :disabled="isEdit"
          />
        </n-form-item>
        <n-form-item label="分类名称" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入分类名称"
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number
            v-model:value="formData.sort_order"
            placeholder="排序值"
            :min="0"
          />
        </n-form-item>
        <n-form-item label="状态" path="is_active">
          <n-switch v-model:value="formData.is_active" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '添加' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 编辑分类弹窗 -->
    <n-modal v-model:show="showEditModal" preset="dialog" title="编辑分类">
      <n-form
        ref="editFormRef"
        :model="editFormData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="分类代码" path="code">
          <n-input
            v-model:value="editFormData.code"
            placeholder="请输入分类代码（英文）"
            disabled
          />
        </n-form-item>
        <n-form-item label="分类名称" path="name">
          <n-input
            v-model:value="editFormData.name"
            placeholder="请输入分类名称"
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="editFormData.description"
            type="textarea"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number
            v-model:value="editFormData.sort_order"
            placeholder="排序值"
            :min="0"
          />
        </n-form-item>
        <n-form-item label="状态" path="is_active">
          <n-switch v-model:value="editFormData.is_active" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdate" :loading="submitting">
            更新
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, computed } from 'vue'
import {
  NDataTable, NButton, NIcon, NSpace, NModal, NForm, NFormItem,
  NInput, NInputNumber, NSwitch, NTag, useMessage, useDialog
} from 'naive-ui'
import { AddOutline, RefreshOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'
import { optionCategoriesApi } from '@/api/options'
import type { OptionCategory, CreateOptionCategoryRequest, UpdateOptionCategoryRequest } from '@/types/options'
import { useAuthStore } from '@/stores/modules/auth'

const message = useMessage()
const dialog = useDialog()
const authStore = useAuthStore()

// 权限检查
const canRead = computed(() => authStore.hasPermission('settings:options:read') || authStore.hasPermission('settings:options'))
const canCreate = computed(() => authStore.hasPermission('settings:options:create') || authStore.hasPermission('settings:options'))
const canUpdate = computed(() => authStore.hasPermission('settings:options:update') || authStore.hasPermission('settings:options'))
const canDelete = computed(() => authStore.hasPermission('settings:options:delete') || authStore.hasPermission('settings:options'))

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddModal = ref(false)
const showEditModal = ref(false)
const isEdit = ref(false)
const categories = ref<OptionCategory[]>([])
const formRef = ref()
const editFormRef = ref()

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
    loadCategories()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadCategories()
  }
})

// 表单数据
const formData = reactive<CreateOptionCategoryRequest>({
  code: '',
  name: '',
  description: '',
  sort_order: 0,
  is_active: true
})

const editFormData = reactive<UpdateOptionCategoryRequest & { id?: string; code?: string }>({
  name: '',
  description: '',
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '分类代码只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  sort_order: [
    {
      required: true,
      message: '请输入排序值',
      trigger: 'blur',
      validator: (rule: any, value: any) => {
        if (value === null || value === undefined || value === '') {
          return new Error('请输入排序值')
        }
        if (isNaN(Number(value))) {
          return new Error('排序值必须是数字')
        }
        return true
      }
    }
  ]
}

// 表格列配置
const columns: DataTableColumns<OptionCategory> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '分类代码',
    key: 'code',
    width: 150
  },
  {
    title: '分类名称',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80
  },
  {
    title: '状态',
    key: 'is_active',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_active ? 'success' : 'default'
      }, {
        default: () => row.is_active ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      const actions: any[] = []
      
      if (canUpdate.value) {
        actions.push(
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑',
            icon: () => h(NIcon, {}, { default: () => h(CreateOutline) })
          })
        )
      }
      
      if (canDelete.value) {
        actions.push(
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除',
            icon: () => h(NIcon, {}, { default: () => h(TrashOutline) })
          })
        )
      }
      
      return h(NSpace, {}, {
        default: () => actions
      })
    }
  }
]

// 加载分类数据
const loadCategories = async () => {
  if (!canRead.value) {
    message.error('您没有权限查看选项分类数据')
    return
  }
  
  try {
    loading.value = true
    const response = await optionCategoriesApi.getList({
      page: pagination.page,
      page_size: pagination.pageSize
    })
    categories.value = response.data
    pagination.itemCount = response.total
  } catch (error) {
    message.error('加载分类数据失败')
    console.error('Load categories error:', error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadCategories()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    description: '',
    sort_order: 0,
    is_active: true
  })
}

const resetEditForm = () => {
  Object.assign(editFormData, {
    name: '',
    description: '',
    sort_order: 0,
    is_active: true
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!canCreate.value) {
    message.error('您没有权限创建选项分类')
    return
  }
  
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    await optionCategoriesApi.create(formData)
    message.success('添加分类成功')
    showAddModal.value = false
    resetForm()
    loadCategories()
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message || '添加分类失败')
    }
    console.error('Create category error:', error)
  } finally {
    submitting.value = false
  }
}

// 编辑分类
const handleEdit = (row: OptionCategory) => {
  if (!canUpdate.value) {
    message.error('您没有权限编辑选项分类')
    return
  }
  
  Object.assign(editFormData, {
    name: row.name,
    description: row.description,
    sort_order: row.sort_order,
    is_active: row.is_active
  })
  editFormData.id = row.id  // 存储 id 用于 API 调用
  editFormData.code = row.code  // 存储 code 用于显示
  showEditModal.value = true
}

// 更新分类
const handleUpdate = async () => {
  if (!canUpdate.value) {
    message.error('您没有权限更新选项分类')
    return
  }
  
  try {
    await editFormRef.value?.validate()
    submitting.value = true
    
    await optionCategoriesApi.update(Number(editFormData.id!), editFormData)
    message.success('更新分类成功')
    showEditModal.value = false
    resetEditForm()
    loadCategories()
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message || '更新分类失败')
    }
    console.error('Update category error:', error)
  } finally {
    submitting.value = false
  }
}

// 删除分类
const handleDelete = (row: OptionCategory) => {
  if (!canDelete.value) {
    message.error('您没有权限删除选项分类')
    return
  }
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除分类 "${row.name}" 吗？删除后该分类下的所有选项数据也将被删除，此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await optionCategoriesApi.delete(Number(row.id))
        message.success('删除分类成功')
        loadCategories()
      } catch (error) {
        if (error instanceof Error) {
          message.error(error.message || '删除分类失败')
        }
        console.error('Delete category error:', error)
      }
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.option-categories-management {
  padding: 16px 0;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--n-border-color);
}

/* 数据表格样式 */
:deep(.n-data-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.n-data-table-th) {
  background-color: var(--n-color-target);
  font-weight: 600;
}

/* 按钮样式 */
:deep(.n-button + .n-button) {
  margin-left: 8px;
}

/* 表单样式 */
:deep(.n-form) {
  padding: 20px 0;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

/* 模态框样式 */
:deep(.n-modal .n-card) {
  max-width: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>