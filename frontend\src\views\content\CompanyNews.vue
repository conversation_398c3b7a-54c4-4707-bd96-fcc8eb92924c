<template>
  <div class="company-news">
    <!-- 页面头部 -->
    <PageHeader title="公司动态" description="发布和管理公司动态、新闻公告">
      <template #actions>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <n-icon><add-outline /></n-icon>
          </template>
          发布动态
        </n-button>
      </template>
    </PageHeader>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <n-space>
        <n-select
          v-model:value="filters.category"
          placeholder="选择分类"
          style="width: 150px"
          :options="categoryOptions"
          clearable
        />
        <n-select
          v-model:value="filters.status"
          placeholder="选择状态"
          style="width: 120px"
          :options="statusOptions"
          clearable
        />
        <n-input
          v-model:value="filters.keyword"
          placeholder="搜索标题或内容"
          style="width: 250px"
          clearable
        >
          <template #prefix>
            <n-icon><search-outline /></n-icon>
          </template>
        </n-input>
        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <n-icon><search-outline /></n-icon>
          </template>
          搜索
        </n-button>
        <n-button @click="handleReset">
          <template #icon>
            <n-icon><refresh-outline /></n-icon>
          </template>
          重置
        </n-button>
      </n-space>
    </n-card>

    <!-- 动态列表 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="newsList"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: NewsItem) => row.id"
        @update:page="handlePageChange"
      />
    </n-card>

    <!-- 创建/编辑动态弹窗 -->
    <n-modal v-model:show="showCreateModal" preset="card" title="发布公司动态" style="width: 800px">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="top"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="标题" path="title">
            <n-input v-model:value="formData.title" placeholder="请输入动态标题" />
          </n-form-item-gi>
          <n-form-item-gi label="分类" path="category">
            <n-select
              v-model:value="formData.category"
              placeholder="请选择分类"
              :options="categoryOptions"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="摘要" path="summary">
          <n-input
            v-model:value="formData.summary"
            type="textarea"
            placeholder="请输入动态摘要"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="内容" path="content">
          <n-input
            v-model:value="formData.content"
            type="textarea"
            placeholder="请输入动态内容"
            :rows="8"
          />
        </n-form-item>
        
        <n-form-item label="封面图片">
          <n-upload
            ref="uploadRef"
            :file-list="formData.images"
            list-type="image-card"
            :max="5"
            accept="image/*"
            @update:file-list="handleImageUpload"
          >
            点击上传图片
          </n-upload>
        </n-form-item>
        
        <n-form-item label="视频">
          <n-upload
            :file-list="formData.videos"
            list-type="text"
            :max="1"
            accept="video/*"
            @update:file-list="handleVideoUpload"
          >
            <n-button>上传视频</n-button>
          </n-upload>
        </n-form-item>
        
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="发布时间">
            <n-date-picker
              v-model:value="formData.publishTime"
              type="datetime"
              placeholder="选择发布时间"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="状态">
            <n-select
              v-model:value="formData.status"
              placeholder="选择状态"
              :options="statusOptions"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ editingId ? '更新' : '发布' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 分享二维码弹窗 -->
    <n-modal v-model:show="showQrModal" preset="card" title="分享二维码" style="width: 400px">
      <div class="qr-container">
        <div class="qr-code">
          <canvas ref="qrCanvas" width="200" height="200"></canvas>
        </div>
        <p class="qr-tip">扫描二维码分享此动态</p>
        <n-space justify="center">
          <n-button type="primary" @click="downloadQr">下载二维码</n-button>
        </n-space>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw } from 'vue'
import { NButton, NIcon, NTag, NSpace, useMessage, type UploadFileInfo } from 'naive-ui'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  AddOutline,
  SearchOutline,
  RefreshOutline,
  EyeOutline,
  CreateOutline,
  TrashOutline,
  ShareOutline
} from '@vicons/ionicons5'
import QRCode from 'qrcode'
import { useAuthStore } from '@/stores'

// 类型定义
interface NewsItem {
  id: string
  title: string
  category: string
  summary: string
  status: 'published' | 'draft' | 'offline'
  publishTime: string
  author: string
  views: number
  likes: number
  content?: string
}

interface FormData {
  title: string
  category: string
  summary: string
  content: string
  images: UploadFileInfo[]
  videos: UploadFileInfo[]
  publishTime: number
  status: string
}

const message = useMessage()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showCreateModal = ref(false)
const showQrModal = ref(false)
const editingId = ref<string | null>(null)
const qrCanvas = ref<HTMLCanvasElement>()
const currentShareUrl = ref('')

// 筛选条件
const filters = reactive({
  category: null,
  status: null,
  keyword: ''
})

// 表单数据
const formData = reactive<FormData>({
  title: '',
  category: '',
  summary: '',
  content: '',
  images: [],
  videos: [],
  publishTime: Date.now(),
  status: 'published'
})

// 表单验证规则
const formRules = {
  title: {
    required: true,
    message: '请输入动态标题',
    trigger: 'blur'
  },
  category: {
    required: true,
    message: '请选择分类',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入动态内容',
    trigger: 'blur'
  }
}

// 选项数据
const categoryOptions = [
  { label: '公司新闻', value: 'news' },
  { label: '公告通知', value: 'announcement' },
  { label: '活动资讯', value: 'activity' },
  { label: '行业动态', value: 'industry' },
  { label: '产品更新', value: 'product' }
]

const statusOptions = [
  { label: '已发布', value: 'published' },
  { label: '草稿', value: 'draft' },
  { label: '已下线', value: 'offline' }
]

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 新闻列表数据
const newsList = ref<NewsItem[]>([
  {
    id: '1',
    title: '公司2024年度总结大会成功举办',
    category: 'news',
    summary: '回顾过去一年的成就，展望未来发展方向',
    status: 'published',
    publishTime: '2024-01-15 10:00:00',
    author: '管理员',
    views: 1250,
    likes: 89
  },
  {
    id: '2',
    title: '新产品发布会邀请函',
    category: 'announcement',
    summary: '诚邀您参加我们的新产品发布会',
    status: 'published',
    publishTime: '2024-01-10 14:30:00',
    author: '市场部',
    views: 856,
    likes: 45
  },
  {
    id: '3',
    title: '员工培训计划启动',
    category: 'activity',
    summary: '提升员工技能，促进职业发展',
    status: 'draft',
    publishTime: '2024-01-08 09:00:00',
    author: '人事部',
    views: 0,
    likes: 0
  }
])

// 表格列配置
const columns = [
  {
    title: '标题',
    key: 'title',
    width: 300,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
    render(row: NewsItem) {
      const category = categoryOptions.find(item => item.value === row.category)
      return h(NTag, { type: 'info', size: 'small' }, { default: () => category?.label || row.category })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: NewsItem) {
      const statusMap = {
        published: { type: 'success' as const, text: '已发布' },
        draft: { type: 'warning' as const, text: '草稿' },
        offline: { type: 'error' as const, text: '已下线' }
      }
      const status = statusMap[row.status as keyof typeof statusMap]
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    }
  },
  {
    title: '发布时间',
    key: 'publishTime',
    width: 160
  },
  {
    title: '作者',
    key: 'author',
    width: 100
  },
  {
    title: '浏览/点赞',
    key: 'stats',
    width: 120,
    render(row: NewsItem) {
      return `${row.views}/${row.likes}`
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row: NewsItem) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'info',
            onClick: () => handleView(row)
          }, {
            default: () => '查看',
            icon: () => h(NIcon, null, { default: () => h(markRaw(EyeOutline)) })
          }),
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑',
            icon: () => h(NIcon, null, { default: () => h(markRaw(CreateOutline)) })
          }),
          h(NButton, {
            size: 'small',
            type: 'warning',
            onClick: () => handleShare(row)
          }, {
            default: () => '分享',
            icon: () => h(NIcon, null, { default: () => h(markRaw(ShareOutline)) })
          }),
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除',
            icon: () => h(NIcon, null, { default: () => h(markRaw(TrashOutline)) })
          })
        ]
      })
    }
  }
]

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadNewsList()
}

const handleReset = () => {
  filters.category = null
  filters.status = null
  filters.keyword = ''
  pagination.page = 1
  loadNewsList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadNewsList()
}

const loadNewsList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.itemCount = newsList.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleView = (row: NewsItem) => {
  // 查看详情逻辑
  message.info(`查看动态：${row.title}`)
}

const handleEdit = (row: NewsItem) => {
  editingId.value = row.id
  Object.assign(formData, {
    title: row.title,
    category: row.category,
    summary: row.summary,
    content: row.content || '',
    images: [],
    videos: [],
    publishTime: new Date(row.publishTime).getTime(),
    status: row.status
  })
  showCreateModal.value = true
}

const handleDelete = (row: NewsItem) => {
  // 删除逻辑
  message.success(`删除动态：${row.title}`)
}

const handleShare = async (row: NewsItem) => {
  const userId = authStore.user?.id || 'default'
  currentShareUrl.value = `${window.location.origin}/share/news/${row.id}?userId=${userId}`
  
  showQrModal.value = true
  
  // 生成二维码
  if (qrCanvas.value) {
    try {
      await QRCode.toCanvas(qrCanvas.value, currentShareUrl.value, {
        width: 200,
        margin: 2
      })
    } catch (error) {
      message.error('生成二维码失败')
    }
  }
}

const downloadQr = () => {
  if (qrCanvas.value) {
    const link = document.createElement('a')
    link.download = 'share-qrcode.png'
    link.href = qrCanvas.value.toDataURL()
    link.click()
  }
}

const handleImageUpload = (fileList: UploadFileInfo[]) => {
  formData.images = fileList
}

const handleVideoUpload = (fileList: UploadFileInfo[]) => {
  formData.videos = fileList
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingId.value) {
      message.success('更新动态成功')
    } else {
      message.success('发布动态成功')
    }
    
    showCreateModal.value = false
    resetForm()
    loadNewsList()
  } catch (error) {
    message.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingId.value = null
  Object.assign(formData, {
    title: '',
    category: '',
    summary: '',
    content: '',
    images: [],
    videos: [],
    publishTime: Date.now(),
    status: 'published'
  })
}

// 生命周期
onMounted(() => {
  loadNewsList()
})
</script>

<style scoped>
.company-news {
  padding: 16px;
}



.filter-card {
  margin-bottom: 16px;
}

.qr-container {
  text-align: center;
  padding: 20px;
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.qr-tip {
  color: #666;
  margin-bottom: 16px;
}
</style>