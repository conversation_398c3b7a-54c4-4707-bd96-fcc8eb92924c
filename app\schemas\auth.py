from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class LoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    captcha_code: Optional[str] = Field(None, description="验证码")
    captcha_key: Optional[str] = Field(None, description="验证码密钥")


class LoginResponse(BaseModel):
    """用户登录响应"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="令牌过期时间（秒）")
    user: dict = Field(..., description="用户信息")


class RegisterRequest(BaseModel):
    """用户注册请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, description="密码")
    real_name: Optional[str] = Field(None, max_length=50, description="真实姓名")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    captcha_code: str = Field(..., description="验证码")
    captcha_key: str = Field(..., description="验证码密钥")


class RegisterResponse(BaseModel):
    """用户注册响应"""
    id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    real_name: Optional[str] = Field(None, description="真实姓名")
    created_at: str = Field(..., description="创建时间")


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应"""
    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="令牌过期时间（秒）")


class CaptchaResponse(BaseModel):
    """验证码响应"""
    captcha_key: str = Field(..., description="验证码密钥")
    captcha_image: str = Field(..., description="验证码图片（base64编码）")


class VerifyCaptchaRequest(BaseModel):
    """验证验证码请求"""
    captcha_key: str = Field(..., description="验证码密钥")
    captcha_code: str = Field(..., description="验证码")


class UserInfoResponse(BaseModel):
    """用户信息响应"""
    id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    real_name: Optional[str] = Field(None, description="真实姓名")
    nickname: Optional[str] = Field(None, description="昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    phone: Optional[str] = Field(None, description="手机号")
    gender: Optional[str] = Field(None, description="性别")
    department_id: Optional[str] = Field(None, description="部门ID")
    department_name: Optional[str] = Field(None, description="部门名称")
    position: Optional[str] = Field(None, description="职位")
    is_superuser: bool = Field(default=False, description="是否超级管理员")
    is_active: bool = Field(default=True, description="是否启用")
    last_login: Optional[str] = Field(None, description="最后登录时间")
    created_at: str = Field(..., description="创建时间")


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码")


class ResetPasswordRequest(BaseModel):
    """重置密码请求"""
    email: EmailStr = Field(..., description="邮箱")
    reset_token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=6, description="新密码")


class LogoutResponse(BaseModel):
    """注销响应"""
    message: str = Field(default="注销成功", description="响应消息")