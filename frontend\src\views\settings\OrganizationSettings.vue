<template>
  <div class="organization-settings">
    <div class="page-header">
      <h2>组织架构管理</h2>
      <p class="page-description">管理企业组织架构，包括部门和人员信息</p>
    </div>

    <div class="content-wrapper">
      <div class="toolbar">
        <n-space>
          <n-button type="primary" @click="handleAddDepartment">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            新增部门
          </n-button>
          <n-button @click="handleAddEmployee">
            <template #icon>
              <n-icon><PersonAddOutline /></n-icon>
            </template>
            新增员工
          </n-button>
          <n-button @click="handleExpandAll">
            <template #icon>
              <n-icon><ChevronDownOutline /></n-icon>
            </template>
            {{ expandAll ? '收起全部' : '展开全部' }}
          </n-button>
        </n-space>
      </div>

      <div class="organization-tree">
        <n-tree
          :data="treeData"
          :expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          key-field="id"
          label-field="name"
          children-field="children"
          :render-prefix="renderPrefix"
          :render-suffix="renderSuffix"
          @update:expanded-keys="handleExpandedKeysChange"
          @update:selected-keys="handleSelectedKeysChange"
        />
      </div>
    </div>

    <!-- 新增/编辑部门弹窗 -->
    <n-modal v-model:show="showDepartmentModal" preset="dialog" title="部门信息">
      <n-form
        ref="departmentFormRef"
        :model="departmentForm"
        :rules="departmentRules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item label="部门名称" path="name">
          <n-input v-model:value="departmentForm.name" placeholder="请输入部门名称" />
        </n-form-item>
        <n-form-item label="上级部门" path="parentId">
          <n-tree-select
            v-model:value="departmentForm.parentId"
            :options="departmentOptions"
            key-field="id"
            label-field="name"
            children-field="children"
            placeholder="请选择上级部门"
            clearable
          />
        </n-form-item>
        <n-form-item label="部门描述" path="description">
          <n-input
            v-model:value="departmentForm.description"
            type="textarea"
            placeholder="请输入部门描述"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showDepartmentModal = false">取消</n-button>
          <n-button type="primary" @click="handleSaveDepartment">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 新增/编辑员工弹窗 -->
    <n-modal v-model:show="showEmployeeModal" preset="dialog" title="员工信息">
      <n-form
        ref="employeeFormRef"
        :model="employeeForm"
        :rules="employeeRules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item label="员工姓名" path="name">
          <n-input v-model:value="employeeForm.name" placeholder="请输入员工姓名" />
        </n-form-item>
        <n-form-item label="所属部门" path="departmentId">
          <n-tree-select
            v-model:value="employeeForm.departmentId"
            :options="departmentOptions"
            key-field="id"
            label-field="name"
            children-field="children"
            placeholder="请选择所属部门"
          />
        </n-form-item>
        <n-form-item label="职位" path="position">
          <n-input v-model:value="employeeForm.position" placeholder="请输入职位" />
        </n-form-item>
        <n-form-item label="手机号码" path="phone">
          <n-input v-model:value="employeeForm.phone" placeholder="请输入手机号码" />
        </n-form-item>
        <n-form-item label="邮箱" path="email">
          <n-input v-model:value="employeeForm.email" placeholder="请输入邮箱" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showEmployeeModal = false">取消</n-button>
          <n-button type="primary" @click="handleSaveEmployee">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted } from 'vue'
import {
  NTree,
  NButton,
  NSpace,
  NIcon,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NTreeSelect,
  useMessage,
  type TreeOption,
  type FormInst
} from 'naive-ui'
import {
  AddOutline,
  PersonAddOutline,
  ChevronDownOutline,
  BusinessOutline,
  PersonOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'

// 消息提示
const message = useMessage()

// 树形数据状态
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const expandAll = ref(false)

// 弹窗状态
const showDepartmentModal = ref(false)
const showEmployeeModal = ref(false)

// 表单引用
const departmentFormRef = ref<FormInst | null>(null)
const employeeFormRef = ref<FormInst | null>(null)

// 部门表单数据
const departmentForm = ref({
  id: '',
  name: '',
  parentId: null as string | null,
  description: ''
})

// 员工表单数据
const employeeForm = ref({
  id: '',
  name: '',
  departmentId: '',
  position: '',
  phone: '',
  email: ''
})

// 表单验证规则
const departmentRules = {
  name: {
    required: true,
    message: '请输入部门名称',
    trigger: 'blur'
  }
}

const employeeRules = {
  name: {
    required: true,
    message: '请输入员工姓名',
    trigger: 'blur'
  },
  departmentId: {
    required: true,
    message: '请选择所属部门',
    trigger: 'change'
  },
  position: {
    required: true,
    message: '请输入职位',
    trigger: 'blur'
  }
}

// 模拟组织架构数据
const organizationData = ref([
  {
    id: 'dept-1',
    name: '总经理办公室',
    type: 'department',
    description: '公司最高管理层',
    children: [
      {
        id: 'emp-1',
        name: '张总',
        type: 'employee',
        position: '总经理',
        phone: '13800138001',
        email: '<EMAIL>',
        departmentId: 'dept-1'
      },
      {
        id: 'emp-2',
        name: '李秘书',
        type: 'employee',
        position: '总经理秘书',
        phone: '13800138002',
        email: '<EMAIL>',
        departmentId: 'dept-1'
      }
    ]
  },
  {
    id: 'dept-2',
    name: '销售部',
    type: 'department',
    description: '负责公司产品销售和客户关系维护',
    children: [
      {
        id: 'emp-3',
        name: '王经理',
        type: 'employee',
        position: '销售经理',
        phone: '13800138003',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      },
      {
        id: 'emp-4',
        name: '赵专员',
        type: 'employee',
        position: '销售专员',
        phone: '13800138004',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      },
      {
        id: 'emp-5',
        name: '钱专员',
        type: 'employee',
        position: '销售专员',
        phone: '13800138005',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      }
    ]
  },
  {
    id: 'dept-3',
    name: '技术部',
    type: 'department',
    description: '负责产品研发和技术支持',
    children: [
      {
        id: 'dept-3-1',
        name: '前端开发组',
        type: 'department',
        description: '负责前端界面开发',
        parentId: 'dept-3',
        children: [
          {
            id: 'emp-6',
            name: '孙工程师',
            type: 'employee',
            position: '前端工程师',
            phone: '13800138006',
            email: '<EMAIL>',
            departmentId: 'dept-3-1'
          },
          {
            id: 'emp-7',
            name: '周工程师',
            type: 'employee',
            position: '前端工程师',
            phone: '13800138007',
            email: '<EMAIL>',
            departmentId: 'dept-3-1'
          }
        ]
      },
      {
        id: 'dept-3-2',
        name: '后端开发组',
        type: 'department',
        description: '负责后端服务开发',
        parentId: 'dept-3',
        children: [
          {
            id: 'emp-8',
            name: '吴工程师',
            type: 'employee',
            position: '后端工程师',
            phone: '13800138008',
            email: '<EMAIL>',
            departmentId: 'dept-3-2'
          }
        ]
      }
    ]
  },
  {
    id: 'dept-4',
    name: '人事部',
    type: 'department',
    description: '负责人力资源管理',
    children: [
      {
        id: 'emp-9',
        name: '郑主管',
        type: 'employee',
        position: '人事主管',
        phone: '13800138009',
        email: '<EMAIL>',
        departmentId: 'dept-4'
      }
    ]
  }
])

// 计算树形数据
const treeData = computed(() => organizationData.value)

// 计算部门选项（用于下拉选择）
const departmentOptions = computed(() => {
  const extractDepartments = (data: any[]): any[] => {
    const result: any[] = []
    data.forEach(item => {
      if (item.type === 'department') {
        result.push({
          id: item.id,
          name: item.name,
          children: item.children ? extractDepartments(item.children) : []
        })
      }
    })
    return result
  }
  return extractDepartments(organizationData.value)
})

// 渲染树节点前缀图标
const renderPrefix = ({ option }: { option: TreeOption }) => {
  const item = option as any
  return h(NIcon, {
    style: { marginRight: '8px' },
    color: item.type === 'department' ? '#1890ff' : '#52c41a'
  }, {
    default: () => h(item.type === 'department' ? BusinessOutline : PersonOutline)
  })
}

// 渲染树节点后缀操作按钮
const renderSuffix = ({ option }: { option: TreeOption }) => {
  const item = option as any
  return h(NSpace, { size: 'small' }, {
    default: () => [
      h(NButton, {
        size: 'tiny',
        type: 'primary',
        ghost: true,
        onClick: (e: Event) => {
          e.stopPropagation()
          handleEdit(item)
        }
      }, {
        icon: () => h(NIcon, null, { default: () => h(CreateOutline) }),
        default: () => '编辑'
      }),
      h(NButton, {
        size: 'tiny',
        type: 'error',
        ghost: true,
        onClick: (e: Event) => {
          e.stopPropagation()
          handleDelete(item)
        }
      }, {
        icon: () => h(NIcon, null, { default: () => h(TrashOutline) }),
        default: () => '删除'
      })
    ]
  })
}

// 处理展开状态变化
const handleExpandedKeysChange = (keys: string[]) => {
  expandedKeys.value = keys
}

// 处理选中状态变化
const handleSelectedKeysChange = (keys: string[]) => {
  selectedKeys.value = keys
}

// 展开/收起全部
const handleExpandAll = () => {
  if (expandAll.value) {
    expandedKeys.value = []
  } else {
    const getAllKeys = (data: any[]): string[] => {
      const keys: string[] = []
      data.forEach(item => {
        if (item.type === 'department') {
          keys.push(item.id)
          if (item.children) {
            keys.push(...getAllKeys(item.children))
          }
        }
      })
      return keys
    }
    expandedKeys.value = getAllKeys(organizationData.value)
  }
  expandAll.value = !expandAll.value
}

// 新增部门
const handleAddDepartment = () => {
  departmentForm.value = {
    id: '',
    name: '',
    parentId: null,
    description: ''
  }
  showDepartmentModal.value = true
}

// 新增员工
const handleAddEmployee = () => {
  employeeForm.value = {
    id: '',
    name: '',
    departmentId: '',
    position: '',
    phone: '',
    email: ''
  }
  showEmployeeModal.value = true
}

// 编辑
const handleEdit = (item: any) => {
  if (item.type === 'department') {
    departmentForm.value = {
      id: item.id,
      name: item.name,
      parentId: item.parentId || null,
      description: item.description || ''
    }
    showDepartmentModal.value = true
  } else {
    employeeForm.value = {
      id: item.id,
      name: item.name,
      departmentId: item.departmentId,
      position: item.position,
      phone: item.phone,
      email: item.email
    }
    showEmployeeModal.value = true
  }
}

// 删除
const handleDelete = (item: any) => {
  // 这里应该调用删除API
  message.success(`删除${item.type === 'department' ? '部门' : '员工'}：${item.name}`)
}

// 保存部门
const handleSaveDepartment = async () => {
  try {
    await departmentFormRef.value?.validate()
    // 这里应该调用保存API
    message.success(departmentForm.value.id ? '部门更新成功' : '部门创建成功')
    showDepartmentModal.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 保存员工
const handleSaveEmployee = async () => {
  try {
    await employeeFormRef.value?.validate()
    // 这里应该调用保存API
    message.success(employeeForm.value.id ? '员工信息更新成功' : '员工创建成功')
    showEmployeeModal.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 初始化
onMounted(() => {
  // 默认展开第一级部门
  expandedKeys.value = organizationData.value
    .filter(item => item.type === 'department')
    .map(item => item.id)
})
</script>

<style scoped>
.organization-settings {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toolbar {
  display: flex;
  justify-content: flex-start;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.organization-tree {
  min-height: 400px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fff;
}

:deep(.n-tree .n-tree-node-content) {
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.n-tree .n-tree-node-content:hover) {
  background: #f5f5f5;
}

:deep(.n-tree .n-tree-node-content--selected) {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

:deep(.n-tree .n-tree-node-content__text) {
  font-weight: 500;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}
</style>