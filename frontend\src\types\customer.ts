// 客户信息数据结构类型定义

// 重新导出Customer类型
export type { Customer } from '@/api/customerService'

// 基础信息接口
export interface CustomerBasicInfo {
  id?: string
  // 基本信息
  customerName: string // 客户称呼
  phone: string // 手机号
  gender: 'male' | 'female' | 'unknown' // 性别
  area: number // 面积
  decorationType: 'rough' | 'fine' | 'renovation' | 'partial' | 'private' // 装修类型：毛坯、精装、旧改、局改、私房
  customerCategory: 'A' | 'B' | 'C' | 'D' // 客户分类
  isKeyCustomer: boolean // 重点客户
  channelSource: string // 渠道来源
  communityName: string // 小区名称
  houseStatus: 'not_delivered' | 'delivered' | 'renovating' | 'completed' // 房屋状态：未交房、已交房、装修中、已完工
  remarks?: string // 备注信息
  tags: string[] // 客户标签
  notes: string // 备注信息
  createdBy: string // 创建人
  createdDepartment: string // 创建人部门
  createdAt: string // 创建时间
  updatedAt?: string // 更新时间
}

// 跟进信息接口
export interface CustomerFollowInfo {
  id?: string
  customerId: string // 关联的客户ID
  visitCount: number // 几访进店
  designer: string // 设计师
  designDirector: string // 设计师总监
  designDepartment: string // 设计部门
  hasVisitedStore: boolean // 是否到店
  storeVisitTime?: number | null // 到店时间
  measureTime?: number | null // 量房时间
  followContent: string // 跟进内容
  contractTime?: number | null // 签单时间
  contractPackage: string // 签单套餐
  transactionScreenshot: string // 交易截图
  createdAt: string // 创建时间
  updatedAt?: string // 更新时间
}

// 施工信息接口
export interface CustomerConstructionInfo {
  id?: string
  customerId: string // 关联的客户ID
  houseNumber: string // 门牌号
  projectSupervisor: string // 项目监理
  startTime?: number | null // 开工时间
  endTime?: number | null // 完工时间
  plannedDuration: number // 计划周期（天数）
  equipmentNumber: string // 设备号
  constructionProgress: number // 施工进度
  constructionNotes: string // 施工备注
  createdAt: string // 创建时间
  updatedAt?: string // 更新时间
}

// 完整的客户信息接口
export interface CustomerFullInfo {
  basicInfo: CustomerBasicInfo
  followInfo?: CustomerFollowInfo[]
  constructionInfo?: CustomerConstructionInfo
}

// 客户状态枚举
export enum CustomerStage {
  POTENTIAL = 'potential', // 潜在客户
  FOLLOWING = 'following', // 跟进中
  CONTRACTED = 'contracted', // 已签约
  CONSTRUCTION = 'construction', // 施工中
  COMPLETED = 'completed' // 已完工
}

// 客户状态判断函数
export function getCustomerStage(customer: CustomerFullInfo): CustomerStage {
  if (customer.constructionInfo) {
    return CustomerStage.CONSTRUCTION
  }
  
  if (customer.followInfo && customer.followInfo.length > 0) {
    const hasSignedOrder = customer.followInfo.some(follow => follow.contractTime)
    if (hasSignedOrder) {
      return CustomerStage.CONTRACTED
    }
    return CustomerStage.FOLLOWING
  }
  
  return CustomerStage.POTENTIAL
}

// 表单验证规则
export const customerValidationRules = {
  basicInfo: {
    customerName: {
      required: true,
      message: '请输入客户称呼',
      trigger: ['input', 'blur']
    },
    phone: {
      required: true,
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: ['input', 'blur']
    },
    area: {
      required: true,
      type: 'number' as const,
      message: '请输入房屋面积',
      trigger: ['input', 'blur']
    },
    channelSource: {
      required: true,
      message: '请选择渠道来源',
      trigger: ['change', 'blur']
    },
    communityName: {
      required: true,
      message: '请输入小区名称',
      trigger: ['input', 'blur']
    }
  },
  followInfo: {
    visitCount: {
      required: true,
      type: 'number' as const,
      message: '请输入访问次数',
      trigger: ['input', 'blur']
    },
    designer: {
      required: true,
      message: '请输入设计师姓名',
      trigger: ['input', 'blur']
    },
    designDepartment: {
      required: true,
      message: '请输入设计部门',
      trigger: ['input', 'blur']
    },
    followContent: {
      required: true,
      message: '请输入跟进内容',
      trigger: ['input', 'blur']
    }
  },
  constructionInfo: {
    houseNumber: {
      required: true,
      message: '请输入门牌号',
      trigger: ['input', 'blur']
    },
    projectSupervisor: {
      required: true,
      message: '请输入项目监理',
      trigger: ['input', 'blur']
    },
    plannedDuration: {
      required: true,
      type: 'number' as const,
      message: '请输入计划周期',
      trigger: ['input', 'blur']
    },
    equipmentNumber: {
      required: true,
      message: '请输入设备号',
      trigger: ['input', 'blur']
    }
  }
}

// 选项数据
export const customerOptions = {
  gender: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' },
    { label: '未知', value: 'unknown' }
  ],
  decorationType: [
    { label: '毛坯', value: 'rough' },
    { label: '精装', value: 'fine' },
    { label: '旧改', value: 'renovation' },
    { label: '局改', value: 'partial' },
    { label: '私房', value: 'private' }
  ],
  customerCategory: [
    { label: 'A类客户', value: 'A' },
    { label: 'B类客户', value: 'B' },
    { label: 'C类客户', value: 'C' },
    { label: 'D类客户', value: 'D' }
  ],
  houseStatus: [
    { label: '未交房', value: 'not_delivered' },
    { label: '已交房', value: 'delivered' },
    { label: '装修中', value: 'renovating' },
    { label: '已完工', value: 'completed' }
  ],
  channelSource: [
    { label: '线上推广', value: 'online' },
    { label: '朋友介绍', value: 'referral' },
    { label: '门店咨询', value: 'store' },
    { label: '电话咨询', value: 'phone' },
    { label: '展会活动', value: 'exhibition' },
    { label: '其他', value: 'other' }
  ],
  contractPackage: [
    { label: '基础套餐', value: 'basic' },
    { label: '标准套餐', value: 'standard' },
    { label: '豪华套餐', value: 'luxury' },
    { label: '全包套餐', value: 'full' },
    { label: '半包套餐', value: 'half' },
    { label: '自定义套餐', value: 'custom' }
  ]
}