<script setup lang="ts">
import { RouterView } from 'vue-router'
import { 
  NConfigProvider, 
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NLoadingBarProvider,
  darkTheme, 
  lightTheme 
} from 'naive-ui'
import { useAppStore } from '@/stores'
import { computed, watch, onMounted } from 'vue'

const appStore = useAppStore()

// 主题配置
const theme = computed(() => {
  return appStore.isDarkMode ? darkTheme : lightTheme
})

// 主题覆盖配置
const themeOverrides = {
  common: {
    primaryColor: '#667eea',
    primaryColorHover: '#5a6fd8',
    primaryColorPressed: '#4c63d2',
    primaryColorSuppl: '#7c8cff'
  }
}

// 更新body类名
const updateBodyClass = (isDark: boolean) => {
  if (isDark) {
    document.body.classList.add('dark')
  } else {
    document.body.classList.remove('dark')
  }
}

// 监听主题变化
watch(
  () => appStore.isDarkMode,
  (isDark) => {
    updateBodyClass(isDark)
  },
  { immediate: true }
)

// 组件挂载时初始化主题
onMounted(() => {
  updateBodyClass(appStore.isDarkMode)
})
</script>

<template>
  <NConfigProvider :theme="theme" :theme-overrides="themeOverrides">
    <NLoadingBarProvider>
      <NDialogProvider>
        <NNotificationProvider>
          <NMessageProvider>
            <div id="app">
              <RouterView />
            </div>
          </NMessageProvider>
        </NNotificationProvider>
      </NDialogProvider>
    </NLoadingBarProvider>
  </NConfigProvider>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 暗色主题样式 */
body.dark {
  color: #e5e7eb;
  background-color: #1f2937;
}

#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式断点 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

</style>
