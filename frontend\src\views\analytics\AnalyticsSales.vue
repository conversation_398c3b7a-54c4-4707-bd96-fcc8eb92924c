<template>
  <div class="analytics-sales">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
  
          <p class="page-description">销售数据统计与分析报表</p>
        </div>
        <n-space>
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            @update:value="handleDateChange"
          />
          <n-button type="primary" @click="handleExport">
            <template #icon>
              <n-icon><download-outline /></n-icon>
            </template>
            导出报表
          </n-button>
          <n-button type="default" @click="handleRefresh">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            刷新数据
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 核心指标卡片 -->
    <n-grid :cols="4" :x-gap="16" class="metrics-grid">
      <n-card class="metric-card">
        <n-statistic
          label="总销售额"
          :value="metrics.totalSales"
          :precision="2"
        >
          <template #prefix>¥</template>
        </n-statistic>
        <div class="metric-trend">
          <n-tag :type="metrics.salesTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.salesTrend >= 0 ? '+' : '' }}{{ metrics.salesTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="订单数量"
          :value="metrics.totalOrders"
        />
        <div class="metric-trend">
          <n-tag :type="metrics.ordersTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.ordersTrend >= 0 ? '+' : '' }}{{ metrics.ordersTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="平均客单价"
          :value="metrics.avgOrderValue"
          :precision="2"
        >
          <template #prefix>¥</template>
        </n-statistic>
        <div class="metric-trend">
          <n-tag :type="metrics.avgTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.avgTrend >= 0 ? '+' : '' }}{{ metrics.avgTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="转化率"
          :value="metrics.conversionRate"
          :precision="2"
        >
          <template #suffix>%</template>
        </n-statistic>
        <div class="metric-trend">
          <n-tag :type="metrics.conversionTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.conversionTrend >= 0 ? '+' : '' }}{{ metrics.conversionTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
    </n-grid>

    <!-- 图表区域 -->
    <n-grid :cols="2" :x-gap="16" class="charts-grid">
      <!-- 销售趋势图 -->
      <n-card title="销售趋势" class="chart-card">
        <div class="chart-container" ref="salesTrendRef"></div>
      </n-card>
      
      <!-- 销售渠道分布 -->
      <n-card title="销售渠道分布" class="chart-card">
        <div class="chart-container" ref="channelDistributionRef"></div>
      </n-card>
    </n-grid>

    <n-grid :cols="2" :x-gap="16" class="charts-grid">
      <!-- 产品销售排行 -->
      <n-card title="产品销售排行" class="chart-card">
        <div class="chart-container" ref="productRankingRef"></div>
      </n-card>
      
      <!-- 销售人员业绩 -->
      <n-card title="销售人员业绩" class="chart-card">
        <div class="chart-container" ref="salesPersonRef"></div>
      </n-card>
    </n-grid>

    <!-- 详细数据表格 -->
    <n-card title="销售明细" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索订单号、客户名称"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><search-outline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="订单状态"
            clearable
            style="width: 120px"
            :options="statusOptions"
          />
        </n-space>
      </template>
      
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="salesData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: SalesRecord) => row.id"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  DownloadOutline,
  RefreshOutline,
  SearchOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import type { SalesRecord } from '@/types'
import type { DataTableColumns } from 'naive-ui'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const dateRange = ref<[number, number] | null>(null)
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const salesData = ref<SalesRecord[]>([])

// 图表引用
const salesTrendRef = ref<HTMLElement>()
const channelDistributionRef = ref<HTMLElement>()
const productRankingRef = ref<HTMLElement>()
const salesPersonRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  totalSales: 0,
  salesTrend: 0,
  totalOrders: 0,
  ordersTrend: 0,
  avgOrderValue: 0,
  avgTrend: 0,
  conversionRate: 0,
  conversionTrend: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 状态选项
const statusOptions = [
  { label: '待付款', value: 'pending' },
  { label: '已付款', value: 'paid' },
  { label: '已发货', value: 'shipped' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
  { label: '已退款', value: 'refunded' }
]

// 表格列配置
const columns: DataTableColumns<SalesRecord> = [
  {
    title: '订单号',
    key: 'orderNo',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '客户名称',
    key: 'customerName',
    width: 120
  },
  {
    title: '产品名称',
    key: 'productName',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '销售金额',
    key: 'amount',
    width: 120,
    render: (row) => `¥${row.amount.toLocaleString()}`
  },
  {
    title: '数量',
    key: 'quantity',
    width: 80
  },
  {
    title: '销售人员',
    key: 'salesPerson',
    width: 100
  },
  {
    title: '销售渠道',
    key: 'channel',
    width: 100
  },
  {
    title: '订单状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const status = statusOptions.find(item => item.value === row.status)
      const statusMap: Record<string, string> = {
        pending: 'warning',
        paid: 'info',
        shipped: 'primary',
        completed: 'success',
        cancelled: 'error',
        refunded: 'error'
      }
      return h(
        'n-tag',
        { type: statusMap[row.status] || 'default', size: 'small' },
        { default: () => status?.label || row.status }
      )
    }
  },
  {
    title: '下单时间',
    key: 'orderDate',
    width: 150,
    render: (row) => new Date(row.orderDate).toLocaleString()
  }
]

// 图表实例
let salesTrendChart: echarts.ECharts | null = null
let channelDistributionChart: echarts.ECharts | null = null
let productRankingChart: echarts.ECharts | null = null
let salesPersonChart: echarts.ECharts | null = null

// 方法
const handleDateChange = () => {
  fetchData()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const handleRefresh = () => {
  fetchData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchSalesData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchSalesData()
}

// 初始化销售趋势图
const initSalesTrendChart = () => {
  if (!salesTrendRef.value) return
  
  salesTrendChart = echarts.init(salesTrendRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['销售额', '订单数']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额(万元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'line',
        yAxisIndex: 0,
        data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
        smooth: true,
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '订单数',
        type: 'bar',
        yAxisIndex: 1,
        data: [45, 52, 38, 48, 35, 78, 72, 65, 68, 82, 95, 105],
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }
  
  salesTrendChart.setOption(option)
}

// 初始化渠道分布图
const initChannelDistributionChart = () => {
  if (!channelDistributionRef.value) return
  
  channelDistributionChart = echarts.init(channelDistributionRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '销售渠道',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 335, name: '线上商城' },
          { value: 310, name: '门店销售' },
          { value: 234, name: '电话销售' },
          { value: 135, name: '微信销售' },
          { value: 148, name: '其他渠道' }
        ]
      }
    ]
  }
  
  channelDistributionChart.setOption(option)
}

// 初始化产品排行图
const initProductRankingChart = () => {
  if (!productRankingRef.value) return
  
  productRankingChart = echarts.init(productRankingRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['产品E', '产品D', '产品C', '产品B', '产品A']
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: [120, 200, 150, 80, 70],
        itemStyle: {
          color: '#fac858'
        }
      }
    ]
  }
  
  productRankingChart.setOption(option)
}

// 初始化销售人员业绩图
const initSalesPersonChart = () => {
  if (!salesPersonRef.value) return
  
  salesPersonChart = echarts.init(salesPersonRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['张三', '李四', '王五', '赵六', '钱七']
    },
    yAxis: {
      type: 'value',
      name: '销售额(万元)'
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: [85, 73, 65, 58, 42],
        itemStyle: {
          color: '#ee6666'
        }
      }
    ]
  }
  
  salesPersonChart.setOption(option)
}

// 获取核心指标数据
const fetchMetrics = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    Object.assign(metrics, {
      totalSales: 2580000,
      salesTrend: 12.5,
      totalOrders: 1256,
      ordersTrend: 8.3,
      avgOrderValue: 2054.78,
      avgTrend: 3.8,
      conversionRate: 15.6,
      conversionTrend: -2.1
    })
  } catch (error) {
    message.error('获取指标数据失败')
  }
}

// 获取销售明细数据
const fetchSalesData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: SalesRecord[] = [
      {
        id: 1,
        orderNo: 'ORD202403001',
        customerName: '张三',
        productName: '产品A',
        amount: 2580,
        quantity: 2,
        salesPerson: '李销售',
        channel: '线上商城',
        status: 'completed',
        orderDate: '2024-03-01T10:30:00Z'
      },
      {
        id: 2,
        orderNo: 'ORD202403002',
        customerName: '李四',
        productName: '产品B',
        amount: 1890,
        quantity: 1,
        salesPerson: '王销售',
        channel: '门店销售',
        status: 'shipped',
        orderDate: '2024-03-01T14:20:00Z'
      }
    ]
    
    salesData.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('获取销售数据失败')
  } finally {
    loading.value = false
  }
}

// 获取所有数据
const fetchData = async () => {
  await Promise.all([
    fetchMetrics(),
    fetchSalesData()
  ])
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initSalesTrendChart()
  initChannelDistributionChart()
  initProductRankingChart()
  initSalesPersonChart()
}

// 监听搜索和筛选
watch([searchKeyword, statusFilter], () => {
  pagination.page = 1
  fetchSalesData()
})

// 初始化
onMounted(async () => {
  await fetchData()
  await initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    salesTrendChart?.resize()
    channelDistributionChart?.resize()
    productRankingChart?.resize()
    salesPersonChart?.resize()
  })
})
</script>

<style scoped>
.analytics-sales {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.metrics-grid {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
  position: relative;
}

.metric-trend {
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.trend-text {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.charts-grid {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  width: 100%;
  height: 320px;
}

.table-card {
  margin-bottom: 20px;
}
</style>