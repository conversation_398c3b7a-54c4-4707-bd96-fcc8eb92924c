<template>
  <div class="storage-settings">
    <n-form
      ref="storageFormRef"
      :model="storageSettings"
      :rules="storageRules"
      label-placement="left"
      label-width="150px"
      class="settings-form"
    >
      <n-form-item label="存储类型" path="storageType">
        <n-radio-group v-model:value="storageSettings.storageType">
          <n-space>
            <n-radio value="local">本地存储</n-radio>
            <n-radio value="oss">阿里云OSS</n-radio>
            <n-radio value="qiniu">七牛云</n-radio>
            <n-radio value="tencent">腾讯云COS</n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
      
      <template v-if="storageSettings.storageType === 'local'">
        <n-form-item label="存储路径" path="localPath">
          <n-input
            v-model:value="storageSettings.localPath"
            placeholder="请输入本地存储路径"
          />
        </n-form-item>
      </template>
      
      <template v-if="storageSettings.storageType === 'oss'">
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="AccessKey ID" path="ossAccessKeyId">
            <n-input
              v-model:value="storageSettings.ossAccessKeyId"
              placeholder="请输入AccessKey ID"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="AccessKey Secret" path="ossAccessKeySecret">
            <n-input
              v-model:value="storageSettings.ossAccessKeySecret"
              type="password"
              placeholder="请输入AccessKey Secret"
              show-password-on="click"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="Bucket名称" path="ossBucket">
            <n-input
              v-model:value="storageSettings.ossBucket"
              placeholder="请输入Bucket名称"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="地域节点" path="ossEndpoint">
            <n-input
              v-model:value="storageSettings.ossEndpoint"
              placeholder="请输入地域节点"
            />
          </n-form-item-gi>
        </n-grid>
      </template>
      
      <template v-if="storageSettings.storageType === 'qiniu'">
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="AccessKey" path="qiniuAccessKey">
            <n-input
              v-model:value="storageSettings.qiniuAccessKey"
              placeholder="请输入AccessKey"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="SecretKey" path="qiniuSecretKey">
            <n-input
              v-model:value="storageSettings.qiniuSecretKey"
              type="password"
              placeholder="请输入SecretKey"
              show-password-on="click"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="存储空间" path="qiniuBucket">
            <n-input
              v-model:value="storageSettings.qiniuBucket"
              placeholder="请输入存储空间名称"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="访问域名" path="qiniuDomain">
            <n-input
              v-model:value="storageSettings.qiniuDomain"
              placeholder="请输入访问域名"
            />
          </n-form-item-gi>
        </n-grid>
      </template>
      
      <template v-if="storageSettings.storageType === 'tencent'">
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="SecretId" path="tencentSecretId">
            <n-input
              v-model:value="storageSettings.tencentSecretId"
              placeholder="请输入SecretId"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="SecretKey" path="tencentSecretKey">
            <n-input
              v-model:value="storageSettings.tencentSecretKey"
              type="password"
              placeholder="请输入SecretKey"
              show-password-on="click"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi label="存储桶" path="tencentBucket">
            <n-input
              v-model:value="storageSettings.tencentBucket"
              placeholder="请输入存储桶名称"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="地域" path="tencentRegion">
            <n-input
              v-model:value="storageSettings.tencentRegion"
              placeholder="请输入地域"
            />
          </n-form-item-gi>
        </n-grid>
      </template>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="最大文件大小(MB)" path="maxFileSize">
          <n-input-number
            v-model:value="storageSettings.maxFileSize"
            :min="1"
            :max="100"
            placeholder="请输入最大文件大小"
            style="width: 100%"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="允许的文件类型">
          <n-select
            v-model:value="storageSettings.allowedFileTypes"
            multiple
            placeholder="请选择允许的文件类型"
            :options="fileTypeOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="文件命名规则">
        <n-radio-group v-model:value="storageSettings.namingRule">
          <n-space>
            <n-radio value="original">保持原名</n-radio>
            <n-radio value="timestamp">时间戳</n-radio>
            <n-radio value="uuid">UUID</n-radio>
            <n-radio value="hash">文件哈希</n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
      
      <n-form-item>
        <n-space>
          <n-button type="primary" @click="handleSave">
            <template #icon>
              <n-icon><save-outline /></n-icon>
            </template>
            保存设置
          </n-button>
          <n-button type="default" @click="handleReset">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
          <n-button type="info" @click="handleTestConnection">
            <template #icon>
              <n-icon><cloud-outline /></n-icon>
            </template>
            测试连接
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { 
  NForm, NFormItem, NFormItemGi, NGrid, NInput, NInputNumber, NButton, NIcon, NSpace, 
  NRadioGroup, NRadio, NSelect, useMessage 
} from 'naive-ui'
import { SaveOutline, RefreshOutline, CloudOutline } from '@vicons/ionicons5'
import { createDiscreteApi } from 'naive-ui'

const { message } = createDiscreteApi(['message'])

interface StorageSettings {
  storageType: string
  localPath: string
  ossAccessKeyId: string
  ossAccessKeySecret: string
  ossBucket: string
  ossEndpoint: string
  qiniuAccessKey: string
  qiniuSecretKey: string
  qiniuBucket: string
  qiniuDomain: string
  tencentSecretId: string
  tencentSecretKey: string
  tencentBucket: string
  tencentRegion: string
  maxFileSize: number
  allowedFileTypes: string[]
  namingRule: string
}

const storageFormRef = ref()
const storageSettings = reactive<StorageSettings>({
  storageType: 'local',
  localPath: './uploads',
  ossAccessKeyId: '',
  ossAccessKeySecret: '',
  ossBucket: '',
  ossEndpoint: '',
  qiniuAccessKey: '',
  qiniuSecretKey: '',
  qiniuBucket: '',
  qiniuDomain: '',
  tencentSecretId: '',
  tencentSecretKey: '',
  tencentBucket: '',
  tencentRegion: '',
  maxFileSize: 10,
  allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
  namingRule: 'timestamp'
})

const fileTypeOptions = [
  { label: 'JPG', value: 'jpg' },
  { label: 'JPEG', value: 'jpeg' },
  { label: 'PNG', value: 'png' },
  { label: 'GIF', value: 'gif' },
  { label: 'PDF', value: 'pdf' },
  { label: 'DOC', value: 'doc' },
  { label: 'DOCX', value: 'docx' },
  { label: 'XLS', value: 'xls' },
  { label: 'XLSX', value: 'xlsx' },
  { label: 'TXT', value: 'txt' },
  { label: 'ZIP', value: 'zip' },
  { label: 'RAR', value: 'rar' }
]

const storageRules = {
  storageType: {
    required: true,
    message: '请选择存储类型',
    trigger: 'change'
  },
  localPath: {
    required: true,
    message: '请输入本地存储路径',
    trigger: 'blur'
  },
  maxFileSize: {
    required: true,
    type: 'number' as const,
    min: 1,
    max: 100,
    message: '文件大小必须在1-100MB之间',
    trigger: 'blur'
  }
}

const handleSave = async () => {
  try {
    await storageFormRef.value?.validate()
    // TODO: 调用API保存设置
    message.success('存储设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const handleReset = () => {
  storageFormRef.value?.restoreValidation()
  Object.assign(storageSettings, {
    storageType: 'local',
    localPath: './uploads',
    ossAccessKeyId: '',
    ossAccessKeySecret: '',
    ossBucket: '',
    ossEndpoint: '',
    qiniuAccessKey: '',
    qiniuSecretKey: '',
    qiniuBucket: '',
    qiniuDomain: '',
    tencentSecretId: '',
    tencentSecretKey: '',
    tencentBucket: '',
    tencentRegion: '',
    maxFileSize: 10,
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    namingRule: 'timestamp'
  })
}

const handleTestConnection = async () => {
  try {
    // TODO: 调用API测试存储连接
    message.success('存储连接测试成功')
  } catch (error) {
    message.error('存储连接测试失败')
  }
}

onMounted(() => {
  // TODO: 从API加载设置数据
})
</script>

<style scoped>
.storage-settings {
  padding: 24px;
}

.settings-form {
  max-width: 800px;
}
</style>