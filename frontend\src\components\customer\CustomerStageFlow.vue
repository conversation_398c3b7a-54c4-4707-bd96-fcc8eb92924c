<template>
  <div class="customer-stage-flow">
    <n-steps
      :current="currentStepIndex"
      :status="stepStatus"
      size="small"
    >
      <n-step
        v-for="(stage, index) in stages"
        :key="stage.value"
        :title="stage.label"
        :description="stage.description"
      >
        <template #icon>
          <n-icon :color="getStageIconColor(stage.value, index)">
            <component :is="stage.icon" />
          </n-icon>
        </template>
      </n-step>
    </n-steps>

    <div class="stage-actions" v-if="showActions">
      <n-space>
        <n-button
          v-if="canAdvanceStage"
          type="primary"
          size="small"
          @click="handleAdvanceStage"
        >
          推进到下一阶段
        </n-button>
        <n-button
          v-if="canRegressStage"
          type="default"
          size="small"
          @click="handleRegressStage"
        >
          回退阶段
        </n-button>
      </n-space>
    </div>

    <!-- 阶段详情弹窗 -->
    <n-modal
      v-model:show="showStageModal"
      preset="dialog"
      title="阶段操作确认"
      :positive-text="modalConfig.positiveText"
      :negative-text="modalConfig.negativeText"
      @positive-click="handleStageConfirm"
    >
      <div class="stage-modal-content">
        <p>{{ modalConfig.content }}</p>
        <n-form v-if="modalConfig.showForm" ref="formRef" :model="stageForm" :rules="stageFormRules">
          <n-form-item label="操作说明" path="description">
            <n-input
              v-model:value="stageForm.description"
              type="textarea"
              placeholder="请输入阶段变更说明..."
              :rows="3"
            />
          </n-form-item>
        </n-form>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useMessage } from 'naive-ui'
import {
  PersonOutline,
  CallOutline,
  DocumentTextOutline,
  HammerOutline,
  CheckmarkCircleOutline
} from '@vicons/ionicons5'
import { CustomerStage, customerStageUtils } from '@/utils/customerStage'
import type { Customer } from '@/types/customer'

interface Props {
  customer: Customer | null
  showActions?: boolean
  readonly?: boolean
}

interface Emits {
  (e: 'stage-updated', stage: CustomerStage): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  readonly: false
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const showStageModal = ref(false)
const formRef = ref()
const stageForm = reactive({
  description: '',
  targetStage: CustomerStage.INITIAL
})

// 阶段配置
const stages = [
  {
    value: CustomerStage.INITIAL,
    label: '潜在客户',
    description: '初步接触',
    icon: PersonOutline
  },
  {
    value: CustomerStage.FOLLOWING,
    label: '跟进中',
    description: '积极沟通',
    icon: CallOutline
  },
  {
    value: CustomerStage.CONTRACTED,
    label: '已签约',
    description: '合同签署',
    icon: DocumentTextOutline
  },
  {
    value: CustomerStage.CONSTRUCTION,
    label: '施工中',
    description: '项目实施',
    icon: HammerOutline
  },
  {
    value: CustomerStage.COMPLETED,
    label: '已完工',
    description: '项目完成',
    icon: CheckmarkCircleOutline
  }
]

// 计算属性
const currentStage = computed(() => {
  if (!props.customer) return CustomerStage.INITIAL
  return customerStageUtils.calculateStage(props.customer)
})

const currentStepIndex = computed(() => {
  return stages.findIndex(stage => stage.value === currentStage.value)
})

const stepStatus = computed(() => {
  return currentStepIndex.value >= 0 ? 'process' : 'wait'
})

const canAdvanceStage = computed(() => {
  if (props.readonly) return false
  return currentStepIndex.value < stages.length - 1
})

const canRegressStage = computed(() => {
  if (props.readonly) return false
  return currentStepIndex.value > 0
})

const modalConfig = computed(() => {
  const isAdvance = stageForm.targetStage > currentStage.value
  return {
    content: isAdvance 
      ? `确定要将客户推进到"${getStageLabel(stageForm.targetStage)}"阶段吗？`
      : `确定要将客户回退到"${getStageLabel(stageForm.targetStage)}"阶段吗？`,
    positiveText: isAdvance ? '推进' : '回退',
    negativeText: '取消',
    showForm: true
  }
})

// 表单验证规则
const stageFormRules = {
  description: {
    required: true,
    message: '请输入阶段变更说明',
    trigger: ['blur', 'input']
  }
}

// 方法
const getStageIconColor = (stage: CustomerStage, index: number) => {
  if (index <= currentStepIndex.value) {
    return customerStageUtils.getStageColor(stage)
  }
  return '#d9d9d9'
}

const getStageLabel = (stage: CustomerStage) => {
  const stageConfig = stages.find(s => s.value === stage)
  return stageConfig?.label || '未知阶段'
}

const handleAdvanceStage = () => {
  if (currentStepIndex.value < stages.length - 1) {
    stageForm.targetStage = stages[currentStepIndex.value + 1].value
    stageForm.description = ''
    showStageModal.value = true
  }
}

const handleRegressStage = () => {
  if (currentStepIndex.value > 0) {
    stageForm.targetStage = stages[currentStepIndex.value - 1].value
    stageForm.description = ''
    showStageModal.value = true
  }
}

const handleStageConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 这里应该调用API更新客户阶段
    // await customerStore.updateCustomerStage(props.customer.id, stageForm.targetStage, stageForm.description)
    
    message.success('阶段更新成功')
    emit('stage-updated', stageForm.targetStage)
    showStageModal.value = false
  } catch (error) {
    console.error('阶段更新失败:', error)
  }
}
</script>

<style scoped lang="less">
.customer-stage-flow {
  .stage-actions {
    margin-top: 16px;
    text-align: center;
  }

  .stage-modal-content {
    padding: 16px 0;
  }
}

:deep(.n-steps) {
  .n-step {
    .n-step-indicator {
      .n-step-indicator-slot {
        .n-icon {
          font-size: 18px;
        }
      }
    }
  }
}
</style>