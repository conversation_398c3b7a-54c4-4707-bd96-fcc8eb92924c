<template>
  <div class="group-analysis">
    <!-- 分析配置 -->
    <n-card title="分析配置" class="config-card">
      <div class="config-row">
        <div class="config-item">
          <n-form-item label="分析维度">
            <n-select
              v-model:value="config.dimension"
              :options="dimensionOptions"
              @update:value="handleConfigChange"
            />
          </n-form-item>
        </div>
        <div class="config-item">
          <n-form-item label="时间范围">
            <n-date-picker
              v-model:value="config.timeRange"
              type="daterange"
              @update:value="handleConfigChange"
            />
          </n-form-item>
        </div>
        <div class="config-item">
          <n-form-item label="群组类型">
            <n-select
              v-model:value="config.groupType"
              :options="groupTypeOptions"
              clearable
              @update:value="handleConfigChange"
            />
          </n-form-item>
        </div>
        <div class="config-item">
          <n-form-item label="对比分析">
            <n-switch
              v-model:value="config.enableComparison"
              @update:value="handleConfigChange"
            />
          </n-form-item>
        </div>
      </div>
    </n-card>

    <!-- 分析结果 -->
    <div class="analysis-results">
      <!-- 概览指标 -->
      <n-card title="概览指标" class="overview-card">
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-value">{{ analysisData.totalGroups }}</div>
            <div class="metric-label">总群组数</div>
            <div class="metric-change positive">+{{ analysisData.groupGrowth }}%</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ analysisData.activeGroups }}</div>
            <div class="metric-label">活跃群组</div>
            <div class="metric-change positive">+{{ analysisData.activeGrowth }}%</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ analysisData.totalMembers }}</div>
            <div class="metric-label">总成员数</div>
            <div class="metric-change positive">+{{ analysisData.memberGrowth }}%</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ analysisData.avgActivity }}</div>
            <div class="metric-label">平均活跃度</div>
            <div class="metric-change negative">-{{ analysisData.activityChange }}%</div>
          </div>
        </div>
      </n-card>

      <!-- 群组活跃度趋势 -->
      <n-card title="群组活跃度趋势" class="trend-card">
        <div class="chart-container">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </n-card>

      <!-- 群组类型分布 -->
      <div class="chart-row">
        <n-card title="群组类型分布" class="chart-card">
          <div class="chart-container small">
            <div ref="typeChartRef" class="chart"></div>
          </div>
        </n-card>
        
        <n-card title="成员规模分布" class="chart-card">
          <div class="chart-container small">
            <div ref="sizeChartRef" class="chart"></div>
          </div>
        </n-card>
      </div>

      <!-- 消息活跃度分析 -->
      <n-card title="消息活跃度分析" class="activity-card">
        <div class="activity-analysis">
          <div class="activity-chart">
            <div ref="activityChartRef" class="chart"></div>
          </div>
          <div class="activity-stats">
            <h4>活跃度统计</h4>
            <div class="stat-list">
              <div class="stat-item">
                <span class="stat-label">日均消息数：</span>
                <span class="stat-value">{{ analysisData.dailyMessages }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">峰值时段：</span>
                <span class="stat-value">{{ analysisData.peakHours }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">活跃群组占比：</span>
                <span class="stat-value">{{ analysisData.activeRatio }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均响应时间：</span>
                <span class="stat-value">{{ analysisData.avgResponseTime }}分钟</span>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 群组排行榜 -->
      <n-card title="群组排行榜" class="ranking-card">
        <n-tabs v-model:value="rankingTab" type="line">
          <n-tab-pane name="activity" tab="活跃度排行">
            <n-data-table
              :columns="rankingColumns"
              :data="activityRanking"
              size="small"
              :pagination="false"
            />
          </n-tab-pane>
          <n-tab-pane name="members" tab="成员数排行">
            <n-data-table
              :columns="rankingColumns"
              :data="memberRanking"
              size="small"
              :pagination="false"
            />
          </n-tab-pane>
          <n-tab-pane name="growth" tab="增长率排行">
            <n-data-table
              :columns="growthColumns"
              :data="growthRanking"
              size="small"
              :pagination="false"
            />
          </n-tab-pane>
        </n-tabs>
      </n-card>

      <!-- 群组健康度评估 -->
      <n-card title="群组健康度评估" class="health-card">
        <div class="health-assessment">
          <div class="health-overview">
            <div class="health-score">
              <div class="score-circle">
                <div class="score-number">{{ healthScore }}</div>
                <div class="score-label">健康度评分</div>
              </div>
            </div>
            <div class="health-factors">
              <div class="factor-item">
                <span class="factor-label">活跃度：</span>
                <n-progress
                  type="line"
                  :percentage="healthFactors.activity"
                  :show-indicator="false"
                  status="success"
                />
                <span class="factor-value">{{ healthFactors.activity }}%</span>
              </div>
              <div class="factor-item">
                <span class="factor-label">参与度：</span>
                <n-progress
                  type="line"
                  :percentage="healthFactors.engagement"
                  :show-indicator="false"
                  status="info"
                />
                <span class="factor-value">{{ healthFactors.engagement }}%</span>
              </div>
              <div class="factor-item">
                <span class="factor-label">稳定性：</span>
                <n-progress
                  type="line"
                  :percentage="healthFactors.stability"
                  :show-indicator="false"
                  status="warning"
                />
                <span class="factor-value">{{ healthFactors.stability }}%</span>
              </div>
              <div class="factor-item">
                <span class="factor-label">增长性：</span>
                <n-progress
                  type="line"
                  :percentage="healthFactors.growth"
                  :show-indicator="false"
                  status="error"
                />
                <span class="factor-value">{{ healthFactors.growth }}%</span>
              </div>
            </div>
          </div>
          
          <div class="health-suggestions">
            <h4>优化建议</h4>
            <div class="suggestion-list">
              <div class="suggestion-item" v-for="suggestion in suggestions" :key="suggestion.id">
                <n-icon :color="suggestion.color"><suggestion.icon /></n-icon>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-button type="primary" @click="exportReport">
        <template #icon>
          <n-icon><DownloadOutline /></n-icon>
        </template>
        导出报告
      </n-button>
      <n-button @click="refreshAnalysis" :loading="loading">
        <template #icon>
          <n-icon><RefreshOutline /></n-icon>
        </template>
        刷新分析
      </n-button>
      <n-button @click="$emit('close')">
        关闭
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard, NFormItem, NSelect, NDatePicker, NSwitch, NTabs, NTabPane,
  NDataTable, NProgress, NButton, NIcon, useMessage
} from 'naive-ui'
import {
  DownloadOutline, RefreshOutline, TrendingUpOutline, AlertCircleOutline,
  CheckmarkCircleOutline, InformationCircleOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface Emits {
  (e: 'close'): void
}

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const rankingTab = ref('activity')
const trendChartRef = ref<HTMLElement>()
const typeChartRef = ref<HTMLElement>()
const sizeChartRef = ref<HTMLElement>()
const activityChartRef = ref<HTMLElement>()

// 分析配置
const config = ref({
  dimension: 'activity',
  timeRange: null,
  groupType: null,
  enableComparison: false
})

// 配置选项
const dimensionOptions = [
  { label: '活跃度分析', value: 'activity' },
  { label: '成员分析', value: 'members' },
  { label: '消息分析', value: 'messages' },
  { label: '增长分析', value: 'growth' }
]

const groupTypeOptions = [
  { label: '普通群', value: 'normal' },
  { label: '企业群', value: 'enterprise' },
  { label: '客服群', value: 'service' }
]

// 分析数据
const analysisData = ref({
  totalGroups: 156,
  groupGrowth: 12.5,
  activeGroups: 89,
  activeGrowth: 8.3,
  totalMembers: 2456,
  memberGrowth: 15.2,
  avgActivity: 78.5,
  activityChange: 3.2,
  dailyMessages: 1234,
  peakHours: '20:00-22:00',
  activeRatio: 67.3,
  avgResponseTime: 5.2
})

// 健康度评分
const healthScore = ref(85)
const healthFactors = ref({
  activity: 88,
  engagement: 76,
  stability: 92,
  growth: 68
})

// 优化建议
const suggestions = ref([
  {
    id: 1,
    icon: TrendingUpOutline,
    color: '#18a058',
    title: '提升群组活跃度',
    description: '建议在低活跃时段发起话题讨论，增加群组互动'
  },
  {
    id: 2,
    icon: AlertCircleOutline,
    color: '#f0a020',
    title: '关注沉默群组',
    description: '有23个群组近期活跃度下降，建议及时干预'
  },
  {
    id: 3,
    icon: CheckmarkCircleOutline,
    color: '#2080f0',
    title: '优化群组结构',
    description: '建议将大型群组拆分为多个小群，提高管理效率'
  }
])

// 排行榜数据
const activityRanking = ref([
  { rank: 1, name: '产品讨论群', value: 95.6, change: '+5.2%' },
  { rank: 2, name: '技术交流群', value: 89.3, change: '+2.1%' },
  { rank: 3, name: '客户服务群', value: 87.8, change: '-1.3%' },
  { rank: 4, name: '市场推广群', value: 82.5, change: '+3.7%' },
  { rank: 5, name: '内部沟通群', value: 78.9, change: '+1.8%' }
])

const memberRanking = ref([
  { rank: 1, name: '全员大群', value: 500, change: '+12' },
  { rank: 2, name: '客户交流群', value: 356, change: '+8' },
  { rank: 3, name: '合作伙伴群', value: 289, change: '+15' },
  { rank: 4, name: '产品用户群', value: 234, change: '+6' },
  { rank: 5, name: '技术支持群', value: 198, change: '+3' }
])

const growthRanking = ref([
  { rank: 1, name: '新品发布群', growth: 45.6, members: 89 },
  { rank: 2, name: '活动推广群', growth: 32.1, members: 156 },
  { rank: 3, name: '用户反馈群', growth: 28.7, members: 234 },
  { rank: 4, name: '合作洽谈群', growth: 25.3, members: 67 },
  { rank: 5, name: '内测体验群', growth: 22.9, members: 123 }
])

// 表格列配置
const rankingColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 60,
    render: (row: any) => h('span', { class: `rank-${row.rank}` }, row.rank)
  },
  {
    title: '群组名称',
    key: 'name'
  },
  {
    title: '数值',
    key: 'value',
    render: (row: any) => h('span', { class: 'value-text' }, row.value)
  },
  {
    title: '变化',
    key: 'change',
    render: (row: any) => {
      const isPositive = row.change.startsWith('+')
      return h('span', {
        class: isPositive ? 'change-positive' : 'change-negative'
      }, row.change)
    }
  }
]

const growthColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 60,
    render: (row: any) => h('span', { class: `rank-${row.rank}` }, row.rank)
  },
  {
    title: '群组名称',
    key: 'name'
  },
  {
    title: '增长率',
    key: 'growth',
    render: (row: any) => h('span', { class: 'growth-text' }, `${row.growth}%`)
  },
  {
    title: '成员数',
    key: 'members'
  }
]

// 方法
const handleConfigChange = () => {
  // 配置变化时重新分析
  refreshAnalysis()
}

const refreshAnalysis = async () => {
  loading.value = true
  try {
    // 模拟分析数据刷新
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('分析数据已刷新')
    // 重新初始化图表
    nextTick(() => {
      initCharts()
    })
  } catch (error) {
    message.error('刷新分析数据失败')
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  message.info('导出分析报告功能开发中')
}

// 初始化图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['活跃群组', '总群组']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '活跃群组',
        type: 'line',
        data: [45, 52, 61, 58, 67, 73, 89],
        smooth: true,
        itemStyle: { color: '#18a058' }
      },
      {
        name: '总群组',
        type: 'line',
        data: [78, 85, 92, 98, 108, 125, 156],
        smooth: true,
        itemStyle: { color: '#2080f0' }
      }
    ]
  }
  chart.setOption(option)
}

const initTypeChart = () => {
  if (!typeChartRef.value) return
  
  const chart = echarts.init(typeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '70%',
      data: [
        { value: 89, name: '普通群' },
        { value: 45, name: '企业群' },
        { value: 22, name: '客服群' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

const initSizeChart = () => {
  if (!sizeChartRef.value) return
  
  const chart = echarts.init(sizeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['<50', '50-100', '100-200', '200-300', '>300']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      type: 'bar',
      data: [23, 45, 56, 23, 9],
      itemStyle: { color: '#f0a020' }
    }]
  }
  chart.setOption(option)
}

const initActivityChart = () => {
  if (!activityChartRef.value) return
  
  const chart = echarts.init(activityChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '消息数',
      type: 'bar',
      data: [
        12, 8, 5, 3, 2, 5, 15, 25, 35, 45, 55, 65,
        75, 85, 95, 105, 115, 125, 135, 145, 125, 85, 45, 25
      ],
      itemStyle: { color: '#2080f0' }
    }]
  }
  chart.setOption(option)
}

const initCharts = () => {
  initTrendChart()
  initTypeChart()
  initSizeChart()
  initActivityChart()
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.group-analysis {
  padding: 16px;
}

.config-card {
  margin-bottom: 24px;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  align-items: end;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.overview-card {
  margin-bottom: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.metric-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.metric-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-container {
  height: 400px;
}

.chart-container.small {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.activity-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.activity-chart {
  height: 300px;
}

.activity-stats {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-stats h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.stat-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  font-weight: 500;
  color: #1a1a1a;
}

.health-assessment {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

.health-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.health-score {
  display: flex;
  justify-content: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #18a058, #36ad6a);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.score-number {
  font-size: 32px;
  font-weight: 600;
}

.score-label {
  font-size: 12px;
  opacity: 0.9;
}

.health-factors {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.factor-item {
  display: grid;
  grid-template-columns: 80px 1fr 50px;
  gap: 12px;
  align-items: center;
}

.factor-label {
  font-size: 14px;
  color: #666;
}

.factor-value {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  text-align: right;
}

.health-suggestions {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.health-suggestions h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.suggestion-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.rank-1 {
  color: #f0a020;
  font-weight: 600;
}

.rank-2 {
  color: #999;
  font-weight: 600;
}

.rank-3 {
  color: #cd7f32;
  font-weight: 600;
}

.value-text {
  font-weight: 500;
}

.growth-text {
  color: #18a058;
  font-weight: 500;
}

.change-positive {
  color: #18a058;
  font-weight: 500;
}

.change-negative {
  color: #d03050;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
  margin-top: 24px;
}
</style>