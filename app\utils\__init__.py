"""工具模块初始化文件

导出所有工具函数、异常类和常用工具
"""

# 数据库工具函数
from .db_utils import (
    get_user_statistics,
    get_department_statistics,
    get_customer_statistics,
    search_entities,
    batch_update_customer_status,
    get_follow_up_reminders,
    cleanup_old_records,
    generate_report_data,
    generate_uuid,
    format_phone,
    validate_email
)

# 异常处理
from .exceptions import (
    # 异常类
    BusinessException,
    ValidationException,
    AuthenticationException,
    AuthorizationException,
    NotFoundException,
    ConflictException,
    DatabaseException,
    
    # 异常处理函数
    business_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    sqlalchemy_exception_handler,
    general_exception_handler,
    setup_exception_handlers
)

__all__ = [
    # 数据库工具函数
    "get_user_statistics",
    "get_department_statistics",
    "get_customer_statistics",
    "search_entities",
    "batch_update_customer_status",
    "get_follow_up_reminders",
    "cleanup_old_records",
    "generate_report_data",
    "generate_uuid",
    "format_phone",
    "validate_email",
    
    # 异常类
    "BusinessException",
    "ValidationException",
    "AuthenticationException",
    "AuthorizationException",
    "NotFoundException",
    "ConflictException",
    "DatabaseException",
    
    # 异常处理函数
    "business_exception_handler",
    "http_exception_handler",
    "validation_exception_handler",
    "sqlalchemy_exception_handler",
    "general_exception_handler",
    "setup_exception_handlers"
]