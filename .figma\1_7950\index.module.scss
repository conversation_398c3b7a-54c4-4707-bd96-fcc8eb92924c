* {
  box-sizing: border-box;
}

.myCards2 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #ffffff;
  padding-top: 15px;
  width: 375px;
  height: 812px;
  overflow: hidden;

  .group42 {
    width: 42px;
    height: 42px;
  }

  .aIPhoneXsBarsStatusD {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    margin-right: 10px;
    margin-left: 25px;
    min-width: 341px;
    height: 17px;

    .aTime {
      width: 30px;
      height: 17px;
      text-align: center;
      line-height: 18px;
      letter-spacing: 0;
      color: #1e1e2d;
      font-family: "SF Pro Display", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
      font-weight: 600;
    }

    .container {
      width: 67px;
      height: 12px;
    }
  }

  .tabBar {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    margin: 23px 20px 0px;
    min-width: 335px;
    height: 42px;

    .myCards {
      width: 86px;
      height: 20px;
      text-align: center;
      line-height: 18px;
      letter-spacing: 0;
      color: #1e1e2d;
      font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .autoWrapper4 {
    position: relative;
    margin-top: 32px;
    margin-left: 20px;
    width: 545px;
    height: 450px;

    .monthlySpendingLimit2 {
      display: flex;
      position: absolute;
      bottom: -130px;
      left: 0;
      flex-direction: column;
      align-items: flex-start;
      width: 335px;
      height: 152px;

      .monthlySpendingLimit {
        width: 208px;
        height: 20px;
        line-height: 18px;
        letter-spacing: 0;
        color: #1e1e2d;
        font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 18px;
        font-weight: 500;
      }

      .rectangle39 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        margin-top: 19px;
        border-radius: 18px;
        background: #f4f4f4;
        padding: 23px 24px;

        .amount854500 {
          width: 121px;
          height: 15px;
          line-height: 13px;
          letter-spacing: 0;
          color: #1e1e2d;
          font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
          font-size: 13px;
        }

        .group76 {
          position: relative;
          margin-top: 11px;
          width: 287px;
          height: 16px;

          .rectangle40 {
            display: flex;
            position: absolute;
            top: 5px;
            left: 0;
            align-items: center;
            border-radius: 30px;
            background: #ffffff;
            padding-right: 188px;
            width: 287px;
            height: 7px;

            .rectangle41 {
              border-radius: 30px;
              background: #0066ff;
              width: 99px;
              height: 7px;
            }
          }

          .ellipse6 {
            display: flex;
            position: absolute;
            top: 0;
            left: 91px;
            align-items: center;
            border-radius: 50%;
            background: #0066ff;
            padding: 4px;
            width: 16px;
            height: 16px;

            .ellipse7 {
              border-radius: 50%;
              background: #ffffff;
              width: 9px;
              height: 9px;
            }
          }
        }

        .group77 {
          display: flex;
          align-items: center;
          align-self: stretch;
          margin-top: 11px;

          .a0 {
            width: 15px;
            height: 14px;
            line-height: 12px;
            letter-spacing: 0;
            color: #a2a2a7;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 12px;
          }

          .a4600 {
            margin: 0px 0px 0px 64px;
            width: 41px;
            height: 14px;
            line-height: 12px;
            letter-spacing: 0;
            color: #1e1e2d;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 12px;
          }

          .a10000 {
            margin: 0px 0px 0px 123px;
            width: 44px;
            height: 14px;
            line-height: 12px;
            letter-spacing: 0;
            color: #a2a2a7;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 12px;
          }
        }
      }
    }

    .card {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      flex-direction: column;
      align-items: flex-start;
      padding: 1px 210px 51px 0px;
      width: 545px;
      height: 450px;

      .rectangle2 {
        display: flex;
        align-items: center;
        border: 1px solid #2a2c3c;
        border-radius: 25px;
        background: #25253d;
        height: 199px;

        .worldmap2X1 {
          opacity: 0.16;
          width: 335px;
          height: 199px;
        }
      }

      .lastTransaction {
        display: flex;
        flex-direction: column;
        align-items: center;
        align-self: stretch;
        margin-top: 30px;

        .group33 {
          display: flex;
          align-items: center;
          align-self: stretch;

          .group28 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 16px;

            .appleStore {
              width: 93px;
              height: 18px;
              line-height: 16px;
              letter-spacing: 0;
              color: #1e1e2d;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 16px;
              font-weight: 500;
            }

            .entertainment {
              margin: 6px 0px 0px;
              width: 86px;
              height: 14px;
              line-height: 12px;
              letter-spacing: 0;
              color: #a2a2a7;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 12px;
            }
          }

          .a599 {
            margin: 0px 0px 0px 125px;
            width: 59px;
            height: 18px;
            line-height: 16px;
            letter-spacing: 0;
            color: #1e1e2d;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 16px;
            font-weight: 500;
          }
        }

        .group34 {
          display: flex;
          align-items: center;
          align-self: stretch;
          margin-top: 22px;

          .group29 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 16px;

            .spotify {
              width: 56px;
              height: 18px;
              line-height: 16px;
              letter-spacing: 0;
              color: #1e1e2d;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 16px;
              font-weight: 500;
            }

            .music {
              margin: 6px 0px 0px;
              width: 35px;
              height: 14px;
              line-height: 12px;
              letter-spacing: 0;
              color: #a2a2a7;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 12px;
            }
          }

          .a1299 {
            margin: 0px 0px 0px 158px;
            width: 63px;
            height: 18px;
            line-height: 16px;
            letter-spacing: 0;
            color: #1e1e2d;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 16px;
            font-weight: 500;
          }
        }

        .group36 {
          display: flex;
          align-items: center;
          align-self: stretch;
          margin-top: 22px;

          .group30 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 16px;

            .grocery {
              width: 64px;
              height: 18px;
              line-height: 16px;
              letter-spacing: 0;
              color: #1e1e2d;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 16px;
              font-weight: 500;
            }

            .shopping {
              margin: 6px 0px 0px;
              width: 58px;
              height: 14px;
              line-height: 12px;
              letter-spacing: 0;
              color: #a2a2a7;
              font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 12px;
            }
          }

          .a88 {
            margin: 0px 0px 0px 164px;
            width: 49px;
            height: 18px;
            line-height: 16px;
            letter-spacing: 0;
            color: #1e1e2d;
            font-family: Poppins, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }

      .rectangle22 {
        display: flex;
        position: absolute;
        top: 0;
        left: 20px;
        flex-direction: column;
        align-items: flex-start;
        padding: 22px 224px 255px 0px;
        width: 525px;
        height: 450px;

        .group1000000882 {
          width: 29px;
          height: 25px;
        }

        .autoWrapper {
          position: relative;
          margin-top: 26px;
          width: 301px;
          height: 58px;

          .a4562112245957852 {
            position: absolute;
            top: 0;
            left: 0;
            width: 301px;
            height: 58px;
            letter-spacing: 0;
            color: #ffffff;
            font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 24px;
          }

          .aRJonson {
            position: absolute;
            bottom: -15px;
            left: 0;
            width: 117px;
            height: 32px;
            letter-spacing: 0;
            color: #ffffff;
            font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
            font-size: 13px;
          }
        }

        .autoWrapper2 {
          display: flex;
          position: relative;
          align-items: flex-start;
          align-self: stretch;
          margin-top: 6px;
          padding-right: 2px;
          padding-left: 0;

          .group9 {
            position: relative;
            margin-top: 9px;
            width: 73px;
            height: 31px;

            .expiryDate {
              position: absolute;
              top: 0;
              left: 0;
              width: 73px;
              height: 22px;
              letter-spacing: 0;
              color: #a2a2a7;
              font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 9px;
            }

            .a242000 {
              position: absolute;
              top: 15px;
              left: 0;
              width: 53px;
              height: 16px;
              letter-spacing: 0;
              color: #ffffff;
              font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 13px;
            }
          }

          .group1000000883 {
            position: relative;
            margin-top: 9px;
            margin-left: 10px;
            width: 63px;
            height: 47px;

            .cVv {
              position: absolute;
              top: 0;
              left: 0;
              width: 53px;
              height: 22px;
              letter-spacing: 0;
              color: #a2a2a7;
              font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 9px;
            }

            .a6986 {
              position: absolute;
              top: 15px;
              left: 0;
              width: 63px;
              height: 32px;
              letter-spacing: 0;
              color: #ffffff;
              font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 13px;
            }
          }

          .ellipse1 {
            position: absolute;
            right: -140px;
            bottom: -210px;
            opacity: 0.2;
            border-radius: 50%;
            width: 270px;
            height: 361px;
            rotate: -41deg;
            background-image: linear-gradient(
              91.69deg,
              #0066ff 2.1%,
              #0018f1 89.59%
            );
            backdrop-filter: blur(25px);
          }

          .group3 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            z-index: 1;
            margin-left: 24px;

            .group2 {
              position: relative;
              margin-left: 77px;
              width: 36px;
              height: 20px;

              .ellipse2 {
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 50%;
                background: #ea001b;
                width: 20px;
                height: 20px;
              }

              .ellipse3 {
                position: absolute;
                top: 0;
                left: 16px;
                border-radius: 50%;
                background: #f79f1a;
                width: 20px;
                height: 20px;
              }
            }

            .mastercard {
              margin: 6px 0px 0px;
              width: 129px;
              height: 32px;
              text-align: right;
              letter-spacing: 0;
              color: #ffffff;
              font-family: Inter, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
              font-size: 13px;
            }
          }
        }

        .autoWrapper3 {
          position: absolute;
          top: 24px;
          left: 279px;
          width: 16px;
          height: 21px;

          .union {
            position: absolute;
            top: 6px;
            left: 0;
            width: 3px;
            height: 8px;
          }

          .union2 {
            position: absolute;
            top: 0;
            left: 12px;
            width: 4px;
            height: 21px;
          }

          .union3 {
            position: absolute;
            top: 2px;
            left: 8px;
            width: 4px;
            height: 17px;
          }

          .union4 {
            position: absolute;
            top: 4px;
            left: 4px;
            width: 3px;
            height: 13px;
          }
        }
      }
    }
  }

  .group17 {
    margin-top: 148px;
    width: 375px;
    height: 86px;
  }
}
