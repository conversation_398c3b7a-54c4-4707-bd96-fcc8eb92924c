from typing import Any, Dict, Optional
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
import logging
from datetime import datetime

from .response import ResponseFormatter


logger = logging.getLogger(__name__)


class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(
        self,
        message: str = "业务处理失败",
        code: int = 400,
        data: Any = None
    ):
        self.message = message
        self.code = code
        self.data = data
        super().__init__(self.message)


class ValidationException(BusinessException):
    """验证异常"""
    
    def __init__(self, message: str = "参数验证失败", errors: Any = None):
        super().__init__(message=message, code=422, data=errors)


class AuthenticationException(BusinessException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(message=message, code=401)


class AuthorizationException(BusinessException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(message=message, code=403)


class NotFoundException(BusinessException):
    """资源不存在异常"""
    
    def __init__(self, message: str = "资源不存在"):
        super().__init__(message=message, code=404)


class ConflictException(BusinessException):
    """冲突异常"""
    
    def __init__(self, message: str = "资源冲突"):
        super().__init__(message=message, code=409)


class DatabaseException(BusinessException):
    """数据库异常"""
    
    def __init__(self, message: str = "数据库操作失败"):
        super().__init__(message=message, code=500)


async def business_exception_handler(
    request: Request, 
    exc: BusinessException
) -> JSONResponse:
    """业务异常处理器"""
    logger.warning(
        f"Business exception: {exc.message} - Path: {request.url.path} - Code: {exc.code}"
    )
    
    return ResponseFormatter.error(
        message=exc.message,
        code=exc.code,
        data=exc.data
    )


async def http_exception_handler(
    request: Request, 
    exc: HTTPException
) -> JSONResponse:
    """HTTP异常处理器"""
    logger.warning(
        f"HTTP exception: {exc.detail} - Path: {request.url.path} - Status: {exc.status_code}"
    )
    
    return ResponseFormatter.error(
        message=str(exc.detail),
        code=exc.status_code
    )


async def validation_exception_handler(
    request: Request, 
    exc: RequestValidationError
) -> JSONResponse:
    """请求验证异常处理器"""
    logger.warning(
        f"Validation error: {exc.errors()} - Path: {request.url.path}"
    )
    
    # 格式化验证错误信息
    errors = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"][1:])  # 跳过 'body'
        errors.append({
            "field": field,
            "message": error["msg"],
            "type": error["type"]
        })
    
    return ResponseFormatter.validation_error(
        errors=errors,
        message="请求参数验证失败"
    )


async def sqlalchemy_exception_handler(
    request: Request, 
    exc: SQLAlchemyError
) -> JSONResponse:
    """SQLAlchemy异常处理器"""
    logger.error(
        f"Database error: {str(exc)} - Path: {request.url.path}",
        exc_info=True
    )
    
    # 处理完整性约束错误
    if isinstance(exc, IntegrityError):
        if "UNIQUE constraint failed" in str(exc.orig):
            return ResponseFormatter.error(
                message="数据已存在，请检查唯一性约束",
                code=409
            )
        elif "FOREIGN KEY constraint failed" in str(exc.orig):
            return ResponseFormatter.error(
                message="关联数据不存在，请检查外键约束",
                code=400
            )
    
    return ResponseFormatter.server_error(
        message="数据库操作失败"
    )


async def general_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """通用异常处理器"""
    logger.error(
        f"Unexpected error: {str(exc)} - Path: {request.url.path}",
        exc_info=True
    )
    
    return ResponseFormatter.server_error(
        message="服务器内部错误"
    )


def setup_exception_handlers(app):
    """设置异常处理器"""
    
    # 业务异常
    app.add_exception_handler(BusinessException, business_exception_handler)
    
    # HTTP异常
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # 验证异常
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 数据库异常
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    
    # 通用异常
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers setup completed")