<template>
  <div class="site-list">
    <n-card title="工地管理" :bordered="false">
      <template #header-extra>
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增工地
        </n-button>
      </template>

      <!-- 搜索栏 -->
      <n-space class="mb-4" justify="space-between">
        <n-space>
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索工地名称、地址"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="工地状态"
            clearable
            style="width: 150px"
            :options="statusOptions"
          />
        </n-space>
        <n-button @click="handleSearch" type="primary" ghost>
          <template #icon>
            <n-icon><SearchOutline /></n-icon>
          </template>
          搜索
        </n-button>
      </n-space>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="工地信息">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="工地名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入工地名称" />
        </n-form-item>
        <n-form-item label="工地地址" path="address">
          <n-input v-model:value="formData.address" placeholder="请输入工地地址" />
        </n-form-item>
        <n-form-item label="负责人" path="manager">
          <n-input v-model:value="formData.manager" placeholder="请输入负责人" />
        </n-form-item>
        <n-form-item label="联系电话" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入联系电话" />
        </n-form-item>
        <n-form-item label="工地状态" path="status">
          <n-select
            v-model:value="formData.status"
            placeholder="请选择工地状态"
            :options="statusOptions"
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import {
  NCard, NButton, NSpace, NInput, NSelect, NDataTable, NModal,
  NForm, NFormItem, NIcon, useMessage, useDialog
} from 'naive-ui'
import { AddOutline, SearchOutline, LocationOutline } from '@vicons/ionicons5'
import ActionButtons from '@/components/common/ActionButtons.vue'
import { useRouter } from 'vue-router'

interface SiteRecord {
  id: string
  name: string
  address: string
  manager: string
  phone: string
  status: string
  remark?: string
  createdAt: string
  updatedAt: string
}

const router = useRouter()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const submitting = ref(false)
const showModal = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')

const statusOptions = [
  { label: '规划中', value: 'planning' },
  { label: '施工中', value: 'construction' },
  { label: '已完工', value: 'completed' },
  { label: '暂停', value: 'paused' }
]

const formData = reactive({
  id: '',
  name: '',
  address: '',
  manager: '',
  phone: '',
  status: '',
  remark: ''
})

const formRules = {
  name: { required: true, message: '请输入工地名称', trigger: 'blur' },
  address: { required: true, message: '请输入工地地址', trigger: 'blur' },
  manager: { required: true, message: '请输入负责人', trigger: 'blur' },
  phone: { required: true, message: '请输入联系电话', trigger: 'blur' },
  status: { required: true, message: '请选择工地状态', trigger: 'change' }
}

const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const tableData = ref<SiteRecord[]>([])

const columns = [
  { title: '工地名称', key: 'name', width: 200 },
  { title: '工地地址', key: 'address', width: 300 },
  { title: '负责人', key: 'manager', width: 120 },
  { title: '联系电话', key: 'phone', width: 150 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: SiteRecord) => {
      const statusMap: Record<string, { text: string; type: string }> = {
        planning: { text: '规划中', type: 'info' },
        construction: { text: '施工中', type: 'warning' },
        completed: { text: '已完工', type: 'success' },
        paused: { text: '暂停', type: 'error' }
      }
      const status = statusMap[row.status]
      return h('span', { class: `status-${status?.type}` }, status?.text || row.status)
    }
  },
  { title: '创建时间', key: 'createdAt', width: 180 },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row: SiteRecord) => {
      return h(ActionButtons, {
        onView: () => handleView(row),
        onEdit: () => handleEdit(row),
        onDelete: () => handleDelete(row)
      })
    }
  }
]

const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData: SiteRecord[] = [
      {
        id: '1',
        name: '阳光花园小区',
        address: '北京市朝阳区建国路88号',
        manager: '张三',
        phone: '13800138001',
        status: 'construction',
        remark: '高端住宅项目',
        createdAt: '2024-01-15 10:30:00',
        updatedAt: '2024-01-20 14:20:00'
      },
      {
        id: '2',
        name: '商业广场',
        address: '上海市浦东新区陆家嘴金融区',
        manager: '李四',
        phone: '13800138002',
        status: 'planning',
        remark: '大型商业综合体',
        createdAt: '2024-01-10 09:15:00',
        updatedAt: '2024-01-18 16:45:00'
      }
    ]
    
    tableData.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    address: '',
    manager: '',
    phone: '',
    status: '',
    remark: ''
  })
  showModal.value = true
}

const handleEdit = (row: SiteRecord) => {
  Object.assign(formData, row)
  showModal.value = true
}

const handleView = (row: SiteRecord) => {
  router.push(`/sites/detail/${row.id}`)
}

const handleDelete = (row: SiteRecord) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除工地"${row.name}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 这里调用删除API
        message.success('删除成功')
        await loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    // 这里调用保存API
    message.success(formData.id ? '更新成功' : '创建成功')
    showModal.value = false
    await loadData()
  } catch (error) {
    message.error('保存失败')
  } finally {
    submitting.value = false
  }
}

const handleSearch = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.site-list {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.status-info {
  color: #2080f0;
  background: #f0f9ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-warning {
  color: #f0a020;
  background: #fffbeb;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-success {
  color: #18a058;
  background: #f0fff4;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-error {
  color: #d03050;
  background: #fef2f2;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}
</style>