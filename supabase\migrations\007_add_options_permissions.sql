-- 添加选项管理权限
INSERT INTO permissions (name, display_name, description, module, action, resource) VALUES
('settings:options', '选项管理', '管理系统选项数据', 'settings', 'manage', 'options'),
('settings:options:read', '查看选项', '查看选项分类和数据', 'settings', 'read', 'options'),
('settings:options:create', '创建选项', '创建选项分类和数据', 'settings', 'create', 'options'),
('settings:options:update', '编辑选项', '编辑选项分类和数据', 'settings', 'update', 'options'),
('settings:options:delete', '删除选项', '删除选项分类和数据', 'settings', 'delete', 'options');

-- 为超级管理员角色分配选项管理权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'super_admin'
AND p.name IN (
  'settings:options',
  'settings:options:read',
  'settings:options:create', 
  'settings:options:update',
  'settings:options:delete'
);

-- 为销售经理角色分配选项查看权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'sales_manager'
AND p.name IN ('settings:options:read');

-- 更新序列值
SELECT setval('permissions_id_seq', (SELECT MAX(id) FROM permissions));
SELECT setval('role_permissions_id_seq', (SELECT MAX(id) FROM role_permissions));