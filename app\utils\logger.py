import logging
import logging.config
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json
from pythonjsonlogger import jsonlogger

from app.config import settings


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """自定义JSON日志格式化器"""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]):
        super().add_fields(log_record, record, message_dict)
        
        # 添加时间戳
        log_record['timestamp'] = datetime.utcnow().isoformat()
        
        # 添加日志级别
        log_record['level'] = record.levelname
        
        # 添加模块信息
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # 添加应用信息
        log_record['app'] = settings.app.app_name
        log_record['environment'] = settings.app.app_environment


class RequestContextFilter(logging.Filter):
    """请求上下文过滤器"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        # 可以在这里添加请求ID、用户ID等上下文信息
        # 这些信息通常从请求中间件或上下文变量中获取
        record.request_id = getattr(record, 'request_id', None)
        record.user_id = getattr(record, 'user_id', None)
        return True


def get_logging_config() -> Dict[str, Any]:
    """获取日志配置"""
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'detailed': {
                'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s(): %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'json': {
                '()': CustomJsonFormatter,
                'format': '%(timestamp)s %(level)s %(name)s %(message)s'
            }
        },
        'filters': {
            'request_context': {
                '()': RequestContextFilter
            }
        },
        'handlers': {
            'console': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'standard',
                'stream': sys.stdout,
                'filters': ['request_context']
            },
            'file': {
                'level': 'INFO',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'detailed',
                'filename': 'logs/app.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf-8',
                'filters': ['request_context']
            },
            'error_file': {
                'level': 'ERROR',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'detailed',
                'filename': 'logs/error.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf-8',
                'filters': ['request_context']
            },
            'json_file': {
                'level': 'INFO',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'json',
                'filename': 'logs/app.json',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf-8',
                'filters': ['request_context']
            }
        },
        'loggers': {
            'app': {
                'level': settings.app.app_log_level,
                'handlers': ['console', 'file', 'error_file'],
                'propagate': False
            },
            'uvicorn': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False
            },
            'sqlalchemy': {
                'level': 'WARNING',
                'handlers': ['file'],
                'propagate': False
            },
            'sqlalchemy.engine': {
                'level': 'INFO' if settings.app.app_debug else 'WARNING',
                'handlers': ['file'],
                'propagate': False
            }
        },
        'root': {
            'level': settings.app.app_log_level,
            'handlers': ['console', 'file', 'error_file']
        }
    }
    
    # 在生产环境中添加JSON日志
    if settings.app.app_environment == 'production':
        config['loggers']['app']['handlers'].append('json_file')
        config['root']['handlers'].append('json_file')
    
    return config


def setup_logging():
    """设置日志配置"""
    config = get_logging_config()
    logging.config.dictConfig(config)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    logger = logging.getLogger('app')
    logger.info(f"日志系统初始化完成 - 级别: {settings.app.app_log_level}")


class Logger:
    """日志工具类"""
    
    def __init__(self, name: str = 'app'):
        self.logger = logging.getLogger(name)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self._log('debug', message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self._log('info', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log('warning', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self._log('error', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self._log('critical', message, **kwargs)
    
    def _log(self, level: str, message: str, **kwargs):
        """内部日志方法"""
        extra = {}
        
        # 添加额外的上下文信息
        if kwargs:
            extra.update(kwargs)
        
        getattr(self.logger, level)(message, extra=extra)
    
    def log_request(self, method: str, path: str, status_code: int, duration: float, **kwargs):
        """记录请求日志"""
        self.info(
            f"{method} {path} - {status_code} - {duration:.3f}s",
            method=method,
            path=path,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_database_operation(self, operation: str, table: str, duration: float, **kwargs):
        """记录数据库操作日志"""
        self.info(
            f"DB {operation} on {table} - {duration:.3f}s",
            operation=operation,
            table=table,
            duration=duration,
            **kwargs
        )
    
    def log_business_event(self, event: str, user_id: Optional[str] = None, **kwargs):
        """记录业务事件日志"""
        self.info(
            f"Business event: {event}",
            event=event,
            user_id=user_id,
            **kwargs
        )
    
    def log_security_event(self, event: str, user_id: Optional[str] = None, ip: Optional[str] = None, **kwargs):
        """记录安全事件日志"""
        self.warning(
            f"Security event: {event}",
            event=event,
            user_id=user_id,
            ip=ip,
            **kwargs
        )


# 全局日志实例
logger = Logger('app')


# 便捷函数
def get_logger(name: str = 'app') -> Logger:
    """获取日志实例"""
    return Logger(name)