{"migrationId": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "timestamp": "2025-08-18T07:10:29.747Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:09:38.159Z", "errors": [], "endTime": "2025-08-18T07:09:43.331Z", "duration": 5172}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:09:43.337Z", "errors": [], "endTime": "2025-08-18T07:09:45.936Z", "duration": 2599}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:09:45.939Z", "errors": [], "endTime": "2025-08-18T07:09:49.527Z", "duration": 3588}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:09:49.530Z", "errors": [], "endTime": "2025-08-18T07:09:51.843Z", "duration": 2313}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:09:51.846Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:09:52.274Z", "errors": [], "endTime": "2025-08-18T07:09:53.807Z", "duration": 1533}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:09:53.810Z", "errors": [], "endTime": "2025-08-18T07:09:57.021Z", "duration": 3211}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:09:57.024Z", "errors": [], "endTime": "2025-08-18T07:09:59.541Z", "duration": 2517}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:09:59.544Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:10:00.114Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:10:01.936Z", "duration": 1822}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:01.940Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:02.202Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:10:03.840Z", "errors": ["Unknown column 'meeting_time' in 'field list'"], "endTime": "2025-08-18T07:10:08.124Z", "duration": 4284}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:08.127Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:10:08.545Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:10:11.469Z", "duration": 2924}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:11.471Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:11.993Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:12.930Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:10:13.614Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:10:14.397Z", "errors": ["Unknown column 'created_by' in 'field list'"], "endTime": "2025-08-18T07:10:26.520Z", "duration": 12123}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:10:26.522Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:10:29.745Z", "duration": 3223}], "logs": [{"id": "8418f377-f1bf-49fc-839b-b403ac2f0226", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:09:38.159Z", "end_time": "2025-08-18T07:09:43.331Z", "duration_ms": 5172}, {"id": "cee30a0d-8f5e-46e2-aab0-57ce96ec65df", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:09:43.337Z", "end_time": "2025-08-18T07:09:45.936Z", "duration_ms": 2599}, {"id": "2326dfc8-9d0d-4c9e-9d52-09d13c44fbbe", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:09:45.939Z", "end_time": "2025-08-18T07:09:49.527Z", "duration_ms": 3588}, {"id": "6f53d91d-1d64-472c-bc1b-08c7deda68de", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:09:49.530Z", "end_time": "2025-08-18T07:09:51.843Z", "duration_ms": 2313}, {"id": "ffc776de-e206-4137-b75c-eb60b19e7ffd", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:09:51.846Z", "end_time": "2025-08-18T07:09:52.274Z", "duration_ms": 428}, {"id": "36d4da4b-e415-44fd-b619-264690bc2552", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:09:52.274Z", "end_time": "2025-08-18T07:09:53.807Z", "duration_ms": 1533}, {"id": "79a8220b-10f2-4a11-8648-cde89cf85bcb", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:09:53.810Z", "end_time": "2025-08-18T07:09:57.021Z", "duration_ms": 3211}, {"id": "146f98a3-0260-44a7-af32-fb6022a82f05", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:09:57.024Z", "end_time": "2025-08-18T07:09:59.541Z", "duration_ms": 2517}, {"id": "6b039c2f-82eb-4e79-b32d-07f975778fca", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:09:59.544Z", "end_time": "2025-08-18T07:10:00.114Z", "duration_ms": 570}, {"id": "1317fb45-78af-4006-89cf-6abb9a772938", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:00.115Z", "end_time": "2025-08-18T07:10:01.936Z", "duration_ms": 1821}, {"id": "1742c17b-f3fb-49b7-ae7e-fbc84ccd3445", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:01.940Z", "end_time": "2025-08-18T07:10:02.202Z", "duration_ms": 262}, {"id": "6d1ad74d-ad53-4f56-b561-c420e48e48da", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:02.202Z", "end_time": "2025-08-18T07:10:03.839Z", "duration_ms": 1637}, {"id": "694651f5-57c6-452d-8aa2-b972b012a7e7", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:03.840Z", "end_time": "2025-08-18T07:10:08.124Z", "duration_ms": 4284}, {"id": "a1aedd8e-ab78-4325-bdf4-0de53a132b69", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:08.127Z", "end_time": "2025-08-18T07:10:08.544Z", "duration_ms": 417}, {"id": "1ea22afd-4854-408d-a20a-ebfe49add252", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:08.545Z", "end_time": "2025-08-18T07:10:11.469Z", "duration_ms": 2924}, {"id": "fcea76ba-b4e0-4380-b1ca-3dedf695665d", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:11.471Z", "end_time": "2025-08-18T07:10:11.993Z", "duration_ms": 522}, {"id": "1cc495b0-62e4-4e52-bfe6-3da7b9bc5b64", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:11.993Z", "end_time": "2025-08-18T07:10:12.929Z", "duration_ms": 936}, {"id": "0910eed5-6d1a-4168-a361-f38a4bbd3d83", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:12.930Z", "end_time": "2025-08-18T07:10:13.614Z", "duration_ms": 684}, {"id": "1f155bac-8714-4b6f-9d30-5cc4582b3144", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:13.614Z", "end_time": "2025-08-18T07:10:14.397Z", "duration_ms": 783}, {"id": "b1c2d5f9-c511-4ab3-ae3f-2cdd6160a582", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:14.397Z", "end_time": "2025-08-18T07:10:26.520Z", "duration_ms": 12123}, {"id": "e9b9299f-2ae8-45ec-a687-e23632067b4c", "migration_id": "c6ff3c3f-a166-46d6-81af-ba66a69dc270", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:10:26.522Z", "end_time": "2025-08-18T07:10:29.745Z", "duration_ms": 3223}]}