<template>
  <div class="points-management">
    <div class="page-header">
      <h1>积分管理</h1>
      <p>管理积分商城的产品和积分规则</p>
    </div>
    
    <div class="content-container">
      <n-card title="积分商城产品管理" class="mb-4">
        <template #header-extra>
          <n-button type="primary" @click="showAddModal = true">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            添加产品
          </n-button>
        </template>
        
        <n-data-table
          :columns="columns"
          :data="pointsStore.products"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row) => row.id"
        />
      </n-card>
      
      <!-- 添加产品模态框 -->
      <n-modal v-model:show="showAddModal" preset="dialog" title="添加积分商品">
        <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="100px">
          <n-form-item label="商品名称" path="name">
            <n-input v-model:value="formData.name" placeholder="请输入商品名称" />
          </n-form-item>
          <n-form-item label="所需积分" path="points">
            <n-input-number v-model:value="formData.points" placeholder="请输入所需积分" :min="1" />
          </n-form-item>
          <n-form-item label="商品描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入商品描述"
              :rows="3"
            />
          </n-form-item>
          <n-form-item label="库存数量" path="stock">
            <n-input-number v-model:value="formData.stock" placeholder="请输入库存数量" :min="0" />
          </n-form-item>
          <n-form-item label="状态" path="status">
            <n-select
              v-model:value="formData.status"
              :options="statusOptions"
              placeholder="请选择状态"
            />
          </n-form-item>
        </n-form>
        
        <template #action>
          <n-space>
            <n-button @click="showAddModal = false">取消</n-button>
            <n-button type="primary" @click="handleSubmit">确定</n-button>
          </n-space>
        </template>
      </n-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NSpace,
  NTag,
  useMessage
} from 'naive-ui'
import { AddOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'
import { usePointsStore, type Product } from '@/stores/modules/points'

const message = useMessage()
const pointsStore = usePointsStore()
const loading = ref(false)
const showAddModal = ref(false)
const formRef = ref()
const editingId = ref<number | null>(null)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 表格列配置
const columns: DataTableColumns<Product> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '商品名称',
    key: 'name',
    width: 150
  },
  {
    title: '所需积分',
    key: 'points',
    width: 120,
    render(row) {
      return h(NTag, { type: 'info' }, { default: () => `${row.points}积分` })
    }
  },
  {
    title: '商品描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '库存',
    key: 'stock',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(
        NTag,
        {
          type: row.status === 'active' ? 'success' : 'warning'
        },
        {
          default: () => row.status === 'active' ? '启用' : '禁用'
        }
      )
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h(NSpace, null, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              secondary: true,
              onClick: () => handleEdit(row)
            },
            { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'error',
              secondary: true,
              onClick: () => handleDelete(row.id)
            },
            { default: () => '删除', icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
          )
        ]
      })
    }
  }
]

// 表单数据
const formData = reactive({
  name: '',
  points: null as number | null,
  description: '',
  stock: null as number | null,
  status: 'active' as 'active' | 'inactive'
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'inactive' }
]

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入商品名称',
    trigger: ['input', 'blur']
  },
  points: {
    required: true,
    type: 'number' as const,
    message: '请输入所需积分',
    trigger: ['input', 'blur']
  },
  description: {
    required: true,
    message: '请输入商品描述',
    trigger: ['input', 'blur']
  },
  stock: {
    required: true,
    type: 'number' as const,
    message: '请输入库存数量',
    trigger: ['input', 'blur']
  }
}

// 处理编辑
const handleEdit = (row: Product) => {
  Object.assign(formData, row)
  editingId.value = row.id
  showAddModal.value = true
}

// 处理删除
const handleDelete = (id: number) => {
  if (pointsStore.deleteProduct(id)) {
    message.success('删除成功')
  } else {
    message.error('删除失败')
  }
}

// 处理提交
const handleSubmit = () => {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (editingId.value) {
        // 编辑模式
        const result = pointsStore.updateProduct(editingId.value, {
          name: formData.name,
          points: formData.points!,
          description: formData.description,
          stock: formData.stock!,
          status: formData.status
        })
        if (result) {
          message.success('更新成功')
        } else {
          message.error('更新失败')
        }
      } else {
        // 新增模式
        pointsStore.addProduct({
          name: formData.name,
          points: formData.points!,
          description: formData.description,
          stock: formData.stock!,
          status: formData.status
        })
        message.success('保存成功')
      }
      
      showAddModal.value = false
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    points: null,
    description: '',
    stock: null,
    status: 'active'
  })
  editingId.value = null
}

// 组件挂载时加载数据
onMounted(() => {
  // 这里可以调用API加载数据
})
</script>

<style scoped>
.points-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.content-container {
  background: #ffffff;
  border-radius: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>