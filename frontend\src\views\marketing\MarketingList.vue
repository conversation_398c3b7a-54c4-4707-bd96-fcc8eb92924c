<template>
    <!-- 页面头部 -->
    <PageHeader :title="currentPageTitle" description="管理和查看营销活动" title-row>
      <template #actions>
        <n-button type="primary" @click="createCampaign">
          <template #icon>
            <CreateIcon />
          </template>
          创建活动
        </n-button>
      </template>
    </PageHeader>

    <!-- 筛选器 -->
    <div class="filters">
      <n-space>
        <n-input
          v-model:value="filters.search"
          placeholder="搜索活动名称、描述"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="filters.type"
          placeholder="活动类型"
          clearable
          style="width: 150px"
          :options="typeOptions"
        />
        
        <n-select
          v-model:value="filters.status"
          placeholder="活动状态"
          clearable
          style="width: 150px"
          :options="statusOptions"
        />
        
        <n-date-picker
          v-model:value="filters.dateRange"
          type="daterange"
          placeholder="活动时间"
          clearable
        />
        
        <n-button type="default" @click="resetFilters">重置</n-button>
        <n-button type="primary" @click="loadCampaigns">搜索</n-button>
      </n-space>
    </div>

    <!-- 活动列表 -->
    <n-data-table
      :columns="columns"
      :data="campaigns"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: Campaign) => row.id"
    />

    <!-- 创建/编辑活动弹窗 -->
    <CampaignFormModal
      v-model:show="showCreateModal"
      :campaign="editingCampaign"
      @refresh="loadCampaigns"
      @close="handleModalClose"
    />

    <!-- 活动详情弹窗 -->
    <CampaignDetailModal
      v-model:show="showDetailModal"
      :campaign="selectedCampaign"
    />

    <!-- 参与者列表弹窗 -->
    <ParticipantsModal
      v-model:show="showParticipantsModal"
      :campaign="selectedCampaign"
    />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import { SearchOutline as SearchIcon, AddOutline as AddIcon, PlayOutline as StartIcon, PauseOutline, StopOutline as StopIcon, TrashOutline as DeleteIcon, EyeOutline as ViewIcon, PeopleOutline as ParticipantsIcon, CreateOutline as EditIcon } from '@vicons/ionicons5'
import { useMarketingStore } from '@/stores/marketingStore'
import type { DataTableColumns } from 'naive-ui'
import PageHeader from '@/components/common/PageHeader.vue'
import CampaignFormModal from './components/CampaignFormModal.vue'
import CampaignDetailModal from './components/CampaignDetailModal.vue'
import ParticipantsModal from './components/ParticipantsModal.vue'

interface Campaign {
  id: number
  name: string
  type: string
  description?: string
  status: string
  start_time: string
  end_time: string
  target_audience?: string
  budget?: number
  participants_count?: number
  conversion_rate?: number
  created_at: string
  updated_at: string
  config: any
}

const router = useRouter()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()
const marketingStore = useMarketingStore()

// 计算属性 - 获取当前页面标题
const currentPageTitle = computed(() => {
  return route.meta.title as string || '营销活动'
})

// 预创建图标组件，避免在render函数中重复创建
const ViewIconComponent = markRaw(ViewIcon)
const EditIconComponent = markRaw(EditIcon)
const ParticipantsIconComponent = markRaw(ParticipantsIcon)
const StartIconComponent = markRaw(StartIcon)
const StopIconComponent = markRaw(StopIcon)
const DeleteIconComponent = markRaw(DeleteIcon)

// 使用Store的状态
const loading = computed(() => marketingStore.loading)
const campaigns = computed(() => marketingStore.campaigns)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showParticipantsModal = ref(false)
const editingCampaign = ref<Campaign | null>(null)
const selectedCampaign = ref<Campaign | null>(null)

const filters = reactive({
  search: '',
  type: null as string | null,
  status: null as string | null,
  dateRange: null as [number, number] | null
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page
    loadCampaigns()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadCampaigns()
  }
})

const typeOptions = [
  { label: '抽奖活动', value: 'lottery' },
  { label: '大转盘', value: 'wheel' },
  { label: '秒杀活动', value: 'flash_sale' },
  { label: '限时折扣', value: 'time_discount' },
  { label: '拼团活动', value: 'group_buy' },
  { label: '转发分享', value: 'share' },
  { label: '优惠券', value: 'coupon' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '进行中', value: 'active' },
  { label: '已暂停', value: 'paused' },
  { label: '已结束', value: 'ended' },
  { label: '已取消', value: 'cancelled' }
]

const columns: DataTableColumns<Campaign> = [
  {
    title: '活动名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '活动类型',
    key: 'type',
    width: 120,
    render(row) {
      const typeMap: Record<string, { text: string; type: string }> = {
        lottery: { text: '抽奖活动', type: 'info' },
        wheel: { text: '大转盘', type: 'primary' },
        flash_sale: { text: '秒杀活动', type: 'error' },
        time_discount: { text: '限时折扣', type: 'warning' },
        group_buy: { text: '拼团活动', type: 'success' },
        share: { text: '转发分享', type: 'success' },
        coupon: { text: '优惠券', type: 'warning' },
        other: { text: '其他', type: 'default' }
      }
      const type = typeMap[row.type] || { text: row.type, type: 'default' }
      return h('n-tag', { type: type.type }, { default: () => type.text })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap: Record<string, { text: string; type: string }> = {
        draft: { text: '草稿', type: 'default' },
        active: { text: '进行中', type: 'success' },
        paused: { text: '已暂停', type: 'warning' },
        ended: { text: '已结束', type: 'info' },
        cancelled: { text: '已取消', type: 'error' }
      }
      const status = statusMap[row.status] || { text: row.status, type: 'default' }
      return h('n-tag', { type: status.type }, { default: () => status.text })
    }
  },
  {
    title: '开始时间',
    key: 'start_time',
    width: 150,
    render(row) {
      return new Date(row.start_time).toLocaleString()
    }
  },
  {
    title: '结束时间',
    key: 'end_time',
    width: 150,
    render(row) {
      return new Date(row.end_time).toLocaleString()
    }
  },
  {
    title: '参与人数',
    key: 'participants_count',
    width: 100,
    render(row) {
      return row.participants_count || 0
    }
  },
  {
    title: '转化率',
    key: 'conversion_rate',
    width: 100,
    render(row) {
      return row.conversion_rate ? `${(row.conversion_rate * 100).toFixed(1)}%` : '-'
    }
  },
  {
    title: '预算',
    key: 'budget',
    width: 120,
    render(row) {
      return row.budget ? `¥${row.budget.toLocaleString()}` : '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h('n-space', {}, {
        default: () => [
          h('n-button', {
            size: 'small',
            onClick: () => viewCampaign(row)
          }, {
            default: () => '查看',
            icon: () => h(ViewIconComponent)
          }),
          h('n-button', {
            size: 'small',
            onClick: () => editCampaign(row)
          }, {
            default: () => '编辑',
            icon: () => h(EditIconComponent)
          }),
          h('n-button', {
            size: 'small',
            onClick: () => viewParticipants(row)
          }, {
            default: () => '参与者',
            icon: () => h(ParticipantsIconComponent)
          }),
          row.status === 'draft' || row.status === 'paused' ? h('n-button', {
            size: 'small',
            type: 'success',
            onClick: () => startCampaign(row)
          }, {
            default: () => '启动',
            icon: () => h(StartIconComponent)
          }) : null,
          row.status === 'active' ? h('n-button', {
            size: 'small',
            type: 'warning',
            onClick: () => pauseCampaign(row)
          }, {
            default: () => '暂停',
            icon: () => h(StopIconComponent)
          }) : null,
          h('n-button', {
            size: 'small',
            type: 'error',
            onClick: () => deleteCampaign(row)
          }, {
            default: () => '删除',
            icon: () => h(DeleteIconComponent)
          })
        ].filter(Boolean)
      })
    }
  }
]

const loadCampaigns = async () => {
  try {
    const params: any = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: filters.search,
      type: filters.type || undefined,
      status: filters.status || undefined
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }
    
    await marketingStore.fetchCampaigns(params)
    pagination.itemCount = marketingStore.total
  } catch (error) {
    console.error('Load campaigns error:', error)
  }
}

const viewCampaign = (campaign: Campaign) => {
  selectedCampaign.value = campaign
  showDetailModal.value = true
}

const createCampaign = () => {
  router.push('/marketing/create')
}

const editCampaign = (campaign: Campaign) => {
  router.push(`/marketing/edit/${campaign.id}`)
}

const viewParticipants = (campaign: Campaign) => {
  selectedCampaign.value = campaign
  showParticipantsModal.value = true
}

const startCampaign = async (campaign: Campaign) => {
  try {
    await marketingStore.startCampaign(campaign.id)
    loadCampaigns()
  } catch (error) {
    console.error('Start campaign error:', error)
  }
}

const pauseCampaign = async (campaign: Campaign) => {
  try {
    await marketingStore.pauseCampaign(campaign.id)
    loadCampaigns()
  } catch (error) {
    console.error('Pause campaign error:', error)
  }
}

const deleteCampaign = (campaign: Campaign) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除活动"${campaign.name}"吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await marketingStore.deleteCampaign(campaign.id)
        loadCampaigns()
      } catch (error) {
        console.error('Delete campaign error:', error)
      }
    }
  })
}

const handleModalClose = () => {
  editingCampaign.value = null
  showCreateModal.value = false
}

const resetFilters = () => {
  filters.search = ''
  filters.type = null
  filters.status = null
  filters.dateRange = null
  loadCampaigns()
}

onMounted(() => {
  loadCampaigns()
})
</script>

<style scoped>

.filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}
</style>