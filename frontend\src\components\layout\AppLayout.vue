<template>
  <n-config-provider :theme="isDarkMode ? darkTheme : null">
    <n-layout has-sider>
      <!-- 侧边栏 -->
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="64"
        :width="240"
        :collapsed="sidebarCollapsed"
        show-trigger
        @collapse="handleSidebarCollapse"
        @expand="handleSidebarExpand"
      >
        <SideMenu />
      </n-layout-sider>
      
      <!-- 主内容区域 -->
      <n-layout>
        <!-- 顶部导航 -->
        <n-layout-header bordered>
          <AppHeader />
        </n-layout-header>
        
        <!-- 内容区域 -->
        <n-layout-content class="main-content">
          <div class="content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content">
              <n-spin :show="pageLoading">
                <router-view v-slot="{ Component }">
                  <transition name="fade" mode="out-in">
                    <component :is="Component" />
                  </transition>
                </router-view>
              </n-spin>
            </div>
          </div>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </n-config-provider>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  NLayout,
  NLayoutSider,
  NLayoutHeader,
  NLayoutContent,
  NConfigProvider,
  NSpin,
  darkTheme
} from 'naive-ui'
import { useAppStore, useAuthStore, useOptionsStore } from '@/stores'
import SideMenu from './SideMenu.vue'
import AppHeader from './AppHeader.vue'

const route = useRoute()
const appStore = useAppStore()
const authStore = useAuthStore()
const optionsStore = useOptionsStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const isDarkMode = computed(() => appStore.isDarkMode)
const pageLoading = computed(() => appStore.pageLoading)
// 侧边栏折叠处理
const handleSidebarCollapse = () => {
  appStore.toggleSidebar()
  appStore.saveSidebarState()
}

const handleSidebarExpand = () => {
  appStore.toggleSidebar()
  appStore.saveSidebarState()
}

// 组件挂载时初始化
onMounted(async () => {
  // 初始化应用设置
  appStore.initApp()
  
  // 初始化认证状态
  await authStore.initAuth()
  
  // 如果用户已认证，初始化选项数据
  if (authStore.isAuthenticated) {
    await optionsStore.initializeOptions()
  }
})
</script>

<style scoped>
.main-content {
  height: calc(100vh - 64px); /* 只减去header高度 */
  overflow: hidden;
  background: #ffffff;
}

.content-wrapper {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
  box-sizing: border-box;
  background: #ffffff;
}

.page-content {
  height: 100%;
  min-height: 0;
  background: #ffffff;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>