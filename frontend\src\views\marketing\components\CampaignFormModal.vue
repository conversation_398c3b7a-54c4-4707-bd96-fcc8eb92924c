<template>
  <n-modal :show="show" @update:show="$emit('update:show', $event)">
    <n-card
      style="width: 800px; max-height: 80vh; overflow-y: auto"
      :title="campaign ? '编辑活动' : '创建活动'"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="120px"
      >
        <!-- 基本信息 -->
        <n-divider title-placement="left">基本信息</n-divider>
        
        <n-form-item label="活动名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入活动名称" />
        </n-form-item>
        
        <n-form-item label="活动类型" path="type">
          <n-select
            v-model:value="formData.type"
            :options="typeOptions"
            placeholder="请选择活动类型"
            @update:value="handleTypeChange"
          />
        </n-form-item>

        <n-form-item label="展现样式" path="display_style">
          <n-select
            v-model:value="formData.config.display_style"
            :options="displayStyleOptions"
            placeholder="请选择展现样式"
          />
        </n-form-item>
        
        <n-form-item label="活动描述">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动描述"
          />
        </n-form-item>
        
        <n-form-item label="活动时间" path="timeRange">
          <n-date-picker
            v-model:value="timeRange"
            type="datetimerange"
            placeholder="选择活动开始和结束时间"
            style="width: 100%"
            @update:value="handleTimeRangeChange"
          />
        </n-form-item>
        
        <n-form-item label="目标受众">
          <n-input v-model:value="formData.target_audience" placeholder="请输入目标受众描述" />
        </n-form-item>
        
        <n-form-item label="活动预算">
          <n-input-number
            v-model:value="formData.budget"
            :min="0"
            :precision="2"
            placeholder="请输入活动预算"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </n-input-number>
        </n-form-item>

        <!-- 活动配置 -->
        <n-divider title-placement="left">活动配置</n-divider>
        
        <!-- 抽奖活动配置 -->
        <template v-if="formData.type === 'lottery'">
          <n-form-item label="奖品设置">
            <div class="prizes-config">
              <div v-for="(prize, index) in formData.config.prizes" :key="index" class="prize-item">
                <n-space>
                  <n-input v-model:value="prize.name" placeholder="奖品名称" style="width: 150px" />
                  <n-input-number v-model:value="prize.quantity" :min="1" placeholder="数量" style="width: 100px" />
                  <n-input-number v-model:value="prize.probability" :min="0" :max="100" :precision="2" placeholder="中奖率%" style="width: 120px" />
                  <n-button @click="removePrize(index)" type="error" size="small">
                    <template #icon>
                      <DeleteIcon />
                    </template>
                  </n-button>
                </n-space>
              </div>
              <n-button @click="addPrize" dashed style="width: 100%; margin-top: 8px">
                <template #icon>
                  <AddIcon />
                </template>
                添加奖品
              </n-button>
            </div>
          </n-form-item>
          
          <n-form-item label="参与条件">
            <n-checkbox-group v-model:value="formData.config.participation_rules">
              <n-space>
                <n-checkbox value="follow">关注账号</n-checkbox>
                <n-checkbox value="share">分享活动</n-checkbox>
                <n-checkbox value="comment">评论互动</n-checkbox>
                <n-checkbox value="invite">邀请好友</n-checkbox>
              </n-space>
            </n-checkbox-group>
          </n-form-item>
          
          <n-form-item label="每人限制">
            <n-input-number v-model:value="formData.config.max_attempts_per_user" :min="1" placeholder="每人最多参与次数" style="width: 100%" />
          </n-form-item>
        </template>

        <!-- 秒杀活动配置 -->
        <template v-if="formData.type === 'flash_sale'">
          <n-form-item label="商品信息">
            <n-space vertical style="width: 100%">
              <n-input v-model:value="formData.config.product_name" placeholder="商品名称" />
              <n-input-number v-model:value="formData.config.original_price" :min="0" :precision="2" placeholder="原价" style="width: 100%">
                <template #prefix>¥</template>
              </n-input-number>
              <n-input-number v-model:value="formData.config.sale_price" :min="0" :precision="2" placeholder="秒杀价" style="width: 100%">
                <template #prefix>¥</template>
              </n-input-number>
              <n-input-number v-model:value="formData.config.stock_quantity" :min="1" placeholder="库存数量" style="width: 100%" />
            </n-space>
          </n-form-item>
          
          <n-form-item label="限购设置">
            <n-input-number v-model:value="formData.config.max_purchase_per_user" :min="1" placeholder="每人限购数量" style="width: 100%" />
          </n-form-item>
        </template>

        <!-- 转发分享配置 -->
        <template v-if="formData.type === 'share'">
          <n-form-item label="分享内容">
            <n-input
              v-model:value="formData.config.share_content"
              type="textarea"
              :rows="3"
              placeholder="请输入分享文案"
            />
          </n-form-item>
          
          <n-form-item label="分享奖励">
            <n-space vertical style="width: 100%">
              <n-input v-model:value="formData.config.reward_type" placeholder="奖励类型（如：积分、优惠券等）" />
              <n-input-number v-model:value="formData.config.reward_amount" :min="0" placeholder="奖励数量" style="width: 100%" />
            </n-space>
          </n-form-item>
          
          <n-form-item label="分享目标">
            <n-input-number v-model:value="formData.config.target_shares" :min="1" placeholder="目标分享次数" style="width: 100%" />
          </n-form-item>
        </template>

        <!-- 优惠券配置 -->
        <template v-if="formData.type === 'coupon'">
          <n-form-item label="优惠券类型">
            <n-radio-group v-model:value="formData.config.coupon_type">
              <n-space>
                <n-radio value="discount">折扣券</n-radio>
                <n-radio value="cash">现金券</n-radio>
                <n-radio value="free_shipping">包邮券</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          
          <n-form-item label="优惠金额">
            <n-input-number
              v-model:value="formData.config.discount_amount"
              :min="0"
              :precision="2"
              :placeholder="formData.config.coupon_type === 'discount' ? '折扣（如：0.8表示8折）' : '优惠金额'"
              style="width: 100%"
            >
              <template #prefix v-if="formData.config.coupon_type !== 'discount'">¥</template>
            </n-input-number>
          </n-form-item>
          
          <n-form-item label="使用门槛">
            <n-input-number v-model:value="formData.config.min_order_amount" :min="0" :precision="2" placeholder="最低消费金额" style="width: 100%">
              <template #prefix>¥</template>
            </n-input-number>
          </n-form-item>
          
          <n-form-item label="发放数量">
            <n-input-number v-model:value="formData.config.total_quantity" :min="1" placeholder="优惠券总数量" style="width: 100%" />
          </n-form-item>
        </template>

        <!-- 积分活动配置 -->
        <template v-if="formData.type === 'points'">
          <n-form-item label="积分规则">
            <div class="points-rules">
              <div v-for="(rule, index) in formData.config.point_rules" :key="index" class="rule-item">
                <n-space>
                  <n-input v-model:value="rule.action" placeholder="行为（如：签到、购买等）" style="width: 150px" />
                  <n-input-number v-model:value="rule.points" :min="1" placeholder="获得积分" style="width: 120px" />
                  <n-input-number v-model:value="rule.daily_limit" :min="0" placeholder="每日限制" style="width: 120px" />
                  <n-button @click="removePointRule(index)" type="error" size="small">
                    <template #icon>
                      <DeleteIcon />
                    </template>
                  </n-button>
                </n-space>
              </div>
              <n-button @click="addPointRule" dashed style="width: 100%; margin-top: 8px">
                <template #icon>
                  <AddIcon />
                </template>
                添加规则
              </n-button>
            </div>
          </n-form-item>
        </template>

        <!-- 大转盘配置 -->
        <template v-if="formData.type === 'wheel'">
          <n-form-item label="转盘奖品">
            <div class="prizes-config">
              <div v-for="(prize, index) in formData.config.prizes" :key="index" class="prize-item">
                <n-space>
                  <n-input
                    v-model:value="prize.name"
                    placeholder="奖品名称"
                    style="width: 150px"
                  />
                  <n-input-number
                    v-model:value="prize.quantity"
                    placeholder="数量"
                    :min="1"
                    style="width: 80px"
                  />
                  <n-input-number
                    v-model:value="prize.probability"
                    placeholder="中奖率(%)"
                    :min="0"
                    :max="100"
                    style="width: 100px"
                  />
                  <n-input
                    v-model:value="prize.color"
                    placeholder="扇形颜色"
                    style="width: 100px"
                  />
                  <n-button @click="removePrize(index)" type="error" size="small">
                    <template #icon>
                      <DeleteIcon />
                    </template>
                  </n-button>
                </n-space>
              </div>
              <n-button @click="addPrize" dashed style="width: 100%; margin-top: 8px">
                <template #icon>
                  <AddIcon />
                </template>
                添加奖品
              </n-button>
            </div>
          </n-form-item>
          <n-form-item label="每人限制次数">
            <n-input-number
              v-model:value="formData.config.max_attempts_per_user"
              placeholder="每人最多抽奖次数"
              :min="1"
            />
          </n-form-item>
          <n-form-item label="转盘样式">
            <n-select
              v-model:value="formData.config.wheel_style"
              :options="[
                { label: '经典样式', value: 'classic' },
                { label: '现代样式', value: 'modern' },
                { label: '炫彩样式', value: 'colorful' }
              ]"
              placeholder="选择转盘样式"
            />
          </n-form-item>
        </template>

        <!-- 限时折扣配置 -->
        <template v-if="formData.type === 'time_discount'">
          <n-form-item label="商品名称">
            <n-input v-model:value="formData.config.product_name" placeholder="请输入商品名称" />
          </n-form-item>
          <n-form-item label="原价">
            <n-input-number
              v-model:value="formData.config.original_price"
              placeholder="原价"
              :min="0"
              :precision="2"
            />
          </n-form-item>
          <n-form-item label="折扣类型">
            <n-radio-group v-model:value="formData.config.discount_type">
              <n-radio value="percentage">百分比折扣</n-radio>
              <n-radio value="fixed">固定金额减免</n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="formData.config.discount_type === 'percentage' ? '折扣率(%)' : '减免金额'">
            <n-input-number
              v-model:value="formData.config.discount_value"
              :placeholder="formData.config.discount_type === 'percentage' ? '折扣率' : '减免金额'"
              :min="0"
              :max="formData.config.discount_type === 'percentage' ? 100 : undefined"
              :precision="2"
            />
          </n-form-item>
          <n-form-item label="库存数量">
            <n-input-number
              v-model:value="formData.config.stock_quantity"
              placeholder="库存数量"
              :min="1"
            />
          </n-form-item>
        </template>

        <!-- 满减活动配置 -->
        <template v-if="formData.type === 'full_reduction'">
          <n-form-item label="满减规则">
            <div class="reduction-rules">
              <div v-for="(rule, index) in formData.config.reduction_rules" :key="index" class="rule-item">
                <n-space>
                  <n-input-number
                    v-model:value="rule.min_amount"
                    placeholder="满金额"
                    :min="0"
                    :precision="2"
                    style="width: 120px"
                  />
                  <span>减</span>
                  <n-input-number
                    v-model:value="rule.reduction_amount"
                    placeholder="减金额"
                    :min="0"
                    :precision="2"
                    style="width: 120px"
                  />
                  <n-button @click="removeReductionRule(index)" type="error" size="small">
                    <template #icon>
                      <DeleteIcon />
                    </template>
                  </n-button>
                </n-space>
              </div>
              <n-button @click="addReductionRule" dashed style="width: 100%; margin-top: 8px">
                <template #icon>
                  <AddIcon />
                </template>
                添加规则
              </n-button>
            </div>
          </n-form-item>
          <n-form-item label="是否可叠加">
            <n-switch v-model:value="formData.config.stackable" />
          </n-form-item>
        </template>

        <!-- 拼团活动配置 -->
        <template v-if="formData.type === 'group_buy'">
          <n-form-item label="商品名称">
            <n-input v-model:value="formData.config.product_name" placeholder="请输入商品名称" />
          </n-form-item>
          <n-form-item label="原价">
            <n-input-number
              v-model:value="formData.config.original_price"
              placeholder="原价"
              :min="0"
              :precision="2"
            />
          </n-form-item>
          <n-form-item label="团购价">
            <n-input-number
              v-model:value="formData.config.group_price"
              placeholder="团购价"
              :min="0"
              :precision="2"
            />
          </n-form-item>
          <n-form-item label="成团人数">
            <n-input-number
              v-model:value="formData.config.min_group_size"
              placeholder="最少成团人数"
              :min="2"
            />
          </n-form-item>
          <n-form-item label="拼团时限(小时)">
            <n-input-number
              v-model:value="formData.config.group_time_limit"
              placeholder="拼团时限"
              :min="1"
            />
          </n-form-item>
          <n-form-item label="库存数量">
            <n-input-number
              v-model:value="formData.config.stock_quantity"
              placeholder="库存数量"
              :min="1"
            />
          </n-form-item>
        </template>

        <!-- 积分兑换配置 -->
        <template v-if="formData.type === 'point_exchange'">
          <n-form-item label="兑换商品">
            <div class="exchange-items">
              <div v-for="(item, index) in formData.config.exchange_items" :key="index" class="exchange-item">
                <n-space>
                  <n-input
                    v-model:value="item.name"
                    placeholder="商品名称"
                    style="width: 150px"
                  />
                  <n-input-number
                    v-model:value="item.points_required"
                    placeholder="所需积分"
                    :min="1"
                    style="width: 120px"
                  />
                  <n-input-number
                    v-model:value="item.stock"
                    placeholder="库存"
                    :min="1"
                    style="width: 80px"
                  />
                  <n-input
                    v-model:value="item.image_url"
                    placeholder="商品图片URL"
                    style="width: 200px"
                  />
                  <n-button @click="removeExchangeItem(index)" type="error" size="small">
                    <template #icon>
                      <DeleteIcon />
                    </template>
                  </n-button>
                </n-space>
              </div>
              <n-button @click="addExchangeItem" dashed style="width: 100%; margin-top: 8px">
                <template #icon>
                  <AddIcon />
                </template>
                添加商品
              </n-button>
            </div>
          </n-form-item>
          <n-form-item label="每人限兑">
            <n-input-number
              v-model:value="formData.config.max_exchange_per_user"
              placeholder="每人最多兑换次数"
              :min="1"
            />
          </n-form-item>
        </template>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="$emit('close')">取消</n-button>
          <n-button type="primary" @click="saveCampaign" :loading="saving">
            {{ campaign ? '更新' : '创建' }}
          </n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { AddOutline as AddIcon, TrashOutline as DeleteIcon } from '@vicons/ionicons5'
import type { FormInst } from 'naive-ui'
import { useMarketingStore } from '@/stores/marketingStore'

interface Campaign {
  id?: number
  name: string
  type: string
  description?: string
  status: string
  start_time: string
  end_time: string
  target_audience?: string
  budget?: number
  config: any
}

interface Props {
  show: boolean
  campaign?: Campaign | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'refresh'): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const marketingStore = useMarketingStore()
const formRef = ref<FormInst | null>(null)

const saving = ref(false)
const timeRange = ref<[number, number] | null>(null)

const formData = reactive<Campaign>({
  name: '',
  type: '',
  description: '',
  start_time: '',
  end_time: '',
  target_audience: '',
  budget: undefined,
  config: {
    display_style: 'modal'
  },
  status: 'draft'
})

const typeOptions = [
  { label: '抽奖活动', value: 'lottery' },
  { label: '大转盘', value: 'wheel' },
  { label: '秒杀活动', value: 'flash_sale' },
  { label: '限时折扣', value: 'time_discount' },
  { label: '拼团活动', value: 'group_buy' },
  { label: '转发分享', value: 'share' },
  { label: '优惠券', value: 'coupon' },
  { label: '其他', value: 'other' }
]

const displayStyleOptions = [
  { label: '弹窗样式', value: 'modal' },
  { label: '页面嵌入', value: 'embed' },
  { label: '浮动按钮', value: 'float' },
  { label: '横幅展示', value: 'banner' },
  { label: '卡片样式', value: 'card' }
]

const formRules = {
  name: {
    required: true,
    message: '请输入活动名称',
    trigger: 'blur'
  },
  type: {
    required: true,
    message: '请选择活动类型',
    trigger: 'change'
  },
  timeRange: {
    required: true,
    message: '请选择活动时间',
    trigger: 'change'
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.campaign) {
    Object.assign(formData, props.campaign)
    if (props.campaign.start_time && props.campaign.end_time) {
      timeRange.value = [
        new Date(props.campaign.start_time).getTime(),
        new Date(props.campaign.end_time).getTime()
      ]
    }
  } else {
    resetForm()
  }
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    description: '',
    start_time: '',
    end_time: '',
    target_audience: '',
    budget: undefined,
    config: {
      display_style: 'modal'
    },
    status: 'draft'
  })
  timeRange.value = null
}

const handleTypeChange = (type: string) => {
  // 保存当前的展现样式
  const currentDisplayStyle = formData.config.display_style || 'modal'
  
  // 根据活动类型初始化配置
  switch (type) {
    case 'lottery':
      formData.config = {
        display_style: currentDisplayStyle,
        prizes: [{ name: '', quantity: 1, probability: 10 }],
        participation_rules: [],
        max_attempts_per_user: 1
      }
      break
    case 'wheel':
      formData.config = {
        display_style: currentDisplayStyle,
        prizes: [{ name: '', quantity: 1, probability: 10, color: '#ff6b6b' }],
        max_attempts_per_user: 1,
        wheel_style: 'classic'
      }
      break
    case 'flash_sale':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        sale_price: 0,
        stock_quantity: 1,
        max_purchase_per_user: 1
      }
      break
    case 'time_discount':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        discount_type: 'percentage',
        discount_value: 0,
        stock_quantity: 1
      }
      break
    case 'group_buy':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        group_price: 0,
        min_group_size: 2,
        group_time_limit: 24,
        stock_quantity: 1
      }
      break
    case 'share':
      formData.config = {
        display_style: currentDisplayStyle,
        share_content: '',
        reward_type: '',
        reward_amount: 0,
        target_shares: 100
      }
      break
    case 'coupon':
      formData.config = {
        display_style: currentDisplayStyle,
        coupon_type: 'discount',
        discount_amount: 0,
        min_order_amount: 0,
        total_quantity: 100
      }
      break
    default:
      formData.config = {
        display_style: currentDisplayStyle
      }
  }
}

const handleTimeRangeChange = (value: [number, number] | null) => {
  if (value) {
    formData.start_time = new Date(value[0]).toISOString()
    formData.end_time = new Date(value[1]).toISOString()
  } else {
    formData.start_time = ''
    formData.end_time = ''
  }
}

// 奖品管理
const addPrize = () => {
  formData.config.prizes.push({ name: '', quantity: 1, probability: 10 })
}

const removePrize = (index: number) => {
  formData.config.prizes.splice(index, 1)
}

// 积分规则管理
const addPointRule = () => {
  formData.config.point_rules.push({ action: '', points: 10, daily_limit: 1 })
}

const removePointRule = (index: number) => {
  formData.config.point_rules.splice(index, 1)
}

// 满减规则管理
const addReductionRule = () => {
  formData.config.reduction_rules.push({ min_amount: 100, reduction_amount: 10 })
}

const removeReductionRule = (index: number) => {
  formData.config.reduction_rules.splice(index, 1)
}

// 兑换商品管理
const addExchangeItem = () => {
  formData.config.exchange_items.push({ name: '', points_required: 100, stock: 1, image_url: '' })
}

const removeExchangeItem = (index: number) => {
  formData.config.exchange_items.splice(index, 1)
}

const saveCampaign = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    if (props.campaign) {
      await marketingStore.updateCampaign(props.campaign.id!, formData)
      message.success('更新活动成功')
    } else {
      await marketingStore.createCampaign(formData)
      message.success('创建活动成功')
    }
    
    emit('refresh')
    emit('close')
  } catch (error) {
    if (error instanceof Error) {
      message.error(props.campaign ? '更新活动失败' : '创建活动失败')
      console.error('Save campaign error:', error)
    }
  } finally {
    saving.value = false
  }
}

watch(() => props.show, (show) => {
  if (show) {
    initFormData()
  }
})

watch(() => props.campaign, () => {
  if (props.show) {
    initFormData()
  }
})
</script>

<style scoped>
.prizes-config,
.points-rules,
.reduction-rules,
.exchange-items {
  width: 100%;
}

.prize-item,
.rule-item,
.exchange-item {
  margin-bottom: 8px;
}
</style>