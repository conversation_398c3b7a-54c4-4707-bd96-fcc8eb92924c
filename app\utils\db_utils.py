"""数据库操作工具函数

提供常用的数据库操作、统计和工具函数
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import uuid4

from sqlalchemy import and_, or_, func, text, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import SQLAlchemyError

from app.models.user import User
from app.models.department import Department
from app.models.customer import Customer
from app.models.follow import CustomerFollowRecord, Meeting
from app.services.database_manager import db_manager, handle_db_errors

logger = logging.getLogger(__name__)


@handle_db_errors
async def get_user_statistics(db: AsyncSession, user_id: str) -> Dict[str, Any]:
    """
    获取用户统计信息
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户统计信息字典
    """
    # 客户数量统计
    customer_count_result = await db.execute(
        select(func.count(Customer.id))
        .where(Customer.owner_id == user_id, Customer.is_deleted == False)
    )
    customer_count = customer_count_result.scalar()
    
    # 跟进记录数量统计
    follow_count_result = await db.execute(
        select(func.count(CustomerFollowRecord.id))
        .where(CustomerFollowRecord.user_id == user_id)
    )
    follow_count = follow_count_result.scalar()
    
    # 会议数量统计
    meeting_count_result = await db.execute(
        select(func.count(Meeting.id))
        .where(Meeting.organizer_id == user_id)
    )
    meeting_count = meeting_count_result.scalar()
    
    # 本月新增客户数量
    current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_customer_count_result = await db.execute(
        select(func.count(Customer.id))
        .where(
            Customer.owner_id == user_id,
            Customer.is_deleted == False,
            Customer.created_at >= current_month_start
        )
    )
    new_customer_count = new_customer_count_result.scalar()
    
    return {
        "user_id": user_id,
        "customer_count": customer_count,
        "follow_count": follow_count,
        "meeting_count": meeting_count,
        "new_customer_count": new_customer_count,
        "statistics_date": datetime.now()
    }


@handle_db_errors
async def get_department_statistics(db: AsyncSession, department_id: str) -> Dict[str, Any]:
    """
    获取部门统计信息
    
    Args:
        db: 数据库会话
        department_id: 部门ID
        
    Returns:
        部门统计信息字典
    """
    # 部门用户数量
    user_count_result = await db.execute(
        select(func.count(User.id))
        .where(User.department_id == department_id, User.is_active == True)
    )
    user_count = user_count_result.scalar()
    
    # 部门客户数量
    customer_count_result = await db.execute(
        select(func.count(Customer.id))
        .where(Customer.department_id == department_id, Customer.is_deleted == False)
    )
    customer_count = customer_count_result.scalar()
    
    # 部门跟进记录数量（本月）
    current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    follow_count_result = await db.execute(
        select(func.count(CustomerFollowRecord.id))
        .join(User, CustomerFollowRecord.user_id == User.id)
        .where(
            User.department_id == department_id,
            CustomerFollowRecord.follow_time >= current_month_start
        )
    )
    follow_count = follow_count_result.scalar()
    
    return {
        "department_id": department_id,
        "user_count": user_count,
        "customer_count": customer_count,
        "follow_count": follow_count,
        "statistics_date": datetime.now()
    }


@handle_db_errors
async def get_customer_statistics(
    db: AsyncSession,
    *,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    获取客户统计信息
    
    Args:
        db: 数据库会话
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        客户统计信息字典
    """
    if not start_date:
        start_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    if not end_date:
        end_date = datetime.now()
    
    # 总客户数量
    total_count_result = await db.execute(
        select(func.count(Customer.id))
        .where(Customer.is_deleted == False)
    )
    total_count = total_count_result.scalar()
    
    # 新增客户数量
    new_count_result = await db.execute(
        select(func.count(Customer.id))
        .where(
            Customer.is_deleted == False,
            Customer.created_at >= start_date,
            Customer.created_at <= end_date
        )
    )
    new_count = new_count_result.scalar()
    
    # 按状态统计
    status_stats_result = await db.execute(
        select(Customer.status, func.count(Customer.id))
        .where(Customer.is_deleted == False)
        .group_by(Customer.status)
    )
    status_stats = {status: count for status, count in status_stats_result.fetchall()}
    
    # 按等级统计
    level_stats_result = await db.execute(
        select(Customer.level, func.count(Customer.id))
        .where(Customer.is_deleted == False)
        .group_by(Customer.level)
    )
    level_stats = {level: count for level, count in level_stats_result.fetchall()}
    
    return {
        "total_count": total_count,
        "new_count": new_count,
        "status_stats": status_stats,
        "level_stats": level_stats,
        "start_date": start_date,
        "end_date": end_date,
        "statistics_date": datetime.now()
    }


@handle_db_errors
async def search_entities(
    db: AsyncSession,
    *,
    keyword: str,
    entity_types: List[str] = None,
    limit: int = 50
) -> Dict[str, List[Dict[str, Any]]]:
    """
    全局搜索实体
    
    Args:
        db: 数据库会话
        keyword: 搜索关键词
        entity_types: 实体类型列表 ['user', 'customer', 'department']
        limit: 每种类型的限制数量
        
    Returns:
        搜索结果字典
    """
    if not entity_types:
        entity_types = ['user', 'customer', 'department']
    
    results = {}
    search_pattern = f"%{keyword}%"
    
    # 搜索用户
    if 'user' in entity_types:
        user_result = await db.execute(
            select(User.id, User.username, User.real_name, User.email, User.phone)
            .where(
                and_(
                    User.is_active == True,
                    or_(
                        User.username.like(search_pattern),
                        User.real_name.like(search_pattern),
                        User.email.like(search_pattern),
                        User.phone.like(search_pattern)
                    )
                )
            )
            .limit(limit)
        )
        results['users'] = [
            {
                "id": row.id,
                "username": row.username,
                "real_name": row.real_name,
                "email": row.email,
                "phone": row.phone,
                "type": "user"
            }
            for row in user_result.fetchall()
        ]
    
    # 搜索客户
    if 'customer' in entity_types:
        customer_result = await db.execute(
            select(Customer.id, Customer.name, Customer.phone, Customer.email, Customer.company)
            .where(
                and_(
                    Customer.is_deleted == False,
                    or_(
                        Customer.name.like(search_pattern),
                        Customer.phone.like(search_pattern),
                        Customer.email.like(search_pattern),
                        Customer.company.like(search_pattern)
                    )
                )
            )
            .limit(limit)
        )
        results['customers'] = [
            {
                "id": row.id,
                "name": row.name,
                "phone": row.phone,
                "email": row.email,
                "company": row.company,
                "type": "customer"
            }
            for row in customer_result.fetchall()
        ]
    
    # 搜索部门
    if 'department' in entity_types:
        department_result = await db.execute(
            select(Department.id, Department.name, Department.code, Department.description)
            .where(
                and_(
                    Department.is_active == True,
                    or_(
                        Department.name.like(search_pattern),
                        Department.code.like(search_pattern),
                        Department.description.like(search_pattern)
                    )
                )
            )
            .limit(limit)
        )
        results['departments'] = [
            {
                "id": row.id,
                "name": row.name,
                "code": row.code,
                "description": row.description,
                "type": "department"
            }
            for row in department_result.fetchall()
        ]
    
    return results


@handle_db_errors
async def batch_update_customer_status(
    db: AsyncSession,
    *,
    customer_ids: List[str],
    new_status: str,
    operator_id: str
) -> int:
    """
    批量更新客户状态
    
    Args:
        db: 数据库会话
        customer_ids: 客户ID列表
        new_status: 新状态
        operator_id: 操作人ID
        
    Returns:
        更新的记录数量
    """
    # 使用原生SQL进行批量更新
    sql = text("""
        UPDATE customers 
        SET status = :new_status, updated_at = CURRENT_TIMESTAMP 
        WHERE id IN :customer_ids AND is_deleted = 0
    """)
    
    result = await db.execute(sql, {
        "new_status": new_status,
        "customer_ids": tuple(customer_ids)
    })
    
    await db.commit()
    return result.rowcount


@handle_db_errors
async def get_follow_up_reminders(
    db: AsyncSession,
    *,
    user_id: Optional[str] = None,
    days_ahead: int = 7
) -> List[Dict[str, Any]]:
    """
    获取跟进提醒列表
    
    Args:
        db: 数据库会话
        user_id: 用户ID（可选）
        days_ahead: 提前天数
        
    Returns:
        跟进提醒列表
    """
    end_date = datetime.now() + timedelta(days=days_ahead)
    
    query = select(
        Customer.id,
        Customer.name,
        Customer.phone,
        Customer.company,
        Customer.next_follow_time,
        Customer.owner_id,
        User.real_name.label('owner_name')
    ).join(
        User, Customer.owner_id == User.id
    ).where(
        and_(
            Customer.is_deleted == False,
            Customer.next_follow_time.isnot(None),
            Customer.next_follow_time <= end_date,
            Customer.next_follow_time >= datetime.now()
        )
    )
    
    if user_id:
        query = query.where(Customer.owner_id == user_id)
    
    query = query.order_by(Customer.next_follow_time)
    
    result = await db.execute(query)
    
    return [
        {
            "customer_id": row.id,
            "customer_name": row.name,
            "customer_phone": row.phone,
            "customer_company": row.company,
            "next_follow_time": row.next_follow_time,
            "owner_id": row.owner_id,
            "owner_name": row.owner_name,
            "days_remaining": (row.next_follow_time - datetime.now()).days
        }
        for row in result.fetchall()
    ]


@handle_db_errors
async def cleanup_old_records(
    db: AsyncSession,
    *,
    table_name: str,
    date_field: str,
    days_old: int = 365
) -> int:
    """
    清理旧记录
    
    Args:
        db: 数据库会话
        table_name: 表名
        date_field: 日期字段名
        days_old: 保留天数
        
    Returns:
        删除的记录数量
    """
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    sql = text(f"""
        DELETE FROM {table_name} 
        WHERE {date_field} < :cutoff_date
    """)
    
    result = await db.execute(sql, {"cutoff_date": cutoff_date})
    await db.commit()
    
    logger.info(f"Cleaned up {result.rowcount} old records from {table_name}")
    return result.rowcount


@handle_db_errors
async def generate_report_data(
    db: AsyncSession,
    *,
    report_type: str,
    start_date: datetime,
    end_date: datetime,
    filters: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    生成报表数据
    
    Args:
        db: 数据库会话
        report_type: 报表类型 ['customer', 'follow', 'meeting', 'department']
        start_date: 开始日期
        end_date: 结束日期
        filters: 额外过滤条件
        
    Returns:
        报表数据字典
    """
    if not filters:
        filters = {}
    
    if report_type == 'customer':
        # 客户报表
        query = select(
            func.date(Customer.created_at).label('date'),
            func.count(Customer.id).label('count'),
            Customer.status
        ).where(
            and_(
                Customer.created_at >= start_date,
                Customer.created_at <= end_date,
                Customer.is_deleted == False
            )
        ).group_by(
            func.date(Customer.created_at),
            Customer.status
        ).order_by(func.date(Customer.created_at))
        
        if 'department_id' in filters:
            query = query.where(Customer.department_id == filters['department_id'])
        
        result = await db.execute(query)
        data = result.fetchall()
        
        return {
            "report_type": report_type,
            "data": [
                {
                    "date": row.date.isoformat(),
                    "count": row.count,
                    "status": row.status
                }
                for row in data
            ],
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
    
    elif report_type == 'follow':
        # 跟进报表
        query = select(
            func.date(CustomerFollowRecord.follow_time).label('date'),
            func.count(CustomerFollowRecord.id).label('count'),
            CustomerFollowRecord.follow_type
        ).where(
            and_(
                CustomerFollowRecord.follow_time >= start_date,
                CustomerFollowRecord.follow_time <= end_date
            )
        ).group_by(
            func.date(CustomerFollowRecord.follow_time),
            CustomerFollowRecord.follow_type
        ).order_by(func.date(CustomerFollowRecord.follow_time))
        
        if 'user_id' in filters:
            query = query.where(CustomerFollowRecord.user_id == filters['user_id'])
        
        result = await db.execute(query)
        data = result.fetchall()
        
        return {
            "report_type": report_type,
            "data": [
                {
                    "date": row.date.isoformat(),
                    "count": row.count,
                    "follow_type": row.follow_type
                }
                for row in data
            ],
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
    
    else:
        return {
            "report_type": report_type,
            "error": "Unsupported report type",
            "data": []
        }


# 工具函数
def generate_uuid() -> str:
    """生成UUID字符串"""
    return str(uuid4())


def format_phone(phone: str) -> str:
    """格式化手机号"""
    if not phone:
        return phone
    # 移除所有非数字字符
    digits = ''.join(filter(str.isdigit, phone))
    # 格式化为 xxx-xxxx-xxxx
    if len(digits) == 11:
        return f"{digits[:3]}-{digits[3:7]}-{digits[7:]}"
    return phone


def validate_email(email: str) -> bool:
    """简单的邮箱验证"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))