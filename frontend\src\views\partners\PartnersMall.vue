<template>
  <div class="partners-mall">
    <div class="user-points">
      <n-card>
        <div class="points-info">
          <div class="points-avatar">
            <n-avatar size="large" :src="userInfo.avatar" />
          </div>
          <div class="points-details">
            <h3>{{ userInfo.name }}</h3>
            <div class="points-balance">
              <span class="points-label">可用积分：</span>
              <span class="points-value">{{ userInfo.points }}</span>
              <span class="points-unit">分</span>
            </div>
          </div>
          <div class="points-actions">
            <n-button type="primary" @click="showPointsHistory = true">
              积分明细
            </n-button>
          </div>
        </div>
      </n-card>
    </div>

    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索商品名称"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="categoryFilter"
          placeholder="商品分类"
          clearable
          style="width: 150px"
          :options="categoryOptions"
        />
        
        <n-select
          v-model:value="pointsRangeFilter"
          placeholder="积分范围"
          clearable
          style="width: 150px"
          :options="pointsRangeOptions"
        />
        
        <n-select
          v-model:value="sortBy"
          placeholder="排序方式"
          style="width: 150px"
          :options="sortOptions"
        />
      </n-space>
    </div>

    <div class="products-grid">
      <n-grid :cols="4" :x-gap="16" :y-gap="16">
        <n-grid-item v-for="product in filteredProducts" :key="product.id">
          <n-card class="product-card" hoverable>
            <div class="product-image">
              <img :src="product.image" :alt="product.name" />
              <div v-if="product.stock <= 10" class="stock-warning">
                仅剩{{ product.stock }}件
              </div>
            </div>
            
            <div class="product-info">
              <h4 class="product-name">{{ product.name }}</h4>
              <p class="product-desc">{{ product.description }}</p>
              
              <div class="product-points">
                <span class="points-required">{{ product.points }}</span>
                <span class="points-unit">积分</span>
              </div>
              
              <div class="product-meta">
                <span class="stock-info">库存：{{ product.stock }}</span>
                <span class="exchange-count">已兑换：{{ product.exchangeCount }}</span>
              </div>
              
              <div class="product-actions">
                <n-button
                  type="primary"
                  block
                  :disabled="product.points > userInfo.points || product.stock === 0"
                  @click="exchangeProduct(product)"
                >
                  {{ product.stock === 0 ? '已售罄' : '立即兑换' }}
                </n-button>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 兑换确认弹窗 -->
    <n-modal v-model:show="showExchangeModal" preset="card" style="width: 500px" title="确认兑换">
      <div v-if="selectedProduct" class="exchange-confirm">
        <div class="product-summary">
          <img :src="selectedProduct.image" :alt="selectedProduct.name" class="summary-image" />
          <div class="summary-info">
            <h4>{{ selectedProduct.name }}</h4>
            <p>{{ selectedProduct.description }}</p>
            <div class="summary-points">
              <span>需要积分：</span>
              <span class="points-value">{{ selectedProduct.points }}</span>
              <span>分</span>
            </div>
          </div>
        </div>
        
        <div class="exchange-form">
          <n-form-item label="兑换数量">
            <n-input-number
              v-model:value="exchangeQuantity"
              :min="1"
              :max="Math.min(selectedProduct.stock, Math.floor(userInfo.points / selectedProduct.points))"
              style="width: 100%"
            />
          </n-form-item>
          
          <n-form-item label="收货地址">
            <n-input
              v-model:value="deliveryAddress"
              type="textarea"
              placeholder="请输入收货地址"
              :rows="3"
            />
          </n-form-item>
          
          <div class="exchange-summary">
            <div class="summary-row">
              <span>商品单价：</span>
              <span>{{ selectedProduct.points }}积分</span>
            </div>
            <div class="summary-row">
              <span>兑换数量：</span>
              <span>{{ exchangeQuantity }}件</span>
            </div>
            <div class="summary-row total">
              <span>总计：</span>
              <span class="total-points">{{ selectedProduct.points * exchangeQuantity }}积分</span>
            </div>
            <div class="summary-row">
              <span>兑换后余额：</span>
              <span>{{ userInfo.points - selectedProduct.points * exchangeQuantity }}积分</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showExchangeModal = false">取消</n-button>
          <n-button type="primary" @click="confirmExchange">确认兑换</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 积分明细弹窗 -->
    <n-modal v-model:show="showPointsHistory" preset="card" style="width: 800px" title="积分明细">
      <n-data-table
        :columns="pointsHistoryColumns"
        :data="pointsHistory"
        :pagination="pointsPagination"
        size="small"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import {
  NCard,
  NGrid,
  NGridItem,
  NInput,
  NButton,
  NSelect,
  NSpace,
  NAvatar,
  NModal,
  NFormItem,
  NInputNumber,
  NDataTable,
  NTag,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import { SearchOutline as SearchIcon } from '@vicons/ionicons5'

interface Product {
  id: string
  name: string
  description: string
  image: string
  points: number
  stock: number
  exchangeCount: number
  category: string
}

interface UserInfo {
  name: string
  avatar: string
  points: number
}

interface PointsHistoryItem {
  id: string
  type: 'earn' | 'spend'
  amount: number
  description: string
  date: string
  balance: number
}

const message = useMessage()
const searchKeyword = ref('')
const categoryFilter = ref<string | null>(null)
const pointsRangeFilter = ref<string | null>(null)
const sortBy = ref('default')
const showExchangeModal = ref(false)
const showPointsHistory = ref(false)
const selectedProduct = ref<Product | null>(null)
const exchangeQuantity = ref(1)
const deliveryAddress = ref('')

const userInfo = ref<UserInfo>({
  name: '张三',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=张三',
  points: 5200
})

const categoryOptions = [
  { label: '电子产品', value: 'electronics' },
  { label: '生活用品', value: 'lifestyle' },
  { label: '办公用品', value: 'office' },
  { label: '礼品卡券', value: 'voucher' }
]

const pointsRangeOptions = [
  { label: '100以下', value: '0-100' },
  { label: '100-500', value: '100-500' },
  { label: '500-1000', value: '500-1000' },
  { label: '1000以上', value: '1000+' }
]

const sortOptions = [
  { label: '默认排序', value: 'default' },
  { label: '积分从低到高', value: 'points-asc' },
  { label: '积分从高到低', value: 'points-desc' },
  { label: '兑换量从高到低', value: 'popular' }
]

const products = ref<Product[]>([])
const pointsHistory = ref<PointsHistoryItem[]>([])

const pointsPagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
}

const filteredProducts = computed(() => {
  let result = products.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(product => 
      product.name.toLowerCase().includes(keyword) ||
      product.description.toLowerCase().includes(keyword)
    )
  }
  
  // 分类过滤
  if (categoryFilter.value) {
    result = result.filter(product => product.category === categoryFilter.value)
  }
  
  // 积分范围过滤
  if (pointsRangeFilter.value) {
    const range = pointsRangeFilter.value
    result = result.filter(product => {
      if (range === '0-100') return product.points < 100
      if (range === '100-500') return product.points >= 100 && product.points <= 500
      if (range === '500-1000') return product.points >= 500 && product.points <= 1000
      if (range === '1000+') return product.points > 1000
      return true
    })
  }
  
  // 排序
  if (sortBy.value === 'points-asc') {
    result.sort((a, b) => a.points - b.points)
  } else if (sortBy.value === 'points-desc') {
    result.sort((a, b) => b.points - a.points)
  } else if (sortBy.value === 'popular') {
    result.sort((a, b) => b.exchangeCount - a.exchangeCount)
  }
  
  return result
})

const pointsHistoryColumns: DataTableColumns<PointsHistoryItem> = [
  {
    title: '类型',
    key: 'type',
    render: (row) => {
      const typeMap = {
        earn: { type: 'success', text: '获得' },
        spend: { type: 'warning', text: '消费' }
      }
      const config = typeMap[row.type]
      return h(NTag, { type: config.type as any, size: 'small' }, { default: () => config.text })
    }
  },
  {
    title: '积分变动',
    key: 'amount',
    render: (row) => {
      const sign = row.type === 'earn' ? '+' : '-'
      const color = row.type === 'earn' ? '#18a058' : '#d03050'
      return h('span', { style: { color } }, `${sign}${row.amount}`)
    }
  },
  {
    title: '说明',
    key: 'description'
  },
  {
    title: '时间',
    key: 'date'
  },
  {
    title: '余额',
    key: 'balance'
  }
]

const loadProducts = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    products.value = [
      {
        id: '1',
        name: 'iPhone 15 Pro',
        description: '最新款苹果手机，256GB存储',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20smartphone%20product%20photo&image_size=square',
        points: 15000,
        stock: 5,
        exchangeCount: 12,
        category: 'electronics'
      },
      {
        id: '2',
        name: '小米空气净化器',
        description: '高效过滤，静音运行',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=Xiaomi%20air%20purifier%20white%20modern%20design&image_size=square',
        points: 2500,
        stock: 15,
        exchangeCount: 28,
        category: 'lifestyle'
      },
      {
        id: '3',
        name: '星巴克咖啡券',
        description: '50元星巴克代金券',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=Starbucks%20gift%20card%20voucher%20green%20design&image_size=square',
        points: 500,
        stock: 100,
        exchangeCount: 156,
        category: 'voucher'
      },
      {
        id: '4',
        name: '罗技无线鼠标',
        description: '人体工学设计，无线连接',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=Logitech%20wireless%20mouse%20black%20ergonomic%20design&image_size=square',
        points: 800,
        stock: 25,
        exchangeCount: 45,
        category: 'office'
      },
      {
        id: '5',
        name: 'AirPods Pro',
        description: '主动降噪无线耳机',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=Apple%20AirPods%20Pro%20white%20wireless%20earbuds&image_size=square',
        points: 3500,
        stock: 8,
        exchangeCount: 22,
        category: 'electronics'
      },
      {
        id: '6',
        name: '保温杯',
        description: '304不锈钢，24小时保温',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=stainless%20steel%20thermal%20mug%20insulated%20bottle&image_size=square',
        points: 300,
        stock: 50,
        exchangeCount: 89,
        category: 'lifestyle'
      }
    ]
  } catch (error) {
    message.error('加载商品失败')
  }
}

const loadPointsHistory = async () => {
  try {
    pointsHistory.value = [
      {
        id: '1',
        type: 'earn',
        amount: 500,
        description: '推荐客户成交奖励',
        date: '2024-01-20 14:30',
        balance: 5200
      },
      {
        id: '2',
        type: 'spend',
        amount: 300,
        description: '兑换保温杯',
        date: '2024-01-18 10:15',
        balance: 4700
      },
      {
        id: '3',
        type: 'earn',
        amount: 800,
        description: '月度业绩奖励',
        date: '2024-01-15 16:00',
        balance: 5000
      }
    ]
  } catch (error) {
    message.error('加载积分明细失败')
  }
}

const exchangeProduct = (product: Product) => {
  selectedProduct.value = product
  exchangeQuantity.value = 1
  deliveryAddress.value = ''
  showExchangeModal.value = true
}

const confirmExchange = async () => {
  if (!selectedProduct.value) return
  
  if (!deliveryAddress.value.trim()) {
    message.error('请填写收货地址')
    return
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const totalPoints = selectedProduct.value.points * exchangeQuantity.value
    userInfo.value.points -= totalPoints
    selectedProduct.value.stock -= exchangeQuantity.value
    selectedProduct.value.exchangeCount += exchangeQuantity.value
    
    message.success('兑换成功！商品将在3-5个工作日内发货')
    showExchangeModal.value = false
    
    // 添加积分消费记录
    pointsHistory.value.unshift({
      id: Date.now().toString(),
      type: 'spend',
      amount: totalPoints,
      description: `兑换${selectedProduct.value.name} x${exchangeQuantity.value}`,
      date: new Date().toLocaleString(),
      balance: userInfo.value.points
    })
  } catch (error) {
    message.error('兑换失败，请重试')
  }
}

onMounted(() => {
  loadProducts()
  loadPointsHistory()
})
</script>

<style scoped>
.partners-mall {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.mall-header {
  margin-bottom: 24px;
}

.points-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.points-details h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.points-balance {
  display: flex;
  align-items: center;
  gap: 4px;
}

.points-label {
  color: #666;
  font-size: 14px;
}

.points-value {
  font-size: 24px;
  font-weight: 700;
  color: #18a058;
}

.points-unit {
  color: #666;
  font-size: 14px;
}

.filters {
  margin-bottom: 24px;
}

.products-grid {
  margin-bottom: 24px;
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 16px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stock-warning {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #f56565;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.product-desc {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.product-points {
  margin-bottom: 12px;
}

.points-required {
  font-size: 20px;
  font-weight: 700;
  color: #2080f0;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 12px;
  color: #999;
}

.product-actions {
  margin-top: auto;
}

.exchange-confirm {
  padding: 16px 0;
}

.product-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.summary-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
}

.summary-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.summary-info p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.summary-points {
  display: flex;
  align-items: center;
  gap: 4px;
}

.exchange-form {
  margin-bottom: 16px;
}

.exchange-summary {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-row.total {
  font-weight: 600;
  border-top: 1px solid #e0e0e0;
  padding-top: 8px;
  margin-top: 8px;
}

.total-points {
  color: #2080f0;
  font-size: 18px;
}
</style>