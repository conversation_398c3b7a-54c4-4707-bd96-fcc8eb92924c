<template>
  <div class="create-campaign">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="goBack">
            <template #icon>
              <n-icon><ArrowBackOutline /></n-icon>
            </template>
          </n-button>
          <h1>{{ isEdit ? '编辑营销活动' : '创建营销活动' }}</h1>
        </div>
        <div class="header-actions">
          <n-button @click="saveDraft">保存草稿</n-button>
          <n-button type="primary" @click="submitForm">{{ isEdit ? '更新活动' : '创建活动' }}</n-button>
        </div>
      </div>
    </div>

    <div class="page-content">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="top"
        class="campaign-form"
      >
        <!-- 基本信息 -->
        <n-card title="基本信息" class="form-section">
          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="活动名称" path="name">
                <n-input v-model:value="formData.name" placeholder="请输入活动名称" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="活动类型" path="type">
                <n-select
                  v-model:value="formData.type"
                  :options="typeOptions"
                  placeholder="请选择活动类型"
                  @update:value="handleTypeChange"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          
          <n-form-item label="活动描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入活动描述"
            />
          </n-form-item>
          
          <n-grid :cols="3" :x-gap="24">
            <n-grid-item>
              <n-form-item label="活动时间" path="timeRange">
                <n-date-picker
                  v-model:value="timeRange"
                  type="datetimerange"
                  clearable
                  @update:value="handleTimeRangeChange"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="目标受众">
                <n-input v-model:value="formData.target_audience" placeholder="如：全体用户" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="预算（元）">
                <n-input-number v-model:value="formData.budget" :min="0" placeholder="活动预算" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </n-card>

        <!-- 展现样式 -->
        <n-card title="展现样式" class="form-section">
          <n-form-item label="展现方式">
            <n-radio-group v-model:value="formData.config.display_style">
              <n-space>
                <n-radio value="modal">弹窗样式</n-radio>
                <n-radio value="embed">页面嵌入</n-radio>
                <n-radio value="float">浮动按钮</n-radio>
                <n-radio value="banner">横幅展示</n-radio>
                <n-radio value="card">卡片样式</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
        </n-card>

        <!-- 活动配置 -->
        <n-card title="活动配置" class="form-section">
          <!-- 抽奖活动配置 -->
          <template v-if="formData.type === 'lottery'">
            <div class="config-section">
              <h4>奖品设置</h4>
              <div v-for="(prize, index) in formData.config.prizes" :key="index" class="prize-item">
                <n-grid :cols="4" :x-gap="12">
                  <n-grid-item>
                    <n-input v-model:value="prize.name" placeholder="奖品名称" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-input-number v-model:value="prize.quantity" :min="1" placeholder="数量" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-input-number v-model:value="prize.probability" :min="0" :max="100" placeholder="中奖概率(%)" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-button @click="removePrize(index)" type="error" secondary>
                      <template #icon><n-icon><TrashOutline /></n-icon></template>
                    </n-button>
                  </n-grid-item>
                </n-grid>
              </div>
              <n-button @click="addPrize" dashed block>
                <template #icon><n-icon><AddOutline /></n-icon></template>
                添加奖品
              </n-button>
              
              <n-divider />
              
              <n-form-item label="每人最大参与次数">
                <n-input-number v-model:value="formData.config.max_attempts_per_user" :min="1" />
              </n-form-item>
            </div>
          </template>

          <!-- 大转盘配置 -->
          <template v-if="formData.type === 'wheel'">
            <div class="config-section">
              <h4>转盘奖品设置</h4>
              <div v-for="(prize, index) in formData.config.prizes" :key="index" class="prize-item">
                <n-grid :cols="5" :x-gap="12">
                  <n-grid-item>
                    <n-input v-model:value="prize.name" placeholder="奖品名称" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-input-number v-model:value="prize.quantity" :min="1" placeholder="数量" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-input-number v-model:value="prize.probability" :min="0" :max="100" placeholder="中奖概率(%)" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-color-picker v-model:value="prize.color" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-button @click="removePrize(index)" type="error" secondary>
                      <template #icon><n-icon><TrashOutline /></n-icon></template>
                    </n-button>
                  </n-grid-item>
                </n-grid>
              </div>
              <n-button @click="addWheelPrize" dashed block>
                <template #icon><n-icon><AddOutline /></n-icon></template>
                添加奖品
              </n-button>
              
              <n-divider />
              
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="每人最大参与次数">
                    <n-input-number v-model:value="formData.config.max_attempts_per_user" :min="1" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="转盘样式">
                    <n-select v-model:value="formData.config.wheel_style" :options="wheelStyleOptions" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>

          <!-- 秒杀活动配置 -->
          <template v-if="formData.type === 'flash_sale'">
            <div class="config-section">
              <h4>商品信息</h4>
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="商品名称">
                    <n-input v-model:value="formData.config.product_name" placeholder="请输入商品名称" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="库存数量">
                    <n-input-number v-model:value="formData.config.stock_quantity" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              
              <n-grid :cols="3" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="原价（元）">
                    <n-input-number v-model:value="formData.config.original_price" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="秒杀价（元）">
                    <n-input-number v-model:value="formData.config.sale_price" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="每人限购">
                    <n-input-number v-model:value="formData.config.max_purchase_per_user" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>

          <!-- 限时折扣配置 -->
          <template v-if="formData.type === 'time_discount'">
            <div class="config-section">
              <h4>折扣信息</h4>
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="商品名称">
                    <n-input v-model:value="formData.config.product_name" placeholder="请输入商品名称" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="原价（元）">
                    <n-input-number v-model:value="formData.config.original_price" :min="0" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              
              <n-grid :cols="3" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="折扣类型">
                    <n-select v-model:value="formData.config.discount_type" :options="discountTypeOptions" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="折扣值">
                    <n-input-number v-model:value="formData.config.discount_value" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="库存数量">
                    <n-input-number v-model:value="formData.config.stock_quantity" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>

          <!-- 拼团活动配置 -->
          <template v-if="formData.type === 'group_buy'">
            <div class="config-section">
              <h4>拼团信息</h4>
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="商品名称">
                    <n-input v-model:value="formData.config.product_name" placeholder="请输入商品名称" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="库存数量">
                    <n-input-number v-model:value="formData.config.stock_quantity" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              
              <n-grid :cols="4" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="原价（元）">
                    <n-input-number v-model:value="formData.config.original_price" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="拼团价（元）">
                    <n-input-number v-model:value="formData.config.group_price" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="成团人数">
                    <n-input-number v-model:value="formData.config.min_group_size" :min="2" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="拼团时限（小时）">
                    <n-input-number v-model:value="formData.config.group_time_limit" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>

          <!-- 转发分享配置 -->
          <template v-if="formData.type === 'share'">
            <div class="config-section">
              <h4>分享设置</h4>
              <n-form-item label="分享内容">
                <n-input v-model:value="formData.config.share_content" type="textarea" :rows="3" placeholder="请输入分享内容" />
              </n-form-item>
              
              <n-grid :cols="3" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="奖励类型">
                    <n-select v-model:value="formData.config.reward_type" :options="rewardTypeOptions" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="奖励数量">
                    <n-input-number v-model:value="formData.config.reward_amount" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="目标分享数">
                    <n-input-number v-model:value="formData.config.target_shares" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>

          <!-- 优惠券配置 -->
          <template v-if="formData.type === 'coupon'">
            <div class="config-section">
              <h4>优惠券设置</h4>
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="优惠券类型">
                    <n-select v-model:value="formData.config.coupon_type" :options="couponTypeOptions" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="发放总量">
                    <n-input-number v-model:value="formData.config.total_quantity" :min="1" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="优惠金额（元）">
                    <n-input-number v-model:value="formData.config.discount_amount" :min="0" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="最低消费金额（元）">
                    <n-input-number v-model:value="formData.config.min_order_amount" :min="0" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>
          </template>
        </n-card>
      </n-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  AddOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useMarketingStore } from '@/stores/marketingStore'
import type { Campaign } from '@/api/marketingService'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const marketingStore = useMarketingStore()

const formRef = ref()
const isEdit = computed(() => !!route.params.id)
const timeRange = ref<[number, number] | null>(null)

// 表单数据
const formData = reactive<Partial<Campaign>>({
  name: '',
  type: '',
  description: '',
  start_time: '',
  end_time: '',
  target_audience: '',
  budget: undefined,
  config: {
    display_style: 'modal'
  },
  status: 'draft'
})

// 选项配置
const typeOptions = [
  { label: '抽奖活动', value: 'lottery' },
  { label: '大转盘', value: 'wheel' },
  { label: '秒杀活动', value: 'flash_sale' },
  { label: '限时折扣', value: 'time_discount' },
  { label: '拼团活动', value: 'group_buy' },
  { label: '转发分享', value: 'share' },
  { label: '优惠券', value: 'coupon' },
  { label: '其他', value: 'other' }
]

const wheelStyleOptions = [
  { label: '经典样式', value: 'classic' },
  { label: '现代样式', value: 'modern' },
  { label: '简约样式', value: 'minimal' }
]

const discountTypeOptions = [
  { label: '百分比折扣', value: 'percentage' },
  { label: '固定金额', value: 'fixed' }
]

const rewardTypeOptions = [
  { label: '积分', value: 'points' },
  { label: '优惠券', value: 'coupon' },
  { label: '现金', value: 'cash' }
]

const couponTypeOptions = [
  { label: '满减券', value: 'discount' },
  { label: '折扣券', value: 'percentage' },
  { label: '免邮券', value: 'free_shipping' }
]

// 表单验证规则
const formRules = {
  name: {
    required: true,
    message: '请输入活动名称',
    trigger: 'blur'
  },
  type: {
    required: true,
    message: '请选择活动类型',
    trigger: 'change'
  },
  timeRange: {
    required: true,
    message: '请选择活动时间',
    trigger: 'change'
  }
}

// 方法
const goBack = () => {
  router.back()
}

const handleTypeChange = (type: string) => {
  const currentDisplayStyle = formData.config?.display_style || 'modal'
  
  switch (type) {
    case 'lottery':
      formData.config = {
        display_style: currentDisplayStyle,
        prizes: [{ name: '', quantity: 1, probability: 10 }],
        participation_rules: [],
        max_attempts_per_user: 1
      }
      break
    case 'wheel':
      formData.config = {
        display_style: currentDisplayStyle,
        prizes: [{ name: '', quantity: 1, probability: 10, color: '#ff6b6b' }],
        max_attempts_per_user: 1,
        wheel_style: 'classic'
      }
      break
    case 'flash_sale':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        sale_price: 0,
        stock_quantity: 1,
        max_purchase_per_user: 1
      }
      break
    case 'time_discount':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        discount_type: 'percentage',
        discount_value: 0,
        stock_quantity: 1
      }
      break
    case 'group_buy':
      formData.config = {
        display_style: currentDisplayStyle,
        product_name: '',
        original_price: 0,
        group_price: 0,
        min_group_size: 2,
        group_time_limit: 24,
        stock_quantity: 1
      }
      break
    case 'share':
      formData.config = {
        display_style: currentDisplayStyle,
        share_content: '',
        reward_type: '',
        reward_amount: 0,
        target_shares: 100
      }
      break
    case 'coupon':
      formData.config = {
        display_style: currentDisplayStyle,
        coupon_type: 'discount',
        discount_amount: 0,
        min_order_amount: 0,
        total_quantity: 100
      }
      break
    default:
      formData.config = {
        display_style: currentDisplayStyle
      }
  }
}

const handleTimeRangeChange = (value: [number, number] | null) => {
  if (value) {
    formData.start_time = new Date(value[0]).toISOString()
    formData.end_time = new Date(value[1]).toISOString()
  } else {
    formData.start_time = ''
    formData.end_time = ''
  }
}

const addPrize = () => {
  if (!formData.config?.prizes) {
    formData.config!.prizes = []
  }
  formData.config.prizes.push({ name: '', quantity: 1, probability: 10 })
}

const addWheelPrize = () => {
  if (!formData.config?.prizes) {
    formData.config!.prizes = []
  }
  formData.config.prizes.push({ name: '', quantity: 1, probability: 10, color: '#ff6b6b' })
}

const removePrize = (index: number) => {
  if (formData.config?.prizes) {
    formData.config.prizes.splice(index, 1)
  }
}

const saveDraft = async () => {
  try {
    formData.status = 'draft'
    if (isEdit.value) {
      await marketingStore.updateCampaign(Number(route.params.id), formData as Campaign)
      message.success('草稿保存成功')
    } else {
      await marketingStore.createCampaign(formData as Campaign)
      message.success('草稿保存成功')
      router.push('/marketing')
    }
  } catch (error) {
    message.error('保存失败')
  }
}

const submitForm = async () => {
  try {
    await formRef.value?.validate()
    
    if (isEdit.value) {
      await marketingStore.updateCampaign(Number(route.params.id), formData as Campaign)
      message.success('活动更新成功')
    } else {
      await marketingStore.createCampaign(formData as Campaign)
      message.success('活动创建成功')
    }
    
    router.push('/marketing')
  } catch (error) {
    message.error('提交失败，请检查表单信息')
  }
}

// 初始化
onMounted(async () => {
  if (isEdit.value) {
    try {
      const response = await marketingStore.fetchCampaign(Number(route.params.id))
      const campaign = response.data
      Object.assign(formData, campaign)
      if (campaign.start_time && campaign.end_time) {
        timeRange.value = [
          new Date(campaign.start_time).getTime(),
          new Date(campaign.end_time).getTime()
        ]
      }
    } catch (error) {
      message.error('加载活动信息失败')
      router.push('/marketing')
    }
  }
})
</script>

<style scoped>
.create-campaign {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.campaign-form {
  max-width: 1200px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-weight: 600;
}

.prize-item {
  margin-bottom: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.prize-item:last-child {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 16px 0;
    gap: 16px;
  }
  
  .header-left {
    width: 100%;
    justify-content: center;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>