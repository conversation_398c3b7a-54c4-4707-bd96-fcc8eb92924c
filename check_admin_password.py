#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中admin用户的密码哈希
"""

import asyncio
import pymysql
from app.utils.security import verify_password, get_password_hash

def check_mysql_admin():
    """检查MySQL数据库中的admin用户"""
    try:
        # 连接MySQL数据库
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='root',
            database='yysh',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 查询admin用户
            cursor.execute("SELECT id, username, password FROM users WHERE username='admin'")
            result = cursor.fetchone()
            
            if result:
                user_id, username, password_hash = result
                print(f"找到用户: ID={user_id}, 用户名={username}")
                print(f"密码哈希: {password_hash}")
                
                # 验证密码
                is_valid = verify_password('admin123', password_hash)
                print(f"密码'admin123'验证结果: {is_valid}")
                
                if not is_valid:
                    print("\n密码验证失败，生成新的密码哈希...")
                    new_hash = get_password_hash('admin123')
                    print(f"新密码哈希: {new_hash}")
                    
                    # 更新密码
                    cursor.execute("UPDATE users SET password = %s WHERE username = 'admin'", (new_hash,))
                    connection.commit()
                    print("密码已更新")
                    
                    # 再次验证
                    is_valid_new = verify_password('admin123', new_hash)
                    print(f"新密码验证结果: {is_valid_new}")
            else:
                print("未找到admin用户")
                
                # 创建admin用户
                print("创建admin用户...")
                new_hash = get_password_hash('admin123')
                cursor.execute(
                    "INSERT INTO users (username, password, name, role, department_id, status) VALUES (%s, %s, %s, %s, %s, %s)",
                    ('admin', new_hash, '系统管理员', 'admin', 3, 1)
                )
                connection.commit()
                print("admin用户创建成功")
        
        connection.close()
        print("\n数据库连接已关闭")
        
    except Exception as e:
        print(f"数据库操作失败: {e}")

if __name__ == "__main__":
    check_mysql_admin()