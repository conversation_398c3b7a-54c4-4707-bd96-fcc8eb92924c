<template>
  <div class="visit-stage-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
    >
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="客户" path="customerId">
          <n-select
            v-model:value="formData.customerId"
            placeholder="请选择客户"
            :options="customerOptions"
            filterable
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi label="子阶段" path="subStage">
          <n-select
            v-model:value="formData.subStage"
            placeholder="请选择子阶段"
            :options="subStageOptions"
            @update:value="handleSubStageChange"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="设计师" path="designerId">
          <n-select
            v-model:value="formData.designerId"
            placeholder="请选择设计师"
            :options="designerOptions"
            filterable
            clearable
            @update:value="handleDesignerChange"
          />
        </n-form-item-gi>
        <n-form-item-gi label="跟进方式" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择跟进方式"
            :options="typeOptions"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="跟进状态" path="status">
          <n-select
            v-model:value="formData.status"
            placeholder="请选择跟进状态"
            :options="statusOptions"
          />
        </n-form-item-gi>
        <n-form-item-gi :label="visitDateLabel" path="visitDate">
          <n-date-picker
            v-model:value="formData.visitDate"
            type="datetime"
            :placeholder="visitDatePlaceholder"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="跟进时间" path="followTime">
          <n-date-picker
            v-model:value="formData.followTime"
            type="datetime"
            placeholder="请选择跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
        <n-form-item-gi label="下次跟进">
          <n-date-picker
            v-model:value="formData.nextFollowTime"
            type="datetime"
            placeholder="请选择下次跟进时间"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <!-- 量房特殊字段 -->
      <div v-if="formData.subStage === 'measure'" class="measure-fields">
        <n-divider title-placement="left">
          <n-text type="info">量房信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="量房时间">
            <n-date-picker
              v-model:value="formData.measureDate"
              type="datetime"
              placeholder="请选择量房时间"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="房屋面积">
            <n-input-number
              v-model:value="houseArea"
              placeholder="请输入房屋面积"
              style="width: 100%"
              :min="0"
              :precision="2"
            >
              <template #suffix>㎡</template>
            </n-input-number>
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <!-- 到店特殊字段 -->
      <div v-if="formData.subStage === 'visit'" class="visit-fields">
        <n-divider title-placement="left">
          <n-text type="success">到店信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="参观项目">
            <n-checkbox-group v-model:value="visitProjects">
              <n-space>
                <n-checkbox value="showroom" label="展厅参观" />
                <n-checkbox value="materials" label="材料展示" />
                <n-checkbox value="design" label="设计方案" />
                <n-checkbox value="contract" label="合同洽谈" />
              </n-space>
            </n-checkbox-group>
          </n-form-item-gi>
          <n-form-item-gi label="满意度">
            <n-rate v-model:value="satisfaction" :count="5" allow-half />
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <!-- 拜访特殊字段 -->
      <div v-if="formData.subStage === 'home_visit'" class="home-visit-fields">
        <n-divider title-placement="left">
          <n-text type="warning">拜访信息</n-text>
        </n-divider>
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="拜访地址">
            <n-input
              v-model:value="visitAddress"
              placeholder="请输入拜访地址"
            />
          </n-form-item-gi>
          <n-form-item-gi label="拜访时长">
            <n-input-number
              v-model:value="visitDuration"
              placeholder="请输入拜访时长"
              style="width: 100%"
              :min="0"
            >
              <template #suffix>分钟</template>
            </n-input-number>
          </n-form-item-gi>
        </n-grid>
      </div>
      
      <n-form-item label="跟进内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入跟进内容"
          :rows="4"
        />
      </n-form-item>
      
      <n-form-item label="备注">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

interface FormData {
  id: number | null
  customerId: number | null
  type: string
  status: string
  followTime: number | null
  content: string
  nextFollowTime: number | null
  remark: string
  stage: 'follow' | 'visit' | 'deal'
  subStage: string
  designer: string
  designerId: number | null
  amount: number | null
  contractNo: string
  paymentStatus: string
  visitDate: number | null
  measureDate: number | null
  dealDate: number | null
}

interface Props {
  formData: FormData
  customerOptions: Array<{ label: string; value: number }>
  designerOptions: Array<{ label: string; value: number }>
}

interface Emits {
  'update:formData': [value: FormData]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)

// 额外字段
const houseArea = ref<number | null>(null)
const visitProjects = ref<string[]>([])
const satisfaction = ref<number | null>(null)
const visitAddress = ref('')
const visitDuration = ref<number | null>(null)

// 子阶段选项
const subStageOptions = [
  { label: '量房', value: 'measure' },
  { label: '到店', value: 'visit' },
  { label: '拜访', value: 'home_visit' }
]

// 跟进方式选项
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '到店参观', value: 'visit' },
  { label: '上门量房', value: 'measure' },
  { label: '家访', value: 'home_visit' },
  { label: '其他', value: 'other' }
]

// 跟进状态选项
const statusOptions = [
  { label: '已联系', value: 'contacted' },
  { label: '有意向', value: 'interested' },
  { label: '无意向', value: 'not_interested' },
  { label: '待跟进', value: 'pending' },
  { label: '已成交', value: 'closed' }
]

// 计算属性
const visitDateLabel = computed(() => {
  const labelMap: Record<string, string> = {
    measure: '量房时间',
    visit: '到店时间',
    home_visit: '拜访时间'
  }
  return labelMap[props.formData.subStage] || '到店时间'
})

const visitDatePlaceholder = computed(() => {
  const placeholderMap: Record<string, string> = {
    measure: '请选择量房时间',
    visit: '请选择到店时间',
    home_visit: '请选择拜访时间'
  }
  return placeholderMap[props.formData.subStage] || '请选择到店时间'
})

// 表单验证规则
const formRules: FormRules = {
  customerId: {
    required: true,
    type: 'number',
    message: '请选择客户',
    trigger: 'change'
  },
  subStage: {
    required: true,
    message: '请选择子阶段',
    trigger: 'change'
  },
  designerId: {
    required: true,
    type: 'number',
    message: '请选择设计师',
    trigger: 'change'
  },
  type: {
    required: true,
    message: '请选择跟进方式',
    trigger: 'change'
  },
  status: {
    required: true,
    message: '请选择跟进状态',
    trigger: 'change'
  },
  followTime: {
    required: true,
    type: 'number',
    message: '请选择跟进时间',
    trigger: 'change'
  },
  visitDate: {
    required: true,
    type: 'number',
    message: '请选择时间',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入跟进内容',
    trigger: 'blur'
  }
}

// 方法
const handleSubStageChange = (value: string) => {
  // 根据子阶段自动设置跟进方式
  const typeMap: Record<string, string> = {
    measure: 'measure',
    visit: 'visit',
    home_visit: 'home_visit'
  }
  if (typeMap[value]) {
    props.formData.type = typeMap[value]
  }
}

const handleDesignerChange = (value: number | null) => {
  if (value && props.designerOptions) {
    const designer = props.designerOptions.find(item => item.value === value)
    if (designer) {
      props.formData.designer = designer.label
    }
  } else {
    props.formData.designer = ''
  }
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newValue) => {
    emit('update:formData', newValue)
  },
  { deep: true }
)

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.visit-stage-form {
  padding: 16px 0;
}

.measure-fields,
.visit-fields,
.home-visit-fields {
  margin: 20px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #52c41a;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-date-picker) {
  border-radius: 6px;
}

:deep(.n-divider .n-divider__title) {
  font-weight: 600;
}

:deep(.n-checkbox-group) {
  width: 100%;
}

:deep(.n-rate) {
  font-size: 18px;
}
</style>