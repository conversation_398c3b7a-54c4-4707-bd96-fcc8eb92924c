<template>
  <div class="site-detail">
    <n-card title="工地详情" :bordered="false">
      <template #header-extra>
        <n-space>
          <n-button @click="$router.back()">
            <template #icon>
              <n-icon><ArrowBackOutline /></n-icon>
            </template>
            返回
          </n-button>
          <n-button type="primary" @click="handleEdit">
            <template #icon>
              <n-icon><CreateOutline /></n-icon>
            </template>
            编辑
          </n-button>
        </n-space>
      </template>

      <n-spin :show="loading">
        <div v-if="siteData" class="site-info">
          <!-- 基本信息 -->
          <n-card title="基本信息" class="mb-4">
            <n-descriptions :column="2" label-placement="left">
              <n-descriptions-item label="工地名称">
                {{ siteData.name }}
              </n-descriptions-item>
              <n-descriptions-item label="工地状态">
                <n-tag :type="getStatusType(siteData.status)">
                  {{ getStatusText(siteData.status) }}
                </n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="工地地址">
                {{ siteData.address }}
              </n-descriptions-item>
              <n-descriptions-item label="负责人">
                {{ siteData.manager }}
              </n-descriptions-item>
              <n-descriptions-item label="联系电话">
                {{ siteData.phone }}
              </n-descriptions-item>
              <n-descriptions-item label="创建时间">
                {{ siteData.createdAt }}
              </n-descriptions-item>
              <n-descriptions-item label="更新时间">
                {{ siteData.updatedAt }}
              </n-descriptions-item>
              <n-descriptions-item label="备注" :span="2">
                {{ siteData.remark || '无' }}
              </n-descriptions-item>
            </n-descriptions>
          </n-card>

          <!-- 工单统计 -->
          <n-card title="工单统计" class="mb-4">
            <n-grid :cols="4" :x-gap="16">
              <n-grid-item>
                <n-statistic label="总工单数" :value="statistics.total">
                  <template #prefix>
                    <n-icon color="#2080f0"><DocumentTextOutline /></n-icon>
                  </template>
                </n-statistic>
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="待处理" :value="statistics.pending">
                  <template #prefix>
                    <n-icon color="#f0a020"><TimeOutline /></n-icon>
                  </template>
                </n-statistic>
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="处理中" :value="statistics.processing">
                  <template #prefix>
                    <n-icon color="#2080f0"><PlayOutline /></n-icon>
                  </template>
                </n-statistic>
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="已完成" :value="statistics.completed">
                  <template #prefix>
                    <n-icon color="#18a058"><CheckmarkOutline /></n-icon>
                  </template>
                </n-statistic>
              </n-grid-item>
            </n-grid>
          </n-card>

          <!-- 最近工单 -->
          <n-card title="最近工单">
            <n-data-table
              :columns="ticketColumns"
              :data="recentTickets"
              :pagination="false"
              size="small"
            />
            <div class="mt-4 text-center">
              <n-button text @click="viewAllTickets">
                查看全部工单
                <template #icon>
                  <n-icon><ArrowForwardOutline /></n-icon>
                </template>
              </n-button>
            </div>
          </n-card>
        </div>
      </n-spin>
    </n-card>

    <!-- 编辑弹窗 -->
    <n-modal v-model:show="showEditModal" preset="dialog" title="编辑工地信息">
      <n-form
        ref="formRef"
        :model="editForm"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="工地名称" path="name">
          <n-input v-model:value="editForm.name" placeholder="请输入工地名称" />
        </n-form-item>
        <n-form-item label="工地地址" path="address">
          <n-input v-model:value="editForm.address" placeholder="请输入工地地址" />
        </n-form-item>
        <n-form-item label="负责人" path="manager">
          <n-input v-model:value="editForm.manager" placeholder="请输入负责人" />
        </n-form-item>
        <n-form-item label="联系电话" path="phone">
          <n-input v-model:value="editForm.phone" placeholder="请输入联系电话" />
        </n-form-item>
        <n-form-item label="工地状态" path="status">
          <n-select
            v-model:value="editForm.status"
            placeholder="请选择工地状态"
            :options="statusOptions"
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="editForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="handleSave" :loading="saving">
            保存
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NCard, NButton, NSpace, NIcon, NSpin, NDescriptions, NDescriptionsItem,
  NTag, NGrid, NGridItem, NStatistic, NDataTable, NModal, NForm, NFormItem,
  NInput, NSelect, useMessage
} from 'naive-ui'
import {
  ArrowBackOutline, CreateOutline, DocumentTextOutline, TimeOutline,
  PlayOutline, CheckmarkOutline, ArrowForwardOutline
} from '@vicons/ionicons5'
import ActionButtons from '@/components/common/ActionButtons.vue'

interface SiteData {
  id: string
  name: string
  address: string
  manager: string
  phone: string
  status: string
  remark?: string
  createdAt: string
  updatedAt: string
}

interface TicketRecord {
  id: string
  title: string
  status: string
  priority: string
  createdAt: string
  assignee: string
}

const route = useRoute()
const router = useRouter()
const message = useMessage()

const loading = ref(false)
const saving = ref(false)
const showEditModal = ref(false)
const siteData = ref<SiteData | null>(null)

const statusOptions = [
  { label: '规划中', value: 'planning' },
  { label: '施工中', value: 'construction' },
  { label: '已完工', value: 'completed' },
  { label: '暂停', value: 'paused' }
]

const editForm = reactive({
  name: '',
  address: '',
  manager: '',
  phone: '',
  status: '',
  remark: ''
})

const formRules = {
  name: { required: true, message: '请输入工地名称', trigger: 'blur' },
  address: { required: true, message: '请输入工地地址', trigger: 'blur' },
  manager: { required: true, message: '请输入负责人', trigger: 'blur' },
  phone: { required: true, message: '请输入联系电话', trigger: 'blur' },
  status: { required: true, message: '请选择工地状态', trigger: 'change' }
}

const statistics = reactive({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0
})

const recentTickets = ref<TicketRecord[]>([])

const ticketColumns = [
  { title: '工单标题', key: 'title', width: 200 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: TicketRecord) => {
      const statusMap = {
        pending: { text: '待处理', type: 'warning' },
        processing: { text: '处理中', type: 'info' },
        completed: { text: '已完成', type: 'success' },
        closed: { text: '已关闭', type: 'default' }
      }
      const status = statusMap[row.status as keyof typeof statusMap]
      return h(NTag, { type: status?.type as any }, () => status?.text || row.status)
    }
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    render: (row: TicketRecord) => {
      const priorityMap = {
        high: { text: '高', type: 'error' },
        medium: { text: '中', type: 'warning' },
        low: { text: '低', type: 'info' }
      }
      const priority = priorityMap[row.priority as keyof typeof priorityMap]
      return h(NTag, { type: priority?.type as any, size: 'small' }, () => priority?.text || row.priority)
    }
  },
  { title: '负责人', key: 'assignee', width: 120 },
  { title: '创建时间', key: 'createdAt', width: 180 },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row: TicketRecord) => {
      return h(ActionButtons, {
        showEdit: false,
        showDelete: false,
        viewText: '查看',
        onView: () => viewTicket(row)
      })
    }
  }
]

const getStatusType = (status: string): 'info' | 'warning' | 'success' | 'error' | 'default' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'error'> = {
    planning: 'info',
    construction: 'warning',
    completed: 'success',
    paused: 'error'
  }
  return typeMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    planning: '规划中',
    construction: '施工中',
    completed: '已完工',
    paused: '暂停'
  }
  return textMap[status as keyof typeof textMap] || status
}

const loadSiteData = async () => {
  loading.value = true
  try {
    const siteId = route.params.id as string
    
    // 模拟数据
    const mockSite: SiteData = {
      id: siteId,
      name: '阳光花园小区',
      address: '北京市朝阳区建国路88号',
      manager: '张三',
      phone: '13800138001',
      status: 'construction',
      remark: '高端住宅项目，预计2024年底完工',
      createdAt: '2024-01-15 10:30:00',
      updatedAt: '2024-01-20 14:20:00'
    }
    
    const mockStatistics = {
      total: 25,
      pending: 5,
      processing: 8,
      completed: 12
    }
    
    const mockTickets: TicketRecord[] = [
      {
        id: '1',
        title: '电梯故障维修',
        status: 'processing',
        priority: 'high',
        createdAt: '2024-01-20 09:30:00',
        assignee: '李工程师'
      },
      {
        id: '2',
        title: '水管漏水处理',
        status: 'pending',
        priority: 'medium',
        createdAt: '2024-01-19 14:20:00',
        assignee: '王师傅'
      },
      {
        id: '3',
        title: '门禁系统升级',
        status: 'completed',
        priority: 'low',
        createdAt: '2024-01-18 11:15:00',
        assignee: '赵技术员'
      }
    ]
    
    siteData.value = mockSite
    Object.assign(statistics, mockStatistics)
    recentTickets.value = mockTickets
  } catch (error) {
    message.error('加载工地信息失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  if (siteData.value) {
    Object.assign(editForm, siteData.value)
    showEditModal.value = true
  }
}

const handleSave = async () => {
  saving.value = true
  try {
    // 这里调用保存API
    message.success('保存成功')
    showEditModal.value = false
    await loadSiteData()
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const viewTicket = (ticket: TicketRecord) => {
  router.push(`/afterservice/process/${ticket.id}`)
}

const viewAllTickets = () => {
  router.push(`/afterservice/tickets?siteId=${route.params.id}`)
}

onMounted(() => {
  loadSiteData()
})
</script>

<style scoped>
.site-detail {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.text-center {
  text-align: center;
}

.site-info {
  min-height: 400px;
}
</style>