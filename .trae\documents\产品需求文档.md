# YYSH客户管理系统产品需求文档

## 1. 产品概述

YYSH客户管理系统是一个面向销售团队的客户关系管理平台，旨在帮助企业高效管理客户信息、跟进销售流程、分析业务数据。
系统将从传统HTML/JS架构升级到Vue 3 + TypeScript + Naive UI现代化技术栈，提供更好的用户体验和开发效率。
目标是打造一个功能完善、界面友好、性能优异的现代化CRM系统，提升销售团队的工作效率和客户转化率。

## 2. 核心功能

### 2.1 用户角色

| 角色    | 注册方式    | 核心权限                 |
| ----- | ------- | -------------------- |
| 系统管理员 | 系统预设账号  | 系统配置、用户管理、数据统计、权限分配  |
| 销售经理  | 管理员创建账号 | 团队管理、客户分配、业绩统计、数据分析  |
| 销售人员  | 管理员创建账号 | 客户管理、跟进记录、会议安排、个人统计  |
| 设计师   | 管理员创建账号 | 查看客户信息、参与会议记录、设计方案管理 |

### 2.2 功能模块

我们的客户管理系统包含以下主要页面：

1. **登录页面**: 用户身份验证、企业微信登录集成
2. **仪表板首页**: 数据概览、待办事项、快捷操作、业绩图表
3. **客户管理页面**: 客户列表、客户详情、公海客户、跟进记录、待办事项
4. **会议管理页面**: 会议安排、会议记录、设计师营销人员参与会议和会议管理
5. **营销活动页面**: 活动列表、创建活动、进度跟进、数据分析、客户追踪
6. **微信管理页面**: 微信客户、浏览轨迹、分享记录、群组管理、消息管理
7. **数据分析页面**: 数据分析概览、销售统计、客户分析、客户价值分析、销售漏斗分析、转化率统计、业绩分析仪表板
8. **售后管理页面**: 工单列表、工单处理、工单提醒、工单时间线
9. **工地管理页面**: 工地列表、独立的工地管理模块
10. **有优合伙人页面**: 人员列表、数据分析、积分商城
11. **动态内容页面**: 公司动态、素材动态、案例展示
12. **系统管理页面**:基础设置、客户设置、积分管理&#x20;

### 2.3 页面详情

| 页面名称    | 模块名称   | 功能描述                         |
| ------- | ------ | ---------------------------- |
| 登录页面    | 用户认证   | 用户名密码登录、企业微信扫码登录、记住登录状态、密码重置 |
| 登录页面    | 安全验证   | 登录验证码、登录日志记录、异常登录提醒          |
| 仪表板首页   | 数据概览   | 显示客户总数、今日新增、跟进统计、成交数据等关键指标   |
| 仪表板首页   | 待办事项   | 显示今日待跟进客户、预约会议、逾期任务等提醒信息     |
| 仪表板首页   | 快捷操作   | 快速添加客户、创建跟进记录、安排会议等常用功能入口    |
| 仪表板首页   | 业绩图表   | 销售趋势图、客户来源分析、转化漏斗图等可视化数据     |
| 客户管理页面  | 客户列表   | 分页显示客户信息、多条件筛选、排序、批量操作       |
| 客户管理页面  | 客户详情   | 完整客户信息、跟进历史、会议记录、成交记录        |
| 客户管理页面  | 公海管理   | 客户入池规则、公海客户列表、客户领取、自动分配      |
| 客户管理页面  | 跟进记录   | 创建跟进记录、编辑记录、设置下次跟进时间、跟进提醒    |
| 客户管理页面  | 待办事项   | 显示跟进记录、筛选查询、跟进效果统计           |
| 会议管理页面  | 会议列表   | 显示会议安排、会议状态、参与人员、会议结果        |
| 会议管理页面  | 会议操作   | 创建会议、编辑会议、邀请设计师、会议提醒         |
| 营销活动页面  | 活动列表   | 显示营销活动、活动状态、参与客户、活动效果        |
| 营销活动页面  | 创建活动   | 创建活动、编辑活动、客户邀请、效果跟踪          |
| 营销活动页面  | 进度跟进   | 活动进度监控、执行状态跟踪、问题处理           |
| 营销活动页面  | 数据分析   | 活动参与率、客户转化率、ROI分析、活动对比       |
| 营销活动页面  | 客户追踪   | 客户行为追踪、互动记录、效果评估             |
| 微信管理页面  | 微信客户   | 微信客户信息管理、客户标签、互动记录           |
| 微信管理页面  | 浏览轨迹   | 客户浏览行为分析、页面访问统计、热点内容分析       |
| 微信管理页面  | 分享记录   | 内容分享统计、分享效果分析、传播路径追踪         |
| 微信管理页面  | 群组管理   | 微信群组管理、群成员统计、群活跃度分析          |
| 微信管理页面  | 消息管理   | 消息发送记录、消息效果统计、自动回复设置         |
| 数据分析页面  | 数据概览   | 综合数据仪表板、关键指标展示、趋势分析          |
| 数据分析页面  | 销售统计   | 个人业绩、团队业绩、目标完成度、排行榜          |
| 数据分析页面  | 客户分析   | 客户来源分析、客户价值分析、流失客户分析         |
| 数据分析页面  | 客户价值分析 | 客户生命周期价值、消费行为分析、价值分层         |
| 数据分析页面  | 销售漏斗分析 | 销售流程分析、转化率统计、瓶颈识别            |
| 数据分析页面  | 转化率统计  | 各阶段转化率、转化趋势、影响因素分析           |
| 数据分析页面  | 业绩仪表板  | 实时业绩监控、目标达成情况、预测分析           |
| 售后管理页面  | 工单列表   | 工单信息展示、状态管理、优先级设置            |
| 售后管理页面  | 工单处理   | 工单分配、处理流程、解决方案记录             |
| 售后管理页面  | 工单提醒   | 超时提醒、状态变更通知、处理进度跟踪           |
| 售后管理页面  | 工单时间线  | 工单处理历程、操作记录、时间节点追踪           |
| 工地管理页面  | 工地信息   | 工地基本信息、项目进度、人员配置             |
| 工地管理页面  | 进度管理   | 施工进度跟踪、里程碑管理、质量检查            |
| 有优合伙人页面 | 人员列表   | 合伙人信息管理、业绩统计、等级管理            |
| 有优合伙人页面 | 数据分析   | 合伙人业绩分析、收益统计、发展趋势            |
| 有优合伙人页面 | 积分商城   | 积分兑换、商品管理、订单处理               |
| 系统设置页面  | 基础设置   | 系统参数设置、业务规则配置、通知设置           |
| 系统设置页面  | 客户设置   | 客户来源、跟进类型、客户状态等基础数据管理        |
| 动态内容页面  | 公司动态   | 公司新闻发布、动态管理、内容编辑             |
| 动态内容页面  | 素材动态   | 营销素材管理、素材分类、使用统计             |
| 动态内容页面  | 案例展示   | 成功案例展示、案例分类、效果展示             |
| 系统管理页面  | 积分管理   | 积分规则设置、积分发放、积分统计             |

## 3. 核心流程

### 管理员流程

1. 系统登录 → 仪表板概览 → 查看系统整体运营状况
2. 系统设置 → 基础配置 → 客户设置 → 权限管理
3. 数据分析 → 查看团队业绩 → 生成业绩报表 → 制定销售策略
4. 系统管理 → 积分管理 → 有优合伙人管理

### 销售人员流程

1. 系统登录 → 仪表板首页 → 查看待办事项 → 处理今日跟进任务
2. 客户管理 → 客户列表 → 添加新客户 → 创建跟进记录
3. 会议管理 → 安排客户会议 → 记录会议内容
4. 营销活动 → 参与活动执行 → 跟踪客户参与情况
5. 数据分析 → 个人业绩统计 → 客户转化分析

### 售后服务流程

1. 售后管理 → 工单列表 → 处理客户问题
2. 工地管理 → 项目进度跟踪 → 质量管理
3. 工单处理 → 问题解决 → 客户回访

### 微信营销流程

1. 微信管理 → 微信客户管理 → 浏览轨迹分析
2. 动态内容 → 发布公司动态 → 案例展示
3. 分享记录 → 效果分析 → 优化营销策略

### 合伙人管理流程

1. 有优合伙人 → 人员管理 → 业绩统计
2. 积分商城 → 积分兑换 → 激励管理
3. 数据分析 → 合伙人效果评估

```mermaid
graph TD
    A[登录页面] --> B[仪表板首页]
    B --> C[客户管理]
    B --> D[会议管理]
    B --> E[营销活动]
    B --> F[微信管理]
    B --> G[数据分析]
    B --> H[售后管理]
    B --> I[工地管理]
    B --> J[有优合伙人]
    B --> K[系统设置]
    B --> L[动态内容]
    B --> M[系统管理]
    
    C --> C1[客户列表]
    C --> C2[客户详情]
    C --> C3[公海客户]
    C --> C4[跟进记录]
    C --> C5[待办事项]
    
    D --> D1[会议列表]
    
    E --> E1[活动列表]
    E --> E2[创建活动]
    E --> E3[进度跟进]
    E --> E4[数据分析]
    E --> E5[客户追踪]
    
    F --> F1[微信客户]
    F --> F2[浏览轨迹]
    F --> F3[分享记录]
    F --> F4[群组管理]
    F --> F5[消息管理]
    
    G --> G1[数据概览]
    G --> G2[销售统计]
    G --> G3[客户分析]
    G --> G4[客户价值分析]
    G --> G5[销售漏斗分析]
    G --> G6[转化率统计]
    G --> G7[业绩仪表板]
    
    H --> H1[工单列表]
    H --> H2[工单处理]
    H --> H3[工单提醒]
    H --> H4[工单时间线]
    
    J --> J1[人员列表]
    J --> J2[数据分析]
    J --> J3[积分商城]
    
    K --> K1[基础设置]
    K --> K2[客户设置]
    K --> K3[积分管理]
    
    L --> L1[公司动态]
    L --> L2[素材动态]
    L --> L3[案例展示]

```

## 4. 用户界面设计

### 4.1 设计风格

* **主色调**: 主色 #1890ff (蓝色)，辅助色 #52c41a (绿色)，警告色 #faad14 (橙色)，错误色 #f5222d (红色)

* **按钮样式**: 圆角按钮设计，主要按钮使用实心样式，次要按钮使用边框样式

* **字体**: 系统默认字体 -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto，主要文字14px，标题16-20px

* **布局风格**: 左侧导航 + 顶部的经典后台布局，卡片式内容展示，表格和表单结合

* **图标风格**: 使用Naive UI内置图标库，线性图标风格，统一视觉效果

### 4.2 页面设计概览

| 页面名称   | 模块名称 | UI元素                                                         |
| ------ | ---- | ------------------------------------------------------------ |
| 登录页面   | 登录表单 | 居中卡片布局，品牌Logo，用户名密码输入框，登录按钮，企业微信登录按钮                         |
| 登录页面   | 背景设计 | 渐变背景色 linear-gradient(135deg, #667eea 0%, #764ba2 100%)，简洁现代 |
| 仪表板首页  | 统计卡片 | 4列网格布局，每个卡片显示数字、标题、趋势图标，使用不同主题色                              |
| 仪表板首页  | 图表区域 | 2列布局，左侧折线图显示销售趋势，右侧饼图显示客户来源分布                                |
| 仪表板首页  | 待办列表 | 列表样式，每项显示客户名称、任务类型、时间，支持快速操作                                 |
| 客户管理页面 | 搜索筛选 | 顶部搜索栏，下拉筛选器（状态、来源、负责人），日期范围选择器                               |
| 客户管理页面 | 客户表格 | 分页表格，显示客户基本信息，操作列包含查看、编辑、删除按钮                                |
| 客户管理页面 | 客户详情 | 抽屉式侧边栏，标签页切换（基本信息、跟进记录、会议记录）                                 |
| 跟进记录页面 | 记录表单 | 模态框表单，客户选择器、跟进类型下拉、富文本编辑器、日期时间选择器                            |
| 跟进记录页面 | 记录列表 | 时间线样式展示，每条记录显示时间、类型、内容、结果                                    |
| 员工管理页面 | 员工表格 | 表格布局，头像、姓名、角色、状态、操作列，支持批量操作                                  |
| 员工管理页面 | 权限配置 | 树形权限选择器，角色标签，权限说明文字                                          |
| 数据分析页面 | 图表展示 | 响应式网格布局，多种图表类型（柱状图、折线图、饼图、雷达图）                               |
| 数据分析页面 | 筛选控件 | 顶部筛选栏，时间范围、人员选择、数据维度切换                                       |

### 4.3 响应式设计

系统采用桌面优先的响应式设计策略，主要面向PC端使用，同时兼容平板设备。

* **桌面端** (≥1200px): 完整功能展示，左侧导航固定，内容区域自适应

* **平板端** (768px-1199px): 左侧导航可收缩，表格支持横向滚动，图表自适应缩放

* **移动端** (≤767px): 导航改为顶部抽屉式，表格改为卡片式展示，简化操作流程

触控优化：按钮最小点击区域44px，表单控件间距适中，支持手势操作。

## 5. 技术实现要点

### 5.1 前端技术栈

* **Vue 3**: 使用Composition API，提供更好的TypeScript支持和响应式系统

* **TypeScript**: 全面类型化，减少运行时错误，提升开发效率

* **Naive UI**: 现代化组件库，提供丰富的业务组件和主题定制

* **Pinia**: 状态管理，替代Vuex，提供更好的TypeScript支持

* **Vue Router 4**: 路由管理，支持路由守卫和权限控制

* **Axios**: HTTP客户端，统一API调用和请求拦截

* **Vite**: 构建工具，快速开发体验和热更新

* **ECharts**: 数据可视化图表库，支持多种图表类型

* **@vicons/ionicons5**: 图标库，提供丰富的图标资源

* **@vueuse/core**: Vue组合式工具库，提供常用的组合式函数

* **Supabase**: 后端即服务，提供数据库、认证和实时功能

### 5.2 开发工具链

* **ESLint + Prettier**: 代码规范和格式化

* **Vitest**: 单元测试框架

* **Playwright**: 端到端测试

* **TypeScript**: 类型检查和编译

### 5.3 后端架构

* **Node.js + Express**: 后端服务框架

* **Supabase PostgreSQL**: 数据库存储和管理

* **JWT**: 用户认证和授权

* **文件上传**: 支持多种文件类型上传

### 5.4 关键功能实现

#### 权限控制

* 基于角色的权限控制(RBAC)

* 路由级权限验证和导航守卫

* 按钮级权限控制和功能隐藏

* 数据权限隔离和用户数据安全

#### 数据可视化

* 使用ECharts图表库，支持多种图表类型

* 响应式图表设计，适配不同屏幕尺寸

* 实时数据更新和动态图表刷新

* 多维度数据分析和交互式图表

#### 微信集成

* 微信客户管理和数据同步

* 浏览轨迹追踪和行为分析

* 分享记录统计和传播效果分析

* 群组管理和消息推送

#### 售后服务

* 工单管理系统和流程控制

* 工地进度跟踪和项目管理

* 自动提醒和状态通知

* 时间线追踪和历史记录

#### 合伙人体系

* 合伙人等级管理和业绩统计

* 积分系统和奖励机制

* 积分商城和兑换功能

* 数据分析和效果评估

#### 性能优化

* 组件懒加载和代码分割

* 虚拟滚动和大数据处理

* 图片懒加载和资源优化

* 接口缓存策略和请求优化

#### 用户体验

* 主题切换(白天/夜晚模式)

* 加载状态提示和骨架屏

* 操作反馈和消息通知

* 错误处理和异常恢复

* 响应式设计和移动端适配

这个产品需求文档为YYSH客户管理系统的现代化升级提供了完整的功能规划和设计指导，确保新系统能够满足业务需求并提供优秀的用户体验。
