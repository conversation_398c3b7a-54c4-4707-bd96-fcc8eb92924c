const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件（临时禁用）
// router.use(authenticateToken);

// 获取营销链接列表
router.get('/links', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { 
      page = 1, 
      pageSize = 10, 
      keyword, 
      type, 
      status, 
      userId,
      startDate,
      endDate 
    } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (ml.title LIKE ? OR ml.description LIKE ? OR ml.link_code LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm, searchTerm);
    }

    // 类型筛选
    if (type) {
      whereClause += ' AND ml.type = ?';
      whereParams.push(type);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND ml.status = ?';
      whereParams.push(status);
    }

    // 创建人筛选
    if (userId) {
      whereClause += ' AND ml.created_by = ?';
      whereParams.push(userId);
    }

    // 日期范围筛选
    if (startDate) {
      whereClause += ' AND ml.created_at >= ?';
      whereParams.push(startDate);
    }
    if (endDate) {
      whereClause += ' AND ml.created_at <= ?';
      whereParams.push(endDate + ' 23:59:59');
    }

    const sql = `
      SELECT 
        ml.*,
        u.name as creator_name,
        u.position as creator_position,
        COUNT(lal.id) as access_count
      FROM marketing_links ml
      LEFT JOIN users u ON ml.created_by = u.id
      LEFT JOIN link_access_logs lal ON ml.id = lal.link_id
      WHERE ${whereClause}
      GROUP BY ml.id
      ORDER BY ml.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countSql = `
      SELECT COUNT(*) as total
      FROM marketing_links ml
      LEFT JOIN users u ON ml.created_by = u.id
      WHERE ${whereClause}
    `;

    const offset = (page - 1) * pageSize;
    const links = await db.query(sql, [...whereParams, parseInt(pageSize), offset]);
    const countResult = await db.query(countSql, whereParams);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: links.rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取营销链接列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取营销链接列表失败',
      data: []
    });
  }
});

// 获取营销链接详情
router.get('/links/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const sql = `
      SELECT 
        ml.*,
        u.name as creator_name,
        u.position as creator_position
      FROM marketing_links ml
      LEFT JOIN users u ON ml.created_by = u.id
      WHERE ml.id = ?
    `;

    const result = await db.query(sql, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '营销链接不存在'
      });
    }

    const link = result.rows[0];

    // 获取访问记录
    const accessLogsSql = `
      SELECT 
        lal.*,
        c.name as customer_name,
        c.phone as customer_phone
      FROM link_access_logs lal
      LEFT JOIN customers c ON lal.customer_id = c.id
      WHERE lal.link_id = ?
      ORDER BY lal.created_at DESC
      LIMIT 50
    `;

    const accessLogsResult = await db.query(accessLogsSql, [id]);
    link.accessLogs = accessLogsResult.rows;

    // 获取统计数据
    const statsSql = `
      SELECT 
        COUNT(*) as total_access,
        COUNT(DISTINCT lal.customer_id) as unique_customers,
        COUNT(DISTINCT DATE(lal.created_at)) as active_days
      FROM link_access_logs lal
      WHERE lal.link_id = ?
    `;

    const statsResult = await db.query(statsSql, [id]);
    link.stats = statsResult.rows[0];

    res.json({
      success: true,
      data: link
    });

  } catch (error) {
    console.error('获取营销链接详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取营销链接详情失败'
    });
  }
});

// 创建营销链接
router.post('/links', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { 
      title, 
      description, 
      type, 
      target_url, 
      link_code,
      expire_at,
      max_access_count,
      settings 
    } = req.body;

    // 验证必填字段
    if (!title || !type || !target_url) {
      return res.status(400).json({
        success: false,
        message: '标题、类型和目标链接为必填项'
      });
    }

    // 生成链接码（如果未提供）
    let finalLinkCode = link_code;
    if (!finalLinkCode) {
      finalLinkCode = Math.random().toString(36).substring(2, 10);
    }

    // 检查链接码是否已存在
    const codeCheck = await db.query('SELECT id FROM marketing_links WHERE link_code = ?', [finalLinkCode]);
    if (codeCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '链接码已存在'
      });
    }

    const linkData = {
      title,
      description: description || null,
      type,
      target_url,
      link_code: finalLinkCode,
      expire_at: expire_at || null,
      max_access_count: max_access_count || null,
      settings: settings ? JSON.stringify(settings) : null,
      status: 'active',
      created_by: 1, // 临时使用默认用户ID
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const insertSql = `
      INSERT INTO marketing_links (
        title, description, type, target_url, link_code, expire_at,
        max_access_count, settings, status, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      linkData.title,
      linkData.description,
      linkData.type,
      linkData.target_url,
      linkData.link_code,
      linkData.expire_at,
      linkData.max_access_count,
      linkData.settings,
      linkData.status,
      linkData.created_by,
      linkData.created_at,
      linkData.updated_at
    ];

    const result = await db.query(insertSql, insertParams);

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'create',
        'marketing_link',
        result.lastID,
        `创建营销链接：${title}`,
        new Date().toISOString()
      ]
    );

    res.status(201).json({
      success: true,
      message: '营销链接创建成功',
      data: {
        id: result.lastID,
        ...linkData
      }
    });

  } catch (error) {
    console.error('创建营销链接错误:', error);
    res.status(500).json({
      success: false,
      message: '创建营销链接失败'
    });
  }
});

// 更新营销链接
router.put('/links/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { 
      title, 
      description, 
      type, 
      target_url, 
      expire_at,
      max_access_count,
      settings,
      status 
    } = req.body;

    // 检查营销链接是否存在
    const linkCheck = await db.query('SELECT * FROM marketing_links WHERE id = ?', [id]);
    if (linkCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '营销链接不存在'
      });
    }

    // 构建更新数据
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (title) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (type) updateData.type = type;
    if (target_url) updateData.target_url = target_url;
    if (expire_at !== undefined) updateData.expire_at = expire_at;
    if (max_access_count !== undefined) updateData.max_access_count = max_access_count;
    if (settings !== undefined) updateData.settings = settings ? JSON.stringify(settings) : null;
    if (status) updateData.status = status;

    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    const updateSql = `UPDATE marketing_links SET ${updateFields} WHERE id = ?`;
    await db.query(updateSql, [...updateValues, id]);

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'update',
        'marketing_link',
        id,
        `更新营销链接：${title || linkCheck.rows[0].title}`,
        new Date().toISOString()
      ]
    );

    res.json({
      success: true,
      message: '营销链接更新成功'
    });

  } catch (error) {
    console.error('更新营销链接错误:', error);
    res.status(500).json({
      success: false,
      message: '更新营销链接失败'
    });
  }
});

// 删除营销链接（软删除）
router.delete('/links/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查营销链接是否存在
    const linkCheck = await db.query('SELECT * FROM marketing_links WHERE id = ?', [id]);
    if (linkCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '营销链接不存在'
      });
    }

    const link = linkCheck.rows[0];

    // 软删除
    await db.query(
      'UPDATE marketing_links SET status = ?, deleted_at = ?, updated_at = ? WHERE id = ?',
      ['deleted', new Date().toISOString(), new Date().toISOString(), id]
    );

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'delete',
        'marketing_link',
        id,
        `删除营销链接：${link.title}`,
        new Date().toISOString()
      ]
    );

    res.json({
      success: true,
      message: '营销链接删除成功'
    });

  } catch (error) {
    console.error('删除营销链接错误:', error);
    res.status(500).json({
      success: false,
      message: '删除营销链接失败'
    });
  }
});

// 获取链接访问记录
router.get('/links/:id/access-logs', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { page = 1, pageSize = 20 } = req.query;

    const sql = `
      SELECT 
        lal.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email
      FROM link_access_logs lal
      LEFT JOIN customers c ON lal.customer_id = c.id
      WHERE lal.link_id = ?
      ORDER BY lal.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countSql = `
      SELECT COUNT(*) as total
      FROM link_access_logs lal
      WHERE lal.link_id = ?
    `;

    const offset = (page - 1) * pageSize;
    const logs = await db.query(sql, [id, parseInt(pageSize), offset]);
    const countResult = await db.query(countSql, [id]);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: logs.rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取链接访问记录错误:', error);
    res.status(500).json({
      success: false,
      message: '获取链接访问记录失败',
      data: []
    });
  }
});

// 获取营销统计数据
router.get('/stats/overview', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { startDate, endDate } = req.query;

    let dateFilter = '';
    let dateParams = [];

    if (startDate && endDate) {
      dateFilter = 'AND lal.created_at BETWEEN ? AND ?';
      dateParams = [startDate, endDate + ' 23:59:59'];
    }

    const stats = await Promise.all([
      // 总链接数
      db.query('SELECT COUNT(*) as total FROM marketing_links WHERE status != "deleted"'),
      // 活跃链接数
      db.query('SELECT COUNT(*) as active FROM marketing_links WHERE status = "active"'),
      // 总访问次数
      db.query(`SELECT COUNT(*) as total FROM link_access_logs lal WHERE 1=1 ${dateFilter}`, dateParams),
      // 独立访客数
      db.query(`SELECT COUNT(DISTINCT customer_id) as unique FROM link_access_logs lal WHERE customer_id IS NOT NULL ${dateFilter}`, dateParams),
      // 各类型链接统计
      db.query(`
        SELECT 
          ml.type,
          COUNT(ml.id) as link_count,
          COUNT(lal.id) as access_count
        FROM marketing_links ml
        LEFT JOIN link_access_logs lal ON ml.id = lal.link_id ${dateFilter ? 'AND lal.created_at BETWEEN ? AND ?' : ''}
        WHERE ml.status != "deleted"
        GROUP BY ml.type
      `, dateFilter ? dateParams : []),
      // 每日访问趋势（最近7天）
      db.query(`
        SELECT 
          DATE(lal.created_at) as date,
          COUNT(*) as access_count,
          COUNT(DISTINCT lal.customer_id) as unique_visitors
        FROM link_access_logs lal
        WHERE lal.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(lal.created_at)
        ORDER BY date ASC
      `)
    ]);

    res.json({
      success: true,
      data: {
        totalLinks: stats[0].rows[0].total,
        activeLinks: stats[1].rows[0].active,
        totalAccess: stats[2].rows[0].total,
        uniqueVisitors: stats[3].rows[0].unique,
        typeStats: stats[4].rows,
        dailyTrend: stats[5].rows
      }
    });

  } catch (error) {
    console.error('获取营销统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取营销统计失败'
    });
  }
});

module.exports = router;