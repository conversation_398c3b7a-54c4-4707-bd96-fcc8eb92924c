#!/usr/bin/env python3
"""
添加users表缺少的字段
"""

import pymysql
from app.config import settings

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=settings.database.host,
        port=settings.database.port,
        user=settings.database.username,
        password=settings.database.password,
        database=settings.database.database,
        charset='utf8mb4'
    )

def add_missing_fields():
    """添加缺少的字段"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 需要添加的字段
    alter_statements = [
        "ALTER TABLE users ADD COLUMN last_login DATETIME DEFAULT NULL COMMENT '最后登录时间'",
        "ALTER TABLE users ADD COLUMN gender ENUM('male', 'female', 'other') DEFAULT NULL COMMENT '性别'",
        "ALTER TABLE users ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL COMMENT '员工编号'",
        "ALTER TABLE users ADD COLUMN birthday DATE DEFAULT NULL COMMENT '生日'",
        "ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证'",
        "ALTER TABLE users ADD COLUMN nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称'",
        "ALTER TABLE users ADD COLUMN hire_date DATE DEFAULT NULL COMMENT '入职日期'",
        "ALTER TABLE users ADD COLUMN notes TEXT DEFAULT NULL COMMENT '备注'",
        "ALTER TABLE users ADD COLUMN login_count INT DEFAULT 0 COMMENT '登录次数'",
        "ALTER TABLE users ADD COLUMN work_phone VARCHAR(20) DEFAULT NULL COMMENT '工作电话'",
        "ALTER TABLE users ADD COLUMN real_name VARCHAR(100) DEFAULT NULL COMMENT '真实姓名'",
        "ALTER TABLE users ADD COLUMN address TEXT DEFAULT NULL COMMENT '地址'",
        "ALTER TABLE users ADD COLUMN avatar VARCHAR(500) DEFAULT NULL COMMENT '头像URL'",
        "ALTER TABLE users ADD COLUMN position VARCHAR(100) DEFAULT NULL COMMENT '职位'"
    ]
    
    try:
        print("=== 开始添加缺少的字段 ===")
        
        for i, statement in enumerate(alter_statements, 1):
            try:
                print(f"[{i}/{len(alter_statements)}] 执行: {statement}")
                cursor.execute(statement)
                conn.commit()
                print("  ✅ 成功")
            except pymysql.err.OperationalError as e:
                if "Duplicate column name" in str(e):
                    print("  ⚠️  字段已存在，跳过")
                else:
                    print(f"  ❌ 失败: {e}")
                    raise
            except Exception as e:
                print(f"  ❌ 失败: {e}")
                raise
        
        print("\n=== 字段添加完成 ===")
        
        # 验证字段是否添加成功
        print("\n=== 验证表结构 ===")
        cursor.execute("DESCRIBE users")
        columns = cursor.fetchall()
        
        print("当前users表字段:")
        for column in columns:
            print(f"  {column[0]}: {column[1]}")
        
        print(f"\n✅ 总共 {len(columns)} 个字段")
        
    except Exception as e:
        print(f"❌ 添加字段过程中出现错误: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    try:
        add_missing_fields()
        print("\n🎉 所有缺少的字段已成功添加！")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()