"""数据库会话管理器

提供异步数据库会话管理、事务处理和连接池管理
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, Any, Dict, List
from uuid import uuid4

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import sessionmaker

from app.database import async_session_factory, engine
from app.models.base import BaseModel

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.session_factory = async_session_factory
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取数据库会话上下文管理器
        
        Yields:
            AsyncSession: 异步数据库会话
        """
        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {e}")
                raise
            finally:
                await session.close()
    
    @asynccontextmanager
    async def get_transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取事务会话上下文管理器
        
        Yields:
            AsyncSession: 异步数据库会话（事务模式）
        """
        async with self.session_factory() as session:
            async with session.begin():
                try:
                    yield session
                except Exception as e:
                    await session.rollback()
                    logger.error(f"Transaction error: {e}")
                    raise
    
    async def execute_raw_sql(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        执行原生SQL语句
        
        Args:
            sql: SQL语句
            params: 参数字典
            
        Returns:
            执行结果
        """
        async with self.get_session() as session:
            try:
                result = await session.execute(text(sql), params or {})
                await session.commit()
                return result
            except SQLAlchemyError as e:
                await session.rollback()
                logger.error(f"Raw SQL execution error: {e}")
                raise
    
    async def execute_raw_query(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        执行原生查询SQL
        
        Args:
            sql: SQL查询语句
            params: 参数字典
            
        Returns:
            查询结果列表
        """
        async with self.get_session() as session:
            try:
                result = await session.execute(text(sql), params or {})
                rows = result.fetchall()
                # 转换为字典列表
                columns = result.keys()
                return [dict(zip(columns, row)) for row in rows]
            except SQLAlchemyError as e:
                logger.error(f"Raw query execution error: {e}")
                raise
    
    async def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            健康检查结果
        """
        try:
            async with self.get_session() as session:
                # 执行简单查询测试连接
                result = await session.execute(text("SELECT 1 as test"))
                test_value = result.scalar()
                
                # 获取数据库版本信息
                version_result = await session.execute(text("SELECT VERSION() as version"))
                version = version_result.scalar()
                
                return {
                    "status": "healthy",
                    "test_query": test_value == 1,
                    "database_version": version,
                    "connection_pool_size": engine.pool.size(),
                    "checked_in_connections": engine.pool.checkedin(),
                    "checked_out_connections": engine.pool.checkedout(),
                    "overflow_connections": engine.pool.overflow(),
                    "invalid_connections": engine.pool.invalidated()
                }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def create_tables(self) -> bool:
        """
        创建所有数据库表
        
        Returns:
            是否成功创建
        """
        try:
            async with engine.begin() as conn:
                await conn.run_sync(BaseModel.metadata.create_all)
            logger.info("Database tables created successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            return False
    
    async def drop_tables(self) -> bool:
        """
        删除所有数据库表
        
        Returns:
            是否成功删除
        """
        try:
            async with engine.begin() as conn:
                await conn.run_sync(BaseModel.metadata.drop_all)
            logger.info("Database tables dropped successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to drop tables: {e}")
            return False
    
    async def reset_database(self) -> bool:
        """
        重置数据库（删除并重新创建所有表）
        
        Returns:
            是否成功重置
        """
        try:
            # 先删除所有表
            await self.drop_tables()
            # 再创建所有表
            await self.create_tables()
            logger.info("Database reset successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reset database: {e}")
            return False
    
    async def backup_table(
        self,
        table_name: str,
        backup_name: Optional[str] = None
    ) -> bool:
        """
        备份指定表
        
        Args:
            table_name: 表名
            backup_name: 备份表名（可选）
            
        Returns:
            是否成功备份
        """
        if not backup_name:
            backup_name = f"{table_name}_backup_{uuid4().hex[:8]}"
        
        try:
            sql = f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}"
            await self.execute_raw_sql(sql)
            logger.info(f"Table {table_name} backed up to {backup_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to backup table {table_name}: {e}")
            return False
    
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            表信息字典
        """
        try:
            # 获取表结构信息
            structure_sql = f"""
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_KEY,
                EXTRA
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}' 
            AND TABLE_SCHEMA = DATABASE()
            ORDER BY ORDINAL_POSITION
            """
            
            structure = await self.execute_raw_query(structure_sql)
            
            # 获取表统计信息
            stats_sql = f"""
            SELECT 
                COUNT(*) as row_count,
                AVG_ROW_LENGTH as avg_row_length,
                DATA_LENGTH as data_length,
                INDEX_LENGTH as index_length
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = '{table_name}' 
            AND TABLE_SCHEMA = DATABASE()
            """
            
            stats = await self.execute_raw_query(stats_sql)
            
            return {
                "table_name": table_name,
                "structure": structure,
                "statistics": stats[0] if stats else {},
                "status": "success"
            }
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            return {
                "table_name": table_name,
                "error": str(e),
                "status": "error"
            }
    
    async def optimize_table(self, table_name: str) -> bool:
        """
        优化指定表
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功优化
        """
        try:
            sql = f"OPTIMIZE TABLE {table_name}"
            await self.execute_raw_sql(sql)
            logger.info(f"Table {table_name} optimized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to optimize table {table_name}: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 事务装饰器
def transactional(func):
    """
    事务装饰器，自动处理事务提交和回滚
    """
    async def wrapper(*args, **kwargs):
        async with db_manager.get_transaction() as session:
            # 将session注入到kwargs中
            kwargs['db'] = session
            return await func(*args, **kwargs)
    return wrapper


# 异常处理装饰器
def handle_db_errors(func):
    """
    数据库异常处理装饰器
    """
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except IntegrityError as e:
            logger.error(f"Database integrity error in {func.__name__}: {e}")
            raise ValueError(f"数据完整性错误: {e.orig}")
        except SQLAlchemyError as e:
            logger.error(f"Database error in {func.__name__}: {e}")
            raise RuntimeError(f"数据库操作错误: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            raise
    return wrapper