import type { Customer } from '@/api/customerService'
import type { PoolCustomer } from '@/api/poolService'

// 客户来源选项
export const customerSources = [
  '线上推广',
  '朋友推荐',
  '电话营销',
  '展会活动',
  '官网咨询',
  '社交媒体',
  '合作伙伴',
  '老客户介绍'
]

// 客户等级选项
export const customerLevels = ['A', 'B', 'C', 'D'] as const

// 客户状态选项
export const customerStatuses = [
  'potential',
  'contacted', 
  'negotiating',
  'deal',
  'lost'
] as const

// 模拟客户数据
export const mockCustomers: Customer[] = [
  {
    id: 1,
    name: '张三',
    gender: 'male',
    phone: '13800138001',
    decorationType: 'fine',
    company: '阿里巴巴',
    position: '技术总监',
    source: 'online',
    level: 'A',
    status: 'contacted',
    assigned_to: 1,
    region: '浙江省杭州市',
    address: '杭州市西湖区文三路969号',
    remark: '技术背景深厚，决策影响力大',
    last_follow_time: '2024-01-15T10:30:00Z',


    tags: ['重点客户', '技术导向'],
    created_at: '2024-01-10T09:00:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: '李四',
    gender: 'female',
    phone: '13800138002',
    decorationType: 'rough',
    company: '腾讯科技',
    position: '产品经理',
    source: 'referral',
    level: 'A',
    status: 'negotiating',
    assigned_to: 2,
    region: '广东省深圳市',
    address: '深圳市南山区科技园',
    remark: '产品思维敏锐，沟通效率高',
    last_follow_time: '2024-01-14T16:20:00Z',


    tags: ['产品导向', '决策者'],
    created_at: '2024-01-08T14:30:00Z',
    updated_at: '2024-01-14T16:20:00Z'
  },
  {
    id: 3,
    name: '王五',
    gender: 'male',
    phone: '13800138003',
    decorationType: 'simple',
    company: '字节跳动',
    position: '运营总监',
    source: 'phone',
    level: 'B',
    status: 'potential',
    assigned_to: 1,
    region: '北京市朝阳区',
    address: '北京市朝阳区知春路',
    remark: '运营经验丰富，关注ROI',

    tags: ['运营导向'],
    created_at: '2024-01-12T11:15:00Z',
    updated_at: '2024-01-12T11:15:00Z'
  },
  {
    id: 4,
    name: '赵六',
    gender: 'male',
    phone: '13800138004',
    decorationType: 'simple',
    company: '美团',
    position: 'CTO',
    source: 'exhibition',
    level: 'A',
    status: 'deal',
    assigned_to: 3,
    region: '北京市海淀区',
    address: '北京市海淀区中关村',
    remark: '技术决策者，执行力强',
    last_follow_time: '2024-01-13T09:45:00Z',



    tags: ['重点客户', '已成交', 'CTO'],
    created_at: '2024-01-05T08:20:00Z',
    updated_at: '2024-01-13T09:45:00Z'
  },
  {
    id: 5,
    name: '孙七',
    gender: 'female',
    phone: '13800138005',
    decorationType: 'luxury',
    company: '滴滴出行',
    position: '业务总监',
    source: 'online',
    level: 'B',
    status: 'lost',
    assigned_to: 2,
    region: '北京市朝阳区',
    address: '北京市朝阳区望京',
    remark: '预算控制严格，价格敏感',
    last_follow_time: '2024-01-11T15:30:00Z',



    tags: ['预算敏感'],
    created_at: '2024-01-03T13:10:00Z',
    updated_at: '2024-01-11T15:30:00Z'
  },
  {
    id: 6,
    name: '周八',
    gender: 'male',
    phone: '13800138006',
    decorationType: 'rough',
    company: '京东集团',
    position: '采购经理',
    source: 'online',
    level: 'C',
    status: 'contacted',
    assigned_to: 1,
    region: '北京市大兴区',
    address: '北京市大兴区京东总部',
    remark: '采购流程规范，决策周期长',
    last_follow_time: '2024-01-16T12:00:00Z',

    
    tags: ['采购导向'],
    created_at: '2024-01-14T10:45:00Z',
    updated_at: '2024-01-16T12:00:00Z'
  },
  {
    id: 7,
    name: '吴九',
    gender: 'male',
    phone: '13800138007',
    decorationType: 'fine',
    company: '网易',
    position: '技术架构师',
    source: 'referral',
    level: 'B',
    status: 'negotiating',
    assigned_to: 3,
    region: '浙江省杭州市',
    address: '杭州市滨江区网易大厦',
    remark: '技术能力强，注重架构设计',
    last_follow_time: '2024-01-17T14:15:00Z',




    tags: ['技术导向', '架构师'],
    created_at: '2024-01-09T16:30:00Z',
    updated_at: '2024-01-17T14:15:00Z'
  },
  {
    id: 8,
    name: '郑十',
    gender: 'female',
    phone: '13800138008',
    decorationType: 'simple',
    company: '小米科技',
    position: '产品总监',
    source: 'referral',
    level: 'A',
    status: 'potential',
    assigned_to: 2,
    region: '北京市海淀区',
    address: '北京市海淀区小米科技园',
    remark: '产品嗅觉敏锐，决策果断',



    tags: ['重点客户', '老客户推荐'],
    created_at: '2024-01-16T09:20:00Z',
    updated_at: '2024-01-16T09:20:00Z'
  }
]

// 模拟公海客户数据
export const mockPoolCustomers: PoolCustomer[] = [
  {
    id: 101,
    name: '陈一',
    gender: 'male',
    phone: '13900139001',
    company: '华为技术',
    position: '解决方案专家',
    source: 'online',
    level: 'A',
    status: 'potential',
    region: '广东省深圳市',
    address: '深圳市龙岗区华为基地',
    remark: '解决方案经验丰富，技术要求高',


    tags: ['大客户', '解决方案'],
    pool_entry_time: '2024-01-18T08:00:00Z',
    pool_reason: '销售离职',
    created_at: '2024-01-02T10:30:00Z',
    updated_at: '2024-01-18T08:00:00Z'
  },
  {
    id: 102,
    name: '刘二',
    gender: 'male',
    phone: '13900139002',
    decorationType: 'fine',
    company: '中国移动',
    position: '项目经理',
    source: 'exhibition',
    level: 'B',
    status: 'contacted',
    region: '北京市朝阳区',
    address: '北京市朝阳区中国移动大厦',
    remark: '国企客户，决策周期较长',


    tags: ['国企', '项目导向'],
    pool_entry_time: '2024-01-17T15:30:00Z',
    pool_reason: '客户要求更换销售',
    created_at: '2024-01-06T14:20:00Z',
    updated_at: '2024-01-17T15:30:00Z'
  },
  {
    id: 103,
    name: '杨三',
    gender: 'female',
    phone: '13900139003',
    decorationType: 'simple',
    company: '中国银行',
    position: '信息科技部经理',
    source: 'online',
    level: 'A',
    status: 'potential',
    region: '北京市西城区',
    address: '北京市西城区中国银行总部',
    remark: '金融行业客户，对安全性要求很高',


    tags: ['金融', '安全导向'],
    pool_entry_time: '2024-01-19T09:15:00Z',
    pool_reason: '长期无跟进',
    created_at: '2024-01-01T11:45:00Z',
    updated_at: '2024-01-19T09:15:00Z'
  },
  {
    id: 104,
    name: '黄四',
    gender: 'male',
    phone: '13900139004',
    decorationType: 'rough',
    company: '中石油',
    position: '信息化主管',
    source: 'referral',
    level: 'B',
    status: 'contacted',
    region: '北京市东城区',
    address: '北京市东城区中石油大厦',
    remark: '传统行业客户，需要定制化方案',


    tags: ['传统行业', '定制化'],
    pool_entry_time: '2024-01-18T13:45:00Z',
    pool_reason: '销售调岗',
    created_at: '2024-01-04T16:10:00Z',
    updated_at: '2024-01-18T13:45:00Z'
  },
  {
    id: 105,
    name: '林五',
    gender: 'female',
    phone: '13900139005',
    decorationType: 'simple',
    company: '招商银行',
    position: '数字化转型负责人',
    source: 'referral',
    level: 'A',
    status: 'potential',
    region: '广东省深圳市',
    address: '深圳市福田区招商银行大厦',
    remark: '数字化转型项目，预算充足',


    tags: ['数字化', '大预算'],
    pool_entry_time: '2024-01-19T11:20:00Z',
    pool_reason: '客户主动要求',
    created_at: '2024-01-07T09:30:00Z',
    updated_at: '2024-01-19T11:20:00Z'
  }
]

// 模拟延迟函数
export const mockDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 检查是否为开发环境
export const isDevelopment = process.env.NODE_ENV !== 'production'