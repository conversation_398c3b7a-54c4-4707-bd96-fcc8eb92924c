"""具体CRUD服务实现

为各个模型提供专门的CRUD操作服务
"""

from typing import List, Optional, Dict, Any
from uuid import uuid4

from sqlalchemy import and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, joinedload

from app.models.user import User
from app.models.department import Department
from app.models.customer import Customer
from app.models.follow import CustomerFollowRecord, Meeting, PublicPool, CustomerBehavior
from app.models.auth import Role, Permission, OptionCategory, OptionItem
from app.models.marketing import (
    MarketingCampaign, CampaignParticipant, CampaignShare,
    SalesFunnelStats, CustomerValueAnalysis, FollowUp
)
from app.services.base_crud import CRUDBase
from app.services.database_manager import handle_db_errors


class CRUDUser(CRUDBase[User, dict, dict]):
    """用户CRUD操作"""
    
    @handle_db_errors
    async def get_by_username(self, db: AsyncSession, *, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await db.execute(
            select(User)
            .options(joinedload(User.department))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await db.execute(
            select(User)
            .options(joinedload(User.department))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_by_phone(self, db: AsyncSession, *, phone: str) -> Optional[User]:
        """根据手机号获取用户"""
        result = await db.execute(
            select(User)
            .options(joinedload(User.department))
            .where(User.phone == phone)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_active_users(
        self,
        db: AsyncSession,
        *,
        department_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """获取活跃用户列表"""
        query = select(User).options(joinedload(User.department)).where(User.is_active == True)
        
        if department_id:
            query = query.where(User.department_id == department_id)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    @handle_db_errors
    async def authenticate(
        self,
        db: AsyncSession,
        *,
        username: str,
        password: str
    ) -> Optional[User]:
        """用户认证（需要配合密码验证）"""
        user = await self.get_by_username(db, username=username)
        if not user or not user.is_active:
            return None
        # 注意：这里需要配合密码哈希验证，暂时返回用户对象
        return user


class CRUDDepartment(CRUDBase[Department, dict, dict]):
    """部门CRUD操作"""
    
    @handle_db_errors
    async def get_by_code(self, db: AsyncSession, *, code: str) -> Optional[Department]:
        """根据部门编码获取部门"""
        result = await db.execute(
            select(Department)
            .options(joinedload(Department.manager))
            .where(Department.code == code)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_tree_structure(self, db: AsyncSession) -> List[Department]:
        """获取部门树形结构"""
        result = await db.execute(
            select(Department)
            .options(joinedload(Department.manager), joinedload(Department.children))
            .where(Department.is_active == True)
            .order_by(Department.level, Department.sort_order)
        )
        return result.scalars().all()
    
    @handle_db_errors
    async def get_children(
        self,
        db: AsyncSession,
        *,
        parent_id: Optional[str] = None
    ) -> List[Department]:
        """获取子部门列表"""
        query = select(Department).options(joinedload(Department.manager))
        
        if parent_id:
            query = query.where(Department.parent_id == parent_id)
        else:
            query = query.where(Department.parent_id.is_(None))
        
        query = query.where(Department.is_active == True).order_by(Department.sort_order)
        result = await db.execute(query)
        return result.scalars().all()


class CRUDCustomer(CRUDBase[Customer, dict, dict]):
    """客户CRUD操作"""
    
    @handle_db_errors
    async def get_by_phone(self, db: AsyncSession, *, phone: str) -> Optional[Customer]:
        """根据手机号获取客户"""
        result = await db.execute(
            select(Customer)
            .options(
                joinedload(Customer.owner),
                joinedload(Customer.department),
                joinedload(Customer.creator)
            )
            .where(Customer.phone == phone)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[Customer]:
        """根据邮箱获取客户"""
        result = await db.execute(
            select(Customer)
            .options(
                joinedload(Customer.owner),
                joinedload(Customer.department),
                joinedload(Customer.creator)
            )
            .where(Customer.email == email)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_by_owner(
        self,
        db: AsyncSession,
        *,
        owner_id: str,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Customer]:
        """获取指定负责人的客户列表"""
        query = select(Customer).options(
            joinedload(Customer.owner),
            joinedload(Customer.department)
        ).where(Customer.owner_id == owner_id, Customer.is_deleted == False)
        
        if status:
            query = query.where(Customer.status == status)
        
        query = query.order_by(Customer.last_contact_time.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    @handle_db_errors
    async def get_by_department(
        self,
        db: AsyncSession,
        *,
        department_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Customer]:
        """获取指定部门的客户列表"""
        query = select(Customer).options(
            joinedload(Customer.owner),
            joinedload(Customer.department)
        ).where(
            Customer.department_id == department_id,
            Customer.is_deleted == False
        ).order_by(Customer.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @handle_db_errors
    async def search_customers(
        self,
        db: AsyncSession,
        *,
        keyword: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Customer]:
        """搜索客户"""
        search_pattern = f"%{keyword}%"
        query = select(Customer).options(
            joinedload(Customer.owner),
            joinedload(Customer.department)
        ).where(
            and_(
                Customer.is_deleted == False,
                or_(
                    Customer.name.like(search_pattern),
                    Customer.phone.like(search_pattern),
                    Customer.email.like(search_pattern),
                    Customer.company.like(search_pattern)
                )
            )
        ).order_by(Customer.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()


class CRUDCustomerFollowRecord(CRUDBase[CustomerFollowRecord, dict, dict]):
    """客户跟进记录CRUD操作"""
    
    @handle_db_errors
    async def get_by_customer(
        self,
        db: AsyncSession,
        *,
        customer_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[CustomerFollowRecord]:
        """获取客户的跟进记录"""
        query = select(CustomerFollowRecord).options(
            joinedload(CustomerFollowRecord.customer),
            joinedload(CustomerFollowRecord.user)
        ).where(
            CustomerFollowRecord.customer_id == customer_id
        ).order_by(CustomerFollowRecord.follow_time.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @handle_db_errors
    async def get_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[CustomerFollowRecord]:
        """获取用户的跟进记录"""
        query = select(CustomerFollowRecord).options(
            joinedload(CustomerFollowRecord.customer),
            joinedload(CustomerFollowRecord.user)
        ).where(
            CustomerFollowRecord.user_id == user_id
        ).order_by(CustomerFollowRecord.follow_time.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()


class CRUDMeeting(CRUDBase[Meeting, dict, dict]):
    """会议CRUD操作"""
    
    @handle_db_errors
    async def get_by_customer(
        self,
        db: AsyncSession,
        *,
        customer_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Meeting]:
        """获取客户相关的会议"""
        query = select(Meeting).options(
            joinedload(Meeting.customer),
            joinedload(Meeting.organizer)
        ).where(
            Meeting.customer_id == customer_id
        ).order_by(Meeting.meeting_time.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @handle_db_errors
    async def get_by_organizer(
        self,
        db: AsyncSession,
        *,
        organizer_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Meeting]:
        """获取组织者的会议"""
        query = select(Meeting).options(
            joinedload(Meeting.customer),
            joinedload(Meeting.organizer)
        ).where(
            Meeting.organizer_id == organizer_id
        ).order_by(Meeting.meeting_time.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()


class CRUDRole(CRUDBase[Role, dict, dict]):
    """角色CRUD操作"""
    
    @handle_db_errors
    async def get_by_code(self, db: AsyncSession, *, code: str) -> Optional[Role]:
        """根据角色编码获取角色"""
        result = await db.execute(
            select(Role).options(selectinload(Role.permissions))
            .where(Role.code == code)
        )
        return result.scalar_one_or_none()
    
    @handle_db_errors
    async def get_with_permissions(self, db: AsyncSession, *, role_id: str) -> Optional[Role]:
        """获取角色及其权限"""
        result = await db.execute(
            select(Role).options(selectinload(Role.permissions))
            .where(Role.id == role_id)
        )
        return result.scalar_one_or_none()


class CRUDPermission(CRUDBase[Permission, dict, dict]):
    """权限CRUD操作"""
    
    @handle_db_errors
    async def get_by_code(self, db: AsyncSession, *, code: str) -> Optional[Permission]:
        """根据权限编码获取权限"""
        result = await db.execute(
            select(Permission).where(Permission.code == code)
        )
        return result.scalar_one_or_none()


# CRUD实例
crud_user = CRUDUser(User)
crud_department = CRUDDepartment(Department)
crud_customer = CRUDCustomer(Customer)
crud_customer_follow_record = CRUDCustomerFollowRecord(CustomerFollowRecord)
crud_meeting = CRUDMeeting(Meeting)
crud_role = CRUDRole(Role)
crud_permission = CRUDPermission(Permission)
crud_option_category = CRUDBase(OptionCategory)
crud_option_item = CRUDBase(OptionItem)
crud_public_pool = CRUDBase(PublicPool)
crud_customer_behavior = CRUDBase(CustomerBehavior)
crud_marketing_campaign = CRUDBase(MarketingCampaign)
crud_campaign_participant = CRUDBase(CampaignParticipant)
crud_campaign_share = CRUDBase(CampaignShare)
crud_sales_funnel_stats = CRUDBase(SalesFunnelStats)
crud_customer_value_analysis = CRUDBase(CustomerValueAnalysis)
crud_follow_up = CRUDBase(FollowUp)