<template>
  <div class="app-header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 侧边栏切换按钮 -->
      <n-button
        quaternary
        circle
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <template #icon>
          <n-icon size="18">
            <MenuOutline />
          </n-icon>
        </template>
      </n-button>
      
      <!-- 页面标题 -->
      <div class="page-title">
        {{ currentPageTitle }}
      </div>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 搜索框 -->
      <div class="search-container">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索客户、跟进记录..."
          clearable
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <n-icon size="16">
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
      </div>
      
      <!-- 通知铃铛 -->
      <n-badge :value="notificationCount" :max="99">
        <n-button
          quaternary
          circle
          @click="showNotifications = true"
          class="notification-btn"
        >
          <template #icon>
            <n-icon size="18">
              <NotificationsOutline />
            </n-icon>
          </template>
        </n-button>
      </n-badge>
      
      <!-- 主题切换 -->
      <n-button
        quaternary
        circle
        @click="toggleTheme"
        class="theme-toggle"
      >
        <template #icon>
          <n-icon size="18">
            <SunnyOutline v-if="isDarkMode" />
            <MoonOutline v-else />
          </n-icon>
        </template>
      </n-button>
      
      <!-- 用户菜单 -->
      <n-dropdown
        :options="userMenuOptions"
        @select="handleUserMenuSelect"
        placement="bottom-end"
      >
        <div class="user-info">
          <n-avatar
            round
            size="small"
            :src="user?.avatar"
            :fallback-src="defaultAvatar"
          >
            {{ user?.name?.charAt(0) || 'U' }}
          </n-avatar>
          <span class="user-name">{{ user?.name || '用户' }}</span>
          <n-icon size="14" class="dropdown-icon">
            <ChevronDownOutline />
          </n-icon>
        </div>
      </n-dropdown>
    </div>
    
    <!-- 通知抽屉 -->
    <n-drawer
      v-model:show="showNotifications"
      :width="400"
      placement="right"
    >
      <n-drawer-content title="通知中心">
        <NotificationList />
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NButton,
  NIcon,
  NInput,
  NBadge,
  NDropdown,
  NAvatar,
  NDrawer,
  NDrawerContent,
  useMessage,
  useDialog
} from 'naive-ui'
import {
  MenuOutline,
  SearchOutline,
  NotificationsOutline,
  SunnyOutline,
  MoonOutline,
  ChevronDownOutline,
  PersonOutline,
  SettingsOutline,
  LogOutOutline
} from '@vicons/ionicons5'
import { useAppStore, useAuthStore } from '@/stores'
import NotificationList from '@/components/common/NotificationList.vue'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()
const appStore = useAppStore()
const authStore = useAuthStore()

// 响应式数据
const searchKeyword = ref('')
const showNotifications = ref(false)
const notificationCount = ref(5) // 模拟通知数量

// 计算属性
const isDarkMode = computed(() => appStore.isDarkMode)
const user = computed(() => authStore.user)
const currentPageTitle = computed(() => {
  return route.meta.title as string || '仪表板'
})

// 默认头像
const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPHBhdGggZD0iTTIwIDIwQzIyLjc2MTQgMjAgMjUgMTcuNzYxNCAyNSAxNUMyNSAxMi4yMzg2IDIyLjc2MTQgMTAgMjAgMTBDMTcuMjM4NiAxMCAxNSAxMi4yMzg2IDE1IDE1QzE1IDE3Ljc2MTQgMTcuMjM4NiAyMCAyMCAyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTIwIDIyQzE1LjU4MTcgMjIgMTIgMjUuNTgxNyAxMiAzMEMxMiAzMS4xMDQ2IDEyLjg5NTQgMzIgMTQgMzJIMjZDMjcuMTA0NiAzMiAyOCAzMS4xMDQ2IDI4IDMwQzI4IDI1LjU4MTcgMjQuNDE4MyAyMiAyMCAyMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+'

// 预创建用户菜单图标组件，避免在menuOptions中重复创建
const ProfileIcon = () => h(NIcon, null, { default: () => h(markRaw(PersonOutline)) })
const SettingsIcon = () => h(NIcon, null, { default: () => h(markRaw(SettingsOutline)) })
const LogoutIcon = () => h(NIcon, null, { default: () => h(markRaw(LogOutOutline)) })

// 用户菜单选项
const userMenuOptions = [
  {
    label: '个人资料',
    key: 'profile',
    icon: ProfileIcon
  },
  {
    label: '账户设置',
    key: 'settings',
    icon: SettingsIcon
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: LogoutIcon
  }
]

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 切换主题
const toggleTheme = () => {
  appStore.toggleTheme()
}

// 搜索处理
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    // 跳转到搜索页面或执行搜索逻辑
    router.push({
      path: '/search',
      query: { q: searchKeyword.value }
    })
  }
}

// 用户菜单选择处理
const handleUserMenuSelect = (key: string) => {
  switch (key) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  dialog.warning({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await authStore.logout()
        router.push('/login')
      } catch (error) {
        message.error('退出登录失败')
      }
    }
  })
}
</script>

<style scoped>
.app-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: var(--n-color);
  border-bottom: 1px solid var(--n-border-color);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  width: 36px;
  height: 36px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  width: 300px;
}

.search-input {
  width: 100%;
}

.notification-btn,
.theme-toggle {
  width: 36px;
  height: 36px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--n-hover-color);
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  color: var(--n-text-color-disabled);
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .search-container {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .header-right {
    gap: 8px;
  }
}
</style>