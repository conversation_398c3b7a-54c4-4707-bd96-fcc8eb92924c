import request from 'supertest';
import express from 'express';
import customerRouter from '../routes/customer';
import { errorHandler } from '../middleware/errorHandler';

// Mock MySQLManager模块
jest.mock('../../src/database/MySQLManager', () => {
  const mockMySQLManager = {
    initialize: jest.fn().mockResolvedValue(undefined),
    query: jest.fn().mockResolvedValue({
      success: true,
      data: [],
      affectedRows: 0
    }),
    insert: jest.fn().mockResolvedValue({
      success: true,
      insertId: 1,
      affectedRows: 1
    }),
    update: jest.fn().mockResolvedValue({
      success: true,
      affectedRows: 1
    }),
    delete: jest.fn().mockResolvedValue({
      success: true,
      affectedRows: 1
    }),
    testConnection: jest.fn().mockResolvedValue(true)
  };
  
  return {
    MySQLManager: jest.fn().mockImplementation(() => mockMySQLManager)
  };
});

// 获取mock实例用于测试
const { MySQLManager } = require('../../src/database/MySQLManager');
const mockMySQLManager = new MySQLManager();

describe('Customer API Routes', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/customer', customerRouter);
    app.use(errorHandler);
    
    // 重置所有模拟
    jest.clearAllMocks();
    
    // 重新设置默认的成功响应
    mockMySQLManager.query.mockResolvedValue({ success: true, data: [], affectedRows: 0 });
    mockMySQLManager.insert.mockResolvedValue({ success: true, insertId: 1, affectedRows: 1 });
    mockMySQLManager.update.mockResolvedValue({ success: true, affectedRows: 1 });
    mockMySQLManager.delete.mockResolvedValue({ success: true, affectedRows: 1 });
    mockMySQLManager.initialize.mockResolvedValue(undefined);
  });

  describe('GET /api/customer', () => {
    it('应该返回客户列表', async () => {
      const mockCustomers = [
        {
          id: 1,
          name: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
          status: '潜在客户'
        }
      ];

      // 模拟查询结果
      mockMySQLManager.query
        .mockResolvedValueOnce({ success: true, data: mockCustomers })
        .mockResolvedValueOnce({ success: true, data: [{ total: 1 }] });

      const response = await request(app)
        .get('/api/customer')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.data).toEqual(mockCustomers);
      expect(response.body.data.pagination).toBeDefined();
    });

    it('应该支持分页参数', async () => {
      mockMySQLManager.query
        .mockResolvedValueOnce({ success: true, data: [] })
        .mockResolvedValueOnce({ success: true, data: [{ total: 0 }] });

      const response = await request(app)
        .get('/api/customer?page=2&pageSize=5')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.page).toBe(2);
      expect(response.body.data.pagination.pageSize).toBe(5);
    });

    it('应该支持关键词搜索', async () => {
      mockMySQLManager.query
        .mockResolvedValueOnce({ success: true, data: [] })
        .mockResolvedValueOnce({ success: true, data: [{ total: 0 }] });

      await request(app)
        .get('/api/customer?keyword=张三')
        .expect(200);

      expect(mockMySQLManager.query).toHaveBeenCalledWith(
        expect.stringContaining('LIKE'),
        expect.arrayContaining(['%张三%'])
      );
    });
  });

  describe('POST /api/customer', () => {
    it('应该创建新客户', async () => {
      const newCustomer = {
        name: '李四',
        phone: '13900139000',
        email: '<EMAIL>',
        status: '潜在客户'
      };

      // 先mock电话号码检查（返回空数组表示电话不存在）
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: []
      });

      // 然后mock插入操作
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: { insertId: 2 },
        affectedRows: 1
      });

      // mock操作日志插入
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 1
      });

      const response = await request(app)
        .post('/api/customer')
        .send(newCustomer)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(2);
      
      // 验证电话检查调用
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(1,
        'SELECT id FROM customers WHERE phone = ?',
        ['13900139000']
      );
      
      // 验证插入调用
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(2,
        expect.stringContaining('INSERT INTO customers'),
        expect.arrayContaining(['李四', '13900139000'])
      );
    });

    it('应该验证必填字段', async () => {
      const invalidCustomer = {
        email: '<EMAIL>'
        // 缺少必填的name和phone字段
      };

      const response = await request(app)
        .post('/api/customer')
        .send(invalidCustomer)
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('客户姓名是必填项');
    });
  });

  describe('GET /api/customer/:id', () => {
    it('应该返回指定客户详情', async () => {
      const mockCustomer = {
        id: 1,
        name: '张三',
        phone: '13800138000',
        email: '<EMAIL>'
      };

      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: [mockCustomer]
      });

      const response = await request(app)
        .get('/api/customer/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockCustomer);
    });

    it('应该处理客户不存在的情况', async () => {
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: []
      });

      const response = await request(app)
        .get('/api/customer/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('客户不存在');
    });
  });

  describe('PUT /api/customer/:id', () => {
    it('应该更新客户信息', async () => {
      const updateData = {
        name: '张三更新',
        phone: '13800138000',
        email: '<EMAIL>',
        status: '潜在客户'
      };

      // 1. 模拟客户存在检查
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: [{ id: 1, name: '张三' }]
      });

      // 2. 模拟电话号码重复检查（返回空数组表示没有重复）
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: []
      });

      // 3. 模拟更新操作
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 1
      });

      // 4. 模拟操作日志插入
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 1
      });

      const response = await request(app)
        .put('/api/customer/1')
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // 验证客户存在检查
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(1,
        'SELECT id, name FROM customers WHERE id = ?',
        [1]
      );
      
      // 验证电话重复检查
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(2,
        'SELECT id FROM customers WHERE phone = ? AND id != ?',
        ['13800138000', 1]
      );
      
      // 验证更新操作
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(3,
        expect.stringContaining('UPDATE customers SET'),
        expect.arrayContaining(['张三更新', '13800138000'])
      );
    });
  });

  describe('DELETE /api/customer/:id', () => {
    it('应该删除指定客户', async () => {
      // 1. 模拟查询客户存在
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: [{ id: 1, name: '张三' }]
      });

      // 2. 模拟软删除操作
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 1
      });

      // 3. 模拟操作日志插入
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 1
      });

      const response = await request(app)
        .delete('/api/customer/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // 验证客户存在检查
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(1,
        'SELECT id, name FROM customers WHERE id = ?',
        [1]
      );
      
      // 验证软删除操作
      expect(mockMySQLManager.query).toHaveBeenNthCalledWith(2,
        expect.stringContaining('UPDATE customers'),
        expect.arrayContaining([expect.any(Date), expect.any(Date), 1])
      );
    });
  });

  describe('DELETE /api/customer/batch', () => {
    it('应该批量删除客户', async () => {
      const customerIds = [1, 2, 3];
      
      // 模拟查询现有客户
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: [
          { id: 1, name: '张三' },
          { id: 2, name: '李四' },
          { id: 3, name: '王五' }
        ]
      });
      
      // 模拟批量删除操作
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 3
      });

      const response = await request(app)
        .delete('/api/customer/batch')
        .send({ ids: customerIds })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.deletedCount).toBe(3);
      expect(mockMySQLManager.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id, name FROM customers WHERE id IN (?,?,?) AND is_deleted = 0'),
        customerIds
      );
    });

    it('应该验证批量删除的ID数组', async () => {
      const response = await request(app)
        .delete('/api/customer/batch')
        .send({ ids: [] })
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('请提供要删除的客户ID列表');
    });
  });

  describe('PUT /api/customer/batch', () => {
    it('应该批量更新客户状态', async () => {
      const updateData = {
        ids: [1, 2, 3],
        updates: {
          status: '正式客户'
        }
      };

      // 模拟查询现有客户
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: [
          { id: 1, name: '张三' },
          { id: 2, name: '李四' },
          { id: 3, name: '王五' }
        ]
      });
      
      // 模拟批量更新操作
      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        affectedRows: 3
      });

      const response = await request(app)
        .put('/api/customer/batch')
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.updatedCount).toBe(3);
      expect(mockMySQLManager.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE customers SET status = ?, updated_at = ? WHERE id IN (?,?,?)'),
        ['正式客户', expect.any(Date), 1, 2, 3]
      );
    });

    it('应该验证批量更新的必填字段', async () => {
      const response = await request(app)
        .put('/api/customer/batch')
        .send({ ids: [1, 2] })
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('请提供要更新的字段');
    });
  });

  describe('GET /api/customer/export', () => {
    it('应该导出客户数据为CSV', async () => {
      const mockCustomers = [
        {
          id: 1,
          name: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
          status: '潜在客户'
        },
        {
          id: 2,
          name: '李四',
          phone: '13900139000',
          email: '<EMAIL>',
          status: '正式客户'
        }
      ];

      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: mockCustomers
      });

      const response = await request(app)
        .get('/api/customer/export?format=csv');

      if (response.status !== 200) {
        console.log('Export CSV error response:', response.body);
      }
      
      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(response.text).toContain('张三');
      expect(response.text).toContain('李四');
    });

    it('应该导出客户数据为Excel', async () => {
      const mockCustomers = [
        {
          id: 1,
          name: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
          status: '潜在客户'
        }
      ];

      mockMySQLManager.query.mockResolvedValueOnce({
        success: true,
        data: mockCustomers
      });

      const response = await request(app)
        .get('/api/customer/export?format=xlsx');

      if (response.status !== 200) {
        console.log('Export Excel error response:', response.body);
      }
      
      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(response.headers['content-disposition']).toContain('attachment');
    });

    it('应该验证导出格式参数', async () => {
      const response = await request(app)
        .get('/api/customer/export?format=invalid')
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('查询参数验证失败');
    });
  });

  describe('POST /api/customer/import', () => {
    it('应该处理缺少文件的导入请求', async () => {
      const response = await request(app)
        .post('/api/customer/import')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('请上传Excel文件');
    });

    it('应该处理导入数据验证错误', async () => {
      const invalidCustomers = [
        { name: '', phone: '13800138000' }, // 姓名为空
        { name: '测试客户2', phone: 'invalid' } // 电话格式错误
      ];

      const response = await request(app)
        .post('/api/customer/import')
        .send({ customers: invalidCustomers })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('请上传Excel文件');
    });

    it('应该验证导入数据格式', async () => {
      const response = await request(app)
        .post('/api/customer/import')
        .send({ customers: 'invalid' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('请上传Excel文件');
    });
  });

  describe('错误处理', () => {
    it('应该处理数据库查询错误', async () => {
      // 重置mock并设置为抛出异常
      mockMySQLManager.query.mockReset();
      mockMySQLManager.query.mockRejectedValue(new Error('数据库连接失败'));

      const response = await request(app)
        .get('/api/customer')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('数据库查询失败');
    });

    it('应该处理无效的客户ID', async () => {
      const response = await request(app)
        .get('/api/customer/invalid-id')
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('路径参数验证失败');
    });

    it('应该处理数据库连接失败', async () => {
      // 重置mock并设置为抛出异常
      mockMySQLManager.query.mockReset();
      mockMySQLManager.query.mockRejectedValue(new Error('数据库连接失败'));

      const response = await request(app)
        .get('/api/customer')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.message).toContain('数据库查询失败');
    });
  });
});