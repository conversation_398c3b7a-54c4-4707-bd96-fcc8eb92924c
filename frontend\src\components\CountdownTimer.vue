<template>
  <div class="countdown-timer">
    <n-space align="center">
      <n-icon :component="Time" :class="timeClass" />
      <span :class="timeClass">{{ timeDisplay }}</span>
      <n-tag v-if="isUrgent" type="error" size="small">紧急</n-tag>
      <n-tag v-else-if="isWarning" type="warning" size="small">即将到期</n-tag>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Time } from '@vicons/ionicons5'

interface Props {
  targetTime: string | number
  warningHours?: number // 警告时间（小时）
  urgentHours?: number  // 紧急时间（小时）
}

const props = withDefaults(defineProps<Props>(), {
  warningHours: 24,
  urgentHours: 6
})

const currentTime = ref(Date.now())
let timer: NodeJS.Timeout | null = null

// 计算剩余时间
const remainingTime = computed(() => {
  const target = typeof props.targetTime === 'string' 
    ? new Date(props.targetTime).getTime() 
    : props.targetTime
  return Math.max(0, target - currentTime.value)
})

// 判断是否已过期
const isOverdue = computed(() => remainingTime.value <= 0)

// 判断是否紧急（剩余时间小于紧急时间）
const isUrgent = computed(() => {
  const urgentMs = props.urgentHours * 60 * 60 * 1000
  return remainingTime.value > 0 && remainingTime.value <= urgentMs
})

// 判断是否警告（剩余时间小于警告时间但大于紧急时间）
const isWarning = computed(() => {
  const warningMs = props.warningHours * 60 * 60 * 1000
  const urgentMs = props.urgentHours * 60 * 60 * 1000
  return remainingTime.value > urgentMs && remainingTime.value <= warningMs
})

// 时间显示样式类
const timeClass = computed(() => {
  if (isOverdue.value) return 'text-red-600 font-bold'
  if (isUrgent.value) return 'text-red-500 font-semibold'
  if (isWarning.value) return 'text-orange-500'
  return 'text-green-600'
})

// 格式化时间显示
const timeDisplay = computed(() => {
  if (isOverdue.value) {
    const overdue = currentTime.value - (typeof props.targetTime === 'string' 
      ? new Date(props.targetTime).getTime() 
      : props.targetTime)
    return `已超期 ${formatDuration(overdue)}`
  }
  
  return `剩余 ${formatDuration(remainingTime.value)}`
})

// 格式化持续时间
const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    const remainingHours = hours % 24
    return `${days}天${remainingHours > 0 ? remainingHours + '小时' : ''}`
  }
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60
    return `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`
  }
  
  if (minutes > 0) {
    const remainingSeconds = seconds % 60
    return `${minutes}分钟${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`
  }
  
  return `${seconds}秒`
}

// 更新当前时间
const updateTime = () => {
  currentTime.value = Date.now()
}

onMounted(() => {
  // 每秒更新一次时间
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.countdown-timer {
  display: inline-block;
}

.text-red-600 {
  color: #dc2626;
}

.text-red-500 {
  color: #ef4444;
}

.text-orange-500 {
  color: #f97316;
}

.text-green-600 {
  color: #16a34a;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}
</style>