import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

async function testMySQLConnection() {
  console.log('🔄 开始测试 MySQL 数据库连接...');
  
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'yysh_miniprogram',
    charset: process.env.DB_CHARSET || 'utf8mb4',
    timezone: process.env.DB_TIMEZONE || '+08:00',
    connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10'),
    queueLimit: parseInt(process.env.MYSQL_QUEUE_LIMIT || '0'),
    acquireTimeout: parseInt(process.env.MYSQL_ACQUIRE_TIMEOUT || '60000'),
    timeout: parseInt(process.env.MYSQL_TIMEOUT || '60000'),
    reconnect: process.env.MYSQL_RECONNECT === 'true',
    ssl: process.env.MYSQL_SSL === 'true'
  };

  console.log('📋 连接配置:');
  console.log(`   主机: ${config.host}:${config.port}`);
  console.log(`   数据库: ${config.database}`);
  console.log(`   用户: ${config.user}`);
  console.log(`   字符集: ${config.charset}`);
  console.log(`   时区: ${config.timezone}`);

  let connection;
  
  try {
    // 创建连接
    connection = await mysql.createConnection(config);
    console.log('✅ MySQL 连接创建成功!');

    // 测试基本查询
    const [rows] = await connection.execute('SELECT 1 as test, NOW() as `current_time`, VERSION() as version');
    console.log('✅ 基本查询测试成功!');
    console.log('📊 查询结果:', rows);

    // 测试数据库是否存在
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbExists = (databases as any[]).some((db: any) => db.Database === config.database);
    
    if (dbExists) {
      console.log(`✅ 数据库 '${config.database}' 存在`);
      
      // 切换到目标数据库
      await connection.query(`USE \`${config.database}\``);
      
      // 查看表列表
      const [tables] = await connection.query('SHOW TABLES');
      console.log(`📋 数据库中的表 (${(tables as any[]).length} 个):`);
      (tables as any[]).forEach((table: any, index: number) => {
        const tableName = Object.values(table)[0];
        console.log(`   ${index + 1}. ${tableName}`);
      });
    } else {
      console.log(`⚠️  数据库 '${config.database}' 不存在，需要创建`);
    }

    console.log('🎉 MySQL 连接测试完成!');
    return true;
    
  } catch (error) {
    console.error('❌ MySQL 连接测试失败:');
    console.error('错误详情:', error);
    
    if (error instanceof Error) {
      console.error('错误消息:', error.message);
      if ('code' in error) {
        console.error('错误代码:', (error as any).code);
      }
      if ('errno' in error) {
        console.error('错误编号:', (error as any).errno);
      }
    }
    
    return false;
    
  } finally {
    if (connection) {
      try {
        await connection.end();
        console.log('🔌 MySQL 连接已关闭');
      } catch (closeError) {
        console.error('关闭连接时出错:', closeError);
      }
    }
  }
}

// 运行测试
// 直接运行测试函数
testMySQLConnection()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });

export default testMySQLConnection;