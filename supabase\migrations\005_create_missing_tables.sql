-- ========================================
-- 创建迁移脚本所需的缺失表
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 描述: 为数据迁移脚本创建缺失的用户、跟进记录和公海记录表
-- ========================================

-- ========================================
-- 1. 用户表
-- ========================================

-- 用户表（用于存储系统用户信息）
CREATE TABLE IF NOT EXISTS "users" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "username" VARCHAR(50) NOT NULL UNIQUE,
  "email" VARCHAR(100) NOT NULL UNIQUE,
  "role" VARCHAR(50) NOT NULL DEFAULT 'sales',
  "department" VARCHAR(100),
  "phone" VARCHAR(20),
  "avatar" VARCHAR(500),
  "status" VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive, suspended
  "last_login_at" TIMESTAMP WITH TIME ZONE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 2. 跟进记录表
-- ========================================

-- 跟进记录表
CREATE TABLE IF NOT EXISTS "follow_ups" (
  "id" BIGSERIAL PRIMARY KEY,
  "customer_id" BIGINT NOT NULL REFERENCES "customers"("id") ON DELETE CASCADE,
  "type" VARCHAR(50) NOT NULL, -- phone, email, meeting, wechat, visit
  "content" TEXT NOT NULL,
  "result" VARCHAR(50), -- positive, negative, neutral, pending
  "next_action" TEXT,
  "next_follow_up_at" TIMESTAMP WITH TIME ZONE,
  "attachments" JSONB DEFAULT '[]'::jsonb,
  "tags" JSONB DEFAULT '[]'::jsonb,
  "created_by" UUID NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 3. 公海记录表
-- ========================================

-- 公海记录表
CREATE TABLE IF NOT EXISTS "public_pool" (
  "id" BIGSERIAL PRIMARY KEY,
  "customer_id" BIGINT NOT NULL REFERENCES "customers"("id") ON DELETE CASCADE,
  "reason" VARCHAR(200) NOT NULL, -- 进入公海的原因
  "moved_by" UUID NOT NULL, -- 操作人
  "moved_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 进入公海时间
  "claimed_by" UUID, -- 领取人
  "claimed_at" TIMESTAMP WITH TIME ZONE, -- 领取时间
  "status" VARCHAR(20) DEFAULT 'available', -- available, claimed, expired
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 4. 创建索引
-- ========================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users"("username");
CREATE INDEX IF NOT EXISTS "idx_users_email" ON "users"("email");
CREATE INDEX IF NOT EXISTS "idx_users_role_status" ON "users"("role", "status");

-- 跟进记录索引
CREATE INDEX IF NOT EXISTS "idx_follow_ups_customer_id" ON "follow_ups"("customer_id");
CREATE INDEX IF NOT EXISTS "idx_follow_ups_created_by" ON "follow_ups"("created_by");
CREATE INDEX IF NOT EXISTS "idx_follow_ups_type_result" ON "follow_ups"("type", "result");
CREATE INDEX IF NOT EXISTS "idx_follow_ups_next_follow_up" ON "follow_ups"("next_follow_up_at");

-- 公海记录索引
CREATE INDEX IF NOT EXISTS "idx_public_pool_customer_id" ON "public_pool"("customer_id");
CREATE INDEX IF NOT EXISTS "idx_public_pool_status" ON "public_pool"("status");
CREATE INDEX IF NOT EXISTS "idx_public_pool_moved_by" ON "public_pool"("moved_by");
CREATE INDEX IF NOT EXISTS "idx_public_pool_claimed_by" ON "public_pool"("claimed_by");

-- ========================================
-- 5. 创建更新时间触发器
-- ========================================

-- 创建更新时间函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表创建更新时间触发器
DROP TRIGGER IF EXISTS update_users_updated_at ON "users";
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON "users"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为跟进记录表创建更新时间触发器
DROP TRIGGER IF EXISTS update_follow_ups_updated_at ON "follow_ups";
CREATE TRIGGER update_follow_ups_updated_at
    BEFORE UPDATE ON "follow_ups"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 6. 启用行级安全策略 (RLS)
-- ========================================

ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "follow_ups" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public_pool" ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "Users can view all users" ON "users" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Users can update own profile" ON "users" FOR UPDATE USING (id = auth.uid());
CREATE POLICY "Service role can manage users" ON "users" FOR ALL USING (auth.role() = 'service_role');

-- 跟进记录策略
CREATE POLICY "Users can view follow ups" ON "follow_ups" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Users can create follow ups" ON "follow_ups" FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update own follow ups" ON "follow_ups" FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Service role can manage follow ups" ON "follow_ups" FOR ALL USING (auth.role() = 'service_role');

-- 公海记录策略
CREATE POLICY "Users can view public pool" ON "public_pool" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Users can create public pool records" ON "public_pool" FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update public pool records" ON "public_pool" FOR UPDATE USING (moved_by = auth.uid() OR claimed_by = auth.uid());
CREATE POLICY "Service role can manage public pool" ON "public_pool" FOR ALL USING (auth.role() = 'service_role');

-- ========================================
-- 7. 授予权限
-- ========================================

-- 授予匿名用户基本读取权限
GRANT SELECT ON "users" TO anon;
GRANT SELECT ON "follow_ups" TO anon;
GRANT SELECT ON "public_pool" TO anon;

-- 授予认证用户完整权限
GRANT ALL PRIVILEGES ON "users" TO authenticated;
GRANT ALL PRIVILEGES ON "follow_ups" TO authenticated;
GRANT ALL PRIVILEGES ON "public_pool" TO authenticated;

-- 授予序列权限
GRANT USAGE, SELECT ON SEQUENCE "follow_ups_id_seq" TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE "public_pool_id_seq" TO authenticated;

-- ========================================
-- 完成
-- ========================================

SELECT '缺失表创建完成！现在可以运行数据迁移脚本了。' as message;