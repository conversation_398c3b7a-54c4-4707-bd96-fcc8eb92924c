<template>
  <div class="points-mall">
    
    <n-tabs v-model:value="activeTab" type="line" animated>
      <n-tab-pane name="products" tab="商品列表">
        <div class="products-grid">
          <div class="search-bar">
            <n-space>
              <n-input
                v-model:value="searchKeyword"
                placeholder="搜索商品名称"
                clearable
                style="width: 300px"
              >
                <template #prefix>
                  <n-icon><SearchOutline /></n-icon>
                </template>
              </n-input>
              <n-select
                v-model:value="statusFilter"
                placeholder="商品状态"
                clearable
                style="width: 150px"
                :options="statusOptions"
              />
            </n-space>
          </div>
          
          <div class="products-container">
            <div class="product-card" v-for="product in filteredProducts" :key="product.id">
              <div class="product-image">
                <n-icon size="48" color="#18a058">
                  <GiftOutline />
                </n-icon>
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <p class="product-description">{{ product.description }}</p>
                <div class="product-points">
                  <n-tag type="info" size="large">
                    <template #icon>
                      <n-icon><DiamondOutline /></n-icon>
                    </template>
                    {{ product.points }} 积分
                  </n-tag>
                </div>
                <div class="product-stock">
                  <span class="stock-label">库存：</span>
                  <span :class="{ 'low-stock': product.stock < 10 }">{{ product.stock }}</span>
                </div>
                <div class="product-actions">
                  <n-button
                    type="primary"
                    :disabled="product.status !== 'active' || product.stock === 0"
                    @click="handleExchange(product)"
                  >
                    <template #icon>
                      <n-icon><SwapHorizontalOutline /></n-icon>
                    </template>
                    {{ product.stock === 0 ? '缺货' : '立即兑换' }}
                  </n-button>
                </div>
              </div>
              <div class="product-status" v-if="product.status !== 'active'">
                <n-tag type="warning">已下架</n-tag>
              </div>
            </div>
          </div>
        </div>
      </n-tab-pane>
      
      <n-tab-pane name="records" tab="兑换记录">
        <div class="exchange-records">
          <div class="records-header">
            <n-space justify="space-between">
              <n-space>
                <n-input
                  v-model:value="recordSearchKeyword"
                  placeholder="搜索商品名称或用户"
                  clearable
                  style="width: 300px"
                >
                  <template #prefix>
                    <n-icon><SearchOutline /></n-icon>
                  </template>
                </n-input>
                <n-select
                  v-model:value="recordStatusFilter"
                  placeholder="兑换状态"
                  clearable
                  style="width: 150px"
                  :options="recordStatusOptions"
                />
                <n-date-picker
                  v-model:value="dateRange"
                  type="daterange"
                  clearable
                  placeholder="选择日期范围"
                />
              </n-space>
              <n-button type="primary" @click="exportRecords">
                <template #icon>
                  <n-icon><DownloadOutline /></n-icon>
                </template>
                导出记录
              </n-button>
            </n-space>
          </div>
          
          <n-data-table
            :columns="recordColumns"
            :data="filteredRecords"
            :loading="recordsLoading"
            :pagination="recordsPagination"
            :row-key="(row) => row.id"
          />
        </div>
      </n-tab-pane>
    </n-tabs>
    
    <!-- 兑换确认弹窗 -->
    <n-modal v-model:show="showExchangeModal" preset="card" style="width: 500px" title="确认兑换">
      <div v-if="selectedProduct" class="exchange-confirm">
        <div class="product-preview">
          <div class="preview-image">
            <n-icon size="64" color="#18a058">
              <GiftOutline />
            </n-icon>
          </div>
          <div class="preview-info">
            <h3>{{ selectedProduct.name }}</h3>
            <p>{{ selectedProduct.description }}</p>
            <div class="points-cost">
              <n-tag type="info" size="large">
                <template #icon>
                  <n-icon><DiamondOutline /></n-icon>
                </template>
                需要 {{ selectedProduct.points }} 积分
              </n-tag>
            </div>
          </div>
        </div>
        
        <n-form
          ref="exchangeFormRef"
          :model="exchangeForm"
          :rules="{
            quantity: [
              {
                required: true,
                type: 'number',
                message: '请输入兑换数量',
                trigger: 'blur'
              }
            ],
            address: [
              {
                required: true,
                message: '请输入收货地址',
                trigger: 'blur'
              }
            ],
            phone: [
              {
                required: true,
                message: '请输入联系电话',
                trigger: 'blur'
              }
            ]
          }"
          label-placement="left"
          label-width="100px"
        >
          <n-form-item label="兑换数量" path="quantity">
            <n-input-number
              v-model:value="exchangeForm.quantity"
              :min="1"
              :max="selectedProduct.stock"
              placeholder="请输入兑换数量"
            />
          </n-form-item>
          <n-form-item label="收货地址" path="address">
            <n-input
              v-model:value="exchangeForm.address"
              type="textarea"
              placeholder="请输入收货地址"
              :rows="3"
            />
          </n-form-item>
          <n-form-item label="联系电话" path="phone">
            <n-input
              v-model:value="exchangeForm.phone"
              placeholder="请输入联系电话"
            />
          </n-form-item>
          <n-form-item label="备注" path="remark">
            <n-input
              v-model:value="exchangeForm.remark"
              type="textarea"
              placeholder="请输入备注信息（可选）"
              :rows="2"
            />
          </n-form-item>
        </n-form>
        
        <div class="total-points">
          <span>总计消耗积分：</span>
          <n-tag type="warning" size="large">
            {{ (selectedProduct.points * (exchangeForm.quantity || 1)) }} 积分
          </n-tag>
        </div>
      </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showExchangeModal = false">取消</n-button>
          <n-button type="primary" @click="confirmExchange">确认兑换</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import {
  NTabs,
  NTabPane,
  NInput,
  NButton,
  NIcon,
  NSpace,
  NSelect,
  NTag,
  NDataTable,
  NDatePicker,
  NModal,
  NForm,
  NFormItem,
  NInputNumber,
  useMessage,
  useDialog,
  type DataTableColumns
} from 'naive-ui'
import {
  SearchOutline,
  GiftOutline,
  DiamondOutline,
  SwapHorizontalOutline,
  DownloadOutline,
  PersonOutline,
  TimeOutline
} from '@vicons/ionicons5'
import { usePointsStore, type Product, type ExchangeRecord } from '@/stores/modules/points'

const message = useMessage()
const dialog = useDialog()
const pointsStore = usePointsStore()
const activeTab = ref('products')
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const recordSearchKeyword = ref('')
const recordStatusFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)
const recordsLoading = ref(false)
const showExchangeModal = ref(false)
const selectedProduct = ref<Product | null>(null)
const exchangeFormRef = ref()

const exchangeForm = ref({
  quantity: 1,
  address: '',
  phone: '',
  remark: ''
})

const exchangeRules = {
  quantity: {
    required: true,
    type: 'number',
    message: '请输入兑换数量',
    trigger: 'blur'
  },
  address: {
    required: true,
    message: '请输入收货地址',
    trigger: 'blur'
  },
  phone: {
    required: true,
    message: '请输入联系电话',
    trigger: 'blur'
  }
}

const statusOptions = [
  { label: '上架中', value: 'active' },
  { label: '已下架', value: 'inactive' }
]

const recordStatusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已发货', value: 'shipped' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// 使用store中的数据

const recordsPagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
}

const filteredProducts = computed(() => {
  let result = pointsStore.products
  
  if (searchKeyword.value) {
    result = result.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(item => item.status === statusFilter.value)
  }
  
  return result
})

const filteredRecords = computed(() => {
  let result = pointsStore.exchangeRecords
  
  if (recordSearchKeyword.value) {
    result = result.filter(item =>
      item.productName.toLowerCase().includes(recordSearchKeyword.value.toLowerCase()) ||
      item.userName.toLowerCase().includes(recordSearchKeyword.value.toLowerCase())
    )
  }
  
  if (recordStatusFilter.value) {
    result = result.filter(item => item.status === recordStatusFilter.value)
  }
  
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(item => {
      const exchangeTime = new Date(item.exchangeTime).getTime()
      return exchangeTime >= start && exchangeTime <= end
    })
  }
  
  return result
})

const getStatusTag = (status: string) => {
  const statusMap: Record<string, { type: string; label: string }> = {
    pending: { type: 'warning', label: '待处理' },
    processing: { type: 'info', label: '处理中' },
    shipped: { type: 'primary', label: '已发货' },
    completed: { type: 'success', label: '已完成' },
    cancelled: { type: 'error', label: '已取消' }
  }
  const config = statusMap[status] || { type: 'default', label: status }
  return h(NTag, { type: config.type as any }, { default: () => config.label })
}

const recordColumns: DataTableColumns<ExchangeRecord> = [
  {
    title: '订单号',
    key: 'id',
    width: 100,
    render: (row) => `EX${row.id.toString().padStart(6, '0')}`
  },
  {
    title: '商品名称',
    key: 'productName'
  },
  {
    title: '用户信息',
    key: 'user',
    render: (row) => h('div', {}, [
      h('div', { style: 'font-weight: 500' }, row.userName),
      h('div', { style: 'font-size: 12px; color: #666' }, row.userPhone)
    ])
  },
  {
    title: '数量',
    key: 'quantity',
    width: 80
  },
  {
    title: '消耗积分',
    key: 'totalPoints',
    width: 100,
    render: (row) => h(NTag, { type: 'info' }, { default: () => `${row.totalPoints}积分` })
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => getStatusTag(row.status)
  },
  {
    title: '兑换时间',
    key: 'exchangeTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => h(NSpace, {}, {
      default: () => [
        h(NButton, {
          size: 'small',
          type: 'primary',
          quaternary: true,
          onClick: () => handleViewRecord(row)
        }, { default: () => '查看详情' }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          quaternary: true,
          disabled: row.status === 'completed' || row.status === 'cancelled',
          onClick: () => handleUpdateStatus(row)
        }, { default: () => '更新状态' })
      ]
    })
  }
]

const handleExchange = (product: Product) => {
  selectedProduct.value = product
  exchangeForm.value = {
    quantity: 1,
    address: '',
    phone: '',
    remark: ''
  }
  showExchangeModal.value = true
}

const confirmExchange = async () => {
  try {
    await exchangeFormRef.value?.validate()
    
    if (!selectedProduct.value) return
    
    const totalPoints = selectedProduct.value.points * exchangeForm.value.quantity
    
    // 使用store创建兑换记录
    pointsStore.addExchangeRecord({
      productId: selectedProduct.value.id,
      productName: selectedProduct.value.name,
      userId: 1001, // 模拟当前用户ID
      userName: '当前用户', // 模拟当前用户名
      userPhone: exchangeForm.value.phone,
      quantity: exchangeForm.value.quantity,
      totalPoints,
      address: exchangeForm.value.address,
      remark: exchangeForm.value.remark,
      status: 'pending'
    })
    
    message.success('兑换成功！')
    showExchangeModal.value = false
    activeTab.value = 'records'
  } catch (error) {
    message.error('兑换失败，请检查表单信息')
  }
}

const handleViewRecord = (record: ExchangeRecord) => {
  dialog.info({
    title: '兑换详情',
    content: () => h('div', {}, [
      h('p', {}, `订单号：EX${record.id.toString().padStart(6, '0')}`),
      h('p', {}, `商品名称：${record.productName}`),
      h('p', {}, `用户：${record.userName} (${record.userPhone})`),
      h('p', {}, `数量：${record.quantity}`),
      h('p', {}, `消耗积分：${record.totalPoints}`),
      h('p', {}, `收货地址：${record.address}`),
      record.remark ? h('p', {}, `备注：${record.remark}`) : null,
      h('p', {}, `兑换时间：${record.exchangeTime}`),
      h('p', {}, `更新时间：${record.updateTime}`)
    ]),
    positiveText: '确定'
  })
}

const handleUpdateStatus = (record: ExchangeRecord) => {
  let newStatus = record.status
  
  dialog.create({
    title: '更新状态',
    content: () => {
      return h('div', {}, [
        h('p', {}, `订单号：EX${record.id.toString().padStart(6, '0')}`),
        h('p', {}, `当前状态：${recordStatusOptions.find(opt => opt.value === record.status)?.label}`),
        h(NSelect, {
          value: newStatus,
          onUpdateValue: (value: 'pending' | 'processing' | 'shipped' | 'completed' | 'cancelled') => { newStatus = value },
          options: recordStatusOptions,
          placeholder: '请选择新状态'
        })
      ])
    },
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      if (newStatus !== record.status) {
        const result = pointsStore.updateExchangeRecord(record.id, { status: newStatus as any })
        if (result) {
          message.success('状态更新成功')
        } else {
          message.error('状态更新失败')
        }
      }
    }
  })
}

const exportRecords = () => {
  // 模拟导出功能
  message.info('导出功能开发中...')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.points-mall {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.search-bar {
  margin-bottom: 24px;
}

.products-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.product-image {
  text-align: center;
  margin-bottom: 16px;
}

.product-info {
  text-align: center;
}

.product-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.product-description {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.product-points {
  margin-bottom: 12px;
}

.product-stock {
  margin-bottom: 16px;
  font-size: 14px;
}

.stock-label {
  color: #6b7280;
}

.low-stock {
  color: #ef4444;
  font-weight: 600;
}

.product-actions {
  margin-top: 16px;
}

.product-status {
  position: absolute;
  top: 12px;
  right: 12px;
}

.records-header {
  margin-bottom: 16px;
}

.exchange-confirm {
  padding: 16px 0;
}

.product-preview {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.preview-image {
  margin-right: 16px;
}

.preview-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-info p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
}

.points-cost {
  margin-bottom: 0;
}

.total-points {
  margin-top: 16px;
  padding: 12px;
  background: #fef3c7;
  border-radius: 6px;
  text-align: center;
  font-weight: 600;
}

.total-points span {
  margin-right: 8px;
}
</style>