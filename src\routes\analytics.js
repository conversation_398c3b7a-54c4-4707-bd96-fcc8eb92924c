const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取综合分析数据
router.get('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    
    let customerWhereClause = 'status != "deleted"';
    let followWhereClause = '1=1';
    let whereParams = [];

    // 权限控制：普通员工只能看到自己的数据
    if (req.user.role === 'employee') {
      customerWhereClause += ' AND assigned_to = ?';
      followWhereClause += ' AND user_id = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 总客户数
      db.query(`SELECT COUNT(*) as total FROM customers WHERE ${customerWhereClause}`, whereParams),
      // 总跟进数
      db.query(`SELECT COUNT(*) as total FROM follow_records WHERE ${followWhereClause}`, whereParams),
      // 成交客户数
      db.query(`SELECT COUNT(*) as total FROM customers WHERE level = "customer" AND ${customerWhereClause}`, whereParams),
      // 总收入（模拟数据）
      db.query(`SELECT COUNT(*) * 50000 as total FROM customers WHERE level = "customer" AND ${customerWhereClause}`, whereParams)
    ]);

    // 客户来源分布
    const sourceStats = await db.query(`
      SELECT source as name, COUNT(*) as value 
      FROM customers 
      WHERE ${customerWhereClause}
      GROUP BY source
      ORDER BY value DESC
    `, whereParams);

    // 客户状态分布
    const statusStats = await db.query(`
      SELECT 
        CASE status
          WHEN 'active' THEN '活跃客户'
          WHEN 'inactive' THEN '非活跃客户'
          WHEN 'potential' THEN '潜在客户'
          ELSE status
        END as name,
        COUNT(*) as value 
      FROM customers 
      WHERE ${customerWhereClause}
      GROUP BY status
      ORDER BY value DESC
    `, whereParams);

    // 月度增长趋势
    const growthTrend = await db.query(`
      SELECT 
        DATE_FORMAT(created_at, '%m月') as month,
        COUNT(*) as customers,
        COUNT(CASE WHEN level = 'customer' THEN 1 END) as deals
      FROM customers 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH) AND ${customerWhereClause}
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY DATE_FORMAT(created_at, '%Y-%m')
    `, whereParams);

    // 跟进方式分布
    const followTypes = await db.query(`
      SELECT 
        CASE type
          WHEN 'phone' THEN '电话'
          WHEN 'email' THEN '邮件'
          WHEN 'visit' THEN '拜访'
          WHEN 'wechat' THEN '微信'
          ELSE type
        END as name,
        COUNT(*) as value 
      FROM follow_records 
      WHERE ${followWhereClause}
      GROUP BY type
      ORDER BY value DESC
    `, whereParams);

    // 销售绩效（仅管理员和经理可见）
    const salesPerformance = req.user.role !== 'employee' ? await db.query(`
      SELECT 
        u.name,
        COUNT(DISTINCT c.id) as customers,
        COUNT(fr.id) as follows,
        COUNT(DISTINCT CASE WHEN c.level = 'customer' THEN c.id END) as deals,
        COUNT(DISTINCT CASE WHEN c.level = 'customer' THEN c.id END) * 50000 as revenue
      FROM users u
      LEFT JOIN customers c ON u.id = c.assigned_to AND c.status != 'deleted'
      LEFT JOIN follow_records fr ON u.id = fr.user_id
      WHERE u.status = 'active' AND u.role IN ('employee', 'manager')
      GROUP BY u.id, u.name
      ORDER BY customers DESC
    `) : { rows: [] };

    res.json({
      success: true,
      data: {
        stats: {
          totalCustomers: stats[0].rows[0].total,
          totalFollows: stats[1].rows[0].total,
          totalDeals: stats[2].rows[0].total,
          totalRevenue: stats[3].rows[0].total
        },
        charts: {
          customerSource: sourceStats.rows,
          customerStatus: statusStats.rows,
          growthTrend: growthTrend.rows,
          followTypes: followTypes.rows
        },
        tables: {
          salesPerformance: salesPerformance.rows,
          customerSourceStats: sourceStats.rows.map(item => ({
            source: item.name,
            count: item.value,
            rate: '0%',
            conversion: '0%'
          })),
          monthlyData: growthTrend.rows.map(item => ({
            month: item.month,
            customers: item.customers,
            follows: 0,
            deals: item.deals,
            revenue: item.deals * 50000
          }))
        }
      }
    });

  } catch (error) {
    console.error('获取综合分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分析数据失败'
    });
  }
});

// 获取仪表板概览数据
router.get('/dashboard', async (req, res) => {
  try {
    const db = req.app.locals.db;
    
    let customerWhereClause = 'status != "deleted"';
    let followWhereClause = '1=1';
    let whereParams = [];

    // 权限控制：普通员工只能看到自己的数据
    if (req.user.role === 'employee') {
      customerWhereClause += ' AND assigned_to = ?';
      followWhereClause += ' AND user_id = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 客户统计
      db.query(`SELECT COUNT(*) as total FROM customers WHERE ${customerWhereClause}`, whereParams),
      db.query(`SELECT COUNT(*) as active FROM customers WHERE status = "active" AND ${customerWhereClause.replace('status != "deleted"', '1=1')}`, whereParams),
      db.query(`SELECT COUNT(*) as new_this_month FROM customers WHERE created_at >= DATE_FORMAT(NOW(), '%Y-%m-01') AND ${customerWhereClause}`, whereParams),
      
      // 跟进统计
      db.query(`SELECT COUNT(*) as total FROM follow_records WHERE ${followWhereClause}`, whereParams),
      db.query(`SELECT COUNT(*) as today FROM follow_records WHERE DATE(created_at) = CURDATE() AND ${followWhereClause}`, whereParams),
      db.query(`SELECT COUNT(*) as this_week FROM follow_records WHERE YEARWEEK(created_at) = YEARWEEK(NOW()) AND ${followWhereClause}`, whereParams),
      
      // 转化统计
      db.query(`SELECT COUNT(*) as converted FROM customers WHERE level = "customer" AND ${customerWhereClause}`, whereParams),
      
      // 最近7天客户新增趋势
      db.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM customers 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND ${customerWhereClause}
        GROUP BY DATE(created_at)
        ORDER BY date
      `, whereParams),
      
      // 最近7天跟进趋势
      db.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM follow_records 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND ${followWhereClause}
        GROUP BY DATE(created_at)
        ORDER BY date
      `, whereParams)
    ]);

    // 计算转化率
    const totalCustomers = stats[0].rows[0].total;
    const convertedCustomers = stats[6].rows[0].converted;
    const conversionRate = totalCustomers > 0 ? ((convertedCustomers / totalCustomers) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        customers: {
          total: stats[0].rows[0].total,
          active: stats[1].rows[0].active,
          newThisMonth: stats[2].rows[0].new_this_month
        },
        follows: {
          total: stats[3].rows[0].total,
          today: stats[4].rows[0].today,
          thisWeek: stats[5].rows[0].this_week
        },
        conversion: {
          converted: convertedCustomers,
          rate: conversionRate
        },
        trends: {
          customers: stats[7].rows,
          follows: stats[8].rows
        }
      }
    });

  } catch (error) {
    console.error('获取仪表板数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表板数据失败'
    });
  }
});

// 获取客户分析数据
router.get('/customers', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { period = '30' } = req.query; // 默认30天

    let whereClause = 'status != "deleted"';
    let whereParams = [];

    // 权限控制
    if (req.user.role === 'employee') {
      whereClause += ' AND assigned_to = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 客户来源分布
      db.query(`
        SELECT source, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY source
        ORDER BY count DESC
      `, whereParams),
      
      // 客户等级分布
      db.query(`
        SELECT level, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY level
        ORDER BY count DESC
      `, whereParams),
      
      // 客户状态分布
      db.query(`
        SELECT status, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY status
        ORDER BY count DESC
      `, whereParams),
      
      // 按月新增客户趋势
      db.query(`
        SELECT 
          DATE_FORMAT(created_at, '%Y-%m') as month,
          COUNT(*) as count
        FROM customers 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY) AND ${whereClause}
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
      `, whereParams),
      
      // 负责人客户分布（仅管理员和经理可见）
      req.user.role !== 'employee' ? db.query(`
        SELECT 
          u.name as user_name,
          COUNT(c.id) as count
        FROM users u
        LEFT JOIN customers c ON u.id = c.assigned_to AND c.status != "deleted"
        WHERE u.status = "active"
        GROUP BY u.id, u.name
        ORDER BY count DESC
      `) : Promise.resolve({ rows: [] })
    ]);

    res.json({
      success: true,
      data: {
        sourceDistribution: stats[0].rows,
        levelDistribution: stats[1].rows,
        statusDistribution: stats[2].rows,
        monthlyTrend: stats[3].rows,
        userDistribution: stats[4].rows
      }
    });

  } catch (error) {
    console.error('获取客户分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取客户分析数据失败'
    });
  }
});

// 获取跟进分析数据
router.get('/follows', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { period = '30' } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 权限控制
    if (req.user.role === 'employee') {
      whereClause += ' AND user_id = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 跟进类型分布
      db.query(`
        SELECT type, COUNT(*) as count 
        FROM follow_records 
        WHERE ${whereClause}
        GROUP BY type
        ORDER BY count DESC
      `, whereParams),
      
      // 跟进状态分布
      db.query(`
        SELECT status, COUNT(*) as count 
        FROM follow_records 
        WHERE ${whereClause}
        GROUP BY status
        ORDER BY count DESC
      `, whereParams),
      
      // 每日跟进趋势
      db.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM follow_records 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY) AND ${whereClause}
        GROUP BY DATE(created_at)
        ORDER BY date
      `, whereParams),
      
      // 跟进效果分析（按小时分布）
      db.query(`
        SELECT 
          HOUR(created_at) as hour,
          COUNT(*) as count
        FROM follow_records 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY) AND ${whereClause}
        GROUP BY HOUR(created_at)
        ORDER BY hour
      `, whereParams),
      
      // 用户跟进排行（仅管理员和经理可见）
      req.user.role !== 'employee' ? db.query(`
        SELECT 
          u.name as user_name,
          COUNT(fr.id) as count,
          COUNT(DISTINCT fr.customer_id) as unique_customers
        FROM users u
        LEFT JOIN follow_records fr ON u.id = fr.user_id 
          AND fr.created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY)
        WHERE u.status = "active"
        GROUP BY u.id, u.name
        ORDER BY count DESC
      `) : Promise.resolve({ rows: [] })
    ]);

    res.json({
      success: true,
      data: {
        typeDistribution: stats[0].rows,
        statusDistribution: stats[1].rows,
        dailyTrend: stats[2].rows,
        hourlyDistribution: stats[3].rows,
        userRanking: stats[4].rows
      }
    });

  } catch (error) {
    console.error('获取跟进分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取跟进分析数据失败'
    });
  }
});

// 获取销售漏斗数据
router.get('/funnel', async (req, res) => {
  try {
    const db = req.app.locals.db;

    let whereClause = 'status != "deleted"';
    let whereParams = [];

    // 权限控制
    if (req.user.role === 'employee') {
      whereClause += ' AND assigned_to = ?';
      whereParams.push(req.user.userId);
    }

    const funnelData = await db.query(`
      SELECT 
        level,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customers WHERE ${whereClause}), 2) as percentage
      FROM customers 
      WHERE ${whereClause}
      GROUP BY level
      ORDER BY 
        CASE level
          WHEN 'lead' THEN 1
          WHEN 'potential' THEN 2
          WHEN 'opportunity' THEN 3
          WHEN 'customer' THEN 4
          ELSE 5
        END
    `, whereParams);

    res.json({
      success: true,
      data: funnelData.rows
    });

  } catch (error) {
    console.error('获取销售漏斗数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取销售漏斗数据失败'
    });
  }
});

// 获取团队绩效数据（仅管理员和经理）
router.get('/performance', requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { period = '30' } = req.query;

    const performanceData = await db.query(`
      SELECT 
        u.id,
        u.name,
        u.role,
        COUNT(DISTINCT c.id) as total_customers,
        COUNT(DISTINCT CASE WHEN c.created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY) THEN c.id END) as new_customers,
        COUNT(DISTINCT CASE WHEN c.level = 'customer' THEN c.id END) as converted_customers,
        COUNT(fr.id) as total_follows,
        COUNT(CASE WHEN fr.created_at >= DATE_SUB(NOW(), INTERVAL ${period} DAY) THEN fr.id END) as recent_follows,
        ROUND(
          COUNT(DISTINCT CASE WHEN c.level = 'customer' THEN c.id END) * 100.0 / 
          NULLIF(COUNT(DISTINCT c.id), 0), 2
        ) as conversion_rate
      FROM users u
      LEFT JOIN customers c ON u.id = c.assigned_to AND c.status != 'deleted'
      LEFT JOIN follow_records fr ON u.id = fr.user_id
      WHERE u.status = 'active' AND u.role IN ('employee', 'manager')
      GROUP BY u.id, u.name, u.role
      ORDER BY total_customers DESC
    `);

    res.json({
      success: true,
      data: performanceData.rows
    });

  } catch (error) {
    console.error('获取团队绩效数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取团队绩效数据失败'
    });
  }
});

// 导出数据报表
router.get('/export/:type', requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { type } = req.params;
    const { startDate, endDate } = req.query;

    let sql = '';
    let params = [];

    switch (type) {
      case 'customers':
        sql = `
          SELECT 
            c.id, c.name, c.phone, c.email, c.company, c.position,
            c.source, c.level, c.status, c.created_at,
            u.name as assigned_name
          FROM customers c
          LEFT JOIN users u ON c.assigned_to = u.id
          WHERE c.status != 'deleted'
        `;
        break;
      
      case 'follows':
        sql = `
          SELECT 
            fr.id, fr.type, fr.content, fr.status, fr.created_at,
            c.name as customer_name, c.phone as customer_phone,
            u.name as user_name
          FROM follow_records fr
          LEFT JOIN customers c ON fr.customer_id = c.id
          LEFT JOIN users u ON fr.user_id = u.id
          WHERE 1=1
        `;
        break;
      
      default:
        return res.status(400).json({
          success: false,
          message: '不支持的导出类型'
        });
    }

    // 添加日期筛选
    if (startDate) {
      sql += ` AND ${type === 'customers' ? 'c' : 'fr'}.created_at >= ?`;
      params.push(startDate);
    }
    if (endDate) {
      sql += ` AND ${type === 'customers' ? 'c' : 'fr'}.created_at <= ?`;
      params.push(endDate + ' 23:59:59');
    }

    sql += ` ORDER BY ${type === 'customers' ? 'c' : 'fr'}.created_at DESC`;

    const result = await db.query(sql, params);

    res.json({
      success: true,
      data: result.rows,
      total: result.rows.length
    });

  } catch (error) {
    console.error('导出数据错误:', error);
    res.status(500).json({
      success: false,
      message: '导出数据失败'
    });
  }
});

module.exports = router;