import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Product {
  id: number
  name: string
  points: number
  description: string
  stock: number
  status: 'active' | 'inactive'
  createdAt: string
}

export interface ExchangeRecord {
  id: number
  productId: number
  productName: string
  userId: number
  userName: string
  userPhone: string
  quantity: number
  totalPoints: number
  address: string
  remark?: string
  status: 'pending' | 'processing' | 'shipped' | 'completed' | 'cancelled'
  exchangeTime: string
  updateTime: string
}

export const usePointsStore = defineStore('points', () => {
  // 商品列表
  const products = ref<Product[]>([
    {
      id: 1,
      name: '优惠券10元',
      points: 100,
      description: '可在商城使用的10元优惠券',
      stock: 50,
      status: 'active',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      name: '精美礼品盒',
      points: 500,
      description: '包含多种精美小礼品的礼品盒',
      stock: 20,
      status: 'active',
      createdAt: '2024-01-14'
    },
    {
      id: 3,
      name: '会员升级券',
      points: 1000,
      description: '可升级为高级会员的特权券',
      stock: 10,
      status: 'inactive',
      createdAt: '2024-01-13'
    },
    {
      id: 4,
      name: '蓝牙耳机',
      points: 2000,
      description: '高品质无线蓝牙耳机，音质清晰',
      stock: 5,
      status: 'active',
      createdAt: '2024-01-12'
    },
    {
      id: 5,
      name: '保温杯',
      points: 800,
      description: '304不锈钢保温杯，保温效果佳',
      stock: 0,
      status: 'active',
      createdAt: '2024-01-11'
    }
  ])

  // 兑换记录
  const exchangeRecords = ref<ExchangeRecord[]>([
    {
      id: 1,
      productId: 1,
      productName: '优惠券10元',
      userId: 1001,
      userName: '张三',
      userPhone: '13800138001',
      quantity: 2,
      totalPoints: 200,
      address: '北京市朝阳区xxx街道xxx号',
      remark: '请尽快发货',
      status: 'completed',
      exchangeTime: '2024-01-20 10:30:00',
      updateTime: '2024-01-22 15:20:00'
    },
    {
      id: 2,
      productId: 2,
      productName: '精美礼品盒',
      userId: 1002,
      userName: '李四',
      userPhone: '13800138002',
      quantity: 1,
      totalPoints: 500,
      address: '上海市浦东新区xxx路xxx号',
      status: 'shipped',
      exchangeTime: '2024-01-19 14:15:00',
      updateTime: '2024-01-21 09:30:00'
    },
    {
      id: 3,
      productId: 4,
      productName: '蓝牙耳机',
      userId: 1003,
      userName: '王五',
      userPhone: '13800138003',
      quantity: 1,
      totalPoints: 2000,
      address: '广州市天河区xxx大道xxx号',
      remark: '工作日配送',
      status: 'processing',
      exchangeTime: '2024-01-18 16:45:00',
      updateTime: '2024-01-19 10:00:00'
    },
    {
      id: 4,
      productId: 3,
      productName: '会员升级券',
      userId: 1004,
      userName: '赵六',
      userPhone: '13800138004',
      quantity: 1,
      totalPoints: 1000,
      address: '深圳市南山区xxx街xxx号',
      status: 'pending',
      exchangeTime: '2024-01-17 11:20:00',
      updateTime: '2024-01-17 11:20:00'
    }
  ])

  // 计算属性
  const activeProducts = computed(() => 
    products.value.filter(product => product.status === 'active')
  )

  const totalProducts = computed(() => products.value.length)

  const totalExchanges = computed(() => exchangeRecords.value.length)

  const totalPointsExchanged = computed(() => 
    exchangeRecords.value.reduce((total, record) => total + record.totalPoints, 0)
  )

  const pendingExchanges = computed(() => 
    exchangeRecords.value.filter(record => record.status === 'pending').length
  )

  // 商品管理方法
  const addProduct = (product: Omit<Product, 'id' | 'createdAt'>) => {
    const newProduct: Product = {
      ...product,
      id: Date.now(),
      createdAt: new Date().toISOString().split('T')[0]
    }
    products.value.unshift(newProduct)
    return newProduct
  }

  const updateProduct = (id: number, updates: Partial<Product>) => {
    const index = products.value.findIndex(product => product.id === id)
    if (index > -1) {
      products.value[index] = { ...products.value[index], ...updates }
      return products.value[index]
    }
    return null
  }

  const deleteProduct = (id: number) => {
    const index = products.value.findIndex(product => product.id === id)
    if (index > -1) {
      products.value.splice(index, 1)
      return true
    }
    return false
  }

  const getProductById = (id: number) => {
    return products.value.find(product => product.id === id)
  }

  // 兑换记录管理方法
  const addExchangeRecord = (record: Omit<ExchangeRecord, 'id' | 'exchangeTime' | 'updateTime'>) => {
    const newRecord: ExchangeRecord = {
      ...record,
      id: Date.now(),
      exchangeTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }
    exchangeRecords.value.unshift(newRecord)
    
    // 更新商品库存
    const product = getProductById(record.productId)
    if (product) {
      updateProduct(product.id, { stock: product.stock - record.quantity })
    }
    
    return newRecord
  }

  const updateExchangeRecord = (id: number, updates: Partial<ExchangeRecord>) => {
    const index = exchangeRecords.value.findIndex(record => record.id === id)
    if (index > -1) {
      exchangeRecords.value[index] = {
        ...exchangeRecords.value[index],
        ...updates,
        updateTime: new Date().toLocaleString()
      }
      return exchangeRecords.value[index]
    }
    return null
  }

  const getExchangeRecordById = (id: number) => {
    return exchangeRecords.value.find(record => record.id === id)
  }

  const getExchangeRecordsByUserId = (userId: number) => {
    return exchangeRecords.value.filter(record => record.userId === userId)
  }

  const getExchangeRecordsByProductId = (productId: number) => {
    return exchangeRecords.value.filter(record => record.productId === productId)
  }

  // 统计方法
  const getExchangeStatsByDateRange = (startDate: string, endDate: string) => {
    const filteredRecords = exchangeRecords.value.filter(record => {
      const exchangeDate = new Date(record.exchangeTime)
      const start = new Date(startDate)
      const end = new Date(endDate)
      return exchangeDate >= start && exchangeDate <= end
    })

    return {
      totalExchanges: filteredRecords.length,
      totalPoints: filteredRecords.reduce((total, record) => total + record.totalPoints, 0),
      completedExchanges: filteredRecords.filter(record => record.status === 'completed').length,
      pendingExchanges: filteredRecords.filter(record => record.status === 'pending').length
    }
  }

  const getTopExchangedProducts = (limit: number = 5) => {
    const productStats = new Map<number, { product: Product; exchangeCount: number; totalPoints: number }>()

    exchangeRecords.value.forEach(record => {
      const product = getProductById(record.productId)
      if (product) {
        const existing = productStats.get(record.productId)
        if (existing) {
          existing.exchangeCount += record.quantity
          existing.totalPoints += record.totalPoints
        } else {
          productStats.set(record.productId, {
            product,
            exchangeCount: record.quantity,
            totalPoints: record.totalPoints
          })
        }
      }
    })

    return Array.from(productStats.values())
      .sort((a, b) => b.exchangeCount - a.exchangeCount)
      .slice(0, limit)
  }

  // 库存管理
  const updateStock = (productId: number, quantity: number) => {
    const product = getProductById(productId)
    if (product) {
      const newStock = Math.max(0, product.stock + quantity)
      updateProduct(productId, { stock: newStock })
      return newStock
    }
    return null
  }

  const checkLowStock = (threshold: number = 10) => {
    return products.value.filter(product => 
      product.status === 'active' && product.stock <= threshold
    )
  }

  return {
    // 状态
    products,
    exchangeRecords,
    
    // 计算属性
    activeProducts,
    totalProducts,
    totalExchanges,
    totalPointsExchanged,
    pendingExchanges,
    
    // 商品管理方法
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    
    // 兑换记录管理方法
    addExchangeRecord,
    updateExchangeRecord,
    getExchangeRecordById,
    getExchangeRecordsByUserId,
    getExchangeRecordsByProductId,
    
    // 统计方法
    getExchangeStatsByDateRange,
    getTopExchangedProducts,
    
    // 库存管理
    updateStock,
    checkLowStock
  }
})