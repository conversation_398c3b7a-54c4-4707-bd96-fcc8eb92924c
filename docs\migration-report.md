# 数据迁移报告

## 迁移概述

本次数据迁移成功将项目中的测试数据迁移到 Supabase 数据库中，为前端开发提供了完整的测试数据环境。

## 迁移结果

### ✅ 成功迁移的数据

1. **客户数据 (customers)** - 5条记录
   - 包含阿里巴巴、腾讯、百度、字节跳动、美团等公司的客户信息
   - 涵盖不同状态：active、potential、inactive
   - 包含完整的联系信息、公司信息、地址等

2. **营销活动数据 (marketing_campaigns)** - 3条记录
   - 春季产品发布会（进行中）
   - 夏季促销活动（已完成）
   - 秋季会员招募（草稿状态）
   - 包含预算、参与人数、转化率等完整数据

### ⚠️ 部分迁移的数据

由于数据库表结构限制，以下数据未能完全迁移：

1. **用户数据 (users)** - 表不存在
2. **跟进记录 (follow_ups)** - 表不存在
3. **见面记录 (meetings)** - 字段不匹配
4. **公海记录 (public_pool)** - 表不存在

## 迁移脚本

### 可用的迁移命令

```bash
# 迁移基础测试数据（客户和营销活动）
npm run migrate

# 尝试迁移所有数据（包括不存在的表）
npm run migrate:all

# 开发环境迁移
npm run migrate:dev
```

### 脚本文件

1. **scripts/migrate-mock-data.ts** - 基础数据迁移脚本
   - 迁移客户数据
   - 迁移营销活动数据
   - 包含数据验证

2. **scripts/migrate-all-data.ts** - 完整数据迁移脚本
   - 尝试迁移所有类型的数据
   - 包含错误处理和容错机制

3. **scripts/test-migration.ts** - 连接测试脚本
   - 验证 Supabase 连接
   - 测试基本数据操作

## 前端使用方式

### 1. 获取客户数据

```typescript
import { supabase } from '@/utils/supabase'

// 获取所有客户
const { data: customers, error } = await supabase
  .from('customers')
  .select('*')

// 获取活跃客户
const { data: activeCustomers } = await supabase
  .from('customers')
  .select('*')
  .eq('status', 'active')

// 按级别筛选
const { data: vipCustomers } = await supabase
  .from('customers')
  .select('*')
  .eq('level', 'A')
```

### 2. 获取营销活动数据

```typescript
// 获取所有营销活动
const { data: campaigns } = await supabase
  .from('marketing_campaigns')
  .select('*')

// 获取进行中的活动
const { data: activeCampaigns } = await supabase
  .from('marketing_campaigns')
  .select('*')
  .eq('status', 'active')

// 按类型筛选
const { data: events } = await supabase
  .from('marketing_campaigns')
  .select('*')
  .eq('type', 'event')
```

### 3. 数据统计

```typescript
// 客户总数
const { count: customerCount } = await supabase
  .from('customers')
  .select('*', { count: 'exact', head: true })

// 活动参与人数统计
const { data: participantStats } = await supabase
  .from('marketing_campaigns')
  .select('name, participant_count, conversion_count')
```

## 环境配置

### 必需的环境变量

确保 `.env` 文件包含以下配置：

```env
# Supabase 配置
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 依赖包

```json
{
  "@supabase/supabase-js": "^2.x.x",
  "dotenv": "^16.x.x",
  "tsx": "^4.x.x"
}
```

## 数据结构

### 客户表 (customers)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键 |
| name | varchar | 客户姓名 |
| phone | varchar | 电话号码 |
| email | varchar | 邮箱地址 |
| company | varchar | 公司名称 |
| position | varchar | 职位 |
| source | varchar | 来源渠道 |
| level | varchar | 客户级别 (A/B/C) |
| status | varchar | 状态 (active/potential/inactive) |
| region | varchar | 地区 |
| address | varchar | 详细地址 |
| tags | varchar | 标签 |
| notes | text | 备注 |

### 营销活动表 (marketing_campaigns)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键 |
| name | varchar | 活动名称 |
| type | varchar | 活动类型 |
| description | text | 活动描述 |
| start_time | timestamptz | 开始时间 |
| end_time | timestamptz | 结束时间 |
| status | varchar | 状态 |
| budget | numeric | 预算 |
| spent | numeric | 已花费 |
| participant_count | integer | 参与人数 |
| conversion_count | integer | 转化人数 |
| target_audience | jsonb | 目标受众 |
| rules | jsonb | 活动规则 |
| prizes | jsonb | 奖品设置 |

## 注意事项

1. **数据权限**: 确保 Supabase 中的 RLS (Row Level Security) 策略允许匿名用户和认证用户访问这些表

2. **数据清理**: 迁移脚本会先清空现有数据，请谨慎在生产环境使用

3. **字段映射**: 部分字段可能与原始 mock 数据结构不完全一致，已根据实际表结构进行调整

4. **扩展性**: 可以根据需要在迁移脚本中添加更多测试数据

## 故障排除

### 常见错误

1. **连接失败**: 检查环境变量配置
2. **权限错误**: 检查 Supabase 表权限设置
3. **字段错误**: 检查表结构是否与脚本中的数据匹配

### 调试方法

```bash
# 测试连接
npx tsx scripts/test-migration.ts

# 查看详细错误
npx tsx scripts/migrate-mock-data.ts
```

## 总结

本次数据迁移成功为前端开发提供了丰富的测试数据，包括：
- 5个不同公司的客户数据
- 3个不同类型的营销活动
- 完整的数据验证机制
- 便捷的迁移脚本

前端开发人员现在可以使用这些数据进行功能开发和测试，无需依赖后端 API 的完整实现。