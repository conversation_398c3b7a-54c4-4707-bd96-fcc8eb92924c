{"migrationId": "1623228f-cd17-473b-b452-ded11a95d32c", "timestamp": "2025-08-18T07:27:20.652Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 9, "failedTables": 12, "totalRecords": 165, "migratedRecords": 0, "failedRecords": 165}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:26:38.167Z", "errors": ["Duplicate entry 'e4af9ff6-7ad5-4b9c-ae03-a49484f4d95b' for key 'users.PRIMARY'", "数据验证失败: MySQL记录数(3) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:42.933Z", "duration": 4766}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T07:26:42.937Z", "errors": ["Duplicate entry '1' for key 'roles.PRIMARY'", "数据验证失败: MySQL记录数(6) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:44.910Z", "duration": 1973}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T07:26:44.913Z", "errors": ["Duplicate entry '1' for key 'permissions.PRIMARY'", "数据验证失败: MySQL记录数(77) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:46.611Z", "duration": 1698}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T07:26:46.615Z", "errors": ["Duplicate entry '1' for key 'role_permissions.PRIMARY'", "数据验证失败: MySQL记录数(6) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:49.537Z", "duration": 2922}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:26:49.540Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 0, "failedRecords": 15, "startTime": "2025-08-18T07:26:49.871Z", "errors": ["Duplicate entry '1' for key 'option_categories.PRIMARY'", "数据验证失败: MySQL记录数(15) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:53.136Z", "duration": 3265}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T07:26:53.139Z", "errors": ["Duplicate entry '1' for key 'option_items.PRIMARY'", "数据验证失败: MySQL记录数(42) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:55.095Z", "duration": 1956}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T07:26:55.097Z", "errors": ["Duplicate entry '63' for key 'customers.PRIMARY'", "数据验证失败: MySQL记录数(5) != 迁移记录数(0)"], "endTime": "2025-08-18T07:26:57.319Z", "duration": 2222}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:26:57.322Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:26:57.587Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:27:02.351Z", "duration": 4764}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:02.353Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:02.653Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:27:03.562Z", "errors": ["Duplicate entry '4' for key 'meetings.PRIMARY'", "数据验证失败: MySQL记录数(2) != 迁移记录数(0)"], "endTime": "2025-08-18T07:27:06.925Z", "duration": 3363}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:06.929Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:27:07.222Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:27:09.844Z", "duration": 2622}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:09.846Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:10.373Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:11.998Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:27:13.634Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:27:14.358Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`follow_ups`, CONSTRAINT `fk_follow_ups_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:27:18.636Z", "duration": 4278}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:27:18.638Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:27:20.649Z", "duration": 2011}], "logs": [{"id": "cdf5e97a-82b0-4543-91a1-ef36f5580b75", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:38.167Z", "end_time": "2025-08-18T07:26:42.933Z", "duration_ms": 4766}, {"id": "bc6ce606-5dcb-4aaf-8617-31d9a5e803d5", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:42.937Z", "end_time": "2025-08-18T07:26:44.910Z", "duration_ms": 1973}, {"id": "9e4fe333-54cf-4946-986c-dc33b595fe14", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:44.913Z", "end_time": "2025-08-18T07:26:46.611Z", "duration_ms": 1698}, {"id": "e877034b-b1d0-404e-b91f-da071938069c", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:46.615Z", "end_time": "2025-08-18T07:26:49.537Z", "duration_ms": 2922}, {"id": "54423afe-463c-4512-b7bd-34d2fc97a3ad", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:49.540Z", "end_time": "2025-08-18T07:26:49.871Z", "duration_ms": 331}, {"id": "a159eb12-aca3-4375-912a-b8db7df187b2", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:49.871Z", "end_time": "2025-08-18T07:26:53.136Z", "duration_ms": 3265}, {"id": "a16dbefe-c144-41d9-a4f3-06c10a0bbd38", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:53.139Z", "end_time": "2025-08-18T07:26:55.095Z", "duration_ms": 1956}, {"id": "1a29b6e1-0457-4a06-8b59-523ef3164c4a", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:55.097Z", "end_time": "2025-08-18T07:26:57.319Z", "duration_ms": 2222}, {"id": "3ff2d2fa-1080-4343-b887-7e30411ffbfc", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:57.322Z", "end_time": "2025-08-18T07:26:57.587Z", "duration_ms": 265}, {"id": "96f2cbdc-bcec-4245-bd6e-147f99250091", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:26:57.587Z", "end_time": "2025-08-18T07:27:02.351Z", "duration_ms": 4764}, {"id": "67c070c7-8be8-4dfd-b156-3d202b2cbaad", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:02.353Z", "end_time": "2025-08-18T07:27:02.653Z", "duration_ms": 300}, {"id": "bc22bb31-c367-48cb-9544-9d5720d12984", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:02.653Z", "end_time": "2025-08-18T07:27:03.562Z", "duration_ms": 909}, {"id": "3d0f8751-6314-44f2-967a-76713d578a4a", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:03.562Z", "end_time": "2025-08-18T07:27:06.925Z", "duration_ms": 3363}, {"id": "d9e78100-f8db-4d7e-9736-b825466ccc44", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:06.929Z", "end_time": "2025-08-18T07:27:07.222Z", "duration_ms": 293}, {"id": "2100d4df-b6a4-4b55-8418-fc11e910f2c6", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:07.223Z", "end_time": "2025-08-18T07:27:09.844Z", "duration_ms": 2621}, {"id": "8f3b0526-c03c-4afd-9684-3ebcd3a82caa", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:09.846Z", "end_time": "2025-08-18T07:27:10.372Z", "duration_ms": 526}, {"id": "c652a0af-fc28-4977-9177-1190e5acbd87", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:10.373Z", "end_time": "2025-08-18T07:27:11.998Z", "duration_ms": 1625}, {"id": "a5f241a2-71bf-462a-937c-b4b477b05d4c", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:11.998Z", "end_time": "2025-08-18T07:27:13.634Z", "duration_ms": 1636}, {"id": "cd6fe5bb-c382-4599-b944-169ec80427a1", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:13.635Z", "end_time": "2025-08-18T07:27:14.358Z", "duration_ms": 723}, {"id": "1f95c9fe-5dcf-48b4-9755-4d324e178669", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:14.358Z", "end_time": "2025-08-18T07:27:18.636Z", "duration_ms": 4278}, {"id": "075b801b-3b2a-41fc-a1c8-faa3879c8ada", "migration_id": "1623228f-cd17-473b-b452-ded11a95d32c", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:27:18.638Z", "end_time": "2025-08-18T07:27:20.649Z", "duration_ms": 2011}]}