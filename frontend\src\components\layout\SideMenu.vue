<template>
  <div class="side-menu" :class="{ collapsed: sidebarCollapsed, dark: isDarkMode }">
    <!-- Logo区域 -->
    <div class="logo-container">
      <div class="logo">
        <n-icon size="32" color="#1890ff">
          <BusinessOutline />
        </n-icon>
        <span v-if="!sidebarCollapsed" class="logo-text">YYSH</span>
      </div>
    </div>
    
    <!-- 菜单区域 -->
    <div class="menu-container">
      <n-menu
        :value="activeMenuKey"
        :expanded-keys="expandedKeys"
        :collapsed="sidebarCollapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="filteredMenuOptions as NaiveMenuOption[]"
        :render-icon="renderMenuIcon"
        :expand-icon="renderExpandIcon"
        :accordion="false"
        :show-option="() => true"
        :render-label="renderMenuLabel"
        @update:value="handleMenuSelect"
        @update:expanded-keys="handleMenuExpand"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h, ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NMenu, NIcon } from 'naive-ui'
import type { MenuOption as NaiveMenuOption } from 'naive-ui'
import type { MenuOption } from '@/types/menu'
import {
  BusinessOutline,
  ChevronDownOutline
} from '@vicons/ionicons5'
import { useAppStore } from '@/stores/modules/app'
import { useAuthStore } from '@/stores/modules/auth'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const authStore = useAuthStore()

// 展开的菜单键 - 默认关闭所有子菜单
const expandedKeys = ref<string[]>([])

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const isDarkMode = computed(() => appStore.isDarkMode)
// 路由映射配置
const routeMap: Record<string, string> = {
  // 仪表板
  'dashboard': '/dashboard',
  
  // 客户管理
  'customer-list': '/customer/list',
  'customer-pool': '/customer/pool', 
  'follow-records': '/follow/records',
  'follow-todos': '/follow/todos',
  
  // 会议管理
  'meeting': '/meeting',
  
  // 营销活动
  'marketing-list': '/marketing/list',
  'marketing-create': '/marketing/create',
  'marketing-progress': '/marketing/progress',
  'marketing-analytics': '/marketing/analytics',
  'tracking': '/marketing/tracking',
  
  // 微信管理
  'wechat-customers': '/wechat/customers',
  'wechat-customer-analysis': '/wechat/customer-analysis',
  'wechat-shares': '/wechat/shares',
  'wechat-groups': '/wechat/groups',
  'wechat-messages': '/wechat/messages',
  
  // 数据分析
  'analytics-overview': '/analytics/overview',
  'analytics-sales': '/analytics/sales',
  'analytics-customer-value': '/analytics/customer-value',
  'analytics-sales-funnel': '/analytics/sales-funnel',
  'analytics-conversion': '/analytics/conversion',
  'analytics-performance': '/analytics/performance',
  
  // 售后管理
  'afterservice': '/afterservice/tickets',
  'afterservice-tickets': '/afterservice/tickets',
  'afterservice-process': '/afterservice/process',
  'afterservice-reminders': '/afterservice/reminders',
  'afterservice-timeline': '/afterservice/timeline',
  
  // 工地管理
  'sites': '/sites',
  'sites-list': '/sites/list',
  'sites-detail': '/sites/detail',
  
  // 动态内容
  'content-company-news': '/content/company-news',
  'content-material-library': '/content/material-library',
  'content-case-showcase': '/content/case-showcase',
  
  // 有优合伙人
  'partners-list': '/partners/list',
  'partners-analytics': '/partners/analytics',
  'partners-mall': '/partners/mall',
  
  // 系统设置
  'settings': '/settings/basic',
  'settings-basic': '/settings/basic',
  'settings-customer': '/settings/customer',
  'settings-organization': '/settings/organization',
  'settings-security': '/settings/security',
  'settings-storage': '/settings/storage',
  'settings-points': '/settings/points',
  'settings-roles': '/settings/roles',
  'settings-options': '/settings/options',
  'system-points': '/settings/points'
}

const activeMenuKey = computed(() => {
  // 根据当前路由设置激活的菜单项
  const path = route.path
  console.log('Current route path:', path)
  
  // 创建路由到菜单项的反向映射
  const pathToMenuKey: Record<string, string> = {}
  Object.entries(routeMap).forEach(([key, routePath]) => {
    pathToMenuKey[routePath] = key
  })
  
  // 精确匹配
  if (pathToMenuKey[path]) {
    return pathToMenuKey[path]
  }
  
  // 模糊匹配（用于处理动态路由或子路由）
  // 客户管理模块
  if (path.startsWith('/customer/')) {
    if (path.includes('/detail/') || path.includes('/edit/')) return 'customer-list'
    if (path.includes('/pool')) return 'customer-pool'
    return 'customer-list'
  }
  
  // 跟进管理模块
  if (path.startsWith('/follow/')) {
    if (path.includes('/records')) return 'follow-records'
    if (path.includes('/todos')) return 'follow-todos'
    return 'follow-records'
  }
  
  // 营销活动模块
  if (path.startsWith('/marketing/')) {
    if (path.includes('/create') || path.includes('/edit/')) return 'marketing-create'
    if (path.includes('/progress')) return 'marketing-progress'
    if (path.includes('/analytics')) return 'marketing-analytics'
    if (path.includes('/tracking')) return 'tracking'
    return 'marketing-list'
  }
  
  // 微信管理模块
  if (path.startsWith('/wechat/')) {
    if (path.includes('/customer-analysis')) return 'wechat-customer-analysis'
    if (path.includes('/shares')) return 'wechat-shares'
    if (path.includes('/groups')) return 'wechat-groups'
    if (path.includes('/messages')) return 'wechat-messages'
    return 'wechat-customers'
  }
  
  // 数据分析模块
  if (path.startsWith('/analytics/')) {
    if (path.includes('/sales')) return 'analytics-sales'
    if (path.includes('/customer-value')) return 'analytics-customer-value'
    if (path.includes('/sales-funnel')) return 'analytics-sales-funnel'
    if (path.includes('/conversion')) return 'analytics-conversion'
    if (path.includes('/performance')) return 'analytics-performance'
    return 'analytics-overview'
  }
  
  // 售后管理模块
  if (path.startsWith('/afterservice/')) {
    if (path.includes('/process')) return 'afterservice-process'
    if (path.includes('/reminders')) return 'afterservice-reminders'
    if (path.includes('/timeline')) return 'afterservice-timeline'
    return 'afterservice-tickets'
  }
  
  // 工地管理模块
  if (path.startsWith('/sites/')) {
    if (path.includes('/detail/')) return 'sites-detail'
    if (path.includes('/list')) return 'sites-list'
    return 'sites'
  }
  
  // 动态内容模块
  if (path.startsWith('/content/')) {
    if (path.includes('/company-news')) return 'content-company-news'
    if (path.includes('/material-library')) return 'content-material-library'
    if (path.includes('/case-showcase')) return 'content-case-showcase'
    return 'content-company-news'
  }
  
  // 有优合伙人模块
  if (path.startsWith('/partners/')) {
    if (path.includes('/analytics')) return 'partners-analytics'
    if (path.includes('/mall')) return 'partners-mall'
    return 'partners-list'
  }
  
  // 系统设置模块
  if (path.startsWith('/settings/')) {
    if (path.includes('/customer')) return 'settings-customer'
    if (path.includes('/organization')) return 'settings-organization'
    if (path.includes('/security')) return 'settings-security'
    if (path.includes('/storage')) return 'settings-storage'
    if (path.includes('/points')) return 'settings-points'
    if (path.includes('/roles')) return 'settings-roles'
    if (path.includes('/options')) return 'settings-options'
    return 'settings-basic'
  }
  
  // 父菜单路由映射 - 确保精确匹配，避免冲突
  if (path === '/dashboard') return 'dashboard'
  if (path === '/meeting') return 'meeting'
  if (path === '/sites') return 'sites'
  
  // 默认返回仪表板
  return 'dashboard'
})

// 渲染菜单图标
const renderMenuIcon = (option: NaiveMenuOption) => {
  const customOption = option as any as MenuOption
  if (customOption.icon && typeof customOption.icon === 'function') {
    return customOption.icon()
  }
  return null
}

// 渲染展开图标
const renderExpandIcon = () => {
  return h(NIcon, null, { default: () => h(ChevronDownOutline) })
}

// 渲染菜单标签（用于折叠状态下的工具提示）
const renderMenuLabel = (option: NaiveMenuOption) => {
  const customOption = option as any as MenuOption
  const labelText = typeof customOption.label === 'function' ? customOption.label() : customOption.label
  if (sidebarCollapsed.value) {
    return h('span', {
      'data-title': labelText,
      style: 'position: relative;'
    }, [labelText])
  }
  return labelText
}

// 处理菜单展开 - 确保互斥激活
const handleMenuExpand = (keys: string[]) => {
  console.log('Menu expand keys:', keys)
  // 确保只有一个父菜单可以展开（互斥激活）
  if (keys.length > 1) {
    // 如果有多个展开的菜单，只保留最新的一个
    const latestKey = keys[keys.length - 1]
    expandedKeys.value = [latestKey]
  } else {
    expandedKeys.value = keys
  }
  console.log('Updated expanded keys after mutual exclusion:', expandedKeys.value)
}

// 过滤菜单选项（根据权限）
const filteredMenuOptions = computed(() => {
  const filterMenu = (options: MenuOption[]): MenuOption[] => {
    return options.filter(option => {
      // 检查权限
      if (option.meta?.permissions) {
        const hasPermission = option.meta.permissions.some((permission: string) => 
          authStore.hasPermission(permission)
        )
        if (!hasPermission) return false
      }
      
      // 递归过滤子菜单
      if (option.children) {
        option.children = filterMenu(option.children)
        // 如果所有子菜单都被过滤掉了，则隐藏父菜单
        if (option.children.length === 0) return false
      }
      
      return true
    })
  }
  
  const filtered = filterMenu([...appStore.menuOptions])
  console.log('Filtered menu options:', filtered)
  console.log('Original menu options:', appStore.menuOptions)
  return filtered
})

// 菜单选择处理
const handleMenuSelect = (key: string) => {
  console.log('Menu select key:', key)
  console.log('Current expanded keys:', expandedKeys.value)
  
  // 检查是否是父菜单，如果是则展开/收起（互斥展开）
  const parentMenus = ['customer', 'marketing', 'wechat', 'analytics', 'afterservice', 'content', 'partners', 'settings', 'system']
  if (parentMenus.includes(key)) {
    if (expandedKeys.value.includes(key)) {
      // 如果当前菜单已展开，则收起
      expandedKeys.value = expandedKeys.value.filter(k => k !== key)
    } else {
      // 如果当前菜单未展开，则收起其他所有菜单，只展开当前菜单（互斥展开）
      expandedKeys.value = [key]
    }
    console.log('Updated expanded keys:', expandedKeys.value)
    return
  }
  
  // 设置激活的菜单项（确保互斥激活）
  appStore.setActiveMenuKey(key)
  
  // 根据菜单key导航到对应路由
  const targetRoute = routeMap[key]
  if (targetRoute && targetRoute !== route.path) {
    router.push(targetRoute)
  }
}

// 初始化展开状态
onMounted(() => {
  console.log('SideMenu mounted')
  console.log('Initial menu options:', appStore.menuOptions)
  console.log('Initial expanded keys:', expandedKeys.value)
  
  // 根据当前路由自动展开对应的父菜单（互斥展开）
  const path = route.path
  let targetParent = ''
  if (path.startsWith('/customer') || path.startsWith('/follow')) {
    targetParent = 'customer'
  } else if (path.startsWith('/marketing')) {
    targetParent = 'marketing'
  } else if (path.startsWith('/wechat')) {
    targetParent = 'wechat'
  } else if (path.startsWith('/analytics')) {
    targetParent = 'analytics'
  } else if (path.startsWith('/afterservice')) {
    targetParent = 'afterservice'
  } else if (path.startsWith('/content')) {
    targetParent = 'content'
  } else if (path.startsWith('/partners')) {
    targetParent = 'partners'
  } else if (path.startsWith('/settings')) {
    targetParent = 'settings'
  } else if (path.startsWith('/sites')) {
    targetParent = 'sites'
  }
  
  // 只展开当前路由对应的父菜单
  if (targetParent) {
    expandedKeys.value = [targetParent]
  }
  
  console.log('Auto-expanded keys based on route:', expandedKeys.value)
})

// 监听路由变化
watch(() => route.path, (newPath) => {
  console.log('Route changed to:', newPath)
  // 根据新路由自动展开对应的父菜单（互斥展开）
  let targetParent = ''
  if (newPath.startsWith('/customer') || newPath.startsWith('/follow')) {
    targetParent = 'customer'
  } else if (newPath.startsWith('/marketing') || newPath === '/tracking') {
    targetParent = 'marketing'
  } else if (newPath.startsWith('/wechat')) {
    targetParent = 'wechat'
  } else if (newPath.startsWith('/analytics')) {
    targetParent = 'analytics'
  } else if (newPath.startsWith('/afterservice')) {
    targetParent = 'afterservice'
  } else if (newPath.startsWith('/content')) {
    targetParent = 'content'
  } else if (newPath.startsWith('/partners')) {
    targetParent = 'partners'
  } else if (newPath.startsWith('/settings')) {
    targetParent = 'settings'
  } else if (newPath.startsWith('/sites')) {
    targetParent = 'sites'
  }
  
  // 如果需要展开新的父菜单，则收起其他所有菜单，只展开目标菜单
  if (targetParent && !expandedKeys.value.includes(targetParent)) {
    expandedKeys.value = [targetParent]
  }
})
</script>

<style scoped>
.side-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  border-right: 1px solid #e5e7eb;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.side-menu.collapsed {
  width: 64px;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
}

.logo-container {
  padding: 20px 16px;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.logo {
  display: flex;
  align-items: center;
  gap: 14px;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.02em;
}

.logo:hover {
  transform: scale(1.03);
  color: #2563eb;
  text-shadow: 0 1px 2px rgba(37, 99, 235, 0.1);
}

.logo-text {
  white-space: nowrap;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 菜单容器样式 - 添加滚动功能但隐藏滚动条 */
.menu-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.menu-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

:deep(.n-menu) {
  padding-top: 8px;
}

/* 菜单项基础样式 */
:deep(.n-menu-item) {
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 菜单项悬停效果 */
:deep(.n-menu-item:hover) {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

/* 菜单项点击效果 */
:deep(.n-menu-item:active) {
  transform: translateX(2px) scale(0.98);
  transition: all 0.1s ease;
}

/* 激活状态样式 */
:deep(.n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
  transform: translateX(6px);
}

:deep(.n-menu-item.n-menu-item--selected::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #fff;
  border-radius: 0 2px 2px 0;
}

:deep(.n-menu-item.n-menu-item--selected .n-menu-item-content-header) {
  color: white;
  font-weight: 600;
}

:deep(.n-menu-item.n-menu-item--selected .n-icon) {
  color: white;
}

/* 菜单项内容样式 */
:deep(.n-menu-item-content) {
  padding-left: 18px !important;
  transition: all 0.3s ease;
}

:deep(.n-menu-item-content-header) {
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 图标样式 */
:deep(.n-menu-item .n-icon) {
  transition: all 0.3s ease;
}

:deep(.n-menu-item:hover .n-icon) {
  transform: scale(1.1);
  color: #1890ff;
}

/* 子菜单样式 */
:deep(.n-submenu) {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

:deep(.n-submenu:hover) {
  background: rgba(24, 144, 255, 0.03);
  border-radius: 8px;
}

:deep(.n-submenu-children) {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 8px;
  margin: 4px 8px;
}

:deep(.n-submenu-children .n-menu-item) {
  margin-left: 16px;
  margin-right: 8px;
  border-radius: 6px;
  position: relative;
}

/* 子菜单项激活状态样式 */
:deep(.n-submenu-children .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
  transform: translateX(8px);
  border-left: 4px solid #389e0d;
}

:deep(.n-submenu-children .n-menu-item.n-menu-item--selected::before) {
  content: '';
  position: absolute;
  left: -4px;
  top: 0;
  width: 4px;
  height: 100%;
  background: #fff;
  border-radius: 0 2px 2px 0;
}

:deep(.n-submenu-children .n-menu-item.n-menu-item--selected .n-menu-item-content-header) {
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:deep(.n-submenu-children .n-menu-item.n-menu-item--selected .n-icon) {
  color: white;
}

/* 子菜单项悬停效果 */
:deep(.n-submenu-children .n-menu-item:hover:not(.n-menu-item--selected)) {
  background: rgba(82, 196, 26, 0.1);
  transform: translateX(4px);
  border-left: 2px solid #52c41a;
}

:deep(.n-submenu-children .n-menu-item:nth-child(1)) {
  transition-delay: 0.05s;
}

:deep(.n-submenu-children .n-menu-item:nth-child(2)) {
  transition-delay: 0.1s;
}

:deep(.n-submenu-children .n-menu-item:nth-child(3)) {
  transition-delay: 0.15s;
}

:deep(.n-submenu-children .n-menu-item:nth-child(4)) {
  transition-delay: 0.2s;
}

:deep(.n-submenu-children .n-menu-item:nth-child(5)) {
  transition-delay: 0.25s;
}

:deep(.n-submenu-children .n-menu-item:hover) {
  transform: translateX(8px);
  background: rgba(24, 144, 255, 0.08);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

:deep(.n-submenu-children .n-menu-item-content) {
  padding-left: 32px !important;
}

/* 子菜单激活状态 */
:deep(.n-submenu-children .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  transform: translateX(12px);
  box-shadow: 0 4px 12px rgba(64, 169, 255, 0.3);
}

/* 展开/收起动画 */
:deep(.n-submenu-arrow) {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-submenu.n-submenu--expanded .n-submenu-arrow) {
  transform: rotate(90deg);
}

/* 子菜单动画关键帧 */
@keyframes submenuSlideDown {
  0% {
    opacity: 0;
    max-height: 0;
    transform: scaleY(0.3);
  }
  50% {
    opacity: 0.5;
    max-height: 250px;
  }
  100% {
    opacity: 1;
    max-height: 500px;
    transform: scaleY(1);
  }
}

@keyframes submenuSlideUp {
  0% {
    opacity: 1;
    max-height: 500px;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.5;
    max-height: 250px;
  }
  100% {
    opacity: 0;
    max-height: 0;
    transform: scaleY(0.3);
  }
}

/* 折叠状态下的样式 */
:deep(.n-menu.n-menu--collapsed .n-menu-item) {
  margin: 6px 8px;
  padding: 12px 0 !important;
  justify-content: center;
  border-radius: 12px;
  position: relative;
  min-height: 48px;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-menu-item-content) {
  padding: 0 !important;
  justify-content: center;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-menu-item-content-header) {
  justify-content: center;
  gap: 0 !important;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-menu-item .n-icon) {
  font-size: 20px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-menu.n-menu--collapsed .n-menu-item:hover) {
  transform: scale(1.1);
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

:deep(.n-menu.n-menu--collapsed .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

:deep(.n-menu.n-menu--collapsed .n-menu-item.n-menu-item--selected .n-icon) {
  color: white;
  transform: scale(1.1);
}

/* 折叠状态下的工具提示 */
:deep(.n-menu.n-menu--collapsed .n-menu-item::after) {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  margin-left: 8px;
  pointer-events: none;
}

:deep(.n-menu.n-menu--collapsed .n-menu-item:hover::after) {
  opacity: 1;
  visibility: visible;
}

/* 折叠状态下隐藏子菜单但保持父菜单项显示 */
:deep(.n-menu.n-menu--collapsed .n-submenu) {
  position: relative;
}

:deep(.n-menu.n-menu--collapsed .n-submenu-children) {
  display: none;
}

/* 折叠状态下的父菜单项样式 */
:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item) {
  margin: 6px 8px;
  padding: 12px 0 !important;
  justify-content: center;
  border-radius: 12px;
  position: relative;
  min-height: 48px;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item-content) {
  padding: 0 !important;
  justify-content: center;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item-content-header) {
  justify-content: center;
  gap: 0 !important;
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item .n-icon) {
  font-size: 20px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item:hover) {
  transform: scale(1.1);
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

:deep(.n-menu.n-menu--collapsed .n-submenu .n-menu-item.n-menu-item--selected .n-icon) {
  color: white;
  transform: scale(1.1);
}

/* 折叠状态下隐藏展开箭头 */
:deep(.n-menu.n-menu--collapsed .n-submenu-arrow) {
  display: none;
}

/* 加载动画 */
@keyframes menuItemPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

:deep(.n-menu-item.n-menu-item--selected) {
  animation: menuItemPulse 2s infinite;
}

/* 菜单箭头样式 */
.menu-arrow {
  margin-left: auto;
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  color: #94a3b8;
}

.menu-arrow.expanded {
  transform: rotate(180deg);
  color: #2563eb;
}

.menu-item.has-children:hover .menu-arrow {
  color: #2563eb;
}

/* 子菜单点样式 */
.submenu-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #cbd5e1;
  margin-right: 12px;
  transition: all 0.25s ease;
  flex-shrink: 0;
}

.submenu-item:hover .submenu-dot {
  background: #2563eb;
  transform: scale(1.2);
}

.submenu-item.active .submenu-dot {
  background: #1d4ed8;
  transform: scale(1.3);
  box-shadow: 0 0 0 2px rgba(29, 78, 216, 0.2);
}

.submenu-item {
  display: flex;
  align-items: center;
}

/* Vue过渡动画 */
.submenu-enter-active,
.submenu-leave-active {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.submenu-enter-from,
.submenu-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-8px);
}

.submenu-enter-to,
.submenu-leave-from {
  max-height: 300px;
  opacity: 1;
  transform: translateY(0);
}

/* 动画 */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }
  to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-8px);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .menu-item {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .submenu-item {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Naive UI Menu 样式覆盖 */
:deep(.n-menu-item) {
  border-radius: 8px !important;
  margin: 4px 12px !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.n-menu-item:hover) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  color: #2563eb !important;
  transform: translateX(2px) !important;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1) !important;
}

:deep(.n-menu-item--selected) {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  color: #1d4ed8 !important;
}

/* 子菜单项样式 */
:deep(.n-submenu-children .n-menu-item) {
  margin: 2px 20px 2px !important;
  padding-right: 2px !important;
  font-size: 13px !important;
  border-radius: 6px !important;
}

:deep(.n-submenu-children .n-menu-item:hover) {
  background: #f1f5f9 !important;
  color: #1d4ed8 !important;
  transform: translateX(4px) !important;
}

:deep(.n-submenu-children .n-menu-item--selected) {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15) !important;
}

:deep(.n-menu-item--selected::before) {
  background: linear-gradient(180deg, #2563eb 0%, #1d4ed8 100%) !important;
  width: 4px !important;
  border-radius: 0 4px 4px 0 !important;
}

:deep(.n-submenu-children) {
  background: rgba(248, 250, 252, 0.5) !important;
  border-radius: 8px !important;
  margin: 4px 12px 8px 20px !important;
  padding: 4px 0 !important;
}

:deep(.n-menu-item-content) {
  padding: 14px 20px !important;
  font-weight: 500 !important;
  letter-spacing: 0.01em !important;
}

:deep(.n-menu-item-content-header) {
  display: flex !important;
  align-items: center !important;
  gap: 14px !important;
}

/* 夜间模式样式适配 */
.dark .side-menu {
  background: linear-gradient(180deg, #1f2937 0%, #111827 50%, #0f172a 100%);
  border-right: 1px solid #374151;
}

.dark .side-menu.collapsed {
  background: linear-gradient(180deg, #1f2937 0%, #111827 50%, #0f172a 100%);
}

.dark .logo-container {
  border-bottom: 1px solid #374151;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.dark .logo-text {
  color: #f9fafb;
}

/* 夜间模式菜单项样式 */
.dark :deep(.n-menu-item) {
  color: #d1d5db !important;
}

.dark :deep(.n-menu-item:hover) {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
  color: #60a5fa !important;
}

.dark :deep(.n-menu-item--selected) {
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
  color: #ffffff !important;
}

/* 夜间模式子菜单样式 */
.dark :deep(.n-submenu-children) {
  background: rgba(31, 41, 55, 0.8) !important;
}

.dark :deep(.n-submenu-children .n-menu-item) {
  color: #9ca3af !important;
}

.dark :deep(.n-submenu-children .n-menu-item:hover) {
  background: #4b5563 !important;
  color: #60a5fa !important;
}

.dark :deep(.n-submenu-children .n-menu-item--selected) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
  color: white !important;
}

/* 夜间模式折叠状态样式 */
.dark :deep(.n-menu.n-menu--collapsed .n-menu-item) {
  color: #d1d5db;
}

.dark :deep(.n-menu.n-menu--collapsed .n-menu-item:hover) {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
  color: #60a5fa;
}

.dark :deep(.n-menu.n-menu--collapsed .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
  color: white;
}

/* 夜间模式图标颜色 */
.dark .menu-item .n-icon {
  color: #9ca3af;
}

.dark .menu-item:hover .n-icon {
  color: #60a5fa;
}

.dark .menu-item.active .n-icon {
  color: #ffffff;
}

/* 夜间模式子菜单点样式 */
.dark .submenu-dot {
  background: #6b7280;
}

.dark .submenu-item:hover .submenu-dot {
  background: #60a5fa;
}

.dark .submenu-item.active .submenu-dot {
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

/* 夜间模式箭头样式 */
.dark .menu-arrow {
  color: #6b7280;
}

.dark .menu-arrow.expanded {
  color: #60a5fa;
}

.dark .menu-item.has-children:hover .menu-arrow {
  color: #60a5fa;
}
</style>