{"migrationId": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "timestamp": "2025-08-18T06:12:57.081Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 10, "failedTables": 11, "totalRecords": 165, "migratedRecords": 15, "failedRecords": 150}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:12:09.615Z", "errors": ["Unknown column 'username' in 'field list'"], "endTime": "2025-08-18T06:12:16.597Z", "duration": 6982}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:12:16.601Z", "errors": ["Unknown column 'display_name' in 'field list'"], "endTime": "2025-08-18T06:12:18.900Z", "duration": 2299}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T06:12:18.904Z", "errors": ["Unknown column 'display_name' in 'field list'"], "endTime": "2025-08-18T06:12:21.305Z", "duration": 2401}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:12:21.309Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`role_permissions`, CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T06:12:24.200Z", "duration": 2891}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:24.203Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:12:24.892Z", "errors": [], "endTime": "2025-08-18T06:12:29.596Z", "duration": 4704}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:12:29.599Z", "errors": ["Unknown column 'code' in 'field list'"], "endTime": "2025-08-18T06:12:32.867Z", "duration": 3268}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:12:32.870Z", "errors": ["Unknown column 'company' in 'field list'"], "endTime": "2025-08-18T06:12:35.390Z", "duration": 2520}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:35.392Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:12:35.754Z", "errors": ["Unknown column 'cover_image' in 'field list'"], "endTime": "2025-08-18T06:12:38.690Z", "duration": 2936}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:38.692Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:39.250Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:12:40.384Z", "errors": ["Unknown column 'type' in 'field list'"], "endTime": "2025-08-18T06:12:42.687Z", "duration": 2303}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:42.690Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:12:42.966Z", "errors": ["Unknown column 'auto_release_days' in 'field list'"], "endTime": "2025-08-18T06:12:44.497Z", "duration": 1531}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:44.500Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:44.864Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:46.643Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:12:47.825Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:12:49.441Z", "errors": ["Unknown column 'type' in 'field list'"], "endTime": "2025-08-18T06:12:53.721Z", "duration": 4280}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:12:53.723Z", "errors": ["Unknown column 'moved_by' in 'field list'"], "endTime": "2025-08-18T06:12:57.078Z", "duration": 3355}], "logs": [{"id": "1862caa8-c161-464c-a148-ebbef7edae4f", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:09.615Z", "end_time": "2025-08-18T06:12:16.597Z", "duration_ms": 6982}, {"id": "941dabb4-51fc-4a0d-9f42-862300a2e13d", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:16.602Z", "end_time": "2025-08-18T06:12:18.900Z", "duration_ms": 2298}, {"id": "e9c4cc1f-8e91-4803-9741-317b7ae76ef0", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:18.904Z", "end_time": "2025-08-18T06:12:21.305Z", "duration_ms": 2401}, {"id": "fd9ee5e6-fb2b-43b8-82c5-5932d57548ea", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:21.309Z", "end_time": "2025-08-18T06:12:24.200Z", "duration_ms": 2891}, {"id": "12586d52-b9c1-411f-b51c-63d7869e8166", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:24.203Z", "end_time": "2025-08-18T06:12:24.892Z", "duration_ms": 689}, {"id": "18d30f47-6038-4b83-b4f0-21e16fc1bc2d", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:12:24.892Z", "end_time": "2025-08-18T06:12:29.596Z", "duration_ms": 4704}, {"id": "e4da1267-fb91-4d07-abf4-c655ad8b63cd", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:29.599Z", "end_time": "2025-08-18T06:12:32.867Z", "duration_ms": 3268}, {"id": "143d318b-89fb-4c7e-a7b1-cea26d6dcdc2", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:32.870Z", "end_time": "2025-08-18T06:12:35.390Z", "duration_ms": 2520}, {"id": "edeb17ae-8267-49a4-9aeb-6ca910798712", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:35.392Z", "end_time": "2025-08-18T06:12:35.753Z", "duration_ms": 361}, {"id": "92a837d9-a46e-4370-a8a7-924940654198", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:35.754Z", "end_time": "2025-08-18T06:12:38.690Z", "duration_ms": 2936}, {"id": "d472c1ba-9fdb-4f54-8f9f-cff0522f454d", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:38.692Z", "end_time": "2025-08-18T06:12:39.250Z", "duration_ms": 558}, {"id": "fe62485a-85fc-4bf0-aa3d-e18903b09bec", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:39.250Z", "end_time": "2025-08-18T06:12:40.384Z", "duration_ms": 1134}, {"id": "00439bc4-d979-49fb-a1b0-61f95e96821e", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:40.384Z", "end_time": "2025-08-18T06:12:42.687Z", "duration_ms": 2303}, {"id": "32414f10-19f6-4f84-b165-7032d4748628", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:42.690Z", "end_time": "2025-08-18T06:12:42.966Z", "duration_ms": 276}, {"id": "26cd6f16-cb46-4c42-984c-77397250ee5a", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:42.966Z", "end_time": "2025-08-18T06:12:44.497Z", "duration_ms": 1531}, {"id": "07e68384-a663-463a-8d29-693b01eaf14b", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:44.500Z", "end_time": "2025-08-18T06:12:44.863Z", "duration_ms": 363}, {"id": "94d47b78-20e2-40ab-92c1-c33c89bc9094", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:44.864Z", "end_time": "2025-08-18T06:12:46.643Z", "duration_ms": 1779}, {"id": "a29ee548-7a06-4630-a409-c88eac7650cd", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:46.644Z", "end_time": "2025-08-18T06:12:47.825Z", "duration_ms": 1181}, {"id": "0fd88642-764b-42f5-b468-6fa3174ead1b", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:47.825Z", "end_time": "2025-08-18T06:12:49.440Z", "duration_ms": 1615}, {"id": "8398fdd7-38d3-4ca1-b0f1-6e40887da382", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:49.441Z", "end_time": "2025-08-18T06:12:53.721Z", "duration_ms": 4280}, {"id": "c77ecccd-8323-4b11-9a85-4ffadd55dc73", "migration_id": "a547e04a-70ca-4310-a5e3-9f41c57690f6", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:12:53.723Z", "end_time": "2025-08-18T06:12:57.078Z", "duration_ms": 3355}]}