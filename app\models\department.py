"""部门模型

定义部门相关的数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel


class Department(BaseModel):
    """部门模型
    
    对应数据库中的departments表
    """
    __tablename__ = "departments"
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="部门名称")
    code = Column(String(50), unique=True, nullable=True, comment="部门编码")
    description = Column(Text, nullable=True, comment="部门描述")
    
    # 层级关系
    parent_id = Column(String(36), nullable=True, comment="上级部门ID")
    level = Column(Integer, default=1, nullable=False, comment="部门层级")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    
    # 负责人
    manager_id = Column(String(36), nullable=True, comment="部门负责人ID")
    
    # 状态
    is_active = Column(<PERSON><PERSON><PERSON>, default=True, nullable=False, comment="是否启用")
    
    # 关联关系
    users = relationship("User", back_populates="department", lazy="select")
    
    def __repr__(self):
        return f"<Department(id={self.id}, name={self.name})>"
    
    @property
    def full_name(self):
        """获取完整部门名称（包含层级）"""
        # 这里可以实现递归获取完整路径的逻辑
        return self.name