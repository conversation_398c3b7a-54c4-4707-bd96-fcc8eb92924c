import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { createDiscrete<PERSON><PERSON> } from 'naive-ui'
import type { User } from '@/types'

// 创建独立的message API，用于在store中使用
const { message } = createDiscreteApi(['message'])

export const useUserStore = defineStore('user', () => {
  
  // 状态
  const users = ref<User[]>([])
  const loading = ref(false)
  const total = ref(0)
  
  // 统计数据
  const stats = reactive({
    total: 0,
    active: 0,
    todayLogin: 0,
    newUsers: 0
  })
  
  // 获取用户列表
  const fetchUsers = async (_params?: any) => {
    loading.value = true
    try {
      // 模拟API调用
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'admin',
          realName: '管理员',
          phone: '13800138000',
      
          roleId: 1,
          roleName: '超级管理员',
          departmentId: 1,
          departmentName: '管理部',
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          remark: '系统管理员'
        },
        {
          id: 2,
          username: 'sales001',
          realName: '张三',
          phone: '13800138001',
      
          roleId: 4,
          roleName: '销售员',
          departmentId: 2,
          departmentName: '销售部',
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          remark: '销售员'
        }
      ]
      
      users.value = mockUsers
      total.value = mockUsers.length
      
      // 更新统计数据
      stats.total = mockUsers.length
      stats.active = mockUsers.filter(u => u.status === 'active').length
      stats.todayLogin = 1
      stats.newUsers = 0
      
    } catch (error) {
      console.error('获取用户列表失败:', error)
      message.error('获取用户列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 创建用户
  const createUser = async (userData: Partial<User>) => {
    try {
      // 模拟API调用
      const newUser: User = {
        id: Date.now(),
        username: userData.username || '',
        realName: userData.realName || '',
        phone: userData.phone || '',
    
        roleId: userData.roleId || 1,
        roleName: userData.roleName || '用户',
        departmentId: userData.departmentId || 1,
        departmentName: userData.departmentName || '默认部门',
        status: userData.status || 'active',
        lastLoginAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        remark: userData.remark || ''
      }
      
      users.value.push(newUser)
      total.value = users.value.length
      message.success('用户创建成功')
      return newUser
    } catch (error) {
      console.error('创建用户失败:', error)
      message.error('创建用户失败')
      throw error
    }
  }
  
  // 更新用户
  const updateUser = async (id: number, userData: Partial<User>) => {
    try {
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index] = { ...users.value[index], ...userData }
        message.success('用户更新成功')
        return users.value[index]
      }
      throw new Error('用户不存在')
    } catch (error) {
      console.error('更新用户失败:', error)
      message.error('更新用户失败')
      throw error
    }
  }
  
  // 删除用户
  const deleteUser = async (id: number) => {
    try {
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value.splice(index, 1)
        total.value = users.value.length
        message.success('用户删除成功')
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      message.error('删除用户失败')
      throw error
    }
  }
  
  // 重置密码
  const resetPassword = async (_id: number, _newPassword: string) => {
    try {
      // 模拟API调用
      message.success('密码重置成功')
    } catch (error) {
      console.error('重置密码失败:', error)
      message.error('重置密码失败')
      throw error
    }
  }
  
  return {
    // 状态
    users,
    loading,
    total,
    stats,
    
    // 方法
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    resetPassword
  }
})