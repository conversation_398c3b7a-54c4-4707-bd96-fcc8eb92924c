// 选项分类接口
export interface OptionCategory {
  id: string
  code: string
  name: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// 选项数据接口
export interface OptionItem {
  id: string
  category_id: string
  code: string
  value: string
  label: string
  description?: string
  color?: string
  icon?: string
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 创建选项分类请求
export interface CreateOptionCategoryRequest {
  code: string
  name: string
  description: string
  sort_order: number
  is_active: boolean
}

// 更新选项分类请求
export interface UpdateOptionCategoryRequest {
  code?: string
  name: string
  description: string
  sort_order: number
  is_active: boolean
}

// 创建选项数据请求
export interface CreateOptionItemRequest {
  category_id: string
  code: string
  value: string
  label: string
  description: string
  sort_order: number
  is_active: boolean
  color?: string
  icon?: string
}

// 更新选项数据请求
export interface UpdateOptionItemRequest {
  id?: string
  category_id?: string
  code: string
  value: string
  label: string
  description: string
  sort_order: number
  is_active: boolean
  color?: string
  icon?: string
}

// 分页参数基础接口
export interface PaginationParams {
  page: number
  page_size: number
}

// 获取选项分类列表参数
export interface GetOptionCategoriesParams extends PaginationParams {
  search?: string
  is_active?: boolean
  sort_by?: 'id' | 'code' | 'name' | 'sort_order' | 'created_at'
  sort_order?: 'asc' | 'desc'
}

// 获取选项数据列表参数
export interface GetOptionItemsParams extends PaginationParams {
  category_code?: string
  search?: string
  is_active?: boolean
  sort_by?: 'id' | 'value' | 'label' | 'sort_order' | 'created_at'
  sort_order?: 'asc' | 'desc'
}

// 分页响应基础接口
export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 选项分类列表响应
export type OptionCategoryListResponse = PaginationResponse<OptionCategory>

// 选项数据列表响应
export type OptionItemListResponse = PaginationResponse<OptionItem>

// 包含选项项的分类接口
export interface OptionCategoryWithItems extends OptionCategory {
  items: OptionItem[]
}

// 批量操作请求
export interface BatchDeleteRequest {
  ids: number[]
}
// 批量更新排序请求
export interface BatchUpdateSortRequest {
  items: Array<{
    id: string
    sort_order: number
  }>
}

export interface BatchUpdateStatusRequest {
  ids: number[]
  is_active: boolean
}

// 选项数据的扩展属性类型定义
export interface OptionExtraData {
  // 颜色相关
  color?: string
  backgroundColor?: string
  
  // 图标相关
  icon?: string
  iconColor?: string
  
  // 业务相关
  businessType?: string
  priority?: number
  
  // 显示相关
  tooltip?: string
  badge?: string
  
  // 权限相关
  permissions?: string[]
  roles?: string[]
  
  // 其他自定义属性
  [key: string]: any
}

// 常用的选项分类代码常量
export const OPTION_CATEGORIES = {
  CUSTOMER_SOURCE: 'customer_source',
  CUSTOMER_LEVEL: 'customer_level', 
  GENDER: 'gender',
  DECORATION_TYPE: 'decoration_type',
  HOUSE_STATUS: 'house_status',
  FOLLOW_STATUS: 'follow_status',
  CUSTOMER_TAG: 'customer_tag',
  SALES_STAGE: 'sales_stage',
  CONTACT_METHOD: 'contact_method',
  REGION: 'region'
} as const

// 选项分类代码类型
export type OptionCategoryCode = typeof OPTION_CATEGORIES[keyof typeof OPTION_CATEGORIES]

// 用于前端组件的选项接口
export interface SelectOption {
  label: string
  value: string
  disabled?: boolean
  extra?: OptionExtraData
}

// 选项组接口（用于分组显示）
export interface OptionGroup {
  label: string
  key: string
  children: SelectOption[]
}

// 选项配置接口（用于动态配置选项行为）
export interface OptionConfig {
  categoryCode: string
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  placeholder?: string
  maxTagCount?: number
  showCheckAll?: boolean
  groupBy?: string
}

// API 响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 错误响应接口
export interface ApiError {
  code: number
  message: string
  details?: string
  field?: string
}