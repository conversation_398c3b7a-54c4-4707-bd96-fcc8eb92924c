/**
 * Jest Test Setup
 * 测试环境初始化配置
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.TEST_MYSQL_HOST = process.env.TEST_MYSQL_HOST || 'localhost';
process.env.TEST_MYSQL_PORT = process.env.TEST_MYSQL_PORT || '3306';
process.env.TEST_MYSQL_USER = process.env.TEST_MYSQL_USER || 'root';
process.env.TEST_MYSQL_PASSWORD = process.env.TEST_MYSQL_PASSWORD || '';
process.env.TEST_MYSQL_DATABASE = process.env.TEST_MYSQL_DATABASE || 'yysh_crm_test';

// 设置测试超时时间
jest.setTimeout(30000);

// 全局测试前置处理
beforeAll(async () => {
  console.log('🚀 开始运行测试...');
});

// 全局测试后置处理
afterAll(async () => {
  console.log('✅ 测试运行完成');
});

// 捕获未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 捕获未处理的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

// 模拟控制台方法（可选）
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};