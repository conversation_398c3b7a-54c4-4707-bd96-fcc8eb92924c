import type { Customer } from '@/api/customerService'

/**
 * 客户阶段枚举
 */
export enum CustomerStage {
  INITIAL = 'initial',        // 初始阶段
  POTENTIAL = 'potential',    // 潜在客户
  FOLLOWING = 'following',    // 跟进中
  CONTRACTED = 'contracted',  // 已签约
  CONSTRUCTION = 'construction', // 施工中
  COMPLETED = 'completed'     // 已完工
}

/**
 * 客户阶段工具类
 */
export const customerStageUtils = {
  /**
   * 根据客户信息计算当前阶段
   * @param customer 客户信息
   * @returns 客户阶段
   */
  calculateStage(customer: Customer): CustomerStage {
    if (!customer) return CustomerStage.INITIAL

    // 根据客户状态判断阶段
    switch (customer.status) {
      case 'deal':
        return CustomerStage.COMPLETED
      case 'negotiating':
        return CustomerStage.CONTRACTED
      case 'contacted':
        return CustomerStage.FOLLOWING
      case 'potential':
        return CustomerStage.POTENTIAL
      case 'lost':
        return CustomerStage.INITIAL
      default:
        // 检查基础信息是否完整
        if (customer.name && customer.phone) {
          return CustomerStage.POTENTIAL
        }
        return CustomerStage.INITIAL
    }
  },

  /**
   * 获取阶段进度百分比
   * @param stage 客户阶段
   * @returns 进度百分比 (0-100)
   */
  getStageProgress(stage: CustomerStage): number {
    switch (stage) {
      case CustomerStage.INITIAL:
        return 10
      case CustomerStage.POTENTIAL:
        return 25
      case CustomerStage.FOLLOWING:
        return 50
      case CustomerStage.CONTRACTED:
        return 75
      case CustomerStage.CONSTRUCTION:
        return 90
      case CustomerStage.COMPLETED:
        return 100
      default:
        return 0
    }
  },

  /**
   * 计算客户信息完整度
   * @param customer 客户信息
   * @returns 完整度百分比 (0-100)
   */
  calculateInfoCompleteness(customer: Customer): number {
    if (!customer) return 0

    let completedFields = 0
    const totalFields = 10

    // 基础信息字段
    if (customer.name) completedFields++
    if (customer.phone) completedFields++
    if (customer.gender) completedFields++
    if (customer.address) completedFields++
    if (customer.source) completedFields++
    if (customer.decorationType) completedFields++
    if (customer.company) completedFields++
    if (customer.position) completedFields++
    if (customer.region && customer.region.length > 0) completedFields++
    if (customer.remark) completedFields++

    return Math.round((completedFields / totalFields) * 100)
  },

  /**
   * 获取下一步行动建议
   * @param customer 客户信息
   * @returns 建议列表
   */
  getNextActionSuggestions(customer: Customer): string[] {
    const stage = this.calculateStage(customer)

    switch (stage) {
      case CustomerStage.INITIAL:
        return [
          '完善客户基础信息',
          '确认客户联系方式',
          '了解客户装修需求'
        ]
      case CustomerStage.POTENTIAL:
        return [
          '安排首次跟进',
          '邀请客户到店咨询',
          '了解客户预算范围',
          '分配专属设计师'
        ]
      case CustomerStage.FOLLOWING:
        return [
          '安排上门量房',
          '制定设计方案',
          '准备报价单',
          '推进签约进程'
        ]
      case CustomerStage.CONTRACTED:
        return [
          '安排项目监理',
          '确定开工时间',
          '准备施工材料',
          '办理开工手续'
        ]
      case CustomerStage.CONSTRUCTION:
        return [
          '跟进施工进度',
          '定期质量检查',
          '与客户沟通进展',
          '准备竣工验收'
        ]
      case CustomerStage.COMPLETED:
        return [
          '客户满意度调查',
          '售后服务跟进',
          '维护客户关系',
          '推荐新客户'
        ]
      default:
        return []
    }
  },

  /**
   * 获取阶段显示文本
   * @param stage 客户阶段
   * @returns 显示文本
   */
  getStageText(stage: CustomerStage): string {
    switch (stage) {
      case CustomerStage.INITIAL:
        return '初始阶段'
      case CustomerStage.POTENTIAL:
        return '潜在客户'
      case CustomerStage.FOLLOWING:
        return '跟进中'
      case CustomerStage.CONTRACTED:
        return '已签约'
      case CustomerStage.CONSTRUCTION:
        return '施工中'
      case CustomerStage.COMPLETED:
        return '已完工'
      default:
        return '未知'
    }
  },

  /**
   * 获取阶段标签类型
   * @param stage 客户阶段
   * @returns 标签类型
   */
  getStageTagType(stage: CustomerStage): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
    switch (stage) {
      case CustomerStage.INITIAL:
        return 'default'
      case CustomerStage.POTENTIAL:
        return 'info'
      case CustomerStage.FOLLOWING:
        return 'primary'
      case CustomerStage.CONTRACTED:
        return 'warning'
      case CustomerStage.CONSTRUCTION:
        return 'warning'
      case CustomerStage.COMPLETED:
        return 'success'
      default:
        return 'default'
    }
  },

  /**
   * 获取阶段颜色
   * @param stage 客户阶段
   * @returns 颜色值
   */
  getStageColor(stage: CustomerStage): string {
    switch (stage) {
      case CustomerStage.INITIAL:
        return '#d9d9d9'
      case CustomerStage.POTENTIAL:
        return '#1890ff'
      case CustomerStage.FOLLOWING:
        return '#722ed1'
      case CustomerStage.CONTRACTED:
        return '#fa8c16'
      case CustomerStage.CONSTRUCTION:
        return '#faad14'
      case CustomerStage.COMPLETED:
        return '#52c41a'
      default:
        return '#d9d9d9'
    }
  },

  /**
   * 获取阶段图标
   * @param stage 客户阶段
   * @returns 图标名称
   */
  getStageIcon(stage: CustomerStage): string {
    switch (stage) {
      case CustomerStage.INITIAL:
        return 'person-add-outline'
      case CustomerStage.POTENTIAL:
        return 'person-outline'
      case CustomerStage.FOLLOWING:
        return 'chatbubble-outline'
      case CustomerStage.CONTRACTED:
        return 'document-text-outline'
      case CustomerStage.CONSTRUCTION:
        return 'hammer-outline'
      case CustomerStage.COMPLETED:
        return 'checkmark-circle-outline'
      default:
        return 'help-outline'
    }
  },

  /**
   * 获取所有阶段列表
   * @returns 阶段列表
   */
  getAllStages(): Array<{ value: CustomerStage; label: string; color: string }> {
    return [
      {
        value: CustomerStage.INITIAL,
        label: this.getStageText(CustomerStage.INITIAL),
        color: this.getStageColor(CustomerStage.INITIAL)
      },
      {
        value: CustomerStage.POTENTIAL,
        label: this.getStageText(CustomerStage.POTENTIAL),
        color: this.getStageColor(CustomerStage.POTENTIAL)
      },
      {
        value: CustomerStage.FOLLOWING,
        label: this.getStageText(CustomerStage.FOLLOWING),
        color: this.getStageColor(CustomerStage.FOLLOWING)
      },
      {
        value: CustomerStage.CONTRACTED,
        label: this.getStageText(CustomerStage.CONTRACTED),
        color: this.getStageColor(CustomerStage.CONTRACTED)
      },
      {
        value: CustomerStage.CONSTRUCTION,
        label: this.getStageText(CustomerStage.CONSTRUCTION),
        color: this.getStageColor(CustomerStage.CONSTRUCTION)
      },
      {
        value: CustomerStage.COMPLETED,
        label: this.getStageText(CustomerStage.COMPLETED),
        color: this.getStageColor(CustomerStage.COMPLETED)
      }
    ]
  }
}