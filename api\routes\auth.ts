/**
 * This is a user authentication API route demo.
 * Handle user registration, login, token management, etc.
 */
import { Router, Request, Response } from 'express';

// 全局类型声明
declare global {
  var wechatLoginTokens: Map<string, {
    status: string;
    createdAt: number;
    expiresAt: number;
    userToken?: string;
  }>;
}

// 简单的二维码SVG生成函数
function generateQRCodeSVG(data: string): string {
  const size = 200;
  const moduleSize = 8;
  const modules = Math.floor(size / moduleSize);
  
  // 简化的二维码模式生成（实际项目中应使用专业的二维码库）
  const pattern = generateSimplePattern(data, modules);
  
  let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`;
  svg += `<rect width="${size}" height="${size}" fill="white"/>`;
  
  for (let y = 0; y < modules; y++) {
    for (let x = 0; x < modules; x++) {
      if (pattern[y] && pattern[y][x]) {
        svg += `<rect x="${x * moduleSize}" y="${y * moduleSize}" width="${moduleSize}" height="${moduleSize}" fill="black"/>`;
      }
    }
  }
  
  svg += '</svg>';
  return svg;
}

// 生成简单的二维码模式
function generateSimplePattern(data: string, size: number): boolean[][] {
  const pattern: boolean[][] = [];
  
  // 初始化模式
  for (let i = 0; i < size; i++) {
    pattern[i] = new Array(size).fill(false);
  }
  
  // 添加定位标记（三个角落的方块）
  addFinderPattern(pattern, 0, 0);
  addFinderPattern(pattern, size - 7, 0);
  addFinderPattern(pattern, 0, size - 7);
  
  // 根据数据生成简单的模式
  const hash = simpleHash(data);
  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      if (!isFinderPattern(i, j, size)) {
        pattern[i][j] = ((hash + i * j) % 3) === 0;
      }
    }
  }
  
  return pattern;
}

// 添加定位标记
function addFinderPattern(pattern: boolean[][], startX: number, startY: number) {
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 7; j++) {
      if (startX + i < pattern.length && startY + j < pattern[0].length) {
        const isBlack = (i === 0 || i === 6 || j === 0 || j === 6) || 
                       (i >= 2 && i <= 4 && j >= 2 && j <= 4);
        pattern[startX + i][startY + j] = isBlack;
      }
    }
  }
}

// 检查是否是定位标记区域
function isFinderPattern(x: number, y: number, size: number): boolean {
  return (x < 9 && y < 9) || 
         (x < 9 && y >= size - 8) || 
         (x >= size - 8 && y < 9);
}

// 简单哈希函数
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash);
}


const router = Router();

/**
 * User Login
 * POST /api/auth/register
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  // TODO: Implement register logic
});

/**
 * User Login
 * POST /api/auth/login
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { username, password, captcha } = req.body;
    
    // 验证必填字段
    if (!username || !password) {
      res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
      return;
    }
    
    // 默认管理员账号验证
    if (username === 'admin' && password === 'password') {
      // 生成简单的token
      const token = 'admin_token_' + Date.now();
      
      res.status(200).json({
        success: true,
        message: '登录成功',
        data: {
          token,
          user: {
            id: 1,
            username: 'admin',
            name: '管理员',
            role: 'admin',
            permissions: ['*']
          }
        }
      });
      return;
    }
    
    // 用户名或密码错误
    res.status(401).json({
      success: false,
      message: '用户名或密码错误'
    });
    
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * User Logout
 * POST /api/auth/logout
 */
router.post('/logout', async (req: Request, res: Response): Promise<void> => {
  // TODO: Implement logout logic
  res.status(200).json({
    success: true,
    message: '退出成功'
  });
});

/**
 * Get Captcha
 * GET /api/auth/captcha
 */
router.get('/captcha', async (req: Request, res: Response): Promise<void> => {
  try {
    // 生成简单的数字验证码
    const captcha = Math.floor(1000 + Math.random() * 9000).toString();
    
    // 这里应该将验证码存储到session或redis中
    // 为了演示，我们返回一个简单的SVG验证码
    const svg = `
      <svg width="120" height="40" xmlns="http://www.w3.org/2000/svg">
        <rect width="120" height="40" fill="#f0f0f0" stroke="#ccc"/>
        <text x="60" y="25" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">${captcha}</text>
        <line x1="10" y1="10" x2="110" y2="30" stroke="#999" stroke-width="1"/>
        <line x1="20" y1="30" x2="100" y2="10" stroke="#999" stroke-width="1"/>
      </svg>
    `;
    
    res.setHeader('Content-Type', 'image/svg+xml');
    res.send(svg);
  } catch (error) {
    console.error('生成验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '生成验证码失败'
    });
  }
});

/**
 * WeChat Work Login
 * POST /api/auth/wechat-login
 */
router.post('/wechat-login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { code, state } = req.body;
    
    // 验证必填字段
    if (!code) {
      res.status(400).json({
        success: false,
        message: '授权码不能为空'
      });
      return;
    }
    
    // TODO: 实际项目中需要调用企业微信API获取用户信息
    // 这里模拟企业微信登录成功
    const mockWeChatUser = {
      userid: 'wechat_user_001',
      name: '微信用户',
      mobile: '13800138000',
      email: '<EMAIL>',
      department: ['技术部'],
      position: '开发工程师'
    };
    
    // 生成token
    const token = 'wechat_token_' + Date.now();
    
    res.status(200).json({
      success: true,
      message: '企业微信登录成功',
      data: {
        token,
        user: {
          id: 2,
          username: mockWeChatUser.userid,
          name: mockWeChatUser.name,
          mobile: mockWeChatUser.mobile,
          email: mockWeChatUser.email,
          role: 'user',
          permissions: ['read', 'write'],
          loginType: 'wechat'
        }
      }
    });
    
  } catch (error) {
    console.error('企业微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '企业微信登录失败'
    });
  }
});

/**
 * Get WeChat Work QR Code
 * GET /api/auth/wechat-qr
 */
router.get('/wechat-qr', async (req: Request, res: Response): Promise<void> => {
  try {
    // 生成唯一的token用于标识此次登录请求
    const token = 'qr_token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // 生成本地SVG二维码
    const qrData = 'wechat_login:' + token;
    const qrSvg = generateQRCodeSVG(qrData);
    
    // 将SVG转换为Data URL
    const qrUrl = `data:image/svg+xml;base64,${Buffer.from(qrSvg).toString('base64')}`;
    
    // 在实际项目中，这里应该将token存储到Redis等缓存中，设置过期时间
    // 这里我们使用内存存储（仅用于演示）
    global.wechatLoginTokens = global.wechatLoginTokens || new Map();
    global.wechatLoginTokens.set(token, {
      status: 'pending',
      createdAt: Date.now(),
      expiresAt: Date.now() + 5 * 60 * 1000 // 5分钟过期
    });
    
    res.status(200).json({
      success: true,
      data: {
        qrUrl,
        token
      }
    });
    
  } catch (error) {
    console.error('获取企业微信二维码错误:', error);
    res.status(500).json({
      success: false,
      message: '获取企业微信二维码失败'
    });
  }
});

/**
 * Check WeChat Login Status
 * GET /api/auth/wechat-status
 */
router.get('/wechat-status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.query;
    
    if (!token) {
      res.status(400).json({
        success: false,
        message: 'Token参数不能为空'
      });
      return;
    }
    
    // 检查token状态
    global.wechatLoginTokens = global.wechatLoginTokens || new Map();
    const loginInfo = global.wechatLoginTokens.get(token as string);
    
    if (!loginInfo) {
      res.status(200).json({
        success: true,
        data: {
          status: 'expired'
        }
      });
      return;
    }
    
    // 检查是否过期
    if (Date.now() > loginInfo.expiresAt) {
      global.wechatLoginTokens.delete(token as string);
      res.status(200).json({
        success: true,
        data: {
          status: 'expired'
        }
      });
      return;
    }
    
    // 模拟扫码登录成功（实际项目中这里应该检查企业微信的回调状态）
    // 为了演示，我们在30秒后自动设置为成功状态
    if (Date.now() - loginInfo.createdAt > 30000 && loginInfo.status === 'pending') {
      const userToken = 'wechat_user_token_' + Date.now();
      loginInfo.status = 'success';
      loginInfo.userToken = userToken;
      
      res.status(200).json({
        success: true,
        data: {
          status: 'success',
          userToken
        }
      });
      
      // 清理token
      global.wechatLoginTokens.delete(token as string);
      return;
    }
    
    res.status(200).json({
      success: true,
      data: {
        status: loginInfo.status
      }
    });
    
  } catch (error) {
    console.error('检查企业微信登录状态错误:', error);
    res.status(500).json({
      success: false,
      message: '检查登录状态失败'
    });
  }
});

/**
 * Get WeChat Work Login URL (保留原有接口)
 * GET /api/auth/wechat-url
 */
router.get('/wechat-url', async (req: Request, res: Response): Promise<void> => {
  try {
    // TODO: 实际项目中需要配置企业微信应用信息
    const corpId = process.env.WECHAT_CORP_ID || 'your_corp_id';
    const agentId = process.env.WECHAT_AGENT_ID || 'your_agent_id';
    const redirectUri = encodeURIComponent(process.env.WECHAT_REDIRECT_URI || 'http://localhost:8080/auth/wechat/callback');
    const state = 'STATE_' + Date.now();
    
    const wechatUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&agentid=${agentId}&state=${state}#wechat_redirect`;
    
    res.status(200).json({
      success: true,
      data: {
        url: wechatUrl,
        state
      }
    });
    
  } catch (error) {
    console.error('获取企业微信登录URL错误:', error);
    res.status(500).json({
      success: false,
      message: '获取企业微信登录URL失败'
    });
  }
});

/**
 * Get User Info
 * GET /api/auth/user
 */
router.get('/user', async (req: Request, res: Response): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader?.replace('Bearer ', '');
    
    if (!token) {
      res.status(401).json({
        success: false,
        message: '未提供认证token'
      });
      return;
    }
    
    // 验证token并返回用户信息
    if (token.startsWith('admin_token_')) {
      res.status(200).json({
        success: true,
        data: {
          id: 1,
          username: 'admin',
          name: '管理员',
          role: 'admin',
          permissions: ['*']
        }
      });
      return;
    }
    
    if (token.startsWith('wechat_user_token_')) {
      res.status(200).json({
        success: true,
        data: {
          id: 2,
          username: 'wechat_user_001',
          name: '微信用户',
          mobile: '13800138000',
          email: '<EMAIL>',
          role: 'user',
          permissions: ['read', 'write'],
          loginType: 'wechat'
        }
      });
      return;
    }
    
    res.status(401).json({
      success: false,
      message: '无效的token'
    });
    
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

export default router;