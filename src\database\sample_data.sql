-- ========================================
-- YYSH 客户关系管理系统 - 示例数据
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 描述: 用于测试和演示的示例数据
-- ========================================

USE `yysh_crm`;

-- ========================================
-- 1. 用户示例数据
-- ========================================

-- 插入示例用户
INSERT INTO `users` (`id`, `work_wechat_id`, `name`, `avatar`, `mobile`, `email`, `department_id`, `position`, `role`, `permissions`) VALUES
(1, 'admin001', '系统管理员', '/static/avatars/admin.png', '13800000001', '<EMAIL>', 3, '系统管理员', 'admin', '["all"]'),
(2, 'manager001', '王经理', '/static/avatars/manager1.png', '13800000002', '<EMAIL>', 1, '销售经理', 'manager', '["customer.view_all", "follow.view_all", "analytics.view_team"]'),
(3, 'sales001', '张销售', '/static/avatars/sales1.png', '13800000003', '<EMAIL>', 1, '高级销售', 'sales', '["customer.manage", "follow.manage", "pool.claim"]'),
(4, 'sales002', '李销售', '/static/avatars/sales2.png', '13800000004', '<EMAIL>', 1, '销售专员', 'sales', '["customer.manage", "follow.manage", "pool.claim"]'),
(5, 'sales003', '刘销售', '/static/avatars/sales3.png', '13800000005', '<EMAIL>', 1, '销售专员', 'sales', '["customer.manage", "follow.manage", "pool.claim"]'),
(6, 'designer001', '陈设计师', '/static/avatars/designer1.png', '13800000006', '<EMAIL>', 2, '首席设计师', 'designer', '["design.manage", "meeting.participate"]'),
(7, 'designer002', '赵设计师', '/static/avatars/designer2.png', '13800000007', '<EMAIL>', 2, '高级设计师', 'designer', '["design.manage", "meeting.participate"]');

-- ========================================
-- 2. 客户示例数据
-- ========================================

-- 插入示例客户
INSERT INTO `customers` (`id`, `name`, `phone`, `email`, `company`, `position`, `wechat`, `address`, `source`, `status`, `level`, `birthday`, `gender`, `is_vip`, `is_high_value`, `tags`, `remark`, `owner_id`, `last_follow_time`, `next_follow_time`, `follow_count`, `deal_amount`) VALUES
(1, '张三', '13912345678', '<EMAIL>', '阿里巴巴集团', 'CTO', 'zhangsan_wx', '杭州市西湖区文三路969号', 'referral', 'interested', 'A', '1985-06-15', 'male', 1, 1, '["重要客户", "技术专家", "决策者"]', '技术背景深厚，对产品有深入了解，是重要的合作伙伴。预算充足，决策权限大。', 3, '2024-12-18 14:30:00', '2024-12-20 10:00:00', 5, 0.00),

(2, '李四', '13823456789', '<EMAIL>', '腾讯科技', '产品总监', 'lisi_wx', '深圳市南山区科技园', 'online', 'potential', 'B', '1988-03-22', 'male', 0, 0, '["价格敏感", "服务导向"]', '对价格比较敏感，需要详细的ROI分析。重视售后服务质量。', 3, '2024-12-17 16:45:00', '2024-12-21 14:00:00', 3, 0.00),

(3, '王五', '13734567890', '<EMAIL>', '百度在线', '技术经理', 'wangwu_wx', '北京市海淀区上地十街10号', 'event', 'deal', 'A', '1990-11-08', 'male', 1, 1, '["重要客户", "技术专家"]', '已成交客户，技术实力强，后续有扩展需求的可能。', 4, '2024-12-15 11:20:00', NULL, 8, 150000.00),

(4, '赵六', '13645678901', '<EMAIL>', '字节跳动', 'HR总监', 'zhaoliu_wx', '北京市朝阳区北三环东路36号', 'telemarketing', 'interested', 'B', '1987-09-12', 'female', 0, 0, '["决策者", "服务导向"]', 'HR部门负责人，关注员工体验和系统易用性。有一定决策权。', 4, '2024-12-16 09:15:00', '2024-12-22 15:30:00', 4, 0.00),

(5, '孙七', '13556789012', '<EMAIL>', '美团点评', '运营总监', 'sunqi_wx', '北京市朝阳区望京东路6号', 'store', 'potential', 'C', '1992-01-25', 'female', 0, 0, '["创新型", "价格敏感"]', '年轻的运营总监，思维活跃，喜欢尝试新技术。预算有限。', 5, '2024-12-14 13:40:00', '2024-12-23 10:30:00', 2, 0.00),

(6, '周八', '13467890123', '<EMAIL>', '滴滴出行', 'CFO', 'zhouba_wx', '北京市海淀区中关村软件园', 'referral', 'lost', 'A', '1983-07-18', 'male', 0, 1, '["高价值", "决策者"]', '财务负责人，预算充足但决策谨慎。因为内部政策变化暂停合作。', 5, '2024-12-10 10:25:00', NULL, 6, 0.00),

(7, '吴九', '13378901234', '<EMAIL>', '小米科技', '市场总监', 'wujiu_wx', '北京市海淀区清河中街68号', 'online', 'interested', 'B', '1989-04-30', 'female', 0, 0, '["创新型", "技术专家"]', '市场部门负责人，对新技术接受度高。正在评估多个方案。', 3, '2024-12-19 08:50:00', '2024-12-24 14:00:00', 1, 0.00),

(8, '郑十', '13289012345', '<EMAIL>', '华为技术', '解决方案专家', 'zhengshi_wx', '深圳市龙岗区坂田华为基地', 'event', 'potential', 'A', '1986-12-03', 'male', 1, 1, '["重要客户", "技术专家", "大客户"]', '华为的解决方案专家，技术实力强，项目规模大。', 4, '2024-12-18 15:20:00', '2024-12-25 09:00:00', 3, 0.00),

-- 公海客户
(9, '陈公海', '13190123456', '<EMAIL>', '网易科技', '产品经理', 'chengonghai_wx', '杭州市滨江区网商路599号', 'other', 'potential', 'C', '1991-08-16', 'male', 0, 0, '["传统型"]', '长期未跟进的客户，已进入公海池。', 0, '2024-11-15 16:30:00', NULL, 1, 0.00),

(10, '林公海', '13101234567', '<EMAIL>', '京东集团', '采购总监', 'lingonghai_wx', '北京市大兴区京东总部', 'referral', 'potential', 'B', '1984-05-20', 'female', 0, 0, '["价格敏感", "决策者"]', '采购部门负责人，决策周期长，已超期未跟进。', 0, '2024-11-20 14:15:00', NULL, 2, 0.00);

-- 更新公海客户状态
UPDATE `customers` SET `is_in_pool` = 1, `pool_time` = '2024-12-01 10:00:00' WHERE `id` IN (9, 10);

-- ========================================
-- 3. 跟进记录示例数据
-- ========================================

-- 插入示例跟进记录
INSERT INTO `follow_records` (`id`, `customer_id`, `user_id`, `type`, `stage`, `content`, `result`, `result_detail`, `has_next_plan`, `next_time`, `next_content`, `duration`, `location`) VALUES
(1, 1, 3, 'phone', 'first_contact', '初次电话联系，了解客户基本需求。客户对我们的产品表现出浓厚兴趣，特别是在技术架构方面。', 'effective', '客户同意下周安排技术交流会议，将邀请技术团队参与。', 1, '2024-12-20 10:00:00', '安排技术交流会议，准备产品技术方案演示', 30, '电话沟通'),

(2, 1, 3, 'meeting', 'demo_present', '现场产品演示，客户技术团队参与。详细展示了系统架构和核心功能。', 'effective', '技术团队对产品架构给予高度评价，提出了一些定制化需求。', 1, '2024-12-22 14:00:00', '准备定制化方案和报价', 120, '客户公司会议室'),

(3, 2, 3, 'wechat', 'need_analysis', '通过微信了解客户具体需求，发送了产品介绍资料。', 'effective', '客户对产品功能基本满意，主要关心价格和实施周期。', 1, '2024-12-21 14:00:00', '准备详细报价方案', 0, '微信沟通'),

(4, 3, 4, 'visit', 'contract_sign', '客户到访公司，签署正式合作协议。', 'effective', '成功签约，项目金额15万元，实施周期3个月。', 0, NULL, NULL, 180, '公司会议室'),

(5, 4, 4, 'email', 'interest_confirm', '发送邮件确认客户需求，附上初步方案。', 'effective', '客户确认需求基本符合，要求提供更详细的功能清单。', 1, '2024-12-22 15:30:00', '发送详细功能清单和演示视频', 0, '邮件沟通'),

(6, 5, 5, 'phone', 'first_contact', '电话回访，了解客户对之前方案的反馈。', 'pending', '客户表示需要内部讨论，一周后给予回复。', 1, '2024-12-23 10:30:00', '电话跟进讨论结果', 25, '电话沟通'),

(7, 7, 3, 'meeting', 'solution_discuss', '面对面讨论解决方案，客户提出了具体的业务场景需求。', 'effective', '客户对方案整体满意，需要针对特定场景进行优化。', 1, '2024-12-24 14:00:00', '提供优化后的解决方案', 90, '客户公司'),

(8, 8, 4, 'visit', 'demo_present', '客户到公司参观，现场演示产品功能。', 'effective', '客户对产品印象深刻，表示有合作意向，需要内部评估。', 1, '2024-12-25 09:00:00', '发送正式商务提案', 150, '公司展厅');

-- ========================================
-- 4. 见面记录示例数据
-- ========================================

-- 插入示例见面记录
INSERT INTO `meeting_records` (`id`, `follow_record_id`, `customer_id`, `user_id`, `meeting_type`, `meeting_time`, `designer_id`, `designer_name`, `visit_count`, `address`, `notes`) VALUES
(1, 2, 1, 3, 'visit', '2024-12-18 14:00:00', 6, '陈设计师', 1, '公司会议室A', '客户技术团队3人参与，对产品架构设计给予高度评价'),
(2, 4, 3, 4, 'visit', '2024-12-15 10:00:00', 6, '陈设计师', 2, '公司会议室B', '签约会议，客户决策层参与，成功达成合作'),
(3, 7, 7, 3, 'external', '2024-12-19 15:00:00', 7, '赵设计师', 1, '小米科技总部', '在客户公司进行方案讨论，现场环境良好'),
(4, 8, 8, 4, 'visit', '2024-12-18 14:30:00', 6, '陈设计师', 1, '公司展厅', '产品演示效果良好，客户对界面设计很满意');

-- ========================================
-- 5. 公海池记录示例数据
-- ========================================

-- 插入公海池操作记录
INSERT INTO `customer_pool_records` (`id`, `customer_id`, `action`, `from_user_id`, `to_user_id`, `reason`, `auto_release`) VALUES
(1, 9, 'release', 3, NULL, '超过30天未跟进，自动释放到公海', 1),
(2, 10, 'release', 4, NULL, '客户长期无响应，手动释放到公海', 0);

-- ========================================
-- 6. 营销链接示例数据
-- ========================================

-- 插入示例营销链接
INSERT INTO `marketing_links` (`id`, `title`, `description`, `url`, `short_url`, `qr_code`, `cover_image`, `category`, `tags`, `click_count`, `share_count`, `created_by`) VALUES
(1, '产品介绍页面', 'YYSH客户管理系统产品详细介绍', 'https://www.yysh.com/products/crm', 'https://short.yysh.com/p1', '/static/qr/product_intro.png', '/static/covers/product_cover.jpg', 'product', '["产品介绍", "CRM系统"]', 156, 23, 3),
(2, '免费试用申请', '申请YYSH CRM系统免费试用', 'https://www.yysh.com/trial', 'https://short.yysh.com/t1', '/static/qr/trial.png', '/static/covers/trial_cover.jpg', 'trial', '["免费试用", "申请"]', 89, 15, 3),
(3, '客户案例分享', '成功客户案例分享和经验总结', 'https://www.yysh.com/cases', 'https://short.yysh.com/c1', '/static/qr/cases.png', '/static/covers/case_cover.jpg', 'case', '["客户案例", "成功故事"]', 234, 45, 4),
(4, '技术白皮书', 'CRM系统技术架构白皮书下载', 'https://www.yysh.com/whitepaper', 'https://short.yysh.com/w1', '/static/qr/whitepaper.png', '/static/covers/tech_cover.jpg', 'document', '["技术文档", "白皮书"]', 67, 8, 4);

-- ========================================
-- 7. 链接访问记录示例数据
-- ========================================

-- 插入示例访问记录
INSERT INTO `link_access_logs` (`id`, `link_id`, `user_id`, `customer_id`, `ip_address`, `user_agent`, `referer`, `access_time`) VALUES
(1, 1, 3, 1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://www.google.com', '2024-12-18 10:30:00'),
(2, 1, 3, 2, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 'https://www.baidu.com', '2024-12-18 14:20:00'),
(3, 2, 4, 3, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'https://www.yysh.com', '2024-12-17 16:45:00'),
(4, 3, 3, 4, '*************', 'Mozilla/5.0 (Android 11; Mobile)', 'https://weixin.qq.com', '2024-12-19 09:15:00');

-- ========================================
-- 8. 数据统计示例数据
-- ========================================

-- 插入示例统计数据
INSERT INTO `analytics_stats` (`id`, `user_id`, `date`, `customer_count`, `new_customer_count`, `follow_count`, `deal_count`, `deal_amount`, `pool_claim_count`) VALUES
(1, 3, '2024-12-18', 3, 1, 3, 0, 0.00, 0),
(2, 4, '2024-12-18', 3, 0, 2, 1, 150000.00, 0),
(3, 5, '2024-12-18', 2, 0, 1, 0, 0.00, 0),
(4, 3, '2024-12-17', 2, 0, 1, 0, 0.00, 0),
(5, 4, '2024-12-17', 3, 1, 1, 0, 0.00, 0),
(6, 5, '2024-12-17', 2, 1, 0, 0, 0.00, 0),
(7, 3, '2024-12-16', 2, 0, 0, 0, 0.00, 0),
(8, 4, '2024-12-16', 2, 0, 1, 0, 0.00, 0),
(9, 5, '2024-12-16', 1, 0, 0, 0, 0.00, 0);

-- ========================================
-- 9. 操作日志示例数据
-- ========================================

-- 插入示例操作日志
INSERT INTO `operation_logs` (`id`, `user_id`, `module`, `action`, `target_type`, `target_id`, `content`, `ip_address`, `user_agent`) VALUES
(1, 3, 'customer', 'create', 'customer', 1, '创建客户：张三', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(2, 3, 'follow', 'create', 'follow_record', 1, '添加跟进记录：初次电话联系', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(3, 4, 'customer', 'update', 'customer', 3, '更新客户状态：成交', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)'),
(4, 5, 'pool', 'release', 'customer', 9, '释放客户到公海：陈公海', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'),
(5, 3, 'marketing', 'create', 'marketing_link', 1, '创建营销链接：产品介绍页面', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)');

-- ========================================
-- 示例数据插入完成
-- ========================================

-- 显示插入结果统计
SELECT 'YYSH客户管理系统示例数据插入完成！' as message;

SELECT 
    '用户数据' as data_type,
    COUNT(*) as record_count
FROM users
WHERE id > 0

UNION ALL

SELECT 
    '客户数据' as data_type,
    COUNT(*) as record_count
FROM customers

UNION ALL

SELECT 
    '跟进记录' as data_type,
    COUNT(*) as record_count
FROM follow_records

UNION ALL

SELECT 
    '见面记录' as data_type,
    COUNT(*) as record_count
FROM meeting_records

UNION ALL

SELECT 
    '公海记录' as data_type,
    COUNT(*) as record_count
FROM customer_pool_records

UNION ALL

SELECT 
    '营销链接' as data_type,
    COUNT(*) as record_count
FROM marketing_links

UNION ALL

SELECT 
    '访问记录' as data_type,
    COUNT(*) as record_count
FROM link_access_logs

UNION ALL

SELECT 
    '统计数据' as data_type,
    COUNT(*) as record_count
FROM analytics_stats

UNION ALL

SELECT 
    '操作日志' as data_type,
    COUNT(*) as record_count
FROM operation_logs;