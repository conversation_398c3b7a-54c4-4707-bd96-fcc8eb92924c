const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件（临时禁用）
// router.use(authenticateToken);

// 获取用户列表
router.get('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, pageSize = 10, keyword, role, status, departmentId } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (u.name LIKE ? OR u.mobile LIKE ? OR u.email LIKE ? OR u.work_wechat_id LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // 角色筛选
    if (role) {
      whereClause += ' AND u.role = ?';
      whereParams.push(role);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND u.status = ?';
      whereParams.push(status);
    }

    // 部门筛选
    if (departmentId) {
      whereClause += ' AND u.department_id = ?';
      whereParams.push(departmentId);
    }

    const pageNum = parseInt(page) || 1;
    const pageSizeNum = parseInt(pageSize) || 10;
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 构建查询SQL - 直接拼接LIMIT和OFFSET避免参数问题
    const sql = `
      SELECT 
        u.id, u.name, u.role, u.mobile as phone, u.email, u.avatar, u.status, 
        u.department_id, u.position, u.work_wechat_id, u.last_login_time as last_login_at, u.created_at,
        d.name as department_name
      FROM users u
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    const countSql = `
      SELECT COUNT(*) as total
      FROM users u
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE ${whereClause}
    `;

    // 使用pool.query执行查询
    const [users] = await db.pool.query(sql, whereParams);
    const [countRows] = await db.pool.query(countSql, whereParams);
    const total = countRows[0].total;

    res.json({
      success: true,
      data: users.rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      data: []
    });
  }
});

// 获取用户详情
router.get('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const sql = `
      SELECT 
        u.*,
        d.name as department_name
      FROM users u
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE u.id = ?
    `;

    const result = await db.query(sql, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = result.rows[0];

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('获取用户详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败'
    });
  }
});

// 创建用户
router.post('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { name, phone, email, role, department_id, position, work_wechat_id, avatar } = req.body;

    // 验证必填字段
    if (!name || !phone || !role) {
      return res.status(400).json({
        success: false,
        message: '姓名、手机号和角色为必填项'
      });
    }

    // 检查手机号是否已存在
    const phoneCheck = await db.query('SELECT id FROM users WHERE mobile = ?', [phone]);
    if (phoneCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '手机号已存在'
      });
    }

    // 检查企微ID是否已存在
    if (work_wechat_id) {
      const wechatCheck = await db.query('SELECT id FROM users WHERE work_wechat_id = ?', [work_wechat_id]);
      if (wechatCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          message: '企微ID已存在'
        });
      }
    }

    const userData = {
      name,
      mobile: phone,
      email: email || null,
      role,
      department_id: department_id || null,
      position: position || null,
      work_wechat_id: work_wechat_id || null,
      avatar: avatar || null,
      status: 1, // 新数据库中status是tinyint(1)，1表示正常
      username: phone || `user_${Date.now()}`, // 新数据库需要username字段
      password: 'default123', // 新数据库需要password字段，设置默认密码
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const insertSql = `
      INSERT INTO users (
        name, mobile, email, role, department_id, position, 
        work_wechat_id, avatar, status, username, password, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      userData.name,
      userData.mobile,
      userData.email,
      userData.role,
      userData.department_id,
      userData.position,
      userData.work_wechat_id,
      userData.avatar,
      userData.status,
      userData.username,
      userData.password,
      userData.created_at,
      userData.updated_at
    ];

    const result = await db.query(insertSql, insertParams);

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: { 
        id: result.lastID,
        ...userData
      }
    });

  } catch (error) {
    console.error('创建用户错误:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败'
    });
  }
});

// 更新用户
router.put('/:id', requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { name, phone, email, role, department_id, status } = req.body;

    // 检查用户是否存在
    const user = await db.findOne('users', 'id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 权限检查：manager只能修改自己部门的用户
    if (req.user.role === 'manager' && user.department_id !== req.user.departmentId) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 检查手机号是否被其他用户使用
    if (phone && phone !== user.mobile) {
      const existingUser = await db.findOne('users', 'mobile = ? AND id != ?', [phone, id]);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '手机号已被其他用户使用'
        });
      }
    }

    // 更新用户信息
    const updateData = {
      updated_at: new Date()
    };

    if (name) updateData.name = name;
    if (phone) updateData.mobile = phone;
    if (email !== undefined) updateData.email = email;
    if (role) updateData.role = role;
    if (department_id !== undefined) updateData.department_id = department_id;
    if (status) updateData.status = status === 'active' ? 1 : 0; // 转换状态值

    await db.update('users', updateData, 'id = ?', [id]);

    res.json({
      success: true,
      message: '用户更新成功'
    });

  } catch (error) {
    console.error('更新用户错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户失败'
    });
  }
});

// 删除用户（软删除）
router.delete('/:id', requireRole(['admin']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查用户是否存在
    const user = await db.findOne('users', 'id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 不能删除自己
    if (parseInt(id) === req.user.userId) {
      return res.status(400).json({
        success: false,
        message: '不能删除自己'
      });
    }

    // 软删除
    await db.update(
      'users',
      { 
        status: 0, // 新数据库中0表示禁用
        updated_at: new Date()
      },
      'id = ?',
      [id]
    );

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败'
    });
  }
});

// 获取用户统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const db = req.app.locals.db;

    const stats = await Promise.all([
      // 总用户数
      db.query('SELECT COUNT(*) as total FROM users WHERE status = 1'),
      // 活跃用户数
      db.query('SELECT COUNT(*) as active FROM users WHERE status = 1'),
      // 各角色用户数
      db.query(`
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE status = 1 
        GROUP BY role
      `),
      // 最近7天新增用户
      db.query(`
        SELECT COUNT(*) as new_users 
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND status = 1
      `)
    ]);

    const roleStats = {};
    stats[2].rows.forEach(item => {
      roleStats[item.role] = item.count;
    });

    res.json({
      success: true,
      data: {
        total: stats[0].rows[0].total,
        active: stats[1].rows[0].active,
        newUsers: stats[3].rows[0].new_users,
        roleStats
      }
    });

  } catch (error) {
    console.error('获取用户统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败'
    });
  }
});

module.exports = router;