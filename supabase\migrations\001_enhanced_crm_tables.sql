-- ========================================
-- YYSH 客户关系管理系统 - 增强功能表
-- 版本: 2.0
-- 创建时间: 2024-12-19
-- 描述: 添加角色权限、营销活动、会议管理等功能表
-- ========================================

-- ========================================
-- 1. 角色权限管理表
-- ========================================

-- 角色表
CREATE TABLE IF NOT EXISTS "roles" (
  "id" BIGSERIAL PRIMARY KEY,
  "name" VARCHAR(50) NOT NULL UNIQUE,
  "display_name" VARCHAR(100) NOT NULL,
  "description" TEXT,
  "permissions" JSONB DEFAULT '[]'::jsonb,
  "is_system" BOOLEAN NOT NULL DEFAULT false,
  "status" BOOLEAN NOT NULL DEFAULT true,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 权限表
CREATE TABLE IF NOT EXISTS "permissions" (
  "id" BIGSERIAL PRIMARY KEY,
  "name" VARCHAR(100) NOT NULL UNIQUE,
  "display_name" VARCHAR(200) NOT NULL,
  "module" VARCHAR(50) NOT NULL,
  "action" VARCHAR(50) NOT NULL,
  "resource" VARCHAR(50),
  "description" TEXT,
  "is_system" BOOLEAN NOT NULL DEFAULT false,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS "role_permissions" (
  "id" BIGSERIAL PRIMARY KEY,
  "role_id" BIGINT NOT NULL REFERENCES "roles"("id") ON DELETE CASCADE,
  "permission_id" BIGINT NOT NULL REFERENCES "permissions"("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE("role_id", "permission_id")
);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS "user_roles" (
  "id" BIGSERIAL PRIMARY KEY,
  "user_id" UUID NOT NULL,
  "role_id" BIGINT NOT NULL REFERENCES "roles"("id") ON DELETE CASCADE,
  "assigned_by" UUID,
  "assigned_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "expires_at" TIMESTAMP WITH TIME ZONE,
  UNIQUE("user_id", "role_id")
);

-- ========================================
-- 2. 营销活动管理表
-- ========================================

-- 营销活动表
CREATE TABLE IF NOT EXISTS "marketing_campaigns" (
  "id" BIGSERIAL PRIMARY KEY,
  "name" VARCHAR(200) NOT NULL,
  "type" VARCHAR(50) NOT NULL, -- lottery, seckill, share, coupon, event
  "description" TEXT,
  "cover_image" VARCHAR(500),
  "start_time" TIMESTAMP WITH TIME ZONE NOT NULL,
  "end_time" TIMESTAMP WITH TIME ZONE NOT NULL,
  "status" VARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, active, paused, ended
  "target_audience" JSONB DEFAULT '{}'::jsonb,
  "rules" JSONB DEFAULT '{}'::jsonb,
  "prizes" JSONB DEFAULT '[]'::jsonb,
  "budget" DECIMAL(10,2) DEFAULT 0,
  "spent" DECIMAL(10,2) DEFAULT 0,
  "participant_count" INTEGER DEFAULT 0,
  "conversion_count" INTEGER DEFAULT 0,
  "share_count" INTEGER DEFAULT 0,
  "created_by" UUID NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 活动参与记录表
CREATE TABLE IF NOT EXISTS "campaign_participants" (
  "id" BIGSERIAL PRIMARY KEY,
  "campaign_id" BIGINT NOT NULL REFERENCES "marketing_campaigns"("id") ON DELETE CASCADE,
  "customer_id" BIGINT,
  "user_id" UUID,
  "phone" VARCHAR(20),
  "wechat_openid" VARCHAR(100),
  "participation_data" JSONB DEFAULT '{}'::jsonb,
  "result" JSONB DEFAULT '{}'::jsonb,
  "is_winner" BOOLEAN DEFAULT false,
  "prize_claimed" BOOLEAN DEFAULT false,
  "participated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "ip_address" INET,
  "user_agent" TEXT
);

-- 活动分享记录表
CREATE TABLE IF NOT EXISTS "campaign_shares" (
  "id" BIGSERIAL PRIMARY KEY,
  "campaign_id" BIGINT NOT NULL REFERENCES "marketing_campaigns"("id") ON DELETE CASCADE,
  "sharer_id" BIGINT,
  "platform" VARCHAR(20) NOT NULL, -- wechat, weibo, qq, link
  "share_url" VARCHAR(1000),
  "click_count" INTEGER DEFAULT 0,
  "conversion_count" INTEGER DEFAULT 0,
  "shared_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 3. 会议管理表
-- ========================================

-- 会议表
CREATE TABLE IF NOT EXISTS "meetings" (
  "id" BIGSERIAL PRIMARY KEY,
  "title" VARCHAR(200) NOT NULL,
  "type" VARCHAR(50) NOT NULL, -- internal, customer, training, review
  "description" TEXT,
  "location" VARCHAR(200),
  "meeting_url" VARCHAR(500),
  "start_time" TIMESTAMP WITH TIME ZONE NOT NULL,
  "end_time" TIMESTAMP WITH TIME ZONE NOT NULL,
  "status" VARCHAR(20) NOT NULL DEFAULT 'scheduled', -- scheduled, ongoing, completed, cancelled
  "organizer_id" UUID NOT NULL,
  "agenda" JSONB DEFAULT '[]'::jsonb,
  "materials" JSONB DEFAULT '[]'::jsonb,
  "recording_url" VARCHAR(500),
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 会议参与者表
CREATE TABLE IF NOT EXISTS "meeting_participants" (
  "id" BIGSERIAL PRIMARY KEY,
  "meeting_id" BIGINT NOT NULL REFERENCES "meetings"("id") ON DELETE CASCADE,
  "user_id" UUID,
  "customer_id" BIGINT,
  "email" VARCHAR(100),
  "name" VARCHAR(100),
  "role" VARCHAR(50) DEFAULT 'participant', -- organizer, presenter, participant
  "status" VARCHAR(20) DEFAULT 'invited', -- invited, accepted, declined, attended
  "joined_at" TIMESTAMP WITH TIME ZONE,
  "left_at" TIMESTAMP WITH TIME ZONE,
  "notes" TEXT
);

-- ========================================
-- 4. 公海池规则配置表
-- ========================================

-- 公海池规则表
CREATE TABLE IF NOT EXISTS "pool_rules" (
  "id" BIGSERIAL PRIMARY KEY,
  "name" VARCHAR(100) NOT NULL,
  "description" TEXT,
  "conditions" JSONB NOT NULL, -- 进入公海的条件
  "auto_release_days" INTEGER NOT NULL DEFAULT 30,
  "reminder_days" INTEGER NOT NULL DEFAULT 7,
  "max_claims_per_day" INTEGER DEFAULT 10,
  "priority" INTEGER DEFAULT 0,
  "is_active" BOOLEAN DEFAULT true,
  "applies_to_roles" JSONB DEFAULT '[]'::jsonb,
  "applies_to_departments" JSONB DEFAULT '[]'::jsonb,
  "created_by" UUID NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 5. 客户行为跟踪表
-- ========================================

-- 客户行为记录表
CREATE TABLE IF NOT EXISTS "customer_behaviors" (
  "id" BIGSERIAL PRIMARY KEY,
  "customer_id" BIGINT,
  "session_id" VARCHAR(100),
  "behavior_type" VARCHAR(50) NOT NULL, -- page_view, click, download, form_submit, purchase
  "page_url" VARCHAR(1000),
  "page_title" VARCHAR(200),
  "element" VARCHAR(200),
  "value" TEXT,
  "duration" INTEGER, -- 停留时间(秒)
  "referrer" VARCHAR(1000),
  "utm_source" VARCHAR(100),
  "utm_medium" VARCHAR(100),
  "utm_campaign" VARCHAR(100),
  "device_type" VARCHAR(20),
  "browser" VARCHAR(50),
  "os" VARCHAR(50),
  "ip_address" INET,
  "location" JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 微信客户跟踪表
CREATE TABLE IF NOT EXISTS "wechat_customer_tracking" (
  "id" BIGSERIAL PRIMARY KEY,
  "customer_id" BIGINT,
  "openid" VARCHAR(100),
  "unionid" VARCHAR(100),
  "nickname" VARCHAR(100),
  "avatar" VARCHAR(500),
  "subscribe_status" BOOLEAN DEFAULT false,
  "subscribe_time" TIMESTAMP WITH TIME ZONE,
  "unsubscribe_time" TIMESTAMP WITH TIME ZONE,
  "last_interaction" TIMESTAMP WITH TIME ZONE,
  "interaction_count" INTEGER DEFAULT 0,
  "tags" JSONB DEFAULT '[]'::jsonb,
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 6. 数据分析增强表
-- ========================================

-- 销售漏斗分析表
CREATE TABLE IF NOT EXISTS "sales_funnel_stats" (
  "id" BIGSERIAL PRIMARY KEY,
  "date" DATE NOT NULL,
  "user_id" UUID,
  "department_id" BIGINT,
  "stage" VARCHAR(50) NOT NULL,
  "customer_count" INTEGER DEFAULT 0,
  "conversion_rate" DECIMAL(5,2) DEFAULT 0,
  "avg_stage_duration" INTEGER DEFAULT 0, -- 平均停留天数
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 客户价值分析表
CREATE TABLE IF NOT EXISTS "customer_value_analysis" (
  "id" BIGSERIAL PRIMARY KEY,
  "customer_id" BIGINT NOT NULL,
  "analysis_date" DATE NOT NULL,
  "total_value" DECIMAL(12,2) DEFAULT 0,
  "potential_value" DECIMAL(12,2) DEFAULT 0,
  "interaction_score" INTEGER DEFAULT 0,
  "engagement_score" INTEGER DEFAULT 0,
  "loyalty_score" INTEGER DEFAULT 0,
  "risk_score" INTEGER DEFAULT 0,
  "value_tier" VARCHAR(20), -- high, medium, low
  "churn_probability" DECIMAL(5,2) DEFAULT 0,
  "next_purchase_prediction" DATE,
  "recommended_actions" JSONB DEFAULT '[]'::jsonb,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 7. 创建索引
-- ========================================

-- 角色权限索引
CREATE INDEX IF NOT EXISTS "idx_roles_name" ON "roles"("name");
CREATE INDEX IF NOT EXISTS "idx_permissions_module_action" ON "permissions"("module", "action");
CREATE INDEX IF NOT EXISTS "idx_user_roles_user_id" ON "user_roles"("user_id");

-- 营销活动索引
CREATE INDEX IF NOT EXISTS "idx_campaigns_type_status" ON "marketing_campaigns"("type", "status");
CREATE INDEX IF NOT EXISTS "idx_campaigns_time" ON "marketing_campaigns"("start_time", "end_time");
CREATE INDEX IF NOT EXISTS "idx_participants_campaign_customer" ON "campaign_participants"("campaign_id", "customer_id");

-- 会议管理索引
CREATE INDEX IF NOT EXISTS "idx_meetings_organizer_time" ON "meetings"("organizer_id", "start_time");
CREATE INDEX IF NOT EXISTS "idx_meeting_participants_meeting_user" ON "meeting_participants"("meeting_id", "user_id");

-- 客户行为索引
CREATE INDEX IF NOT EXISTS "idx_behaviors_customer_type" ON "customer_behaviors"("customer_id", "behavior_type");
CREATE INDEX IF NOT EXISTS "idx_behaviors_created_at" ON "customer_behaviors"("created_at");
CREATE INDEX IF NOT EXISTS "idx_wechat_tracking_openid" ON "wechat_customer_tracking"("openid");

-- 分析表索引
CREATE INDEX IF NOT EXISTS "idx_funnel_stats_date_user" ON "sales_funnel_stats"("date", "user_id");
CREATE INDEX IF NOT EXISTS "idx_value_analysis_customer_date" ON "customer_value_analysis"("customer_id", "analysis_date");

-- ========================================
-- 8. 插入基础数据
-- ========================================

-- 插入系统角色
INSERT INTO "roles" ("name", "display_name", "description", "is_system") VALUES
('super_admin', '超级管理员', '拥有所有权限的系统管理员', true),
('admin', '管理员', '拥有大部分管理权限', true),
('sales_manager', '销售主管', '销售团队管理者', true),
('sales', '销售人员', '普通销售人员', true),
('designer', '设计师', '设计师角色', true),
('customer_service', '客服人员', '客户服务人员', true)
ON CONFLICT ("name") DO NOTHING;

-- 插入系统权限
INSERT INTO "permissions" ("name", "display_name", "module", "action", "resource", "is_system") VALUES
-- 客户管理权限
('customer.view', '查看客户', 'customer', 'view', 'customer', true),
('customer.create', '创建客户', 'customer', 'create', 'customer', true),
('customer.edit', '编辑客户', 'customer', 'edit', 'customer', true),
('customer.delete', '删除客户', 'customer', 'delete', 'customer', true),
('customer.assign', '分配客户', 'customer', 'assign', 'customer', true),
('customer.export', '导出客户', 'customer', 'export', 'customer', true),
('customer.import', '导入客户', 'customer', 'import', 'customer', true),

-- 公海池权限
('pool.view', '查看公海池', 'pool', 'view', 'pool', true),
('pool.claim', '领取公海客户', 'pool', 'claim', 'pool', true),
('pool.release', '释放客户到公海', 'pool', 'release', 'pool', true),
('pool.manage', '管理公海规则', 'pool', 'manage', 'pool', true),

-- 跟进管理权限
('follow.view', '查看跟进记录', 'follow', 'view', 'follow', true),
('follow.create', '创建跟进记录', 'follow', 'create', 'follow', true),
('follow.edit', '编辑跟进记录', 'follow', 'edit', 'follow', true),
('follow.delete', '删除跟进记录', 'follow', 'delete', 'follow', true),

-- 营销活动权限
('campaign.view', '查看营销活动', 'campaign', 'view', 'campaign', true),
('campaign.create', '创建营销活动', 'campaign', 'create', 'campaign', true),
('campaign.edit', '编辑营销活动', 'campaign', 'edit', 'campaign', true),
('campaign.delete', '删除营销活动', 'campaign', 'delete', 'campaign', true),
('campaign.manage', '管理营销活动', 'campaign', 'manage', 'campaign', true),

-- 会议管理权限
('meeting.view', '查看会议', 'meeting', 'view', 'meeting', true),
('meeting.create', '创建会议', 'meeting', 'create', 'meeting', true),
('meeting.edit', '编辑会议', 'meeting', 'edit', 'meeting', true),
('meeting.delete', '删除会议', 'meeting', 'delete', 'meeting', true),

-- 数据分析权限
('analytics.view', '查看数据分析', 'analytics', 'view', 'analytics', true),
('analytics.export', '导出分析报告', 'analytics', 'export', 'analytics', true),

-- 用户管理权限
('user.view', '查看用户', 'user', 'view', 'user', true),
('user.create', '创建用户', 'user', 'create', 'user', true),
('user.edit', '编辑用户', 'user', 'edit', 'user', true),
('user.delete', '删除用户', 'user', 'delete', 'user', true),

-- 系统设置权限
('system.view', '查看系统设置', 'system', 'view', 'system', true),
('system.edit', '编辑系统设置', 'system', 'edit', 'system', true),
('system.backup', '系统备份', 'system', 'backup', 'system', true),
('system.log', '查看系统日志', 'system', 'log', 'system', true)
ON CONFLICT ("name") DO NOTHING;

-- 插入默认公海规则（使用系统UUID）
INSERT INTO "pool_rules" ("name", "description", "conditions", "auto_release_days", "reminder_days", "created_by") VALUES
('默认公海规则', '超过30天未跟进的客户自动进入公海', '{"no_follow_days": 30}', 30, 7, '00000000-0000-0000-0000-000000000001'::uuid),
('高价值客户规则', '高价值客户60天未跟进才进入公海', '{"no_follow_days": 60, "customer_level": ["A", "B"]}', 60, 14, '00000000-0000-0000-0000-000000000001'::uuid)
ON CONFLICT DO NOTHING;

-- ========================================
-- 9. 启用行级安全策略 (RLS)
-- ========================================

ALTER TABLE "roles" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "permissions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "role_permissions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "user_roles" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "marketing_campaigns" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "campaign_participants" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "campaign_shares" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "meetings" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "meeting_participants" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "pool_rules" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "customer_behaviors" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "wechat_customer_tracking" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "sales_funnel_stats" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "customer_value_analysis" ENABLE ROW LEVEL SECURITY;

-- 创建基本的RLS策略
CREATE POLICY "Enable read access for authenticated users" ON "roles" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read access for authenticated users" ON "permissions" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read access for authenticated users" ON "role_permissions" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read access for authenticated users" ON "user_roles" FOR SELECT USING (auth.role() = 'authenticated');

-- 营销活动策略
CREATE POLICY "Users can view campaigns" ON "marketing_campaigns" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Users can create campaigns" ON "marketing_campaigns" FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update own campaigns" ON "marketing_campaigns" FOR UPDATE USING (created_by = auth.uid());

-- 会议策略
CREATE POLICY "Users can view meetings" ON "meetings" FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Users can create meetings" ON "meetings" FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update own meetings" ON "meetings" FOR UPDATE USING (organizer_id = auth.uid());

-- 客户行为策略
CREATE POLICY "Enable insert for service role" ON "customer_behaviors" FOR INSERT WITH CHECK (auth.role() = 'service_role');
CREATE POLICY "Enable read for authenticated users" ON "customer_behaviors" FOR SELECT USING (auth.role() = 'authenticated');

-- ========================================
-- 完成
-- ========================================

SELECT 'YYSH客户管理系统增强功能表创建完成！' as message;