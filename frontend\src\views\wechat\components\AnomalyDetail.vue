<template>
  <div class="anomaly-detail">
    <!-- 异常基本信息 -->
    <div class="anomaly-header">
      <div class="anomaly-icon">
        <n-icon size="24" :color="getAnomalyColor(anomaly.severity)">
          <WarningOutline />
        </n-icon>
      </div>
      <div class="anomaly-info">
        <h3 class="anomaly-title">{{ anomaly.title }}</h3>
        <div class="anomaly-meta">
          <n-tag :type="getAnomalyTagType(anomaly.severity)" size="small">
            {{ anomaly.severity }}
          </n-tag>
          <span class="anomaly-time">{{ new Date(anomaly.detected_at).toLocaleString() }}</span>
        </div>
      </div>
    </div>

    <!-- 异常描述 -->
    <div class="anomaly-description">
      <h4>异常描述</h4>
      <p>{{ anomaly.description }}</p>
    </div>

    <!-- 异常数据 -->
    <div class="anomaly-data" v-if="anomalyData">
      <h4>异常数据分析</h4>
      
      <!-- 数据对比 -->
      <n-grid :cols="2" :x-gap="16" class="data-comparison">
        <n-grid-item>
          <n-card title="异常期间" size="small">
            <n-statistic label="异常值" :value="anomalyData.anomaly_value">
              <template #suffix>
                <span class="unit">{{ anomalyData.unit }}</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card title="正常期间" size="small">
            <n-statistic label="正常值" :value="anomalyData.normal_value">
              <template #suffix>
                <span class="unit">{{ anomalyData.unit }}</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 变化趋势图 -->
      <div class="trend-section">
        <h5>变化趋势</h5>
        <div class="trend-chart" ref="trendChartRef"></div>
      </div>

      <!-- 影响范围 -->
      <div class="impact-section">
        <h5>影响范围</h5>
        <n-list>
          <n-list-item v-for="impact in anomalyData.impacts" :key="impact.type">
            <div class="impact-item">
              <div class="impact-type">{{ impact.type }}</div>
              <div class="impact-value">{{ impact.value }}</div>
              <div class="impact-change">
                <n-tag :type="impact.change > 0 ? 'error' : 'success'" size="small">
                  {{ impact.change > 0 ? '+' : '' }}{{ impact.change }}%
                </n-tag>
              </div>
            </div>
          </n-list-item>
        </n-list>
      </div>
    </div>

    <!-- 可能原因 -->
    <div class="possible-causes">
      <h4>可能原因</h4>
      <div class="causes-list">
        <div class="cause-item" v-for="(cause, index) in possibleCauses" :key="index">
          <div class="cause-probability">
            <n-progress
              type="circle"
              :percentage="cause.probability"
              :stroke-width="6"
              :size="40"
            >
              <span style="font-size: 10px">{{ cause.probability }}%</span>
            </n-progress>
          </div>
          <div class="cause-content">
            <div class="cause-title">{{ cause.title }}</div>
            <div class="cause-desc">{{ cause.description }}</div>
          </div>
          <div class="cause-actions">
            <n-button size="small" @click="handleInvestigateCause(cause)">
              调查
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 建议措施 -->
    <div class="recommendations">
      <h4>建议措施</h4>
      <div class="recommendations-list">
        <div class="recommendation-item" v-for="(rec, index) in recommendations" :key="index">
          <div class="recommendation-priority">
            <n-tag :type="getPriorityType(rec.priority)" size="small">
              {{ rec.priority }}
            </n-tag>
          </div>
          <div class="recommendation-content">
            <div class="recommendation-title">{{ rec.title }}</div>
            <div class="recommendation-desc">{{ rec.description }}</div>
            <div class="recommendation-effort">
              <span>预期效果: {{ rec.expected_effect }}</span>
              <span>实施成本: {{ rec.implementation_cost }}</span>
            </div>
          </div>
          <div class="recommendation-actions">
            <n-button size="small" type="primary" @click="handleImplementRecommendation(rec)">
              实施
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录 -->
    <div class="history-section">
      <h4>相似异常历史</h4>
      <n-timeline>
        <n-timeline-item
          v-for="history in anomalyHistory"
          :key="history.id"
          :type="getHistoryType(history.status)"
        >
          <template #header>
            <div class="history-header">
              <span class="history-title">{{ history.title }}</span>
              <n-tag :type="getStatusTagType(history.status)" size="small">
                {{ history.status }}
              </n-tag>
            </div>
          </template>
          <div class="history-content">
            <div class="history-desc">{{ history.description }}</div>
            <div class="history-meta">
              <span>发生时间: {{ new Date(history.occurred_at).toLocaleString() }}</span>
              <span v-if="history.resolved_at">解决时间: {{ new Date(history.resolved_at).toLocaleString() }}</span>
              <span v-if="history.resolution">解决方案: {{ history.resolution }}</span>
            </div>
          </div>
        </n-timeline-item>
      </n-timeline>
      
      <div v-if="!anomalyHistory.length" class="no-history">
        <n-empty description="暂无相似异常历史记录" size="small" />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-space>
        <n-button @click="handleMarkAsResolved" type="success">
          <template #icon>
            <n-icon><CheckmarkCircleOutline /></n-icon>
          </template>
          标记为已解决
        </n-button>
        <n-button @click="handleCreateTicket">
          <template #icon>
            <n-icon><DocumentTextOutline /></n-icon>
          </template>
          创建工单
        </n-button>
        <n-button @click="handleExportReport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报告
        </n-button>
        <n-button @click="$emit('close')">
          关闭
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import {
  NCard,
  NGrid,
  NGridItem,
  NStatistic,
  NList,
  NListItem,
  NTag,
  NProgress,
  NButton,
  NSpace,
  NIcon,
  NTimeline,
  NTimelineItem,
  NEmpty,
  useMessage
} from 'naive-ui'
import {
  WarningOutline,
  CheckmarkCircleOutline,
  DocumentTextOutline,
  DownloadOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface Props {
  anomaly: {
    id: number
    title: string
    description: string
    severity: string
    detected_at: string
  }
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 引用
const trendChartRef = ref<HTMLElement>()

// 图表实例
let trendChart: echarts.ECharts | null = null

// 异常数据
const anomalyData = ref({
  anomaly_value: 65,
  normal_value: 35,
  unit: '%',
  trend_data: {
    times: ['12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30'],
    values: [35, 38, 42, 48, 65, 62, 58, 40],
    threshold: 45
  },
  impacts: [
    { type: '用户流失', value: '125人', change: 15 },
    { type: '转化率', value: '8.5%', change: -25 },
    { type: '收入影响', value: '¥2,350', change: 12 }
  ]
})

// 可能原因
const possibleCauses = ref([
  {
    title: '服务器性能问题',
    description: '服务器响应时间增加，导致页面加载缓慢',
    probability: 75
  },
  {
    title: '网络连接异常',
    description: 'CDN或网络服务商出现故障',
    probability: 60
  },
  {
    title: '页面内容更新',
    description: '最近的页面内容或布局更新影响用户体验',
    probability: 45
  },
  {
    title: '竞争对手活动',
    description: '竞争对手推出促销活动，分流用户',
    probability: 30
  }
])

// 建议措施
const recommendations = ref([
  {
    title: '立即检查服务器状态',
    description: '检查服务器CPU、内存使用率，查看是否有异常进程',
    priority: '高',
    expected_effect: '快速定位问题',
    implementation_cost: '低'
  },
  {
    title: '优化页面加载性能',
    description: '压缩图片、合并CSS/JS文件，启用浏览器缓存',
    priority: '高',
    expected_effect: '提升30%加载速度',
    implementation_cost: '中'
  },
  {
    title: '增加用户引导',
    description: '在关键页面添加引导提示，降低用户困惑',
    priority: '中',
    expected_effect: '降低15%跳出率',
    implementation_cost: '低'
  },
  {
    title: 'A/B测试页面设计',
    description: '测试不同的页面布局和内容，找到最优方案',
    priority: '低',
    expected_effect: '长期优化效果',
    implementation_cost: '高'
  }
])

// 异常历史
const anomalyHistory = ref([
  {
    id: 1,
    title: '跳出率异常升高',
    description: '类似的跳出率异常，持续时间2小时',
    status: '已解决',
    occurred_at: '2024-01-03T14:00:00Z',
    resolved_at: '2024-01-03T16:00:00Z',
    resolution: '重启服务器，优化数据库查询'
  },
  {
    id: 2,
    title: '页面响应时间异常',
    description: '页面加载时间超过正常值3倍',
    status: '已解决',
    occurred_at: '2024-01-01T10:30:00Z',
    resolved_at: '2024-01-01T11:15:00Z',
    resolution: '清理缓存，更新CDN配置'
  },
  {
    id: 3,
    title: '用户活跃度下降',
    description: '用户停留时间和页面浏览量显著下降',
    status: '处理中',
    occurred_at: '2023-12-28T16:00:00Z',
    resolved_at: null,
    resolution: null
  }
])

// 方法
const getAnomalyColor = (severity: string) => {
  const colorMap: Record<string, string> = {
    '高': '#d03050',
    '中': '#f0a020',
    '低': '#18a058'
  }
  return colorMap[severity] || '#666'
}

const getAnomalyTagType = (severity: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'error' | 'warning' | 'success'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[severity] || 'default'
}

const getPriorityType = (priority: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'error' | 'warning' | 'info'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'default'
}

const getHistoryType = (status: string): 'success' | 'error' | 'info' | 'warning' | 'default' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default'> = {
    '已解决': 'success',
    '处理中': 'warning',
    '未处理': 'error'
  }
  return typeMap[status] || 'default'
}

const getStatusTagType = (status: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    '已解决': 'success',
    '处理中': 'warning',
    '未处理': 'error'
  }
  return typeMap[status] || 'default'
}

const handleInvestigateCause = (cause: any) => {
  message.info(`开始调查: ${cause.title}`)
  // TODO: 实现原因调查功能
}

const handleImplementRecommendation = (rec: any) => {
  message.info(`开始实施: ${rec.title}`)
  // TODO: 实现建议实施功能
}

const handleMarkAsResolved = () => {
  message.success('异常已标记为已解决')
  // TODO: 实现标记解决功能
  emit('close')
}

const handleCreateTicket = () => {
  message.info('工单创建功能开发中')
  // TODO: 实现创建工单功能
}

const handleExportReport = () => {
  message.info('报告导出功能开发中')
  // TODO: 实现导出报告功能
}

// 图表渲染
const renderTrendChart = () => {
  if (!trendChartRef.value || !anomalyData.value) return
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }
  
  const data = anomalyData.value.trend_data
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.times
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '实际值',
        type: 'line',
        data: data.values,
        smooth: true,
        itemStyle: {
          color: '#2080f0'
        },
        areaStyle: {
          opacity: 0.3
        }
      },
      {
        name: '阈值',
        type: 'line',
        data: Array.from({ length: data.times.length }).fill(data.threshold),
        lineStyle: {
          color: '#d03050',
          type: 'dashed'
        },
        symbol: 'none'
      }
    ],
    legend: {
      data: ['实际值', '阈值']
    }
  }
  
  trendChart.setOption(option)
}

// 响应式处理
const handleResize = () => {
  trendChart?.resize()
}

// 初始化
onMounted(async () => {
  window.addEventListener('resize', handleResize)
  
  await nextTick()
  renderTrendChart()
})

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
}

// 组件卸载时清理
const { onBeforeUnmount } = require('vue')
onBeforeUnmount(cleanup)
</script>

<style scoped>
.anomaly-detail {
  padding: 0;
}

.anomaly-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.anomaly-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.anomaly-info {
  flex: 1;
}

.anomaly-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.anomaly-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.anomaly-time {
  font-size: 14px;
  color: #666;
}

.anomaly-description {
  margin-bottom: 24px;
}

.anomaly-description h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
}

.anomaly-description p {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.anomaly-data {
  margin-bottom: 24px;
}

.anomaly-data h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.data-comparison {
  margin-bottom: 24px;
}

.trend-section {
  margin-bottom: 24px;
}

.trend-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
}

.trend-chart {
  height: 250px;
}

.impact-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.impact-item:last-child {
  border-bottom: none;
}

.impact-type {
  font-weight: 500;
  min-width: 80px;
}

.impact-value {
  flex: 1;
  color: #666;
}

.impact-change {
  display: flex;
  align-items: center;
}

.possible-causes {
  margin-bottom: 24px;
}

.possible-causes h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.causes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cause-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.cause-probability {
  display: flex;
  align-items: center;
}

.cause-content {
  flex: 1;
}

.cause-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.cause-desc {
  color: #666;
  line-height: 1.4;
}

.cause-actions {
  display: flex;
  align-items: center;
}

.recommendations {
  margin-bottom: 24px;
}

.recommendations h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.recommendation-priority {
  display: flex;
  align-items: center;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.recommendation-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.recommendation-effort {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.recommendation-actions {
  display: flex;
  align-items: center;
}

.history-section {
  margin-bottom: 24px;
}

.history-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-title {
  font-weight: 500;
}

.history-content {
  margin-top: 8px;
}

.history-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.history-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.no-history {
  padding: 20px 0;
}

.action-buttons {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.unit {
  font-size: 12px;
  color: #666;
}
</style>