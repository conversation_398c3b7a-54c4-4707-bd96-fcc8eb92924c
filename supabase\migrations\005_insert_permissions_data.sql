-- 插入权限数据
INSERT INTO permissions (name, display_name, description, module, action, resource) VALUES
-- 客户管理权限
('customer:read', '查看客户', '查看客户列表和详情', 'customer', 'read', 'customer'),
('customer:create', '创建客户', '创建新客户', 'customer', 'create', 'customer'),
('customer:update', '编辑客户', '编辑客户信息', 'customer', 'update', 'customer'),
('customer:delete', '删除客户', '删除客户记录', 'customer', 'delete', 'customer'),
('customer:pool', '公海管理', '管理公海客户', 'customer', 'manage', 'pool'),
('customer:assign', '分配客户', '分配客户给销售', 'customer', 'assign', 'customer'),
('customer:export', '导出客户', '导出客户数据', 'customer', 'export', 'customer'),
('customer:import', '导入客户', '批量导入客户', 'customer', 'import', 'customer'),

-- 角色管理权限
('role:read', '查看角色', '查看角色列表和详情', 'role', 'read', 'role'),
('role:create', '创建角色', '创建新角色', 'role', 'create', 'role'),
('role:update', '编辑角色', '编辑角色信息和权限', 'role', 'update', 'role'),
('role:delete', '删除角色', '删除角色', 'role', 'delete', 'role'),
('role:assign', '分配角色', '给用户分配角色', 'role', 'assign', 'role'),

-- 用户管理权限
('user:read', '查看用户', '查看用户列表和详情', 'user', 'read', 'user'),
('user:create', '创建用户', '创建新用户', 'user', 'create', 'user'),
('user:update', '编辑用户', '编辑用户信息', 'user', 'update', 'user'),
('user:delete', '删除用户', '删除用户账号', 'user', 'delete', 'user'),
('user:reset_password', '重置密码', '重置用户密码', 'user', 'reset', 'password'),

-- 跟进管理权限
('follow:read', '查看跟进', '查看跟进记录', 'follow', 'read', 'follow'),
('follow:create', '创建跟进', '创建跟进记录', 'follow', 'create', 'follow'),
('follow:update', '编辑跟进', '编辑跟进记录', 'follow', 'update', 'follow'),
('follow:delete', '删除跟进', '删除跟进记录', 'follow', 'delete', 'follow'),

-- 会议管理权限
('meeting:read', '查看会议', '查看会议列表和详情', 'meeting', 'read', 'meeting'),
('meeting:create', '创建会议', '创建新会议', 'meeting', 'create', 'meeting'),
('meeting:update', '编辑会议', '编辑会议信息', 'meeting', 'update', 'meeting'),
('meeting:delete', '删除会议', '删除会议', 'meeting', 'delete', 'meeting'),
('meeting:join', '参加会议', '参加会议', 'meeting', 'join', 'meeting'),

-- 营销活动权限
('marketing:read', '查看活动', '查看营销活动', 'marketing', 'read', 'marketing'),
('marketing:create', '创建活动', '创建营销活动', 'marketing', 'create', 'marketing'),
('marketing:update', '编辑活动', '编辑营销活动', 'marketing', 'update', 'marketing'),
('marketing:delete', '删除活动', '删除营销活动', 'marketing', 'delete', 'marketing'),
('marketing:manage', '管理活动', '管理活动参与者和数据', 'marketing', 'manage', 'marketing'),

-- 数据分析权限
('analytics:read', '查看分析', '查看数据分析报表', 'analytics', 'read', 'analytics'),
('analytics:export', '导出报表', '导出分析报表', 'analytics', 'export', 'analytics'),

-- 系统设置权限
('settings:read', '查看设置', '查看系统设置', 'system', 'read', 'settings'),
('settings:update', '修改设置', '修改系统设置', 'system', 'update', 'settings'),
('settings:backup', '数据备份', '备份系统数据', 'system', 'backup', 'data'),
('settings:restore', '数据恢复', '恢复系统数据', 'system', 'restore', 'data');

-- 创建预定义角色
INSERT INTO roles (name, display_name, description, is_system, status) VALUES
('super_admin', '超级管理员', '拥有系统所有权限的管理员角色', true, true),
('sales_manager', '销售经理', '销售团队管理者，拥有客户和销售相关权限', false, true),
('sales_rep', '销售员', '普通销售人员，拥有基本的客户管理权限', false, true),
('customer_service', '客服', '客户服务人员，拥有客户查看和跟进权限', false, true);

-- 为超级管理员角色分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'super_admin';

-- 为销售经理角色分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'sales_manager'
AND p.name IN (
  'customer:read', 'customer:create', 'customer:update', 'customer:pool', 'customer:assign', 'customer:export',
  'follow:read', 'follow:create', 'follow:update',
  'meeting:read', 'meeting:create', 'meeting:update', 'meeting:join',
  'marketing:read', 'marketing:create', 'marketing:update', 'marketing:manage',
  'analytics:read', 'analytics:export',
  'user:read'
);

-- 为销售员角色分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'sales_rep'
AND p.name IN (
  'customer:read', 'customer:create', 'customer:update',
  'follow:read', 'follow:create', 'follow:update',
  'meeting:read', 'meeting:create', 'meeting:update', 'meeting:join',
  'marketing:read'
);

-- 为客服角色分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'customer_service'
AND p.name IN (
  'customer:read',
  'follow:read', 'follow:create', 'follow:update',
  'meeting:read', 'meeting:join'
);

-- 更新序列值
SELECT setval('permissions_id_seq', (SELECT MAX(id) FROM permissions));
SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles));
SELECT setval('role_permissions_id_seq', (SELECT MAX(id) FROM role_permissions));