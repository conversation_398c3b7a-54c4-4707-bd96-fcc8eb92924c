"""FastAPI应用入口

YYSH项目的主应用文件
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import uvicorn
import os
from contextlib import asynccontextmanager

from app.schemas.common import HealthResponse, ErrorResponse
from app import __version__
from app.api.auth import router as auth_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print(f"🚀 YYSH FastAPI应用启动 - 版本: {__version__}")
    print(f"📅 启动时间: {datetime.now()}")
    
    yield
    
    # 关闭时执行
    print("🛑 YYSH FastAPI应用关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="YYSH API",
    description="YYSH项目后端API服务",
    version=__version__,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该配置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            message="服务器内部错误",
            error=str(exc)
        ).dict()
    )


# 健康检查接口
@app.get(
    "/health",
    response_model=HealthResponse,
    tags=["系统"],
    summary="健康检查",
    description="检查API服务运行状态"
)
async def health_check():
    """健康检查接口"""
    return HealthResponse(
        success=True,
        message="服务运行正常",
        timestamp=datetime.now(),
        version=__version__
    )


# 注册路由
app.include_router(auth_router, prefix="/api", tags=["auth"])


# 根路径
@app.get(
    "/",
    tags=["系统"],
    summary="API根路径",
    description="API服务根路径信息"
)
async def root():
    """根路径接口"""
    return {
        "success": True,
        "message": "欢迎使用YYSH API服务",
        "version": __version__,
        "docs": "/docs",
        "health": "/health"
    }


# 开发环境直接运行
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )