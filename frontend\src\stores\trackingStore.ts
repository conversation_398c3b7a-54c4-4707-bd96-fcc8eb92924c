import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  trackingService, 
  reminderService, 
  behaviorService, 
  trackingStatsService,
  type TrackingRecord,
  type AutoReminder,
  type CustomerBehavior,
  type TrackingStats
} from '@/api/trackingService'

export const useTrackingStore = defineStore('tracking', () => {
  // 跟踪记录状态
  const trackingRecords = ref<TrackingRecord[]>([])
  const currentTrackingRecord = ref<TrackingRecord | null>(null)
  const trackingLoading = ref(false)
  const trackingTotal = ref(0)

  // 提醒状态
  const reminders = ref<AutoReminder[]>([])
  const todayReminders = ref<AutoReminder[]>([])
  const overdueReminders = ref<AutoReminder[]>([])
  const reminderLoading = ref(false)
  const reminderTotal = ref(0)

  // 客户行为状态
  const customerBehaviors = ref<CustomerBehavior[]>([])
  const behaviorLoading = ref(false)
  const behaviorTotal = ref(0)

  // 统计数据状态
  const trackingStats = ref<TrackingStats | null>(null)
  const trackingTrends = ref<Array<{
    date: string
    total_records: number
    completed_records: number
    completion_rate: number
  }>>([])
  const userRanking = ref<Array<{
    user_id: number
    user_name: string
    total_records: number
    completed_records: number
    completion_rate: number
  }>>([])
  const statsLoading = ref(false)

  // 计算属性
  const pendingTrackingRecords = computed(() => 
    trackingRecords.value.filter(record => record.status === 'pending')
  )

  const completedTrackingRecords = computed(() => 
    trackingRecords.value.filter(record => record.status === 'completed')
  )

  const overdueTrackingRecords = computed(() => {
    const now = new Date()
    return trackingRecords.value.filter(record => 
      record.status === 'pending' && 
      record.next_follow_time && 
      new Date(record.next_follow_time) < now
    )
  })

  const unreadReminders = computed(() => 
    reminders.value.filter(reminder => reminder.status === 'pending' || reminder.status === 'sent')
  )

  const trackingRecordCount = computed(() => trackingRecords.value.length)
  const reminderCount = computed(() => reminders.value.length)
  const unreadReminderCount = computed(() => unreadReminders.value.length)

  // 跟踪记录相关方法
  const fetchTrackingRecords = async (params?: {
    page?: number
    pageSize?: number
    customer_id?: number
    user_id?: number
    type?: string
    status?: string
    start_date?: string
    end_date?: string
    keyword?: string
  }) => {
    trackingLoading.value = true
    try {
      const response = await trackingService.getTrackingRecords(params || {})
      trackingRecords.value = response.data
      trackingTotal.value = response.total
      return response
    } catch (error: any) {
      console.error('Failed to fetch tracking records:', error?.message || error)
      throw error
    } finally {
      trackingLoading.value = false
    }
  }

  const fetchTrackingRecord = async (id: number) => {
    try {
      const response = await trackingService.getTrackingRecord(id)
      currentTrackingRecord.value = response.data
      return response
    } catch (error) {
      console.error('Failed to fetch tracking record:', error)
      throw error
    }
  }

  const createTrackingRecord = async (data: Omit<TrackingRecord, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await trackingService.createTrackingRecord(data)
      trackingRecords.value.unshift(response.data)
      trackingTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to create tracking record:', error)
      throw error
    }
  }

  const updateTrackingRecord = async (id: number, data: Partial<TrackingRecord>) => {
    try {
      const response = await trackingService.updateTrackingRecord(id, data)
      const index = trackingRecords.value.findIndex(record => record.id === id)
      if (index !== -1) {
        trackingRecords.value[index] = response.data
      }
      if (currentTrackingRecord.value?.id === id) {
        currentTrackingRecord.value = response.data
      }
      return response
    } catch (error) {
      console.error('Failed to update tracking record:', error)
      throw error
    }
  }

  const deleteTrackingRecord = async (id: number) => {
    try {
      await trackingService.deleteTrackingRecord(id)
      trackingRecords.value = trackingRecords.value.filter(record => record.id !== id)
      trackingTotal.value -= 1
      if (currentTrackingRecord.value?.id === id) {
        currentTrackingRecord.value = null
      }
    } catch (error) {
      console.error('Failed to delete tracking record:', error)
      throw error
    }
  }

  const batchDeleteTrackingRecords = async (ids: number[]) => {
    try {
      await trackingService.batchDeleteTrackingRecords(ids)
      trackingRecords.value = trackingRecords.value.filter(record => !ids.includes(record.id))
      trackingTotal.value -= ids.length
    } catch (error) {
      console.error('Failed to batch delete tracking records:', error)
      throw error
    }
  }

  const completeTrackingRecord = async (id: number, result: string) => {
    try {
      const response = await trackingService.completeTrackingRecord(id, result)
      const index = trackingRecords.value.findIndex(record => record.id === id)
      if (index !== -1) {
        trackingRecords.value[index] = response.data
      }
      return response
    } catch (error) {
      console.error('Failed to complete tracking record:', error)
      throw error
    }
  }

  const fetchCustomerTrackingRecords = async (customerId: number, params?: {
    page?: number
    pageSize?: number
    type?: string
    status?: string
  }) => {
    try {
      const response = await trackingService.getCustomerTrackingRecords(customerId, params)
      return response
    } catch (error) {
      console.error('Failed to fetch customer tracking records:', error)
      throw error
    }
  }

  // 提醒相关方法
  const fetchReminders = async (params?: {
    page?: number
    pageSize?: number
    user_id?: number
    customer_id?: number
    type?: string
    status?: string
    start_date?: string
    end_date?: string
  }) => {
    reminderLoading.value = true
    try {
      const response = await reminderService.getReminders(params || {})
      reminders.value = response.data
      reminderTotal.value = response.total
      return response
    } catch (error) {
      console.error('Failed to fetch reminders:', error)
      throw error
    } finally {
      reminderLoading.value = false
    }
  }

  const createReminder = async (data: Omit<AutoReminder, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await reminderService.createReminder(data)
      reminders.value.unshift(response.data)
      reminderTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to create reminder:', error)
      throw error
    }
  }

  const updateReminder = async (id: number, data: Partial<AutoReminder>) => {
    try {
      const response = await reminderService.updateReminder(id, data)
      const index = reminders.value.findIndex(reminder => reminder.id === id)
      if (index !== -1) {
        reminders.value[index] = response.data
      }
      return response
    } catch (error) {
      console.error('Failed to update reminder:', error)
      throw error
    }
  }

  const deleteReminder = async (id: number) => {
    try {
      await reminderService.deleteReminder(id)
      reminders.value = reminders.value.filter(reminder => reminder.id !== id)
      reminderTotal.value -= 1
    } catch (error) {
      console.error('Failed to delete reminder:', error)
      throw error
    }
  }

  const markReminderAsRead = async (id: number) => {
    try {
      const response = await reminderService.markReminderAsRead(id)
      const index = reminders.value.findIndex(reminder => reminder.id === id)
      if (index !== -1) {
        reminders.value[index] = response.data
      }
      // 同时更新今日提醒和逾期提醒
      const todayIndex = todayReminders.value.findIndex(reminder => reminder.id === id)
      if (todayIndex !== -1) {
        todayReminders.value[todayIndex] = response.data
      }
      const overdueIndex = overdueReminders.value.findIndex(reminder => reminder.id === id)
      if (overdueIndex !== -1) {
        overdueReminders.value[overdueIndex] = response.data
      }
      return response
    } catch (error) {
      console.error('Failed to mark reminder as read:', error)
      throw error
    }
  }

  const dismissReminder = async (id: number) => {
    try {
      const response = await reminderService.dismissReminder(id)
      const index = reminders.value.findIndex(reminder => reminder.id === id)
      if (index !== -1) {
        reminders.value[index] = response.data
      }
      return response
    } catch (error) {
      console.error('Failed to dismiss reminder:', error)
      throw error
    }
  }

  const fetchTodayReminders = async () => {
    try {
      const response = await reminderService.getTodayReminders()
      todayReminders.value = response.data
      return response
    } catch (error: any) {
      console.error('Failed to fetch today reminders:', error?.message || error)
      throw error
    }
  }

  const fetchOverdueReminders = async () => {
    try {
      const response = await reminderService.getOverdueReminders()
      overdueReminders.value = response.data
      return response
    } catch (error: any) {
      console.error('Failed to fetch overdue reminders:', error?.message || error)
      throw error
    }
  }

  // 客户行为相关方法
  const fetchCustomerBehaviors = async (customerId: number, params?: {
    page?: number
    pageSize?: number
    start_date?: string
    end_date?: string
    action?: string
  }) => {
    behaviorLoading.value = true
    try {
      const response = await behaviorService.getCustomerBehaviors(customerId, params || {})
      customerBehaviors.value = response.data
      behaviorTotal.value = response.total
      return response
    } catch (error: any) {
      console.error('Failed to fetch customer behaviors:', error?.message || error)
      throw error
    } finally {
      behaviorLoading.value = false
    }
  }

  const recordCustomerBehavior = async (data: Omit<CustomerBehavior, 'id' | 'created_at'>) => {
    try {
      const response = await behaviorService.recordCustomerBehavior(data)
      customerBehaviors.value.unshift(response.data)
      behaviorTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to record customer behavior:', error)
      throw error
    }
  }

  const fetchCustomerBehaviorStats = async (customerId: number, dateRange?: [string, string]) => {
    try {
      const params = dateRange ? {
        start_date: dateRange[0],
        end_date: dateRange[1]
      } : {}
      const response = await behaviorService.getCustomerBehaviorStats(customerId, params)
      return response
    } catch (error) {
      console.error('Fetch customer behavior stats error:', error)
      throw error
    }
  }

  // 统计数据相关方法
  const fetchTrackingStats = async (dateRange?: [string, string]) => {
    try {
      const params = dateRange ? {
        start_date: dateRange[0],
        end_date: dateRange[1]
      } : {}
      const response = await trackingStatsService.getTrackingStats(params)
      trackingStats.value = response
      return response
    } catch (error) {
      console.error('Fetch tracking stats error:', error)
      throw error
    }
  }

  const fetchTrackingTrends = async (dateRange?: [string, string]) => {
    try {
      const params = dateRange ? {
        start_date: dateRange[0],
        end_date: dateRange[1]
      } : {}
      const response = await trackingStatsService.getTrackingTrends(params)
      trackingTrends.value = response
      return response
    } catch (error) {
      console.error('Fetch tracking trends error:', error)
      throw error
    }
  }

  const fetchUserTrackingRanking = async (dateRange?: [string, string]) => {
    try {
      const params = dateRange ? {
        start_date: dateRange[0],
        end_date: dateRange[1]
      } : {}
      const response = await trackingStatsService.getUserTrackingRanking(params)
      userRanking.value = response
      return response
    } catch (error) {
      console.error('Fetch user tracking ranking error:', error)
      throw error
    }
  }

  const getTrackingStatistics = async (params?: {
    start_time?: string
    end_time?: string
  }) => {
    try {
      const mappedParams = params ? {
        user_id: undefined,
        start_date: params.start_time,
        end_date: params.end_time
      } : {}
      const response = await trackingStatsService.getTrackingStats(mappedParams)
      return response
    } catch (error) {
      console.error('Failed to get tracking statistics:', error)
      throw error
    }
  }

  const getCustomerRanking = async (params?: {
    start_time?: string
    end_time?: string
  }) => {
    try {
      const mappedParams = params ? {
        start_date: params.start_time,
        end_date: params.end_time,
        limit: undefined
      } : {}
      const response = await trackingStatsService.getUserTrackingRanking(mappedParams)
      return response
    } catch (error) {
      console.error('Failed to get customer ranking:', error)
      throw error
    }
  }

  const getEffectAnalysis = async (params?: {
    start_time?: string
    end_time?: string
  }) => {
    try {
      const mappedParams = params ? {
        user_id: undefined,
        start_date: params.start_time,
        end_date: params.end_time,
        period: undefined
      } : {}
      const response = await trackingStatsService.getTrackingTrends(mappedParams)
      return response
    } catch (error) {
      console.error('Failed to get effect analysis:', error)
      throw error
    }
  }

  // 重置状态
  const resetTrackingState = () => {
    trackingRecords.value = []
    currentTrackingRecord.value = null
    trackingTotal.value = 0
  }

  const resetReminderState = () => {
    reminders.value = []
    todayReminders.value = []
    overdueReminders.value = []
    reminderTotal.value = 0
  }

  const resetBehaviorState = () => {
    customerBehaviors.value = []
    behaviorTotal.value = 0
  }

  const resetStatsState = () => {
    trackingStats.value = null
    trackingTrends.value = []
    userRanking.value = []
  }

  const resetAllState = () => {
    resetTrackingState()
    resetReminderState()
    resetBehaviorState()
    resetStatsState()
  }

  return {
    // 状态
    trackingRecords,
    currentTrackingRecord,
    trackingLoading,
    trackingTotal,
    reminders,
    todayReminders,
    overdueReminders,
    reminderLoading,
    reminderTotal,
    customerBehaviors,
    behaviorLoading,
    behaviorTotal,
    trackingStats,
    trackingTrends,
    userRanking,
    statsLoading,

    // 计算属性
    pendingTrackingRecords,
    completedTrackingRecords,
    overdueTrackingRecords,
    unreadReminders,
    trackingRecordCount,
    reminderCount,
    unreadReminderCount,

    // 跟踪记录方法
    fetchTrackingRecords,
    fetchTrackingRecord,
    createTrackingRecord,
    updateTrackingRecord,
    deleteTrackingRecord,
    batchDeleteTrackingRecords,
    completeTrackingRecord,
    fetchCustomerTrackingRecords,

    // 提醒方法
    fetchReminders,
    createReminder,
    updateReminder,
    deleteReminder,
    markReminderAsRead,
    dismissReminder,
    fetchTodayReminders,
    fetchOverdueReminders,

    // 客户行为方法
    fetchCustomerBehaviors,
    recordCustomerBehavior,
    fetchCustomerBehaviorStats,

    // 统计数据方法
    fetchTrackingStats,
    fetchTrackingTrends,
    fetchUserTrackingRanking,
    getTrackingStatistics,
    getCustomerRanking,
    getEffectAnalysis,

    // 重置方法
    resetTrackingState,
    resetReminderState,
    resetBehaviorState,
    resetStatsState,
    resetAllState
  }
})