{"migrationId": "82035866-521e-4bdd-9058-4df7d0e672d5", "timestamp": "2025-08-18T07:24:21.808Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 17, "failedTables": 4, "totalRecords": 165, "migratedRecords": 156, "failedRecords": 9}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:23:37.871Z", "errors": [], "endTime": "2025-08-18T07:23:42.803Z", "duration": 4932}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:23:42.809Z", "errors": [], "endTime": "2025-08-18T07:23:47.393Z", "duration": 4584}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:23:47.396Z", "errors": [], "endTime": "2025-08-18T07:23:50.679Z", "duration": 3283}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:23:50.683Z", "errors": [], "endTime": "2025-08-18T07:23:53.105Z", "duration": 2422}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:23:53.107Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:23:55.286Z", "errors": [], "endTime": "2025-08-18T07:23:57.234Z", "duration": 1948}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:23:57.237Z", "errors": [], "endTime": "2025-08-18T07:23:59.511Z", "duration": 2274}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:23:59.514Z", "errors": [], "endTime": "2025-08-18T07:24:01.463Z", "duration": 1949}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:01.465Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:24:01.978Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:24:04.500Z", "duration": 2522}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:04.502Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:04.906Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 2, "failedRecords": 0, "startTime": "2025-08-18T07:24:05.571Z", "errors": [], "endTime": "2025-08-18T07:24:10.019Z", "duration": 4448}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:10.022Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:24:10.431Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:24:12.607Z", "duration": 2176}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:12.609Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:13.039Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:13.905Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:24:15.504Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:24:16.590Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`follow_ups`, CONSTRAINT `fk_follow_ups_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:24:19.787Z", "duration": 3197}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:24:19.790Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:24:21.805Z", "duration": 2015}], "logs": [{"id": "85c8bc7c-08f1-4404-8204-df7e7cf4be84", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:23:37.871Z", "end_time": "2025-08-18T07:23:42.804Z", "duration_ms": 4933}, {"id": "cdc396d6-f15e-4526-a33b-63ddb2c9f0e7", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:23:42.809Z", "end_time": "2025-08-18T07:23:47.393Z", "duration_ms": 4584}, {"id": "882db8a5-7f82-4d10-b103-6563df7f9822", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:23:47.396Z", "end_time": "2025-08-18T07:23:50.679Z", "duration_ms": 3283}, {"id": "d712cb47-cb5c-4291-bdd2-7ceb0b65d8ad", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:23:50.683Z", "end_time": "2025-08-18T07:23:53.105Z", "duration_ms": 2422}, {"id": "318327c4-2fad-4d80-b3c6-4c47ce00a41e", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:23:53.107Z", "end_time": "2025-08-18T07:23:55.285Z", "duration_ms": 2178}, {"id": "0b66c6a3-9188-4e7d-ba3f-f98b015551fd", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:23:55.286Z", "end_time": "2025-08-18T07:23:57.234Z", "duration_ms": 1948}, {"id": "57e4ab6f-987d-4995-afe5-0348651131b2", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:23:57.237Z", "end_time": "2025-08-18T07:23:59.511Z", "duration_ms": 2274}, {"id": "c9719124-ac4e-4839-99ee-6d56082cf6c5", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:23:59.514Z", "end_time": "2025-08-18T07:24:01.463Z", "duration_ms": 1949}, {"id": "e4227075-a806-4fe2-bcf2-07bfa247fda2", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:01.465Z", "end_time": "2025-08-18T07:24:01.978Z", "duration_ms": 513}, {"id": "be07adc5-66d6-44bf-b255-b6b274f36630", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:01.978Z", "end_time": "2025-08-18T07:24:04.500Z", "duration_ms": 2522}, {"id": "5bc2b823-172d-4a70-a489-0eedcc205a8e", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:04.502Z", "end_time": "2025-08-18T07:24:04.906Z", "duration_ms": 404}, {"id": "88884949-aa84-420e-a6df-20e52db284c3", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:04.906Z", "end_time": "2025-08-18T07:24:05.571Z", "duration_ms": 665}, {"id": "5a06f0da-0e52-4f5c-bfe0-822356a47c73", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 2, "start_time": "2025-08-18T07:24:05.571Z", "end_time": "2025-08-18T07:24:10.019Z", "duration_ms": 4448}, {"id": "0adec0e0-a7a5-41d7-9081-ec5e538a8699", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:10.022Z", "end_time": "2025-08-18T07:24:10.431Z", "duration_ms": 409}, {"id": "9557a6a3-0476-4f5e-bc1f-3d7e6d9fa8e8", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:10.431Z", "end_time": "2025-08-18T07:24:12.607Z", "duration_ms": 2176}, {"id": "24e1709a-e48f-4c63-a646-7a592f70a4ab", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:12.609Z", "end_time": "2025-08-18T07:24:13.039Z", "duration_ms": 430}, {"id": "4c6cbe50-2845-4c44-99ff-a83191b2a833", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:13.039Z", "end_time": "2025-08-18T07:24:13.905Z", "duration_ms": 866}, {"id": "9b1d3f2d-8f83-4e18-800d-90cd61a5372e", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:13.905Z", "end_time": "2025-08-18T07:24:15.504Z", "duration_ms": 1599}, {"id": "82686431-28b0-422b-9ac0-20bff2038790", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:15.504Z", "end_time": "2025-08-18T07:24:16.590Z", "duration_ms": 1086}, {"id": "ca5f3d5d-4270-49a9-9786-a17546238e99", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:16.590Z", "end_time": "2025-08-18T07:24:19.787Z", "duration_ms": 3197}, {"id": "eb5ca1cf-3637-48e0-b096-437d9f77c8f3", "migration_id": "82035866-521e-4bdd-9058-4df7d0e672d5", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:24:19.790Z", "end_time": "2025-08-18T07:24:21.805Z", "duration_ms": 2015}]}