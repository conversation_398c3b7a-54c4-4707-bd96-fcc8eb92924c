<template>
  <div class="conversion-analysis">

    <!-- 筛选条件 -->
    <n-card class="filter-card">
      <n-form inline :label-width="80">
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="filters.dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="营销活动">
          <n-select
            v-model:value="filters.campaign"
            placeholder="选择营销活动"
            clearable
            style="width: 180px"
            :options="campaignOptions"
          />
        </n-form-item>
        <n-form-item label="转化类型">
          <n-select
            v-model:value="filters.conversionType"
            placeholder="选择转化类型"
            clearable
            style="width: 150px"
            :options="conversionTypeOptions"
          />
        </n-form-item>
        <n-form-item label="客户来源">
          <n-select
            v-model:value="filters.source"
            placeholder="选择客户来源"
            clearable
            style="width: 150px"
            :options="sourceOptions"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button @click="handleSearch" type="primary">
              <template #icon>
                <n-icon><Search /></n-icon>
              </template>
              查询
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><Refresh /></n-icon>
              </template>
              重置
            </n-button>
            <n-button @click="handleExport">
              <template #icon>
                <n-icon><Download /></n-icon>
              </template>
              导出
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 转化概览 -->
    <n-grid :cols="4" :x-gap="20" class="stats-grid">
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="总访问量" :value="stats.totalVisits">
            <template #suffix>
              <n-icon color="#2080f0"><Eye /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="总转化数" :value="stats.totalConversions">
            <template #suffix>
              <n-icon color="#18a058"><CheckmarkCircle /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="整体转化率" :value="stats.overallConversionRate" :precision="2">
            <template #suffix>
              <span>%</span>
              <n-icon color="#f0a020"><TrendingUp /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card class="stat-card">
          <n-statistic label="转化收入" :value="stats.conversionRevenue" :precision="2">
            <template #prefix>¥</template>
            <template #suffix>
              <n-icon color="#d03050"><Cash /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表分析 -->
    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="转化率趋势">
          <div ref="conversionTrendChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="营销渠道转化对比">
          <div ref="channelComparisonChart" style="height: 350px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="2" :x-gap="20" class="charts-grid">
      <n-grid-item>
        <n-card title="转化漏斗分析">
          <div ref="conversionFunnelChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="转化时间分布">
          <div ref="conversionTimeChart" style="height: 300px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 营销活动效果分析 -->
    <n-card title="营销活动效果分析" class="table-card">
      <template #header-extra>
        <n-space>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="campaignColumns"
        :data="campaignAnalysisData"
        :loading="loading"
        size="medium"
        :row-key="(row: CampaignAnalysis) => row.id"
      />
    </n-card>

    <!-- 客户转化详情 -->
    <n-card title="客户转化详情" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客户姓名或手机号"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
        </n-space>
      </template>
      
      <n-data-table
        :columns="conversionColumns"
        :data="filteredConversionData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: ConversionRecord) => row.id"
        size="medium"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NIcon,
  NTag,
  NStatistic,
  NGrid,
  NGridItem,
  NProgress,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import {
  SearchOutline as Search,
  RefreshOutline as Refresh,
  DownloadOutline as Download,
  EyeOutline as Eye,
  CheckmarkCircleOutline as CheckmarkCircle,
  TrendingUpOutline as TrendingUp,
  CashOutline as Cash
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface CampaignAnalysis {
  id: number
  name: string
  type: string
  startDate: string
  endDate: string
  totalVisits: number
  totalConversions: number
  conversionRate: number
  cost: number
  revenue: number
  roi: number
  status: string
}

interface ConversionRecord {
  id: number
  customerName: string
  phone: string

  source: string
  campaign: string
  conversionType: string
  conversionDate: string
  conversionValue: number
  conversionPath: string[]
  touchpoints: number
  conversionTime: number // 转化时长(小时)
  status: string
}

const message = useMessage()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const campaignAnalysisData = ref<CampaignAnalysis[]>([])
const conversionData = ref<ConversionRecord[]>([])

// 图表引用
const conversionTrendChart = ref<HTMLElement>()
const channelComparisonChart = ref<HTMLElement>()
const conversionFunnelChart = ref<HTMLElement>()
const conversionTimeChart = ref<HTMLElement>()

// 图表实例
let conversionTrendInstance: echarts.ECharts | null = null
let channelComparisonInstance: echarts.ECharts | null = null
let conversionFunnelInstance: echarts.ECharts | null = null
let conversionTimeInstance: echarts.ECharts | null = null

// 筛选条件
const filters = reactive({
  dateRange: null as [number, number] | null,
  campaign: null as string | null,
  conversionType: null as string | null,
  source: null as string | null
})

// 统计数据
const stats = reactive({
  totalVisits: 0,
  totalConversions: 0,
  overallConversionRate: 0,
  conversionRevenue: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 选项配置
const campaignOptions = [
  { label: '新年促销活动', value: 'new_year_promotion' },
  { label: '春季大促', value: 'spring_sale' },
  { label: '会员专享', value: 'member_exclusive' },
  { label: '限时秒杀', value: 'flash_sale' }
]

const conversionTypeOptions = [
  { label: '注册转化', value: 'registration' },
  { label: '购买转化', value: 'purchase' },
  { label: '咨询转化', value: 'inquiry' },
  { label: '下载转化', value: 'download' }
]

const sourceOptions = [
  { label: '搜索引擎', value: 'search_engine' },
  { label: '社交媒体', value: 'social_media' },
  { label: '直接访问', value: 'direct' },

  { label: '广告投放', value: 'advertising' }
]

// 营销活动效果表格列
const campaignColumns: DataTableColumns<CampaignAnalysis> = [
  {
    title: '活动名称',
    key: 'name',
    width: 150,
    fixed: 'left'
  },
  {
    title: '活动类型',
    key: 'type',
    width: 100,
    render(row) {
      const typeMap: Record<string, { text: string; type: 'success' | 'info' | 'warning' | 'error' | 'default' | 'primary' }> = {
        promotion: { text: '促销活动', type: 'success' },
        content: { text: '内容营销', type: 'info' },
        social: { text: '社交营销', type: 'warning' },
      
      }
      const type = typeMap[row.type] || { text: row.type, type: 'default' as const }
      return h(NTag, { type: type.type, size: 'small' }, { default: () => type.text })
    }
  },
  {
    title: '开始时间',
    key: 'startDate',
    width: 120,
    render(row) {
      return new Date(row.startDate).toLocaleDateString()
    }
  },
  {
    title: '结束时间',
    key: 'endDate',
    width: 120,
    render(row) {
      return new Date(row.endDate).toLocaleDateString()
    }
  },
  {
    title: '访问量',
    key: 'totalVisits',
    width: 100,
    sorter: (a, b) => a.totalVisits - b.totalVisits
  },
  {
    title: '转化数',
    key: 'totalConversions',
    width: 100,
    sorter: (a, b) => a.totalConversions - b.totalConversions
  },
  {
    title: '转化率',
    key: 'conversionRate',
    width: 120,
    render(row) {
      return h(NProgress, {
        type: 'line',
        percentage: row.conversionRate,
        showIndicator: true,
        status: row.conversionRate >= 10 ? 'success' : row.conversionRate >= 5 ? 'warning' : 'error'
      })
    },
    sorter: (a, b) => a.conversionRate - b.conversionRate
  },
  {
    title: '投入成本',
    key: 'cost',
    width: 120,
    render(row) {
      return `¥${row.cost.toLocaleString()}`
    },
    sorter: (a, b) => a.cost - b.cost
  },
  {
    title: '转化收入',
    key: 'revenue',
    width: 120,
    render(row) {
      return `¥${row.revenue.toLocaleString()}`
    },
    sorter: (a, b) => a.revenue - b.revenue
  },
  {
    title: 'ROI',
    key: 'roi',
    width: 100,
    render(row) {
      const color = row.roi >= 300 ? '#18a058' : row.roi >= 200 ? '#f0a020' : '#d03050'
      return h('span', { style: { color, fontWeight: 'bold' } }, `${row.roi}%`)
    },
    sorter: (a, b) => a.roi - b.roi
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        active: { text: '进行中', type: 'success' as const },
        completed: { text: '已完成', type: 'info' as const },
        paused: { text: '已暂停', type: 'warning' as const },
        cancelled: { text: '已取消', type: 'error' as const }
      }
      const status = statusMap[row.status as keyof typeof statusMap] || { text: row.status, type: 'default' as const }
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    }
  }
]

// 客户转化详情表格列
const conversionColumns: DataTableColumns<ConversionRecord> = [
  {
    title: '客户姓名',
    key: 'customerName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '手机号',
    key: 'phone',
    width: 130
  },
  {
    title: '客户来源',
    key: 'source',
    width: 120,
    render(row) {
      const sourceMap: Record<string, string> = {
        search_engine: '搜索引擎',
        social_media: '社交媒体',
        direct: '直接访问',
        邮件营销: '邮件营销',
        advertising: '广告投放'
      }
      return sourceMap[row.source] || row.source
    }
  },
  {
    title: '营销活动',
    key: 'campaign',
    width: 150
  },
  {
    title: '转化类型',
    key: 'conversionType',
    width: 100,
    render(row) {
      const typeMap = {
        registration: { text: '注册', type: 'info' as const },
        purchase: { text: '购买', type: 'success' as const },
        inquiry: { text: '咨询', type: 'warning' as const },
        download: { text: '下载', type: 'default' as const }
      }
      const type = typeMap[row.conversionType as keyof typeof typeMap] || { text: row.conversionType, type: 'default' as const }
      return h(NTag, { type: type.type, size: 'small' }, { default: () => type.text })
    }
  },
  {
    title: '转化时间',
    key: 'conversionDate',
    width: 150,
    render(row) {
      return new Date(row.conversionDate).toLocaleString()
    }
  },
  {
    title: '转化价值',
    key: 'conversionValue',
    width: 120,
    render(row) {
      return `¥${row.conversionValue.toLocaleString()}`
    },
    sorter: (a, b) => a.conversionValue - b.conversionValue
  },
  {
    title: '触点数量',
    key: 'touchpoints',
    width: 100,
    sorter: (a, b) => a.touchpoints - b.touchpoints
  },
  {
    title: '转化时长',
    key: 'conversionTime',
    width: 120,
    render(row) {
      const hours = row.conversionTime
      if (hours < 24) {
        return `${hours}小时`
      } else {
        const days = Math.floor(hours / 24)
        const remainingHours = hours % 24
        return `${days}天${remainingHours}小时`
      }
    },
    sorter: (a, b) => a.conversionTime - b.conversionTime
  },
  {
    title: '转化路径',
    key: 'conversionPath',
    width: 200,
    render(row) {
      return h('n-space', { size: 'small' }, {
        default: () => row.conversionPath.map((path, index) => 
          h('n-tag', { size: 'small', type: 'info' }, { default: () => `${index + 1}.${path}` })
        )
      })
    }
  }
]

// 计算属性
const filteredConversionData = computed(() => {
  if (!searchKeyword.value) return conversionData.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return conversionData.value.filter(record => 
    record.customerName.toLowerCase().includes(keyword) ||
    record.phone.includes(keyword)
  )
})

// 方法
const handleSearch = () => {
  loadData()
  loadCharts()
}

const handleReset = () => {
  Object.assign(filters, {
    dateRange: null,
    campaign: null,
    conversionType: null,
    source: null
  })
  searchKeyword.value = ''
  loadData()
  loadCharts()
}

const handleRefresh = () => {
  loadData()
  loadCharts()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟营销活动数据
    campaignAnalysisData.value = [
      {
        id: 1,
        name: '新年促销活动',
        type: 'promotion',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-01-31T23:59:59Z',
        totalVisits: 15000,
        totalConversions: 1200,
        conversionRate: 8.0,
        cost: 50000,
        revenue: 180000,
        roi: 360,
        status: 'completed'
      },
      {
        id: 2,
        name: '春季大促',
        type: 'promotion',
        startDate: '2024-03-01T00:00:00Z',
        endDate: '2024-03-31T23:59:59Z',
        totalVisits: 12000,
        totalConversions: 800,
        conversionRate: 6.7,
        cost: 40000,
        revenue: 120000,
        roi: 300,
        status: 'active'
      },
      {
        id: 3,
        name: '会员专享',
        type: 'content',
        startDate: '2024-02-01T00:00:00Z',
        endDate: '2024-02-29T23:59:59Z',
        totalVisits: 8000,
        totalConversions: 600,
        conversionRate: 7.5,
        cost: 25000,
        revenue: 90000,
        roi: 360,
        status: 'completed'
      }
    ]
    
    // 模拟客户转化数据
    conversionData.value = [
      {
        id: 1,
        customerName: '张三',
        phone: '13800138001',
    
        source: 'search_engine',
        campaign: '新年促销活动',
        conversionType: 'purchase',
        conversionDate: '2024-01-15T14:30:00Z',
        conversionValue: 1500,
        conversionPath: ['搜索', '浏览商品', '加入购物车', '结算支付'],
        touchpoints: 4,
        conversionTime: 72,
        status: 'completed'
      },
      {
        id: 2,
        customerName: '李四',
        phone: '13800138002',
    
        source: 'social_media',
        campaign: '春季大促',
        conversionType: 'registration',
        conversionDate: '2024-03-10T10:20:00Z',
        conversionValue: 0,
        conversionPath: ['社交分享', '点击链接', '注册账号'],
        touchpoints: 3,
        conversionTime: 24,
        status: 'completed'
      },
      {
        id: 3,
        customerName: '王五',
        phone: '13800138003',
        source: '邮件营销',
        campaign: '会员专享',
        conversionType: 'inquiry',
        conversionDate: '2024-02-20T16:45:00Z',
        conversionValue: 800,
        conversionPath: ['邮件推送', '点击链接', '在线咨询', '留下联系方式'],
        touchpoints: 4,
        conversionTime: 48,
        status: 'completed'
      }
    ]
    
    pagination.itemCount = conversionData.value.length
    
    // 更新统计数据
    updateStats()
  } catch (error) {
    message.error('获取转化率数据失败')
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  const campaigns = campaignAnalysisData.value
  const conversions = conversionData.value
  
  stats.totalVisits = campaigns.reduce((sum, c) => sum + c.totalVisits, 0)
  stats.totalConversions = campaigns.reduce((sum, c) => sum + c.totalConversions, 0)
  stats.overallConversionRate = stats.totalVisits > 0 ? (stats.totalConversions / stats.totalVisits) * 100 : 0
  stats.conversionRevenue = conversions.reduce((sum, c) => sum + c.conversionValue, 0)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (conversionTrendChart.value) {
      conversionTrendInstance = echarts.init(conversionTrendChart.value)
    }
    if (channelComparisonChart.value) {
      channelComparisonInstance = echarts.init(channelComparisonChart.value)
    }
    if (conversionFunnelChart.value) {
      conversionFunnelInstance = echarts.init(conversionFunnelChart.value)
    }
    if (conversionTimeChart.value) {
      conversionTimeInstance = echarts.init(conversionTimeChart.value)
    }
    
    loadCharts()
  })
}

// 加载图表数据
const loadCharts = () => {
  // 转化率趋势图
  if (conversionTrendInstance) {
    const option = {
      title: { text: '转化率趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['访问量', '转化数', '转化率'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: [
        {
          type: 'value',
          name: '数量',
          position: 'left'
        },
        {
          type: 'value',
          name: '转化率(%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '访问量',
          type: 'bar',
          data: [15000, 8000, 12000, 10000, 14000, 16000]
        },
        {
          name: '转化数',
          type: 'bar',
          data: [1200, 600, 800, 700, 980, 1100]
        },
        {
          name: '转化率',
          type: 'line',
          yAxisIndex: 1,
          data: [8.0, 7.5, 6.7, 7.0, 7.0, 6.9],
          smooth: true
        }
      ]
    }
    conversionTrendInstance.setOption(option)
  }
  
  // 营销渠道转化对比图
  if (channelComparisonInstance) {
    const option = {
      title: { text: '营销渠道转化对比', left: 'center' },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['访问量', '转化数'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: ['搜索引擎', '社交媒体', '直接访问', '邮件营销', '广告投放']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '访问量',
          type: 'bar',
          data: [8000, 6000, 4000, 3000, 5000]
        },
        {
          name: '转化数',
          type: 'bar',
          data: [640, 420, 280, 240, 350]
        }
      ]
    }
    channelComparisonInstance.setOption(option)
  }
  
  // 转化漏斗分析图
  if (conversionFunnelInstance) {
    const option = {
      title: { text: '转化漏斗分析', left: 'center' },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}人 ({d}%)'
      },
      series: [{
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        data: [
          { value: 10000, name: '访问' },
          { value: 3000, name: '浏览商品' },
          { value: 1500, name: '加入购物车' },
          { value: 800, name: '开始结算' },
          { value: 600, name: '完成支付' }
        ]
      }]
    }
    conversionFunnelInstance.setOption(option)
  }
  
  // 转化时间分布图
  if (conversionTimeInstance) {
    const option = {
      title: { text: '转化时间分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 150, name: '1小时内' },
          { value: 280, name: '1-24小时' },
          { value: 320, name: '1-3天' },
          { value: 180, name: '3-7天' },
          { value: 70, name: '7天以上' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    conversionTimeInstance.setOption(option)
  }
}

// 初始化
onMounted(() => {
  loadData()
  initCharts()
})
</script>

<style scoped>
.conversion-analysis {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-grid {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.charts-grid {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}
</style>