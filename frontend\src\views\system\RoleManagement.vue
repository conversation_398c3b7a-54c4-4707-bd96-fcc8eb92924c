<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
  
          <p class="page-description">管理系统角色和权限分配</p>
        </div>
        <n-button type="primary" @click="handleCreate">
          <template #icon>
            <n-icon><Plus /></n-icon>
          </template>
          新建角色
        </n-button>
      </n-space>
    </div>

    <!-- 筛选区域 -->
    <n-card class="filter-card">
      <n-form inline :model="filters" label-placement="left">
        <n-form-item label="搜索">
          <n-input
            v-model:value="filters.search"
            placeholder="搜索角色名称或描述"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="状态">
          <n-select
            v-model:value="statusValue"
            placeholder="选择状态"
            clearable
            style="width: 120px"
            :options="statusOptions"
            :render-label="(option: any) => option.label"
            :render-option="(option: any) => option.label"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button @click="handleSearch" type="primary">
              <template #icon>
                <n-icon><Search /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button type="default" @click="handleReset">
              <template #icon>
                <n-icon><refresh-outline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 角色列表 -->
    <n-card class="table-card">
      <n-data-table
        :columns="columns"
        :data="roleStore.roles"
        :loading="roleStore.loading"
        :pagination="paginationReactive"
        :row-key="(row: RoleWithPermissions) => row.id"
        size="medium"
      />
    </n-card>

    <!-- 角色表单弹窗 -->
    <RoleFormModal
      v-model:show="showFormModal"
      :role="currentRole"
      @success="handleFormSuccess"
    />

    <!-- 权限分配弹窗 -->
    <PermissionModal
      v-model:show="showPermissionModal"
      :role="currentRole"
      @success="handlePermissionSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDataTable,
  NIcon,
  NTag,
  NPopconfirm,
  NTooltip,
  useMessage,
  type DataTableColumns
} from 'naive-ui'
import {
  AddOutline as Plus,
  SearchOutline as Search,
  RefreshOutline as Refresh,
  CreateOutline as Edit,
  TrashOutline as Delete,
  ShieldOutline as Shield,
  PeopleOutline as Users
} from '@vicons/ionicons5'
import { useRoleStore } from '@/stores/modules/role'
import type { RoleWithPermissions, RoleQueryParams } from '@/api/roleService'
import RoleFormModal from './components/RoleFormModal.vue'
import PermissionModal from './components/PermissionModal.vue'

const message = useMessage()
const roleStore = useRoleStore()

// 预创建图标组件，避免在render函数中重复创建
const EditIcon = () => h(NIcon, null, { default: () => h(Edit) })
const PermissionIcon = () => h(NIcon, null, { default: () => h(Shield) })
const DeleteIcon = () => h(NIcon, null, { default: () => h(Delete) })

// 响应式数据
const showFormModal = ref(false)
const showPermissionModal = ref(false)
const currentRole = ref<RoleWithPermissions | null>(null)

// 筛选条件
const filters = reactive<RoleQueryParams>({
  search: '',
  status: undefined as boolean | undefined
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 状态值转换
const statusValue = computed({
  get: () => filters.status === undefined ? null : String(filters.status),
  set: (val: string | null) => {
    if (val === null) {
      filters.status = undefined
    } else {
      filters.status = val === 'true'
    }
  }
})

// 分页配置
const paginationReactive = computed(() => ({
  page: roleStore.pagination.page,
  pageSize: roleStore.pagination.pageSize,
  itemCount: roleStore.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    roleStore.setPagination(page)
    loadRoles()
  },
  onUpdatePageSize: (pageSize: number) => {
    roleStore.setPagination(1, pageSize)
    loadRoles()
  }
}))

// 表格列配置
const columns: DataTableColumns<RoleWithPermissions> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '角色名称',
    key: 'name',
    width: 150,
    render(row) {
      return h('div', {
        style: {
          fontWeight: row.is_system ? 'bold' : 'normal',
          color: row.is_system ? '#2080f0' : 'inherit'
        }
      }, row.name)
    }
  },
  {
    title: '角色描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '权限数量',
    key: 'permissions',
    width: 100,
    render(row) {
      return h(NTag, {
        type: 'info',
        size: 'small'
      }, {
        default: () => `${row.permissions?.length || 0}个`
      })
    }
  },
  {
    title: '类型',
    key: 'is_system',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_system ? 'warning' : 'default',
        size: 'small'
      }, {
        default: () => row.is_system ? '系统角色' : '自定义'
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.status ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => row.status ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'primary',
              quaternary: true,
              onClick: () => handleEdit(row)
            }, {
              icon: EditIcon
            }),
            default: () => '编辑角色'
          }),
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'info',
              quaternary: true,
              onClick: () => handlePermission(row)
            }, {
              icon: PermissionIcon
            }),
            default: () => '权限管理'
          }),
          !row.is_system && h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row.id)
          }, {
            trigger: () => h(NTooltip, { trigger: 'hover' }, {
              trigger: () => h(NButton, {
                size: 'small',
                type: 'error',
                quaternary: true
              }, {
                icon: DeleteIcon
              }),
              default: () => '删除角色'
            }),
            default: () => '确定删除这个角色吗？'
          })
        ].filter(Boolean)
      })
    }
  }
]

// 加载角色列表
const loadRoles = async () => {
  try {
    roleStore.setFilters(filters)
    await roleStore.fetchRoles()
  } catch (error) {
    message.error('获取角色列表失败')
  }
}

// 搜索
const handleSearch = () => {
  roleStore.setFilters(filters)
  roleStore.setPagination(1)
  loadRoles()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    search: '',
    status: undefined
  })
  roleStore.resetFilters()
  roleStore.setPagination(1)
  loadRoles()
}

// 新建角色
const handleCreate = () => {
  currentRole.value = null
  showFormModal.value = true
}

// 编辑角色
const handleEdit = (role: RoleWithPermissions) => {
  currentRole.value = role
  showFormModal.value = true
}

// 权限管理
const handlePermission = (role: RoleWithPermissions) => {
  currentRole.value = role
  showPermissionModal.value = true
}

// 删除角色
const handleDelete = async (id: number) => {
  try {
    await roleStore.deleteRole(id)
    message.success('删除角色成功')
    loadRoles()
  } catch (error) {
    message.error('删除角色失败')
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  showFormModal.value = false
  loadRoles()
}

// 权限分配成功回调
const handlePermissionSuccess = () => {
  showPermissionModal.value = false
  loadRoles()
}

// 初始化
onMounted(() => {
  loadRoles()
  roleStore.fetchPermissions()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-description {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  background: #fff;
}
</style>