"""安全工具函数

提供密码哈希、JWT令牌生成和验证等安全相关功能
"""

import bcrypt
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext

from ..config import settings

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 哈希密码
        
    Returns:
        bool: 密码是否正确
    """
    try:
        # 兼容旧的无加密密码
        if not hashed_password.startswith('$2b$'):
            return plain_password == hashed_password
        
        return pwd_context.verify(plain_password, hashed_password)
    except Exception:
        return False


def get_password_hash(password: str) -> str:
    """生成密码哈希
    
    Args:
        password: 明文密码
        
    Returns:
        str: 哈希密码
    """
    return pwd_context.hash(password)


def create_access_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        str: JWT令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.security.access_token_expire_minutes
        )
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.security.secret_key, 
        algorithm=settings.security.algorithm
    )
    
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        Optional[Dict[str, Any]]: 解码后的数据，验证失败返回None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.security.secret_key, 
            algorithms=[settings.security.algorithm]
        )
        return payload
    except jwt.PyJWTError:
        return None


def create_refresh_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """创建刷新令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        str: JWT刷新令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=settings.security.refresh_token_expire_days
        )
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.security.secret_key, 
        algorithm=settings.security.algorithm
    )
    
    return encoded_jwt


def verify_refresh_token(token: str) -> Optional[Dict[str, Any]]:
    """验证刷新令牌
    
    Args:
        token: JWT刷新令牌
        
    Returns:
        Optional[Dict[str, Any]]: 解码后的数据，验证失败返回None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.security.secret_key, 
            algorithms=[settings.security.algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "refresh":
            return None
            
        return payload
    except jwt.PyJWTError:
        return None


def generate_reset_token(user_id: str) -> str:
    """生成密码重置令牌
    
    Args:
        user_id: 用户ID
        
    Returns:
        str: 重置令牌
    """
    data = {
        "sub": user_id,
        "type": "reset",
        "exp": datetime.utcnow() + timedelta(hours=1)  # 1小时有效期
    }
    
    return jwt.encode(
        data, 
        settings.security.secret_key, 
        algorithm=settings.security.algorithm
    )


def verify_reset_token(token: str) -> Optional[str]:
    """验证密码重置令牌
    
    Args:
        token: 重置令牌
        
    Returns:
        Optional[str]: 用户ID，验证失败返回None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.security.secret_key, 
            algorithms=[settings.security.algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "reset":
            return None
            
        return payload.get("sub")
    except jwt.PyJWTError:
        return None