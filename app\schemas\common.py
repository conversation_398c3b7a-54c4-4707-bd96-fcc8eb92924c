"""通用数据模式

定义API响应的通用数据结构
"""

from pydantic import BaseModel, Field
from typing import Optional, Any, Generic, TypeVar
from datetime import datetime

T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模式"""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")


class ErrorResponse(BaseModel):
    """错误响应模式"""
    
    success: bool = Field(False, description="操作是否成功")
    message: str = Field(..., description="错误消息")
    error: Optional[str] = Field(None, description="详细错误信息")


class PaginationInfo(BaseModel):
    """分页信息模式"""
    
    page: int = Field(..., ge=1, description="当前页码")
    pageSize: int = Field(..., ge=1, le=100, description="每页数量")
    total: int = Field(..., ge=0, description="总记录数")
    totalPages: int = Field(..., ge=0, description="总页数")


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模式"""
    
    success: bool = Field(True, description="操作是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: list[T] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class HealthResponse(BaseModel):
    """健康检查响应模式"""
    
    success: bool = Field(True, description="服务状态")
    message: str = Field("ok", description="状态消息")
    timestamp: datetime = Field(..., description="检查时间")
    version: str = Field(..., description="应用版本")