<template>
  <div class="follow-process-actions">
    <n-space size="small">
      <n-button
        size="small"
        type="primary"
        @click="handleEdit"
        class="action-btn"
      >
        <template #icon>
          <n-icon><CreateOutline /></n-icon>
        </template>
        编辑
      </n-button>
      
      <n-button
        size="small"
        type="success"
        @click="handleFollow"
        class="action-btn"
      >
        <template #icon>
          <n-icon><ChatbubbleEllipsesOutline /></n-icon>
        </template>
        跟进
      </n-button>
      
      <n-button
        size="small"
        type="info"
        @click="handleMoveToTop"
        class="action-btn action-btn-top"
        title="置顶客户"
      >
        <template #icon>
          <n-icon><ChevronUpOutline /></n-icon>
        </template>
      </n-button>
    </n-space>

    <!-- 客户编辑模态框 -->
    <BasicInfoForm
      v-model:show="showEditModal"
      :model-value="customer"
      :is-edit="true"
      @submit="handleEditSuccess"
    />



    <!-- 客户跟进记录弹窗 -->
    <CustomerFollowModal
      v-model:show="showFollowModal"
      :customer="customer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NButton, NSpace, NIcon, useMessage } from 'naive-ui'
import {
  CreateOutline,
  PersonAddOutline,
  ChatbubbleEllipsesOutline,
  ChevronUpOutline
} from '@vicons/ionicons5'
import BasicInfoForm from '../../views/customer/components/BasicInfoForm.vue'
import CustomerFollowModal from './CustomerFollowModal.vue'

interface Props {
  customer: any
  onEdit?: (customer: any) => void
  onAssign?: (customer: any) => void
  onFollow?: (customer: any) => void
  onMoveToTop?: (customer: any) => void
  onRefresh?: () => void
}

const props = defineProps<Props>()
const message = useMessage()

// 编辑模态框显示状态
const showEditModal = ref(false)
// 跟进模态框显示状态
const showFollowModal = ref(false)

// 编辑按钮点击处理
const handleEdit = () => {
  showEditModal.value = true
}

// 编辑成功处理
const handleEditSuccess = () => {
  message.success('客户信息更新成功')
  // 触发刷新
  if (props.onRefresh) {
    props.onRefresh()
  }
}

// 置顶按钮点击处理
const handleMoveToTop = () => {
  message.success(`客户「${props.customer.name}」已置顶显示`)
  
  // 触发置顶回调
  if (props.onMoveToTop) {
    props.onMoveToTop(props.customer)
  }
  
  // 触发刷新
  if (props.onRefresh) {
    props.onRefresh()
  }
}

// 跟进按钮点击处理
const handleFollow = () => {
  showFollowModal.value = true
  
  // 保持原有的回调兼容性
  if (props.onFollow) {
    props.onFollow(props.customer)
  }
}
</script>

<style scoped>
.follow-process-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.3s ease;
  max-width: 60px !important;
  max-height: 24px !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  border-width: 1px !important;
  line-height: 1.2 !important;
}

/* 编辑按钮样式 - 蓝色系 */
.action-btn-edit {
  background: rgba(102, 126, 234, 0.05) !important;
  color: #667eea !important;
}

.action-btn-edit:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  border-color: #5a6fd8 !important;
  color: #5a6fd8 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2) !important;
}

/* 分配按钮样式 - 橙色系 */
.action-btn-assign {
  background: rgba(255, 152, 0, 0.05) !important;
  color: #ff9800 !important;
}

.action-btn-assign:hover {
  background: rgba(255, 152, 0, 0.1) !important;
  border-color: #f57c00 !important;
  color: #f57c00 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2) !important;
}

/* 跟进按钮样式 - 绿色系 */
.action-btn-follow {
  background: rgba(76, 175, 80, 0.05) !important;
  color: #4caf50 !important;
}

.action-btn-follow:hover {
  background: rgba(76, 175, 80, 0.1) !important;
  border-color: #388e3c !important;
  color: #388e3c !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

/* 置顶按钮样式 - 蓝色系 */
.action-btn-top {
  background: rgba(24, 144, 255, 0.05) !important;
  color: #1890ff !important;
  min-width: 24px !important;
  padding: 4px !important;
}

.action-btn-top:hover {
  background: rgba(24, 144, 255, 0.1) !important;
  border-color: #1677ff !important;
  color: #1677ff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 3px 10px rgba(24, 144, 255, 0.3) !important;
}

/* 图标样式优化 */
.action-btn :deep(.n-icon) {
  font-size: 12px !important;
  margin-right: 4px !important;
}

.action-btn :deep(.n-button__content) {
  font-size: 12px !important;
  font-weight: 400 !important;
}

.action-btn :deep(.n-button__icon) {
  margin-right: 4px !important;
}

/* 减小边框粗细 */
.action-btn :deep(.n-button__border) {
  border-width: 1px !important;
}

.action-btn :deep(.n-button__state-border) {
  border-width: 1px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .follow-process-actions {
    gap: 4px;
  }
  
  .action-btn {
    max-width: 55px !important;
    font-size: 10px !important;
    padding: 1px 4px !important;
  }
  
  .action-btn :deep(.n-icon) {
    font-size: 10px !important;
  }
}

@media (max-width: 480px) {
  .follow-process-actions {
    flex-direction: column;
    gap: 3px;
    width: 100%;
  }
  
  .action-btn {
    width: 100%;
    max-width: none !important;
    font-size: 10px !important;
    padding: 1px 4px !important;
  }
}
</style>