<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    :title="isEdit ? '编辑角色' : '新建角色'"
    :style="{ width: '600px' }"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="80px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="角色名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入角色名称（英文标识符）"
          :disabled="isSystemRole"
        />
      </n-form-item>
      
      <n-form-item label="显示名称" path="display_name">
        <n-input
          v-model:value="formData.display_name"
          placeholder="请输入角色显示名称"
        />
      </n-form-item>
      
      <n-form-item label="角色描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入角色描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="状态" path="status">
        <n-switch
          v-model:value="formData.status"
          :disabled="isSystemRole"
        >
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </n-form-item>
      
      <n-form-item label="权限配置" path="permission_ids">
        <div class="permission-container">
          <n-space vertical size="medium">
            <div v-for="(permissions, module) in permissionsByModule" :key="module" class="permission-module">
              <div class="module-header">
                <n-checkbox
                  :checked="isModuleAllSelected(module)"
                  :indeterminate="isModuleIndeterminate(module)"
                  @update:checked="handleModuleSelect(module, $event)"
                >
                  <span class="module-name">{{ getModuleName(module) }}</span>
                </n-checkbox>
              </div>
              <div class="module-permissions">
                <n-checkbox-group v-model:value="formData.permission_ids">
                  <n-space>
                    <n-checkbox
                      v-for="permission in permissions"
                      :key="permission.id"
                      :value="permission.id"
                      :label="permission.name"
                    />
                  </n-space>
                </n-checkbox-group>
              </div>
            </div>
          </n-space>
        </div>
      </n-form-item>
    </n-form>
    
    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSwitch,
  NButton,
  NSpace,
  NCheckbox,
  NCheckboxGroup,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { useRoleStore } from '@/stores/modules/role'
import type { RoleWithPermissions, CreateRoleData, UpdateRoleData } from '@/api/roleService'

interface Props {
  show: boolean
  role?: RoleWithPermissions | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

const emit = defineEmits<Emits>()

const message = useMessage()
const roleStore = useRoleStore()
const formRef = ref<FormInst>()
const loading = ref(false)

// 表单数据
const formData = ref<CreateRoleData & { permission_ids: number[] }>({
  name: '',
  display_name: '',
  description: '',
  status: true,
  permission_ids: []
})

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.role)
const isSystemRole = computed(() => props.role?.is_system || false)

// 权限按模块分组
const permissionsByModule = computed(() => roleStore.permissionsByModule)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '角色名称只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 模块名称映射
const moduleNameMap: Record<string, string> = {
  customer: '客户管理',
  role: '角色管理',
  user: '用户管理',
  pool: '公海管理',
  marketing: '营销活动',
  meeting: '会议管理',
  analytics: '数据分析',
  system: '系统设置'
}

const getModuleName = (module: string) => {
  return moduleNameMap[module] || module
}

// 检查模块是否全选
const isModuleAllSelected = (module: string) => {
  const permissions = permissionsByModule.value[module] || []
  return permissions.length > 0 && permissions.every(p => formData.value.permission_ids.includes(p.id))
}

// 检查模块是否部分选中
const isModuleIndeterminate = (module: string) => {
  const permissions = permissionsByModule.value[module] || []
  const selectedCount = permissions.filter(p => formData.value.permission_ids.includes(p.id)).length
  return selectedCount > 0 && selectedCount < permissions.length
}

// 处理模块选择
const handleModuleSelect = (module: string, checked: boolean) => {
  const permissions = permissionsByModule.value[module] || []
  const permissionIds = permissions.map(p => p.id)
  
  if (checked) {
    // 添加该模块的所有权限
    permissionIds.forEach(id => {
      if (!formData.value.permission_ids.includes(id)) {
        formData.value.permission_ids.push(id)
      }
    })
  } else {
    // 移除该模块的所有权限
    formData.value.permission_ids = formData.value.permission_ids.filter(
      id => !permissionIds.includes(id)
    )
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    display_name: '',
    description: '',
    status: true,
    permission_ids: []
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.role) {
    formData.value = {
      name: props.role.name,
      display_name: props.role.display_name || '',
      description: props.role.description || '',
      status: props.role.status,
      permission_ids: props.role.permissions?.map(p => p.id) || []
    }
  } else {
    resetForm()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    const submitData: CreateRoleData | UpdateRoleData = {
      name: formData.value.name,
      display_name: formData.value.display_name,
      description: formData.value.description,
      status: formData.value.status,
      permission_ids: formData.value.permission_ids
    }
    
    if (isEdit.value && props.role) {
      await roleStore.updateRole(props.role.id, submitData as UpdateRoleData)
      message.success('更新角色成功')
    } else {
      await roleStore.createRole(submitData as CreateRoleData)
      message.success('创建角色成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (show) => {
    if (show) {
      nextTick(() => {
        initFormData()
      })
    }
  }
)
</script>

<style scoped>
.permission-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  padding: 16px;
}

.permission-module {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.permission-module:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.module-header {
  margin-bottom: 8px;
}

.module-name {
  font-weight: 600;
  color: #333;
}

.module-permissions {
  margin-left: 24px;
}
</style>