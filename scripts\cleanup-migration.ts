import { MySQLManager } from '../src/database/MySQLManager';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import path from 'path';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 清理配置接口
interface CleanupConfig {
  cleanupMode: 'all' | 'failed' | 'partial' | 'logs';
  dryRun: boolean;
  keepLogs: boolean;
  batchSize: number;
  confirmBeforeDelete: boolean;
}

// 清理统计
interface CleanupStats {
  tablesProcessed: number;
  recordsDeleted: number;
  logsDeleted: number;
  errorsEncountered: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
}

/**
 * 数据清理管理器
 */
class DataCleanupManager {
  private mysql: MySQLManager;
  private supabase: any;
  private config: CleanupConfig;
  private stats: CleanupStats;

  constructor(config: CleanupConfig) {
    this.config = config;
    this.stats = {
      tablesProcessed: 0,
      recordsDeleted: 0,
      logsDeleted: 0,
      errorsEncountered: 0,
      startTime: new Date()
    };

    // 初始化MySQL管理器
    const mysqlConfig = {
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'workchat_admin',
      connectionLimit: 10
    };
    
    this.mysql = new MySQLManager(mysqlConfig);

    // 初始化Supabase客户端（用于对比验证）
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
    }
  }

  /**
   * 初始化清理环境
   */
  async initialize(): Promise<void> {
    console.log('🧹 初始化数据清理环境...');
    
    try {
      await this.mysql.initialize();
      console.log('✅ 清理环境初始化完成');
    } catch (error) {
      console.error('❌ 清理环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行数据清理
   */
  async cleanup(): Promise<void> {
    console.log(`🧹 开始数据清理 (模式: ${this.config.cleanupMode})`);
    
    if (this.config.dryRun) {
      console.log('🔍 运行模式: 预览模式 (不会实际删除数据)');
    }

    try {
      switch (this.config.cleanupMode) {
        case 'all':
          await this.cleanupAllData();
          break;
        case 'failed':
          await this.cleanupFailedMigrations();
          break;
        case 'partial':
          await this.cleanupPartialMigrations();
          break;
        case 'logs':
          await this.cleanupMigrationLogs();
          break;
        default:
          throw new Error(`未知的清理模式: ${this.config.cleanupMode}`);
      }

      this.stats.endTime = new Date();
      this.stats.duration = this.stats.endTime.getTime() - this.stats.startTime.getTime();

      await this.generateCleanupReport();
      console.log('🎉 数据清理完成!');
      
    } catch (error) {
      console.error('❌ 数据清理失败:', error);
      throw error;
    }
  }

  /**
   * 清理所有迁移数据
   */
  private async cleanupAllData(): Promise<void> {
    console.log('🗑️ 清理所有迁移数据...');
    
    if (this.config.confirmBeforeDelete && !this.config.dryRun) {
      const confirmation = await this.confirmDeletion('所有迁移数据');
      if (!confirmation) {
        console.log('❌ 用户取消操作');
        return;
      }
    }

    // 获取所有业务表
    const businessTables = [
      'users', 'roles', 'permissions', 'role_permissions', 'user_roles',
      'option_categories', 'option_items', 'customers', 'customer_follow_records',
      'marketing_campaigns', 'campaign_participants', 'campaign_shares',
      'meetings', 'meeting_participants', 'pool_rules', 'customer_behaviors',
      'wechat_customer_tracking', 'sales_funnel_stats', 'customer_value_analysis',
      'follow_ups', 'public_pool'
    ];

    // 按依赖关系逆序删除
    const deletionOrder = [...businessTables].reverse();

    for (const tableName of deletionOrder) {
      await this.cleanupTable(tableName);
    }

    // 清理迁移日志（如果不保留）
    if (!this.config.keepLogs) {
      await this.cleanupMigrationLogs();
    }
  }

  /**
   * 清理失败的迁移数据
   */
  private async cleanupFailedMigrations(): Promise<void> {
    console.log('🗑️ 清理失败的迁移数据...');
    
    try {
      // 查询失败的迁移记录
      const failedMigrations = await this.mysql.query(
        `SELECT DISTINCT migration_id, table_name 
         FROM migration_logs 
         WHERE status = 'failed'
         ORDER BY start_time DESC`
      );

      if (!failedMigrations.success || !failedMigrations.data || failedMigrations.data.length === 0) {
        console.log('ℹ️ 未找到失败的迁移记录');
        return;
      }

      console.log(`📋 找到 ${failedMigrations.data.length} 个失败的迁移记录`);

      // 按迁移ID分组
      const migrationGroups = new Map<string, string[]>();
      for (const record of failedMigrations.data) {
        const migrationId = record.migration_id;
        if (!migrationGroups.has(migrationId)) {
          migrationGroups.set(migrationId, []);
        }
        migrationGroups.get(migrationId)!.push(record.table_name);
      }

      // 清理每个失败的迁移
      for (const [migrationId, tables] of migrationGroups) {
        console.log(`🧹 清理失败迁移 ${migrationId} 的表: ${tables.join(', ')}`);
        
        for (const tableName of tables) {
          await this.cleanupTableByMigration(tableName, migrationId);
        }
      }

    } catch (error) {
      console.error('❌ 清理失败迁移数据时出错:', error);
      this.stats.errorsEncountered++;
    }
  }

  /**
   * 清理部分迁移数据
   */
  private async cleanupPartialMigrations(): Promise<void> {
    console.log('🗑️ 清理部分迁移数据...');
    
    try {
      // 查询未完成的迁移
      const incompleteMigrations = await this.mysql.query(
        `SELECT migration_id, table_name, records_count
         FROM migration_logs 
         WHERE status = 'started' 
         AND end_time IS NULL
         ORDER BY start_time DESC`
      );

      if (!incompleteMigrations.success || !incompleteMigrations.data || incompleteMigrations.data.length === 0) {
        console.log('ℹ️ 未找到未完成的迁移记录');
        return;
      }

      console.log(`📋 找到 ${incompleteMigrations.data.length} 个未完成的迁移记录`);

      for (const record of incompleteMigrations.data) {
        console.log(`🧹 清理未完成迁移: ${record.table_name} (${record.migration_id})`);
        await this.cleanupTableByMigration(record.table_name, record.migration_id);
      }

    } catch (error) {
      console.error('❌ 清理部分迁移数据时出错:', error);
      this.stats.errorsEncountered++;
    }
  }

  /**
   * 清理迁移日志
   */
  private async cleanupMigrationLogs(): Promise<void> {
    console.log('🗑️ 清理迁移日志...');
    
    try {
      // 获取日志总数
      const logCount = await this.mysql.getTableCount('migration_logs');
      
      if (logCount === 0) {
        console.log('ℹ️ 没有迁移日志需要清理');
        return;
      }

      console.log(`📋 找到 ${logCount} 条迁移日志`);

      if (this.config.confirmBeforeDelete && !this.config.dryRun) {
        const confirmation = await this.confirmDeletion(`${logCount} 条迁移日志`);
        if (!confirmation) {
          console.log('❌ 用户取消操作');
          return;
        }
      }

      if (!this.config.dryRun) {
        const result = await this.mysql.query('DELETE FROM migration_logs');
        
        if (result.success) {
          this.stats.logsDeleted = logCount;
          console.log(`✅ 已删除 ${logCount} 条迁移日志`);
        } else {
          console.error(`❌ 删除迁移日志失败: ${result.error}`);
          this.stats.errorsEncountered++;
        }
      } else {
        console.log(`🔍 预览: 将删除 ${logCount} 条迁移日志`);
      }

    } catch (error) {
      console.error('❌ 清理迁移日志时出错:', error);
      this.stats.errorsEncountered++;
    }
  }

  /**
   * 清理单个表
   */
  private async cleanupTable(tableName: string): Promise<void> {
    console.log(`🧹 清理表: ${tableName}`);
    
    try {
      // 检查表是否存在
      const tableExists = await this.mysql.tableExists(tableName);
      
      if (!tableExists) {
        console.log(`ℹ️ 表 ${tableName} 不存在，跳过`);
        return;
      }

      // 获取记录数
      const recordCount = await this.mysql.getTableCount(tableName);
      
      if (recordCount === 0) {
        console.log(`ℹ️ 表 ${tableName} 无数据，跳过`);
        return;
      }

      console.log(`📊 表 ${tableName} 有 ${recordCount} 条记录`);

      if (!this.config.dryRun) {
        // 禁用外键检查
        await this.mysql.query('SET FOREIGN_KEY_CHECKS = 0');
        
        const result = await this.mysql.query(`DELETE FROM ${tableName}`);
        
        // 重新启用外键检查
        await this.mysql.query('SET FOREIGN_KEY_CHECKS = 1');
        
        if (result.success) {
          this.stats.recordsDeleted += recordCount;
          console.log(`✅ 已清理表 ${tableName}: ${recordCount} 条记录`);
        } else {
          console.error(`❌ 清理表 ${tableName} 失败: ${result.error}`);
          this.stats.errorsEncountered++;
        }
      } else {
        console.log(`🔍 预览: 将删除表 ${tableName} 的 ${recordCount} 条记录`);
      }

      this.stats.tablesProcessed++;

    } catch (error) {
      console.error(`❌ 清理表 ${tableName} 时出错:`, error);
      this.stats.errorsEncountered++;
    }
  }

  /**
   * 根据迁移ID清理表数据
   */
  private async cleanupTableByMigration(tableName: string, migrationId: string): Promise<void> {
    console.log(`🧹 清理表 ${tableName} 的迁移 ${migrationId} 数据`);
    
    try {
      // 这里可以根据具体需求实现更精确的清理逻辑
      // 例如，如果表中有migration_id字段，可以只删除特定迁移的数据
      
      // 目前简化为清理整个表
      await this.cleanupTable(tableName);
      
    } catch (error) {
      console.error(`❌ 清理表 ${tableName} 迁移数据时出错:`, error);
      this.stats.errorsEncountered++;
    }
  }

  /**
   * 确认删除操作
   */
  private async confirmDeletion(target: string): Promise<boolean> {
    console.log(`⚠️ 即将删除: ${target}`);
    console.log('⚠️ 此操作不可逆，请确认是否继续？');
    console.log('输入 "yes" 确认，其他任何输入将取消操作:');
    
    // 在实际环境中，这里应该使用适当的用户输入方法
    // 这里简化为返回false，需要手动修改为true来确认
    return false;
  }

  /**
   * 验证清理结果
   */
  private async validateCleanup(): Promise<void> {
    console.log('🔍 验证清理结果...');
    
    try {
      const businessTables = [
        'users', 'roles', 'permissions', 'role_permissions', 'user_roles',
        'option_categories', 'option_items', 'customers', 'customer_follow_records',
        'marketing_campaigns', 'campaign_participants', 'campaign_shares',
        'meetings', 'meeting_participants', 'pool_rules', 'customer_behaviors',
        'wechat_customer_tracking', 'sales_funnel_stats', 'customer_value_analysis',
        'follow_ups', 'public_pool'
      ];

      let totalRecords = 0;
      
      for (const tableName of businessTables) {
        const tableExists = await this.mysql.tableExists(tableName);
        if (tableExists) {
          const count = await this.mysql.getTableCount(tableName);
          totalRecords += count;
          
          if (count > 0) {
            console.log(`📊 表 ${tableName}: ${count} 条记录`);
          }
        }
      }

      if (totalRecords === 0) {
        console.log('✅ 所有业务表已清空');
      } else {
        console.log(`ℹ️ 剩余记录总数: ${totalRecords}`);
      }

    } catch (error) {
      console.error('❌ 验证清理结果时出错:', error);
    }
  }

  /**
   * 生成清理报告
   */
  private async generateCleanupReport(): Promise<void> {
    console.log('📋 生成清理报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      config: this.config,
      stats: this.stats,
      summary: {
        success: this.stats.errorsEncountered === 0,
        tablesProcessed: this.stats.tablesProcessed,
        recordsDeleted: this.stats.recordsDeleted,
        logsDeleted: this.stats.logsDeleted,
        errorsEncountered: this.stats.errorsEncountered,
        duration: this.stats.duration
      }
    };
    
    // 保存报告到文件
    const reportPath = path.join(__dirname, '..', 'docs', 'data-migration', `cleanup-report-${Date.now()}.json`);
    
    try {
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 清理报告已保存: ${reportPath}`);
    } catch (error) {
      console.error('保存清理报告失败:', error);
    }
    
    // 打印摘要
    console.log('\n📊 清理摘要:');
    console.log(`   处理表数: ${this.stats.tablesProcessed}`);
    console.log(`   删除记录数: ${this.stats.recordsDeleted}`);
    console.log(`   删除日志数: ${this.stats.logsDeleted}`);
    console.log(`   错误数: ${this.stats.errorsEncountered}`);
    console.log(`   耗时: ${this.stats.duration}ms`);
    
    if (this.config.dryRun) {
      console.log('\n🔍 注意: 这是预览模式，没有实际删除数据');
    }
  }

  /**
   * 清理资源
   */
  async close(): Promise<void> {
    console.log('🧹 清理资源...');
    
    try {
      await this.mysql.close();
      console.log('✅ 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error);
    }
  }
}

/**
 * 主清理函数
 */
async function main() {
  const config: CleanupConfig = {
    cleanupMode: (process.env.CLEANUP_MODE as any) || 'failed',
    dryRun: process.env.CLEANUP_DRY_RUN === 'true',
    keepLogs: process.env.CLEANUP_KEEP_LOGS === 'true',
    batchSize: parseInt(process.env.CLEANUP_BATCH_SIZE || '1000'),
    confirmBeforeDelete: process.env.CLEANUP_CONFIRM !== 'false'
  };
  
  console.log('🧹 启动数据清理程序');
  console.log('配置:', config);
  
  const cleanupManager = new DataCleanupManager(config);
  
  try {
    await cleanupManager.initialize();
    await cleanupManager.cleanup();
    console.log('🎉 数据清理成功完成!');
  } catch (error) {
    console.error('❌ 数据清理失败:', error);
    process.exit(1);
  } finally {
    await cleanupManager.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { DataCleanupManager, CleanupConfig };