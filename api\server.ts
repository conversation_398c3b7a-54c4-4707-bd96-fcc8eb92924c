/**
 * local server entry file, for local development
 */
import app from './app.js';

/**
 * start server with port
 */
const PORT = process.env.PORT || 3001;

const server = app.listen(PORT, () => {
  console.log(`API服务器运行在端口 ${PORT || 9000}`);
  console.log('API服务已启动，使用MySQL数据库连接');
  console.log(`服务启动时间: ${new Date().toLocaleString()}`);
  console.log('使用端口:', process.env.PORT || 3001);
console.log('环境变量PORT:', process.env.PORT);
  console.log('服务状态: 正常运行');
  console.log(`当前端口: ${PORT}`);
  console.log('环境变量PORT:', process.env.PORT);
  console.log('服务重启成功 - 端口已释放');
  // 启动到8080端口
});

/**
 * close server
 */
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT signal received');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export default app;