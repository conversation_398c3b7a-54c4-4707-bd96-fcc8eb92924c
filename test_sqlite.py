#!/usr/bin/env python3
"""简单的SQLite数据库测试脚本"""

import asyncio
import logging
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_sqlite_connection():
    """测试SQLite数据库连接"""
    try:
        # 确保使用SQLite配置
        os.environ['DATABASE_DATABASE'] = 'test_yysh.db'
        
        from app.config import settings
        from app.database import test_database_connection, create_tables, drop_tables
        
        logger.info(f"数据库URL: {settings.database.async_url}")
        logger.info("开始测试SQLite数据库连接...")
        
        # 测试连接
        is_connected = await test_database_connection()
        
        if is_connected:
            logger.info("✅ SQLite数据库连接成功")
            
            # 测试表创建
            logger.info("开始测试表创建...")
            await create_tables()
            logger.info("✅ 表创建成功")
            
            # 测试基础查询
            from app.database import AsyncSessionLocal
            from sqlalchemy import text
            async with AsyncSessionLocal() as session:
                result = await session.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                logger.info(f"✅ 基础查询成功: {row}")
            
            return True
        else:
            logger.error("❌ SQLite数据库连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ SQLite数据库测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("开始SQLite数据库测试")
    logger.info("=" * 50)
    
    success = await test_sqlite_connection()
    
    if success:
        logger.info("🎉 所有测试通过！")
    else:
        logger.error("❌ 测试失败")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())