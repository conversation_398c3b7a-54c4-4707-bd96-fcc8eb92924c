<template>
  <div class="data-analytics">

    <!-- 时间范围选择 -->
    <n-card class="time-selector">
      <div class="selector-content">
        <div class="quick-selectors">
          <n-button
            v-for="period in quickPeriods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            size="small"
            @click="selectPeriod(period.value)"
          >
            {{ period.label }}
          </n-button>
        </div>
        <div class="custom-selector">
          <n-date-picker
            v-model:value="customDateRange"
            type="daterange"
            clearable
            @update:value="onCustomDateChange"
          />
        </div>
      </div>
    </n-card>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <div class="metrics-grid">
        <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-icon">
            <n-icon size="24" :color="metric.color">
              <component :is="metric.icon" />
            </n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-change" :class="getChangeClass(metric.change)">
              <n-icon size="14">
                <component :is="getChangeIcon(metric.change)" />
              </n-icon>
              <span>{{ Math.abs(metric.change) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分析模块 -->
    <div class="analysis-modules">
      <div class="modules-grid">
        <!-- 客户价值分析 -->
        <n-card title="客户价值分析" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openCustomerValueAnalysis">
              <template #icon>
                <n-icon><AnalyticsOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="value-chart">
              <div ref="customerValueChartRef" class="chart"></div>
            </div>
            <div class="value-summary">
              <div class="summary-item">
                <span class="summary-label">高价值客户：</span>
                <span class="summary-value">{{ customerValueSummary.highValue }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">中价值客户：</span>
                <span class="summary-value">{{ customerValueSummary.mediumValue }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">低价值客户：</span>
                <span class="summary-value">{{ customerValueSummary.lowValue }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">平均客户价值：</span>
                <span class="summary-value">¥{{ customerValueSummary.avgValue }}</span>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 销售漏斗分析 -->
        <n-card title="销售漏斗分析" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openSalesFunnelAnalysis">
              <template #icon>
                <n-icon><FunnelOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="funnel-chart">
              <div ref="salesFunnelChartRef" class="chart"></div>
            </div>
            <div class="funnel-metrics">
              <div class="metric-row">
                <span class="metric-label">线索转化率：</span>
                <span class="metric-value">{{ funnelMetrics.leadConversion }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">机会转化率：</span>
                <span class="metric-value">{{ funnelMetrics.opportunityConversion }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">成交转化率：</span>
                <span class="metric-value">{{ funnelMetrics.dealConversion }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">整体转化率：</span>
                <span class="metric-value">{{ funnelMetrics.overallConversion }}%</span>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 转化率统计 -->
        <n-card title="转化率统计" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openConversionAnalysis">
              <template #icon>
                <n-icon><TrendingUpOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="conversion-chart">
              <div ref="conversionChartRef" class="chart"></div>
            </div>
            <div class="conversion-stats">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ conversionStats.visitToLead }}%</div>
                  <div class="stat-label">访问转线索</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ conversionStats.leadToOpportunity }}%</div>
                  <div class="stat-label">线索转机会</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ conversionStats.opportunityToDeal }}%</div>
                  <div class="stat-label">机会转成交</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ conversionStats.dealToRepeat }}%</div>
                  <div class="stat-label">成交转复购</div>
                </div>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 业绩分析 -->
        <n-card title="业绩分析" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openPerformanceAnalysis">
              <template #icon>
                <n-icon><BarChartOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="performance-chart">
              <div ref="performanceChartRef" class="chart"></div>
            </div>
            <div class="performance-summary">
              <div class="summary-grid">
                <div class="summary-card">
                  <div class="summary-title">本月业绩</div>
                  <div class="summary-amount">¥{{ performanceSummary.thisMonth }}</div>
                  <div class="summary-change positive">+{{ performanceSummary.monthlyGrowth }}%</div>
                </div>
                <div class="summary-card">
                  <div class="summary-title">季度业绩</div>
                  <div class="summary-amount">¥{{ performanceSummary.thisQuarter }}</div>
                  <div class="summary-change positive">+{{ performanceSummary.quarterlyGrowth }}%</div>
                </div>
                <div class="summary-card">
                  <div class="summary-title">年度业绩</div>
                  <div class="summary-amount">¥{{ performanceSummary.thisYear }}</div>
                  <div class="summary-change positive">+{{ performanceSummary.yearlyGrowth }}%</div>
                </div>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 客户行为分析 -->
        <n-card title="客户行为分析" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openBehaviorAnalysis">
              <template #icon>
                <n-icon><PeopleOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="behavior-chart">
              <div ref="behaviorChartRef" class="chart"></div>
            </div>
            <div class="behavior-insights">
              <div class="insight-item">
                <div class="insight-icon">
                  <n-icon color="#2080f0"><TimeOutline /></n-icon>
                </div>
                <div class="insight-content">
                  <div class="insight-title">平均访问时长</div>
                  <div class="insight-value">{{ behaviorInsights.avgVisitDuration }}</div>
                </div>
              </div>
              <div class="insight-item">
                <div class="insight-icon">
                  <n-icon color="#18a058"><EyeOutline /></n-icon>
                </div>
                <div class="insight-content">
                  <div class="insight-title">页面浏览深度</div>
                  <div class="insight-value">{{ behaviorInsights.avgPageDepth }}</div>
                </div>
              </div>
              <div class="insight-item">
                <div class="insight-icon">
                  <n-icon color="#f0a020"><RepeatOutline /></n-icon>
                </div>
                <div class="insight-content">
                  <div class="insight-title">回访率</div>
                  <div class="insight-value">{{ behaviorInsights.returnRate }}%</div>
                </div>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 渠道效果分析 -->
        <n-card title="渠道效果分析" class="module-card">
          <template #header-extra>
            <n-button size="small" @click="openChannelAnalysis">
              <template #icon>
                <n-icon><GitNetworkOutline /></n-icon>
              </template>
              详细分析
            </n-button>
          </template>
          <div class="module-content">
            <div class="channel-chart">
              <div ref="channelChartRef" class="chart"></div>
            </div>
            <div class="channel-ranking">
              <div class="ranking-header">
                <span>渠道排名</span>
                <span>转化率</span>
                <span>成本</span>
              </div>
              <div class="ranking-item" v-for="(channel, index) in channelRanking" :key="channel.name">
                <div class="ranking-position">{{ index + 1 }}</div>
                <div class="ranking-name">{{ channel.name }}</div>
                <div class="ranking-conversion">{{ channel.conversion }}%</div>
                <div class="ranking-cost">¥{{ channel.cost }}</div>
              </div>
            </div>
          </div>
        </n-card>
      </div>
    </div>

    <!-- 自定义报表模态框 -->
    <n-modal v-model:show="showCustomReport" title="自定义报表">
      <n-card style="width: 800px">
        <CustomReportBuilder @close="showCustomReport = false" />
      </n-card>
    </n-modal>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  NCard, NButton, NIcon, NDatePicker, NModal, NDrawer, NDrawerContent,
  useMessage
} from 'naive-ui'
import {
  RefreshOutline, DocumentTextOutline, AnalyticsOutline, FunnelOutline,
  TrendingUpOutline, BarChartOutline, PeopleOutline, GitNetworkOutline,
  TimeOutline, EyeOutline, RepeatOutline, ArrowUpOutline, ArrowDownOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const message = useMessage()
const router = useRouter()
const loading = ref(false)
const showCustomReport = ref(false)
const selectedPeriod = ref('7d')
const customDateRange = ref<[number, number] | null>(null)

// 快速时间选择
const quickPeriods = [
  { label: '今天', value: '1d' },
  { label: '近7天', value: '7d' },
  { label: '近30天', value: '30d' },
  { label: '近90天', value: '90d' },
  { label: '本年', value: 'year' }
]

// 核心指标
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '总收入',
    value: 2580000,
    type: 'currency',
    change: 12.5,
    icon: BarChartOutline,
    color: '#2080f0'
  },
  {
    key: 'customers',
    label: '客户总数',
    value: 1250,
    type: 'number',
    change: 8.3,
    icon: PeopleOutline,
    color: '#18a058'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: 15.8,
    type: 'percentage',
    change: -2.1,
    icon: TrendingUpOutline,
    color: '#f0a020'
  },
  {
    key: 'avgDeal',
    label: '平均成交额',
    value: 25800,
    type: 'currency',
    change: 5.7,
    icon: AnalyticsOutline,
    color: '#d03050'
  }
])

// 客户价值分析数据
const customerValueSummary = ref({
  highValue: 156,
  mediumValue: 423,
  lowValue: 671,
  avgValue: '12,580'
})

// 销售漏斗指标
const funnelMetrics = ref({
  leadConversion: 25.6,
  opportunityConversion: 45.2,
  dealConversion: 68.9,
  overallConversion: 15.8
})

// 转化率统计
const conversionStats = ref({
  visitToLead: 12.5,
  leadToOpportunity: 25.6,
  opportunityToDeal: 45.2,
  dealToRepeat: 32.1
})

// 业绩汇总
const performanceSummary = ref({
  thisMonth: '580,000',
  monthlyGrowth: 12.5,
  thisQuarter: '1,650,000',
  quarterlyGrowth: 18.3,
  thisYear: '5,280,000',
  yearlyGrowth: 25.7
})

// 客户行为洞察
const behaviorInsights = ref({
  avgVisitDuration: '5分32秒',
  avgPageDepth: '3.2页',
  returnRate: 68.5
})

// 渠道排名
const channelRanking = ref([
  { name: '搜索引擎', conversion: 25.6, cost: 1250 },
  { name: '社交媒体', conversion: 18.9, cost: 890 },
  { name: '直接访问', conversion: 32.1, cost: 0 },
  { name: '邮件营销', conversion: 15.3, cost: 560 },
  { name: '推荐链接', conversion: 22.7, cost: 780 }
])

// 图表引用
const customerValueChartRef = ref<HTMLElement>()
const salesFunnelChartRef = ref<HTMLElement>()
const conversionChartRef = ref<HTMLElement>()
const performanceChartRef = ref<HTMLElement>()
const behaviorChartRef = ref<HTMLElement>()
const channelChartRef = ref<HTMLElement>()

// 方法
const formatMetricValue = (value: number, type: string) => {
  switch (type) {
    case 'currency':
      return `¥${value.toLocaleString()}`
    case 'percentage':
      return `${value}%`
    case 'number':
    default:
      return value.toLocaleString()
  }
}

const getChangeClass = (change: number) => {
  return change >= 0 ? 'positive' : 'negative'
}

const getChangeIcon = (change: number) => {
  return change >= 0 ? ArrowUpOutline : ArrowDownOutline
}

const selectPeriod = (period: string) => {
  selectedPeriod.value = period
  customDateRange.value = null
  refreshData()
}

const onCustomDateChange = (value: [number, number] | null) => {
  if (value) {
    selectedPeriod.value = 'custom'
    refreshData()
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    await nextTick()
    initCharts()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const openCustomerValueAnalysis = () => {
  router.push('/analytics/customer-value')
}

const openSalesFunnelAnalysis = () => {
  router.push('/analytics/sales-funnel')
}

const openConversionAnalysis = () => {
  router.push('/analytics/conversion')
}

const openPerformanceAnalysis = () => {
  router.push('/analytics/performance')
}

const openBehaviorAnalysis = () => {
  // TODO: 实现客户行为分析页面
  message.info('客户行为分析功能开发中')
}

const openChannelAnalysis = () => {
  // TODO: 实现渠道效果分析页面
  message.info('渠道效果分析功能开发中')
}

const initCharts = () => {
  // 客户价值分布图
  if (customerValueChartRef.value) {
    const chart = echarts.init(customerValueChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 156, name: '高价值客户', itemStyle: { color: '#d03050' } },
          { value: 423, name: '中价值客户', itemStyle: { color: '#f0a020' } },
          { value: 671, name: '低价值客户', itemStyle: { color: '#18a058' } }
        ]
      }]
    })
  }

  // 销售漏斗图
  if (salesFunnelChartRef.value) {
    const chart = echarts.init(salesFunnelChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'funnel',
        data: [
          { value: 1000, name: '潜在客户' },
          { value: 800, name: '意向客户' },
          { value: 600, name: '商机客户' },
          { value: 400, name: '成交客户' }
        ]
      }]
    })
  }

  // 转化率趋势图
  if (conversionChartRef.value) {
    const chart = echarts.init(conversionChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [12.5, 14.2, 13.8, 15.6, 16.3, 15.8],
        smooth: true,
        itemStyle: { color: '#2080f0' }
      }]
    })
  }

  // 业绩趋势图
  if (performanceChartRef.value) {
    const chart = echarts.init(performanceChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [450000, 520000, 480000, 580000, 620000, 580000],
        itemStyle: { color: '#18a058' }
      }]
    })
  }

  // 客户行为热力图
  if (behaviorChartRef.value) {
    const chart = echarts.init(behaviorChartRef.value)
    const hours = Array.from({ length: 24 }, (_, i) => i + 'h')
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const data = []
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 24; j++) {
        data.push([j, i, Math.floor(Math.random() * 100)])
      }
    }
    
    chart.setOption({
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          return `${days[params.data[1]]} ${hours[params.data[0]]}: ${params.data[2]}次访问`
        }
      },
      grid: { height: '50%', top: '10%' },
      xAxis: {
        type: 'category',
        data: hours,
        splitArea: { show: true }
      },
      yAxis: {
        type: 'category',
        data: days,
        splitArea: { show: true }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        type: 'heatmap',
        data: data,
        label: { show: false }
      }]
    })
  }

  // 渠道效果对比图
  if (channelChartRef.value) {
    const chart = echarts.init(channelChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['转化率', '成本'] },
      xAxis: {
        type: 'category',
        data: ['搜索引擎', '社交媒体', '直接访问', '邮件营销', '推荐链接']
      },
      yAxis: [{ type: 'value' }, { type: 'value' }],
      series: [
        {
          name: '转化率',
          type: 'bar',
          data: [25.6, 18.9, 32.1, 15.3, 22.7],
          itemStyle: { color: '#2080f0' }
        },
        {
          name: '成本',
          type: 'line',
          yAxisIndex: 1,
          data: [1250, 890, 0, 560, 780],
          itemStyle: { color: '#f0a020' }
        }
      ]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.data-analytics {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.page-desc {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.time-selector {
  margin-bottom: 24px;
}

.selector-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.quick-selectors {
  display: flex;
  gap: 8px;
}

.custom-selector {
  flex-shrink: 0;
}

.metrics-overview {
  margin-bottom: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.analysis-modules {
  margin-bottom: 24px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.module-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.module-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart {
  width: 100%;
  height: 200px;
}

.value-summary,
.funnel-metrics,
.conversion-stats,
.performance-summary,
.behavior-insights,
.channel-ranking {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item,
.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.summary-item:last-child,
.metric-row:last-child {
  border-bottom: none;
}

.summary-label,
.metric-label {
  color: #666;
  font-size: 14px;
}

.summary-value,
.metric-value {
  font-weight: 500;
  color: #1a1a1a;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #2080f0;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.summary-card {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
}

.summary-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.summary-amount {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.summary-change {
  font-size: 12px;
  font-weight: 500;
}

.summary-change.positive {
  color: #18a058;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.insight-item:last-child {
  border-bottom: none;
}

.insight-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.insight-value {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
}

.ranking-header {
  display: grid;
  grid-template-columns: 40px 1fr 80px 80px;
  gap: 12px;
  padding: 8px 0;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 8px;
}

.ranking-item {
  display: grid;
  grid-template-columns: 40px 1fr 80px 80px;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-position {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2080f0;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.ranking-name {
  font-weight: 500;
  color: #1a1a1a;
}

.ranking-conversion,
.ranking-cost {
  font-size: 14px;
  color: #666;
  text-align: center;
}
</style>