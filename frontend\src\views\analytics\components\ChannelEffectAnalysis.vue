<template>
  <div class="channel-effect-analysis">
    <!-- 分析配置 -->
    <div class="analysis-config">
      <div class="config-row">
        <div class="config-item">
          <label>分析维度</label>
          <n-select
            v-model:value="config.dimension"
            :options="dimensionOptions"
            placeholder="选择分析维度"
          />
        </div>
        <div class="config-item">
          <label>时间范围</label>
          <n-date-picker
            v-model:value="config.dateRange"
            type="daterange"
            clearable
          />
        </div>
        <div class="config-item">
          <label>渠道类型</label>
          <n-select
            v-model:value="config.channelTypes"
            :options="channelTypeOptions"
            placeholder="选择渠道类型"
            multiple
            clearable
          />
        </div>
        <div class="config-item">
          <label>对比分析</label>
          <n-select
            v-model:value="config.compareWith"
            :options="compareOptions"
            placeholder="选择对比基准"
          />
        </div>
      </div>
      <div class="config-actions">
        <n-button type="primary" @click="runAnalysis" :loading="loading">
          <template #icon>
            <n-icon><AnalyticsOutline /></n-icon>
          </template>
          开始分析
        </n-button>
        <n-button @click="exportReport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报告
        </n-button>
      </div>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="!loading">
      <!-- 渠道概览 -->
      <div class="channel-overview">
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#2080f0"><TrendingUpOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ formatNumber(overview.totalTraffic) }}</div>
            <div class="card-label">总流量</div>
            <div class="card-change positive">+{{ overview.trafficGrowth }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#18a058"><AtOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.avgConversionRate }}%</div>
            <div class="card-label">平均转化率</div>
            <div class="card-change positive">+{{ overview.conversionGrowth }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#f0a020"><CashOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.avgCost }}</div>
            <div class="card-label">平均获客成本</div>
            <div class="card-change negative">-{{ overview.costReduction }}%</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <n-icon size="32" color="#d03050"><StatsChartOutline /></n-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.avgROI }}%</div>
            <div class="card-label">平均ROI</div>
            <div class="card-change positive">+{{ overview.roiGrowth }}%</div>
          </div>
        </div>
      </div>

      <!-- 渠道效果对比 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>渠道效果对比</h3>
          <div class="chart-controls">
            <n-radio-group v-model:value="compareMetric" size="small">
              <n-radio-button value="traffic">流量</n-radio-button>
              <n-radio-button value="conversion">转化率</n-radio-button>
              <n-radio-button value="cost">成本</n-radio-button>
              <n-radio-button value="roi">ROI</n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="chart-container">
          <div ref="comparisonChartRef" class="chart"></div>
        </div>
      </div>

      <!-- 渠道详细分析 -->
      <div class="analysis-row">
        <div class="analysis-section">
          <div class="section-header">
            <h3>渠道流量分析</h3>
          </div>
          <div class="chart-container">
            <div ref="trafficChartRef" class="chart"></div>
          </div>
          <div class="channel-stats">
            <div class="stat-item" v-for="channel in channelStats" :key="channel.name">
              <div class="stat-header">
                <div class="stat-icon" :style="{ backgroundColor: channel.color }">
                  <n-icon size="16" color="white"><component :is="channel.icon" /></n-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-name">{{ channel.name }}</div>
                  <div class="stat-type">{{ channel.type }}</div>
                </div>
                <div class="stat-trend" :class="channel.trend">
                  <n-icon size="12"><component :is="channel.trendIcon" /></n-icon>
                  <span>{{ channel.trendValue }}%</span>
                </div>
              </div>
              <div class="stat-metrics">
                <div class="metric">
                  <span class="metric-label">流量</span>
                  <span class="metric-value">{{ formatNumber(channel.traffic) }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">转化</span>
                  <span class="metric-value">{{ channel.conversions }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">转化率</span>
                  <span class="metric-value">{{ channel.conversionRate }}%</span>
                </div>
                <div class="metric">
                  <span class="metric-label">成本</span>
                  <span class="metric-value">¥{{ channel.cost }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-section">
          <div class="section-header">
            <h3>转化漏斗分析</h3>
          </div>
          <div class="funnel-analysis">
            <div class="funnel-chart">
              <div ref="funnelChartRef" class="chart"></div>
            </div>
            <div class="funnel-insights">
              <div class="insight-item">
                <div class="insight-title">转化阶段</div>
                <div class="insight-content">
                  <div class="funnel-stage" v-for="stage in funnelStages" :key="stage.name">
                    <div class="stage-info">
                      <div class="stage-name">{{ stage.name }}</div>
                      <div class="stage-count">{{ formatNumber(stage.count) }}人</div>
                    </div>
                    <div class="stage-rate">{{ stage.rate }}%</div>
                  </div>
                </div>
              </div>
              <div class="insight-item">
                <div class="insight-title">优化建议</div>
                <div class="insight-content">
                  <div class="optimization-tip" v-for="tip in optimizationTips" :key="tip.stage">
                    <div class="tip-stage">{{ tip.stage }}</div>
                    <div class="tip-content">{{ tip.suggestion }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成本效益分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>成本效益分析</h3>
        </div>
        <div class="cost-analysis">
          <div class="cost-charts">
            <div class="chart-item">
              <div class="chart-title">获客成本趋势</div>
              <div class="chart-container">
                <div ref="costTrendChartRef" class="chart"></div>
              </div>
            </div>
            <div class="chart-item">
              <div class="chart-title">ROI对比分析</div>
              <div class="chart-container">
                <div ref="roiChartRef" class="chart"></div>
              </div>
            </div>
          </div>
          <div class="cost-insights">
            <div class="insights-grid">
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#2080f0"><CashOutline /></n-icon>
                  <span>成本分析</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>总投入成本</span>
                    <span>¥{{ formatNumber(costData.totalCost) }}</span>
                  </div>
                  <div class="metric-row">
                    <span>平均获客成本</span>
                    <span>¥{{ costData.avgAcquisitionCost }}</span>
                  </div>
                  <div class="metric-row">
                    <span>最优渠道成本</span>
                    <span>¥{{ costData.bestChannelCost }}</span>
                  </div>
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#18a058"><StatsChartOutline /></n-icon>
                  <span>收益分析</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>总收益</span>
                    <span>¥{{ formatNumber(costData.totalRevenue) }}</span>
                  </div>
                  <div class="metric-row">
                    <span>平均客户价值</span>
                    <span>¥{{ costData.avgCustomerValue }}</span>
                  </div>
                  <div class="metric-row">
                    <span>最高ROI渠道</span>
                    <span>{{ costData.bestROIChannel }}</span>
                  </div>
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-header">
                  <n-icon size="20" color="#f0a020"><TrendingUpOutline /></n-icon>
                  <span>效率分析</span>
                </div>
                <div class="insight-metrics">
                  <div class="metric-row">
                    <span>整体ROI</span>
                    <span>{{ costData.overallROI }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>投资回收期</span>
                    <span>{{ costData.paybackPeriod }}天</span>
                  </div>
                  <div class="metric-row">
                    <span>利润率</span>
                    <span>{{ costData.profitMargin }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 渠道质量评估 -->
      <div class="analysis-row">
        <div class="analysis-section">
          <div class="section-header">
            <h3>渠道质量评估</h3>
          </div>
          <div class="quality-assessment">
            <div class="quality-radar">
              <div ref="qualityRadarChartRef" class="chart"></div>
            </div>
            <div class="quality-metrics">
              <div class="quality-item" v-for="channel in qualityData" :key="channel.name">
                <div class="quality-header">
                  <div class="quality-name">{{ channel.name }}</div>
                  <div class="quality-score" :class="getScoreClass(channel.score)">
                    {{ channel.score }}分
                  </div>
                </div>
                <div class="quality-dimensions">
                  <div class="dimension" v-for="dim in channel.dimensions" :key="dim.name">
                    <div class="dimension-name">{{ dim.name }}</div>
                    <div class="dimension-bar">
                      <div class="dimension-fill" :style="{ width: dim.value + '%', backgroundColor: dim.color }"></div>
                    </div>
                    <div class="dimension-value">{{ dim.value }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-section">
          <div class="section-header">
            <h3>渠道生命周期</h3>
          </div>
          <div class="lifecycle-analysis">
            <div class="lifecycle-chart">
              <div ref="lifecycleChartRef" class="chart"></div>
            </div>
            <div class="lifecycle-insights">
              <div class="lifecycle-stages">
                <div class="stage-item" v-for="stage in lifecycleStages" :key="stage.name">
                  <div class="stage-header">
                    <div class="stage-icon" :style="{ backgroundColor: stage.color }">
                      <n-icon size="16" color="white"><component :is="stage.icon" /></n-icon>
                    </div>
                    <div class="stage-name">{{ stage.name }}</div>
                  </div>
                  <div class="stage-metrics">
                    <div class="stage-metric">
                      <span>渠道数量</span>
                      <span>{{ stage.count }}</span>
                    </div>
                    <div class="stage-metric">
                      <span>平均表现</span>
                      <span>{{ stage.performance }}%</span>
                    </div>
                    <div class="stage-metric">
                      <span>投入占比</span>
                      <span>{{ stage.investment }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化建议 -->
      <div class="analysis-section">
        <div class="section-header">
          <h3>渠道优化建议</h3>
        </div>
        <div class="recommendations">
          <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
            <div class="recommendation-header">
              <div class="recommendation-icon">
                <n-icon :color="rec.color"><component :is="rec.icon" /></n-icon>
              </div>
              <div class="recommendation-priority" :class="rec.priority">
                {{ getPriorityText(rec.priority) }}
              </div>
            </div>
            <div class="recommendation-content">
              <div class="recommendation-title">{{ rec.title }}</div>
              <div class="recommendation-desc">{{ rec.description }}</div>
              <div class="recommendation-impact">
                <span class="impact-label">预期影响：</span>
                <span class="impact-value">{{ rec.impact }}</span>
              </div>
            </div>
            <div class="recommendation-action">
              <n-button size="small" type="primary" ghost>
                {{ rec.actionText }}
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <n-spin size="large">
        <template #description>
          正在分析渠道效果数据，请稍候...
        </template>
      </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NSelect, NDatePicker, NButton, NIcon, NRadioGroup, NRadioButton,
  NSpin, useMessage
} from 'naive-ui'
import {
  AnalyticsOutline, DownloadOutline, TrendingUpOutline, AtOutline,
  CashOutline, StatsChartOutline, SearchOutline, LogoFacebook,
  LogoGoogle, LogoInstagram, LogoTwitter, MailOutline, PhonePortraitOutline,
  TrendingDownOutline, PlayOutline, PauseOutline, StopOutline,
  CheckmarkCircleOutline, BulbOutline, AlertCircleOutline, WarningOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface AnalysisConfig {
  dimension: string
  dateRange: [number, number] | null
  channelTypes: string[]
  compareWith: string
}

const emit = defineEmits<{
  close: []
}>()

const message = useMessage()
const loading = ref(false)
const compareMetric = ref('traffic')

// 分析配置
const config = reactive<AnalysisConfig>({
  dimension: 'channel_performance',
  dateRange: null,
  channelTypes: [],
  compareWith: 'last_period'
})

// 配置选项
const dimensionOptions = [
  { label: '渠道表现', value: 'channel_performance' },
  { label: '成本效益', value: 'cost_effectiveness' },
  { label: '质量评估', value: 'quality_assessment' },
  { label: '生命周期', value: 'lifecycle_analysis' }
]

const channelTypeOptions = [
  { label: '搜索引擎', value: 'search_engine' },
  { label: '社交媒体', value: 'social_media' },
  { label: '邮件营销', value: 'email_marketing' },
  { label: '内容营销', value: 'content_marketing' },
  { label: '付费广告', value: 'paid_advertising' },
  { label: '合作伙伴', value: 'partnership' }
]

const compareOptions = [
  { label: '上期对比', value: 'last_period' },
  { label: '同期对比', value: 'same_period' },
  { label: '行业平均', value: 'industry_average' },
  { label: '目标值', value: 'target_value' }
]

// 图表引用
const comparisonChartRef = ref<HTMLElement>()
const trafficChartRef = ref<HTMLElement>()
const funnelChartRef = ref<HTMLElement>()
const costTrendChartRef = ref<HTMLElement>()
const roiChartRef = ref<HTMLElement>()
const qualityRadarChartRef = ref<HTMLElement>()
const lifecycleChartRef = ref<HTMLElement>()

// 分析数据
const overview = ref({
  totalTraffic: 285647,
  trafficGrowth: 18.5,
  avgConversionRate: 12.8,
  conversionGrowth: 15.2,
  avgCost: 125,
  costReduction: 8.3,
  avgROI: 285,
  roiGrowth: 22.7
})

const channelStats = ref([
  {
    name: 'Google搜索',
    type: '搜索引擎',
    traffic: 125840,
    conversions: 15680,
    conversionRate: 12.5,
    cost: 98,
    color: '#4285f4',
    icon: LogoGoogle,
    trend: 'positive',
    trendIcon: TrendingUpOutline,
    trendValue: 15.8
  },
  {
    name: 'Facebook广告',
    type: '社交媒体',
    traffic: 89560,
    conversions: 10740,
    conversionRate: 12.0,
    cost: 145,
    color: '#1877f2',
    icon: LogoFacebook,
    trend: 'positive',
    trendIcon: TrendingUpOutline,
    trendValue: 8.3
  },
  {
    name: '邮件营销',
    type: '邮件营销',
    traffic: 45230,
    conversions: 7890,
    conversionRate: 17.4,
    cost: 65,
    color: '#34a853',
    icon: MailOutline,
    trend: 'positive',
    trendIcon: TrendingUpOutline,
    trendValue: 12.5
  },
  {
    name: 'Instagram',
    type: '社交媒体',
    traffic: 32890,
    conversions: 3950,
    conversionRate: 12.0,
    cost: 125,
    color: '#e4405f',
    icon: LogoInstagram,
    trend: 'negative',
    trendIcon: TrendingDownOutline,
    trendValue: -3.2
  }
])

const funnelStages = ref([
  { name: '访问', count: 285647, rate: 100 },
  { name: '浏览', count: 198450, rate: 69.5 },
  { name: '咨询', count: 85630, rate: 30.0 },
  { name: '试用', count: 42815, rate: 15.0 },
  { name: '购买', count: 36560, rate: 12.8 }
])

const optimizationTips = ref([
  { stage: '访问→浏览', suggestion: '优化落地页设计，提升首屏吸引力' },
  { stage: '浏览→咨询', suggestion: '增加引导元素，简化咨询流程' },
  { stage: '咨询→试用', suggestion: '提供更多产品演示和案例' },
  { stage: '试用→购买', suggestion: '加强跟进服务，提供购买激励' }
])

const costData = ref({
  totalCost: 2856470,
  avgAcquisitionCost: 125,
  bestChannelCost: 65,
  totalRevenue: 8569410,
  avgCustomerValue: 375,
  bestROIChannel: '邮件营销',
  overallROI: 285,
  paybackPeriod: 45,
  profitMargin: 68.5
})

const qualityData = ref([
  {
    name: 'Google搜索',
    score: 92,
    dimensions: [
      { name: '流量质量', value: 95, color: '#2080f0' },
      { name: '转化效果', value: 88, color: '#18a058' },
      { name: '成本控制', value: 92, color: '#f0a020' },
      { name: '用户留存', value: 90, color: '#d03050' }
    ]
  },
  {
    name: '邮件营销',
    score: 88,
    dimensions: [
      { name: '流量质量', value: 85, color: '#2080f0' },
      { name: '转化效果', value: 95, color: '#18a058' },
      { name: '成本控制', value: 98, color: '#f0a020' },
      { name: '用户留存', value: 85, color: '#d03050' }
    ]
  },
  {
    name: 'Facebook广告',
    score: 78,
    dimensions: [
      { name: '流量质量', value: 75, color: '#2080f0' },
      { name: '转化效果', value: 80, color: '#18a058' },
      { name: '成本控制', value: 70, color: '#f0a020' },
      { name: '用户留存', value: 88, color: '#d03050' }
    ]
  }
])

const lifecycleStages = ref([
  {
    name: '启动期',
    count: 3,
    performance: 65,
    investment: 25,
    color: '#2080f0',
    icon: PlayOutline
  },
  {
    name: '成长期',
    count: 5,
    performance: 85,
    investment: 45,
    color: '#18a058',
    icon: TrendingUpOutline
  },
  {
    name: '成熟期',
    count: 8,
    performance: 92,
    investment: 25,
    color: '#f0a020',
    icon: CheckmarkCircleOutline
  },
  {
    name: '衰退期',
    count: 2,
    performance: 45,
    investment: 5,
    color: '#d03050',
    icon: StopOutline
  }
])

const recommendations = ref([
  {
    id: '1',
    icon: TrendingUpOutline,
    color: '#18a058',
    priority: 'high',
    title: '加大邮件营销投入',
    description: '邮件营销渠道ROI最高，建议增加投入并扩大覆盖范围',
    impact: 'ROI提升25-30%',
    actionText: '制定计划'
  },
  {
    id: '2',
    icon: WarningOutline,
    color: '#f0a020',
    priority: 'high',
    title: '优化Instagram广告',
    description: 'Instagram渠道表现下滑，需要重新评估投放策略和创意内容',
    impact: '转化率提升15%',
    actionText: '立即优化'
  },
  {
    id: '3',
    icon: BulbOutline,
    color: '#2080f0',
    priority: 'medium',
    title: '探索新兴渠道',
    description: '考虑投入TikTok、小红书等新兴社交媒体渠道',
    impact: '新增流量20%',
    actionText: '调研分析'
  },
  {
    id: '4',
    icon: AlertCircleOutline,
    color: '#d03050',
    priority: 'medium',
    title: '整合渠道数据',
    description: '建立统一的渠道数据追踪体系，提升归因准确性',
    impact: '数据准确性提升40%',
    actionText: '技术实施'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

const getScoreClass = (score: number) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'average'
  return 'poor'
}

const runAnalysis = async () => {
  loading.value = true
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    await nextTick()
    initCharts()
    message.success('分析完成')
  } catch (error) {
    message.error('分析失败，请重试')
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  message.info('报告导出功能开发中')
}

const initCharts = () => {
  // 渠道效果对比图
  if (comparisonChartRef.value) {
    const chart = echarts.init(comparisonChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['Google搜索', 'Facebook广告', '邮件营销', 'Instagram'] },
      xAxis: {
        type: 'category',
        data: ['流量', '转化', '成本', 'ROI']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: 'Google搜索',
          type: 'radar',
          data: [{ value: [95, 88, 85, 90], name: 'Google搜索' }]
        },
        {
          name: 'Facebook广告',
          type: 'radar',
          data: [{ value: [80, 75, 70, 75], name: 'Facebook广告' }]
        },
        {
          name: '邮件营销',
          type: 'radar',
          data: [{ value: [70, 95, 98, 95], name: '邮件营销' }]
        },
        {
          name: 'Instagram',
          type: 'radar',
          data: [{ value: [65, 70, 75, 68], name: 'Instagram' }]
        }
      ]
    })
  }

  // 渠道流量图
  if (trafficChartRef.value) {
    const chart = echarts.init(trafficChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: channelStats.value.map(item => ({
          value: item.traffic,
          name: item.name,
          itemStyle: { color: item.color }
        }))
      }]
    })
  }

  // 转化漏斗图
  if (funnelChartRef.value) {
    const chart = echarts.init(funnelChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        data: funnelStages.value.map(stage => ({
          value: stage.count,
          name: stage.name
        }))
      }]
    })
  }

  // 成本趋势图
  if (costTrendChartRef.value) {
    const chart = echarts.init(costTrendChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [150, 145, 135, 125, 118, 115],
        itemStyle: { color: '#f0a020' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(240, 160, 32, 0.3)' },
            { offset: 1, color: 'rgba(240, 160, 32, 0.1)' }
          ])
        }
      }]
    })
  }

  // ROI对比图
  if (roiChartRef.value) {
    const chart = echarts.init(roiChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: channelStats.value.map(item => item.name)
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [285, 195, 425, 165],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#18a058' },
            { offset: 1, color: '#87ceeb' }
          ])
        }
      }]
    })
  }

  // 质量雷达图
  if (qualityRadarChartRef.value) {
    const chart = echarts.init(qualityRadarChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      legend: {
        data: qualityData.value.map(item => item.name)
      },
      radar: {
        indicator: [
          { name: '流量质量', max: 100 },
          { name: '转化效果', max: 100 },
          { name: '成本控制', max: 100 },
          { name: '用户留存', max: 100 }
        ]
      },
      series: [{
        type: 'radar',
        data: qualityData.value.map(item => ({
          value: item.dimensions.map(dim => dim.value),
          name: item.name
        }))
      }]
    })
  }

  // 生命周期图
  if (lifecycleChartRef.value) {
    const chart = echarts.init(lifecycleChartRef.value)
    chart.setOption({
      tooltip: { trigger: 'item' },
      xAxis: {
        type: 'category',
        data: lifecycleStages.value.map(stage => stage.name)
      },
      yAxis: [
        { type: 'value', name: '渠道数量' },
        { type: 'value', name: '表现分数' }
      ],
      series: [
        {
          name: '渠道数量',
          type: 'bar',
          data: lifecycleStages.value.map(stage => stage.count),
          itemStyle: { color: '#2080f0' }
        },
        {
          name: '表现分数',
          type: 'line',
          yAxisIndex: 1,
          data: lifecycleStages.value.map(stage => stage.performance),
          itemStyle: { color: '#18a058' }
        }
      ]
    })
  }
}

onMounted(() => {
  runAnalysis()
})
</script>

<style scoped>
.channel-effect-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analysis-config {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #1a1a1a;
}

.config-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.channel-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.overview-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  align-items: center;
}

.card-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(32, 128, 240, 0.1);
  border-radius: 12px;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.card-change {
  font-size: 12px;
  font-weight: 500;
}

.card-change.positive {
  color: #18a058;
}

.card-change.negative {
  color: #d03050;
}

.analysis-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.chart-container {
  padding: 20px;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.analysis-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.channel-stats {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
}

.stat-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.stat-info {
  flex: 1;
  margin-left: 12px;
}

.stat-name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.stat-type {
  font-size: 12px;
  color: #666;
}

.stat-trend {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #18a058;
}

.stat-trend.negative {
  color: #d03050;
}

.stat-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-label {
  font-size: 10px;
  color: #999;
}

.metric-value {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.funnel-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  padding: 20px;
}

.funnel-chart {
  height: 250px;
}

.funnel-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.insight-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.insight-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.funnel-stage {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stage-info {
  flex: 1;
}

.stage-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.stage-count {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.stage-rate {
  font-size: 12px;
  color: #2080f0;
  font-weight: 500;
}

.optimization-tip {
  padding: 4px 0;
}

.tip-stage {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.tip-content {
  font-size: 12px;
  color: #1a1a1a;
  line-height: 1.4;
}

.cost-analysis {
  padding: 20px;
}

.cost-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.chart-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.cost-insights {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.insight-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
}

.insight-header {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1a1a1a;
}

.insight-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.metric-row span:first-child {
  color: #666;
}

.metric-row span:last-child {
  font-weight: 500;
  color: #1a1a1a;
}

.quality-assessment {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 20px;
}

.quality-radar {
  height: 300px;
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quality-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.quality-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.quality-name {
  font-weight: 500;
  color: #1a1a1a;
}

.quality-score {
  font-weight: 600;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}

.quality-score.excellent {
  background: #f0f9ff;
  color: #2080f0;
}

.quality-score.good {
  background: #f0fff4;
  color: #18a058;
}

.quality-score.average {
  background: #fff7e6;
  color: #f0a020;
}

.quality-score.poor {
  background: #fee;
  color: #d03050;
}

.quality-dimensions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dimension {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dimension-name {
  width: 60px;
  font-size: 10px;
  color: #666;
}

.dimension-bar {
  flex: 1;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.dimension-fill {
  height: 100%;
  border-radius: 3px;
}

.dimension-value {
  width: 30px;
  font-size: 10px;
  color: #1a1a1a;
  text-align: right;
}

.lifecycle-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  padding: 20px;
}

.lifecycle-chart {
  height: 250px;
}

.lifecycle-insights {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.lifecycle-stages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stage-item {
  background: white;
  border-radius: 6px;
  padding: 12px;
}

.stage-header {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.stage-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.stage-name {
  font-weight: 500;
  color: #1a1a1a;
}

.stage-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stage-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stage-metric span:first-child {
  color: #666;
}

.stage-metric span:last-child {
  font-weight: 500;
  color: #1a1a1a;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.recommendation-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.recommendation-priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

.recommendation-priority.high {
  background: #fee;
  color: #d03050;
}

.recommendation-priority.medium {
  background: #fff7e6;
  color: #f0a020;
}

.recommendation-priority.low {
  background: #f0f9ff;
  color: #2080f0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.recommendation-desc {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.recommendation-impact {
  display: flex;
  gap: 4px;
  align-items: center;
}

.impact-label {
  font-size: 10px;
  color: #999;
}

.impact-value {
  font-size: 10px;
  font-weight: 500;
  color: #18a058;
}

.recommendation-action {
  display: flex;
  align-items: flex-start;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>