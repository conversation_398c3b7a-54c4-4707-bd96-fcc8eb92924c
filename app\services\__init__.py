"""服务层模块

提供数据库操作服务、CRUD操作和业务逻辑处理
"""

from app.services.base_crud import CRUDBase
from app.services.database_manager import (
    DatabaseManager,
    db_manager,
    transactional,
    handle_db_errors
)
from app.services.crud import (
    # CRUD实例
    crud_user,
    crud_department,
    crud_customer,
    crud_customer_follow_record,
    crud_meeting,
    crud_role,
    crud_permission,
    crud_option_category,
    crud_option_item,
    crud_public_pool,
    crud_customer_behavior,
    crud_marketing_campaign,
    crud_campaign_participant,
    crud_campaign_share,
    crud_sales_funnel_stats,
    crud_customer_value_analysis,
    crud_follow_up,
    # CRUD类
    CRUDUser,
    CRUDDepartment,
    CRUDCustomer,
    CRUDCustomerFollowRecord,
    CRUDMeeting,
    CRUDRole,
    CRUDPermission
)

__all__ = [
    # 基础CRUD
    "CRUDBase",
    
    # 数据库管理
    "DatabaseManager",
    "db_manager",
    "transactional",
    "handle_db_errors",
    
    # CRUD类
    "CRUDUser",
    "CRUDDepartment",
    "CRUDCustomer",
    "CRUDCustomerFollowRecord",
    "CRUDMeeting",
    "CRUDRole",
    "CRUDPermission",
    
    # CRUD实例
    "crud_user",
    "crud_department",
    "crud_customer",
    "crud_customer_follow_record",
    "crud_meeting",
    "crud_role",
    "crud_permission",
    "crud_option_category",
    "crud_option_item",
    "crud_public_pool",
    "crud_customer_behavior",
    "crud_marketing_campaign",
    "crud_campaign_participant",
    "crud_campaign_share",
    "crud_sales_funnel_stats",
    "crud_customer_value_analysis",
    "crud_follow_up"
]