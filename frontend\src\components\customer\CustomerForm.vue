<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="isEdit ? '编辑客户' : '新增客户'"
    class="customer-form-modal"
    style="width: 800px;"
  >
    <template #header>
      <div class="modal-header">
        <n-icon :component="isEdit ? Create : PersonAdd" />
        <span>{{ isEdit ? '编辑客户' : '新增客户' }}</span>
      </div>
    </template>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
      class="customer-form"
    >
      <!-- 基本信息 -->
      <n-divider title-placement="left">
        <n-icon :component="Person" style="margin-right: 8px;" />
        基本信息
      </n-divider>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="客户姓名" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入客户姓名"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="手机号码" path="phone">
          <n-input
            v-model:value="formData.phone"
            placeholder="请输入手机号码"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="性别" path="gender">
          <n-select
            v-model:value="formData.gender"
            placeholder="请选择性别"
            :options="genderOptions"
            clearable
          />
        </n-form-item-gi>
        
        <!-- 年龄字段已移除，因为Customer接口中不存在age属性 -->
        
        <n-form-item-gi label="客户等级" path="level">
          <n-select
            v-model:value="formData.level"
            placeholder="请选择客户等级"
            :options="levelOptions"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="客户状态" path="status">
          <n-select
            v-model:value="formData.status"
            placeholder="请选择客户状态"
            :options="statusOptions"
            clearable
          />
        </n-form-item-gi>
      </n-grid>
      
      <!-- 联系信息 -->
      <n-divider title-placement="left">
        <n-icon :component="Call" style="margin-right: 8px;" />
        联系信息
      </n-divider>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="微信号" path="wechat">
          <n-input
            v-model:value="formData.wechat"
            placeholder="请输入微信号"
            clearable
          />
        </n-form-item-gi>
        
        <!-- QQ和邮箱字段已移除，因为Customer接口中不存在这些属性 -->
        
        <n-form-item-gi label="客户来源" path="source">
          <n-select
            v-model:value="formData.source"
            placeholder="请选择客户来源"
            :options="sourceOptions"
            clearable
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="详细地址" path="address">
        <n-input
          v-model:value="formData.address"
          placeholder="请输入详细地址"
          clearable
        />
      </n-form-item>
      
      <!-- 装修信息 -->
      <n-divider title-placement="left">
        <n-icon :component="Home" style="margin-right: 8px;" />
        装修信息
      </n-divider>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="装修类型" path="decorationType">
          <n-select
            v-model:value="formData.decorationType"
            placeholder="请选择装修类型"
            :options="decorationTypeOptions"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="房屋面积" path="houseArea">
          <n-input-number
            v-model:value="formData.houseArea"
            placeholder="请输入房屋面积"
            :min="0"
            clearable
          >
            <template #suffix>㎡</template>
          </n-input-number>
        </n-form-item-gi>
        
        <n-form-item-gi label="小区名称" path="community">
          <n-input
            v-model:value="formData.community"
            placeholder="请输入小区名称"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="装修风格" path="decorationType">
          <n-select
            v-model:value="formData.decorationType"
            placeholder="请选择装修风格"
            :options="decorationStyleOptions"
            clearable
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="预算范围" path="budget">
          <n-select
            v-model:value="formData.budget"
            placeholder="请选择预算范围"
            :options="budgetRangeOptions"
            clearable
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="期望开工时间" path="expectedStartDate">
          <n-date-picker
            v-model:value="formData.expectedStartDate"
            type="date"
            placeholder="请选择期望开工时间"
            clearable
            style="width: 100%;"
          />
        </n-form-item-gi>
      </n-grid>
      
      <!-- 其他信息 -->
      <n-divider title-placement="left">
        <n-icon :component="InformationCircle" style="margin-right: 8px;" />
        其他信息
      </n-divider>
      
      <n-form-item label="客户标签" path="tags">
        <n-dynamic-tags
          v-model:value="formData.tags"
          :max="10"
          placeholder="添加客户标签"
        />
      </n-form-item>
      
      <n-form-item label="备注信息" path="remark">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
          clearable
        />
      </n-form-item>
    </n-form>

    <template #action>
      <div class="modal-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
  NDatePicker,
  NDynamicTags,
  NButton,
  NDivider,
  NIcon,
  useMessage
} from 'naive-ui'
import type {
  FormInst,
  FormRules,
  SelectOption
} from 'naive-ui'
import {
  Person,
  Call,
  Home,
  InformationCircle,
  Create,
  Calendar,
  PersonAdd
} from '@vicons/ionicons5'
import type { Customer, CreateCustomerData, UpdateCustomerData } from '@/api/customerService'

// Props
interface Props {
  show: boolean
  customer?: Customer | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  customer: null,
  loading: false
})

// Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'submit', data: CreateCustomerData | UpdateCustomerData): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// Message
const message = useMessage()

// Refs
const formRef = ref<FormInst>()

// Computed
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.customer?.id)
const loading = computed(() => props.loading)

// 表单数据
interface FormData {
  name: string
  phone: string
  gender?: string
  level?: 'A' | 'B' | 'C' | 'D'
  status: 'potential' | 'contacted' | 'negotiating' | 'deal' | 'lost'
  wechat?: string
  address?: string
  source: string
  decorationType?: 'rough' | 'fine' | 'simple' | 'luxury'
  houseArea?: number
  community?: string
  budget?: string
  expectedStartDate?: number
  tags?: string[]
  remark?: string
}

const defaultFormData = (): FormData => ({
  name: '',
  phone: '',
  gender: undefined,
  level: undefined,
  status: 'potential',
  wechat: '',
  address: '',
  source: '',
  decorationType: undefined,
  houseArea: undefined,
  community: '',
  budget: undefined,
  expectedStartDate: undefined,
  tags: [],
  remark: ''
})

const formData = reactive<FormData>(defaultFormData())

// 选项数据
const genderOptions: SelectOption[] = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]

const levelOptions: SelectOption[] = [
  { label: 'A级客户', value: 'A' },
  { label: 'B级客户', value: 'B' },
  { label: 'C级客户', value: 'C' },
  { label: 'D级客户', value: 'D' }
]

const statusOptions: SelectOption[] = [
  { label: '潜在客户', value: 'potential' },
  { label: '意向客户', value: 'interested' },
  { label: '跟进中', value: 'following' },
  { label: '已成交', value: 'deal' },
  { label: '已失效', value: 'invalid' }
]

const sourceOptions: SelectOption[] = [
  { label: '线上推广', value: 'online' },
  { label: '朋友介绍', value: 'referral' },
  { label: '电话营销', value: 'telemarketing' },
  { label: '门店咨询', value: 'store' },
  { label: '展会活动', value: 'exhibition' },
  { label: '其他', value: 'other' }
]

const decorationTypeOptions: SelectOption[] = [
  { label: '新房装修', value: 'new' },
  { label: '二手房装修', value: 'second_hand' },
  { label: '局部装修', value: 'partial' },
  { label: '软装设计', value: 'soft' }
]

const decorationStyleOptions: SelectOption[] = [
  { label: '现代简约', value: 'modern' },
  { label: '欧式古典', value: 'european' },
  { label: '中式传统', value: 'chinese' },
  { label: '美式乡村', value: 'american' },
  { label: '北欧风格', value: 'nordic' },
  { label: '地中海', value: 'mediterranean' },
  { label: '工业风', value: 'industrial' },
  { label: '其他', value: 'other' }
]

const budgetRangeOptions: SelectOption[] = [
  { label: '5万以下', value: 'below_50k' },
  { label: '5-10万', value: '50k_100k' },
  { label: '10-20万', value: '100k_200k' },
  { label: '20-30万', value: '200k_300k' },
  { label: '30-50万', value: '300k_500k' },
  { label: '50万以上', value: 'above_500k' }
]

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '客户姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  age: [
    { type: 'number', min: 1, max: 120, message: '年龄应在1-120之间', trigger: 'blur' }
  ],
  houseArea: [
    { type: 'number', min: 0, message: '房屋面积不能为负数', trigger: 'blur' }
  ]
}

// 监听客户数据变化，填充表单
watch(
  () => props.customer,
  (customer) => {
    if (customer) {
      Object.assign(formData, {
        name: customer.name || '',
        phone: customer.phone || '',
        gender: customer.gender,
        level: customer.level,
        status: customer.status || 'potential',

        address: customer.address || '',
        source: customer.source || '',
        decorationType: customer.decorationType,

        community: customer.community || '',
        // decorationStyle: customer.decorationStyle, // 这个属性在Customer接口中不存在

        expectedStartDate: customer.last_follow_time ? new Date(customer.last_follow_time).getTime() : undefined,
        tags: customer.tags || [],
        remark: customer.remark || ''
      })
    } else {
      // 重置表单
      Object.assign(formData, defaultFormData())
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态，重置表单验证
watch(
  () => props.show,
  (show) => {
    if (show && formRef.value) {
      formRef.value.restoreValidation()
    }
  }
)

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    const submitData: Partial<Customer> = {
      name: formData.name,
      phone: formData.phone,
      gender: formData.gender as 'male' | 'female' | 'unknown' | undefined,
      level: formData.level,
      status: formData.status,

      address: formData.address,
      source: formData.source as 'phone' | 'online' | 'referral' | 'store' | 'exhibition' | 'other' | undefined,
      decorationType: formData.decorationType,

      community: formData.community,

      last_follow_time: formData.expectedStartDate
        ? new Date(formData.expectedStartDate).toISOString()
        : undefined,
      tags: formData.tags,
      remark: formData.remark
    }
    
    if (isEdit.value && props.customer?.id) {
      emit('submit', { id: props.customer.id, ...submitData } as UpdateCustomerData)
    } else {
      emit('submit', submitData as CreateCustomerData)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    message.error('请检查表单填写是否正确')
  }
}

const handleCancel = () => {
  emit('cancel')
  showModal.value = false
}
</script>

<style scoped>
.customer-form-modal {
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.customer-form {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.customer-form :deep(.n-divider) {
  margin: 24px 0 16px 0;
}

.customer-form :deep(.n-divider__title) {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.customer-form :deep(.n-form-item) {
  margin-bottom: 16px;
}

.customer-form :deep(.n-form-item-label) {
  font-weight: 500;
  color: #262626;
}

.customer-form :deep(.n-input),
.customer-form :deep(.n-select),
.customer-form :deep(.n-date-picker) {
  border-radius: 6px;
}

.customer-form :deep(.n-input:focus-within),
.customer-form :deep(.n-select:focus-within),
.customer-form :deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.customer-form :deep(.n-dynamic-tags .n-tag) {
  border-radius: 4px;
  font-size: 12px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.modal-actions .n-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0 24px;
  height: 36px;
}

/* 滚动条样式 */
.customer-form::-webkit-scrollbar {
  width: 6px;
}

.customer-form::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

.customer-form::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.customer-form::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-form-modal {
    width: 95vw !important;
    max-width: 95vw !important;
  }
  
  .customer-form :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  .customer-form :deep(.n-form-item-label) {
    font-size: 14px;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .modal-actions .n-button {
    width: 100%;
  }
}
</style>