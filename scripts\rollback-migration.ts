import { createClient } from '@supabase/supabase-js';
import { MySQLManager } from '../src/database/MySQLManager';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 回滚配置接口
interface RollbackConfig {
  rollbackMode: 'full' | 'partial' | 'table';
  targetTables?: string[];
  migrationId?: string;
  batchSize: number;
  createBackup: boolean;
  validateData: boolean;
  dryRun: boolean;
  confirmBeforeRollback: boolean;
}

// 回滚日志接口
interface RollbackLog {
  id: string;
  rollback_id: string;
  table_name: string;
  operation: string;
  status: 'started' | 'completed' | 'failed';
  records_count: number;
  error_message?: string;
  start_time: Date;
  end_time?: Date;
  duration_ms?: number;
}

// 表回滚统计
interface TableRollbackStats {
  tableName: string;
  mysqlRecords: number;
  rolledBackRecords: number;
  failedRecords: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  errors: string[];
}

// 备份信息
interface BackupInfo {
  backupId: string;
  timestamp: Date;
  tables: string[];
  backupPath: string;
  size: number;
}

/**
 * 数据回滚管理器
 */
class DataRollbackManager {
  private supabase: any;
  private mysql: MySQLManager;
  private config: RollbackConfig;
  private rollbackId: string;
  private logs: RollbackLog[] = [];
  private stats: TableRollbackStats[] = [];
  private backupInfo?: BackupInfo;

  constructor(config: RollbackConfig) {
    this.config = config;
    this.rollbackId = uuidv4();
    
    // 初始化Supabase客户端
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase配置缺失，请检查环境变量');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseKey);
    
    // 初始化MySQL管理器
    const mysqlConfig = {
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'workchat_admin',
      connectionLimit: 10
    };
    
    this.mysql = new MySQLManager(mysqlConfig);
  }

  /**
   * 初始化回滚环境
   */
  async initialize(): Promise<void> {
    console.log('🔄 初始化数据回滚环境...');
    
    try {
      // 初始化MySQL连接
      await this.mysql.initialize();
      
      // 验证Supabase连接
      const { data, error } = await this.supabase.from('users').select('count', { count: 'exact', head: true });
      if (error && error.code !== 'PGRST116') { // PGRST116 是表不存在的错误，可以忽略
        console.warn('⚠️ Supabase连接验证警告:', error.message);
      }
      
      console.log('✅ 回滚环境初始化完成');
    } catch (error) {
      console.error('❌ 回滚环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行数据回滚
   */
  async rollback(): Promise<void> {
    console.log(`🔄 开始数据回滚 (ID: ${this.rollbackId})`);
    
    if (this.config.dryRun) {
      console.log('🔍 运行模式: 预览模式 (不会实际回滚数据)');
    }

    const startTime = new Date();
    
    try {
      // 确认回滚操作
      if (this.config.confirmBeforeRollback && !this.config.dryRun) {
        const confirmation = await this.confirmRollback();
        if (!confirmation) {
          console.log('❌ 用户取消回滚操作');
          return;
        }
      }

      // 创建备份
      if (this.config.createBackup && !this.config.dryRun) {
        await this.createSupabaseBackup();
      }

      // 根据回滚模式执行不同的回滚策略
      switch (this.config.rollbackMode) {
        case 'full':
          await this.rollbackAllTables();
          break;
        case 'partial':
          await this.rollbackByMigrationId();
          break;
        case 'table':
          await this.rollbackSpecificTables();
          break;
        default:
          throw new Error(`未知的回滚模式: ${this.config.rollbackMode}`);
      }

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      console.log('🎉 数据回滚完成!');
      console.log(`⏱️ 总耗时: ${duration}ms`);
      
      // 生成回滚报告
      await this.generateRollbackReport();
      
    } catch (error) {
      console.error('❌ 数据回滚失败:', error);
      throw error;
    }
  }

  /**
   * 创建Supabase数据备份
   */
  private async createSupabaseBackup(): Promise<void> {
    console.log('💾 创建Supabase数据备份...');
    
    const backupId = uuidv4();
    const timestamp = new Date();
    const backupDir = path.join(__dirname, '..', 'backups', 'supabase');
    const backupPath = path.join(backupDir, `backup-${backupId}-${timestamp.toISOString().replace(/[:.]/g, '-')}.json`);
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
      
      const businessTables = [
        'users', 'roles', 'permissions', 'role_permissions', 'user_roles',
        'option_categories', 'option_items', 'customers', 'customer_follow_records',
        'marketing_campaigns', 'campaign_participants', 'campaign_shares',
        'meetings', 'meeting_participants', 'pool_rules', 'customer_behaviors',
        'wechat_customer_tracking', 'sales_funnel_stats', 'customer_value_analysis',
        'follow_ups', 'public_pool'
      ];

      const backup: any = {
        backupId,
        timestamp: timestamp.toISOString(),
        tables: {}
      };

      let totalSize = 0;

      for (const tableName of businessTables) {
        try {
          const { data, error } = await this.supabase
            .from(tableName)
            .select('*')
            .order('created_at', { ascending: true });

          if (error) {
            console.warn(`⚠️ 备份表 ${tableName} 时出错: ${error.message}`);
            backup.tables[tableName] = { error: error.message, data: [] };
          } else {
            backup.tables[tableName] = { data: data || [] };
            totalSize += JSON.stringify(data || []).length;
            console.log(`✅ 已备份表 ${tableName}: ${(data || []).length} 条记录`);
          }
        } catch (tableError: any) {
          console.warn(`⚠️ 备份表 ${tableName} 时出错: ${tableError.message}`);
          backup.tables[tableName] = { error: tableError.message, data: [] };
        }
      }

      await fs.writeFile(backupPath, JSON.stringify(backup, null, 2));
      
      this.backupInfo = {
        backupId,
        timestamp,
        tables: businessTables,
        backupPath,
        size: totalSize
      };

      console.log(`💾 Supabase备份已创建: ${backupPath}`);
      console.log(`📊 备份大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
      
    } catch (error) {
      console.error('❌ 创建Supabase备份失败:', error);
      throw error;
    }
  }

  /**
   * 回滚所有表
   */
  private async rollbackAllTables(): Promise<void> {
    console.log('🔄 回滚所有表数据...');
    
    // 定义回滚表的顺序（考虑外键依赖，与迁移顺序相反）
    const rollbackOrder = [
      'public_pool',
      'follow_ups',
      'customer_value_analysis',
      'sales_funnel_stats',
      'wechat_customer_tracking',
      'customer_behaviors',
      'pool_rules',
      'meeting_participants',
      'meetings',
      'campaign_shares',
      'campaign_participants',
      'marketing_campaigns',
      'customer_follow_records',
      'customers',
      'option_items',
      'option_categories',
      'user_roles',
      'role_permissions',
      'permissions',
      'roles',
      'users'
    ];

    // 按顺序回滚每个表
    for (const tableName of rollbackOrder) {
      await this.rollbackTable(tableName);
    }
  }

  /**
   * 根据迁移ID回滚
   */
  private async rollbackByMigrationId(): Promise<void> {
    if (!this.config.migrationId) {
      throw new Error('回滚模式为partial时必须提供migrationId');
    }

    console.log(`🔄 回滚迁移 ${this.config.migrationId} 的数据...`);
    
    try {
      // 查询该迁移涉及的表
      const migrationTables = await this.mysql.query(
        `SELECT DISTINCT table_name 
         FROM migration_logs 
         WHERE migration_id = ? AND status = 'completed'
         ORDER BY start_time`,
        [this.config.migrationId]
      );

      if (!migrationTables.success || !migrationTables.data || migrationTables.data.length === 0) {
        console.log(`ℹ️ 未找到迁移 ${this.config.migrationId} 的完成记录`);
        return;
      }

      const tables = migrationTables.data.map((row: any) => row.table_name);
      console.log(`📋 找到需要回滚的表: ${tables.join(', ')}`);

      // 按依赖关系逆序回滚
      const orderedTables = this.orderTablesForRollback(tables);
      
      for (const tableName of orderedTables) {
        await this.rollbackTable(tableName);
      }

    } catch (error) {
      console.error('❌ 根据迁移ID回滚时出错:', error);
      throw error;
    }
  }

  /**
   * 回滚指定表
   */
  private async rollbackSpecificTables(): Promise<void> {
    if (!this.config.targetTables || this.config.targetTables.length === 0) {
      throw new Error('回滚模式为table时必须提供targetTables');
    }

    console.log(`🔄 回滚指定表: ${this.config.targetTables.join(', ')}`);
    
    // 按依赖关系排序
    const orderedTables = this.orderTablesForRollback(this.config.targetTables);
    
    for (const tableName of orderedTables) {
      await this.rollbackTable(tableName);
    }
  }

  /**
   * 回滚单个表
   */
  private async rollbackTable(tableName: string): Promise<void> {
    console.log(`📊 开始回滚表: ${tableName}`);
    
    const tableStats: TableRollbackStats = {
      tableName,
      mysqlRecords: 0,
      rolledBackRecords: 0,
      failedRecords: 0,
      startTime: new Date(),
      errors: []
    };
    
    this.stats.push(tableStats);
    
    const log = this.createLog(tableName, 'rollback', 'started');
    
    try {
      // 检查MySQL表是否存在
      const tableExists = await this.mysql.tableExists(tableName);
      if (!tableExists) {
        console.log(`⚠️ MySQL表 ${tableName} 不存在，跳过回滚`);
        this.completeLog(log, 0);
        return;
      }

      // 获取MySQL表数据总数
      const mysqlCount = await this.mysql.getTableCount(tableName);
      tableStats.mysqlRecords = mysqlCount;
      
      if (mysqlCount === 0) {
        console.log(`⚠️ MySQL表 ${tableName} 无数据，跳过回滚`);
        this.completeLog(log, 0);
        return;
      }
      
      console.log(`📊 MySQL表 ${tableName} 有 ${mysqlCount} 条记录需要回滚`);

      if (!this.config.dryRun) {
        // 清空Supabase表
        await this.clearSupabaseTable(tableName);
        
        // 分批从MySQL读取数据并插入Supabase
        let offset = 0;
        let hasMore = true;
        
        while (hasMore) {
          const result = await this.mysql.query(
            `SELECT * FROM ${tableName} ORDER BY created_at LIMIT ? OFFSET ?`,
            [this.config.batchSize, offset]
          );
          
          if (!result.success || !result.data || result.data.length === 0) {
            hasMore = false;
            break;
          }
          
          // 转换数据格式并插入Supabase
          const convertedData = this.convertDataForSupabase(result.data, tableName);
          
          try {
            const { error } = await this.supabase
              .from(tableName)
              .insert(convertedData);
            
            if (error) {
              console.error(`❌ ${tableName}: 批量插入Supabase失败 - ${error.message}`);
              tableStats.failedRecords += convertedData.length;
              tableStats.errors.push(error.message);
              
              // 尝试逐条插入
              for (const record of convertedData) {
                try {
                  const { error: singleError } = await this.supabase
                    .from(tableName)
                    .insert([record]);
                  
                  if (singleError) {
                    tableStats.failedRecords++;
                    tableStats.errors.push(`记录 ${record.id}: ${singleError.message}`);
                  } else {
                    tableStats.rolledBackRecords++;
                  }
                } catch (singleError: any) {
                  tableStats.failedRecords++;
                  tableStats.errors.push(`记录 ${record.id}: ${singleError.message}`);
                }
              }
            } else {
              tableStats.rolledBackRecords += convertedData.length;
              console.log(`✅ ${tableName}: 已回滚 ${tableStats.rolledBackRecords}/${tableStats.mysqlRecords} 条记录`);
            }
          } catch (batchError: any) {
            tableStats.failedRecords += convertedData.length;
            tableStats.errors.push(batchError.message);
            console.error(`❌ ${tableName}: 回滚批次失败 - ${batchError.message}`);
          }
          
          offset += this.config.batchSize;
          
          // 避免过快请求
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } else {
        console.log(`🔍 预览: 将回滚表 ${tableName} 的 ${mysqlCount} 条记录到Supabase`);
        tableStats.rolledBackRecords = mysqlCount;
      }
      
      tableStats.endTime = new Date();
      tableStats.duration = tableStats.endTime.getTime() - tableStats.startTime.getTime();
      
      this.completeLog(log, tableStats.rolledBackRecords);
      
      console.log(`✅ 表 ${tableName} 回滚完成: ${tableStats.rolledBackRecords}/${tableStats.mysqlRecords} 条记录`);
      
      // 数据验证
      if (this.config.validateData && !this.config.dryRun) {
        await this.validateTableRollback(tableName, tableStats);
      }
      
    } catch (error: any) {
      tableStats.endTime = new Date();
      tableStats.errors.push(error.message);
      
      this.failLog(log, error.message);
      
      console.error(`❌ 表 ${tableName} 回滚失败:`, error);
      throw error;
    }
  }

  /**
   * 清空Supabase表
   */
  private async clearSupabaseTable(tableName: string): Promise<void> {
    console.log(`🗑️ 清空Supabase表: ${tableName}`);
    
    try {
      const { error } = await this.supabase
        .from(tableName)
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // 删除所有记录
      
      if (error) {
        console.warn(`⚠️ 清空Supabase表 ${tableName} 时出错: ${error.message}`);
      } else {
        console.log(`✅ 已清空Supabase表: ${tableName}`);
      }
    } catch (error: any) {
      console.warn(`⚠️ 清空Supabase表 ${tableName} 时出错: ${error.message}`);
    }
  }

  /**
   * 转换数据格式以适配Supabase
   */
  private convertDataForSupabase(data: any[], tableName: string): any[] {
    return data.map(record => {
      const converted = { ...record };
      
      // 处理时间字段 - 转换为ISO字符串
      ['created_at', 'updated_at', 'last_login_at', 'start_time', 'end_time', 
       'due_date', 'completed_at', 'next_follow_time', 'participation_date',
       'share_time', 'entered_at', 'last_interaction', 'last_calculated'].forEach(field => {
        if (converted[field] && converted[field] instanceof Date) {
          converted[field] = converted[field].toISOString();
        }
      });
      
      // 处理JSON字段 - 从字符串解析为对象
      ['conditions', 'actions', 'behavior_data', 'tags'].forEach(field => {
        if (converted[field] && typeof converted[field] === 'string') {
          try {
            converted[field] = JSON.parse(converted[field]);
          } catch {
            // 如果解析失败，保持原值
          }
        }
      });
      
      // 处理布尔字段 - 从数字转换为布尔值
      ['is_active', 'is_admin'].forEach(field => {
        if (typeof converted[field] === 'number') {
          converted[field] = converted[field] === 1;
        }
      });
      
      return converted;
    });
  }

  /**
   * 验证表回滚结果
   */
  private async validateTableRollback(tableName: string, stats: TableRollbackStats): Promise<void> {
    console.log(`🔍 验证表 ${tableName} 回滚结果...`);
    
    try {
      // 检查Supabase记录数
      const { count: supabaseCount } = await this.supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (supabaseCount !== stats.rolledBackRecords) {
        const error = `回滚验证失败: Supabase记录数(${supabaseCount}) != 回滚记录数(${stats.rolledBackRecords})`;
        stats.errors.push(error);
        console.warn(`⚠️ ${error}`);
      } else {
        console.log(`✅ 表 ${tableName} 回滚验证通过`);
      }
    } catch (error: any) {
      stats.errors.push(`回滚验证失败: ${error.message}`);
      console.error(`❌ 表 ${tableName} 回滚验证失败:`, error);
    }
  }

  /**
   * 按依赖关系排序表（用于回滚）
   */
  private orderTablesForRollback(tables: string[]): string[] {
    const rollbackOrder = [
      'public_pool', 'follow_ups', 'customer_value_analysis', 'sales_funnel_stats',
      'wechat_customer_tracking', 'customer_behaviors', 'pool_rules',
      'meeting_participants', 'meetings', 'campaign_shares', 'campaign_participants',
      'marketing_campaigns', 'customer_follow_records', 'customers',
      'option_items', 'option_categories', 'user_roles', 'role_permissions',
      'permissions', 'roles', 'users'
    ];
    
    return rollbackOrder.filter(table => tables.includes(table));
  }

  /**
   * 确认回滚操作
   */
  private async confirmRollback(): Promise<boolean> {
    console.log('⚠️ 即将执行数据回滚操作');
    console.log('⚠️ 这将清空Supabase中的现有数据并从MySQL恢复数据');
    console.log('⚠️ 此操作不可逆，请确认是否继续？');
    console.log('输入 "yes" 确认，其他任何输入将取消操作:');
    
    // 在实际环境中，这里应该使用适当的用户输入方法
    // 这里简化为返回false，需要手动修改为true来确认
    return false;
  }

  /**
   * 创建回滚日志
   */
  private createLog(tableName: string, operation: string, status: 'started' | 'completed' | 'failed'): RollbackLog {
    const log: RollbackLog = {
      id: uuidv4(),
      rollback_id: this.rollbackId,
      table_name: tableName,
      operation,
      status,
      records_count: 0,
      start_time: new Date()
    };
    
    this.logs.push(log);
    return log;
  }

  /**
   * 完成回滚日志
   */
  private completeLog(log: RollbackLog, recordsCount: number): void {
    log.status = 'completed';
    log.records_count = recordsCount;
    log.end_time = new Date();
    log.duration_ms = log.end_time.getTime() - log.start_time.getTime();
  }

  /**
   * 失败回滚日志
   */
  private failLog(log: RollbackLog, errorMessage: string): void {
    log.status = 'failed';
    log.error_message = errorMessage;
    log.end_time = new Date();
    log.duration_ms = log.end_time.getTime() - log.start_time.getTime();
  }

  /**
   * 生成回滚报告
   */
  private async generateRollbackReport(): Promise<void> {
    console.log('📋 生成回滚报告...');
    
    const report = {
      rollbackId: this.rollbackId,
      timestamp: new Date().toISOString(),
      config: this.config,
      backupInfo: this.backupInfo,
      summary: {
        totalTables: this.stats.length,
        successfulTables: this.stats.filter(s => s.errors.length === 0).length,
        failedTables: this.stats.filter(s => s.errors.length > 0).length,
        totalMysqlRecords: this.stats.reduce((sum, s) => sum + s.mysqlRecords, 0),
        rolledBackRecords: this.stats.reduce((sum, s) => sum + s.rolledBackRecords, 0),
        failedRecords: this.stats.reduce((sum, s) => sum + s.failedRecords, 0)
      },
      tableStats: this.stats,
      logs: this.logs
    };
    
    // 保存报告到文件
    const reportPath = path.join(__dirname, '..', 'docs', 'data-migration', `rollback-report-${this.rollbackId}.json`);
    
    try {
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 回滚报告已保存: ${reportPath}`);
    } catch (error) {
      console.error('保存回滚报告失败:', error);
    }
    
    // 打印摘要
    console.log('\n📊 回滚摘要:');
    console.log(`   总表数: ${report.summary.totalTables}`);
    console.log(`   成功表数: ${report.summary.successfulTables}`);
    console.log(`   失败表数: ${report.summary.failedTables}`);
    console.log(`   MySQL总记录数: ${report.summary.totalMysqlRecords}`);
    console.log(`   回滚记录数: ${report.summary.rolledBackRecords}`);
    console.log(`   失败记录数: ${report.summary.failedRecords}`);
    
    if (this.config.dryRun) {
      console.log('\n🔍 注意: 这是预览模式，没有实际回滚数据');
    }
    
    if (report.summary.failedTables > 0) {
      console.log('\n❌ 失败的表:');
      this.stats.filter(s => s.errors.length > 0).forEach(s => {
        console.log(`   ${s.tableName}: ${s.errors.join(', ')}`);
      });
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('🧹 清理回滚资源...');
    
    try {
      await this.mysql.close();
      console.log('✅ 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error);
    }
  }
}

/**
 * 主回滚函数
 */
async function main() {
  const config: RollbackConfig = {
    rollbackMode: (process.env.ROLLBACK_MODE as any) || 'full',
    targetTables: process.env.ROLLBACK_TARGET_TABLES?.split(','),
    migrationId: process.env.ROLLBACK_MIGRATION_ID,
    batchSize: parseInt(process.env.ROLLBACK_BATCH_SIZE || '100'),
    createBackup: process.env.ROLLBACK_CREATE_BACKUP !== 'false',
    validateData: process.env.ROLLBACK_VALIDATE_DATA !== 'false',
    dryRun: process.env.ROLLBACK_DRY_RUN === 'true',
    confirmBeforeRollback: process.env.ROLLBACK_CONFIRM !== 'false'
  };
  
  console.log('🔄 启动数据回滚程序');
  console.log('配置:', config);
  
  const rollbackManager = new DataRollbackManager(config);
  
  try {
    await rollbackManager.initialize();
    await rollbackManager.rollback();
    console.log('🎉 数据回滚成功完成!');
  } catch (error) {
    console.error('❌ 数据回滚失败:', error);
    process.exit(1);
  } finally {
    await rollbackManager.cleanup();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { DataRollbackManager, RollbackConfig };