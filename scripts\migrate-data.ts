import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'

// 导入前端 mock 数据
import { mockCustomers, mockPoolCustomers } from '../frontend/src/mock/customerData'
import { mockCampaigns, mockParticipants, mockCampaignShares } from '../frontend/src/mock/marketingData'
import { DataValidator, ErrorHandler, DataIntegrityChecker } from './validation'

// Supabase 配置
const supabaseUrl = process.env.VITE_SUPABASE_URL || ''
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置信息')
  console.log('请确保设置了以下环境变量:')
  console.log('- VITE_SUPABASE_URL')
  console.log('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// 数据转换工具函数
class DataTransformer {
  // 转换客户数据
  static transformCustomer(customer: any, source: 'sql' | 'mock') {
    if (source === 'sql') {
      return {
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email || null,
        company: customer.company || null,
        position: customer.position || null,
        wechat_id: customer.wechat || null,
        address: customer.address || null,
        source: customer.source || 'unknown',
        status: this.mapCustomerStatus(customer.status),
        level: customer.level || 'D',
        birthday: customer.birthday || null,
        gender: customer.gender || null,
        is_vip: customer.is_vip || false,
        is_high_value: customer.is_high_value || false,
        tags: customer.tags ? JSON.parse(customer.tags) : [],
        notes: customer.remark || null,
        assigned_to: customer.owner_id || null,
        last_contact_at: customer.last_follow_time || null,
        next_follow_at: customer.next_follow_time || null,
        follow_count: customer.follow_count || 0,
        deal_amount: customer.deal_amount || 0,
        deal_probability: 0,
        region: [],
        created_at: customer.created_at || new Date().toISOString(),
        updated_at: customer.updated_at || new Date().toISOString()
      }
    } else {
      return {
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        email: null,
        company: customer.company || null,
        position: customer.position || null,
        wechat_id: null,
        address: customer.address || null,
        source: customer.source || 'unknown',
        status: this.mapCustomerStatus(customer.status),
        level: customer.level || 'D',
        birthday: null,
        gender: null,
        is_vip: false,
        is_high_value: false,
        tags: customer.tags || [],
        notes: customer.notes || customer.remark || null,
        assigned_to: customer.assigned_to || null,
        last_contact_at: customer.last_follow_time || null,
        next_follow_at: customer.next_follow_time || null,
        follow_count: 0,
        deal_amount: customer.deal_amount || 0,
        deal_probability: customer.deal_probability || 0,
        region: customer.region || [],
        created_at: customer.created_at || new Date().toISOString(),
        updated_at: customer.updated_at || new Date().toISOString()
      }
    }
  }

  // 转换跟进记录数据
  static transformFollowRecord(record: any) {
    return {
      id: record.id,
      customer_id: record.customer_id,
      user_id: record.user_id,
      contact_type: this.mapContactType(record.type),
      stage: record.stage || 'initial',
      content: record.content || '',
      result: record.result || '',
      result_detail: record.result_detail || null,
      has_next_plan: record.has_next_plan || false,
      next_follow_at: record.next_time || null,
      next_content: record.next_content || null,
      duration: record.duration || null,
      location: record.location || null,
      created_at: record.created_at || new Date().toISOString(),
      updated_at: record.updated_at || new Date().toISOString()
    }
  }

  // 转换营销活动数据
  static transformCampaign(campaign: any) {
    return {
      id: campaign.id,
      name: campaign.name,
      type: campaign.type,
      description: campaign.description || '',
      status: this.mapCampaignStatus(campaign.status),
      start_date: campaign.start_time || campaign.start_date,
      end_date: campaign.end_time || campaign.end_date,
      target_audience: campaign.target_audience || 'all',
      budget: campaign.budget || 0,
      participants_count: campaign.participants_count || 0,
      conversion_rate: campaign.conversion_rate || 0,
      config: campaign.config || {},
      created_at: campaign.created_at || new Date().toISOString(),
      updated_at: campaign.updated_at || new Date().toISOString()
    }
  }

  // 转换活动参与者数据
  static transformParticipant(participant: any) {
    return {
      id: participant.id,
      campaign_id: participant.campaign_id,
      customer_id: participant.customer_id,
      customer_name: participant.customer_name,
      customer_phone: participant.customer_phone,
      customer_wechat: participant.customer_wechat || null,
      participation_time: participant.participation_time,
      status: participant.status || 'active',
      result: participant.result || null,
      reward: participant.reward || null,
      notes: participant.notes || null,
      created_at: participant.created_at || new Date().toISOString(),
      updated_at: participant.updated_at || new Date().toISOString()
    }
  }

  // 转换会议记录数据
  static transformMeeting(meeting: any) {
    return {
      id: meeting.id,
      follow_record_id: meeting.follow_record_id,
      customer_id: meeting.customer_id,
      user_id: meeting.user_id,
      meeting_type: meeting.meeting_type || 'consultation',
      meeting_time: meeting.meeting_time,
      designer_id: meeting.designer_id || null,
      designer_name: meeting.designer_name || null,
      visit_count: meeting.visit_count || 1,
      address: meeting.address || null,
      notes: meeting.notes || null,
      created_at: meeting.created_at || new Date().toISOString(),
      updated_at: meeting.updated_at || new Date().toISOString()
    }
  }

  // 状态映射函数
  static mapCustomerStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'potential': 'potential',
      'interested': 'contacted',
      'deal': 'converted',
      'lost': 'lost',
      'contacted': 'contacted',
      'converted': 'converted'
    }
    return statusMap[status] || 'potential'
  }

  static mapContactType(type: string): string {
    const typeMap: Record<string, string> = {
      'phone': 'phone',
      'meeting': 'meeting',
      'wechat': 'wechat',
      'email': 'email',
      'visit': 'visit'
    }
    return typeMap[type] || 'phone'
  }

  static mapCampaignStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'active': 'active',
      'draft': 'draft',
      'ended': 'completed',
      'completed': 'completed'
    }
    return statusMap[status] || 'draft'
  }

  // 数据验证函数
  static validateCustomer(customer: any): boolean {
    if (!customer.name || !customer.phone) {
      console.warn(`❌ 客户数据验证失败: 缺少必填字段 (name: ${customer.name}, phone: ${customer.phone})`)
      return false
    }
    
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(customer.phone)) {
      console.warn(`❌ 客户数据验证失败: 手机号格式错误 (${customer.phone})`)
      return false
    }
    
    return true
  }

  static validateCampaign(campaign: any): boolean {
    if (!campaign.name) {
      console.warn(`❌ 营销活动数据验证失败: 缺少活动名称`)
      return false
    }
    return true
  }
}

// 数据迁移类
class DataMigrator {
  private successCount = 0
  private errorCount = 0
  private errors: string[] = []
  private errorHandler: ErrorHandler
  private integrityChecker: DataIntegrityChecker

  constructor() {
    this.errorHandler = new ErrorHandler()
    this.integrityChecker = new DataIntegrityChecker(supabase)
  }

  // 迁移客户数据
  async migrateCustomers() {
    this.errorHandler.logInfo('MIGRATION', '开始迁移客户数据...')
    
    // 迁移前端 mock 客户数据
    this.errorHandler.logInfo('MIGRATION', '迁移前端 mock 客户数据...')
    for (const customer of mockCustomers) {
      try {
        const transformedCustomer = DataTransformer.transformCustomer(customer, 'mock')
        
        if (!DataTransformer.validateCustomer(transformedCustomer)) {
          this.errorHandler.logError('VALIDATION', `客户数据验证失败: ${customer.name}`, { customer: transformedCustomer })
          this.errorCount++
          continue
        }

        const { error } = await supabase
          .from('customers')
          .upsert(transformedCustomer, { onConflict: 'id' })
        
        if (error) {
          this.errorHandler.logError('DATABASE', `插入客户失败 (${customer.name}): ${error.message}`, error)
          this.errors.push(`客户 ${customer.name}: ${error.message}`)
          this.errorCount++
        } else {
          this.errorHandler.logSuccess('MIGRATION', `成功迁移客户: ${customer.name}`)
          this.successCount++
        }
      } catch (err) {
        this.errorHandler.logError('MIGRATION', `处理客户数据失败 (${customer.name})`, err)
        this.errors.push(`客户 ${customer.name}: ${err}`)
        this.errorCount++
      }
    }

    // 迁移公海客户数据
    this.errorHandler.logInfo('MIGRATION', '迁移公海客户数据...')
    for (const customer of mockPoolCustomers) {
      try {
        const transformedCustomer = DataTransformer.transformCustomer(customer, 'mock')
        transformedCustomer.assigned_to = null // 公海客户没有分配人员
        
        if (!DataTransformer.validateCustomer(transformedCustomer)) {
          this.errorHandler.logError('VALIDATION', `公海客户数据验证失败: ${customer.name}`, { customer: transformedCustomer })
          this.errorCount++
          continue
        }

        const { error } = await supabase
          .from('customers')
          .upsert(transformedCustomer, { onConflict: 'id' })
        
        if (error) {
          this.errorHandler.logError('DATABASE', `插入公海客户失败 (${customer.name}): ${error.message}`, error)
          this.errors.push(`公海客户 ${customer.name}: ${error.message}`)
          this.errorCount++
        } else {
          this.errorHandler.logSuccess('MIGRATION', `成功迁移公海客户: ${customer.name}`)
          this.successCount++
        }
      } catch (err) {
        this.errorHandler.logError('MIGRATION', `处理公海客户数据失败 (${customer.name})`, err)
        this.errors.push(`公海客户 ${customer.name}: ${err}`)
        this.errorCount++
      }
    }
  }

  // 迁移营销活动数据
  async migrateCampaigns() {
    this.errorHandler.logInfo('MIGRATION', '开始迁移营销活动数据...')
    
    for (const campaign of mockCampaigns) {
      try {
        const transformedCampaign = DataTransformer.transformCampaign(campaign)
        
        if (!DataTransformer.validateCampaign(transformedCampaign)) {
          this.errorHandler.logError('VALIDATION', `营销活动数据验证失败: ${campaign.name}`, { campaign: transformedCampaign })
          this.errorCount++
          continue
        }

        const { error } = await supabase
          .from('marketing_campaigns')
          .upsert(transformedCampaign, { onConflict: 'id' })
        
        if (error) {
          this.errorHandler.logError('DATABASE', `插入营销活动失败 (${campaign.name}): ${error.message}`, error)
          this.errors.push(`营销活动 ${campaign.name}: ${error.message}`)
          this.errorCount++
        } else {
          this.errorHandler.logSuccess('MIGRATION', `成功迁移营销活动: ${campaign.name}`)
          this.successCount++
        }
      } catch (err) {
        this.errorHandler.logError('MIGRATION', `处理营销活动数据失败 (${campaign.name})`, err)
        this.errors.push(`营销活动 ${campaign.name}: ${err}`)
        this.errorCount++
      }
    }
  }

  // 迁移活动参与者数据
  async migrateParticipants() {
    this.errorHandler.logInfo('MIGRATION', '开始迁移活动参与者数据...')
    
    for (const participant of mockParticipants) {
      try {
        const transformedParticipant = DataTransformer.transformParticipant(participant)

        const { error } = await supabase
          .from('campaign_participants')
          .upsert(transformedParticipant, { onConflict: 'id' })
        
        if (error) {
          this.errorHandler.logError('DATABASE', `插入活动参与者失败 (${participant.customer_name}): ${error.message}`, error)
          this.errors.push(`活动参与者 ${participant.customer_name}: ${error.message}`)
          this.errorCount++
        } else {
          this.errorHandler.logSuccess('MIGRATION', `成功迁移活动参与者: ${participant.customer_name}`)
          this.successCount++
        }
      } catch (err) {
        this.errorHandler.logError('MIGRATION', `处理活动参与者数据失败 (${participant.customer_name})`, err)
        this.errors.push(`活动参与者 ${participant.customer_name}: ${err}`)
        this.errorCount++
      }
    }
  }

  // 迁移活动分享数据
  async migrateShares() {
    this.errorHandler.logInfo('MIGRATION', '开始迁移活动分享数据...')
    
    for (const share of mockCampaignShares) {
      try {
        const { error } = await supabase
          .from('campaign_shares')
          .upsert(share, { onConflict: 'id' })
        
        if (error) {
          this.errorHandler.logError('DATABASE', `插入活动分享失败: ${error.message}`, error)
          this.errors.push(`活动分享: ${error.message}`)
          this.errorCount++
        } else {
          this.errorHandler.logSuccess('MIGRATION', '成功迁移活动分享记录')
          this.successCount++
        }
      } catch (err) {
        this.errorHandler.logError('MIGRATION', '处理活动分享数据失败', err)
        this.errors.push(`活动分享: ${err}`)
        this.errorCount++
      }
    }
  }

  // 从 SQL 文件迁移数据
  async migrateSqlData() {
    console.log('\n📄 开始迁移 SQL 文件数据...')
    
    try {
      const sqlPath = path.join(process.cwd(), 'docs', 'sample_data.sql')
      if (!fs.existsSync(sqlPath)) {
        console.warn('⚠️  未找到 sample_data.sql 文件，跳过 SQL 数据迁移')
        return
      }

      const sqlContent = fs.readFileSync(sqlPath, 'utf-8')
      console.log('📝 SQL 文件读取成功，但需要手动解析和迁移')
      console.log('💡 建议: 可以使用数据库工具直接导入 SQL 文件，或者编写专门的 SQL 解析器')
      
    } catch (err) {
      console.error('❌ 读取 SQL 文件失败:', err)
      this.errors.push(`SQL 文件读取: ${err}`)
      this.errorCount++
    }
  }

  // 验证数据完整性
  async validateData() {
    this.errorHandler.logInfo('VALIDATION', '验证数据完整性...')
    
    try {
      // 检查客户数据
      const { data: customers, error: customersError } = await supabase
        .from('customers')
        .select('id, name, phone')
      
      if (customersError) {
        this.errorHandler.logError('VALIDATION', `查询客户数据失败: ${customersError.message}`, customersError)
      } else {
        this.errorHandler.logSuccess('VALIDATION', `客户数据验证通过，共 ${customers?.length || 0} 条记录`)
      }

      // 检查营销活动数据
      const { data: campaigns, error: campaignsError } = await supabase
        .from('marketing_campaigns')
        .select('id, name, status')
      
      if (campaignsError) {
        this.errorHandler.logError('VALIDATION', `查询营销活动数据失败: ${campaignsError.message}`, campaignsError)
      } else {
        this.errorHandler.logSuccess('VALIDATION', `营销活动数据验证通过，共 ${campaigns?.length || 0} 条记录`)
      }

      // 检查活动参与者数据
      const { data: participants, error: participantsError } = await supabase
        .from('campaign_participants')
        .select('id, customer_name')
      
      if (participantsError) {
        this.errorHandler.logError('VALIDATION', `查询活动参与者数据失败: ${participantsError.message}`, participantsError)
      } else {
        this.errorHandler.logSuccess('VALIDATION', `活动参与者数据验证通过，共 ${participants?.length || 0} 条记录`)
      }

      // 执行数据完整性检查
      const integrityResult = await this.integrityChecker.performFullCheck()
      
      if (!integrityResult.isValid) {
        this.errorHandler.logWarning('INTEGRITY', '数据完整性检查发现问题', {
          foreignKeyIssues: integrityResult.foreignKeyIssues,
          consistencyIssues: integrityResult.consistencyIssues,
          duplicateIssues: integrityResult.duplicateIssues
        })
      } else {
        this.errorHandler.logSuccess('INTEGRITY', '数据完整性检查通过')
      }

    } catch (err) {
      this.errorHandler.logError('VALIDATION', '数据验证失败', err)
      this.errors.push(`数据验证: ${err}`)
      this.errorCount++
    }
  }

  // 生成迁移报告
  generateReport() {
    const report = this.errorHandler.generateReport()
    console.log('\n📊 数据迁移报告')
    console.log('=' .repeat(50))
    console.log(`✅ 成功迁移: ${this.successCount} 条记录`)
    console.log(`❌ 失败记录: ${this.errorCount} 条记录`)
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误详情:')
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`)
      })
    }
    
    console.log(report)
    
    // 保存报告到文件
    try {
      const reportPath = path.join(process.cwd(), 'migration-report.txt')
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const reportContent = `数据迁移报告 - ${timestamp}\n${report}`
      
      fs.writeFileSync(reportPath, reportContent, 'utf8')
      this.errorHandler.logInfo('REPORT', `迁移报告已保存到: ${reportPath}`)
    } catch (error) {
      this.errorHandler.logError('REPORT', '保存迁移报告失败', error)
    }
    
    console.log('\n🎉 数据迁移完成！')
  }

  // 执行完整迁移流程
  async migrate() {
    this.errorHandler.logInfo('MIGRATION', '开始数据迁移流程...')
    console.log('=' .repeat(50))
    
    try {
      // 重置计数器
      this.successCount = 0
      this.errorCount = 0
      this.errors = []

      // 执行迁移
      await this.migrateCustomers()
      await this.migrateCampaigns()
      await this.migrateParticipants()
      await this.migrateShares()
      await this.migrateSqlData()
      
      // 验证数据
      await this.validateData()
      
      // 生成报告
      this.generateReport()
      
      if (!this.errorHandler.hasErrors()) {
        this.errorHandler.logSuccess('MIGRATION', '数据迁移完成！')
      } else {
        this.errorHandler.logWarning('MIGRATION', '数据迁移完成，但存在一些问题，请查看报告')
      }
      
    } catch (err) {
      this.errorHandler.logError('MIGRATION', '迁移流程失败', err)
      this.generateReport()
      process.exit(1)
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 数据迁移工具')
  console.log('目标: 将测试数据迁移到 Supabase 数据库')
  console.log('=' .repeat(50))
  
  const migrator = new DataMigrator()
  await migrator.migrate()
}

// 主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { DataMigrator, DataTransformer }