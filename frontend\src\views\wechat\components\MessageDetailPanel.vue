<template>
  <div class="message-detail-panel">
    <!-- 消息基本信息 -->
    <n-card title="消息信息" class="info-card">
      <div class="message-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">消息ID：</span>
            <span class="value">{{ message.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">消息类型：</span>
            <n-tag :type="getTypeColor(message.type || '')">{{ getTypeText(message.type || '') }}</n-tag>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">发送者：</span>
            <span class="value">{{ message.senderName || '未知用户' }}</span>
          </div>
          <div class="info-item">
            <span class="label">发送者ID：</span>
            <span class="value">{{ message.senderId }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">群组：</span>
            <span class="value">{{ message.groupName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">发送时间：</span>
            <span class="value">{{ formatDateTime(message.sendTime || '') }}</span>
          </div>
        </div>
        <div class="info-row" v-if="message.sentiment">
          <div class="info-item">
            <span class="label">情感倾向：</span>
            <n-tag :type="getSentimentColor(message.sentiment)">{{ getSentimentText(message.sentiment) }}</n-tag>
          </div>
          <div class="info-item" v-if="message.isKeywordHit">
            <span class="label">关键词命中：</span>
            <n-tag type="error">是</n-tag>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 消息内容 -->
    <n-card title="消息内容" class="content-card">
      <div class="message-content">
        <div class="content-text" v-if="message.type === 'text'">
          {{ message.content }}
        </div>
        <div class="content-media" v-else-if="message.type === 'image'">
          <img :src="message.media_url" alt="图片消息" class="media-image" />
          <div class="media-caption" v-if="message.content">{{ message.content }}</div>
        </div>
        <div class="content-file" v-else-if="message.type === 'file'">
          <div class="file-info">
            <n-icon size="24" color="#2080f0"><DocumentOutline /></n-icon>
            <div class="file-details">
              <div class="file-name">{{ message.content || '未知文件' }}</div>
              <div class="file-size">{{ formatFileSize(message.media_size) }}</div>
            </div>
          </div>
          <n-button size="small" @click="downloadFile">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            下载
          </n-button>
        </div>
        <div class="content-voice" v-else-if="message.type === 'voice'">
          <div class="voice-player">
            <n-button circle @click="playVoice">
              <template #icon>
                <n-icon><PlayOutline /></n-icon>
              </template>
            </n-button>
            <span class="voice-duration">{{ message.media_size || 0 }}s</span>
          </div>
        </div>
        <div class="content-link" v-else-if="message.type === 'link'">
          <div class="link-preview">
            <div class="link-title">{{ message.content }}</div>
            <div class="link-desc">{{ message.content }}</div>
            <div class="link-url">{{ message.media_url }}</div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 关键词分析 -->
    <n-card title="关键词分析" class="keyword-card" v-if="message.isKeywordHit">
      <div class="keyword-analysis">
        <div class="matched-keywords">
          <h4>命中关键词</h4>
          <div class="keyword-list">
            <n-tag
              v-for="keyword in matchedKeywords"
              :key="keyword.word"
              :type="keyword.level === 'high' ? 'error' : keyword.level === 'medium' ? 'warning' : 'info'"
              class="keyword-tag"
            >
              {{ keyword.word }}
              <span class="keyword-score">({{ keyword.score }})</span>
            </n-tag>
          </div>
        </div>
        <div class="keyword-context">
          <h4>上下文分析</h4>
          <div class="context-text">
            {{ highlightKeywords(message.content) }}
          </div>
        </div>
      </div>
    </n-card>

    <!-- 情感分析 -->
    <n-card title="情感分析" class="sentiment-card" v-if="message.sentiment">
      <div class="sentiment-analysis">
        <div class="sentiment-overview">
          <div class="sentiment-score">
            <div class="score-circle" :class="`sentiment-${message.sentiment}`">
              <div class="score-number">{{ sentimentScore }}</div>
              <div class="score-label">情感评分</div>
            </div>
          </div>
          <div class="sentiment-details">
            <div class="detail-item">
              <span class="detail-label">情感倾向：</span>
              <span class="detail-value">{{ getSentimentText(message.sentiment) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">置信度：</span>
              <span class="detail-value">{{ sentimentConfidence }}%</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">情感强度：</span>
              <n-progress
                type="line"
                :percentage="sentimentIntensity"
                :show-indicator="false"
                :color="getSentimentProgressColor(message.sentiment)"
              />
            </div>
          </div>
        </div>
        <div class="sentiment-breakdown">
          <h4>情感构成</h4>
          <div class="emotion-bars">
            <div class="emotion-item">
              <span class="emotion-label">积极</span>
              <n-progress type="line" :percentage="emotionBreakdown.positive" color="#18a058" :show-indicator="false" />
              <span class="emotion-value">{{ emotionBreakdown.positive }}%</span>
            </div>
            <div class="emotion-item">
              <span class="emotion-label">中性</span>
              <n-progress type="line" :percentage="emotionBreakdown.neutral" color="#909399" :show-indicator="false" />
              <span class="emotion-value">{{ emotionBreakdown.neutral }}%</span>
            </div>
            <div class="emotion-item">
              <span class="emotion-label">消极</span>
              <n-progress type="line" :percentage="emotionBreakdown.negative" color="#d03050" :show-indicator="false" />
              <span class="emotion-value">{{ emotionBreakdown.negative }}%</span>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 相关消息 -->
    <n-card title="相关消息" class="related-card">
      <div class="related-messages">
        <div class="message-item" v-for="relatedMsg in relatedMessages" :key="relatedMsg.id">
          <div class="message-header">
            <span class="sender-name">{{ relatedMsg.senderName }}</span>
            <span class="send-time">{{ formatTime(relatedMsg.sendTime) }}</span>
          </div>
          <div class="message-text">{{ relatedMsg.content }}</div>
        </div>
        <div class="no-related" v-if="relatedMessages.length === 0">
          暂无相关消息
        </div>
      </div>
    </n-card>

    <!-- 操作记录 -->
    <n-card title="操作记录" class="action-card">
      <div class="action-history">
        <div class="action-item" v-for="action in actionHistory" :key="action.id">
          <div class="action-icon">
            <n-icon :color="action.color"><component :is="action.icon" /></n-icon>
          </div>
          <div class="action-content">
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
            <div class="action-time">{{ formatDateTime(action.time) }}</div>
          </div>
        </div>
        <div class="no-actions" v-if="actionHistory.length === 0">
          暂无操作记录
        </div>
      </div>
    </n-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-button type="primary" @click="markAsImportant">
        <template #icon>
          <n-icon><StarOutline /></n-icon>
        </template>
        标记重要
      </n-button>
      <n-button @click="addToKeywords" v-if="message.type === 'text'">
        <template #icon>
          <n-icon><AddOutline /></n-icon>
        </template>
        添加关键词
      </n-button>
      <n-button @click="reportMessage">
        <template #icon>
          <n-icon><FlagOutline /></n-icon>
        </template>
        举报消息
      </n-button>
      <n-button @click="$emit('close')">
        关闭
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  NCard, NTag, NButton, NIcon, NProgress,
  useMessage
} from 'naive-ui'
import {
  DocumentOutline, DownloadOutline, PlayOutline, StarOutline,
  AddOutline, FlagOutline, CheckmarkCircleOutline, AlertCircleOutline,
  EyeOutline
} from '@vicons/ionicons5'
import type { WechatMessage } from '@/types'

interface Props {
  message: WechatMessage
}

interface Emits {
  (e: 'update'): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const messageApi = useMessage()

// 模拟数据
const matchedKeywords = ref([
  { word: '产品', level: 'high', score: 95 },
  { word: '问题', level: 'medium', score: 78 },
  { word: '反馈', level: 'low', score: 65 }
])

const sentimentScore = ref(85)
const sentimentConfidence = ref(92)
const sentimentIntensity = ref(78)

const emotionBreakdown = ref({
  positive: 75,
  neutral: 15,
  negative: 10
})

const relatedMessages = ref([
  {
    id: '1',
    senderName: '张三',
    content: '我也遇到了同样的问题',
    sendTime: '2024-01-15 14:30:00'
  },
  {
    id: '2',
    senderName: '李四',
    content: '这个功能确实需要优化',
    sendTime: '2024-01-15 14:32:00'
  }
])

const actionHistory = ref([
  {
    id: '1',
    icon: EyeOutline,
    color: '#2080f0',
    title: '消息查看',
    description: '管理员查看了此消息',
    time: '2024-01-15 15:00:00'
  },
  {
    id: '2',
    icon: CheckmarkCircleOutline,
    color: '#18a058',
    title: '关键词匹配',
    description: '系统检测到关键词匹配',
    time: '2024-01-15 14:25:00'
  }
])

// 方法
const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}

const formatTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleTimeString()
}

const formatFileSize = (size?: number) => {
  if (!size) return '未知大小'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(1)} ${units[index]}`
}

const getTypeColor = (type: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    text: 'default',
    image: 'info',
    file: 'warning',
    voice: 'success',
    video: 'error',
    link: 'primary'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    text: '文本',
    image: '图片',
    file: '文件',
    voice: '语音',
    video: '视频',
    link: '链接'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getSentimentColor = (sentiment: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    positive: 'success',
    neutral: 'default',
    negative: 'error'
  }
  return colorMap[sentiment] || 'default'
}

const getSentimentText = (sentiment: string) => {
  const textMap = {
    positive: '正面',
    neutral: '中性',
    negative: '负面'
  }
  return textMap[sentiment as keyof typeof textMap] || sentiment
}

const getSentimentProgressColor = (sentiment: string) => {
  const colorMap = {
    positive: '#18a058',
    neutral: '#909399',
    negative: '#d03050'
  }
  return colorMap[sentiment as keyof typeof colorMap] || '#909399'
}

const highlightKeywords = (content: string) => {
  // 简单的关键词高亮实现
  let highlighted = content
  matchedKeywords.value.forEach(keyword => {
    const regex = new RegExp(keyword.word, 'gi')
    highlighted = highlighted.replace(regex, `**${keyword.word}**`)
  })
  return highlighted
}

const downloadFile = () => {
  messageApi.info('文件下载功能开发中')
}

const playVoice = () => {
  messageApi.info('语音播放功能开发中')
}

const markAsImportant = () => {
  messageApi.success('消息已标记为重要')
  emit('update')
}

const addToKeywords = () => {
  messageApi.info('添加关键词功能开发中')
}

const reportMessage = () => {
  messageApi.info('举报消息功能开发中')
}
</script>

<style scoped>
.message-detail-panel {
  padding: 16px;
}

.info-card,
.content-card,
.keyword-card,
.sentiment-card,
.related-card,
.action-card {
  margin-bottom: 16px;
}

.message-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #1a1a1a;
}

.message-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.content-text {
  font-size: 16px;
  line-height: 1.6;
  color: #1a1a1a;
  white-space: pre-wrap;
}

.content-media {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.media-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 8px;
  object-fit: cover;
}

.media-caption {
  color: #666;
  font-size: 14px;
}

.content-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #1a1a1a;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.content-voice {
  display: flex;
  align-items: center;
  gap: 12px;
}

.voice-player {
  display: flex;
  align-items: center;
  gap: 12px;
}

.voice-duration {
  color: #666;
  font-size: 14px;
}

.content-link {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.link-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.link-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.link-url {
  color: #2080f0;
  font-size: 12px;
}

.keyword-analysis {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.matched-keywords h4,
.keyword-context h4 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
}

.keyword-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.keyword-score {
  font-size: 12px;
  opacity: 0.8;
}

.context-text {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  line-height: 1.6;
  color: #1a1a1a;
}

.sentiment-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sentiment-overview {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 24px;
  align-items: center;
}

.sentiment-score {
  display: flex;
  justify-content: center;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.score-circle.sentiment-positive {
  background: linear-gradient(135deg, #18a058, #36ad6a);
}

.score-circle.sentiment-neutral {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.score-circle.sentiment-negative {
  background: linear-gradient(135deg, #d03050, #dd6b7f);
}

.score-number {
  font-size: 24px;
  font-weight: 600;
}

.score-label {
  font-size: 12px;
  opacity: 0.9;
}

.sentiment-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: grid;
  grid-template-columns: 80px 1fr;
  gap: 12px;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

.sentiment-breakdown h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.emotion-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.emotion-item {
  display: grid;
  grid-template-columns: 60px 1fr 50px;
  gap: 12px;
  align-items: center;
}

.emotion-label {
  font-size: 14px;
  color: #666;
}

.emotion-value {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  text-align: right;
}

.related-messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: 500;
  color: #1a1a1a;
}

.send-time {
  font-size: 12px;
  color: #999;
}

.message-text {
  color: #666;
  line-height: 1.4;
}

.no-related,
.no-actions {
  text-align: center;
  color: #999;
  padding: 24px;
}

.action-history {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.action-icon {
  margin-top: 2px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.action-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.action-time {
  color: #999;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}
</style>