<template>
  <div class="campaign-progress">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <p class="page-description">跟踪营销活动的执行进度，监控关键指标和里程碑</p>
      </div>
      <div class="header-right">
        <n-space>
          <n-button type="primary" @click="showCreateTaskModal = true">
            <template #icon>
              <n-icon><Add /></n-icon>
            </template>
            新建任务
          </n-button>
          <n-button type="info" @click="showReportModal = true">
            <template #icon>
              <n-icon><Analytics /></n-icon>
            </template>
            生成报告
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 活动选择器 -->
    <n-card class="campaign-selector">
      <n-space align="center">
        <span>选择活动：</span>
        <n-select
          v-model:value="selectedCampaignId"
          placeholder="请选择营销活动"
          style="width: 300px"
          :options="campaignOptions"
          :loading="campaignsLoading"
          @update:value="handleCampaignChange"
        />
        <n-button v-if="selectedCampaign" type="default" @click="refreshProgress">
          <template #icon>
            <n-icon><Refresh /></n-icon>
          </template>
          刷新
        </n-button>
      </n-space>
    </n-card>

    <!-- 活动概览 -->
    <div v-if="selectedCampaign" class="campaign-overview">
      <n-card>
        <template #header>
          <n-space justify="space-between">
            <span>{{ selectedCampaign.name }} - 活动概览</span>
            <n-tag :type="getStatusType(selectedCampaign.status)">{{ getStatusText(selectedCampaign.status) }}</n-tag>
          </n-space>
        </template>
        
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic label="活动进度" :value="progressStats.completion" suffix="%">
              <template #prefix>
                <n-icon color="#18a058">
                  <TrendingUp />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="已完成任务" :value="progressStats.completedTasks">
              <template #prefix>
                <n-icon color="#2080f0">
                  <CheckmarkCircle />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="进行中任务" :value="progressStats.activeTasks">
              <template #prefix>
                <n-icon color="#f0a020">
                  <Time />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="逾期任务" :value="progressStats.overdueTasks">
              <template #prefix>
                <n-icon color="#d03050">
                  <Warning />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
        </n-grid>
      </n-card>
    </div>

    <!-- 进度时间线 -->
    <div v-if="selectedCampaign" class="progress-timeline">
      <n-card>
        <template #header>
          <span>活动时间线</span>
        </template>
        
        <n-timeline>
          <n-timeline-item
            v-for="milestone in milestones"
            :key="milestone.id"
            :type="getMilestoneType(milestone.status)"
            :title="milestone.title"
            :time="formatDate(milestone.date)"
          >
            <template #icon>
              <n-icon>
                <component :is="getMilestoneIcon(milestone.type)" />
              </n-icon>
            </template>
            <div class="milestone-content">
              <p>{{ milestone.description }}</p>
              <div v-if="milestone.tasks && milestone.tasks.length > 0" class="milestone-tasks">
                <n-tag
                  v-for="task in milestone.tasks"
                  :key="task.id"
                  :type="getTaskType(task.status)"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px;"
                >
                  {{ task.name }}
                </n-tag>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </n-card>
    </div>

    <!-- 任务管理 -->
    <div v-if="selectedCampaign" class="task-management">
      <n-card>
        <template #header>
          <n-space justify="space-between">
            <span>任务管理</span>
            <n-space>
              <n-select
                v-model:value="taskFilter"
                placeholder="筛选任务"
                style="width: 120px"
                :options="taskFilterOptions"
                clearable
              />
              <n-button size="small" @click="loadTasks">
                <template #icon>
                  <n-icon><Refresh /></n-icon>
                </template>
                刷新
              </n-button>
            </n-space>
          </n-space>
        </template>
        
        <n-data-table
          :columns="taskColumns"
          :data="filteredTasks"
          :loading="tasksLoading"
          :pagination="false"
          :row-key="(row: Task) => row.id"
        />
      </n-card>
    </div>

    <!-- 创建任务弹窗 -->
    <n-modal v-model:show="showCreateTaskModal" preset="dialog" title="创建任务">
      <n-form ref="taskFormRef" :model="taskForm" :rules="taskRules" label-placement="left" label-width="80px">
        <n-form-item label="任务名称" path="name">
          <n-input v-model:value="taskForm.name" placeholder="请输入任务名称" />
        </n-form-item>
        <n-form-item label="任务描述" path="description">
          <n-input
            v-model:value="taskForm.description"
            type="textarea"
            placeholder="请输入任务描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="负责人" path="assignee">
          <n-select
            v-model:value="taskForm.assignee"
            placeholder="请选择负责人"
            :options="userOptions"
          />
        </n-form-item>
        <n-form-item label="优先级" path="priority">
          <n-select
            v-model:value="taskForm.priority"
            placeholder="请选择优先级"
            :options="priorityOptions"
          />
        </n-form-item>
        <n-form-item label="截止时间" path="dueDate">
          <n-date-picker
            v-model:value="taskForm.dueDate"
            type="datetime"
            placeholder="请选择截止时间"
            style="width: 100%"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showCreateTaskModal = false">取消</n-button>
          <n-button type="primary" @click="handleCreateTask">创建</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 报告生成弹窗 -->
    <n-modal v-model:show="showReportModal" preset="dialog" title="生成进度报告">
      <n-form ref="reportFormRef" :model="reportForm" label-placement="left" label-width="100px">
        <n-form-item label="报告类型">
          <n-radio-group v-model:value="reportForm.type">
            <n-radio value="summary">概览报告</n-radio>
            <n-radio value="detailed">详细报告</n-radio>
            <n-radio value="milestone">里程碑报告</n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="reportForm.dateRange"
            type="daterange"
            placeholder="选择时间范围"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="包含内容">
          <n-checkbox-group v-model:value="reportForm.includes">
            <n-checkbox value="tasks">任务详情</n-checkbox>
            <n-checkbox value="metrics">关键指标</n-checkbox>
            <n-checkbox value="issues">问题记录</n-checkbox>
            <n-checkbox value="recommendations">改进建议</n-checkbox>
          </n-checkbox-group>
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showReportModal = false">取消</n-button>
          <n-button type="primary" @click="handleGenerateReport">生成报告</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  Add,
  Analytics,
  Refresh,
  TrendingUp,
  CheckmarkCircle,
  Time,
  Warning,
  Flag,
  Rocket,
  Trophy
} from '@vicons/ionicons5'
import { useMarketingStore } from '@/stores/marketingStore'
import type { DataTableColumns } from 'naive-ui'

interface Campaign {
  id: number
  name: string
  status: string
  start_time: string
  end_time: string
}

interface Task {
  id: number
  name: string
  description: string
  status: string
  priority: string
  assignee: string
  dueDate: string
  createdAt: string
}

interface Milestone {
  id: number
  title: string
  description: string
  type: string
  status: string
  date: string
  tasks?: Task[]
}

const message = useMessage()
const marketingStore = useMarketingStore()

// 响应式数据
const selectedCampaignId = ref<number | null>(null)
const selectedCampaign = ref<Campaign | null>(null)
const campaignsLoading = ref(false)
const tasksLoading = ref(false)
const showCreateTaskModal = ref(false)
const showReportModal = ref(false)
const taskFilter = ref<string | null>(null)

// 表单数据
const taskForm = reactive({
  name: '',
  description: '',
  assignee: null as string | null,
  priority: 'medium',
  dueDate: null as number | null
})

const reportForm = reactive({
  type: 'summary',
  dateRange: null as [number, number] | null,
  includes: ['tasks', 'metrics']
})

// 模拟数据
const campaignOptions = ref([
  { label: '春季促销活动', value: 1 },
  { label: '新品发布会', value: 2 },
  { label: '会员专享活动', value: 3 }
])

const progressStats = ref({
  completion: 75,
  completedTasks: 12,
  activeTasks: 5,
  overdueTasks: 2
})

const milestones = ref<Milestone[]>([
  {
    id: 1,
    title: '活动策划完成',
    description: '完成活动方案设计和审批',
    type: 'planning',
    status: 'completed',
    date: '2024-01-15T10:00:00Z',
    tasks: [
      { id: 1, name: '方案设计', description: '', status: 'completed', priority: 'high', assignee: '张三', dueDate: '', createdAt: '' },
      { id: 2, name: '方案审批', description: '', status: 'completed', priority: 'medium', assignee: '李四', dueDate: '', createdAt: '' }
    ]
  },
  {
    id: 2,
    title: '物料准备',
    description: '完成活动所需物料的采购和制作',
    type: 'preparation',
    status: 'in_progress',
    date: '2024-01-20T10:00:00Z',
    tasks: [
      { id: 3, name: '海报设计', description: '', status: 'completed', priority: 'high', assignee: '王五', dueDate: '', createdAt: '' },
      { id: 4, name: '礼品采购', description: '', status: 'in_progress', priority: 'medium', assignee: '赵六', dueDate: '', createdAt: '' }
    ]
  },
  {
    id: 3,
    title: '活动执行',
    description: '正式启动营销活动',
    type: 'execution',
    status: 'pending',
    date: '2024-01-25T10:00:00Z'
  },
  {
    id: 4,
    title: '效果评估',
    description: '分析活动效果并生成报告',
    type: 'evaluation',
    status: 'pending',
    date: '2024-02-01T10:00:00Z'
  }
])

const tasks = ref<Task[]>([
  {
    id: 1,
    name: '活动页面设计',
    description: '设计活动落地页面',
    status: 'completed',
    priority: 'high',
    assignee: '张三',
    dueDate: '2024-01-20T18:00:00Z',
    createdAt: '2024-01-10T09:00:00Z'
  },
  {
    id: 2,
    name: '推广文案撰写',
    description: '撰写各渠道推广文案',
    status: 'in_progress',
    priority: 'medium',
    assignee: '李四',
    dueDate: '2024-01-22T18:00:00Z',
    createdAt: '2024-01-12T09:00:00Z'
  },
  {
    id: 3,
    name: '数据埋点配置',
    description: '配置活动数据追踪埋点',
    status: 'pending',
    priority: 'high',
    assignee: '王五',
    dueDate: '2024-01-24T18:00:00Z',
    createdAt: '2024-01-15T09:00:00Z'
  }
])

// 计算属性
const filteredTasks = computed(() => {
  if (!taskFilter.value) return tasks.value
  return tasks.value.filter(task => task.status === taskFilter.value)
})

// 选项数据
const taskFilterOptions = [
  { label: '全部', value: null },
  { label: '待开始', value: 'pending' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已逾期', value: 'overdue' }
]

const userOptions = [
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: '赵六', value: 'zhaoliu' }
]

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 表单验证规则
const taskRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  assignee: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 表格列定义
const taskColumns: DataTableColumns<Task> = [
  {
    title: '任务名称',
    key: 'name',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h('n-tag', { type: getTaskType(row.status) }, { default: () => getTaskStatusText(row.status) })
    }
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    render(row) {
      return h('n-tag', { type: getPriorityType(row.priority) }, { default: () => getPriorityText(row.priority) })
    }
  },
  {
    title: '负责人',
    key: 'assignee',
    width: 100
  },
  {
    title: '截止时间',
    key: 'dueDate',
    width: 150,
    render(row) {
      return formatDate(row.dueDate)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h('n-space', {}, {
        default: () => [
          h('n-button', {
            size: 'small',
            onClick: () => handleEditTask(row)
          }, { default: () => '编辑' }),
          h('n-button', {
            size: 'small',
            type: 'success',
            disabled: row.status === 'completed',
            onClick: () => handleCompleteTask(row)
          }, { default: () => '完成' })
        ]
      })
    }
  }
]

// 方法
const handleCampaignChange = (campaignId: number) => {
  const campaign = campaignOptions.value.find(c => c.value === campaignId)
  if (campaign) {
    selectedCampaign.value = {
      id: campaignId,
      name: campaign.label,
      status: 'active',
      start_time: '2024-01-15T00:00:00Z',
      end_time: '2024-02-15T00:00:00Z'
    }
    loadTasks()
  }
}

const refreshProgress = () => {
  loadTasks()
  message.success('进度已刷新')
}

const loadTasks = async () => {
  tasksLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    message.error('加载任务失败')
  } finally {
    tasksLoading.value = false
  }
}

const handleCreateTask = async () => {
  try {
    // 模拟创建任务
    const newTask: Task = {
      id: Date.now(),
      name: taskForm.name,
      description: taskForm.description,
      status: 'pending',
      priority: taskForm.priority,
      assignee: taskForm.assignee || '',
      dueDate: taskForm.dueDate ? new Date(taskForm.dueDate).toISOString() : '',
      createdAt: new Date().toISOString()
    }
    
    tasks.value.push(newTask)
    message.success('任务创建成功')
    showCreateTaskModal.value = false
    
    // 重置表单
    Object.assign(taskForm, {
      name: '',
      description: '',
      assignee: null,
      priority: 'medium',
      dueDate: null
    })
  } catch (error) {
    message.error('创建任务失败')
  }
}

const handleEditTask = (task: Task) => {
  message.info('编辑任务功能开发中')
}

const handleCompleteTask = (task: Task) => {
  task.status = 'completed'
  message.success('任务已完成')
}

const handleGenerateReport = async () => {
  try {
    // 模拟生成报告
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('报告生成成功')
    showReportModal.value = false
  } catch (error) {
    message.error('生成报告失败')
  }
}

// 辅助函数
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'default',
    active: 'success',
    paused: 'warning',
    ended: 'info',
    cancelled: 'error'
  }
  return typeMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    active: '进行中',
    paused: '已暂停',
    ended: '已结束',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

const getMilestoneType = (status: string) => {
  const typeMap: Record<string, string> = {
    completed: 'success',
    in_progress: 'info',
    pending: 'default'
  }
  return typeMap[status] || 'default'
}

const getMilestoneIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    planning: Flag,
    preparation: Rocket,
    execution: CheckmarkCircle,
    evaluation: Trophy
  }
  return iconMap[type] || Flag
}

const getTaskType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'default',
    in_progress: 'info',
    completed: 'success',
    overdue: 'error'
  }
  return typeMap[status] || 'default'
}

const getTaskStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  }
  return typeMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[priority] || priority
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.campaign-selector {
  margin-bottom: 20px;
}

.campaign-overview {
  margin-bottom: 20px;
}

.progress-timeline {
  margin-bottom: 20px;
}

.milestone-content {
  margin-top: 8px;
}

.milestone-tasks {
  margin-top: 8px;
}

.task-management {
  margin-bottom: 20px;
}
</style>