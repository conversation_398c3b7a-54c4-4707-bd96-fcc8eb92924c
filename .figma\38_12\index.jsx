import React from 'react';

import styles from './index.module.scss';

const Component = () => {
  return (
    <div className={styles.frame}>
      <div className={styles.rectangle28}>
        <div className={styles.autoWrapper}>
          <div className={styles.ellipse1} />
          <p className={styles.i}>i</p>
        </div>
        <p className={styles.text}>新增客户</p>
        <p className={styles.x}>X</p>
      </div>
      <div className={styles.autoWrapper3}>
        <p className={styles.text2}>客户姓名</p>
        <p className={styles.a}>*</p>
        <div className={styles.rectangle12} />
        <p className={styles.text3}>性 别</p>
        <div className={styles.autoWrapper2}>
          <div className={styles.rectangle14} />
          <p className={styles.a2}>&#62;</p>
        </div>
      </div>
      <div className={styles.autoWrapper4}>
        <p className={styles.text4}>手机号码</p>
        <p className={styles.a3}>*</p>
        <div className={styles.rectangle12} />
        <p className={styles.text5}>小区名称</p>
        <p className={styles.a3}>*</p>
        <div className={styles.rectangle12} />
      </div>
      <div className={styles.autoWrapper6}>
        <p className={styles.text6}>装修类型</p>
        <p className={styles.a4}>*</p>
        <div className={styles.autoWrapper5}>
          <div className={styles.rectangle16} />
          <p className={styles.a5}>&#62;</p>
        </div>
        <p className={styles.text7}>房屋面积</p>
        <div className={styles.rectangle18} />
        <p className={styles.a6}>㎡</p>
      </div>
      <div className={styles.autoWrapper8}>
        <p className={styles.text6}>客户来源</p>
        <p className={styles.a7}>*</p>
        <div className={styles.autoWrapper5}>
          <div className={styles.rectangle16} />
          <p className={styles.a5}>&#62;</p>
        </div>
        <p className={styles.text8}>客户等级</p>
        <div className={styles.autoWrapper7}>
          <div className={styles.rectangle16} />
          <p className={styles.a8}>&#62;</p>
        </div>
      </div>
      <div className={styles.autoWrapper10}>
        <p className={styles.text6}>交房情况</p>
        <div className={styles.autoWrapper9}>
          <div className={styles.rectangle16} />
          <p className={styles.a9}>&#62;</p>
        </div>
        <p className={styles.text8}>重点客户</p>
        <div className={styles.rectangle25}>
          <div className={styles.ellipse3} />
        </div>
        <div className={styles.rectangle27}>
          <div className={styles.ellipse4} />
        </div>
      </div>
      <div className={styles.autoWrapper11}>
        <p className={styles.text4}>客户标签</p>
        <div className={styles.rectangle23} />
      </div>
      <div className={styles.autoWrapper12}>
        <p className={styles.text4}>备注信息</p>
        <div className={styles.rectangle20} />
      </div>
      <div className={styles.autoWrapper13}>
        <div className={styles.rectangle21}>
          <p className={styles.text9}>取消</p>
        </div>
        <div className={styles.rectangle22}>
          <p className={styles.text10}>创建</p>
        </div>
      </div>
      <p className={styles.a10}>+</p>
    </div>
  );
}

export default Component;
