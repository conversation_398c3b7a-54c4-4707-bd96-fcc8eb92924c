const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件（临时禁用）
// router.use(authenticateToken);

// 获取部门列表
router.get('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, pageSize = 10, keyword, status } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (d.name LIKE ? OR d.description LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND d.status = ?';
      whereParams.push(status);
    }

    const sql = `
      SELECT 
        d.*,
        COUNT(u.id) as user_count
      FROM departments d
      LEFT JOIN users u ON d.id = u.department_id AND u.status != 'deleted'
      WHERE ${whereClause}
      GROUP BY d.id
      ORDER BY d.sort_order ASC, d.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countSql = `
      SELECT COUNT(*) as total
      FROM departments d
      WHERE ${whereClause}
    `;

    const offset = (page - 1) * pageSize;
    const departments = await db.query(sql, [...whereParams, parseInt(pageSize), offset]);
    const countResult = await db.query(countSql, whereParams);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: departments.rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取部门列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取部门列表失败',
      data: []
    });
  }
});

// 获取部门详情
router.get('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const sql = `
      SELECT 
        d.*,
        COUNT(u.id) as user_count
      FROM departments d
      LEFT JOIN users u ON d.id = u.department_id AND u.status != 'deleted'
      WHERE d.id = ?
      GROUP BY d.id
    `;

    const result = await db.query(sql, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const department = result.rows[0];

    // 获取部门用户列表
    const usersSql = `
      SELECT id, name, role, position, mobile as phone, email, status
      FROM users
      WHERE department_id = ? AND status = 1
      ORDER BY role ASC, name ASC
    `;

    const usersResult = await db.query(usersSql, [id]);
    department.users = usersResult.rows;

    res.json({
      success: true,
      data: department
    });

  } catch (error) {
    console.error('获取部门详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取部门详情失败'
    });
  }
});

// 创建部门
router.post('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { name, description, manager_id, sort_order } = req.body;

    // 验证必填字段
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '部门名称为必填项'
      });
    }

    // 检查部门名称是否已存在
    const nameCheck = await db.query('SELECT id FROM departments WHERE name = ?', [name]);
    if (nameCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '部门名称已存在'
      });
    }

    // 检查管理员是否存在
    if (manager_id) {
      const managerCheck = await db.query('SELECT id FROM users WHERE id = ? AND status = 1', [manager_id]);
      if (managerCheck.rows.length === 0) {
        return res.status(400).json({
          success: false,
          message: '指定的管理员不存在或已禁用'
        });
      }
    }

    const departmentData = {
      name,
      description: description || null,
      manager_id: manager_id || null,
      sort_order: sort_order || 0,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const insertSql = `
      INSERT INTO departments (
        name, description, manager_id, sort_order, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      departmentData.name,
      departmentData.description,
      departmentData.manager_id,
      departmentData.sort_order,
      departmentData.status,
      departmentData.created_at,
      departmentData.updated_at
    ];

    const result = await db.query(insertSql, insertParams);

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'create',
        'department',
        result.lastID,
        `创建部门：${name}`,
        new Date().toISOString()
      ]
    );

    res.status(201).json({
      success: true,
      message: '部门创建成功',
      data: {
        id: result.lastID,
        ...departmentData
      }
    });

  } catch (error) {
    console.error('创建部门错误:', error);
    res.status(500).json({
      success: false,
      message: '创建部门失败'
    });
  }
});

// 更新部门
router.put('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { name, description, manager_id, sort_order, status } = req.body;

    // 检查部门是否存在
    const departmentCheck = await db.query('SELECT * FROM departments WHERE id = ?', [id]);
    if (departmentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const currentDepartment = departmentCheck.rows[0];

    // 检查部门名称是否被其他部门使用
    if (name && name !== currentDepartment.name) {
      const nameCheck = await db.query('SELECT id FROM departments WHERE name = ? AND id != ?', [name, id]);
      if (nameCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          message: '部门名称已被其他部门使用'
        });
      }
    }

    // 检查管理员是否存在
    if (manager_id && manager_id !== currentDepartment.manager_id) {
      const managerCheck = await db.query('SELECT id FROM users WHERE id = ? AND status = "active"', [manager_id]);
      if (managerCheck.rows.length === 0) {
        return res.status(400).json({
          success: false,
          message: '指定的管理员不存在或已禁用'
        });
      }
    }

    // 构建更新数据
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (manager_id !== undefined) updateData.manager_id = manager_id;
    if (sort_order !== undefined) updateData.sort_order = sort_order;
    if (status) updateData.status = status;

    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    const updateSql = `UPDATE departments SET ${updateFields} WHERE id = ?`;
    await db.query(updateSql, [...updateValues, id]);

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'update',
        'department',
        id,
        `更新部门：${name || currentDepartment.name}`,
        new Date().toISOString()
      ]
    );

    res.json({
      success: true,
      message: '部门更新成功'
    });

  } catch (error) {
    console.error('更新部门错误:', error);
    res.status(500).json({
      success: false,
      message: '更新部门失败'
    });
  }
});

// 删除部门（软删除）
router.delete('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查部门是否存在
    const departmentCheck = await db.query('SELECT * FROM departments WHERE id = ?', [id]);
    if (departmentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const department = departmentCheck.rows[0];

    // 检查部门下是否有用户
    const userCheck = await db.query('SELECT COUNT(*) as count FROM users WHERE department_id = ? AND status != "deleted"', [id]);
    if (userCheck.rows[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: '部门下还有用户，无法删除'
      });
    }

    // 软删除
    await db.query(
      'UPDATE departments SET status = ?, deleted_at = ?, updated_at = ? WHERE id = ?',
      ['deleted', new Date().toISOString(), new Date().toISOString(), id]
    );

    // 记录操作日志
    await db.query(
      'INSERT INTO operation_logs (user_id, action, target_type, target_id, details, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [
        1, // 临时使用默认用户ID
        'delete',
        'department',
        id,
        `删除部门：${department.name}`,
        new Date().toISOString()
      ]
    );

    res.json({
      success: true,
      message: '部门删除成功'
    });

  } catch (error) {
    console.error('删除部门错误:', error);
    res.status(500).json({
      success: false,
      message: '删除部门失败'
    });
  }
});

// 获取部门统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const db = req.app.locals.db;

    const stats = await Promise.all([
      // 总部门数
      db.query('SELECT COUNT(*) as total FROM departments WHERE status != "deleted"'),
      // 活跃部门数
      db.query('SELECT COUNT(*) as active FROM departments WHERE status = "active"'),
      // 各部门用户数统计
      db.query(`
        SELECT 
          d.name as department_name,
          COUNT(u.id) as user_count
        FROM departments d
        LEFT JOIN users u ON d.id = u.department_id AND u.status != "deleted"
        WHERE d.status != "deleted"
        GROUP BY d.id, d.name
        ORDER BY user_count DESC
      `)
    ]);

    res.json({
      success: true,
      data: {
        total: stats[0].rows[0].total,
        active: stats[1].rows[0].active,
        departmentStats: stats[2].rows
      }
    });

  } catch (error) {
    console.error('获取部门统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取部门统计失败'
    });
  }
});

module.exports = router;