<template>
  <div class="sales-funnel-analysis">
    <!-- 分析配置 -->
    <n-card title="漏斗分析配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>漏斗类型</label>
            <n-select v-model:value="funnelConfig.type" :options="funnelTypeOptions" placeholder="选择漏斗类型" />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="funnelConfig.dateRange" type="daterange" clearable />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>客户来源</label>
            <n-select v-model:value="funnelConfig.source" :options="sourceOptions" multiple placeholder="选择客户来源" />
          </div>
          <div class="form-item">
            <label>产品类别</label>
            <n-select v-model:value="funnelConfig.category" :options="categoryOptions" multiple placeholder="选择产品类别" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 漏斗概览 -->
    <n-card title="销售漏斗概览" class="overview-card">
      <div class="funnel-overview">
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#2080f0"><EyeOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ funnelOverview.totalVisitors }}</div>
              <div class="stat-label">总访问量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#18a058"><PersonOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ funnelOverview.totalLeads }}</div>
              <div class="stat-label">潜在客户</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#f0a020"><CartOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ funnelOverview.totalOrders }}</div>
              <div class="stat-label">订单数量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#d03050"><TrendingUpOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ funnelOverview.conversionRate }}%</div>
              <div class="stat-label">总转化率</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 漏斗图表 -->
    <n-card title="销售漏斗图" class="funnel-chart-card">
      <div class="chart-container">
        <div ref="funnelChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 转化步骤分析 -->
    <n-card title="转化步骤详细分析" class="steps-card">
      <div class="steps-analysis">
        <div class="step-item" v-for="(step, index) in funnelSteps" :key="step.name">
          <div class="step-header">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-info">
              <div class="step-name">{{ step.name }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
            <div class="step-stats">
              <div class="step-count">{{ step.count }}</div>
              <div class="step-rate">{{ step.conversionRate }}%</div>
            </div>
          </div>
          
          <div class="step-details">
            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">流入用户:</span>
                <span class="detail-value">{{ step.inflow }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">流出用户:</span>
                <span class="detail-value">{{ step.outflow }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">流失率:</span>
                <span class="detail-value text-error">{{ step.dropRate }}%</span>
              </div>
            </div>
            
            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">平均停留时间:</span>
                <span class="detail-value">{{ step.avgDuration }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">跳出率:</span>
                <span class="detail-value">{{ step.bounceRate }}%</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">优化潜力:</span>
                <span class="detail-value" :class="step.optimizationPotential === 'high' ? 'text-error' : step.optimizationPotential === 'medium' ? 'text-warning' : 'text-success'">
                  {{ step.optimizationPotential === 'high' ? '高' : step.optimizationPotential === 'medium' ? '中' : '低' }}
                </span>
              </div>
            </div>
          </div>
          
          <div v-if="index < funnelSteps.length - 1" class="step-connector">
            <div class="connector-line"></div>
            <div class="connector-rate">{{ step.nextStepRate }}%</div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化趋势分析 -->
    <n-card title="转化趋势分析" class="trend-card">
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 优化建议 -->
    <n-card title="漏斗优化建议" class="suggestions-card">
      <div class="suggestions-list">
        <div class="suggestion-item" v-for="suggestion in optimizationSuggestions" :key="suggestion.id">
          <div class="suggestion-icon">
            <n-icon :color="suggestion.impact === 'high' ? '#d03050' : suggestion.impact === 'medium' ? '#f0a020' : '#18a058'">
              <BulbOutline />
            </n-icon>
          </div>
          <div class="suggestion-content">
            <div class="suggestion-title">{{ suggestion.title }}</div>
            <div class="suggestion-description">{{ suggestion.description }}</div>
            <div class="suggestion-steps">
              <div class="suggestion-step" v-for="(step, index) in suggestion.steps" :key="index">
                {{ index + 1 }}. {{ step }}
              </div>
            </div>
            <div class="suggestion-impact">预期提升: {{ suggestion.expectedImprovement }}</div>
          </div>
          <div class="suggestion-priority">
            <n-tag :type="suggestion.impact === 'high' ? 'error' : suggestion.impact === 'medium' ? 'warning' : 'success'">
              {{ suggestion.impact === 'high' ? '高影响' : suggestion.impact === 'medium' ? '中影响' : '低影响' }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NTag
} from 'naive-ui'
import {
  EyeOutline, PersonOutline, CartOutline, TrendingUpOutline, BulbOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const funnelChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 漏斗配置
const funnelConfig = reactive({
  type: 'sales',
  dateRange: null,
  source: [],
  category: []
})

// 漏斗概览数据
const funnelOverview = ref({
  totalVisitors: 12580,
  totalLeads: 3247,
  totalOrders: 856,
  conversionRate: 6.8
})

// 漏斗步骤数据
const funnelSteps = ref([
  {
    name: '访问网站',
    description: '用户首次访问网站或落地页',
    count: 12580,
    inflow: 12580,
    outflow: 9333,
    conversionRate: 100,
    dropRate: 25.8,
    nextStepRate: 74.2,
    avgDuration: '2分35秒',
    bounceRate: 45.2,
    optimizationPotential: 'medium'
  },
  {
    name: '浏览产品',
    description: '用户查看产品详情页面',
    count: 9333,
    inflow: 9333,
    outflow: 5600,
    conversionRate: 74.2,
    dropRate: 40.0,
    nextStepRate: 60.0,
    avgDuration: '4分12秒',
    bounceRate: 35.8,
    optimizationPotential: 'high'
  },
  {
    name: '加入购物车',
    description: '用户将产品添加到购物车',
    count: 5600,
    inflow: 5600,
    outflow: 2376,
    conversionRate: 44.5,
    dropRate: 57.6,
    nextStepRate: 42.4,
    avgDuration: '1分48秒',
    bounceRate: 52.3,
    optimizationPotential: 'high'
  },
  {
    name: '填写信息',
    description: '用户填写订单和配送信息',
    count: 2376,
    inflow: 2376,
    outflow: 1520,
    conversionRate: 18.9,
    dropRate: 36.0,
    nextStepRate: 64.0,
    avgDuration: '3分25秒',
    bounceRate: 28.7,
    optimizationPotential: 'medium'
  },
  {
    name: '完成支付',
    description: '用户成功完成订单支付',
    count: 856,
    inflow: 1520,
    outflow: 0,
    conversionRate: 6.8,
    dropRate: 43.7,
    nextStepRate: 56.3,
    avgDuration: '2分15秒',
    bounceRate: 15.2,
    optimizationPotential: 'high'
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    title: '优化产品页面体验',
    description: '产品浏览到加入购物车的转化率较低，需要优化产品页面的用户体验。',
    steps: [
      '增加高质量的产品图片和视频',
      '优化产品描述和卖点展示',
      '添加用户评价和推荐',
      '简化加入购物车的操作流程'
    ],
    expectedImprovement: '转化率提升15-20%',
    impact: 'high'
  },
  {
    id: 2,
    title: '减少支付流程摩擦',
    description: '支付环节流失率较高，需要简化支付流程，提升支付成功率。',
    steps: [
      '支持多种支付方式',
      '简化表单填写步骤',
      '增加支付安全提示',
      '优化移动端支付体验'
    ],
    expectedImprovement: '支付成功率提升25-30%',
    impact: 'high'
  },
  {
    id: 3,
    title: '购物车挽回策略',
    description: '针对加入购物车但未完成购买的用户，实施挽回策略。',
    steps: [
      '发送购物车提醒邮件',
      '提供限时优惠券',
      '推送相关产品推荐',
      '简化结算流程'
    ],
    expectedImprovement: '购物车转化率提升10-15%',
    impact: 'medium'
  },
  {
    id: 4,
    title: '提升网站加载速度',
    description: '网站访问到浏览产品的转化率有提升空间，优化页面加载速度。',
    steps: [
      '优化图片和资源加载',
      '使用CDN加速',
      '压缩CSS和JavaScript',
      '优化服务器响应时间'
    ],
    expectedImprovement: '页面转化率提升8-12%',
    impact: 'medium'
  }
])

// 选项数据
const funnelTypeOptions = [
  { label: '销售漏斗', value: 'sales' },
  { label: '注册漏斗', value: 'registration' },
  { label: '营销漏斗', value: 'marketing' },
  { label: '客服漏斗', value: 'support' }
]

const sourceOptions = [
  { label: '搜索引擎', value: 'search' },
  { label: '社交媒体', value: 'social' },
  { label: '直接访问', value: 'direct' },
  { label: '邮件营销', value: 'email' },
  { label: '广告投放', value: 'ads' }
]

const categoryOptions = [
  { label: '电子产品', value: 'electronics' },
  { label: '服装配饰', value: 'fashion' },
  { label: '家居用品', value: 'home' },
  { label: '运动户外', value: 'sports' },
  { label: '美妆护肤', value: 'beauty' }
]

// 图表渲染方法
const renderFunnelChart = () => {
  if (!funnelChartRef.value) return
  
  const chart = echarts.init(funnelChartRef.value)
  
  const option = {
    title: {
      text: '销售转化漏斗',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: funnelSteps.value.map(step => ({
          value: step.conversionRate,
          name: `${step.name} (${step.count})`,
          itemStyle: {
            color: step.optimizationPotential === 'high' ? '#ff4d4f' : 
                   step.optimizationPotential === 'medium' ? '#faad14' : '#52c41a'
          }
        }))
      }
    ]
  }
  
  chart.setOption(option)
}

const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const visitData = [10200, 11500, 12800, 11200, 13500, 14200, 12800, 15600, 13200, 16800, 14500, 12580]
  const leadData = [2550, 2875, 3200, 2800, 3375, 3550, 3200, 3900, 3300, 4200, 3625, 3247]
  const orderData = [680, 765, 852, 746, 899, 946, 852, 1040, 880, 1120, 966, 856]
  
  const option = {
    title: {
      text: '转化趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['访问量', '潜在客户', '订单数'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '转化率(%)',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '访问量',
        type: 'bar',
        data: visitData,
        itemStyle: { color: '#2080f0' }
      },
      {
        name: '潜在客户',
        type: 'bar',
        data: leadData,
        itemStyle: { color: '#18a058' }
      },
      {
        name: '订单数',
        type: 'bar',
        data: orderData,
        itemStyle: { color: '#f0a020' }
      },
      {
        name: '转化率',
        type: 'line',
        yAxisIndex: 1,
        data: orderData.map((order, index) => ((order / visitData[index]) * 100).toFixed(1)),
        lineStyle: { color: '#d03050', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }
  
  chart.setOption(option)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  renderFunnelChart()
  renderTrendChart()
})
</script>

<style scoped>
.sales-funnel-analysis {
  padding: 24px;
}

.config-card,
.overview-card,
.funnel-chart-card,
.steps-card,
.trend-card,
.suggestions-card {
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.steps-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-item {
  position: relative;
  padding: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.step-info {
  flex: 1;
}

.step-name {
  font-weight: 600;
  font-size: 18px;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.step-description {
  color: #666;
  font-size: 14px;
}

.step-stats {
  text-align: right;
}

.step-count {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.step-rate {
  font-size: 14px;
  color: #2080f0;
  font-weight: 500;
}

.step-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.detail-label {
  color: #666;
}

.detail-value {
  font-weight: 500;
  color: #1a1a1a;
}

.detail-value.text-error {
  color: #d03050;
}

.detail-value.text-warning {
  color: #f0a020;
}

.detail-value.text-success {
  color: #18a058;
}

.step-connector {
  position: absolute;
  bottom: -24px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.connector-line {
  width: 2px;
  height: 20px;
  background: #2080f0;
}

.connector-rate {
  background: #2080f0;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.suggestion-icon {
  font-size: 24px;
  margin-top: 4px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  font-size: 16px;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.suggestion-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.suggestion-steps {
  margin-bottom: 12px;
}

.suggestion-step {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.suggestion-impact {
  font-size: 14px;
  color: #18a058;
  font-weight: 500;
}

.suggestion-priority {
  margin-top: 4px;
}
</style>