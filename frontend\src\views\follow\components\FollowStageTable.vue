<template>
  <div class="follow-stage-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <n-space>
          <n-button type="info" ghost>
            <template #icon>
              <n-icon><filter-outline /></n-icon>
            </template>
            筛选
          </n-button>
        </n-space>
      </div>
      <div class="toolbar-right">
        <n-space>
          <n-select
            v-model:value="subStageFilter"
            placeholder="子阶段筛选"
            clearable
            style="width: 150px"
            :options="subStageOptions"
            @update:value="handleSubStageFilter"
          />
        </n-space>
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: FollowRecord) => row.id"
      remote
      @update:page="$emit('page-change', $event)"
      @update:page-size="$emit('page-size-change', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, markRaw } from 'vue'
import {
  FilterOutline,
  CreateOutline,
  EyeOutline
} from '@vicons/ionicons5'
import type { FollowRecord } from '@/types'
import type { DataTableColumns } from 'naive-ui'

interface Props {
  data: FollowRecord[]
  loading: boolean
  pagination: any
}

interface Emits {
  edit: [record: FollowRecord]
  view: [record: FollowRecord]
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
}

// 获取props和emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 子阶段筛选
const subStageFilter = ref<string | null>(null)

// 子阶段选项
const subStageOptions = [
  { label: '初次联系', value: 'initial' },
  { label: '跟进中', value: 'follow_up' },
  { label: '有意向', value: 'interested' },
  { label: '考虑中', value: 'considering' }
]

// 跟进方式选项
const typeOptions = [
  { label: '电话沟通', value: 'phone' },
  { label: '微信沟通', value: 'wechat' },
  { label: '邮件沟通', value: 'email' },
  { label: '面谈', value: 'meeting' },
  { label: '其他', value: 'other' }
]

// 跟进状态选项
const statusOptions = [
  { label: '已联系', value: 'contacted' },
  { label: '有意向', value: 'interested' },
  { label: '无意向', value: 'not_interested' },
  { label: '待跟进', value: 'pending' },
  { label: '已成交', value: 'closed' }
]

// 过滤后的数据
const filteredData = computed(() => {
  let result = props.data
  
  if (subStageFilter.value) {
    result = result.filter(record => record.subStage === subStageFilter.value)
  }
  
  return result
})

// 表格列配置
const columns: DataTableColumns<FollowRecord> = [
  {
    title: '客户姓名',
    key: 'customerName',
    width: 120,
    render: (row) => row.customerName || row.customer_name
  },
  {
    title: '子阶段',
    key: 'subStage',
    width: 100,
    render: (row) => {
      const subStage = subStageOptions.find(item => item.value === row.subStage)
      return h(
        'n-tag',
        { type: 'info', size: 'small' },
        { default: () => subStage?.label || row.subStage || '未设置' }
      )
    }
  },
  {
    title: '跟进方式',
    key: 'type',
    width: 100,
    render: (row) => {
      const type = typeOptions.find(item => item.value === row.type)
      return type?.label || row.type
    }
  },
  {
    title: '跟进状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const status = statusOptions.find(item => item.value === row.status)
      const statusMap: Record<string, string> = {
        contacted: 'info',
        interested: 'success',
        not_interested: 'warning',
        pending: 'default',
        closed: 'success'
      }
      return h(
        'n-tag',
        { type: statusMap[row.status] || 'default', size: 'small' },
        { default: () => status?.label || row.status }
      )
    }
  },
  {
    title: '跟进时间',
    key: 'followTime',
    width: 160,
    render: (row) => new Date(row.followTime || row.follow_time).toLocaleString()
  },
  {
    title: '跟进内容',
    key: 'content',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '下次跟进',
    key: 'nextFollowTime',
    width: 160,
    render: (row) => row.nextFollowTime || row.next_follow_time ? 
      new Date(row.nextFollowTime || row.next_follow_time!).toLocaleString() : '-'
  },
  {
    title: '跟进人',
    key: 'followBy',
    width: 100,
    render: (row) => row.followBy || row.follow_by || '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: (row) => {
      return [
        h(
          'n-button',
          {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => emit('view', row)
          },
          { 
            default: () => '查看', 
            icon: () => h('n-icon', null, { default: () => h(markRaw(EyeOutline)) }) 
          }
        ),
        h(
          'n-button',
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            style: { marginLeft: '8px' },
            onClick: () => emit('edit', row)
          },
          { 
            default: () => '编辑', 
            icon: () => h('n-icon', null, { default: () => h(markRaw(CreateOutline)) }) 
          }
        )
      ]
    }
  }
]

// 方法
const handleSubStageFilter = () => {
  // 触发父组件重新获取数据
}
</script>

<style scoped>
.follow-stage-table {
  background: white;
  border-radius: 8px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

:deep(.n-data-table) {
  border-radius: 8px;
}

:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f0f8ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
</style>