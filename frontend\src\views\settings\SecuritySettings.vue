<template>
  <div class="security-settings">
    <n-form
      ref="securityFormRef"
      :model="securitySettings"
      :rules="securityRules"
      label-placement="left"
      label-width="150px"
      class="settings-form"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="密码最小长度" path="minPasswordLength">
          <n-input-number
            v-model:value="securitySettings.minPasswordLength"
            :min="6"
            :max="20"
            placeholder="请输入密码最小长度"
            style="width: 100%"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="密码有效期(天)" path="passwordExpireDays">
          <n-input-number
            v-model:value="securitySettings.passwordExpireDays"
            :min="30"
            :max="365"
            placeholder="请输入密码有效期"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="登录失败次数" path="maxLoginAttempts">
          <n-input-number
            v-model:value="securitySettings.maxLoginAttempts"
            :min="3"
            :max="10"
            placeholder="请输入最大登录失败次数"
            style="width: 100%"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="账户锁定时间(分钟)" path="lockoutDuration">
          <n-input-number
            v-model:value="securitySettings.lockoutDuration"
            :min="5"
            :max="60"
            placeholder="请输入账户锁定时间"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="会话超时(分钟)" path="sessionTimeout">
          <n-input-number
            v-model:value="securitySettings.sessionTimeout"
            :min="30"
            :max="480"
            placeholder="请输入会话超时时间"
            style="width: 100%"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="强制HTTPS">
          <n-switch v-model:value="securitySettings.forceHttps" />
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="启用验证码">
        <n-checkbox-group v-model:value="securitySettings.captchaEnabled">
          <n-space>
            <n-checkbox value="login">登录</n-checkbox>
            <n-checkbox value="register">注册</n-checkbox>
            <n-checkbox value="reset_password">重置密码</n-checkbox>
          </n-space>
        </n-checkbox-group>
      </n-form-item>
      
      <n-form-item label="IP白名单">
        <n-dynamic-tags
          v-model:value="securitySettings.ipWhitelist"
          placeholder="请输入IP地址"
        />
      </n-form-item>
      
      <n-form-item label="双因子认证">
        <n-space vertical>
          <n-switch v-model:value="securitySettings.twoFactorEnabled" />
          <n-text depth="3" style="font-size: 12px;">
            启用后，用户登录时需要输入手机验证码或邮箱验证码
          </n-text>
        </n-space>
      </n-form-item>
      
      <n-form-item>
        <n-space>
          <n-button type="primary" @click="handleSave">
            <template #icon>
              <n-icon><save-outline /></n-icon>
            </template>
            保存设置
          </n-button>
          <n-button type="default" @click="handleReset">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { 
  NForm, NFormItem, NFormItemGi, NGrid, NInputNumber, NButton, NIcon, NSpace, 
  NSwitch, NCheckboxGroup, NCheckbox, NDynamicTags, NText, useMessage 
} from 'naive-ui'
import { SaveOutline, RefreshOutline } from '@vicons/ionicons5'
import { createDiscreteApi } from 'naive-ui'

const { message } = createDiscreteApi(['message'])

interface SecuritySettings {
  minPasswordLength: number
  passwordExpireDays: number
  maxLoginAttempts: number
  lockoutDuration: number
  sessionTimeout: number
  forceHttps: boolean
  captchaEnabled: string[]
  ipWhitelist: string[]
  twoFactorEnabled: boolean
}

const securityFormRef = ref()
const securitySettings = reactive<SecuritySettings>({
  minPasswordLength: 8,
  passwordExpireDays: 90,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  sessionTimeout: 120,
  forceHttps: false,
  captchaEnabled: ['login'],
  ipWhitelist: [],
  twoFactorEnabled: false
})

const securityRules = {
  minPasswordLength: {
    required: true,
    type: 'number' as const,
    min: 6,
    max: 20,
    message: '密码最小长度必须在6-20之间',
    trigger: 'blur'
  },
  passwordExpireDays: {
    required: true,
    type: 'number' as const,
    min: 30,
    max: 365,
    message: '密码有效期必须在30-365天之间',
    trigger: 'blur'
  },
  maxLoginAttempts: {
    required: true,
    type: 'number' as const,
    min: 3,
    max: 10,
    message: '登录失败次数必须在3-10次之间',
    trigger: 'blur'
  },
  lockoutDuration: {
    required: true,
    type: 'number' as const,
    min: 5,
    max: 60,
    message: '账户锁定时间必须在5-60分钟之间',
    trigger: 'blur'
  },
  sessionTimeout: {
    required: true,
    type: 'number' as const,
    min: 30,
    max: 480,
    message: '会话超时时间必须在30-480分钟之间',
    trigger: 'blur'
  }
}

const handleSave = async () => {
  try {
    await securityFormRef.value?.validate()
    // TODO: 调用API保存设置
    message.success('安全设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const handleReset = () => {
  securityFormRef.value?.restoreValidation()
  Object.assign(securitySettings, {
    minPasswordLength: 8,
    passwordExpireDays: 90,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    sessionTimeout: 120,
    forceHttps: false,
    captchaEnabled: ['login'],
    ipWhitelist: [],
    twoFactorEnabled: false
  })
}

onMounted(() => {
  // TODO: 从API加载设置数据
})
</script>

<style scoped>
.security-settings {
  padding: 24px;
}

.settings-form {
  max-width: 800px;
}
</style>