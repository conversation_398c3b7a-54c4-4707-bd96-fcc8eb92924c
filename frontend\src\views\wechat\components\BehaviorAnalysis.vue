<template>
  <div class="behavior-analysis">
    <!-- 分析配置 -->
    <div class="config-section">
      <n-form inline :model="configForm" label-placement="left">
        <n-form-item label="分析维度">
          <n-select
            v-model:value="configForm.dimension"
            placeholder="选择分析维度"
            style="width: 150px"
            :options="dimensionOptions"
          />
        </n-form-item>
        <n-form-item label="时间粒度">
          <n-select
            v-model:value="configForm.granularity"
            placeholder="选择时间粒度"
            style="width: 120px"
            :options="granularityOptions"
          />
        </n-form-item>
        <n-form-item label="对比分析">
          <n-checkbox v-model:checked="configForm.compare_enabled">
            启用对比
          </n-checkbox>
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleAnalyze" :loading="loading">
              <template #icon>
                <n-icon><AnalyticsOutline /></n-icon>
              </template>
              开始分析
            </n-button>
            <n-button @click="handleExportReport">
              <template #icon>
                <n-icon><DownloadOutline /></n-icon>
              </template>
              导出报告
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="analysisData">
      <!-- 概览指标 -->
      <n-card title="行为概览" class="overview-card">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic label="总浏览量" :value="analysisData.overview.total_views">
              <template #prefix>
                <n-icon color="#18a058">
                  <EyeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="平均停留" :value="analysisData.overview.avg_duration + 's'">
              <template #prefix>
                <n-icon color="#2080f0">
                  <TimeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="跳出率" :value="analysisData.overview.bounce_rate + '%'">
              <template #prefix>
                <n-icon color="#f0a020">
                  <TrendingDownOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="转化率" :value="analysisData.overview.conversion_rate + '%'">
              <template #prefix>
                <n-icon color="#d03050">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 行为趋势图 -->
      <n-card title="行为趋势" class="trend-card">
        <template #header-extra>
          <n-space>
            <n-select
              v-model:value="trendMetric"
              size="small"
              style="width: 120px"
              :options="trendMetricOptions"
            />
            <n-button size="small" @click="refreshTrendChart">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
            </n-button>
          </n-space>
        </template>
        <div class="trend-chart" ref="trendChartRef"></div>
      </n-card>

      <!-- 用户路径分析 -->
      <n-card title="用户路径分析" class="path-card">
        <div class="path-analysis">
          <div class="path-flow">
            <div class="flow-step" v-for="(step, index) in analysisData.user_paths" :key="index">
              <div class="step-content">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-info">
                  <div class="step-page">{{ step.page_title || step.page_url }}</div>
                  <div class="step-stats">
                    <span>{{ step.user_count }} 用户</span>
                    <span>{{ step.avg_duration }}s 停留</span>
                  </div>
                </div>
                <div class="step-metrics">
                  <n-progress
                    type="circle"
                    :percentage="step.retention_rate"
                    :stroke-width="8"
                    :size="60"
                  >
                    <span style="font-size: 12px">{{ step.retention_rate }}%</span>
                  </n-progress>
                </div>
              </div>
              <div class="flow-arrow" v-if="index < analysisData.user_paths.length - 1">
                <n-icon size="20" color="#ccc">
                  <ArrowForwardOutline />
                </n-icon>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 行为模式分析 -->
      <n-grid :cols="2" :x-gap="16" class="pattern-grid">
        <n-grid-item>
          <n-card title="设备分布">
            <div class="device-chart" ref="deviceChartRef"></div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card title="时间分布">
            <div class="time-chart" ref="timeChartRef"></div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 异常行为检测 -->
      <n-card title="异常行为检测" class="anomaly-card">
        <n-list>
          <n-list-item v-for="anomaly in analysisData.anomalies" :key="anomaly.id">
            <div class="anomaly-item">
              <div class="anomaly-icon">
                <n-icon size="20" :color="getAnomalyColor(anomaly.severity)">
                  <WarningOutline />
                </n-icon>
              </div>
              <div class="anomaly-content">
                <div class="anomaly-title">{{ anomaly.title }}</div>
                <div class="anomaly-desc">{{ anomaly.description }}</div>
                <div class="anomaly-meta">
                  <n-tag :type="getAnomalyTagType(anomaly.severity)" size="small">
                    {{ anomaly.severity }}
                  </n-tag>
                  <span class="anomaly-time">{{ new Date(anomaly.detected_at).toLocaleString() }}</span>
                </div>
              </div>
              <div class="anomaly-actions">
                <n-button size="small" @click="handleInvestigateAnomaly(anomaly)">
                  调查
                </n-button>
              </div>
            </div>
          </n-list-item>
        </n-list>
        
        <div v-if="!analysisData.anomalies.length" class="no-anomalies">
          <n-icon size="40" color="#18a058">
            <CheckmarkCircleOutline />
          </n-icon>
          <div>未检测到异常行为</div>
        </div>
      </n-card>

      <!-- 优化建议 -->
      <n-card title="优化建议" class="suggestions-card">
        <div class="suggestions-list">
          <div class="suggestion-item" v-for="suggestion in analysisData.suggestions" :key="suggestion.id">
            <div class="suggestion-priority">
              <n-tag :type="getPriorityType(suggestion.priority)" size="small">
                {{ suggestion.priority }}
              </n-tag>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
              <div class="suggestion-impact">
                <span>预期提升: {{ suggestion.expected_improvement }}</span>
                <span>实施难度: {{ suggestion.difficulty }}</span>
              </div>
            </div>
            <div class="suggestion-actions">
              <n-button size="small" type="primary" @click="handleImplementSuggestion(suggestion)">
                实施
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!analysisData && !loading">
      <n-empty description="请选择轨迹记录并开始分析">
        <template #icon>
          <n-icon size="60" color="#ccc">
            <AnalyticsOutline />
          </n-icon>
        </template>
        <template #extra>
          <n-button type="primary" @click="handleAnalyze">
            开始分析
          </n-button>
        </template>
      </n-empty>
    </div>

    <!-- 异常详情模态框 -->
    <n-modal
      v-model:show="showAnomalyDetail"
      preset="card"
      title="异常行为详情"
      style="width: 600px"
    >
      <AnomalyDetail
        v-if="selectedAnomaly"
        :anomaly="selectedAnomaly"
        @close="showAnomalyDetail = false"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import {
  NCard,
  NForm,
  NFormItem,
  NSelect,
  NCheckbox,
  NButton,
  NSpace,
  NIcon,
  NGrid,
  NGridItem,
  NStatistic,
  NProgress,
  NList,
  NListItem,
  NTag,
  NEmpty,
  NModal,
  useMessage
} from 'naive-ui'
import {
  AnalyticsOutline,
  DownloadOutline,
  EyeOutline,
  TimeOutline,
  TrendingDownOutline,
  TrendingUpOutline,
  RefreshOutline,
  ArrowForwardOutline,
  WarningOutline,
  CheckmarkCircleOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import AnomalyDetail from './AnomalyDetail.vue'

interface Props {
  trackIds: number[]
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 配置表单
const configForm = reactive({
  dimension: 'page',
  granularity: 'hour',
  compare_enabled: false
})

// 选项数据
const dimensionOptions = [
  { label: '页面维度', value: 'page' },
  { label: '用户维度', value: 'user' },
  { label: '设备维度', value: 'device' },
  { label: '时间维度', value: 'time' }
]

const granularityOptions = [
  { label: '小时', value: 'hour' },
  { label: '天', value: 'day' },
  { label: '周', value: 'week' },
  { label: '月', value: 'month' }
]

const trendMetricOptions = [
  { label: '浏览量', value: 'views' },
  { label: '停留时间', value: 'duration' },
  { label: '跳出率', value: 'bounce_rate' },
  { label: '转化率', value: 'conversion_rate' }
]

// 状态
const loading = ref(false)
const analysisData = ref<any>(null)
const trendMetric = ref('views')
const showAnomalyDetail = ref(false)
const selectedAnomaly = ref<any>(null)

// 引用
const trendChartRef = ref<HTMLElement>()
const deviceChartRef = ref<HTMLElement>()
const timeChartRef = ref<HTMLElement>()

// 图表实例
let trendChart: echarts.ECharts | null = null
let deviceChart: echarts.ECharts | null = null
let timeChart: echarts.ECharts | null = null

// 方法
const handleAnalyze = async () => {
  if (!props.trackIds.length) {
    message.warning('请先选择要分析的轨迹记录')
    return
  }
  
  loading.value = true
  try {
    // 模拟分析数据
    analysisData.value = {
      overview: {
        total_views: 1250,
        avg_duration: 125,
        bounce_rate: 35,
        conversion_rate: 12
      },
      trend_data: {
        dates: ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        views: [120, 150, 180, 160, 200],
        duration: [110, 125, 140, 130, 145],
        bounce_rate: [40, 35, 30, 32, 28],
        conversion_rate: [10, 12, 15, 13, 16]
      },
      user_paths: [
        {
          page_title: '首页',
          page_url: '/home',
          user_count: 1000,
          avg_duration: 45,
          retention_rate: 85
        },
        {
          page_title: '产品列表',
          page_url: '/products',
          user_count: 850,
          avg_duration: 120,
          retention_rate: 70
        },
        {
          page_title: '产品详情',
          page_url: '/product/123',
          user_count: 595,
          avg_duration: 180,
          retention_rate: 45
        },
        {
          page_title: '购物车',
          page_url: '/cart',
          user_count: 268,
          avg_duration: 90,
          retention_rate: 60
        },
        {
          page_title: '结算页',
          page_url: '/checkout',
          user_count: 161,
          avg_duration: 240,
          retention_rate: 75
        }
      ],
      device_distribution: {
        mobile: 65,
        desktop: 30,
        tablet: 5
      },
      time_distribution: {
        '00-06': 5,
        '06-12': 25,
        '12-18': 45,
        '18-24': 25
      },
      anomalies: [
        {
          id: 1,
          title: '异常跳出率峰值',
          description: '在14:00-15:00时段检测到跳出率异常升高至65%',
          severity: '高',
          detected_at: '2024-01-05T14:30:00Z'
        },
        {
          id: 2,
          title: '停留时间异常下降',
          description: '产品详情页平均停留时间较昨日下降40%',
          severity: '中',
          detected_at: '2024-01-05T16:15:00Z'
        }
      ],
      suggestions: [
        {
          id: 1,
          title: '优化页面加载速度',
          description: '检测到首页加载时间较长，建议优化图片和脚本加载',
          priority: '高',
          expected_improvement: '跳出率降低15%',
          difficulty: '中等'
        },
        {
          id: 2,
          title: '增加相关产品推荐',
          description: '在产品详情页添加相关产品推荐，提高用户参与度',
          priority: '中',
          expected_improvement: '停留时间增加20%',
          difficulty: '简单'
        },
        {
          id: 3,
          title: '优化移动端体验',
          description: '移动端用户占比较高，建议优化移动端界面和交互',
          priority: '高',
          expected_improvement: '转化率提升10%',
          difficulty: '复杂'
        }
      ]
    }
    
    // 渲染图表
    await nextTick()
    renderCharts()
    
    message.success('行为分析完成')
  } catch (error) {
    message.error('分析失败')
  } finally {
    loading.value = false
  }
}

const handleExportReport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const refreshTrendChart = () => {
  if (trendChart && analysisData.value) {
    renderTrendChart()
  }
}

const getAnomalyColor = (severity: string) => {
  const colorMap: Record<string, string> = {
    '高': '#d03050',
    '中': '#f0a020',
    '低': '#18a058'
  }
  return colorMap[severity] || '#666'
}

const getAnomalyTagType = (severity: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'error' | 'warning' | 'success'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[severity] || 'default'
}

const getPriorityType = (priority: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'error' | 'warning' | 'info'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'default'
}

const handleInvestigateAnomaly = (anomaly: any) => {
  selectedAnomaly.value = anomaly
  showAnomalyDetail.value = true
}

const handleImplementSuggestion = (suggestion: any) => {
  // TODO: 实现建议实施功能
  message.info(`开始实施建议: ${suggestion.title}`)
}

// 图表渲染
const renderCharts = () => {
  renderTrendChart()
  renderDeviceChart()
  renderTimeChart()
}

const renderTrendChart = () => {
  if (!trendChartRef.value || !analysisData.value) return
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }
  
  const data = analysisData.value.trend_data
  const metricData = data[trendMetric.value]
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'line',
        data: metricData,
        smooth: true,
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

const renderDeviceChart = () => {
  if (!deviceChartRef.value || !analysisData.value) return
  
  if (!deviceChart) {
    deviceChart = echarts.init(deviceChartRef.value)
  }
  
  const data = analysisData.value.device_distribution
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)'
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        data: [
          { value: data.mobile, name: '移动设备' },
          { value: data.desktop, name: '桌面设备' },
          { value: data.tablet, name: '平板设备' }
        ]
      }
    ]
  }
  
  deviceChart.setOption(option)
}

const renderTimeChart = () => {
  if (!timeChartRef.value || !analysisData.value) return
  
  if (!timeChart) {
    timeChart = echarts.init(timeChartRef.value)
  }
  
  const data = analysisData.value.time_distribution
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Object.keys(data)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        type: 'bar',
        data: Object.values(data),
        itemStyle: {
          color: '#2080f0'
        }
      }
    ]
  }
  
  timeChart.setOption(option)
}

// 监听趋势指标变化
watch(trendMetric, () => {
  refreshTrendChart()
})

// 响应式处理
const handleResize = () => {
  trendChart?.resize()
  deviceChart?.resize()
  timeChart?.resize()
}

// 初始化
onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 如果有传入的轨迹ID，自动开始分析
  if (props.trackIds.length > 0) {
    handleAnalyze()
  }
})

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  deviceChart?.dispose()
  timeChart?.dispose()
}

// 组件卸载时清理
const { onBeforeUnmount } = require('vue')
onBeforeUnmount(cleanup)
</script>

<style scoped>
.behavior-analysis {
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.overview-card,
.trend-card,
.path-card,
.anomaly-card,
.suggestions-card {
  margin-bottom: 0;
}

.trend-chart,
.device-chart,
.time-chart {
  height: 300px;
}

.pattern-grid {
  margin-bottom: 0;
}

.path-analysis {
  padding: 16px 0;
}

.path-flow {
  display: flex;
  align-items: center;
  gap: 20px;
  overflow-x: auto;
  padding: 20px 0;
}

.flow-step {
  display: flex;
  align-items: center;
  gap: 20px;
  min-width: 200px;
}

.step-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.step-content:hover {
  border-color: #2080f0;
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.15);
}

.step-number {
  width: 32px;
  height: 32px;
  background: #2080f0;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-info {
  flex: 1;
}

.step-page {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.step-stats {
  font-size: 12px;
  color: #666;
}

.step-stats span {
  margin-right: 12px;
}

.step-metrics {
  display: flex;
  align-items: center;
}

.flow-arrow {
  display: flex;
  align-items: center;
}

.anomaly-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.anomaly-item:last-child {
  border-bottom: none;
}

.anomaly-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.anomaly-content {
  flex: 1;
}

.anomaly-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.anomaly-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.anomaly-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.anomaly-time {
  font-size: 12px;
  color: #999;
}

.anomaly-actions {
  display: flex;
  align-items: center;
}

.no-anomalies {
  text-align: center;
  padding: 40px;
  color: #666;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.suggestion-priority {
  display: flex;
  align-items: center;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.suggestion-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.suggestion-impact {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.suggestion-actions {
  display: flex;
  align-items: center;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}
</style>