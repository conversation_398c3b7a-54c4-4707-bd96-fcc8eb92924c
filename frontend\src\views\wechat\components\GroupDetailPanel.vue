<template>
  <div class="group-detail-panel">
    <!-- 群组基本信息 -->
    <n-card title="群组信息" class="info-card">
      <div class="group-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">群组名称：</span>
            <span class="value">{{ group.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">群组ID：</span>
            <span class="value">{{ group.groupId }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">群主：</span>
            <span class="value">{{ group.ownerName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">成员数：</span>
            <span class="value">{{ group.memberCount }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">群组类型：</span>
            <n-tag :type="getTypeColor(group.type)">{{ getTypeText(group.type) }}</n-tag>
          </div>
          <div class="info-item">
            <span class="label">状态：</span>
            <n-tag :type="getStatusColor(group.status)">{{ getStatusText(group.status) }}</n-tag>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDate(group.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后活跃：</span>
            <span class="value">{{ formatDate(group.lastActiveTime) }}</span>
          </div>
        </div>
        <div class="info-row full-width" v-if="group.description">
          <div class="info-item">
            <span class="label">群组描述：</span>
            <span class="value">{{ group.description }}</span>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 群组统计 -->
    <n-card title="群组统计" class="stats-card">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ group.todayMessageCount || 0 }}</div>
          <div class="stat-label">今日消息</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ group.weekMessageCount || 0 }}</div>
          <div class="stat-label">本周消息</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ group.monthMessageCount || 0 }}</div>
          <div class="stat-label">本月消息</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ group.activeMembers || 0 }}</div>
          <div class="stat-label">活跃成员</div>
        </div>
      </div>
    </n-card>

    <!-- 成员管理 -->
    <n-card title="成员管理" class="members-card">
      <template #header-extra>
        <n-button size="small" @click="refreshMembers">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </template>
      
      <div class="members-section">
        <div class="members-filter">
          <n-input
            v-model:value="memberFilter"
            placeholder="搜索成员"
            clearable
            style="width: 200px;"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="memberRoleFilter"
            placeholder="成员角色"
            clearable
            style="width: 120px;"
            :options="memberRoleOptions"
          />
        </div>
        
        <n-data-table
          :columns="memberColumns"
          :data="filteredMembers"
          :loading="membersLoading"
          :pagination="memberPagination"
          size="small"
        />
      </div>
    </n-card>

    <!-- 消息统计 -->
    <n-card title="消息统计" class="message-stats-card">
      <div class="chart-container">
        <div ref="messageChartRef" class="chart"></div>
      </div>
    </n-card>

    <!-- 活跃度分析 -->
    <n-card title="活跃度分析" class="activity-card">
      <div class="activity-content">
        <div class="activity-chart">
          <div ref="activityChartRef" class="chart"></div>
        </div>
        <div class="activity-insights">
          <h4>活跃度洞察</h4>
          <div class="insight-item">
            <n-icon color="#18a058"><TrendingUpOutline /></n-icon>
            <span>群组活跃度较上周提升 {{ activityGrowth }}%</span>
          </div>
          <div class="insight-item">
            <n-icon color="#2080f0"><TimeOutline /></n-icon>
            <span>最活跃时段：{{ mostActiveTime }}</span>
          </div>
          <div class="insight-item">
            <n-icon color="#f0a020"><PeopleOutline /></n-icon>
            <span>核心活跃成员：{{ coreActiveMembers }} 人</span>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-button type="primary" @click="exportGroupData">
        <template #icon>
          <n-icon><DownloadOutline /></n-icon>
        </template>
        导出数据
      </n-button>
      <n-button @click="viewGroupMessages">
        <template #icon>
          <n-icon><ChatbubbleOutline /></n-icon>
        </template>
        查看消息
      </n-button>
      <n-button @click="groupSettings">
        <template #icon>
          <n-icon><SettingsOutline /></n-icon>
        </template>
        群组设置
      </n-button>
      <n-button @click="$emit('close')">
        关闭
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, h } from 'vue'
import {
  NCard, NTag, NButton, NIcon, NInput, NSelect, NDataTable,
  useMessage
} from 'naive-ui'
import {
  RefreshOutline, SearchOutline, DownloadOutline, ChatbubbleOutline,
  SettingsOutline, TrendingUpOutline, TimeOutline, PeopleOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import type { WechatGroup } from '@/types'

interface Props {
  group: WechatGroup
}

interface Emits {
  (e: 'update'): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const membersLoading = ref(false)
const memberFilter = ref('')
const memberRoleFilter = ref(null)
const messageChartRef = ref<HTMLElement>()
const activityChartRef = ref<HTMLElement>()

// 模拟成员数据
const members = ref([
  {
    id: '1',
    nickname: '张三',
    avatar: '',
    role: 'owner',
    joinTime: '2024-01-01',
    lastActiveTime: '2024-01-15',
    messageCount: 156
  },
  {
    id: '2',
    nickname: '李四',
    avatar: '',
    role: 'admin',
    joinTime: '2024-01-02',
    lastActiveTime: '2024-01-15',
    messageCount: 89
  },
  {
    id: '3',
    nickname: '王五',
    avatar: '',
    role: 'member',
    joinTime: '2024-01-03',
    lastActiveTime: '2024-01-14',
    messageCount: 45
  }
])

// 模拟统计数据
const activityGrowth = ref(15.6)
const mostActiveTime = ref('20:00-22:00')
const coreActiveMembers = ref(8)

// 成员角色选项
const memberRoleOptions = [
  { label: '群主', value: 'owner' },
  { label: '管理员', value: 'admin' },
  { label: '普通成员', value: 'member' }
]

// 计算属性
const filteredMembers = computed(() => {
  let result = members.value
  
  if (memberFilter.value) {
    const keyword = memberFilter.value.toLowerCase()
    result = result.filter(member => 
      member.nickname.toLowerCase().includes(keyword)
    )
  }
  
  if (memberRoleFilter.value) {
    result = result.filter(member => member.role === memberRoleFilter.value)
  }
  
  return result
})

// 成员表格列配置
const memberColumns = [
  {
    title: '成员',
    key: 'member',
    render: (row: any) => h('div', { class: 'member-info' }, [
      h('div', { class: 'member-name' }, row.nickname),
      h('div', { class: 'member-id' }, `ID: ${row.id}`)
    ])
  },
  {
    title: '角色',
    key: 'role',
    render: (row: any) => {
      const roleMap: Record<string, { text: string; type: 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' }> = {
        owner: { text: '群主', type: 'error' },
        admin: { text: '管理员', type: 'warning' },
        member: { text: '普通成员', type: 'default' }
      }
      const role = roleMap[row.role] || { text: '未知', type: 'default' }
      return h(NTag, { type: role.type }, role.text)
    }
  },
  {
    title: '消息数',
    key: 'messageCount',
    sorter: (a: any, b: any) => a.messageCount - b.messageCount
  },
  {
    title: '加入时间',
    key: 'joinTime',
    render: (row: any) => formatDate(row.joinTime)
  },
  {
    title: '最后活跃',
    key: 'lastActiveTime',
    render: (row: any) => formatDate(row.lastActiveTime)
  }
]

// 分页配置
const memberPagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 方法
const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString()
}

const getTypeColor = (type: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'default' | 'success' | 'info'> = {
    normal: 'default',
    enterprise: 'success',
    service: 'info'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    normal: '普通群',
    enterprise: '企业群',
    service: '客服群'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getStatusColor = (status: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const colorMap: Record<string, 'success' | 'error' | 'warning'> = {
    normal: 'success',
    dissolved: 'error',
    left: 'warning'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    normal: '正常',
    dissolved: '已解散',
    left: '已退出'
  }
  return textMap[status as keyof typeof textMap] || status
}

const refreshMembers = async () => {
  membersLoading.value = true
  try {
    // 模拟刷新成员数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('成员数据已刷新')
  } catch (error) {
    message.error('刷新成员数据失败')
  } finally {
    membersLoading.value = false
  }
}

const exportGroupData = () => {
  message.info('导出群组数据功能开发中')
}

const viewGroupMessages = () => {
  message.info('查看群组消息功能开发中')
}

const groupSettings = () => {
  message.info('群组设置功能开发中')
}

// 初始化图表
const initMessageChart = () => {
  if (!messageChartRef.value) return
  
  const chart = echarts.init(messageChartRef.value)
  const option = {
    title: {
      text: '消息趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '消息数',
      type: 'line',
      data: [45, 67, 89, 56, 78, 123, 98],
      smooth: true,
      itemStyle: { color: '#2080f0' }
    }]
  }
  chart.setOption(option)
}

const initActivityChart = () => {
  if (!activityChartRef.value) return
  
  const chart = echarts.init(activityChartRef.value)
  const option = {
    title: {
      text: '24小时活跃度',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '活跃度',
      type: 'bar',
      data: [
        2, 1, 0, 0, 1, 3, 5, 8, 12, 15, 18, 22,
        25, 28, 32, 35, 38, 42, 45, 48, 35, 25, 15, 8
      ],
      itemStyle: { color: '#18a058' }
    }]
  }
  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initMessageChart()
    initActivityChart()
  })
})
</script>

<style scoped>
.group-detail-panel {
  padding: 16px;
}

.info-card,
.stats-card,
.members-card,
.message-stats-card,
.activity-card {
  margin-bottom: 16px;
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.info-row.full-width {
  grid-template-columns: 1fr;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #1a1a1a;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.members-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.members-filter {
  display: flex;
  gap: 12px;
  align-items: center;
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-name {
  font-weight: 500;
  color: #1a1a1a;
}

.member-id {
  font-size: 12px;
  color: #999;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.activity-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.activity-chart {
  height: 300px;
}

.activity-insights {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-insights h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}
</style>