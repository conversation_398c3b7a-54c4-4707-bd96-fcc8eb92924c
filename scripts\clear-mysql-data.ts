import { MySQLManager } from '../src/database/MySQLManager';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

async function clearMySQLData() {
  console.log('🧹 开始清空MySQL数据库数据...');
  
  const mysqlConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'workchat_admin',
    connectionLimit: 10
  };
  
  const mysql = new MySQLManager(mysqlConfig);
  
  try {
    await mysql.initialize();
    
    // 禁用外键检查
    await mysql.query('SET FOREIGN_KEY_CHECKS = 0');
    
    // 清空所有表的数据
    const tables = [
      'migration_logs',
      'public_pool',
      'follow_ups',
      'customer_value_analysis',
      'sales_funnel_stats',
      'wechat_customer_tracking',
      'customer_behaviors',
      'pool_rules',
      'meeting_participants',
      'meetings',
      'campaign_shares',
      'campaign_participants',
      'marketing_campaigns',
      'customer_follow_records',
      'customers',
      'option_items',
      'option_categories',
      'user_roles',
      'role_permissions',
      'permissions',
      'roles',
      'users'
    ];
    
    for (const table of tables) {
      console.log(`🗑️ 清空表: ${table}`);
      const result = await mysql.query(`TRUNCATE TABLE ${table}`);
      if (result.success) {
        console.log(`✅ 表 ${table} 清空成功`);
      } else {
        console.error(`❌ 表 ${table} 清空失败:`, result.error);
      }
    }
    
    // 重新启用外键检查
    await mysql.query('SET FOREIGN_KEY_CHECKS = 1');
    
    console.log('✅ MySQL数据库数据清空完成');
    
  } catch (error) {
    console.error('❌ 清空数据库失败:', error);
  } finally {
    await mysql.close();
  }
}

// 执行清空操作
clearMySQLData().catch(console.error);