const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const path = require('path');

class DatabaseManager {
  constructor(config) {
    this.config = config;
    this.db = null;
  }

  async connect() {
    try {
      const dbPath = path.join(__dirname, '../../data/yysh_crm.db');
      
      this.db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });
      
      // 启用外键约束
      await this.db.exec('PRAGMA foreign_keys = ON');
      
      console.log('SQLite数据库连接成功');
      
      // 初始化数据库表
      await this.initTables();
      
      return true;
    } catch (error) {
      console.error('数据库连接失败:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.db) {
      await this.db.close();
      this.db = null;
      console.log('数据库连接已关闭');
    }
  }

  async query(sql, params = []) {
    try {
      if (sql.trim().toLowerCase().startsWith('select')) {
        const rows = await this.db.all(sql, params);
        return { rows };
      } else {
        const result = await this.db.run(sql, params);
        return { rows: result };
      }
    } catch (error) {
      console.error('数据库查询错误:', error);
      throw error;
    }
  }

  async transaction(callback) {
    await this.db.exec('BEGIN TRANSACTION');
    
    try {
      const result = await callback(this.db);
      await this.db.exec('COMMIT');
      return result;
    } catch (error) {
      await this.db.exec('ROLLBACK');
      throw error;
    }
  }

  async initTables() {
    const initSQL = `
      -- 部门表
      CREATE TABLE IF NOT EXISTS departments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        parent_id INTEGER,
        manager_id INTEGER,
        sort_order INTEGER DEFAULT 0,
        status INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES departments(id),
        FOREIGN KEY (manager_id) REFERENCES users(id)
      );

      -- 用户表 (企微小程序版本)
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        work_wechat_id VARCHAR(100),
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(50) NOT NULL,
        avatar VARCHAR(500),
        mobile VARCHAR(20),
        email VARCHAR(100),
        department_id INTEGER,
        position VARCHAR(100),
        role VARCHAR(20) DEFAULT 'sales' CHECK (role IN ('admin', 'manager', 'sales', 'designer')),
        status INTEGER DEFAULT 1,
        permissions TEXT,
        last_login_time DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id)
      );

      -- 客户表 (企微小程序版本)
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        company VARCHAR(200),
        position VARCHAR(100),
        wechat VARCHAR(100),
        address VARCHAR(500),
        source VARCHAR(20) DEFAULT 'other' CHECK (source IN ('online', 'referral', 'event', 'telemarketing', 'store', 'other')),
        status VARCHAR(20) DEFAULT 'potential' CHECK (status IN ('potential', 'interested', 'deal', 'lost')),
        level VARCHAR(5) DEFAULT 'C' CHECK (level IN ('A', 'B', 'C', 'D')),
        birthday DATE,
        gender VARCHAR(10) DEFAULT 'unknown' CHECK (gender IN ('male', 'female', 'unknown')),
        is_vip INTEGER DEFAULT 0,
        is_high_value INTEGER DEFAULT 0,
        tags TEXT,
        remark TEXT,
        owner_id INTEGER NOT NULL,
        team_id INTEGER,
        collaborators TEXT,
        is_in_pool INTEGER DEFAULT 0,
        pool_time DATETIME,
        last_follow_time DATETIME,
        next_follow_time DATETIME,
        follow_count INTEGER DEFAULT 0,
        deal_amount DECIMAL(10,2) DEFAULT 0.00,
        deal_time DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (owner_id) REFERENCES users(id),
        FOREIGN KEY (team_id) REFERENCES departments(id)
      );

      -- 客户标签表
      CREATE TABLE IF NOT EXISTS customer_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) NOT NULL UNIQUE,
        color VARCHAR(20) DEFAULT '#1890ff',
        category VARCHAR(50) DEFAULT 'custom',
        sort_order INTEGER DEFAULT 0,
        is_system INTEGER DEFAULT 0,
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id)
      );

      -- 客户标签关联表
      CREATE TABLE IF NOT EXISTS customer_tag_relations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        tag_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES customer_tags(id) ON DELETE CASCADE,
        UNIQUE(customer_id, tag_id)
      );

      -- 跟进记录表 (企微小程序版本)
      CREATE TABLE IF NOT EXISTS follow_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        type VARCHAR(20) NOT NULL CHECK (type IN ('phone', 'wechat', 'email', 'visit', 'meeting', 'other')),
        stage VARCHAR(50) NOT NULL,
        content TEXT NOT NULL,
        images TEXT,
        result VARCHAR(20) NOT NULL CHECK (result IN ('effective', 'invalid', 'pending')),
        result_detail TEXT,
        has_next_plan INTEGER DEFAULT 0,
        next_time DATETIME,
        next_content TEXT,
        duration INTEGER,
        location VARCHAR(200),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      -- 见面记录表
      CREATE TABLE IF NOT EXISTS meeting_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        follow_record_id INTEGER NOT NULL,
        customer_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        meeting_type VARCHAR(20) NOT NULL CHECK (meeting_type IN ('measure', 'visit', 'external')),
        meeting_time DATETIME NOT NULL,
        designer_id INTEGER,
        designer_name VARCHAR(50),
        visit_count INTEGER DEFAULT 1,
        address VARCHAR(500),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (follow_record_id) REFERENCES follow_records(id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (designer_id) REFERENCES users(id)
      );

      -- 公海池记录表
      CREATE TABLE IF NOT EXISTS customer_pool_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        action VARCHAR(20) NOT NULL CHECK (action IN ('release', 'claim')),
        from_user_id INTEGER,
        to_user_id INTEGER,
        reason VARCHAR(500),
        auto_release INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (from_user_id) REFERENCES users(id),
        FOREIGN KEY (to_user_id) REFERENCES users(id)
      );

      -- 营销链接表 (企微小程序版本)
      CREATE TABLE IF NOT EXISTS marketing_links (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        url VARCHAR(1000) NOT NULL,
        short_url VARCHAR(200),
        qr_code VARCHAR(500),
        cover_image VARCHAR(500),
        category VARCHAR(50) DEFAULT 'general',
        tags TEXT,
        click_count INTEGER DEFAULT 0,
        share_count INTEGER DEFAULT 0,
        status INTEGER DEFAULT 1,
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id)
      );

      -- 链接访问记录表
      CREATE TABLE IF NOT EXISTS link_access_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        link_id INTEGER NOT NULL,
        user_id INTEGER,
        customer_id INTEGER,
        ip_address VARCHAR(50),
        user_agent VARCHAR(1000),
        referer VARCHAR(1000),
        access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (link_id) REFERENCES marketing_links(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (customer_id) REFERENCES customers(id)
      );

      -- 数据统计表
      CREATE TABLE IF NOT EXISTS analytics_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        date DATE NOT NULL,
        customer_count INTEGER DEFAULT 0,
        new_customer_count INTEGER DEFAULT 0,
        follow_count INTEGER DEFAULT 0,
        deal_count INTEGER DEFAULT 0,
        deal_amount DECIMAL(10,2) DEFAULT 0.00,
        pool_claim_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id, date)
      );

      -- 系统配置表
      CREATE TABLE IF NOT EXISTS system_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        config_type VARCHAR(20) DEFAULT 'string' CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
        description VARCHAR(500),
        is_system INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- 操作日志表
      CREATE TABLE IF NOT EXISTS operation_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        module VARCHAR(50) NOT NULL,
        action VARCHAR(50) NOT NULL,
        target_type VARCHAR(50),
        target_id INTEGER,
        content TEXT,
        ip_address VARCHAR(50),
        user_agent VARCHAR(1000),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      -- 文件上传表
      CREATE TABLE IF NOT EXISTS uploaded_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        original_name VARCHAR(255) NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        file_type VARCHAR(20) DEFAULT 'other' CHECK (file_type IN ('avatar', 'attachment', 'qrcode', 'other')),
        uploaded_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (uploaded_by) REFERENCES users(id)
      );
    `;

    await this.db.exec(initSQL);

    // 检查并更新表结构
    await this.updateTableStructure();

    // 插入默认部门
    const deptExists = await this.db.get('SELECT id FROM departments WHERE id = ?', [1]);
    if (!deptExists) {
      await this.db.run(`INSERT INTO departments (id, name, sort_order) VALUES (1, '销售部', 1)`);
      await this.db.run(`INSERT INTO departments (id, name, sort_order) VALUES (2, '设计部', 2)`);
      await this.db.run(`INSERT INTO departments (id, name, sort_order) VALUES (3, '管理部', 3)`);
      console.log('默认部门创建成功');
    }

    // 插入默认管理员用户
    const adminExists = await this.db.get('SELECT id FROM users WHERE username = ?', ['admin']);
    if (!adminExists) {
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('password', 10);
      
      await this.db.run(`
        INSERT INTO users (username, password, name, email, role, department_id, position, status, permissions)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, ['admin', hashedPassword, '系统管理员', '<EMAIL>', 'admin', 3, '系统管理员', 1, '["all"]']);
      
      console.log('默认管理员用户创建成功: admin/password');
    }

    // 插入系统标签
    const tagExists = await this.db.get('SELECT id FROM customer_tags WHERE name = ?', ['重要客户']);
    if (!tagExists) {
      const systemTags = [
        ['重要客户', '#f50', 'level', 1],
        ['高价值', '#fa8c16', 'level', 1],
        ['决策者', '#52c41a', 'role', 1],
        ['技术专家', '#1890ff', 'role', 1],
        ['价格敏感', '#722ed1', 'behavior', 1],
        ['服务导向', '#eb2f96', 'behavior', 1],
        ['创新型', '#13c2c2', 'type', 1],
        ['传统型', '#faad14', 'type', 1],
        ['大客户', '#f5222d', 'size', 1],
        ['中小企业', '#fa541c', 'size', 1]
      ];

      for (const [name, color, category, isSystem] of systemTags) {
        await this.db.run(`
          INSERT INTO customer_tags (name, color, category, is_system, created_by)
          VALUES (?, ?, ?, ?, ?)
        `, [name, color, category, isSystem, 1]);
      }
      console.log('系统标签创建成功');
    }

    // 插入默认系统配置
    const configs = [
      ['auto_pool_days', '30', 'number', '客户自动进入公海天数', 1],
      ['max_customer_per_user', '500', 'number', '每个销售最大客户数', 1],
      ['follow_reminder_hours', '24', 'number', '跟进提醒提前小时数', 1],
      ['company_name', '云易商汇装饰', 'string', '公司名称', 0],
      ['company_logo', '', 'string', '公司Logo', 0],
      ['wechat_corp_id', '', 'string', '企业微信CorpID', 1],
      ['wechat_agent_id', '', 'string', '企业微信AgentID', 1],
      ['wechat_secret', '', 'string', '企业微信Secret', 1],
      ['miniprogram_appid', '', 'string', '小程序AppID', 1],
      ['miniprogram_secret', '', 'string', '小程序Secret', 1],
      ['enable_auto_pool', 'true', 'boolean', '启用自动公海', 1],
      ['enable_follow_reminder', 'true', 'boolean', '启用跟进提醒', 1]
    ];

    for (const [key, value, type, desc, isSystem] of configs) {
      const exists = await this.db.get('SELECT id FROM system_configs WHERE config_key = ?', [key]);
      if (!exists) {
        await this.db.run(`
          INSERT INTO system_configs (config_key, config_value, config_type, description, is_system)
          VALUES (?, ?, ?, ?, ?)
        `, [key, value, type, desc, isSystem]);
      }
    }

    console.log('数据库表初始化完成');
  }

  // 更新表结构
  async updateTableStructure() {
    try {
      // 检查system_configs表是否有is_system字段
      const tableInfo = await this.db.all("PRAGMA table_info(system_configs)");
      const hasIsSystemField = tableInfo.some(column => column.name === 'is_system');
      
      if (!hasIsSystemField) {
        await this.db.exec('ALTER TABLE system_configs ADD COLUMN is_system INTEGER DEFAULT 0');
        console.log('system_configs表添加is_system字段成功');
      }

      // 检查follow_records表是否有新字段
      const followTableInfo = await this.db.all("PRAGMA table_info(follow_records)");
      const hasStageField = followTableInfo.some(column => column.name === 'stage');
      const hasResultField = followTableInfo.some(column => column.name === 'result');
      
      if (!hasStageField) {
        await this.db.exec('ALTER TABLE follow_records ADD COLUMN stage VARCHAR(50) DEFAULT "initial"');
        console.log('follow_records表添加stage字段成功');
      }
      
      if (!hasResultField) {
        await this.db.exec('ALTER TABLE follow_records ADD COLUMN result VARCHAR(20) DEFAULT "pending"');
        console.log('follow_records表添加result字段成功');
      }

      // 检查users表是否有企业微信相关字段
      const usersTableInfo = await this.db.all("PRAGMA table_info(users)");
      const hasWechatUseridField = usersTableInfo.some(column => column.name === 'wechat_userid');
      const hasLastLoginAtField = usersTableInfo.some(column => column.name === 'last_login_at');
      
      if (!hasWechatUseridField) {
        await this.db.exec('ALTER TABLE users ADD COLUMN wechat_userid VARCHAR(100)');
        console.log('users表添加wechat_userid字段成功');
      }
      
      if (!hasLastLoginAtField) {
        await this.db.exec('ALTER TABLE users ADD COLUMN last_login_at DATETIME');
        console.log('users表添加last_login_at字段成功');
      }

      // 如果存在work_wechat_id字段，将数据迁移到wechat_userid字段
      const hasWorkWechatIdField = usersTableInfo.some(column => column.name === 'work_wechat_id');
      if (hasWorkWechatIdField && hasWechatUseridField) {
        await this.db.exec('UPDATE users SET wechat_userid = work_wechat_id WHERE work_wechat_id IS NOT NULL AND wechat_userid IS NULL');
        console.log('work_wechat_id数据迁移到wechat_userid成功');
      }

    } catch (error) {
      console.error('更新表结构失败:', error);
    }
  }

  // 分页查询
  async paginate(sql, params = [], page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`;
    const { rows: countRows } = await this.query(countSql, params);
    const total = countRows[0].total;
    
    // 获取分页数据
    const dataSql = `${sql} LIMIT ${offset}, ${pageSize}`;
    const { rows: dataRows } = await this.query(dataSql, params);
    
    return {
      data: dataRows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: parseInt(total),
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 插入数据
  async insert(table, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    const result = await this.query(sql, values);
    
    return {
      insertId: result.rows.insertId,
      affectedRows: result.rows.affectedRows
    };
  }

  // 更新数据
  async update(table, data, where, whereParams = []) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
    const result = await this.query(sql, [...values, ...whereParams]);
    
    return {
      affectedRows: result.rows.affectedRows,
      changedRows: result.rows.changedRows
    };
  }

  // 删除数据
  async delete(table, where, whereParams = []) {
    const sql = `DELETE FROM ${table} WHERE ${where}`;
    const result = await this.query(sql, whereParams);
    
    return {
      affectedRows: result.rows.affectedRows
    };
  }

  // 软删除
  async softDelete(table, where, whereParams = []) {
    const sql = `UPDATE ${table} SET deleted_at = NOW() WHERE ${where}`;
    const result = await this.query(sql, whereParams);
    
    return {
      affectedRows: result.rows.affectedRows
    };
  }

  // 查找单条记录
  async findOne(table, where, whereParams = []) {
    const sql = `SELECT * FROM ${table} WHERE ${where} LIMIT 1`;
    const { rows } = await this.query(sql, whereParams);
    return rows[0] || null;
  }

  // 查找多条记录
  async findMany(table, where = '1=1', whereParams = [], options = {}) {
    let sql = `SELECT * FROM ${table} WHERE ${where}`;
    
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`;
    }
    
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
    }
    
    const { rows } = await this.query(sql, whereParams);
    return rows;
  }

  // 检查表是否存在
  async tableExists(tableName) {
    const sql = `
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = ?
    `;
    const { rows } = await this.query(sql, [this.config.database, tableName]);
    return rows[0].count > 0;
  }

  // 获取表结构
  async getTableStructure(tableName) {
    const sql = `DESCRIBE ${tableName}`;
    const { rows } = await this.query(sql);
    return rows;
  }

  // 执行存储过程
  async callProcedure(procedureName, params = []) {
    const placeholders = params.map(() => '?').join(', ');
    const sql = `CALL ${procedureName}(${placeholders})`;
    const { rows } = await this.query(sql, params);
    return rows;
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      config: {
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.user
      }
    };
  }
}

module.exports = DatabaseManager;