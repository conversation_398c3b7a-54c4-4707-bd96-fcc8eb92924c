<template>
  <div class="ticket-timeline">
    <n-card title="时间节点跟踪" :bordered="false">
      <!-- 筛选区域 -->
      <div class="filter-section mb-6">
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索工单号、客户姓名"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <n-icon :component="Search" />
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="工单状态"
            clearable
            style="width: 150px"
            :options="statusOptions"
          />
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            placeholder="选择时间范围"
            style="width: 300px"
          />
          <n-button type="primary" @click="loadTimelines">
            <template #icon>
              <n-icon :component="Search" />
            </template>
            查询
          </n-button>
        </n-space>
      </div>

      <!-- 工单时间线列表 -->
      <div class="timeline-list">
        <n-card
          v-for="ticket in filteredTickets"
          :key="ticket.id"
          class="ticket-card mb-4"
          :bordered="true"
        >
          <!-- 工单基本信息 -->
          <template #header>
            <div class="ticket-header">
              <div class="ticket-info">
                <h3 class="ticket-title">{{ ticket.ticketNo }} - {{ ticket.customerName }}</h3>
                <div class="ticket-meta">
                  <n-tag :type="getStatusType(ticket.status)" size="small">
                    {{ getStatusText(ticket.status) }}
                  </n-tag>
                  <n-tag :type="getPriorityType(ticket.priority)" size="small" class="ml-2">
                    {{ getPriorityText(ticket.priority) }}
                  </n-tag>
                  <span class="ml-4 text-gray-500">{{ ticket.propertyInfo }}</span>
                </div>
              </div>
              <div class="ticket-actions">
                <countdown-timer :target-time="ticket.expectedTime" class="mr-4" />
                <n-button size="small" @click="viewTicketDetail(ticket)">
                  查看详情
                </n-button>
              </div>
            </div>
          </template>

          <!-- 时间线 -->
          <n-timeline>
            <n-timeline-item
              v-for="(event, index) in ticket.timeline"
              :key="index"
              :type="getTimelineType(event.type)"
              :title="event.title"
              :content="event.description"
              :time="formatDate(event.timestamp)"
            >
              <template #icon>
                <n-icon :component="getTimelineIcon(event.type)" />
              </template>
              <div class="timeline-event">
                <div class="event-details">
                  <div class="event-operator" v-if="event.operator">
                    操作人：{{ event.operator }}
                  </div>
                  <div class="event-duration" v-if="event.duration">
                    耗时：{{ formatDuration(event.duration) }}
                  </div>
                  <div class="event-note" v-if="event.note">
                    备注：{{ event.note }}
                  </div>
                </div>
                <div class="event-attachments" v-if="event.attachments?.length">
                  <n-space size="small">
                    <n-tag
                      v-for="file in event.attachments"
                      :key="file.name"
                      type="info"
                      size="small"
                      @click="previewFile(file)"
                      style="cursor: pointer"
                    >
                      <template #icon>
                        <n-icon :component="getFileIcon(file.type)" />
                      </template>
                      {{ file.name }}
                    </n-tag>
                  </n-space>
                </div>
              </div>
            </n-timeline-item>
          </n-timeline>

          <!-- 统计信息 -->
          <template #footer>
            <div class="ticket-stats">
              <n-statistic label="总耗时" :value="calculateTotalDuration(ticket)" />
              <n-statistic label="处理节点" :value="ticket.timeline.length" />
              <n-statistic 
                label="完成率" 
                :value="calculateProgress(ticket)" 
                suffix="%"
              />
              <n-statistic 
                label="平均响应时间" 
                :value="calculateAvgResponseTime(ticket)" 
              />
            </div>
          </template>
        </n-card>
      </div>

      <!-- 空状态 -->
      <n-empty v-if="filteredTickets.length === 0" description="暂无工单数据" />
    </n-card>

    <!-- 工单详情模态框 -->
    <n-modal v-model:show="showDetailModal" preset="dialog" title="工单详情" style="width: 900px">
      <div v-if="selectedTicket">
        <!-- 详细时间线图表 -->
        <div class="detail-timeline">
          <h4>处理时间线分析</h4>
          <div class="timeline-chart">
            <!-- 这里可以集成图表库显示甘特图或时间线图表 -->
            <n-timeline>
              <n-timeline-item
                v-for="(event, index) in selectedTicket.timeline"
                :key="index"
                :type="getTimelineType(event.type)"
              >
                <template #header>
                  <div class="timeline-header">
                    <span class="event-title">{{ event.title }}</span>
                    <span class="event-time">{{ formatDate(event.timestamp) }}</span>
                  </div>
                </template>
                <div class="timeline-content">
                  <p>{{ event.description }}</p>
                  <div class="event-metrics" v-if="event.duration">
                    <n-tag type="info" size="small">
                      耗时：{{ formatDuration(event.duration) }}
                    </n-tag>
                    <n-tag type="success" size="small" class="ml-2" v-if="event.efficiency">
                      效率：{{ event.efficiency }}
                    </n-tag>
                  </div>
                </div>
              </n-timeline-item>
            </n-timeline>
          </div>
        </div>
      </div>
      <template #action>
        <n-button @click="showDetailModal = false">关闭</n-button>
      </template>
    </n-modal>

    <!-- 文件预览模态框 -->
    <n-modal v-model:show="showFileModal" preset="dialog" title="文件预览" style="width: 800px">
      <div v-if="previewFileData">
        <div v-if="previewFileData.type === 'image'">
          <img :src="previewFileData.url" alt="预览图片" style="max-width: 100%; height: auto;" />
        </div>
        <div v-else-if="previewFileData.type === 'pdf'">
          <iframe :src="previewFileData.url" style="width: 100%; height: 500px; border: none;"></iframe>
        </div>
        <div v-else>
          <n-result status="info" title="无法预览" description="该文件类型不支持在线预览">
            <template #footer>
              <n-button type="primary" @click="downloadFile(previewFileData)">
                下载文件
              </n-button>
            </template>
          </n-result>
        </div>
      </div>
      <template #action>
        <n-button @click="showFileModal = false">关闭</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  Search,
  Create,
  CheckmarkCircle,
  CloseCircle,
  Time,
  Person,
  Document,
  Image,
  FileTray
} from '@vicons/ionicons5'
import CountdownTimer from '@/components/CountdownTimer.vue'

interface TimelineEvent {
  type: 'created' | 'assigned' | 'processing' | 'completed' | 'cancelled' | 'updated'
  title: string
  description: string
  timestamp: string
  operator?: string
  duration?: number // 毫秒
  note?: string
  efficiency?: string
  attachments?: Array<{
    name: string
    url: string
    type: 'image' | 'pdf' | 'document' | 'other'
  }>
}

interface TicketWithTimeline {
  id: string
  ticketNo: string
  customerName: string
  propertyInfo: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  expectedTime: string
  timeline: TimelineEvent[]
}

const message = useMessage()
const loading = ref(false)
const showDetailModal = ref(false)
const showFileModal = ref(false)
const selectedTicket = ref<TicketWithTimeline | null>(null)
const previewFileData = ref<any>(null)

// 筛选条件
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)

// 模拟数据
const tickets = ref<TicketWithTimeline[]>([
  {
    id: '1',
    ticketNo: 'T202401001',
    customerName: '张三',
    propertyInfo: '阳光花园 A栋 1201室',
    status: 'processing',
    priority: 'medium',
    expectedTime: '2024-01-16 18:00:00',
    timeline: [
      {
        type: 'created',
        title: '工单创建',
        description: '客户通过微信小程序提交维修需求',
        timestamp: '2024-01-15 09:30:00',
        operator: '系统自动',
        duration: 0
      },
      {
        type: 'assigned',
        title: '工单分配',
        description: '工单已分配给维修部门',
        timestamp: '2024-01-15 10:15:00',
        operator: '客服小王',
        duration: 45 * 60 * 1000, // 45分钟
        note: '已通知维修师傅',
        efficiency: '良好'
      },
      {
        type: 'processing',
        title: '开始处理',
        description: '维修师傅已到达现场开始处理',
        timestamp: '2024-01-15 14:30:00',
        operator: '维修师傅王五',
        duration: 4 * 60 * 60 * 1000 + 15 * 60 * 1000, // 4小时15分钟
        attachments: [
          {
            name: '现场照片1.jpg',
            url: '/uploads/photo1.jpg',
            type: 'image'
          },
          {
            name: '维修报告.pdf',
            url: '/uploads/report.pdf',
            type: 'pdf'
          }
        ]
      }
    ]
  },
  {
    id: '2',
    ticketNo: 'T202401002',
    customerName: '李四',
    propertyInfo: '绿城花园 B栋 2305室',
    status: 'completed',
    priority: 'high',
    expectedTime: '2024-01-15 20:00:00',
    timeline: [
      {
        type: 'created',
        title: '工单创建',
        description: '客户报告空调制冷效果差',
        timestamp: '2024-01-14 14:20:00',
        operator: '系统自动'
      },
      {
        type: 'assigned',
        title: '工单分配',
        description: '紧急工单，立即分配给专业技师',
        timestamp: '2024-01-14 14:35:00',
        operator: '主管张三',
        duration: 15 * 60 * 1000,
        efficiency: '优秀'
      },
      {
        type: 'processing',
        title: '开始处理',
        description: '技师到达现场检查空调系统',
        timestamp: '2024-01-14 16:00:00',
        operator: '空调技师李六',
        duration: 1 * 60 * 60 * 1000 + 25 * 60 * 1000
      },
      {
        type: 'completed',
        title: '处理完成',
        description: '空调清洗保养完成，制冷效果恢复正常',
        timestamp: '2024-01-14 18:30:00',
        operator: '空调技师李六',
        duration: 2 * 60 * 60 * 1000 + 30 * 60 * 1000,
        efficiency: '优秀',
        attachments: [
          {
            name: '维修前后对比.jpg',
            url: '/uploads/before_after.jpg',
            type: 'image'
          },
          {
            name: '保养记录.pdf',
            url: '/uploads/maintenance.pdf',
            type: 'pdf'
          }
        ]
      }
    ]
  }
])

// 选项数据
const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// 筛选后的工单列表
const filteredTickets = computed(() => {
  let result = tickets.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(ticket => 
      ticket.ticketNo.toLowerCase().includes(keyword) ||
      ticket.customerName.toLowerCase().includes(keyword)
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(ticket => ticket.status === statusFilter.value)
  }
  
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(ticket => {
      const createTime = new Date(ticket.timeline[0]?.timestamp).getTime()
      return createTime >= start && createTime <= end
    })
  }
  
  return result
})

// 工具函数
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    cancelled: 'error'
  }
  return types[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  }
  return types[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getTimelineType = (type: string) => {
  const types: Record<string, string> = {
    created: 'info',
    assigned: 'warning',
    processing: 'info',
    completed: 'success',
    cancelled: 'error',
    updated: 'default'
  }
  return types[type] || 'default'
}

const getTimelineIcon = (type: string) => {
  const icons: Record<string, any> = {
    created: Create,
    assigned: Person,
    processing: Time,
    completed: CheckmarkCircle,
    cancelled: CloseCircle,
    updated: Create
  }
  return icons[type] || Create
}

const getFileIcon = (type: string) => {
  const icons: Record<string, any> = {
    image: Image,
    pdf: Document,
    document: Document,
    other: FileTray
  }
  return icons[type] || FileTray
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatDuration = (ms: number) => {
  if (!ms) return '-'
  
  const hours = Math.floor(ms / (1000 * 60 * 60))
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  }
  return `${minutes}分钟`
}

// 计算函数
const calculateTotalDuration = (ticket: TicketWithTimeline) => {
  const firstEvent = ticket.timeline[0]
  const lastEvent = ticket.timeline[ticket.timeline.length - 1]
  
  if (!firstEvent || !lastEvent) return '-'
  
  const start = new Date(firstEvent.timestamp).getTime()
  const end = new Date(lastEvent.timestamp).getTime()
  
  return formatDuration(end - start)
}

const calculateProgress = (ticket: TicketWithTimeline) => {
  const totalSteps = 4 // 创建、分配、处理、完成
  const currentStep = ticket.timeline.length
  return Math.round((currentStep / totalSteps) * 100)
}

const calculateAvgResponseTime = (ticket: TicketWithTimeline) => {
  const durations = ticket.timeline
    .filter(event => event.duration && event.duration > 0)
    .map(event => event.duration!)
  
  if (durations.length === 0) return '-'
  
  const avg = durations.reduce((sum, duration) => sum + duration, 0) / durations.length
  return formatDuration(avg)
}

// 事件处理函数
const loadTimelines = () => {
  loading.value = true
  // 模拟加载
  setTimeout(() => {
    loading.value = false
    message.success('数据加载完成')
  }, 1000)
}

const viewTicketDetail = (ticket: TicketWithTimeline) => {
  selectedTicket.value = ticket
  showDetailModal.value = true
}

const previewFile = (file: any) => {
  previewFileData.value = file
  showFileModal.value = true
}

const downloadFile = (file: any) => {
  // 模拟下载
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.name
  link.click()
  message.success('文件下载开始')
}

onMounted(() => {
  loadTimelines()
})
</script>

<style scoped>
.ticket-timeline {
  padding: 16px;
}

.filter-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.mr-4 {
  margin-right: 16px;
}

.ticket-card {
  transition: all 0.3s ease;
}

.ticket-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ticket-info {
  flex: 1;
}

.ticket-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.ticket-meta {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.ticket-actions {
  display: flex;
  align-items: center;
}

.text-gray-500 {
  color: #6b7280;
}

.timeline-event {
  margin-top: 8px;
}

.event-details {
  margin-bottom: 8px;
}

.event-operator,
.event-duration,
.event-note {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.event-attachments {
  margin-top: 8px;
}

.ticket-stats {
  display: flex;
  gap: 24px;
}

.detail-timeline {
  margin-bottom: 24px;
}

.timeline-chart {
  margin-top: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.event-title {
  font-weight: 600;
}

.event-time {
  font-size: 12px;
  color: #666;
}

.timeline-content {
  margin-top: 8px;
}

.event-metrics {
  margin-top: 8px;
}
</style>