/**
 * Customer Management API Routes
 * 客户管理API路由
 * 
 * Features:
 * - CRUD operations for customers
 * - Pagination and search
 * - Batch operations
 * - Import/Export functionality
 * - Complete error handling
 */

import { Router, Request, Response } from 'express';
import { MySQLManager } from '../../src/database/MySQLManager';
import multer from 'multer';
import * as XLSX from 'xlsx';
import { v4 as uuidv4 } from 'uuid';
import { ApiError, ResponseUtil, asyncHandler } from '../middleware/errorHandler';
import { createValidationMiddleware, createQueryValidationMiddleware, createParamsValidationMiddleware, CommonValidationRules } from '../middleware/validation';

const router = Router();

// 配置multer用于文件上传
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 初始化MySQL管理器
const mysqlManager = new MySQLManager({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'workchat_admin',
  connectionLimit: 10
});

// 初始化数据库连接
mysqlManager.initialize().catch(console.error);

// 接口响应类型定义
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 客户数据接口
interface Customer {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  wechat?: string;
  company?: string;
  position?: string;
  address?: string;
  source?: string;
  status?: string;
  level?: string;
  birthday?: string;
  gender?: string;
  is_vip?: boolean;
  is_high_value?: boolean;
  is_in_pool?: boolean;
  assigned_to?: number;
  follow_count?: number;
  deal_amount?: number;
  remark?: string;
  created_at?: string;
  updated_at?: string;
}

// 搜索参数接口
interface SearchParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
  source?: string;
  level?: string;
  ownerId?: number;
  isInPool?: boolean;
  startDate?: string;
  endDate?: string;
}

/**
 * 获取客户列表
 * GET /api/customer
 */
router.get('/', 
  createQueryValidationMiddleware([
    ...CommonValidationRules.pagination,
    {
      field: 'keyword',
      type: 'string',
      maxLength: 100,
      message: '搜索关键词不能超过100个字符'
    },
    {
      field: 'status',
      type: 'string',
      pattern: /^(潜在客户|意向客户|成交客户|流失客户)$/,
      message: '状态值不正确'
    },
    {
      field: 'source',
      type: 'string',
      maxLength: 50,
      message: '来源不能超过50个字符'
    },
    {
      field: 'level',
      type: 'string',
      pattern: /^(A|B|C|D)$/,
      message: '客户等级必须是 A、B、C 或 D'
    },
    {
      field: 'ownerId',
      type: 'number',
      min: 1,
      message: '负责人ID必须是正整数'
    },
    {
      field: 'isInPool',
      type: 'boolean',
      message: '公海池状态必须是布尔值'
    },
    {
      field: 'startDate',
      type: 'string',
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '开始日期格式不正确，应为 YYYY-MM-DD'
    },
    {
      field: 'endDate',
      type: 'string',
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '结束日期格式不正确，应为 YYYY-MM-DD'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const {
      page = 1,
      pageSize = 10,
      keyword,
      status,
      source,
      level,
      ownerId,
      isInPool,
      startDate,
      endDate
    }: SearchParams = req.query;

    // 确保分页参数为有效数字
    const currentPage = Math.max(1, Number(page) || 1);
    const currentPageSize = Math.max(1, Math.min(100, Number(pageSize) || 10));
    const offset = (currentPage - 1) * currentPageSize;

    let whereClause = '1=1';
    const whereParams: any[] = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.company LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND c.status = ?';
      whereParams.push(status);
    }

    // 来源筛选
    if (source) {
      whereClause += ' AND c.source = ?';
      whereParams.push(source);
    }

    // 客户等级筛选
    if (level) {
      whereClause += ' AND c.level = ?';
      whereParams.push(level);
    }

    // 负责人筛选
    if (ownerId) {
      whereClause += ' AND c.assigned_to = ?';
      whereParams.push(ownerId);
    }

    // 公海池筛选 - 字段不存在，暂时跳过
    // if (isInPool !== undefined) {
    //   whereClause += ' AND c.is_in_pool = ?';
    //   whereParams.push(isInPool ? 1 : 0);
    // }

    // 日期范围筛选
    if (startDate) {
      whereClause += ' AND c.created_at >= ?';
      whereParams.push(startDate);
    }
    if (endDate) {
      whereClause += ' AND c.created_at <= ?';
      whereParams.push(endDate + ' 23:59:59');
    }

    // 查询客户列表
    const sql = `
      SELECT *
      FROM customers c
      WHERE ${whereClause}
      ORDER BY c.updated_at DESC
      LIMIT ?, ?
    `;

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM customers c
      WHERE ${whereClause}
    `;

    // 调试信息
    console.log('SQL查询参数调试:');
    // 确保LIMIT和OFFSET参数是整数
    const limitValue = parseInt(currentPageSize.toString(), 10);
    const offsetValue = parseInt(offset.toString(), 10);
    
    console.log('limitValue:', limitValue, 'offsetValue:', offsetValue);
    console.log('whereParams:', whereParams);

    // 简化查询，直接查询customers表
    let customersResult, countResult;
    try {
      [customersResult, countResult] = await Promise.all([
        mysqlManager.query(sql, [...whereParams, offsetValue, limitValue]),
        mysqlManager.query(countSql, whereParams)
      ]);
    } catch (error) {
      console.log('查询执行失败:', error);
      throw new ApiError('数据库查询失败', 500);
    }

    if (!customersResult.success || !countResult.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    const total = (countResult.data as any[])[0]?.total || 0;

    ResponseUtil.success(res, {
      data: customersResult.data,
      pagination: {
        page: currentPage,
        pageSize: currentPageSize,
        total: total,
        totalPages: Math.ceil(total / currentPageSize)
      }
    }, '获取客户列表成功');
  })
);

/**
 * 导出客户数据
 * GET /api/customer/export
 */
router.get('/export', 
  createQueryValidationMiddleware([
    {
      field: 'format',
      required: false,
      pattern: /^(xlsx|csv)$/,
      message: '导出格式只支持xlsx或csv'
    },
    {
      field: 'keyword',
      required: false,
      type: 'string',
      maxLength: 100
    },
    {
      field: 'status',
      required: false,
      pattern: /^(潜在客户|意向客户|成交客户|流失客户)$/,
      message: '状态值不正确'
    },
    {
      field: 'source',
      required: false,
      type: 'string',
      maxLength: 50
    },
    {
      field: 'startDate',
      required: false,
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '开始日期格式不正确，应为YYYY-MM-DD'
    },
    {
      field: 'endDate',
      required: false,
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '结束日期格式不正确，应为YYYY-MM-DD'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const {
      format = 'xlsx',
      keyword,
      status,
      source,
      level,
      ownerId,
      isInPool,
      startDate,
      endDate
    }: SearchParams & { format?: string } = req.query;

    let whereClause = '1=1';
    const whereParams: any[] = [];

    // 应用相同的筛选条件
    if (keyword) {
      whereClause += ' AND (c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.company LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    if (status) {
      whereClause += ' AND c.status = ?';
      whereParams.push(status);
    }

    if (source) {
      whereClause += ' AND c.source = ?';
      whereParams.push(source);
    }

    if (level) {
      whereClause += ' AND c.level = ?';
      whereParams.push(level);
    }

    if (ownerId) {
      whereClause += ' AND c.assigned_to = ?';
      whereParams.push(ownerId);
    }

    // 公海池筛选 - 字段不存在，暂时跳过
    // if (isInPool !== undefined) {
    //   whereClause += ' AND c.is_in_pool = ?';
    //   whereParams.push(isInPool ? 1 : 0);
    // }

    if (startDate) {
      whereClause += ' AND c.created_at >= ?';
      whereParams.push(startDate);
    }
    if (endDate) {
      whereClause += ' AND c.created_at <= ?';
      whereParams.push(endDate + ' 23:59:59');
    }

    // 查询所有符合条件的客户数据（限制导出数量）
    const sql = `
      SELECT 
        c.id as '客户ID',
        c.name as '客户姓名',
        c.phone as '电话',
        c.email as '邮箱',
        c.company as '公司',
        c.position as '职位',
        c.region as '地区',
        c.address as '地址',
        c.source as '来源',
        c.status as '状态',
        c.level as '等级',
        c.age as '年龄',
        c.gender as '性别',
        c.decoration_type as '装修类型',
        c.house_status as '房屋状态',
        c.budget_range as '预算范围',
        c.contact_time as '联系时间',
        c.notes as '备注',
        u.name as '负责人',
        d.name as '部门',
        DATE_FORMAT(c.created_at, '%Y-%m-%d %H:%i:%s') as '创建时间',
        DATE_FORMAT(c.updated_at, '%Y-%m-%d %H:%i:%s') as '更新时间'
      FROM customers c
      LEFT JOIN users u ON c.assigned_to = u.id
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT 10000
    `;

    const result = await mysqlManager.query(sql, whereParams);

    if (!result.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    const customers = result.data as any[];

    if (customers.length === 0) {
      throw new ApiError('没有找到符合条件的客户数据', 404);
    }

    if (format === 'csv') {
      // 导出CSV格式
      const csvData = customers.map(customer => {
        const row: any = {};
        Object.keys(customer).forEach(key => {
          row[key] = customer[key] || '';
        });
        return row;
      });
      
      const csvContent = Object.keys(csvData[0]).join(',') + '\n' +
        csvData.map(row => Object.values(row).map(value => `"${value}"`).join(',')).join('\n');
      
      const filename = `客户数据_${new Date().toISOString().slice(0, 10)}.csv`;
      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      res.send('\uFEFF' + csvContent); // 添加BOM以支持中文
      
    } else {
      // 创建Excel工作簿
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(customers);

      // 设置列宽
      const colWidths = [
        { wch: 10 }, // 客户ID
        { wch: 15 }, // 客户姓名
        { wch: 15 }, // 电话
        { wch: 20 }, // 邮箱
        { wch: 15 }, // 微信
        { wch: 20 }, // 公司
        { wch: 15 }, // 职位
        { wch: 30 }, // 地址
        { wch: 10 }, // 来源
        { wch: 10 }, // 状态
        { wch: 8 },  // 等级
        { wch: 12 }, // 生日
        { wch: 8 },  // 性别
        { wch: 10 }, // VIP客户
        { wch: 12 }, // 高价值客户
        { wch: 10 }, // 公海池
        { wch: 15 }, // 负责人
        { wch: 15 }, // 部门
        { wch: 10 }, // 跟进次数
        { wch: 12 }, // 成交金额
        { wch: 30 }, // 备注
        { wch: 20 }, // 创建时间
        { wch: 20 }  // 更新时间
      ];
      worksheet['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, '客户数据');

      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      const filename = `客户数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      res.setHeader('Content-Length', excelBuffer.length);

      res.send(excelBuffer);
    }
  })
);

/**
 * 批量删除客户
 * DELETE /api/customer/batch
 */
router.delete('/batch',
  createValidationMiddleware([
    {
      field: 'ids',
      required: true,
      type: 'array',
      minLength: 1,
      message: '请提供要删除的客户ID列表'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    await mysqlManager.initialize();
    
    const { ids, operatorId } = req.body;
    
    // 验证所有ID都是有效的数字
    const validIds = ids.filter((id: any) => Number.isInteger(Number(id)) && Number(id) > 0);
    if (validIds.length === 0) {
      throw new ApiError('请提供有效的客户ID', 400);
    }
    
    if (validIds.length > 100) {
      throw new ApiError('一次最多只能删除100个客户', 400);
    }
    
    // 检查客户是否存在
    const placeholders = validIds.map(() => '?').join(',');
    const checkSql = `SELECT id, name FROM customers WHERE id IN (${placeholders})`;
    const checkResult = await mysqlManager.query(checkSql, validIds);
    
    if (!checkResult.success) {
      throw new ApiError('查询客户信息失败', 500);
    }
    
    const existingCustomers = checkResult.data as any[];
    if (existingCustomers.length === 0) {
      throw new ApiError('没有找到要删除的客户', 404);
    }
    
    // 执行软删除
    const deleteSql = `DELETE FROM customers WHERE id IN (${placeholders})`;
    const deleteResult = await mysqlManager.query(deleteSql, validIds);
    
    if (!deleteResult.success) {
      throw new ApiError('删除客户失败', 500);
    }
    
    // 记录操作日志
    const logPromises = existingCustomers.map(customer => {
      const logSql = `
        INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
        VALUES (?, ?, ?, ?, ?)
      `;
      return mysqlManager.query(logSql, [
        customer.id,
        '删除客户',
        `批量删除了客户：${customer.name}`,
        operatorId || null,
        new Date()
      ]);
    });
    
    await Promise.all(logPromises);
    
    ResponseUtil.success(res, {
      deletedCount: existingCustomers.length,
      deletedCustomers: existingCustomers
    }, `成功删除 ${existingCustomers.length} 个客户`);
  })
);

/**
 * 批量更新客户状态
 * PUT /api/customer/batch
 */
router.put('/batch',
  createValidationMiddleware([
    {
      field: 'ids',
      required: true,
      type: 'array',
      minLength: 1,
      message: '请提供要更新的客户ID列表'
    },
    {
      field: 'updates',
      required: true,
      type: 'object',
      message: '请提供要更新的字段'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    await mysqlManager.initialize();
    
    const { ids, updates, operatorId } = req.body;
    
    // 验证更新字段
    const allowedFields = ['status', 'level', 'assigned_to', 'is_vip', 'is_high_value', 'is_in_pool', 'source'];
    const updateFields = Object.keys(updates);
    const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
    
    if (invalidFields.length > 0) {
      throw new ApiError(`不允许更新的字段: ${invalidFields.join(', ')}`, 400);
    }
    
    if (updateFields.length === 0) {
      throw new ApiError('请提供要更新的字段', 400);
    }
    
    // 验证所有ID都是有效的数字
    const validIds = ids.filter((id: any) => Number.isInteger(Number(id)) && Number(id) > 0);
    if (validIds.length === 0) {
      throw new ApiError('请提供有效的客户ID', 400);
    }
    
    if (validIds.length > 100) {
      throw new ApiError('一次最多只能更新100个客户', 400);
    }
    
    // 检查客户是否存在
    const placeholders = validIds.map(() => '?').join(',');
    const checkSql = `SELECT id, name FROM customers WHERE id IN (${placeholders})`;
    const checkResult = await mysqlManager.query(checkSql, validIds);
    
    if (!checkResult.success) {
      throw new ApiError('查询客户信息失败', 500);
    }
    
    const existingCustomers = checkResult.data as any[];
    if (existingCustomers.length === 0) {
      throw new ApiError('没有找到要更新的客户', 404);
    }
    
    // 构建更新SQL
    const setClause = updateFields.map(field => `${field} = ?`).join(', ');
    const updateSql = `UPDATE customers SET ${setClause}, updated_at = ? WHERE id IN (${placeholders})`;
    const updateValues = [...updateFields.map(field => updates[field]), new Date(), ...validIds];
    
    const updateResult = await mysqlManager.query(updateSql, updateValues);
    
    if (!updateResult.success) {
      throw new ApiError('更新客户失败', 500);
    }
    
    // 记录操作日志
    const updateDescription = updateFields.map(field => `${field}: ${updates[field]}`).join(', ');
    const logPromises = existingCustomers.map(customer => {
      const logSql = `
        INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
        VALUES (?, ?, ?, ?, ?)
      `;
      return mysqlManager.query(logSql, [
        customer.id,
        '批量更新',
        `批量更新了客户 ${customer.name} 的信息：${updateDescription}`,
        operatorId || null,
        new Date()
      ]);
    });
    
    await Promise.all(logPromises);
    
    ResponseUtil.success(res, {
      updatedCount: existingCustomers.length,
      updatedCustomers: existingCustomers,
      updates: updates
    }, `成功更新 ${existingCustomers.length} 个客户`);
  })
);

/**
 * 删除客户
 * DELETE /api/customer/:id
 */
router.delete('/:id', 
  createParamsValidationMiddleware([CommonValidationRules.id]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    // 检查客户是否存在
    const checkResult = await mysqlManager.query(
      'SELECT id, name FROM customers WHERE id = ?',
      [Number(id)]
    );

    if (!checkResult.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    if ((checkResult.data as any[]).length === 0) {
      throw new ApiError('客户不存在', 404);
    }

    const customer = (checkResult.data as any[])[0];

    // 软删除：更新删除标记
    const deleteSql = `
        DELETE FROM customers 
        WHERE id = ?
      `;
    const deleteResult = await mysqlManager.query(deleteSql, [customerId]);

    if (!deleteResult.success) {
      throw new ApiError('数据库删除失败', 500);
    }

    // 记录操作日志
    const logSql = `
      INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
      VALUES (?, ?, ?, ?, ?)
    `;
    await mysqlManager.query(logSql, [
      Number(id),
      '删除客户',
      `删除了客户：${customer.name}`,
      req.body.operatorId || null,
      new Date()
    ]);

    ResponseUtil.success(res, null, '客户删除成功');
  })
);

/**
 * 导入客户数据
 * POST /api/customer/import
 */
router.post('/import', 
  upload.single('file'),
  createValidationMiddleware([
    {
      field: 'operatorId',
      required: false,
      type: 'number',
      min: 1,
      message: '操作员ID必须是正整数'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.file) {
      throw new ApiError('请上传Excel文件', 400);
    }

    // 验证文件类型
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    if (!allowedMimeTypes.includes(req.file.mimetype)) {
      throw new ApiError('只支持Excel文件格式(.xlsx, .xls)', 400);
    }

    // 验证文件大小（最大10MB）
    if (req.file.size > 10 * 1024 * 1024) {
      throw new ApiError('文件大小不能超过10MB', 400);
    }

    const { operatorId } = req.body;

    // 解析Excel文件
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    if (jsonData.length === 0) {
      throw new ApiError('Excel文件中没有数据', 400);
    }

    if (jsonData.length > 1000) {
      throw new ApiError('一次最多只能导入1000条记录', 400);
    }

    const results = {
      total: jsonData.length,
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    // 逐行处理数据
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i] as any;
      const rowIndex = i + 2; // Excel行号（从第2行开始）

      try {
        // 验证必填字段
        const name = row['客户姓名'] || row['姓名'] || row['name'];
        const phone = row['电话'] || row['手机'] || row['phone'];

        if (!name || !phone) {
          results.errors.push(`第${rowIndex}行：客户姓名和电话为必填项`);
          results.failed++;
          continue;
        }

        // 验证电话号码格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
          results.errors.push(`第${rowIndex}行：电话号码格式不正确`);
          results.failed++;
          continue;
        }

        // 检查电话号码是否已存在
        const checkPhoneResult = await mysqlManager.query(
          'SELECT id FROM customers WHERE phone = ?',
          [phone]
        );

        if (!checkPhoneResult.success) {
          results.errors.push(`第${rowIndex}行：数据库查询失败`);
          results.failed++;
          continue;
        }

        if ((checkPhoneResult.data as any[]).length > 0) {
          results.errors.push(`第${rowIndex}行：电话号码 ${phone} 已存在`);
          results.failed++;
          continue;
        }

        // 准备插入数据
        const customerData = {
          name: name,
          phone: phone,
          email: row['邮箱'] || row['email'] || null,
          wechat: row['微信'] || row['wechat'] || null,
          company: row['公司'] || row['company'] || null,
          position: row['职位'] || row['position'] || null,
          address: row['地址'] || row['address'] || null,
          source: row['来源'] || row['source'] || '导入',
          status: row['状态'] || row['status'] || '潜在客户',
          level: row['等级'] || row['level'] || 'C',
          birthday: row['生日'] || row['birthday'] || null,
          gender: row['性别'] || row['gender'] || null,
          is_vip: (row['VIP客户'] === '是' || row['is_vip'] === true) ? 1 : 0,
          is_high_value: (row['高价值客户'] === '是' || row['is_high_value'] === true) ? 1 : 0,
          is_in_pool: (row['公海池'] === '是' || row['is_in_pool'] === true) ? 1 : 0,
          assigned_to: null, // 导入时默认不分配负责人
          follow_count: 0,
          deal_amount: parseFloat(row['成交金额'] || row['deal_amount'] || '0') || 0,
          remark: row['备注'] || row['remark'] || null,
          created_at: new Date(),
          updated_at: new Date()
        };

        const columns = Object.keys(customerData).join(', ');
        const placeholders = Object.keys(customerData).map(() => '?').join(', ');
        const values = Object.values(customerData);

        const insertSql = `INSERT INTO customers (${columns}) VALUES (${placeholders})`;
        const insertResult = await mysqlManager.query(insertSql, values);

        if (!insertResult.success) {
          results.errors.push(`第${rowIndex}行：数据库插入失败`);
          results.failed++;
          continue;
        }

        const customerId = (insertResult.data as any).insertId;

        // 记录操作日志
        const logSql = `
          INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
          VALUES (?, ?, ?, ?, ?)
        `;
        await mysqlManager.query(logSql, [
          customerId,
          '导入客户',
          `通过Excel导入了客户：${name}`,
          operatorId || null,
          new Date()
        ]);

        results.success++;

      } catch (error) {
        console.error(`处理第${rowIndex}行数据错误:`, error);
        results.errors.push(`第${rowIndex}行：处理数据时发生错误`);
        results.failed++;
      }
    }

    ResponseUtil.success(res, results, `导入完成：成功 ${results.success} 条，失败 ${results.failed} 条`);
  })
);

/**
 * 下载导入模板
 * GET /api/customer/template
 */
router.get('/template', async (req: Request, res: Response): Promise<void> => {
  try {
    // 创建模板数据
    const templateData = [
      {
        '客户姓名': '张三',
        '电话': '13800138000',
        '邮箱': '<EMAIL>',
        '微信': 'zhangsan_wx',
        '公司': '示例公司',
        '职位': '经理',
        '地址': '北京市朝阳区',
        '来源': '网络推广',
        '状态': '潜在客户',
        '等级': 'A',
        '生日': '1990-01-01',
        '性别': '男',
        'VIP客户': '否',
        '高价值客户': '是',
        '公海池': '否',
        '成交金额': '0',
        '备注': '这是一个示例客户'
      }
    ];

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 客户姓名
      { wch: 15 }, // 电话
      { wch: 25 }, // 邮箱
      { wch: 15 }, // 微信
      { wch: 20 }, // 公司
      { wch: 15 }, // 职位
      { wch: 30 }, // 地址
      { wch: 12 }, // 来源
      { wch: 12 }, // 状态
      { wch: 8 },  // 等级
      { wch: 12 }, // 生日
      { wch: 8 },  // 性别
      { wch: 10 }, // VIP客户
      { wch: 12 }, // 高价值客户
      { wch: 10 }, // 公海池
      { wch: 12 }, // 成交金额
      { wch: 30 }  // 备注
    ];
    worksheet['!cols'] = colWidths;

    XLSX.utils.book_append_sheet(workbook, worksheet, '客户导入模板');

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // 设置响应头
    const filename = '客户导入模板.xlsx';
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    res.setHeader('Content-Length', excelBuffer.length);

    res.send(excelBuffer);

  } catch (error) {
    console.error('下载模板错误:', error);
    res.status(500).json({
      success: false,
      message: '下载模板失败'
    });
  }
});

/**
 * 获取客户详情
 * GET /api/customer/:id
 */
router.get('/:id', 
  createParamsValidationMiddleware([CommonValidationRules.id]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    const sql = `
      SELECT 
        c.*,
        u.name as owner_name,
        d.name as department_name
      FROM customers c
      LEFT JOIN users u ON c.assigned_to = u.id
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE c.id = ?
    `;

    const result = await mysqlManager.query(sql, [Number(id)]);

    if (!result.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    const customer = (result.data as any[])[0];

    if (!customer) {
      throw new ApiError('客户不存在', 404);
    }

    ResponseUtil.success(res, customer, '获取客户详情成功');
  })
);

/**
 * 创建客户
 * POST /api/customer
 */
router.post('/', 
  createValidationMiddleware([
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 50,
      message: '客户姓名是必填项，长度在1-50个字符之间'
    },
    {
      field: 'phone',
      required: true,
      type: 'string',
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号码'
    },
    {
      field: 'email',
      type: 'string',
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入有效的邮箱地址'
    },
    {
      field: 'company',
      type: 'string',
      maxLength: 100,
      message: '公司名称不能超过100个字符'
    },
    {
      field: 'position',
      type: 'string',
      maxLength: 50,
      message: '职位不能超过50个字符'
    },
    {
      field: 'address',
      type: 'string',
      maxLength: 200,
      message: '地址不能超过200个字符'
    },
    {
      field: 'status',
      type: 'string',
      pattern: /^(潜在客户|意向客户|成交客户|流失客户)$/,
      message: '状态值不正确'
    },
    {
      field: 'level',
      type: 'string',
      pattern: /^(A|B|C|D)$/,
      message: '客户等级必须是 A、B、C 或 D'
    },
    {
      field: 'assigned_to',
      type: 'number',
      min: 1,
      message: '负责人ID必须是正整数'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const customerData: Customer = req.body;

    // 检查电话号码是否已存在
    const checkPhoneResult = await mysqlManager.query(
      'SELECT id FROM customers WHERE phone = ?',
      [customerData.phone]
    );

    if (!checkPhoneResult.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    if ((checkPhoneResult.data as any[]).length > 0) {
      throw new ApiError('该电话号码已存在', 400);
    }

    // 准备插入数据
    const insertData = {
      name: customerData.name,
      phone: customerData.phone,
      email: customerData.email || null,
      wechat: customerData.wechat || null,
      company: customerData.company || null,
      position: customerData.position || null,
      address: customerData.address || null,
      source: customerData.source || '其他',
      status: customerData.status || '潜在客户',
      level: customerData.level || 'C',
      birthday: customerData.birthday || null,
      gender: customerData.gender || null,
      is_vip: customerData.is_vip || false,
      is_high_value: customerData.is_high_value || false,
      is_in_pool: customerData.is_in_pool || false,
      assigned_to: customerData.assigned_to || null,
      follow_count: 0,
      deal_amount: customerData.deal_amount || 0,
      remark: customerData.remark || null,
      created_at: new Date(),
      updated_at: new Date()
    };

    const columns = Object.keys(insertData).join(', ');
    const placeholders = Object.keys(insertData).map(() => '?').join(', ');
    const values = Object.values(insertData);

    const insertSql = `INSERT INTO customers (${columns}) VALUES (${placeholders})`;
    const insertResult = await mysqlManager.query(insertSql, values);

    if (!insertResult.success) {
      throw new ApiError('数据库插入失败', 500);
    }

    const customerId = (insertResult.data as any).insertId;

    // 记录操作日志
    const logSql = `
      INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
      VALUES (?, ?, ?, ?, ?)
    `;
    await mysqlManager.query(logSql, [
      customerId,
      '创建客户',
      `创建了客户：${customerData.name}`,
      req.body.operatorId || null,
      new Date()
    ]);

    ResponseUtil.success(res, { id: customerId }, '客户创建成功', 201);
  })
);

/**
 * 更新客户
 * PUT /api/customer/:id
 */
router.put('/:id', 
  createParamsValidationMiddleware([CommonValidationRules.id]),
  createValidationMiddleware([
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 50,
      message: '客户姓名是必填项，长度在1-50个字符之间'
    },
    {
      field: 'phone',
      required: true,
      type: 'string',
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号码'
    },
    {
      field: 'email',
      type: 'string',
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入有效的邮箱地址'
    },
    {
      field: 'company',
      type: 'string',
      maxLength: 100,
      message: '公司名称不能超过100个字符'
    },
    {
      field: 'position',
      type: 'string',
      maxLength: 50,
      message: '职位不能超过50个字符'
    },
    {
      field: 'address',
      type: 'string',
      maxLength: 200,
      message: '地址不能超过200个字符'
    },
    {
      field: 'status',
      type: 'string',
      pattern: /^(潜在客户|意向客户|成交客户|流失客户)$/,
      message: '状态值不正确'
    },
    {
      field: 'level',
      type: 'string',
      pattern: /^(A|B|C|D)$/,
      message: '客户等级必须是 A、B、C 或 D'
    },
    {
      field: 'assigned_to',
      type: 'number',
      min: 1,
      message: '负责人ID必须是正整数'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const customerData: Customer = req.body;

    // 检查客户是否存在
    const checkResult = await mysqlManager.query(
      'SELECT id, name FROM customers WHERE id = ?',
      [Number(id)]
    );

    if (!checkResult.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    if ((checkResult.data as any[]).length === 0) {
      throw new ApiError('客户不存在', 404);
    }

    const oldCustomer = (checkResult.data as any[])[0];

    // 检查电话号码是否被其他客户使用
    const checkPhoneResult = await mysqlManager.query(
      'SELECT id FROM customers WHERE phone = ? AND id != ?',
      [customerData.phone, Number(id)]
    );

    if (!checkPhoneResult.success) {
      throw new ApiError('数据库查询失败', 500);
    }

    if ((checkPhoneResult.data as any[]).length > 0) {
      throw new ApiError('该电话号码已被其他客户使用', 400);
    }

    // 准备更新数据
    const updateData = {
      name: customerData.name,
      phone: customerData.phone,
      email: customerData.email || null,
      wechat: customerData.wechat || null,
      company: customerData.company || null,
      position: customerData.position || null,
      address: customerData.address || null,
      source: customerData.source,
      status: customerData.status,
      level: customerData.level,
      birthday: customerData.birthday || null,
      gender: customerData.gender || null,
      is_vip: customerData.is_vip,
      is_high_value: customerData.is_high_value,
      is_in_pool: customerData.is_in_pool,
      assigned_to: customerData.assigned_to || null,
      deal_amount: customerData.deal_amount,
      remark: customerData.remark || null,
      updated_at: new Date()
    };

    const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(updateData), Number(id)];

    const updateSql = `UPDATE customers SET ${setClause} WHERE id = ?`;
    const updateResult = await mysqlManager.query(updateSql, values);

    if (!updateResult.success) {
      throw new ApiError('数据库更新失败', 500);
    }

    // 记录操作日志
    const logSql = `
      INSERT INTO customer_logs (customer_id, action, content, created_by, created_at)
      VALUES (?, ?, ?, ?, ?)
    `;
    await mysqlManager.query(logSql, [
      Number(id),
      '更新客户',
      `更新了客户信息：${oldCustomer.name} -> ${customerData.name}`,
      req.body.operatorId || null,
      new Date()
    ]);

    ResponseUtil.success(res, null, '客户更新成功');
  })
);











/**
 * 获取客户统计信息
 * GET /api/customer/stats
 */
router.get('/stats', 
  createQueryValidationMiddleware([
    {
      field: 'startDate',
      required: false,
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '开始日期格式不正确，应为YYYY-MM-DD'
    },
    {
      field: 'endDate',
      required: false,
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '结束日期格式不正确，应为YYYY-MM-DD'
    }
  ]),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    await mysqlManager.initialize();
    
    const { startDate, endDate } = req.query;
    
    let dateFilter = '';
    const params: any[] = [];
    
    if (startDate && endDate) {
      dateFilter = 'AND created_at BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    // 获取总客户数
    const totalSql = `SELECT COUNT(*) as total FROM customers WHERE 1=1 ${dateFilter}`;
    const totalResult = await mysqlManager.query(totalSql, params);
    
    if (!totalResult.success) {
      throw new ApiError('查询总客户数失败', 500);
    }
    
    const total = (totalResult.data as any[])[0].total;
    
    // 按状态统计
    const statusSql = `
      SELECT status, COUNT(*) as count 
      FROM customers 
      WHERE 1=1 ${dateFilter}
      GROUP BY status
    `;
    const statusResult = await mysqlManager.query(statusSql, params);
    
    if (!statusResult.success) {
      throw new ApiError('查询状态统计失败', 500);
    }
    
    // 按来源统计
    const sourceSql = `
      SELECT source, COUNT(*) as count 
      FROM customers 
      WHERE 1=1 ${dateFilter}
      GROUP BY source
    `;
    const sourceResult = await mysqlManager.query(sourceSql, params);
    
    if (!sourceResult.success) {
      throw new ApiError('查询来源统计失败', 500);
    }
    
    // 按月份统计新增客户
    const monthlySql = `
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count
      FROM customers 
      WHERE is_deleted = 0 ${dateFilter}
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month DESC
      LIMIT 12
    `;
    const monthlyResult = await mysqlManager.query(monthlySql, params);
    
    if (!monthlyResult.success) {
      throw new ApiError('查询月度统计失败', 500);
    }
    
    ResponseUtil.success(res, {
      total,
      statusStats: statusResult.data,
      sourceStats: sourceResult.data,
      monthlyStats: monthlyResult.data
    }, '获取客户统计信息成功');
  })
);

export default router;