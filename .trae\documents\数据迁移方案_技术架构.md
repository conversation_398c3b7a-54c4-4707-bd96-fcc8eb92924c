# 硬编码数据迁移到Supabase - 技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[Vue3 前端应用] --> B[Supabase Client SDK]
    B --> C[Supabase 数据库]
    
    D[系统设置管理页面] --> E[选项数据API服务]
    F[客户管理页面] --> E
    G[营销活动页面] --> E
    
    E --> B
    
    subgraph "前端层"
        A
        D
        F
        G
    end
    
    subgraph "数据服务层"
        B
        E
    end
    
    subgraph "数据存储层"
        C
    end
```

## 2. 技术描述

* 前端：Vue3 + TypeScript + Naive UI + Pinia + Vite

* 数据库：Supabase (PostgreSQL)

* 状态管理：Pinia stores

* API通信：Supabase Client SDK

## 3. 路由定义

| 路由                          | 用途               |
| --------------------------- | ---------------- |
| /settings/options           | 系统设置 - 选项数据管理页面  |
| /settings/options/:category | 特定类别的选项管理页面      |
| /customer/list              | 客户列表页面（使用动态选项数据） |
| /customer/create            | 客户创建页面（使用动态选项数据） |
| /customer/edit/:id          | 客户编辑页面（使用动态选项数据） |

## 4. API定义

### 4.1 核心API

**获取选项分类列表**

```typescript
GET /api/option-categories
```

响应：

| 参数名         | 参数类型    | 描述   |
| ----------- | ------- | ---- |
| id          | string  | 分类ID |
| name        | string  | 分类名称 |
| code        | string  | 分类代码 |
| description | string  | 分类描述 |
| sort\_order | number  | 排序序号 |
| is\_active  | boolean | 是否启用 |

**获取指定分类的选项列表**

```typescript
GET /api/option-items?category_code={code}
```

响应：

| 参数名          | 参数类型    | 描述     |
| ------------ | ------- | ------ |
| id           | string  | 选项ID   |
| category\_id | string  | 所属分类ID |
| label        | string  | 显示标签   |
| value        | string  | 选项值    |
| color        | string  | 颜色代码   |
| icon         | string  | 图标名称   |
| description  | string  | 选项描述   |
| sort\_order  | number  | 排序序号   |
| is\_active   | boolean | 是否启用   |

**创建选项**

```typescript
POST /api/option-items
```

请求：

| 参数名          | 参数类型   | 是否必需  | 描述     |
| ------------ | ------ | ----- | ------ |
| category\_id | string | true  | 所属分类ID |
| label        | string | true  | 显示标签   |
| value        | string | true  | 选项值    |
| color        | string | false | 颜色代码   |
| icon         | string | false | 图标名称   |
| description  | string | false | 选项描述   |
| sort\_order  | number | false | 排序序号   |

**更新选项**

```typescript
PUT /api/option-items/{id}
```

**删除选项**

```typescript
DELETE /api/option-items/{id}
```

## 5. 数据模型

### 5.1 数据模型定义

```mermaid
erDiagram
    OPTION_CATEGORIES ||--o{ OPTION_ITEMS : contains
    
    OPTION_CATEGORIES {
        uuid id PK
        string name
        string code UK
        string description
        integer sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    OPTION_ITEMS {
        uuid id PK
        uuid category_id FK
        string label
        string value
        string color
        string icon
        string description
        integer sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
```

### 5.2 数据定义语言

**选项分类表 (option\_categories)**

```sql
-- 创建选项分类表
CREATE TABLE option_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_option_categories_code ON option_categories(code);
CREATE INDEX idx_option_categories_sort_order ON option_categories(sort_order);

-- 设置权限
GRANT SELECT ON option_categories TO anon;
GRANT ALL PRIVILEGES ON option_categories TO authenticated;
```

**选项数据表 (option\_items)**

```sql
-- 创建选项数据表
CREATE TABLE option_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID NOT NULL REFERENCES option_categories(id) ON DELETE CASCADE,
    label VARCHAR(100) NOT NULL,
    value VARCHAR(100) NOT NULL,
    color VARCHAR(20),
    icon VARCHAR(100),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_option_items_category_id ON option_items(category_id);
CREATE INDEX idx_option_items_value ON option_items(value);
CREATE INDEX idx_option_items_sort_order ON option_items(sort_order);
CREATE UNIQUE INDEX idx_option_items_category_value ON option_items(category_id, value);

-- 设置权限
GRANT SELECT ON option_items TO anon;
GRANT ALL PRIVILEGES ON option_items TO authenticated;
```

**初始化数据**

```sql
-- 插入选项分类
INSERT INTO option_categories (name, code, description, sort_order) VALUES
('客户来源', 'customer_source', '客户获取渠道分类', 1),
('客户等级', 'customer_level', '客户价值等级分类', 2),
('性别', 'gender', '性别选项', 3),
('装修类型', 'decoration_type', '房屋装修类型', 4),
('房屋状态', 'house_status', '房屋交付和装修状态', 5);

-- 插入客户来源选项
INSERT INTO option_items (category_id, label, value, color, icon, description, sort_order)
SELECT 
    id,
    unnest(ARRAY['线上推广', '朋友介绍', '展会活动', '门店咨询', '电话营销', '其他渠道']),
    unnest(ARRAY['online', 'referral', 'exhibition', 'store', 'phone', 'other']),
    unnest(ARRAY['#1890ff', '#52c41a', '#faad14', '#722ed1', '#13c2c2', '#8c8c8c']),
    unnest(ARRAY['GlobeOutline', 'ShareOutline', 'TvOutline', 'StorefrontOutline', 'PhonePortraitOutline', 'HelpOutline']),
    unnest(ARRAY['网络广告投放', '客户推荐转介', '线下展览活动', '实体店面咨询', '电话销售推广', '其他获客方式']),
    unnest(ARRAY[1, 2, 3, 4, 5, 6])
FROM option_categories WHERE code = 'customer_source';

-- 插入客户等级选项
INSERT INTO option_items (category_id, label, value, color, icon, description, sort_order)
SELECT 
    id,
    unnest(ARRAY['A级客户', 'B级客户', 'C级客户', 'D级客户']),
    unnest(ARRAY['A', 'B', 'C', 'D']),
    unnest(ARRAY['#f5222d', '#faad14', '#1890ff', '#8c8c8c']),
    unnest(ARRAY['TrophyOutline', 'DiamondOutline', 'RibbonOutline', 'KeyOutline']),
    unnest(ARRAY['高价值客户', '中等价值客户', '一般价值客户', '潜在价值客户']),
    unnest(ARRAY[1, 2, 3, 4])
FROM option_categories WHERE code = 'customer_level';

-- 插入性别选项
INSERT INTO option_items (category_id, label, value, color, icon, description, sort_order)
SELECT 
    id,
    unnest(ARRAY['男', '女']),
    unnest(ARRAY['male', 'female']),
    unnest(ARRAY['#1890ff', '#f5222d']),
    unnest(ARRAY['MaleOutline', 'FemaleOutline']),
    unnest(ARRAY['男性', '女性']),
    unnest(ARRAY[1, 2])
FROM option_categories WHERE code = 'gender';

-- 插入装修类型选项
INSERT INTO option_items (category_id, label, value, color, icon, description, sort_order)
SELECT 
    id,
    unnest(ARRAY['新房', '二手房', '毛坯房', '精装房']),
    unnest(ARRAY['new', 'old', 'rough', 'hardcover']),
    unnest(ARRAY['#52c41a', '#faad14', '#722ed1', '#eb2f96']),
    unnest(ARRAY['ConstructOutline', 'BrushOutline', 'HammerOutline', 'ColorPaletteOutline']),
    unnest(ARRAY['新建房屋', '二手房屋', '毛坯房屋', '精装房屋']),
    unnest(ARRAY[1, 2, 3, 4])
FROM option_categories WHERE code = 'decoration_type';

-- 插入房屋状态选项
INSERT INTO option_items (category_id, label, value, color, icon, description, sort_order)
SELECT 
    id,
    unnest(ARRAY['未交房', '已交房', '装修中', '装修完成']),
    unnest(ARRAY['undelivered', 'delivered', 'renovating', 'completed']),
    unnest(ARRAY['#faad14', '#52c41a', '#1890ff', '#722ed1']),
    unnest(ARRAY['HomeOutline', 'CheckmarkOutline', 'ConstructOutline', 'StarOutline']),
    unnest(ARRAY['房屋未交付', '房屋已交付', '正在装修中', '装修已完成']),
    unnest(ARRAY[1, 2, 3, 4])
FROM option_categories WHERE code = 'house_status';
```

