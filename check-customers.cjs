const mysql = require('mysql2/promise');

async function checkCustomersTable() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'workchat_admin'
  });

  try {
    console.log('检查customers表结构...');
    const [rows] = await connection.execute('DESCRIBE customers');
    console.log('customers表结构:', JSON.stringify(rows, null, 2));
    
    console.log('\n检查customers表数据...');
    const [data] = await connection.execute('SELECT COUNT(*) as count FROM customers');
    console.log('customers表记录数:', data[0].count);
    
    if (data[0].count > 0) {
      const [sample] = await connection.execute('SELECT * FROM customers LIMIT 3');
      console.log('\n示例数据:', JSON.stringify(sample, null, 2));
    }
  } catch (error) {
    console.error('错误:', error.message);
  } finally {
    await connection.end();
  }
}

checkCustomersTable();