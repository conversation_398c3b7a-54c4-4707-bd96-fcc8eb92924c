#!/usr/bin/env tsx

import { ConfigManager, EnvironmentManager } from './mysql/migration-config';
import { MySQLManager } from '../src/database/MySQLManager';
import { createClient } from '@supabase/supabase-js';
import { MigrationLogger } from './mysql/migration-logger';
import { DataIntegrityValidator } from './data-integrity-validator';
import fs from 'fs';
import path from 'path';

/**
 * 迁移脚本测试管理器
 * 用于测试迁移脚本的完整性和可靠性
 */
class MigrationTestManager {
  private logger: MigrationLogger;
  private mysqlManager: MySQLManager;
  private supabaseClient: any;
  private validator: DataIntegrityValidator;
  private testResults: TestResult[] = [];

  constructor() {
    this.logger = new MigrationLogger();
    
    // 初始化MySQL配置
    const mysqlConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'workchat_admin'
    };
    
    this.mysqlManager = new MySQLManager(mysqlConfig);
    this.validator = new DataIntegrityValidator();
  }

  /**
   * 初始化测试环境
   */
  async initialize(): Promise<void> {
    try {
      await this.logger.info('开始初始化测试环境');
      
      // 检查环境变量
      const envManager = new EnvironmentManager();
      const missingVars = envManager.checkRequiredVariables();
      
      if (missingVars.length > 0) {
        throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
      }

      // 初始化数据库连接
      await this.mysqlManager.initialize();
      
      this.supabaseClient = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      await this.logger.info('测试环境初始化完成');
    } catch (error) {
      await this.logger.error('测试环境初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestSummary> {
    await this.logger.info('开始运行迁移脚本测试');
    
    const tests = [
      { name: '环境配置测试', method: this.testEnvironmentConfig.bind(this) },
      { name: '数据库连接测试', method: this.testDatabaseConnections.bind(this) },
      { name: '脚本文件完整性测试', method: this.testScriptIntegrity.bind(this) },
      { name: 'MySQL表结构测试', method: this.testMySQLTableStructure.bind(this) },
      { name: '配置文件验证测试', method: this.testConfigValidation.bind(this) },
      { name: '日志系统测试', method: this.testLoggingSystem.bind(this) },
      { name: '错误处理测试', method: this.testErrorHandling.bind(this) },
      { name: '数据验证器测试', method: this.testDataValidator.bind(this) }
    ];

    for (const test of tests) {
      try {
        await this.logger.info(`运行测试: ${test.name}`);
        const result = await test.method();
        this.testResults.push({
          name: test.name,
          status: result ? 'PASSED' : 'FAILED',
          message: result ? '测试通过' : '测试失败',
          timestamp: new Date()
        });
      } catch (error) {
        this.testResults.push({
          name: test.name,
          status: 'ERROR',
          message: error.message,
          timestamp: new Date()
        });
        await this.logger.error(`测试失败: ${test.name}`, { error: error.message });
      }
    }

    return this.generateTestSummary();
  }

  /**
   * 测试环境配置
   */
  private async testEnvironmentConfig(): Promise<boolean> {
    const requiredVars = [
      'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
      'VITE_SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'
    ];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        throw new Error(`环境变量 ${varName} 未设置`);
      }
    }

    return true;
  }

  /**
   * 测试数据库连接
   */
  private async testDatabaseConnections(): Promise<boolean> {
    // 测试MySQL连接
    const mysqlVersion = await this.mysqlManager.query('SELECT VERSION() as version');
    if (!mysqlVersion || mysqlVersion.length === 0) {
      throw new Error('MySQL连接失败');
    }

    // 测试Supabase连接
    const { data, error } = await this.supabaseClient.from('users').select('count').limit(1);
    if (error && error.code !== 'PGRST116') { // PGRST116 是表不存在的错误，这里可以忽略
      throw new Error(`Supabase连接失败: ${error.message}`);
    }

    return true;
  }

  /**
   * 测试脚本文件完整性
   */
  private async testScriptIntegrity(): Promise<boolean> {
    const requiredFiles = [
      'scripts/mysql/create-mysql-tables.sql',
      'scripts/migrate-to-mysql.ts',
      'scripts/cleanup-migration.ts',
      'scripts/rollback-migration.ts',
      'scripts/data-integrity-validator.ts',
      'scripts/mysql/migration-logger.ts',
      'scripts/mysql/migration-config.ts',
      'scripts/mysql/run-migration.ts',
      'src/database/MySQLManager.ts'
    ];

    for (const filePath of requiredFiles) {
      const fullPath = path.join(process.cwd(), filePath);
      if (!fs.existsSync(fullPath)) {
        throw new Error(`必需文件不存在: ${filePath}`);
      }

      const stats = fs.statSync(fullPath);
      if (stats.size === 0) {
        throw new Error(`文件为空: ${filePath}`);
      }
    }

    return true;
  }

  /**
   * 测试MySQL表结构
   */
  private async testMySQLTableStructure(): Promise<boolean> {
    const sqlPath = path.join(process.cwd(), 'scripts/mysql/create-mysql-tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf-8');

    // 检查SQL文件是否包含必要的表定义
    const requiredTables = [
      'users', 'roles', 'permissions', 'customers', 'marketing_campaigns',
      'meetings', 'customer_behaviors', 'sales_funnel_stats', 'migration_logs'
    ];

    for (const table of requiredTables) {
      if (!sqlContent.includes(`CREATE TABLE ${table}`) && !sqlContent.includes(`CREATE TABLE \`${table}\``)) {
        throw new Error(`SQL文件中缺少表定义: ${table}`);
      }
    }

    // 检查字符集设置
    if (!sqlContent.includes('utf8mb4')) {
      throw new Error('SQL文件中缺少utf8mb4字符集设置');
    }

    return true;
  }

  /**
   * 测试配置文件验证
   */
  private async testConfigValidation(): Promise<boolean> {
    const configManager = new ConfigManager();
    const config = configManager.loadConfig();

    // 验证配置结构
    if (!config.mysql || !config.supabase || !config.migration) {
      throw new Error('配置文件结构不完整');
    }

    // 验证必需配置项
    if (!config.mysql.host || !config.mysql.database) {
      throw new Error('MySQL配置不完整');
    }

    if (!config.supabase.url || !config.supabase.serviceRoleKey) {
      throw new Error('Supabase配置不完整');
    }

    return true;
  }

  /**
   * 测试日志系统
   */
  private async testLoggingSystem(): Promise<boolean> {
    const testMessage = 'Migration test log message';
    const testData = { test: true, timestamp: Date.now() };

    // 测试不同级别的日志
    await this.logger.info(testMessage, testData);
    await this.logger.warn('Test warning message', testData);
    await this.logger.error('Test error message', testData);

    // 检查日志是否正确写入
    const stats = await this.logger.getStats();
    if (stats.totalLogs === 0) {
      throw new Error('日志系统未正常工作');
    }

    return true;
  }

  /**
   * 测试错误处理
   */
  private async testErrorHandling(): Promise<boolean> {
    try {
      // 测试无效SQL查询的错误处理
      await this.mysqlManager.query('SELECT * FROM non_existent_table');
      throw new Error('应该抛出错误但没有抛出');
    } catch (error) {
      if (!error.message.includes('non_existent_table') && !error.message.includes('doesn\'t exist')) {
        throw new Error('错误处理不正确');
      }
    }

    return true;
  }

  /**
   * 测试数据验证器
   */
  private async testDataValidator(): Promise<boolean> {
    // 创建测试表
    await this.mysqlManager.query(`
      CREATE TABLE IF NOT EXISTS test_validation (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 插入测试数据
    await this.mysqlManager.insert('test_validation', {
      name: 'Test User',
      email: '<EMAIL>'
    });

    // 验证数据
    const isValid = await this.validator.validateTableData('test_validation', {
      requiredFields: ['name'],
      uniqueFields: ['email']
    });

    // 清理测试表
    await this.mysqlManager.query('DROP TABLE IF EXISTS test_validation');

    return isValid;
  }

  /**
   * 生成测试摘要
   */
  private generateTestSummary(): TestSummary {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;

    return {
      total,
      passed,
      failed,
      errors,
      successRate: total > 0 ? (passed / total) * 100 : 0,
      results: this.testResults,
      timestamp: new Date()
    };
  }

  /**
   * 生成测试报告
   */
  async generateReport(summary: TestSummary): Promise<void> {
    const reportPath = path.join(process.cwd(), 'migration-test-report.json');
    
    const report = {
      summary,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        timestamp: new Date().toISOString()
      },
      configuration: {
        mysqlHost: process.env.DB_HOST,
        mysqlDatabase: process.env.DB_NAME,
        supabaseUrl: process.env.VITE_SUPABASE_URL
      }
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    await this.logger.info(`测试报告已生成: ${reportPath}`);
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    try {
      await this.mysqlManager.disconnect();
      await this.logger.info('测试环境清理完成');
    } catch (error) {
      console.error('清理资源时出错:', error);
    }
  }
}

// 接口定义
interface TestResult {
  name: string;
  status: 'PASSED' | 'FAILED' | 'ERROR';
  message: string;
  timestamp: Date;
}

interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  errors: number;
  successRate: number;
  results: TestResult[];
  timestamp: Date;
}

// 主函数
async function main() {
  const testManager = new MigrationTestManager();
  
  try {
    console.log('🚀 开始迁移脚本测试...');
    
    await testManager.initialize();
    const summary = await testManager.runAllTests();
    await testManager.generateReport(summary);
    
    console.log('\n📊 测试结果摘要:');
    console.log(`总测试数: ${summary.total}`);
    console.log(`通过: ${summary.passed}`);
    console.log(`失败: ${summary.failed}`);
    console.log(`错误: ${summary.errors}`);
    console.log(`成功率: ${summary.successRate.toFixed(2)}%`);
    
    if (summary.failed > 0 || summary.errors > 0) {
      console.log('\n❌ 失败的测试:');
      summary.results
        .filter(r => r.status !== 'PASSED')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.message}`);
        });
      process.exit(1);
    } else {
      console.log('\n✅ 所有测试通过！迁移脚本准备就绪。');
    }
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  } finally {
    await testManager.cleanup();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { MigrationTestManager };