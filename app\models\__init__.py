"""模型包初始化文件

导入所有模型类，确保SQLAlchemy能够正确识别和创建表结构
"""

# 导入基础模型
from .base import BaseModel, TimestampMixin, UUIDMixin

# 导入认证授权模型
from .auth import Role, Permission, OptionCategory, OptionItem, role_permissions

# 导入用户和部门模型
from .user import User, user_roles
from .department import Department

# 导入客户模型
from .customer import Customer, CustomerStatus, CustomerLevel, CustomerSource

# 导入跟进管理模型
from .follow import (
    CustomerFollowRecord, Meeting, PublicPool, CustomerBehavior,
    FollowType, FollowResult, MeetingStatus, PoolReason,
    meeting_participants
)

# 导入营销活动和统计模型
from .marketing import (
    MarketingCampaign, CampaignParticipant, CampaignShare,
    SalesFunnelStats, CustomerValueAnalysis, FollowUp,
    CampaignStatus, CampaignType, ParticipantStatus, ShareStatus
)

# 导出所有模型类
__all__ = [
    # 基础模型
    'BaseModel', 'TimestampMixin', 'UUIDMixin',
    
    # 用户和部门
    'User', 'Department',
    
    # 认证授权
    'Role', 'Permission', 'OptionCategory', 'OptionItem',
    'user_roles', 'role_permissions',
    
    # 客户管理
    'Customer', 'CustomerStatus', 'CustomerLevel', 'CustomerSource',
    
    # 跟进管理
    'CustomerFollowRecord', 'Meeting', 'PublicPool', 'CustomerBehavior',
    'FollowType', 'FollowResult', 'MeetingStatus', 'PoolReason',
    'meeting_participants',
    
    # 营销活动和统计
    'MarketingCampaign', 'CampaignParticipant', 'CampaignShare',
    'SalesFunnelStats', 'CustomerValueAnalysis', 'FollowUp',
    'CampaignStatus', 'CampaignType', 'ParticipantStatus', 'ShareStatus',
]

# 确保所有模型都被正确导入，以便SQLAlchemy能够创建表
# 这对于自动创建数据库表结构非常重要
print(f"已导入 {len(__all__)} 个模型类和枚举")