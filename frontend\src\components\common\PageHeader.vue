<template>
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <div v-if="titleRow" class="title-row">
          <h1 class="page-title">{{ title }}</h1>
          <p v-if="description" class="page-description">{{ description }}</p>
        </div>
        <template v-else>
          <h1 class="page-title">{{ title }}</h1>
          <p v-if="description" class="page-description">{{ description }}</p>
        </template>
      </div>
      <div v-if="$slots.actions" class="page-actions">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  description?: string
  titleRow?: boolean // 是否将标题和描述放在同一行
}

withDefaults(defineProps<Props>(), {
  description: '',
  titleRow: false
})
</script>

<style scoped>
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.title-row {
  display: flex;
  align-items: baseline;
  gap: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.title-row .page-title {
  margin: 0;
}

.page-description {
  font-size: 14px;
  color: #999999;
  margin: 0;
  line-height: 1.4;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}
</style>