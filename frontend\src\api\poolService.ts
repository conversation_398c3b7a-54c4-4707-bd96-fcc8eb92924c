// Supabase已禁用，改用本地MySQL数据库
// import { supabase } from './supabase'
// import type { Database } from './supabase'
import { mockPoolCustomers, mockDelay, isDevelopment } from '@/mock/customerData'
import type { Customer } from './customerService'

// 响应解包工具函数
const unwrapResponse = <T>(data: T, total?: number, page?: number, pageSize?: number): any => {
  if (total !== undefined) {
    return {
      data,
      total,
      page: page || 1,
      pageSize: pageSize || 20
    }
  }
  return data
}

type PoolRule = Database['public']['Tables']['pool_rules']['Row']
type PoolRuleInsert = Database['public']['Tables']['pool_rules']['Insert']
type PoolRuleUpdate = Database['public']['Tables']['pool_rules']['Update']

export interface PoolCustomer extends Customer {
  pool_entry_time?: string
  pool_reason?: string
  original_owner?: string
}

export interface PoolCustomersParams {
  page?: number
  pageSize?: number
  search?: string
  level?: string
  source?: string
  dateRange?: [number, number]
}

export interface PoolCustomersResponse {
  data: PoolCustomer[]
  total: number
  page: number
  pageSize: number
}

// 获取公海客户列表
export const getPoolCustomers = async (params: PoolCustomersParams = {}): Promise<PoolCustomersResponse> => {
  // 开发环境返回mock数据
  if (isDevelopment) {
    await mockDelay()
    
    const {
      page = 1,
      pageSize = 20,
      search,
      level,
      source
    } = params
    
    let filteredData = [...mockPoolCustomers]
    
    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase()
      filteredData = filteredData.filter(customer => 
        customer.name.toLowerCase().includes(searchLower) ||
        customer.phone.includes(search) ||
        customer.company?.toLowerCase().includes(searchLower)
      )
    }
    
    // 等级过滤
    if (level) {
      filteredData = filteredData.filter(customer => customer.level === level)
    }
    
    // 来源过滤
    if (source) {
      filteredData = filteredData.filter(customer => customer.source === source)
    }
    
    // 分页
    const total = filteredData.length
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)
    
    return unwrapResponse(paginatedData, total, page, pageSize)
  }

  const {
    page = 1,
    pageSize = 20,
    search,
    level,
    source,
    dateRange
  } = params

  let query = supabase
    .from('customers')
    .select('*', { count: 'exact' })
    .is('assigned_to', null) // 公海客户没有分配给任何人
    .order('updated_at', { ascending: false })

  // 搜索条件
  if (search) {
    query = query.or(`name.ilike.%${search}%,company.ilike.%${search}%,phone.ilike.%${search}%`)
  }

  // 客户等级筛选
  if (level) {
    query = query.eq('level', level)
  }

  // 客户来源筛选
  if (source) {
    query = query.eq('source', source)
  }

  // 日期范围筛选
  if (dateRange && dateRange.length === 2) {
    const startDate = new Date(dateRange[0]).toISOString()
    const endDate = new Date(dateRange[1]).toISOString()
    query = query.gte('updated_at', startDate).lte('updated_at', endDate)
  }

  // 分页
  const from = (page - 1) * pageSize
  const to = from + pageSize - 1
  query = query.range(from, to)

  const { data, error, count } = await query

  if (error) {
    throw new Error(`获取公海客户失败: ${error.message}`)
  }

  return unwrapResponse(data || [], count || 0, page, pageSize)
}

// 认领客户
export const claimCustomers = async (customerIds: number[], userId: string): Promise<void> => {
  const { error } = await supabase
    .from('customers')
    .update({ 
      assigned_to: userId,
      updated_at: new Date().toISOString()
    })
    .in('id', customerIds)

  if (error) {
    throw new Error(`认领客户失败: ${error.message}`)
  }
}

// 将客户移入公海
export const moveToPool = async (customerIds: number[], reason: string): Promise<void> => {
  const { error } = await supabase
    .from('customers')
    .update({ 
      assigned_to: null,
      pool_reason: reason,
      updated_at: new Date().toISOString()
    })
    .in('id', customerIds)

  if (error) {
    throw new Error(`移入公海失败: ${error.message}`)
  }
}

// 获取公海规则列表
export const getPoolRules = async (): Promise<PoolRule[]> => {
  const { data, error } = await supabase
    .from('pool_rules')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`获取公海规则失败: ${error.message}`)
  }

  return data || []
}

// 创建公海规则
export const createPoolRule = async (rule: PoolRuleInsert): Promise<PoolRule> => {
  const { data, error } = await supabase
    .from('pool_rules')
    .insert(rule)
    .select()
    .single()

  if (error) {
    throw new Error(`创建公海规则失败: ${error.message}`)
  }

  return data
}

// 更新公海规则
export const updatePoolRule = async (id: number, rule: PoolRuleUpdate): Promise<PoolRule> => {
  const { data, error } = await supabase
    .from('pool_rules')
    .update(rule)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(`更新公海规则失败: ${error.message}`)
  }

  return data
}

// 删除公海规则
export const deletePoolRule = async (id: number): Promise<void> => {
  const { error } = await supabase
    .from('pool_rules')
    .delete()
    .eq('id', id)

  if (error) {
    throw new Error(`删除公海规则失败: ${error.message}`)
  }
}

// 执行公海规则检查
export const executePoolRules = async (): Promise<{ moved: number; rules: number }> => {
  // 获取所有启用的规则
  const { data: rules, error: rulesError } = await supabase
    .from('pool_rules')
    .select('*')
    .eq('is_active', true)

  if (rulesError) {
    throw new Error(`获取公海规则失败: ${rulesError.message}`)
  }

  let totalMoved = 0

  // 执行每个规则
  for (const rule of rules || []) {
    if (rule.condition_type === 'no_contact' && rule.days) {
      // 查找超过指定天数未联系的客户
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - rule.days)

      let query = supabase
        .from('customers')
        .select('id')
        .not('assigned_to', 'is', null) // 有分配的客户
        .lt('last_contact_at', cutoffDate.toISOString())

      // 应用客户等级过滤
      if (rule.customer_levels && rule.customer_levels.length > 0) {
        query = query.in('level', rule.customer_levels)
      }

      const { data: customers, error: customersError } = await query

      if (customersError) {
        console.error('查询客户失败:', customersError)
        continue
      }

      if (customers && customers.length > 0) {
        const customerIds = customers.map(c => c.id)
        
        // 移入公海
        const { error: moveError } = await supabase
          .from('customers')
          .update({ 
            assigned_to: null,
            pool_reason: `根据规则"${rule.name}"自动移入公海`,
            updated_at: new Date().toISOString()
          })
          .in('id', customerIds)

        if (moveError) {
          console.error('移入公海失败:', moveError)
        } else {
          totalMoved += customers.length
        }
      }
    }
  }

  return {
    moved: totalMoved,
    rules: rules?.length || 0
  }
}

// 自动分配公海客户
export const autoAssignPoolCustomers = async (): Promise<{ assigned: number }> => {
  // 获取启用自动分配的规则
  const { data: rules, error: rulesError } = await supabase
    .from('pool_rules')
    .select('*')
    .eq('is_active', true)
    .eq('auto_assign', true)

  if (rulesError) {
    throw new Error(`获取自动分配规则失败: ${rulesError.message}`)
  }

  if (!rules || rules.length === 0) {
    return { assigned: 0 }
  }

  // 获取公海客户
  const { data: poolCustomers, error: customersError } = await supabase
    .from('customers')
    .select('id')
    .is('assigned_to', null)
    .limit(50) // 限制每次分配的数量

  if (customersError || !poolCustomers || poolCustomers.length === 0) {
    return { assigned: 0 }
  }

  // 获取在线销售人员（这里简化处理，实际应该根据业务逻辑获取）
  const { data: salesUsers, error: usersError } = await supabase
    .from('user_roles')
    .select('user_id')
    .eq('role_id', 2) // 假设角色ID 2 是销售角色

  if (usersError || !salesUsers || salesUsers.length === 0) {
    return { assigned: 0 }
  }

  let assigned = 0
  const salesUserIds = salesUsers.map(u => u.user_id)

  // 轮询分配
  for (let i = 0; i < poolCustomers.length; i++) {
    const customer = poolCustomers[i]
    const assignedUserId = salesUserIds[i % salesUserIds.length]

    const { error: assignError } = await supabase
      .from('customers')
      .update({ 
        assigned_to: assignedUserId,
        updated_at: new Date().toISOString()
      })
      .eq('id', customer.id)

    if (!assignError) {
      assigned++
    }
  }

  return { assigned }
}