// Supabase已禁用，改用本地MySQL数据库
// import { supabase } from './supabase'
// import type { Database } from './supabase'
import type { ApiResponse } from '@/types'

// 响应解包工具函数
const unwrapResponse = <T>(data: T, total?: number, page?: number, pageSize?: number) => {
  if (total !== undefined) {
    return {
      data,
      total,
      page: page || 1,
      pageSize: pageSize || 20
    }
  }
  return data
}

type Role = Database['public']['Tables']['roles']['Row']
type RoleInsert = Database['public']['Tables']['roles']['Insert']
type RoleUpdate = Database['public']['Tables']['roles']['Update']

type Permission = Database['public']['Tables']['permissions']['Row']
// type RolePermission = Database['public']['Tables']['role_permissions']['Row']
type UserRole = Database['public']['Tables']['user_roles']['Row']

export interface RoleWithPermissions extends Role {
  role_permissions?: {
    permissions: Permission
  }[]
  permissions: Permission[]
}

export interface CreateRoleData {
  name: string
  display_name: string
  description?: string
  status?: boolean
  permission_ids: number[]
}

export interface UpdateRoleData {
  name?: string
  display_name?: string
  description?: string
  status?: boolean
  permission_ids?: number[]
}

export interface RoleQueryParams {
  page?: number
  page_size?: number
  search?: string
  status?: boolean
}

export interface AssignRoleData {
  user_id: string
  role_ids: number[]
  expires_at?: string
}

class RoleService {
  // 获取角色列表
  async getRoles(params?: RoleQueryParams) {
    try {
      let query = supabase
        .from('roles')
        .select(`
          *,
          role_permissions(
            permissions(*)
          )
        `, { count: 'exact' })
        .order('created_at', { ascending: false })

      // 添加筛选条件
      if (params?.search) {
        query = query.or(`name.ilike.%${params.search}%,display_name.ilike.%${params.search}%,description.ilike.%${params.search}%`)
      }

      if (params?.status !== undefined) {
        query = query.eq('status', params.status)
      }

      // 分页
      if (params?.page && params?.page_size) {
        const from = (params.page - 1) * params.page_size
        const to = from + params.page_size - 1
        query = query.range(from, to)
      }

      const { data, error, count } = await query

      if (error) {
        console.error('获取角色列表失败:', error)
        throw new Error(`获取角色列表失败: ${error.message}`)
      }

      // 转换数据格式
      const roles: RoleWithPermissions[] = data?.map(role => ({
        ...role,
        permissions: role.role_permissions?.map((rp: any) => rp.permissions) || []
      })) || []

      // 如果有分页参数，返回分页结构
      if (params?.page && params?.page_size) {
        return unwrapResponse(roles, count || 0, params.page, params.page_size)
      }
      
      // 否则直接返回数据
      return unwrapResponse(roles)
    } catch (error) {
      console.error('获取角色列表失败:', error)
      throw error
    }
  }

  // 获取角色详情
  async getRoleDetail(id: number): Promise<ApiResponse<RoleWithPermissions>> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select(`
          *,
          role_permissions(
            permissions(*)
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('获取角色详情失败:', error)
        return { success: false, message: error.message, data: undefined as any }
      }

      // 转换数据格式
      const role: RoleWithPermissions = {
        ...data,
        permissions: data.role_permissions?.map((rp: any) => rp.permissions) || []
      }

      return { success: true, message: '获取成功', data: role }
    } catch (error) {
      console.error('获取角色详情失败:', error)
      return { success: false, message: '获取角色详情失败', data: undefined as any }
    }
  }

  // 创建角色
  async createRole(roleData: CreateRoleData): Promise<ApiResponse<Role>> {
    try {
      const { permission_ids, ...roleInfo } = roleData

      // 创建角色
      const { data: role, error: roleError } = await supabase
        .from('roles')
        .insert(roleInfo)
        .select()
        .single()

      if (roleError) {
        console.error('创建角色失败:', roleError)
        return { success: false, message: roleError.message, data: undefined as any }
      }

      // 分配权限
      if (permission_ids && permission_ids.length > 0) {
        const rolePermissions = permission_ids.map(permissionId => ({
          role_id: role.id,
          permission_id: permissionId
        }))

        const { error: permissionError } = await supabase
          .from('role_permissions')
          .insert(rolePermissions)

        if (permissionError) {
          console.error('分配权限失败:', permissionError)
          // 如果权限分配失败，删除已创建的角色
          await supabase.from('roles').delete().eq('id', role.id)
          return { success: false, message: '分配权限失败', data: undefined as any }
        }
      }

      return { success: true, message: '创建成功', data: role }
    } catch (error) {
      console.error('创建角色失败:', error)
      return { success: false, message: '创建角色失败', data: undefined as any }
    }
  }

  // 更新角色
  async updateRole(id: number, roleData: UpdateRoleData) {
    try {
      const { permission_ids, ...roleInfo } = roleData

      // 更新角色基本信息
      const { data: role, error: roleError } = await supabase
        .from('roles')
        .update(roleInfo)
        .eq('id', id)
        .select()
        .single()

      if (roleError) {
        throw roleError
      }

      // 更新权限分配
      if (permission_ids !== undefined) {
        // 删除现有权限
        await supabase
          .from('role_permissions')
          .delete()
          .eq('role_id', id)

        // 添加新权限
        if (permission_ids.length > 0) {
          const rolePermissions = permission_ids.map(permission_id => ({
            role_id: id,
            permission_id
          }))

          const { error: permissionError } = await supabase
            .from('role_permissions')
            .insert(rolePermissions)

          if (permissionError) {
            throw permissionError
          }
        }
      }

      return {
        success: true,
        message: '更新成功',
        data: role
      }
    } catch (error) {
      console.error('更新角色失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新角色失败',
        data: undefined as any
      }
    }
  }

  // 删除角色
  async deleteRole(id: number) {
    try {
      // 检查是否为系统角色
      const { data: role } = await supabase
        .from('roles')
        .select('is_system')
        .eq('id', id)
        .single()

      if (role?.is_system) {
        throw new Error('系统角色不能删除')
      }

      // 删除角色权限关联
      await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', id)

      // 删除用户角色关联
      await supabase
        .from('user_roles')
        .delete()
        .eq('role_id', id)

      // 删除角色
      const { error } = await supabase
        .from('roles')
        .delete()
        .eq('id', id)

      if (error) {
        throw error
      }

      return {
        success: true,
        message: '删除成功',
        data: undefined as any
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '删除角色失败',
        data: undefined as any
      }
    }
  }

  // 获取所有权限
  async getPermissions(): Promise<ApiResponse<Permission[]>> {
    try {
      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .order('module')
        .order('name')

      if (error) {
        console.error('获取权限列表失败:', error)
        return { success: false, message: error.message, data: [] }
      }

      return { success: true, message: '获取成功', data: data || [] }
    } catch (error) {
      console.error('获取权限列表失败:', error)
      return { success: false, message: '获取权限列表失败', data: [] }
    }
  }

  // 分配角色给用户
  async assignRoles(assignData: AssignRoleData) {
    try {
      const { user_id, role_ids, expires_at } = assignData

      // 删除用户现有角色
      await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', user_id)

      // 分配新角色
      if (role_ids.length > 0) {
        const userRoles = role_ids.map(role_id => ({
          user_id,
          role_id,
          expires_at
        }))

        const { error } = await supabase
          .from('user_roles')
          .insert(userRoles)

        if (error) {
          throw error
        }
      }

      return {
        success: true,
        message: '分配成功',
        data: undefined as any
      }
    } catch (error) {
      console.error('分配角色失败:', error)
      return {
          success: false,
          message: error instanceof Error ? error.message : '分配角色失败',
          data: undefined as any
        }
    }
  }

  // 获取用户角色
  async getUserRoles(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          *,
          roles(*)
        `)
        .eq('user_id', userId)

      if (error) {
        throw error
      }

      return {
        success: true,
        message: '获取成功',
        data: data
      }
    } catch (error) {
      console.error('获取用户角色失败:', error)
      return {
          success: false,
          message: error instanceof Error ? error.message : '获取用户角色失败',
          data: undefined as any
        }
    }
  }
}

export const roleService = new RoleService()
export default roleService

export type {
  Role,
  RoleInsert,
  RoleUpdate,
  Permission,
  UserRole
}