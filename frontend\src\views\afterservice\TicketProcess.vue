<template>
  <div class="ticket-process">
    <n-card title="工单处理" :bordered="false">
      <!-- 工单基本信息 -->
      <div class="ticket-info mb-6">
        <n-descriptions title="工单信息" :column="3" bordered>
          <n-descriptions-item label="工单号">{{ ticketInfo.ticketNo }}</n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getStatusType(ticketInfo.status)">{{ getStatusText(ticketInfo.status) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="优先级">
            <n-tag :type="getPriorityType(ticketInfo.priority)">{{ getPriorityText(ticketInfo.priority) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="客户姓名">{{ customerInfo.name }}</n-descriptions-item>
          <n-descriptions-item label="联系电话">{{ customerInfo.phone }}</n-descriptions-item>
          <n-descriptions-item label="客户ID">{{ customerInfo.id }}</n-descriptions-item>
          <n-descriptions-item label="楼盘信息" :span="3">
            <div class="property-info">
              <n-icon :component="Home" class="mr-2" />
              {{ customerInfo.propertyInfo }}
              <n-button text type="primary" @click="viewPropertyDetails" class="ml-2">
                查看详情
              </n-button>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="问题描述" :span="3">{{ ticketInfo.description }}</n-descriptions-item>
          <n-descriptions-item label="创建时间">{{ formatDate(ticketInfo.createdAt) }}</n-descriptions-item>
          <n-descriptions-item label="预计完成时间">
            <span :class="{ 'text-red-500': isOverdue }">
              {{ formatDate(ticketInfo.expectedTime) }}
              <n-tag v-if="isOverdue" type="error" size="small" class="ml-2">已超期</n-tag>
            </span>
          </n-descriptions-item>
          <n-descriptions-item label="剩余时间">
            <countdown-timer :target-time="ticketInfo.expectedTime" />
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 处理表单 -->
      <n-card title="处理工单" class="mb-6">
        <n-form
          ref="processFormRef"
          :model="processForm"
          :rules="processRules"
          label-placement="left"
          label-width="120px"
        >
          <n-form-item label="处理状态" path="status">
            <n-select
              v-model:value="processForm.status"
              :options="statusOptions"
              @update:value="onStatusChange"
            />
          </n-form-item>
          <n-form-item label="分配给" path="assignedTo">
            <n-select
              v-model:value="processForm.assignedTo"
              placeholder="选择处理人员"
              :options="staffOptions"
              filterable
            />
          </n-form-item>
          <n-form-item label="处理说明" path="processNote">
            <n-input
              v-model:value="processForm.processNote"
              type="textarea"
              placeholder="请输入处理说明或备注"
              :rows="4"
            />
          </n-form-item>
          <n-form-item label="预计完成时间" path="expectedTime" v-if="processForm.status === 'processing'">
            <n-date-picker
              v-model:value="processForm.expectedTime"
              type="datetime"
              placeholder="更新预计完成时间"
              style="width: 100%"
            />
          </n-form-item>
          <n-form-item label="实际完成时间" path="completedTime" v-if="processForm.status === 'completed'">
            <n-date-picker
              v-model:value="processForm.completedTime"
              type="datetime"
              placeholder="选择完成时间"
              style="width: 100%"
            />
          </n-form-item>
          <n-form-item label="上传附件">
            <n-upload
              multiple
              directory-dnd
              :max="5"
              :file-list="fileList"
              @update:file-list="handleFileListChange"
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <n-icon size="48" :depth="3">
                    <CloudUpload />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px">
                  点击或者拖动文件到该区域来上传
                </n-text>
                <n-p depth="3" style="margin: 8px 0 0 0">
                  支持图片、文档等格式，最多上传5个文件
                </n-p>
              </n-upload-dragger>
            </n-upload>
          </n-form-item>
          <n-form-item>
            <n-space>
              <n-button type="primary" @click="submitProcess" :loading="submitting">
                提交处理
              </n-button>
              <n-button @click="saveAsDraft">保存草稿</n-button>
              <n-button @click="resetForm">重置</n-button>
            </n-space>
          </n-form-item>
        </n-form>
      </n-card>

      <!-- 处理历史 -->
      <n-card title="处理历史">
        <n-timeline>
          <n-timeline-item
            v-for="(record, index) in processHistory"
            :key="index"
            :type="getTimelineType(record.action)"
            :title="record.action"
            :content="record.description"
            :time="formatDate(record.createdAt)"
          >
            <template #icon>
              <n-icon :component="getTimelineIcon(record.action)" />
            </template>
            <div class="timeline-content">
              <div class="action-user">操作人：{{ record.operator }}</div>
              <div class="action-note" v-if="record.note">备注：{{ record.note }}</div>
              <div class="attachments" v-if="record.attachments?.length">
                <n-space>
                  <n-tag
                    v-for="file in record.attachments"
                    :key="file.name"
                    type="info"
                    size="small"
                  >
                    {{ file.name }}
                  </n-tag>
                </n-space>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </n-card>
    </n-card>

    <!-- 楼盘详情模态框 -->
    <n-modal v-model:show="showPropertyModal" preset="dialog" title="楼盘详情" style="width: 600px">
      <div v-if="propertyDetails">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="楼盘名称">{{ propertyDetails.name }}</n-descriptions-item>
          <n-descriptions-item label="房号">{{ propertyDetails.roomNumber }}</n-descriptions-item>
          <n-descriptions-item label="面积">{{ propertyDetails.area }}㎡</n-descriptions-item>
          <n-descriptions-item label="户型">{{ propertyDetails.layout }}</n-descriptions-item>
          <n-descriptions-item label="装修状态">{{ propertyDetails.decorationStatus }}</n-descriptions-item>
          <n-descriptions-item label="入住时间">{{ propertyDetails.moveInDate }}</n-descriptions-item>
          <n-descriptions-item label="物业费">{{ propertyDetails.propertyFee }}元/月</n-descriptions-item>
          <n-descriptions-item label="停车位">{{ propertyDetails.parkingSpace }}</n-descriptions-item>
          <n-descriptions-item label="备注" :span="2">{{ propertyDetails.remarks }}</n-descriptions-item>
        </n-descriptions>
      </div>
      <template #action>
        <n-button @click="showPropertyModal = false">关闭</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useMessage, type UploadFileInfo } from 'naive-ui'
import { useRoute } from 'vue-router'
import {
  Home,
  CloudUpload,
  Create,
  CheckmarkCircle,
  CloseCircle,
  Time,
  Person
} from '@vicons/ionicons5'
import CountdownTimer from '@/components/CountdownTimer.vue'

interface TicketInfo {
  id: string
  ticketNo: string
  customerId: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: string
  expectedTime: string
  completedAt?: string
}

interface CustomerInfo {
  id: string
  name: string
  phone: string
  propertyInfo: string
}

interface PropertyDetails {
  name: string
  roomNumber: string
  area: number
  layout: string
  decorationStatus: string
  moveInDate: string
  propertyFee: number
  parkingSpace: string
  remarks: string
}

interface ProcessRecord {
  action: string
  description: string
  operator: string
  createdAt: string
  note?: string
  attachments?: Array<{ name: string; url: string }>
}

const message = useMessage()
const route = useRoute()
const submitting = ref(false)
const showPropertyModal = ref(false)
const processFormRef = ref()
const fileList = ref<UploadFileInfo[]>([])

// 工单信息
const ticketInfo = ref<TicketInfo>({
  id: '1',
  ticketNo: 'T202401001',
  customerId: 'C001',
  description: '卫生间水龙头漏水，需要更换密封圈',
  status: 'pending',
  priority: 'medium',
  createdAt: '2024-01-15 09:30:00',
  expectedTime: '2024-01-16 18:00:00'
})

// 客户信息（根据客户ID自动获取）
const customerInfo = ref<CustomerInfo>({
  id: 'C001',
  name: '张三',
  phone: '13800138001',
  propertyInfo: '阳光花园 A栋 1201室'
})

// 楼盘详情
const propertyDetails = ref<PropertyDetails>({
  name: '阳光花园',
  roomNumber: 'A栋1201室',
  area: 120,
  layout: '三室两厅',
  decorationStatus: '精装修',
  moveInDate: '2023-06-15',
  propertyFee: 280,
  parkingSpace: 'A区-15号',
  remarks: '南北通透，采光良好'
})

// 处理表单
const processForm = reactive({
  status: '',
  assignedTo: '',
  processNote: '',
  expectedTime: null as number | null,
  completedTime: null as number | null
})

// 表单验证规则
const processRules = {
  status: { required: true, message: '请选择处理状态', trigger: 'change' },
  processNote: { required: true, message: '请输入处理说明', trigger: 'blur' }
}

// 处理历史
const processHistory = ref<ProcessRecord[]>([
  {
    action: '工单创建',
    description: '客户提交维修需求',
    operator: '系统',
    createdAt: '2024-01-15 09:30:00',
    note: '客户通过微信小程序提交'
  },
  {
    action: '工单分配',
    description: '工单已分配给维修部门',
    operator: '客服小王',
    createdAt: '2024-01-15 10:15:00',
    note: '已通知维修师傅'
  }
])

// 选项数据
const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

const staffOptions = [
  { label: '维修师傅-王五', value: 'staff_001' },
  { label: '电工师傅-赵六', value: 'staff_002' },
  { label: '水电工-李七', value: 'staff_003' },
  { label: '综合维修-张八', value: 'staff_004' }
]

// 计算属性
const isOverdue = computed(() => {
  const now = new Date().getTime()
  const expectedTime = new Date(ticketInfo.value.expectedTime).getTime()
  return now > expectedTime && ticketInfo.value.status !== 'completed'
})

// 工具函数
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    cancelled: 'error'
  }
  return types[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  }
  return types[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getTimelineType = (action: string) => {
  const types: Record<string, string> = {
    '工单创建': 'info',
    '工单分配': 'warning',
    '开始处理': 'info',
    '处理完成': 'success',
    '工单取消': 'error'
  }
  return types[action] || 'default'
}

const getTimelineIcon = (action: string) => {
  const icons: Record<string, any> = {
    '工单创建': Create,
    '工单分配': Person,
    '开始处理': Time,
    '处理完成': CheckmarkCircle,
    '工单取消': CloseCircle
  }
  return icons[action] || Create
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 事件处理函数
const onStatusChange = (status: string) => {
  if (status === 'completed') {
    processForm.completedTime = Date.now()
  }
}

const handleFileListChange = (files: UploadFileInfo[]) => {
  fileList.value = files
}

const viewPropertyDetails = () => {
  showPropertyModal.value = true
}

const submitProcess = async () => {
  try {
    await processFormRef.value?.validate()
    submitting.value = true
    
    // 模拟提交处理
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新工单状态
    ticketInfo.value.status = processForm.status as any
    
    // 添加处理记录
    const newRecord: ProcessRecord = {
      action: getActionText(processForm.status),
      description: processForm.processNote,
      operator: '当前用户',
      createdAt: new Date().toLocaleString('zh-CN'),
      note: processForm.processNote,
      attachments: fileList.value.map((file: any) => ({
        name: file.name,
        url: file.url || '#'
      }))
    }
    
    processHistory.value.push(newRecord)
    
    message.success('工单处理成功')
    resetForm()
  } catch (error) {
    message.error('请完善处理信息')
  } finally {
    submitting.value = false
  }
}

const saveAsDraft = () => {
  message.success('草稿保存成功')
}

const resetForm = () => {
  Object.assign(processForm, {
    status: '',
    assignedTo: '',
    processNote: '',
    expectedTime: null,
    completedTime: null
  })
  fileList.value = []
}

const getActionText = (status: string) => {
  const texts: Record<string, string> = {
    processing: '开始处理',
    completed: '处理完成',
    cancelled: '工单取消'
  }
  return texts[status] || '状态更新'
}

// 根据路由参数或客户ID获取客户信息
const loadCustomerInfo = async (customerId: string) => {
  // 模拟API调用
  const customers = {
    'C001': {
      id: 'C001',
      name: '张三',
      phone: '13800138001',
      propertyInfo: '阳光花园 A栋 1201室'
    },
    'C002': {
      id: 'C002',
      name: '李四',
      phone: '13800138002',
      propertyInfo: '绿城花园 B栋 2305室'
    }
  }
  
  const customer = customers[customerId as keyof typeof customers]
  if (customer) {
    customerInfo.value = customer
  }
}

onMounted(() => {
  // 根据工单ID或客户ID加载数据
  const ticketId = route.params.id as string
  const customerId = route.query.customerId as string
  
  if (customerId) {
    loadCustomerInfo(customerId)
  }
  
  // 初始化表单状态
  processForm.status = ticketInfo.value.status
})
</script>

<style scoped>
.ticket-process {
  padding: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.property-info {
  display: flex;
  align-items: center;
}

.text-red-500 {
  color: #ef4444;
}

.timeline-content {
  margin-top: 8px;
}

.action-user {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.action-note {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.attachments {
  margin-top: 8px;
}
</style>