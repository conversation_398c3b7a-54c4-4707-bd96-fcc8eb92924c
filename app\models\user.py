"""用户模型

定义用户相关的数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Table, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .auth import Role

# 用户角色关联表
user_roles = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', String(36), ForeignKey('users.id'), primary_key=True),
    Column('role_id', String(36), ForeignKey('roles.id'), primary_key=True)
)


class User(BaseModel):
    """用户模型
    
    对应数据库中的users表
    """
    __tablename__ = "users"
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, comment="邮箱")
    phone = Column(String(20), nullable=True, comment="手机号")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    
    # 个人信息
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar = Column(Text, nullable=True, comment="头像URL")
    gender = Column(String(10), nullable=True, comment="性别")
    birthday = Column(DateTime, nullable=True, comment="生日")
    
    # 工作信息
    employee_id = Column(String(50), nullable=True, comment="员工编号")
    position = Column(String(100), nullable=True, comment="职位")
    department_id = Column(String(36), ForeignKey('departments.id'), nullable=True, comment="部门ID")
    hire_date = Column(DateTime, nullable=True, comment="入职日期")
    
    # 联系信息
    work_phone = Column(String(20), nullable=True, comment="工作电话")
    address = Column(Text, nullable=True, comment="地址")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_superuser = Column(Boolean, default=False, nullable=False, comment="是否超级管理员")
    is_verified = Column(Boolean, default=False, nullable=False, comment="是否已验证")
    
    # 登录信息
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    login_count = Column(String(10), default='0', nullable=False, comment="登录次数")
    
    # 备注
    notes = Column(Text, nullable=True, comment="备注")
    
    # 关联关系
    department = relationship("Department", back_populates="users", lazy="select")
    # TODO: 暂时注释掉roles关系，避免循环导入问题
    # roles = relationship("Role", secondary="user_roles", back_populates="users", lazy="select")
    
    # TODO: 暂时注释掉客户关联和跟进记录，避免关系问题
    # assigned_customers = relationship("Customer", foreign_keys="Customer.assigned_user_id", back_populates="assigned_user", lazy="select")
    # created_customers = relationship("Customer", foreign_keys="Customer.created_by", lazy="select")
    # follow_records = relationship("CustomerFollowRecord", back_populates="created_by_user", lazy="select")
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username})>"
    
    @property
    def display_name(self):
        """显示名称"""
        return self.real_name or self.nickname or self.username
    
    def has_permission(self, permission_code: str) -> bool:
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        
        for role in self.roles:
            for permission in role.permissions:
                if permission.code == permission_code:
                    return True
        return False
    
    def has_role(self, role_name: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.name == role_name for role in self.roles)