<template>
  <div class="customer-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="handleBack" class="back-button">
            <template #icon>
              <n-icon size="20"><arrow-back-outline /></n-icon>
            </template>
            返回客户列表
          </n-button>
          <div class="title-section">
            <h1 class="page-title">{{ customer?.name || '客户详情' }}</h1>
            <div class="customer-meta">
              <n-tag :type="getStageTagType(currentStage)" size="medium">
                {{ getStageText(currentStage) }}
              </n-tag>
              <n-tag v-if="customer?.level === 'A'" type="warning" size="medium">
                <template #icon>
                  <n-icon><star-outline /></n-icon>
                </template>
                重要客户
              </n-tag>
              <span class="customer-id">ID: {{ customer?.id }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <n-space size="medium">
            <n-button type="primary" size="medium" @click="handleEditBasicInfo">
              <template #icon>
                <n-icon><create-outline /></n-icon>
              </template>
              编辑基本信息
            </n-button>
            <n-button type="info" size="medium" @click="handleContact">
              <template #icon>
                <n-icon><call-outline /></n-icon>
              </template>
              联系客户
            </n-button>
            <n-dropdown :options="moreActions" @select="handleMoreAction">
              <n-button size="medium">
                <template #icon>
                  <n-icon><ellipsis-horizontal-outline /></n-icon>
                </template>
                更多操作
              </n-button>
            </n-dropdown>
          </n-space>
        </div>
      </div>
    </div>

    <!-- 客户概览 -->
    <div class="overview-section">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 基本信息卡片 -->
        <n-card class="info-card" :span="8" :bordered="false">
          <template #header>
            <div class="card-header">
              <n-icon size="18" color="#1677ff">
                <person-outline />
              </n-icon>
              <span>基本信息</span>
            </div>
          </template>
          
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">客户姓名</span>
              <span class="info-value">{{ customer?.name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">手机号码</span>
              <span class="info-value">
                {{ customer?.phone || '-' }}
                <n-button v-if="customer?.phone" text size="tiny" @click="handleCall">
                  <template #icon>
                    <n-icon><call-outline /></n-icon>
                  </template>
                </n-button>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">性别</span>
              <span class="info-value">{{ customer?.gender || '-' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">性别</span>
                <span class="info-value">{{ customer?.gender || '-' }}</span>
              </div>
            <div class="info-item">
              <span class="info-label">客户来源</span>
              <span class="info-value">{{ customer?.source || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">客户分类</span>
              <span class="info-value">{{ customer?.level || '-' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">地址</span>
                <span class="info-value">{{ customer?.address || '-' }}</span>
              </div>
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDate(customer?.created_at) }}</span>
            </div>
          </div>
        </n-card>

        <!-- 统计信息卡片 -->
        <n-card class="stats-card" :span="8" :bordered="false">
          <template #header>
            <div class="card-header">
              <n-icon size="18" color="#52c41a">
                <bar-chart-outline />
              </n-icon>
              <span>客户统计</span>
            </div>
          </template>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ customerStats.followUpCount }}</div>
              <div class="stat-label">跟进次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ customerStats.meetingCount }}</div>
              <div class="stat-label">见面次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ customerStats.daysSinceCreated }}</div>
              <div class="stat-label">跟进天数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ customerStats.lastContactDays }}</div>
              <div class="stat-label">最后联系</div>
            </div>
          </div>
        </n-card>

        <!-- 进度和提醒卡片 -->
        <n-card class="progress-card" :span="8" :bordered="false">
          <template #header>
            <div class="card-header">
              <n-icon size="18" color="#722ed1">
                <trending-up-outline />
              </n-icon>
              <span>客户进度</span>
            </div>
          </template>
          
          <div class="progress-content">
            <!-- 客户阶段进度 -->
            <div class="stage-progress">
              <div class="progress-header">
                <span class="progress-title">当前阶段</span>
                <span class="progress-percentage">{{ stageProgress }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ 
                    width: `${stageProgress}%`,
                    '--fill-color': getStageColor(currentStage),
                    '--fill-color-light': getStageColorLight(currentStage)
                  }"
                ></div>
              </div>
              <div class="stage-labels">
                <span class="stage-label active">跟进</span>
                <span class="stage-label" :class="{ active: stageProgress >= 33 }">见面</span>
                <span class="stage-label" :class="{ active: stageProgress >= 66 }">签约</span>
                <span class="stage-label" :class="{ active: stageProgress >= 100 }">完成</span>
              </div>
            </div>

            <!-- 信息完整度 -->
            <div class="completeness-section">
              <div class="completeness-header">
                <span class="completeness-title">信息完整度</span>
                <span class="completeness-percentage">{{ infoCompleteness }}%</span>
              </div>
              <n-progress 
                :percentage="infoCompleteness" 
                :color="getCompletenessColor(infoCompleteness)"
                :show-indicator="false"
                :height="8"
              />
              <div class="completeness-tips">
                <n-alert v-if="infoCompleteness < 80" type="warning" size="small">
                  建议完善客户信息以提高跟进效果
                </n-alert>
              </div>
            </div>

            <!-- 下一步操作建议 -->
            <div class="next-actions">
              <div class="actions-title">下一步建议</div>
              <div class="action-suggestions">
                <n-tag 
                  v-for="(suggestion, index) in nextActionSuggestions" 
                  :key="index"
                  type="info"
                  size="small"
                  class="suggestion-tag"
                >
                  {{ suggestion }}
                </n-tag>
              </div>
            </div>
          </div>
        </n-card>
      </n-grid>
    </div>

    <!-- 客户跟进流程 -->
    <div class="stage-flow-section">
      <customer-stage-flow 
        :customer="customer" 
        @stage-updated="handleStageUpdated"
        @follow-up-added="handleFollowUpAdded"
      />
    </div>

    <!-- 编辑基本信息弹窗 -->
    <basic-info-form
          v-model:show="showBasicInfoModal"
          :model-value="customer || undefined"
          @submit="handleBasicInfoSuccess"
        />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  ArrowBackOutline,
  CreateOutline,
  CallOutline,
  EllipsisHorizontalOutline,
  PersonOutline,
  BarChartOutline,
  TrendingUpOutline,
  StarOutline
} from '@vicons/ionicons5'
import { useCustomerStore } from '@/stores/modules/customer'
import { CustomerStage, customerStageUtils } from '@/utils/customerStage'
import CustomerStageFlow from '@/components/customer/CustomerStageFlow.vue'
import BasicInfoForm from './components/BasicInfoForm.vue'
import type { Customer } from '@/api/customerService'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const dialog = useDialog()
const customerStore = useCustomerStore()

// 响应式数据
const customer = ref<Customer | null>(null)
const showBasicInfoModal = ref(false)
const customerStats = reactive({
  followUpCount: 0,
  meetingCount: 0,
  daysSinceCreated: 0,
  lastContactDays: 0
})

// 计算属性
const currentStage = computed(() => {
  if (!customer.value) return CustomerStage.INITIAL
  return customerStageUtils.calculateStage(customer.value)
})

const stageProgress = computed(() => {
  return customerStageUtils.getStageProgress(currentStage.value)
})

const infoCompleteness = computed(() => {
  if (!customer.value) return 0
  return customerStageUtils.calculateInfoCompleteness(customer.value)
})

const nextActionSuggestions = computed(() => {
  if (!customer.value) return []
  return customerStageUtils.getNextActionSuggestions(customer.value)
})

// 更多操作菜单
const moreActions = [
  {
    label: '分配客户',
    key: 'assign',
    icon: () => h('n-icon', null, { default: () => h('person-add-outline') })
  },
  {
    label: '客户标签',
    key: 'tags',
    icon: () => h('n-icon', null, { default: () => h('pricetag-outline') })
  },
  {
    label: '导出信息',
    key: 'export',
    icon: () => h('n-icon', null, { default: () => h('download-outline') })
  },
  {
    type: 'divider',
    key: 'd1'
  },
  {
    label: '删除客户',
    key: 'delete',
    icon: () => h('n-icon', null, { default: () => h('trash-outline') })
  }
]

// 方法
const handleBack = () => {
  router.back()
}

const handleEditBasicInfo = () => {
  showBasicInfoModal.value = true
}

const handleContact = () => {
  if (customer.value?.phone) {
    window.open(`tel:${customer.value.phone}`)
  } else {
    message.warning('客户手机号码为空')
  }
}

const handleCall = () => {
  handleContact()
}

const handleMoreAction = (key: string) => {
  switch (key) {
    case 'assign':
      message.info('分配客户功能开发中')
      break
    case 'tags':
      message.info('客户标签功能开发中')
      break
    case 'export':
      message.info('导出信息功能开发中')
      break
    case 'delete':
      handleDeleteCustomer()
      break
  }
}

const handleDeleteCustomer = () => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除客户"${customer.value?.name}"吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        if (customer.value?.id) {
          await customerStore.deleteCustomer(customer.value.id)
          message.success('删除成功')
          router.push('/customer/list')
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleStageUpdated = () => {
  fetchCustomerDetail()
}

const handleFollowUpAdded = () => {
  fetchCustomerDetail()
  fetchCustomerStats()
}

const handleBasicInfoSuccess = () => {
  fetchCustomerDetail()
  message.success('基本信息更新成功')
}

// 工具方法
const getStageTagType = (stage: CustomerStage) => {
  return customerStageUtils.getStageTagType(stage)
}

const getStageText = (stage: CustomerStage) => {
  return customerStageUtils.getStageText(stage)
}

const getStageColor = (stage: CustomerStage) => {
  return customerStageUtils.getStageColor(stage)
}

const getStageColorLight = (stage: CustomerStage) => {
  const color = getStageColor(stage)
  return color + '40' // 添加透明度
}

const getCompletenessColor = (percentage: number) => {
  if (percentage >= 80) return '#52c41a'
  if (percentage >= 60) return '#faad14'
  return '#ff4d4f'
}

const formatDate = (date: string | Date | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 数据获取
const fetchCustomerDetail = async () => {
  try {
    const customerId = route.params.id as string
    if (customerId) {
      customer.value = await customerStore.getCustomerById(customerId)
    }
  } catch (error) {
    message.error('获取客户详情失败')
    console.error('获取客户详情失败:', error)
  }
}

const fetchCustomerStats = async () => {
  try {
    const customerId = route.params.id as string
    if (customerId) {
      // TODO: 调用API获取客户统计数据
      customerStats.followUpCount = 5
      customerStats.meetingCount = 2
      customerStats.daysSinceCreated = 15
      customerStats.lastContactDays = 2
    }
  } catch (error) {
    console.error('获取客户统计失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  await fetchCustomerDetail()
  await fetchCustomerStats()
})
</script>

<style scoped>
.customer-detail {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.back-button {
  margin-bottom: 12px;
  color: #666;
  font-size: 14px;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.customer-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-id {
  color: #666;
  font-size: 14px;
}

.header-actions {
  margin-left: 24px;
}

.overview-section {
  margin-bottom: 16px;
}

.info-card,
.stats-card,
.progress-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.info-card:hover,
.stats-card:hover,
.progress-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1a1a1a;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 14px;
  min-width: 80px;
}

.info-value {
  color: #1a1a1a;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px 8px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1677ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stage-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-title {
  font-size: 14px;
  color: #666;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #1677ff;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #f0f0f0 0%, #e8e8e8 100%);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 4px;
  background: linear-gradient(90deg, var(--fill-color) 0%, var(--fill-color-light) 100%);
}

.stage-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.stage-label {
  font-size: 11px;
  color: #999;
  transition: color 0.3s ease;
}

.stage-label.active {
  color: #1677ff;
  font-weight: 500;
}

.completeness-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.completeness-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.completeness-title {
  font-size: 14px;
  color: #666;
}

.completeness-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #52c41a;
}

.completeness-tips {
  margin-top: 8px;
}

.next-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.actions-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.action-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-tag:hover {
  transform: translateY(-1px);
}

.stage-flow-section {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .overview-section :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .customer-meta {
    flex-wrap: wrap;
  }
  
  .overview-section :deep(.n-grid) {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .info-grid {
    gap: 12px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .header-actions :deep(.n-space) {
    flex-direction: column;
    width: 100%;
  }
  
  .customer-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>