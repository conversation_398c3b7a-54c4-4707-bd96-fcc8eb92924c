* {
  box-sizing: border-box;
}

.frame {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 14px;
  background: #ffffff;
  padding-bottom: 28px;
  width: 600px;
  height: 670px;
  overflow: hidden;

  .text8 {
    margin: 8px 0px 0px 16px;
    line-height: 19px;
    letter-spacing: 0;
    color: #000000;
    font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
    font-size: 15px;
  }

  .text6 {
    margin: 8px 0px 0px;
    line-height: 19px;
    letter-spacing: 0;
    color: #000000;
    font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
    font-size: 15px;
  }

  .text4 {
    line-height: 19px;
    letter-spacing: 0;
    color: #000000;
    font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", <PERSON><PERSON><PERSON><PERSON>, Arial, Helvetica, sans-serif;
    font-size: 15px;
  }

  .rectangle12 {
    margin-left: 9px;
    outline-width: 1px;
    outline-style: solid;
    outline-color: #d8d8d8;
    border-radius: 4px;
    width: 186px;
    height: 34px;
  }

  .rectangle16 {
    position: absolute;
    top: 0;
    left: 0;
    outline-width: 1px;
    outline-style: solid;
    outline-color: #d8d8d8;
    border-radius: 4px;
    width: 186px;
    height: 34px;
  }

  .autoWrapper5 {
    position: relative;
    margin-left: 9px;
    width: 186px;
    height: 34px;

    .a5 {
      position: absolute;
      top: 7px;
      left: 160px;
      width: 9px;
      height: 22px;
      letter-spacing: 0;
      color: #9a9a9a;
      font-family: "GFS Didot", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 16px;
      rotate: 90deg;
    }
  }

  .rectangle28 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    background: #4673e94f;
    padding: 13px 28px 12px 25px;

    .autoWrapper {
      position: relative;
      margin-top: 2px;
      width: 24px;
      height: 24px;

      .ellipse1 {
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 50%;
        background: #667eea;
        width: 24px;
        height: 24px;
      }

      .i {
        position: absolute;
        top: -1px;
        left: 9px;
        width: 6px;
        height: 25px;
        line-height: 25px;
        letter-spacing: 0;
        color: #f0f0f0;
        font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 20px;
      }
    }

    .text {
      margin: 1px 0px 0px 10px;
      line-height: 25px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 20px;
    }

    .x {
      margin: 0px 0px 0px 420px;
      width: 13px;
      height: 21px;
      letter-spacing: 0;
      color: #949494;
      font-family: Gabarito, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 20px;
    }
  }

  .autoWrapper3 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    margin-top: 26px;
    padding-right: 118px;
    padding-left: 25px;

    .text2 {
      margin: 7px 0px 0px;
      line-height: 19px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .a {
      margin: 10px 0px 0px 4px;
      line-height: 19px;
      letter-spacing: 0;
      color: #ff0000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .text3 {
      margin: 6px 0px 0px 41px;
      line-height: 19px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .autoWrapper2 {
      position: relative;
      margin-left: 22px;
      width: 91px;
      height: 34px;

      .rectangle14 {
        position: absolute;
        top: 0;
        left: 0;
        outline-width: 1px;
        outline-style: solid;
        outline-color: #d8d8d8;
        border-radius: 4px;
        width: 91px;
        height: 34px;
      }

      .a2 {
        position: absolute;
        top: 6px;
        left: 69px;
        width: 9px;
        height: 22px;
        letter-spacing: 0;
        color: #9a9a9a;
        font-family: "GFS Didot", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 16px;
        rotate: 90deg;
      }
    }
  }

  .autoWrapper4 {
    display: flex;
    align-items: center;
    align-self: stretch;
    margin-top: 28px;
    padding-right: 23px;
    padding-left: 25px;

    .a3 {
      margin: 0px 0px 0px 4px;
      line-height: 19px;
      letter-spacing: 0;
      color: #ff0000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .text5 {
      margin: 0px 0px 0px 16px;
      line-height: 19px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }
  }

  .autoWrapper6 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    margin-top: 29px;
    padding-right: 94px;
    padding-left: 25px;

    .a4 {
      margin: 11px 0px 0px 4px;
      line-height: 19px;
      letter-spacing: 0;
      color: #ff0000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .text7 {
      margin: 7px 0px 0px 16px;
      line-height: 19px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .rectangle18 {
      margin-left: 22px;
      outline-width: 1px;
      outline-style: solid;
      outline-color: #d8d8d8;
      border-radius: 4px;
      width: 91px;
      height: 34px;
    }

    .a6 {
      margin: 12px 0px 0px 9px;
      line-height: 19px;
      letter-spacing: 0;
      color: #000000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }
  }

  .autoWrapper8 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    margin-top: 29px;
    padding-right: 23px;
    padding-left: 25px;

    .a7 {
      margin: 9px 0px 0px 4px;
      line-height: 19px;
      letter-spacing: 0;
      color: #ff0000;
      font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
      font-size: 15px;
    }

    .autoWrapper7 {
      position: relative;
      margin-left: 22px;
      width: 186px;
      height: 34px;

      .a8 {
        position: absolute;
        top: 6px;
        left: 159px;
        width: 9px;
        height: 22px;
        letter-spacing: 0;
        color: #9a9a9a;
        font-family: "GFS Didot", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 16px;
        rotate: 90deg;
      }
    }
  }

  .autoWrapper10 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    margin-top: 29px;
    padding-right: 84px;
    padding-left: 25px;

    .autoWrapper9 {
      position: relative;
      margin-left: 22px;
      width: 186px;
      height: 34px;

      .a9 {
        position: absolute;
        top: 6px;
        left: 160px;
        width: 9px;
        height: 22px;
        letter-spacing: 0;
        color: #9a9a9a;
        font-family: "GFS Didot", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 16px;
        rotate: 90deg;
      }
    }

    .rectangle25 {
      display: flex;
      align-items: center;
      margin-top: 5px;
      margin-left: 25px;
      border-radius: 13px;
      background: #e5e3e3;
      padding: 1px 31px 1px 1px;

      .ellipse3 {
        border-radius: 50%;
        background: #ffffff;
        width: 24px;
        height: 24px;
      }
    }

    .rectangle27 {
      display: flex;
      align-items: center;
      margin-top: 4px;
      margin-left: 10px;
      border-radius: 13px;
      background: #4673e9;
      padding: 1px 1px 1px 31px;

      .ellipse4 {
        border-radius: 50%;
        background: #fdfdff;
        width: 24px;
        height: 24px;
      }
    }
  }

  .autoWrapper11 {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    margin-top: 34px;
    padding-right: 445px;
    padding-left: 25px;

    .rectangle23 {
      outline-width: 2px;
      outline-style: solid;
      outline-color: #d8d8d8;
      border-radius: 4px;
      width: 48px;
      height: 26px;
    }
  }

  .autoWrapper12 {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    justify-content: space-between;
    margin-top: 40px;
    padding-right: 30px;
    padding-left: 25px;

    .rectangle20 {
      outline-width: 1px;
      outline-style: solid;
      outline-color: #d8d8d8;
      border-radius: 4px;
      width: 463px;
      height: 109px;
    }
  }

  .autoWrapper13 {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    margin-top: 37px;
    padding-right: 30px;
    padding-left: 424px;

    .rectangle21 {
      display: flex;
      align-items: center;
      outline-width: 1px;
      outline-style: solid;
      outline-color: #d8d8d8;
      border-radius: 4px;
      padding: 7px 16px 8px;

      .text9 {
        line-height: 19px;
        letter-spacing: 0;
        color: #5b5b5b;
        font-family: Gabarito, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 16px;
      }
    }

    .rectangle22 {
      display: flex;
      align-items: center;
      outline-width: 1px;
      outline-style: solid;
      outline-color: #d8d8d8;
      border-radius: 4px;
      background: #4673e9;
      padding: 7px 16px 8px;

      .text10 {
        line-height: 19px;
        letter-spacing: 0;
        color: #ffffff;
        font-family: Gabarito, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .a10 {
    display: flex;
    position: absolute;
    top: 388px;
    left: 121px;
    align-items: center;
    width: 18px;
    height: 36px;
    letter-spacing: 0;
    color: #929292;
    font-family: Inder, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimHei, Arial, Helvetica, sans-serif;
    font-size: 30px;
  }
}
