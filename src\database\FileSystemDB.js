const fs = require('fs').promises;
const path = require('path');

class FileSystemDB {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data');
    this.tables = {};
    this.nextIds = {};
  }

  async connect() {
    try {
      // 确保数据目录存在
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // 初始化表结构
      await this.initTables();
      
      console.log('文件系统数据库连接成功');
      return true;
    } catch (error) {
      console.error('数据库连接失败:', error);
      throw error;
    }
  }

  async disconnect() {
    console.log('数据库连接已关闭');
  }

  async initTables() {
    const tableNames = [
      'users', 'customers', 'departments', 'customer_tags', 
      'customer_tag_relations', 'follow_records', 'meeting_records',
      'system_configs', 'operation_logs'
    ];

    for (const tableName of tableNames) {
      await this.loadTable(tableName);
    }

    // 创建默认管理员用户
    await this.createDefaultAdmin();
    
    console.log('数据库表初始化完成');
  }

  async loadTable(tableName) {
    const filePath = path.join(this.dataDir, `${tableName}.json`);
    
    try {
      const data = await fs.readFile(filePath, 'utf8');
      const tableData = JSON.parse(data);
      this.tables[tableName] = tableData.rows || [];
      this.nextIds[tableName] = tableData.nextId || 1;
    } catch (error) {
      // 文件不存在，创建空表
      this.tables[tableName] = [];
      this.nextIds[tableName] = 1;
      await this.saveTable(tableName);
    }
  }

  async saveTable(tableName) {
    const filePath = path.join(this.dataDir, `${tableName}.json`);
    const data = {
      rows: this.tables[tableName],
      nextId: this.nextIds[tableName]
    };
    
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
  }

  async query(sql, params = []) {
    // 简单的 SQL 解析器
    const sqlLower = sql.trim().toLowerCase();
    
    if (sqlLower.startsWith('select')) {
      return await this.handleSelect(sql, params);
    } else if (sqlLower.startsWith('insert')) {
      return await this.handleInsert(sql, params);
    } else if (sqlLower.startsWith('update')) {
      return await this.handleUpdate(sql, params);
    } else if (sqlLower.startsWith('delete')) {
      return await this.handleDelete(sql, params);
    }
    
    return { rows: [] };
  }

  async handleSelect(sql, params) {
    // 提取表名
    const tableMatch = sql.match(/from\s+(\w+)/i);
    if (!tableMatch) return { rows: [] };
    
    const tableName = tableMatch[1];
    const rows = this.tables[tableName] || [];
    
    // 简单的 WHERE 条件处理
    if (sql.includes('WHERE') || sql.includes('where')) {
      // 这里可以添加更复杂的查询逻辑
      // 现在返回所有行
      return { rows };
    }
    
    return { rows };
  }

  async handleInsert(sql, params) {
    // 提取表名
    const tableMatch = sql.match(/into\s+(\w+)/i);
    if (!tableMatch) return { rows: { lastID: 0 } };
    
    const tableName = tableMatch[1];
    
    // 提取字段名
    const fieldsMatch = sql.match(/\(([^)]+)\)/);
    if (!fieldsMatch) return { rows: { lastID: 0 } };
    
    const fields = fieldsMatch[1].split(',').map(f => f.trim());
    
    // 创建新记录
    const newRecord = { id: this.nextIds[tableName] };
    
    fields.forEach((field, index) => {
      if (field !== 'id') {
        newRecord[field] = params[index] || null;
      }
    });
    
    // 添加时间戳
    newRecord.created_at = new Date().toISOString();
    newRecord.updated_at = new Date().toISOString();
    
    this.tables[tableName].push(newRecord);
    const insertId = this.nextIds[tableName];
    this.nextIds[tableName]++;
    
    await this.saveTable(tableName);
    
    return { rows: { lastID: insertId } };
  }

  async handleUpdate(sql, params) {
    // 简单的更新实现
    const tableMatch = sql.match(/update\s+(\w+)/i);
    if (!tableMatch) return { rows: { changes: 0 } };
    
    const tableName = tableMatch[1];
    const rows = this.tables[tableName] || [];
    
    // 这里可以添加更复杂的更新逻辑
    await this.saveTable(tableName);
    
    return { rows: { changes: 1 } };
  }

  async handleDelete(sql, params) {
    // 简单的删除实现
    const tableMatch = sql.match(/from\s+(\w+)/i);
    if (!tableMatch) return { rows: { changes: 0 } };
    
    const tableName = tableMatch[1];
    
    // 这里可以添加更复杂的删除逻辑
    await this.saveTable(tableName);
    
    return { rows: { changes: 1 } };
  }

  async createDefaultAdmin() {
    const users = this.tables.users || [];
    
    // 检查是否已存在管理员
    const adminExists = users.some(user => user.username === 'admin');
    
    if (!adminExists) {
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const adminUser = {
        id: this.nextIds.users,
        username: 'admin',
        password: hashedPassword,
        name: '系统管理员',
        role: 'admin',
        status: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      this.tables.users.push(adminUser);
      this.nextIds.users++;
      
      await this.saveTable('users');
      console.log('默认管理员账户创建成功 (用户名: admin, 密码: admin123)');
    }
  }

  async transaction(callback) {
    // 简单的事务实现
    try {
      const result = await callback(this);
      return result;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = FileSystemDB;