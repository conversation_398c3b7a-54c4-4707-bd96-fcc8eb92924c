const mysql = require('mysql2/promise');

async function checkTables() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'workchat_admin'
  });

  try {
    console.log('检查数据库连接...');
    
    // 检查所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('数据库中的表:', tables);
    
    // 检查customers表是否存在
    const [customersTables] = await connection.execute("SHOW TABLES LIKE 'customers'");
    if (customersTables.length > 0) {
      console.log('customers表存在');
      
      // 查看customers表结构
      const [columns] = await connection.execute('DESCRIBE customers');
      console.log('customers表结构:', columns);
      
      // 查看customers表数据数量
      const [count] = await connection.execute('SELECT COUNT(*) as count FROM customers');
      console.log('customers表记录数:', count[0].count);
    } else {
      console.log('customers表不存在');
    }
    
    // 检查users表
    const [usersTables] = await connection.execute("SHOW TABLES LIKE 'users'");
    if (usersTables.length > 0) {
      console.log('users表存在');
      const [userColumns] = await connection.execute('DESCRIBE users');
      console.log('users表结构:', userColumns);
    } else {
      console.log('users表不存在');
    }
    
    // 检查departments表
    const [deptTables] = await connection.execute("SHOW TABLES LIKE 'departments'");
    if (deptTables.length > 0) {
      console.log('departments表存在');
      const [deptColumns] = await connection.execute('DESCRIBE departments');
      console.log('departments表结构:', deptColumns);
    } else {
      console.log('departments表不存在');
    }
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await connection.end();
  }
}

checkTables();