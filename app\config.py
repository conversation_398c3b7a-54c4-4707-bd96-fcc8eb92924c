"""应用配置管理

使用Pydantic Settings管理环境变量和应用配置
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os
from pathlib import Path
from dotenv import load_dotenv

# 手动加载.env文件
load_dotenv()


class DatabaseSettings:
    """数据库配置"""
    
    def __init__(self):
        import os
        # 从环境变量获取值，如果为空或不存在则使用默认值
        self.host = os.getenv('DATABASE_HOST') or "localhost"
        self.port = int(os.getenv('DATABASE_PORT') or "3306") if os.getenv('DATABASE_PORT') and os.getenv('DATABASE_PORT').strip() else 3306
        self.username = os.getenv('DATABASE_USERNAME') or "root"
        self.password = os.getenv('DATABASE_PASSWORD') or ""
        self.database = os.getenv('DATABASE_DATABASE') or "yysh"
        self.charset = os.getenv('DATABASE_CHARSET') or "utf8mb4"
    
    @property
    def name(self) -> str:
        """数据库名称别名，兼容性属性"""
        return self.database
    charset: str = Field(default="utf8mb4", description="数据库字符集")
    
    @property
    def url(self) -> str:
        """构建同步数据库连接URL"""
        if self.database.endswith('.db'):
            # SQLite数据库
            return f"sqlite:///{self.database}"
        else:
            # MySQL数据库
            return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    @property
    def async_url(self) -> str:
        """构建异步数据库连接URL"""
        if self.database.endswith('.db'):
            # SQLite数据库（异步）
            return f"sqlite+aiosqlite:///{self.database}"
        else:
            # MySQL数据库（异步）
            return f"mysql+aiomysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    class Config:
        env_prefix = "DATABASE_"


class RedisSettings(BaseSettings):
    """Redis配置"""
    
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    redis_db: int = Field(default=0, description="Redis数据库编号")
    redis_max_connections: int = Field(default=10, description="Redis最大连接数")
    redis_socket_timeout: int = Field(default=5, description="Redis套接字超时")
    redis_socket_connect_timeout: int = Field(default=5, description="Redis连接超时")
    
    @property
    def url(self) -> str:
        """构建Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    class Config:
        env_prefix = ""


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥"
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="访问令牌过期时间(分钟)"
    )
    refresh_token_expire_days: int = Field(
        default=7,
        description="刷新令牌过期时间(天)"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    password_hash_rounds: int = Field(
        default=12,
        description="密码哈希轮数"
    )
    
    class Config:
        env_prefix = ""


class AppSettings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = Field(default="YYSH API", description="应用名称")
    app_debug: bool = Field(default=False, description="调试模式")
    app_environment: str = Field(default="development", description="运行环境")
    
    # 服务器配置
    app_host: str = Field(default="0.0.0.0", description="服务器主机")
    app_port: int = Field(default=8000, description="服务器端口")
    
    # API配置
    api_prefix: str = Field(default="/api", description="API前缀")
    docs_url: Optional[str] = Field(default="/docs", description="文档URL")
    redoc_url: Optional[str] = Field(default="/redoc", description="ReDoc URL")
    
    # CORS配置
    cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173"],
        description="允许的CORS源"
    )
    
    # 日志配置
    app_log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    
    class Config:
        env_prefix = ""


class Settings(BaseSettings):
    """主配置类"""
    
    # 子配置
    app: AppSettings = AppSettings()
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    security: SecuritySettings = SecuritySettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"  # 允许额外字段
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 手动加载子配置的环境变量
        self.database = DatabaseSettings()
        self.app = AppSettings()
        self.redis = RedisSettings()
        self.security = SecuritySettings()


# 创建全局配置实例
settings = Settings()


# 配置验证函数
def validate_settings() -> bool:
    """验证配置是否正确"""
    try:
        # 检查必要的配置项
        if not settings.database.host:
            raise ValueError("数据库主机不能为空")
        
        if not settings.security.secret_key or settings.security.secret_key == "your-secret-key-change-in-production":
            if settings.app.app_environment == "production":
                raise ValueError("生产环境必须设置安全的SECRET_KEY")
        
        return True
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent


# 初始化配置
def init_settings():
    """初始化配置"""
    if not validate_settings():
        raise RuntimeError("配置验证失败，请检查环境变量")
    
    print(f"✅ 配置加载成功 - 环境: {settings.app.app_environment}")
    print(f"📊 数据库: {settings.database.host}:{settings.database.port}")
    print(f"🔧 调试模式: {settings.app.app_debug}")