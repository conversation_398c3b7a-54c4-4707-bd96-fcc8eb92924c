"""数据库连接管理

配置SQLAlchemy数据库连接和会话管理
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session
from sqlalchemy import event
from sqlalchemy.pool import StaticPool
from typing import Generator
import logging

from .config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 创建异步数据库引擎
engine = create_async_engine(
    settings.database.async_url,
    # 连接池配置
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    # 调试模式下显示SQL
    echo=settings.app.app_debug,
    # 其他配置
    future=True
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 为了兼容性，提供别名
async_session_factory = AsyncSessionLocal

# 导入基础模型类
from .models.base import Base


# 数据库会话依赖
async def get_db() -> AsyncSession:
    """获取异步数据库会话
    
    用于FastAPI依赖注入
    """
    async with AsyncSessionLocal() as session:
        yield session


# 数据库连接测试
async def test_database_connection() -> bool:
    """测试数据库连接
    
    Returns:
        bool: 连接是否成功
    """
    try:
        from sqlalchemy import text
        async with engine.begin() as connection:
            await connection.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
            return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


# 创建所有表
async def create_tables():
    """创建所有数据库表"""
    try:
        async with engine.begin() as connection:
            await connection.run_sync(Base.metadata.create_all)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {e}")
        raise


# 删除所有表
async def drop_tables():
    """删除所有数据库表"""
    try:
        async with engine.begin() as connection:
            await connection.run_sync(Base.metadata.drop_all)
        logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"数据库表删除失败: {e}")
        raise


# 数据库事件监听器（仅用于同步操作）
# 注意：异步引擎不支持事件监听器，这里移除了相关配置


class DatabaseManager:
    """数据库管理器
    
    提供数据库操作的高级接口
    """
    
    def __init__(self):
        self.engine = engine
        self.AsyncSessionLocal = AsyncSessionLocal
    
    async def get_session(self) -> AsyncSession:
        """获取异步数据库会话"""
        async with self.AsyncSessionLocal() as session:
            yield session
    
    async def health_check(self) -> bool:
        """数据库健康检查
        
        Returns:
            bool: 数据库是否健康
        """
        try:
            from sqlalchemy import text
            async with AsyncSessionLocal() as session:
                await session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False
    
    async def get_database_stats(self) -> dict:
        """获取数据库统计信息
        
        Returns:
            dict: 数据库统计信息
        """
        try:
            from sqlalchemy import text
            async with AsyncSessionLocal() as session:
                # 获取表数量
                result = await session.execute(text(
                    "SELECT COUNT(*) as table_count FROM sqlite_master WHERE type='table'"
                ))
                table_count = result.scalar()
                
                return {
                    "status": "healthy",
                    "table_count": table_count,
                    "engine": "SQLite"
                }
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def init_db(self):
        """初始化数据库"""
        logger.info("开始初始化数据库...")
        await create_tables()
        logger.info("数据库初始化完成")
    
    async def reset_db(self):
        """重置数据库"""
        logger.warning("开始重置数据库...")
        await drop_tables()
        await create_tables()
        logger.info("数据库重置完成")


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


# 数据库初始化函数
async def init_database():
    """初始化数据库连接和表结构"""
    try:
        logger.info("正在初始化数据库...")
        
        # 测试连接
        from sqlalchemy import text
        async with engine.begin() as connection:
            await connection.execute(text("SELECT 1"))
            logger.info(f"数据库连接成功: {settings.database.host}:{settings.database.port}")
        
        # 创建表结构
        await create_tables()
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise RuntimeError(f"数据库初始化失败: {e}")


# 关闭数据库连接
async def close_database():
    """关闭数据库连接"""
    try:
        await engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")