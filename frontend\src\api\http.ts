import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores'
import router from '@/router'

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 添加认证token
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response.data
    }
    
    // 统一处理响应数据格式
    if (data && typeof data === 'object') {
      // 如果后端返回的数据格式不统一，在这里进行标准化
      if (data.code !== undefined) {
        return {
          success: data.code === 200 || data.code === 0,
          message: data.message || data.msg || '',
          data: data.data || data.result || data,
          code: data.code
        }
      }
      
      // 如果已经是标准格式，直接返回
      if (data.success !== undefined) {
        return data
      }
      
      // 默认认为请求成功
      return {
        success: true,
        message: 'success',
        data: data
      }
    }
    
    return {
      success: true,
      message: 'success',
      data: data
    }
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    // 处理网络错误
    if (!error.response) {
      console.error('网络连接失败，请检查网络设置')
      return Promise.reject({
        success: false,
        message: '网络连接失败',
        data: null
      })
    }
    
    const { status, data } = error.response
    let errorMessage = '请求失败'
    
    // 根据状态码处理不同错误
    switch (status) {
      case 401:
        errorMessage = '登录已过期，请重新登录'
        // 延迟执行，避免在拦截器中直接使用组件
        setTimeout(() => {
          try {
            const authStore = useAuthStore()
            authStore.logout()
            router.push('/login')
          } catch (err) {
            console.error('处理401错误时出现问题:', err)
            // 如果store或router不可用，直接跳转到登录页
            window.location.href = '/login'
          }
        }, 100)
        break
        
      case 403:
        errorMessage = '没有权限访问该资源'
        break
        
      case 404:
        errorMessage = '请求的资源不存在'
        break
        
      case 422:
        errorMessage = data?.message || '表单验证失败'
        break
        
      case 429:
        errorMessage = '请求过于频繁，请稍后再试'
        break
        
      case 500:
        errorMessage = '服务器内部错误'
        break
        
      case 502:
      case 503:
      case 504:
        errorMessage = '服务暂时不可用，请稍后再试'
        break
        
      default:
        errorMessage = data?.message || `请求失败 (${status})`
    }
    
    console.error('API错误:', errorMessage)
    
    return Promise.reject({
      success: false,
      message: data?.message || errorMessage,
      data: null,
      code: status
    })
  }
)

// 封装常用请求方法
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return http.get(url, config)
  },
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return http.post(url, data, config)
  },
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return http.put(url, data, config)
  },
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return http.delete(url, config)
  },
  
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return http.patch(url, data, config)
  }
}

// 文件上传
export const uploadFile = (url: string, file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 文件下载
export const downloadFile = async (url: string, filename?: string, params?: any) => {
  try {
    const response = await http.get(url, {
      params,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    return true
  } catch (error) {
    console.error('文件下载失败:', error)
    return false
  }
}

export { http }
export default http