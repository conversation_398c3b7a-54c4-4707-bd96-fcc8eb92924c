const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function recreateTables() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      multipleStatements: true
    });

    console.log('✅ 连接到MySQL数据库成功');

    // 读取SQL文件
    const sqlPath = path.join(__dirname, 'mysql', 'create-mysql-tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    console.log('📋 开始重新创建表结构...');

    // 首先删除所有表（按依赖关系逆序）
    const dropTables = [
      'migration_logs',
      'public_pool',
      'follow_ups',
      'customer_value_analysis',
      'sales_funnel_stats',
      'wechat_customer_tracking',
      'customer_behaviors',
      'pool_rules',
      'meeting_participants',
      'meetings',
      'campaign_shares',
      'campaign_participants',
      'marketing_campaigns',
      'customer_follow_records',
      'customers',
      'option_items',
      'option_categories',
      'user_roles',
      'role_permissions',
      'permissions',
      'roles',
      'users'
    ];

    console.log('🗑️ 删除现有表...');
    for (const table of dropTables) {
      try {
        await connection.execute(`DROP TABLE IF EXISTS \`${table}\``);
        console.log(`   ✅ 删除表 ${table}`);
      } catch (error) {
        console.log(`   ⚠️ 删除表 ${table} 失败: ${error.message}`);
      }
    }

    // 执行创建表的SQL
    console.log('🏗️ 创建新表结构...');
    
    // 先执行SET语句
    await connection.execute('SET NAMES utf8mb4');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    
    // 分割SQL语句并逐个执行
    const lines = sqlContent.split('\n');
    let currentStatement = '';
    let inCreateTable = false;
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过注释和空行
      if (!trimmedLine || trimmedLine.startsWith('--')) {
        continue;
      }
      
      // 跳过SET和USE语句
      if (trimmedLine.startsWith('SET ') || trimmedLine.startsWith('USE ') || trimmedLine.startsWith('CREATE DATABASE')) {
        continue;
      }
      
      // 检测CREATE TABLE开始
      if (trimmedLine.startsWith('CREATE TABLE')) {
        inCreateTable = true;
        currentStatement = trimmedLine;
        continue;
      }
      
      // 如果在CREATE TABLE语句中
      if (inCreateTable) {
        currentStatement += ' ' + trimmedLine;
        
        // 检测语句结束
        if (trimmedLine.endsWith(';')) {
          try {
            // 移除末尾的分号
            const cleanStatement = currentStatement.replace(/;$/, '');
            await connection.execute(cleanStatement);
            const tableName = cleanStatement.match(/CREATE TABLE IF NOT EXISTS `([^`]+)`/)?.[1] || 'unknown';
            console.log(`   ✅ 创建表 ${tableName}`);
          } catch (error) {
            console.log(`   ❌ 创建表失败: ${error.message}`);
          }
          
          // 重置状态
          inCreateTable = false;
          currentStatement = '';
        }
      }
    }
    
    // 重新启用外键检查
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    
    console.log('✅ 表结构创建完成');

    // 验证表是否创建成功
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 成功创建 ${tables.length} 个表:`);
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`   - ${tableName}`);
    });

  } catch (error) {
    console.error('❌ 重新创建表失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行脚本
recreateTables()
  .then(() => {
    console.log('🎉 表结构重新创建完成!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });