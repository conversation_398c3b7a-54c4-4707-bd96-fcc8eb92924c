<template>
  <div class="settings">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">系统设置</h1>
          <p class="page-description">管理系统的基础配置和参数</p>
        </div>
      </div>
    </div>

    <n-grid :cols="1" :y-gap="20">
      <n-card title="基础设置" class="settings-card">
        <n-form
          ref="basicFormRef"
          :model="basicForm"
          :rules="basicRules"
          label-placement="left"
          label-width="120px"
        >
          <n-grid :cols="2" :x-gap="20">
            <n-grid-item>
              <n-form-item label="系统名称" path="systemName">
                <n-input v-model:value="basicForm.systemName" placeholder="请输入系统名称" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="系统版本" path="systemVersion">
                <n-input v-model:value="basicForm.systemVersion" placeholder="请输入系统版本" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="公司名称" path="companyName">
                <n-input v-model:value="basicForm.companyName" placeholder="请输入公司名称" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="联系电话" path="contactPhone">
                <n-input v-model:value="basicForm.contactPhone" placeholder="请输入联系电话" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="联系邮箱" path="contactEmail">
                <n-input v-model:value="basicForm.contactEmail" placeholder="请输入联系邮箱" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="公司地址" path="companyAddress">
                <n-input v-model:value="basicForm.companyAddress" placeholder="请输入公司地址" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          
          <n-form-item label="系统描述" path="systemDescription">
            <n-input
              v-model:value="basicForm.systemDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入系统描述"
            />
          </n-form-item>

          <n-form-item>
            <n-space>
              <n-button type="primary" @click="handleSaveBasic">
                <template #icon>
                  <n-icon><SaveOutline /></n-icon>
                </template>
                保存设置
              </n-button>
              <n-button @click="handleResetBasic">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                重置
              </n-button>
            </n-space>
          </n-form-item>
        </n-form>
      </n-card>

      <n-card title="客户设置" class="settings-card">
        <n-form
          ref="customerFormRef"
          :model="customerForm"
          :rules="customerRules"
          label-placement="left"
          label-width="120px"
        >
          <n-grid :cols="2" :x-gap="20">
            <n-grid-item>
              <n-form-item label="默认客户等级" path="defaultCustomerLevel">
                <n-select v-model:value="customerForm.defaultCustomerLevel" :options="customerLevelOptions" placeholder="请选择默认客户等级" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="客户编号前缀" path="customerCodePrefix">
                <n-input v-model:value="customerForm.customerCodePrefix" placeholder="请输入客户编号前缀" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="自动分配销售" path="autoAssignSales">
                <n-switch v-model:value="customerForm.autoAssignSales" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="客户跟进提醒" path="followUpReminder">
                <n-switch v-model:value="customerForm.followUpReminder" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="跟进间隔天数" path="followUpDays">
                <n-input-number v-model:value="customerForm.followUpDays" :min="1" :max="30" placeholder="请输入跟进间隔天数" style="width: 100%" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="客户数据保留" path="dataRetentionDays">
                <n-input-number v-model:value="customerForm.dataRetentionDays" :min="30" :max="3650" placeholder="请输入数据保留天数" style="width: 100%" />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item>
            <n-space>
              <n-button type="primary" @click="handleSaveCustomer">
                <template #icon>
                  <n-icon><SaveOutline /></n-icon>
                </template>
                保存设置
              </n-button>
              <n-button @click="handleResetCustomer">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                重置
              </n-button>
            </n-space>
          </n-form-item>
        </n-form>
      </n-card>

      <n-card title="系统信息" class="settings-card">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="系统版本">
            {{ systemInfo.version }}
          </n-descriptions-item>
          <n-descriptions-item label="构建时间">
            {{ systemInfo.buildTime }}
          </n-descriptions-item>
          <n-descriptions-item label="运行环境">
            {{ systemInfo.environment }}
          </n-descriptions-item>
          <n-descriptions-item label="数据库版本">
            {{ systemInfo.databaseVersion }}
          </n-descriptions-item>
          <n-descriptions-item label="服务器时间">
            {{ systemInfo.serverTime }}
          </n-descriptions-item>
          <n-descriptions-item label="在线用户数">
            {{ systemInfo.onlineUsers }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  NGrid,
  NGridItem,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NSwitch,
  NButton,
  NSpace,
  NIcon,
  NDescriptions,
  NDescriptionsItem,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { SaveOutline, RefreshOutline } from '@vicons/ionicons5'

// 消息提示
const message = useMessage()

// 表单引用
const basicFormRef = ref<FormInst | null>(null)
const customerFormRef = ref<FormInst | null>(null)

// 基础设置表单
const basicForm = reactive({
  systemName: 'YYSH客户管理系统',
  systemVersion: '1.0.0',
  companyName: 'YYSH装饰公司',
  contactPhone: '************',
  contactEmail: '<EMAIL>',
  companyAddress: '上海市浦东新区张江高科技园区',
  systemDescription: '专业的家装客户管理系统，提供客户管理、工单处理、会议管理等功能。'
})

// 客户设置表单
const customerForm = reactive({
  defaultCustomerLevel: 'bronze',
  customerCodePrefix: 'YY',
  autoAssignSales: true,
  followUpReminder: true,
  followUpDays: 7,
  dataRetentionDays: 365
})

// 系统信息
const systemInfo = reactive({
  version: '1.0.0',
  buildTime: '2024-01-15 10:30:00',
  environment: 'Production',
  databaseVersion: 'MySQL 8.0',
  serverTime: new Date().toLocaleString(),
  onlineUsers: 12
})

// 客户等级选项
const customerLevelOptions = [
  { label: '青铜客户', value: 'bronze' },
  { label: '白银客户', value: 'silver' },
  { label: '黄金客户', value: 'gold' },
  { label: '钻石客户', value: 'diamond' }
]

// 表单验证规则
const basicRules: FormRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  systemVersion: [
    { required: true, message: '请输入系统版本', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^400-\d{3}-\d{4}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const customerRules: FormRules = {
  defaultCustomerLevel: [
    { required: true, message: '请选择默认客户等级', trigger: 'change' }
  ],
  customerCodePrefix: [
    { required: true, message: '请输入客户编号前缀', trigger: 'blur' },
    { max: 5, message: '客户编号前缀不能超过5个字符', trigger: 'blur' }
  ],
  followUpDays: [
    { required: true, message: '请输入跟进间隔天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 30, message: '跟进间隔天数必须在1-30之间', trigger: 'blur' }
  ],
  dataRetentionDays: [
    { required: true, message: '请输入数据保留天数', trigger: 'blur' },
    { type: 'number', min: 30, max: 3650, message: '数据保留天数必须在30-3650之间', trigger: 'blur' }
  ]
}

// 保存基础设置
const handleSaveBasic = async () => {
  try {
    await basicFormRef.value?.validate()
    // 这里应该调用API保存设置
    console.log('保存基础设置:', basicForm)
    message.success('基础设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

// 重置基础设置
const handleResetBasic = () => {
  Object.assign(basicForm, {
    systemName: 'YYSH客户管理系统',
    systemVersion: '1.0.0',
    companyName: 'YYSH装饰公司',
    contactPhone: '************',
    contactEmail: '<EMAIL>',
    companyAddress: '上海市浦东新区张江高科技园区',
    systemDescription: '专业的家装客户管理系统，提供客户管理、工单处理、会议管理等功能。'
  })
  message.info('基础设置已重置')
}

// 保存客户设置
const handleSaveCustomer = async () => {
  try {
    await customerFormRef.value?.validate()
    // 这里应该调用API保存设置
    console.log('保存客户设置:', customerForm)
    message.success('客户设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

// 重置客户设置
const handleResetCustomer = () => {
  Object.assign(customerForm, {
    defaultCustomerLevel: 'bronze',
    customerCodePrefix: 'YY',
    autoAssignSales: true,
    followUpReminder: true,
    followUpDays: 7,
    dataRetentionDays: 365
  })
  message.info('客户设置已重置')
}

// 更新服务器时间
const updateServerTime = () => {
  systemInfo.serverTime = new Date().toLocaleString()
}

// 组件挂载时初始化
onMounted(() => {
  // 每秒更新服务器时间
  setInterval(updateServerTime, 1000)
})
</script>

<style scoped>
.settings {
  padding: 24px;
  background: white;
  min-height: 100%;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 16px;
  color: var(--n-text-color-disabled);
  margin: 0;
  line-height: 1.4;
}

.settings-card {
  margin-bottom: 20px;
}

.settings-card :deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-divider-color);
}

.settings-card :deep(.n-card__content) {
  padding-top: 20px;
}