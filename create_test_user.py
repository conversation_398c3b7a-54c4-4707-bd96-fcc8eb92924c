#!/usr/bin/env python3
"""创建测试用户脚本"""

import asyncio
from app.models import User
from app.database import AsyncSessionLocal
from app.utils.security import get_password_hash


async def create_test_user():
    """创建测试用户"""
    async with AsyncSessionLocal() as session:
        # 检查用户是否已存在
        from sqlalchemy import select
        result = await session.execute(
            select(User).where(User.username == 'testuser')
        )
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            print(f'测试用户已存在: {existing_user.id}')
            return existing_user.id
        
        # 创建新用户
        user = User(
            username='testuser',
            email='<EMAIL>',
            password_hash=get_password_hash('testpass123'),
            is_active=True
        )
        session.add(user)
        await session.commit()
        await session.refresh(user)
        print(f'测试用户创建成功: {user.id}')
        return user.id


if __name__ == '__main__':
    asyncio.run(create_test_user())