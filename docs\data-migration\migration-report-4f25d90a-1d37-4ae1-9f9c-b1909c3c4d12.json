{"migrationId": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "timestamp": "2025-08-18T06:20:39.399Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 10, "failedTables": 11, "totalRecords": 165, "migratedRecords": 15, "failedRecords": 150}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:19:46.772Z", "errors": ["Unknown column 'role' in 'field list'"], "endTime": "2025-08-18T06:19:51.575Z", "duration": 4803}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:19:51.581Z", "errors": ["Unknown column 'permissions' in 'field list'"], "endTime": "2025-08-18T06:19:54.342Z", "duration": 2761}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 0, "failedRecords": 77, "startTime": "2025-08-18T06:19:54.345Z", "errors": ["Unknown column 'module' in 'field list'"], "endTime": "2025-08-18T06:19:58.635Z", "duration": 4290}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 0, "failedRecords": 6, "startTime": "2025-08-18T06:19:58.637Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`role_permissions`, CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T06:20:01.144Z", "duration": 2507}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:01.147Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T06:20:01.710Z", "errors": [], "endTime": "2025-08-18T06:20:05.252Z", "duration": 3542}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 0, "failedRecords": 42, "startTime": "2025-08-18T06:20:05.255Z", "errors": ["Unknown column 'label' in 'field list'"], "endTime": "2025-08-18T06:20:09.249Z", "duration": 3994}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 0, "failedRecords": 5, "startTime": "2025-08-18T06:20:09.252Z", "errors": ["Unknown column 'position' in 'field list'"], "endTime": "2025-08-18T06:20:11.934Z", "duration": 2682}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:11.937Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:20:12.371Z", "errors": ["Unknown column 'start_time' in 'field list'"], "endTime": "2025-08-18T06:20:18.637Z", "duration": 6266}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:18.639Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:19.161Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:20:20.739Z", "errors": ["Unknown column 'meeting_url' in 'field list'"], "endTime": "2025-08-18T06:20:24.252Z", "duration": 3513}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:24.255Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T06:20:24.644Z", "errors": ["Unknown column 'reminder_days' in 'field list'"], "endTime": "2025-08-18T06:20:28.022Z", "duration": 3378}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:28.024Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:28.432Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:31.101Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T06:20:32.723Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T06:20:33.584Z", "errors": ["Unknown column 'type' in 'field list'"], "endTime": "2025-08-18T06:20:36.560Z", "duration": 2976}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T06:20:36.563Z", "errors": ["Unknown column 'moved_by' in 'field list'"], "endTime": "2025-08-18T06:20:39.396Z", "duration": 2833}], "logs": [{"id": "11ca4fe3-8fc6-4ebf-ba1c-b5647781c82f", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:19:46.772Z", "end_time": "2025-08-18T06:19:51.575Z", "duration_ms": 4803}, {"id": "b99045d8-5461-4bb6-8dc9-04d8f0643759", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:19:51.581Z", "end_time": "2025-08-18T06:19:54.342Z", "duration_ms": 2761}, {"id": "2c375be6-dd95-4805-b524-7d1fac5817a9", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:19:54.345Z", "end_time": "2025-08-18T06:19:58.635Z", "duration_ms": 4290}, {"id": "3de4679b-642d-48b4-b7db-34334c8ad566", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:19:58.637Z", "end_time": "2025-08-18T06:20:01.144Z", "duration_ms": 2507}, {"id": "65a7e1a2-8635-4480-ade8-6bc52ac8cb47", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:01.147Z", "end_time": "2025-08-18T06:20:01.710Z", "duration_ms": 563}, {"id": "f30b4f0b-072c-437a-b77a-909ad057f82d", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T06:20:01.710Z", "end_time": "2025-08-18T06:20:05.252Z", "duration_ms": 3542}, {"id": "eda31175-066c-412b-b8f1-e81ba4862648", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:05.255Z", "end_time": "2025-08-18T06:20:09.249Z", "duration_ms": 3994}, {"id": "77315701-4c22-4a69-b557-ac6ac16d8a43", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:09.252Z", "end_time": "2025-08-18T06:20:11.934Z", "duration_ms": 2682}, {"id": "67a6c7d3-d032-4809-a501-d1a91630c169", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:11.937Z", "end_time": "2025-08-18T06:20:12.371Z", "duration_ms": 434}, {"id": "88118bd7-fb97-4010-82a9-3f0723fa114d", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:12.371Z", "end_time": "2025-08-18T06:20:18.637Z", "duration_ms": 6266}, {"id": "7ba4dfea-35dd-495d-a3fb-0de16b16ac98", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:18.640Z", "end_time": "2025-08-18T06:20:19.161Z", "duration_ms": 521}, {"id": "4e9439d3-55a8-4d4a-a1cb-84f33dd5fb7f", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:19.161Z", "end_time": "2025-08-18T06:20:20.739Z", "duration_ms": 1578}, {"id": "42acee7c-1c9d-4804-9641-1ab811ae15fc", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:20.739Z", "end_time": "2025-08-18T06:20:24.252Z", "duration_ms": 3513}, {"id": "cf00f133-e4d6-44fc-8e04-e1a675d1e368", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:24.255Z", "end_time": "2025-08-18T06:20:24.643Z", "duration_ms": 388}, {"id": "765eb4b5-26fc-4711-baa7-ddc137a1fee3", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:24.644Z", "end_time": "2025-08-18T06:20:28.022Z", "duration_ms": 3378}, {"id": "bdfe5a4a-5da9-4d2d-a41d-37b7ec4150f1", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:28.024Z", "end_time": "2025-08-18T06:20:28.432Z", "duration_ms": 408}, {"id": "2abc5a8c-0295-47f4-80da-61a73c278c3c", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:28.432Z", "end_time": "2025-08-18T06:20:31.101Z", "duration_ms": 2669}, {"id": "6b3822bb-98db-4066-90e8-3ffa4905ca44", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:31.101Z", "end_time": "2025-08-18T06:20:32.723Z", "duration_ms": 1622}, {"id": "631c74db-c51f-4004-8f67-6776181a17e7", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:32.723Z", "end_time": "2025-08-18T06:20:33.584Z", "duration_ms": 861}, {"id": "936a7fc2-12d6-4c1a-8b87-1de994297265", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:33.584Z", "end_time": "2025-08-18T06:20:36.560Z", "duration_ms": 2976}, {"id": "c1722de2-7392-4dfa-8e61-4f98d41555a5", "migration_id": "4f25d90a-1d37-4ae1-9f9c-b1909c3c4d12", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T06:20:36.563Z", "end_time": "2025-08-18T06:20:39.396Z", "duration_ms": 2833}]}