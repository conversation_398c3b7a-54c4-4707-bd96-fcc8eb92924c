import { MySQLManager, MySQLConfig, DatabaseError, DatabaseErrorType } from './MySQLManager';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: '../../.env' });

/**
 * MySQLManager 单元测试
 */
describe('MySQLManager', () => {
  let mysqlManager: MySQLManager;
  let config: MySQLConfig;

  beforeAll(() => {
    // 从环境变量读取MySQL配置
    config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'yysh_miniprogram',
      connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10'),
      acquireTimeout: parseInt(process.env.MYSQL_ACQUIRE_TIMEOUT || '60000'),
      timeout: parseInt(process.env.MYSQL_TIMEOUT || '60000'),
      reconnect: process.env.MYSQL_RECONNECT === 'true'
    };

    mysqlManager = new MySQLManager(config);
  });

  afterAll(async () => {
    if (mysqlManager.isConnected()) {
      await mysqlManager.disconnect();
    }
  });

  describe('连接管理', () => {
    test('应该能够成功连接到数据库', async () => {
      await expect(mysqlManager.connect()).resolves.not.toThrow();
      expect(mysqlManager.isConnected()).toBe(true);
    });

    test('应该能够获取数据库版本', async () => {
      const version = await mysqlManager.getVersion();
      expect(version).toMatch(/^\d+\.\d+\.\d+/);
    });

    test('应该能够检查连接状态', async () => {
      const isConnected = await mysqlManager.checkConnection();
      expect(isConnected).toBe(true);
    });
  });

  describe('基础查询操作', () => {
    test('应该能够执行简单的SELECT查询', async () => {
      const result = await mysqlManager.query('SELECT 1 as test_value');
      expect(result.rows).toHaveLength(1);
      expect(result.rows[0]).toEqual({ test_value: 1 });
    });

    test('应该能够执行带参数的查询', async () => {
      const result = await mysqlManager.query('SELECT ? as param_value', [42]);
      expect(result.rows).toHaveLength(1);
      expect(result.rows[0]).toEqual({ param_value: 42 });
    });

    test('查询不存在的表应该抛出错误', async () => {
      await expect(
        mysqlManager.query('SELECT * FROM non_existent_table')
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('CRUD操作', () => {
    const testTableName = 'test_crud_table';

    beforeAll(async () => {
      // 创建测试表
      await mysqlManager.query(`
        CREATE TABLE IF NOT EXISTS ${testTableName} (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          email VARCHAR(100) UNIQUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    });

    afterAll(async () => {
      // 清理测试表
      await mysqlManager.query(`DROP TABLE IF EXISTS ${testTableName}`);
    });

    beforeEach(async () => {
      // 清空测试数据
      await mysqlManager.query(`DELETE FROM ${testTableName}`);
    });

    test('应该能够插入数据', async () => {
      const insertData = {
        name: 'Test User',
        email: '<EMAIL>'
      };

      const result = await mysqlManager.insert(testTableName, insertData);
      expect(result.insertId).toBeGreaterThan(0);
      expect(result.affectedRows).toBe(1);
    });

    test('应该能够查询插入的数据', async () => {
      // 先插入数据
      await mysqlManager.insert(testTableName, {
        name: 'Query Test',
        email: '<EMAIL>'
      });

      // 查询数据
      const result = await mysqlManager.query(
        `SELECT * FROM ${testTableName} WHERE email = ?`,
        ['<EMAIL>']
      );

      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].name).toBe('Query Test');
      expect(result.rows[0].email).toBe('<EMAIL>');
    });

    test('应该能够更新数据', async () => {
      // 先插入数据
      const insertResult = await mysqlManager.insert(testTableName, {
        name: 'Update Test',
        email: '<EMAIL>'
      });

      // 更新数据
      const updateResult = await mysqlManager.update(
        testTableName,
        { name: 'Updated Name' },
        'id = ?',
        [insertResult.insertId]
      );

      expect(updateResult.affectedRows).toBe(1);

      // 验证更新结果
      const result = await mysqlManager.query(
        `SELECT * FROM ${testTableName} WHERE id = ?`,
        [insertResult.insertId]
      );

      expect(result.rows[0].name).toBe('Updated Name');
    });

    test('应该能够删除数据', async () => {
      // 先插入数据
      const insertResult = await mysqlManager.insert(testTableName, {
        name: 'Delete Test',
        email: '<EMAIL>'
      });

      // 删除数据
      const deleteResult = await mysqlManager.delete(
        testTableName,
        'id = ?',
        [insertResult.insertId]
      );

      expect(deleteResult.affectedRows).toBe(1);

      // 验证删除结果
      const result = await mysqlManager.query(
        `SELECT * FROM ${testTableName} WHERE id = ?`,
        [insertResult.insertId]
      );

      expect(result.rows).toHaveLength(0);
    });
  });

  describe('事务处理', () => {
    const testTableName = 'test_transaction_table';

    beforeAll(async () => {
      // 创建测试表
      await mysqlManager.query(`
        CREATE TABLE IF NOT EXISTS ${testTableName} (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          value INT DEFAULT 0
        )
      `);
    });

    afterAll(async () => {
      // 清理测试表
      await mysqlManager.query(`DROP TABLE IF EXISTS ${testTableName}`);
    });

    beforeEach(async () => {
      // 清空测试数据
      await mysqlManager.query(`DELETE FROM ${testTableName}`);
    });

    test('应该能够成功提交事务', async () => {
      const result = await mysqlManager.transaction(async (connection) => {
        // 插入两条记录
        await connection.execute(
          `INSERT INTO ${testTableName} (name, value) VALUES (?, ?)`,
          ['Transaction Test 1', 100]
        );
        await connection.execute(
          `INSERT INTO ${testTableName} (name, value) VALUES (?, ?)`,
          ['Transaction Test 2', 200]
        );
        return 'success';
      });

      expect(result).toBe('success');

      // 验证数据已提交
      const queryResult = await mysqlManager.query(
        `SELECT COUNT(*) as count FROM ${testTableName}`
      );
      expect(queryResult.rows[0].count).toBe(2);
    });

    test('应该能够回滚失败的事务', async () => {
      await expect(
        mysqlManager.transaction(async (connection) => {
          // 插入一条记录
          await connection.execute(
            `INSERT INTO ${testTableName} (name, value) VALUES (?, ?)`,
            ['Rollback Test', 300]
          );
          
          // 抛出错误触发回滚
          throw new Error('Transaction failed');
        })
      ).rejects.toThrow('Transaction failed');

      // 验证数据已回滚
      const queryResult = await mysqlManager.query(
        `SELECT COUNT(*) as count FROM ${testTableName}`
      );
      expect(queryResult.rows[0].count).toBe(0);
    });
  });

  describe('错误处理', () => {
    test('应该抛出DatabaseError当SQL语法错误时', async () => {
      await expect(
        mysqlManager.query('INVALID SQL SYNTAX')
      ).rejects.toThrow(DatabaseError);
    });

    test('应该抛出DatabaseError当表不存在时', async () => {
      await expect(
        mysqlManager.query('SELECT * FROM non_existent_table')
      ).rejects.toThrow(DatabaseError);
    });

    test('DatabaseError应该包含正确的错误类型', async () => {
      try {
        await mysqlManager.query('SELECT * FROM non_existent_table');
      } catch (error) {
        expect(error).toBeInstanceOf(DatabaseError);
        expect((error as DatabaseError).type).toBe(DatabaseErrorType.QUERY_ERROR);
      }
    });

    test('应该在未连接时抛出错误', async () => {
      const disconnectedManager = new MySQLManager(config);
      
      await expect(
        disconnectedManager.query('SELECT 1')
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('参数验证', () => {
    test('insert方法应该验证表名', async () => {
      await expect(
        mysqlManager.insert('', { name: 'test' })
      ).rejects.toThrow(DatabaseError);
    });

    test('insert方法应该验证插入数据', async () => {
      await expect(
        mysqlManager.insert('test_table', {})
      ).rejects.toThrow(DatabaseError);
    });

    test('update方法应该验证表名', async () => {
      await expect(
        mysqlManager.update('', { name: 'test' }, 'id = ?', [1])
      ).rejects.toThrow(DatabaseError);
    });

    test('update方法应该验证更新数据', async () => {
      await expect(
        mysqlManager.update('test_table', {}, 'id = ?', [1])
      ).rejects.toThrow(DatabaseError);
    });

    test('delete方法应该验证表名', async () => {
      await expect(
        mysqlManager.delete('', 'id = ?', [1])
      ).rejects.toThrow(DatabaseError);
    });

    test('delete方法应该验证删除条件', async () => {
      await expect(
        mysqlManager.delete('test_table', '')
      ).rejects.toThrow(DatabaseError);
    });
  });
});