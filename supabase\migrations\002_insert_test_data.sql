-- 插入测试客户数据
INSERT INTO customers (
  name, gender, phone, email, company, position, source, level, 
  region, address, tags, status, assigned_to, remark
) VALUES 
(
  '张三', 'male', '13800138001', 'z<PERSON><PERSON>@example.com', 
  '阿里巴巴集团', '技术总监', 'online', 'A', 
  '浙江省,杭州市', '杭州市西湖区文三路', 'VIP,技术', 
  'active', 'user_001', '重要客户，技术决策者'
),
(
  '李四', 'female', '13800138002', '<EMAIL>', 
  '腾讯科技', '产品经理', 'referral', 'B', 
  '广东省,深圳市', '深圳市南山区科技园', '产品,创新', 
  'active', 'user_002', '产品导向，关注用户体验'
),
(
  '王五', 'male', '13800138003', '<EMAIL>', 
  '字节跳动', '运营总监', 'phone', 'A', 
  '北京市,朝阳区', '北京市朝阳区望京SOHO', '运营,数据', 
  'potential', NULL, '数据驱动运营专家'
),
(
  '赵六', 'female', '13800138004', '<EMAIL>', 
  '美团', '市场总监', 'event', 'C', 
  '北京市,海淀区', '北京市海淀区中关村', '市场,推广', 
  'active', 'user_001', '市场营销经验丰富'
),
(
  '钱七', 'male', '13800138005', '<EMAIL>', 
  '滴滴出行', 'CTO', 'online', 'A', 
  '北京市,朝阳区', '北京市朝阳区酒仙桥', 'CTO,技术', 
  'potential', NULL, '技术架构专家，决策影响力大'
);

-- 插入角色数据
INSERT INTO roles (name, description, permissions) VALUES 
('超级管理员', '系统最高权限管理员', '["all"]'),
('销售经理', '销售团队管理者', '["customer:read", "customer:write", "customer:assign", "follow:read", "follow:write", "meeting:read", "meeting:write", "analytics:read"]'),
('销售专员', '一线销售人员', '["customer:read", "customer:write", "follow:read", "follow:write", "meeting:read", "meeting:write"]'),
('市场专员', '市场营销人员', '["customer:read", "marketing:read", "marketing:write", "analytics:read"]'),
('客服专员', '客户服务人员', '["customer:read", "follow:read", "follow:write"]');

-- 插入用户角色关联数据
INSERT INTO user_roles (user_id, role_id) VALUES 
('user_001', 1),  -- 超级管理员
('user_002', 2),  -- 销售经理
('user_003', 3),  -- 销售专员
('user_004', 4),  -- 市场专员
('user_005', 5);  -- 客服专员

-- 插入公海规则数据
INSERT INTO pool_rules (name, description, conditions, action, is_active) VALUES 
('长期未跟进规则', '客户超过30天未跟进自动进入公海', '{"days_without_follow": 30}', 'move_to_pool', true),
('无效客户规则', '标记为无效的客户自动进入公海', '{"status": "invalid"}', 'move_to_pool', true),
('重复客户规则', '重复的客户信息自动合并', '{"duplicate_phone": true}', 'merge_customer', true);

-- 插入营销活动数据
INSERT INTO marketing_campaigns (name, type, description, config, status, start_date, end_date) VALUES 
('新年抽奖活动', 'lottery', '2024年新年客户抽奖活动', '{"prizes": [{"name": "一等奖", "count": 1, "probability": 0.01}, {"name": "二等奖", "count": 10, "probability": 0.1}]}', 'active', '2024-01-01', '2024-01-31'),
('春季产品秒杀', 'flash_sale', '春季新品限时秒杀活动', '{"products": [{"id": 1, "name": "产品A", "original_price": 999, "sale_price": 699, "stock": 100}]}', 'draft', '2024-03-01', '2024-03-15'),
('客户转发有礼', 'referral', '老客户转发推荐新客户奖励活动', '{"reward_type": "cash", "reward_amount": 100, "max_referrals": 5}', 'active', '2024-01-15', '2024-12-31');

-- 插入会议数据
INSERT INTO meetings (title, description, start_time, end_time, location, attendees, customer_id, created_by, status) VALUES 
('产品演示会议', '向张三演示我们的核心产品功能', '2024-01-25 14:00:00', '2024-01-25 16:00:00', '公司会议室A', '["user_001", "user_002"]', 1, 'user_001', 'scheduled'),
('需求调研会议', '深入了解李四公司的具体需求', '2024-01-28 10:00:00', '2024-01-28 12:00:00', '客户公司', '["user_002"]', 2, 'user_002', 'scheduled'),
('合作洽谈会议', '与王五讨论合作方案细节', '2024-02-01 15:00:00', '2024-02-01 17:00:00', '咖啡厅', '["user_001"]', 3, 'user_001', 'scheduled');

-- 更新序列值（如果使用的是PostgreSQL）
SELECT setval('customers_id_seq', (SELECT MAX(id) FROM customers));
SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles));
SELECT setval('user_roles_id_seq', (SELECT MAX(id) FROM user_roles));
SELECT setval('pool_rules_id_seq', (SELECT MAX(id) FROM pool_rules));
SELECT setval('marketing_campaigns_id_seq', (SELECT MAX(id) FROM marketing_campaigns));
SELECT setval('meetings_id_seq', (SELECT MAX(id) FROM meetings));