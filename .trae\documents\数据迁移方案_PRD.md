# 硬编码数据迁移到Supabase方案 - 产品需求文档

## 1. 产品概述

本项目旨在将客户管理系统中所有硬编码的选项数据迁移到Supabase数据库中，并在系统设置模块中新增数据管理功能，实现对这些配置数据的动态增删改查操作。

通过此次迁移，系统将具备更好的可配置性和可维护性，管理员可以根据业务需求灵活调整各类选项数据，无需修改代码即可适应业务变化。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 权限说明 | 核心权限 |
|------|----------|----------|
| 系统管理员 | 完整的系统配置权限 | 可管理所有选项数据的增删改查 |
| 业务管理员 | 部分配置权限 | 可查看和修改业务相关选项数据 |
| 普通用户 | 只读权限 | 只能查看和使用选项数据 |

### 2.2 功能模块

本迁移方案涉及以下主要页面：

1. **系统设置页面**：新增选项数据管理模块，包含各类选项的配置界面
2. **客户管理页面**：修改为从数据库动态加载选项数据
3. **营销活动页面**：更新选项数据获取方式
4. **数据库迁移**：创建新表结构并迁移现有数据

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 系统设置页面 | 选项数据管理 | 提供客户来源、客户等级、装修类型等选项的增删改查功能 |
| 系统设置页面 | 数据分类管理 | 按业务类型对选项数据进行分类管理 |
| 客户管理页面 | 动态选项加载 | 从数据库实时获取最新的选项数据用于表单填写 |
| 客户管理页面 | 选项数据缓存 | 实现选项数据的前端缓存机制提升性能 |
| 营销活动页面 | 选项数据同步 | 确保营销相关选项与客户选项保持一致 |
| 数据库管理 | 表结构创建 | 创建option_categories和option_items表存储选项数据 |
| 数据库管理 | 数据迁移脚本 | 将现有硬编码数据批量导入到数据库表中 |

## 3. 核心流程

### 管理员配置流程
1. 管理员登录系统设置页面
2. 选择要管理的选项类别（如客户来源、客户等级等）
3. 进行选项的新增、编辑、删除操作
4. 系统实时更新数据库并刷新前端缓存

### 用户使用流程
1. 用户在客户管理等页面填写表单
2. 系统从数据库动态加载最新的选项数据
3. 用户选择相应选项完成表单填写
4. 数据保存时使用选项的value值存储

```mermaid
graph TD
    A[系统设置页面] --> B[选项数据管理]
    B --> C[客户来源管理]
    B --> D[客户等级管理]
    B --> E[装修类型管理]
    B --> F[其他选项管理]
    
    G[客户管理页面] --> H[动态加载选项]
    H --> I[表单填写]
    I --> J[数据保存]
    
    K[数据库层] --> L[option_categories表]
    K --> M[option_items表]
    
    C --> L
    D --> L
    E --> L
    F --> L
    
    H --> M
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：#1677ff（主蓝色），#f0f2f5（背景灰）
- **按钮样式**：圆角按钮，主要操作使用蓝色，次要操作使用灰色
- **字体**：系统默认字体，标题14px，正文12px
- **布局风格**：卡片式布局，左侧导航，右侧内容区域
- **图标风格**：使用Ionicons图标库，保持与现有系统一致

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 系统设置页面 | 选项管理导航 | 左侧树形导航，支持选项分类展开收起 |
| 系统设置页面 | 选项列表区域 | 表格展示选项数据，支持搜索、排序、分页 |
| 系统设置页面 | 选项编辑表单 | 模态框形式，包含名称、值、颜色、图标、描述等字段 |
| 客户管理页面 | 动态下拉选择器 | 保持原有样式，数据源改为API获取 |
| 客户管理页面 | 选项渲染组件 | 支持图标、颜色、描述的丰富展示效果 |

### 4.3 响应式设计

系统主要面向PC端使用，采用桌面优先的响应式设计，在平板设备上进行适配优化，确保管理功能的易用性。