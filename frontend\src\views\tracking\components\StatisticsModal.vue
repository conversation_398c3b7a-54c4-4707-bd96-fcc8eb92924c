<template>
  <n-modal v-model:show="showModal" preset="card" style="width: 1200px; max-height: 80vh">
    <template #header>
      <span>客户跟踪统计分析</span>
    </template>

    <!-- 时间范围选择 -->
    <div class="mb-6">
      <n-space align="center">
        <span>统计时间：</span>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          placeholder="选择时间范围"
          @update:value="handleDateRangeChange"
        />
        <n-button-group>
          <n-button size="small" @click="setQuickDateRange('week')">
            最近一周
          </n-button>
          <n-button size="small" @click="setQuickDateRange('month')">
            最近一月
          </n-button>
          <n-button size="small" @click="setQuickDateRange('quarter')">
            最近三月
          </n-button>
          <n-button size="small" @click="setQuickDateRange('year')">
            最近一年
          </n-button>
        </n-button-group>
        <n-button type="primary" @click="refreshData" :loading="loading">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
      </n-space>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <n-card>
        <n-statistic label="总跟踪记录" :value="statistics.totalRecords">
          <template #prefix>
            <n-icon color="#18a058">
              <DocumentTextOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-card>
      
      <n-card>
        <n-statistic label="跟进客户数" :value="statistics.totalCustomers">
          <template #prefix>
            <n-icon color="#2080f0">
              <PeopleOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-card>
      
      <n-card>
        <n-statistic label="平均跟进次数" :value="statistics.avgFollowUps" :precision="1">
          <template #prefix>
            <n-icon color="#f0a020">
              <TrendingUpOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-card>
      
      <n-card>
        <n-statistic label="转化率" :value="statistics.conversionRate" suffix="%" :precision="1">
          <template #prefix>
            <n-icon color="#d03050">
              <RadioButtonOnOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-card>
    </div>

    <!-- 图表区域 -->
    <n-tabs type="line" animated>
      <!-- 跟踪趋势 -->
      <n-tab-pane name="trend" tab="跟踪趋势">
        <div class="h-80">
          <div ref="trendChartRef" class="w-full h-full"></div>
        </div>
      </n-tab-pane>

      <!-- 跟踪类型分布 -->
      <n-tab-pane name="type" tab="类型分布">
        <div class="grid grid-cols-2 gap-6">
          <div class="h-80">
            <h3 class="text-center mb-4">跟踪类型分布</h3>
            <div ref="typeChartRef" class="w-full h-full"></div>
          </div>
          <div class="h-80">
            <h3 class="text-center mb-4">跟踪状态分布</h3>
            <div ref="statusChartRef" class="w-full h-full"></div>
          </div>
        </div>
      </n-tab-pane>

      <!-- 客户跟踪排行 -->
      <n-tab-pane name="ranking" tab="客户排行">
        <div class="grid grid-cols-2 gap-6">
          <!-- 跟踪次数排行 -->
          <n-card title="跟踪次数排行">
            <n-list>
              <n-list-item v-for="(item, index) in customerRanking.byCount" :key="item.customer_id">
                <template #prefix>
                  <n-tag :type="index < 3 ? 'warning' : 'default'" size="small">
                    {{ index + 1 }}
                  </n-tag>
                </template>
                <n-thing>
                  <template #header>
                    {{ item.customer_name }}
                  </template>
                  <template #description>
                    {{ item.phone }}
                  </template>
                </n-thing>
                <template #suffix>
                  <n-tag type="info">{{ item.count }}次</n-tag>
                </template>
              </n-list-item>
            </n-list>
          </n-card>

          <!-- 最近跟踪排行 -->
          <n-card title="最近跟踪排行">
            <n-list>
              <n-list-item v-for="(item, index) in customerRanking.byRecent" :key="item.customer_id">
                <template #prefix>
                  <n-tag :type="index < 3 ? 'success' : 'default'" size="small">
                    {{ index + 1 }}
                  </n-tag>
                </template>
                <n-thing>
                  <template #header>
                    {{ item.customer_name }}
                  </template>
                  <template #description>
                    {{ item.phone }}
                  </template>
                </n-thing>
                <template #suffix>
                  <n-tag type="success">
                    {{ formatRelativeTime(item.last_track_time) }}
                  </n-tag>
                </template>
              </n-list-item>
            </n-list>
          </n-card>
        </div>
      </n-tab-pane>

      <!-- 效果分析 -->
      <n-tab-pane name="analysis" tab="效果分析">
        <div class="space-y-6">
          <!-- 跟踪效果统计 -->
          <n-card title="跟踪效果统计">
            <div class="grid grid-cols-3 gap-4">
              <n-statistic label="成功跟进" :value="effectAnalysis.successful" suffix="次">
                <template #prefix>
                  <n-icon color="#18a058"><CheckmarkCircleOutline /></n-icon>
                </template>
              </n-statistic>
              <n-statistic label="无效跟进" :value="effectAnalysis.failed" suffix="次">
                <template #prefix>
                  <n-icon color="#d03050"><CloseCircleOutline /></n-icon>
                </template>
              </n-statistic>
              <n-statistic label="待跟进" :value="effectAnalysis.pending" suffix="次">
                <template #prefix>
                  <n-icon color="#f0a020"><TimeOutline /></n-icon>
                </template>
              </n-statistic>
            </div>
          </n-card>

          <!-- 跟踪周期分析 -->
          <n-card title="跟踪周期分析">
            <div class="h-64">
              <div ref="cycleChartRef" class="w-full h-full"></div>
            </div>
          </n-card>

          <!-- 跟踪质量评分 -->
          <n-card title="跟踪质量评分">
            <div class="grid grid-cols-2 gap-6">
              <div>
                <h4 class="mb-4">质量指标</h4>
                <n-space vertical>
                  <div class="flex justify-between items-center">
                    <span>及时性</span>
                    <n-progress type="line" :percentage="qualityScore.timeliness" />
                  </div>
                  <div class="flex justify-between items-center">
                    <span>完整性</span>
                    <n-progress type="line" :percentage="qualityScore.completeness" />
                  </div>
                  <div class="flex justify-between items-center">
                    <span>有效性</span>
                    <n-progress type="line" :percentage="qualityScore.effectiveness" />
                  </div>
                  <div class="flex justify-between items-center">
                    <span>持续性</span>
                    <n-progress type="line" :percentage="qualityScore.consistency" />
                  </div>
                </n-space>
              </div>
              <div>
                <h4 class="mb-4">综合评分</h4>
                <div class="text-center">
                  <n-progress
                    type="circle"
                    :percentage="qualityScore.overall"
                    :stroke-width="8"
                    :show-indicator="false"
                    style="width: 120px; height: 120px"
                  />
                  <div class="mt-2">
                    <div class="text-2xl font-bold">{{ qualityScore.overall }}分</div>
                    <div class="text-gray-500">{{ getScoreLevel(qualityScore.overall) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </div>
      </n-tab-pane>
    </n-tabs>

    <template #action>
      <n-space>
        <n-button @click="exportData" :loading="exporting">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出报表
        </n-button>
        <n-button @click="showModal = false">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import { useTrackingStore } from '@/stores/trackingStore'
import * as echarts from 'echarts'
import {
  RefreshOutline,
  DocumentTextOutline,
  PeopleOutline,
  TrendingUpOutline,
  RadioButtonOnOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  TimeOutline,
  DownloadOutline
} from '@vicons/ionicons5'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const trackingStore = useTrackingStore()

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const dateRange = ref<[number, number] | null>(null)

// 图表引用
const trendChartRef = ref<HTMLElement>()
const typeChartRef = ref<HTMLElement>()
const statusChartRef = ref<HTMLElement>()
const cycleChartRef = ref<HTMLElement>()

// 图表实例
let trendChart: echarts.ECharts | null = null
let typeChart: echarts.ECharts | null = null
let statusChart: echarts.ECharts | null = null
let cycleChart: echarts.ECharts | null = null

// 统计数据
const statistics = ref({
  totalRecords: 0,
  totalCustomers: 0,
  avgFollowUps: 0,
  conversionRate: 0
})

const customerRanking = ref({
  byCount: [] as Array<{
    customer_id: number
    customer_name: string
    phone: string
    count: number
  }>,
  byRecent: [] as Array<{
    customer_id: number
    customer_name: string
    phone: string
    last_track_time: string
  }>
})

const effectAnalysis = ref({
  successful: 0,
  failed: 0,
  pending: 0
})

const qualityScore = ref({
  timeliness: 85,
  completeness: 92,
  effectiveness: 78,
  consistency: 88,
  overall: 86
})

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 监听器
watch(() => props.show, (newVal) => {
  if (newVal) {
    initDateRange()
    loadStatistics()
    nextTick(() => {
      initCharts()
    })
  } else {
    destroyCharts()
  }
})

// 方法
const initDateRange = () => {
  const now = new Date()
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  dateRange.value = [oneMonthAgo.getTime(), now.getTime()]
}

const setQuickDateRange = (period: 'week' | 'month' | 'quarter' | 'year') => {
  const now = new Date()
  let startTime: Date
  
  switch (period) {
    case 'week':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case 'quarter':
      startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    case 'year':
      startTime = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
      break
    default:
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  }
  
  dateRange.value = [startTime.getTime(), now.getTime()]
  handleDateRangeChange()
}

const handleDateRangeChange = () => {
  loadStatistics()
}

const refreshData = () => {
  loadStatistics()
}

const loadStatistics = async () => {
  if (!dateRange.value) return
  
  loading.value = true
  try {
    const [startTime, endTime] = dateRange.value
    const params = {
      start_time: new Date(startTime).toISOString(),
      end_time: new Date(endTime).toISOString()
    }
    
    // 加载统计数据
    const [statsData, rankingData, effectData] = await Promise.all([
      trackingStore.getTrackingStatistics(params),
      trackingStore.getCustomerRanking(params),
      trackingStore.getEffectAnalysis(params)
    ])
    
    // 更新统计数据
    statistics.value.totalRecords = statsData.total_records || 0
    statistics.value.totalCustomers = statsData.completed_records || 0
    statistics.value.avgFollowUps = statsData.avg_response_time || 0
    statistics.value.conversionRate = statsData.completion_rate || 0
    
    // 适配客户排行数据
    customerRanking.value = {
      byCount: Array.isArray(rankingData) ? rankingData.map((item: any) => ({
        customer_id: item.user_id || item.customer_id || 0,
        customer_name: item.user_name || item.customer_name || '未知',
        phone: item.phone || '',
        count: item.total_records || item.count || 0
      })) : [],
      byRecent: Array.isArray(rankingData) ? rankingData.map((item: any) => ({
        customer_id: item.user_id || item.customer_id || 0,
        customer_name: item.user_name || item.customer_name || '未知',
        phone: item.phone || '',
        last_track_time: item.updated_at || item.last_track_time || new Date().toISOString()
      })) : []
    }
    
    // 适配效果分析数据
    effectAnalysis.value = {
      successful: Array.isArray(effectData) ? effectData.filter((item: any) => item.completion_rate > 0.8).length : 0,
      failed: Array.isArray(effectData) ? effectData.filter((item: any) => item.completion_rate < 0.3).length : 0,
      pending: Array.isArray(effectData) ? effectData.filter((item: any) => item.completion_rate >= 0.3 && item.completion_rate <= 0.8).length : 0
    }
    
    // 更新图表
    updateCharts()
  } catch (error) {
    console.error('Failed to load statistics:', error)
    message.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const initCharts = () => {
  nextTick(() => {
    initTrendChart()
    initTypeChart()
    initStatusChart()
    initCycleChart()
  })
}

const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  
  const option = {
    title: {
      text: '跟踪记录趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['跟踪记录', '新增客户'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: '跟踪记录数'
      },
      {
        type: 'value',
        name: '新增客户数'
      }
    ],
    series: [
      {
        name: '跟踪记录',
        type: 'line',
        data: [],
        smooth: true
      },
      {
        name: '新增客户',
        type: 'bar',
        yAxisIndex: 1,
        data: []
      }
    ]
  }
  
  trendChart.setOption(option)
}

const initTypeChart = () => {
  if (!typeChartRef.value) return
  
  typeChart = echarts.init(typeChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  typeChart.setOption(option)
}

const initStatusChart = () => {
  if (!statusChartRef.value) return
  
  statusChart = echarts.init(statusChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

const initCycleChart = () => {
  if (!cycleChartRef.value) return
  
  cycleChart = echarts.init(cycleChartRef.value)
  
  const option = {
    title: {
      text: '跟踪周期分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1-3天', '4-7天', '8-15天', '16-30天', '30天以上']
    },
    yAxis: {
      type: 'value',
      name: '客户数量'
    },
    series: [
      {
        type: 'bar',
        data: [120, 200, 150, 80, 70],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  cycleChart.setOption(option)
}

const updateCharts = () => {
  // 更新趋势图
  if (trendChart) {
    // 这里应该使用实际的统计数据
    const dates = generateDateRange()
    const trackingData = generateMockData(dates.length, 10, 50)
    const customerData = generateMockData(dates.length, 2, 15)
    
    trendChart.setOption({
      xAxis: {
        data: dates
      },
      series: [
        {
          data: trackingData
        },
        {
          data: customerData
        }
      ]
    })
  }
  
  // 更新类型分布图
  if (typeChart) {
    const typeData = [
      { value: 35, name: '电话跟进' },
      { value: 25, name: '微信跟进' },
      { value: 20, name: '上门拜访' },
      { value: 15, name: '邮件跟进' },
      { value: 5, name: '其他' }
    ]
    
    typeChart.setOption({
      series: [{
        data: typeData
      }]
    })
  }
  
  // 更新状态分布图
  if (statusChart) {
    const statusData = [
      { value: 40, name: '已完成' },
      { value: 30, name: '进行中' },
      { value: 20, name: '待跟进' },
      { value: 10, name: '已取消' }
    ]
    
    statusChart.setOption({
      series: [{
        data: statusData
      }]
    })
  }
}

const destroyCharts = () => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (typeChart) {
    typeChart.dispose()
    typeChart = null
  }
  if (statusChart) {
    statusChart.dispose()
    statusChart = null
  }
  if (cycleChart) {
    cycleChart.dispose()
    cycleChart = null
  }
}

const generateDateRange = () => {
  if (!dateRange.value) return []
  
  const [start, end] = dateRange.value
  const dates = []
  const current = new Date(start)
  const endDate = new Date(end)
  
  while (current <= endDate) {
    dates.push(current.toLocaleDateString())
    current.setDate(current.getDate() + 1)
  }
  
  return dates
}

const generateMockData = (length: number, min: number, max: number) => {
  return Array.from({ length }, () => Math.floor(Math.random() * (max - min + 1)) + min)
}

const formatRelativeTime = (time: string) => {
  const now = new Date()
  const targetTime = new Date(time)
  const diff = now.getTime() - targetTime.getTime()
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '及格'
  return '待改进'
}

const exportData = async () => {
  exporting.value = true
  try {
    // 这里应该调用实际的导出API
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('报表导出成功')
  } catch (error) {
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', () => {
    trendChart?.resize()
    typeChart?.resize()
    statusChart?.resize()
    cycleChart?.resize()
  })
})
</script>

<style scoped>
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.h-64 {
  height: 16rem;
}

.h-80 {
  height: 20rem;
}

.text-center {
  text-align: center;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray-500 {
  color: #6b7280;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}
</style>