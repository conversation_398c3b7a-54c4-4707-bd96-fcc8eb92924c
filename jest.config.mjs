export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>'],
  testMatch: [
    '**/api/tests/**/*.+(ts|tsx|js)',
    '**/api/**/*.(test|spec).+(ts|tsx|js)'
  ],
  transform: {
    '^.+\.(ts|tsx)$': ['ts-jest', {
      useESM: true
    }]
  },
  collectCoverageFrom: [
    'api/**/*.{ts,tsx}',
    '!api/**/*.d.ts',
    '!api/tests/**',
    '!api/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  setupFilesAfterEnv: ['<rootDir>/api/tests/setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/api/$1'
  },
  testTimeout: 10000,
  verbose: true,
  extensionsToTreatAsEsm: ['.ts']
};