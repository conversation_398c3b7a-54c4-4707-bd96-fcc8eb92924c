<template>
  <n-modal v-model:show="visible" preset="dialog" title="客户跟进" style="width: 800px;">
    <template #header>
      <div class="flex items-center gap-3">
        <n-icon size="20" color="#1677ff">
          <PersonOutline />
        </n-icon>
        <span>客户跟进</span>
      </div>
    </template>

    <div class="space-y-6">
      <!-- 客户信息 -->
      <div v-if="customerData" class="bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-medium text-gray-900">客户信息</h3>
          <n-tag :type="getStatusType(customerData.status)" size="small">
            {{ getStatusLabel(customerData.status) }}
          </n-tag>
        </div>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-500">客户姓名：</span>
            <span class="font-medium">{{ customerData.name }}</span>
          </div>
          <div>
            <span class="text-gray-500">联系电话：</span>
            <span class="font-medium">{{ customerData.phone }}</span>
          </div>
          <div>
            <span class="text-gray-500">微信号：</span>
            <span class="font-medium">{{ customerData.wechat || '-' }}</span>
          </div>
          <div>
            <span class="text-gray-500">跟进次数：</span>
            <span class="font-medium">{{ customerData.visitCount || 0 }}次</span>
          </div>
        </div>
      </div>

      <!-- 跟进表单 -->
      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="top">
        <!-- 基础信息 -->
        <div class="grid grid-cols-2 gap-4">
          <n-form-item label="跟进时间" path="follow_time">
            <n-date-picker
              v-model:value="followTimeValue"
              type="datetime"
              placeholder="选择跟进时间"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts > Date.now()"
            />
          </n-form-item>
          <n-form-item label="跟进方式" path="type">
            <n-select
              v-model:value="formData.type"
              :options="followMethodOptions"
              :render-label="renderMethodLabel"
              placeholder="选择跟进方式"
            />
          </n-form-item>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <n-form-item label="客户反馈" path="feedback">
            <n-select
              v-model:value="formData.feedback"
              :options="feedbackOptions"
              :render-label="renderFeedbackLabel"
              placeholder="选择客户反馈"
            />
          </n-form-item>
          <n-form-item label="跟进状态" path="status">
            <n-select
              v-model:value="formData.status"
              :options="statusOptions"
              :render-label="renderStatusLabel"
              placeholder="选择跟进状态"
            />
          </n-form-item>
        </div>

        <!-- 跟进内容 -->
        <n-form-item label="跟进内容" path="content">
          <n-input
            v-model:value="formData.content"
            type="textarea"
            placeholder="请详细描述本次跟进的具体内容、客户反馈、遇到的问题等..."
            :rows="4"
            show-count
            maxlength="500"
          />
        </n-form-item>

        <!-- 后续安排 -->
        <div class="grid grid-cols-2 gap-4">
          <n-form-item label="下次跟进时间">
            <n-date-picker
              v-model:value="nextFollowTimeValue"
              type="datetime"
              placeholder="选择下次跟进时间"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts < Date.now()"
            />
          </n-form-item>
          <n-form-item label="备注">
            <n-input
              v-model:value="formData.remark"
              placeholder="其他备注信息"
              maxlength="200"
            />
          </n-form-item>
        </div>

        <!-- 快速操作 -->
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center gap-2 mb-3">
            <n-icon size="16" color="#1677ff">
              <FlashOutline />
            </n-icon>
            <span class="text-sm font-medium text-gray-700">快速填充模板</span>
          </div>
          <div class="flex flex-wrap gap-2">
            <n-button size="small" @click="fillQuickTemplate('interested')">
              客户感兴趣
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('considering')">
              客户考虑中
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('price_sensitive')">
              价格敏感
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('no_contact')">
              无法联系
            </n-button>
          </div>
        </div>
      </n-form>
    </div>

    <template #action>
      <div class="flex justify-end gap-3">
        <n-button @click="visible = false">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ editData ? '更新' : '保存' }}跟进记录
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, h, type VNodeChild } from 'vue'
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NButton,
  NTag,
  NIcon,
  type FormInst,
  type FormRules,
  type SelectRenderLabel
} from 'naive-ui'
import {
  PersonOutline,
  PhonePortraitOutline,
  ChatbubbleOutline,
  MailOutline,
  HomeOutline,
  StorefrontOutline,
  RemoveCircleOutline,
  HappyOutline,
  SadOutline,
  PauseCircleOutline,
  CloseCircleOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  FlashOutline
} from '@vicons/ionicons5'

// Props
interface CustomerData {
  id: number
  name: string
  phone: string
  wechat?: string
  status: string
  visitCount?: number
}

interface FollowRecord {
  id?: number
  customer_id: number
  follow_time: string
  type: string
  content: string
  feedback: string
  next_follow_time: string
  status: string
  remark: string
}

interface Props {
  show: boolean
  customerData?: CustomerData
  editData?: FollowRecord
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  loading: false
})

// Emits
interface Emits {
  'update:show': [value: boolean]
  'submit': [data: FollowRecord]
}

const emit = defineEmits<Emits>()

// Refs
const formRef = ref<FormInst>()

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value: boolean) => emit('update:show', value)
})

const formData = reactive<FollowRecord>({
  customer_id: 0,
  follow_time: '',
  type: 'phone',
  content: '',
  feedback: '',
  next_follow_time: '',
  status: 'pending',
  remark: ''
})

// 跟进时间的计算属性
const followTimeValue = computed({
  get: () => {
    if (!formData.follow_time) return null
    const date = new Date(formData.follow_time)
    return isNaN(date.getTime()) ? null : date.getTime()
  },
  set: (value: number | null) => {
    formData.follow_time = value ? new Date(value).toISOString() : ''
  }
})

// 下次跟进时间的计算属性
const nextFollowTimeValue = computed({
  get: () => {
    if (!formData.next_follow_time) return null
    const date = new Date(formData.next_follow_time)
    return isNaN(date.getTime()) ? null : date.getTime()
  },
  set: (value: number | null) => {
    formData.next_follow_time = value ? new Date(value).toISOString() : ''
  }
})

// 选项数据
const followMethodOptions = [
  { label: '电话沟通', value: 'phone', icon: PhonePortraitOutline, color: '#1677ff' },
  { label: '微信沟通', value: 'wechat', icon: ChatbubbleOutline, color: '#52c41a' },
  { label: '短信联系', value: 'sms', icon: MailOutline, color: '#faad14' },
  { label: '上门拜访', value: 'visit', icon: HomeOutline, color: '#722ed1' },
  { label: '邀请到店', value: 'store', icon: StorefrontOutline, color: '#eb2f96' },
  { label: '其他方式', value: 'other', icon: RemoveCircleOutline, color: '#8c8c8c' }
]

const feedbackOptions = [
  { label: '非常感兴趣', value: 'very_interested', icon: HappyOutline, color: '#52c41a', type: 'success' },
  { label: '比较感兴趣', value: 'interested', icon: HappyOutline, color: '#1677ff', type: 'info' },
  { label: '一般兴趣', value: 'neutral', icon: RemoveCircleOutline, color: '#faad14', type: 'warning' },
  { label: '暂无兴趣', value: 'not_interested', icon: SadOutline, color: '#ff4d4f', type: 'error' },
  { label: '需要考虑', value: 'considering', icon: PauseCircleOutline, color: '#722ed1', type: 'default' },
  { label: '价格敏感', value: 'price_sensitive', icon: RemoveCircleOutline, color: '#fa8c16', type: 'warning' },
  { label: '无法联系', value: 'no_contact', icon: CloseCircleOutline, color: '#8c8c8c', type: 'default' }
]

const statusOptions = [
  { label: '跟进中', value: 'pending', icon: PauseCircleOutline, color: '#1677ff', type: 'info' },
  { label: '待跟进', value: 'waiting', icon: TimeOutline, color: '#faad14', type: 'warning' },
  { label: '已完成', value: 'completed', icon: CheckmarkCircleOutline, color: '#52c41a', type: 'success' },
  { label: '已转化', value: 'converted', icon: CheckmarkCircleOutline, color: '#722ed1', type: 'success' },
  { label: '已放弃', value: 'abandoned', icon: CloseCircleOutline, color: '#ff4d4f', type: 'error' }
]

// 渲染标签函数
const renderMethodLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = followMethodOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label)
  ])
}

const renderFeedbackLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = feedbackOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label)
  ])
}

const renderStatusLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = statusOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label)
  ])
}

// 获取状态类型和标签
const getStatusType = (status: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const statusMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    'initial': 'info',
    'following': 'warning',
    'appointed': 'info',
    'met': 'success',
    'signed': 'success',
    'lost': 'error'
  }
  return statusMap[status] || 'default'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'initial': '初次接触',
    'following': '跟进中',
    'appointed': '已预约',
    'met': '已见面',
    'signed': '已签约',
    'lost': '已流失'
  }
  return statusMap[status] || status
}

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const templates = {
    interested: {
      feedback: 'very_interested',
      content: '客户对我们的产品/服务表现出浓厚兴趣，询问了详细的价格和服务内容。客户比较关注性价比和服务质量，希望能够提供更详细的方案。',
      status: 'pending'
    },
    considering: {
      feedback: 'considering',
      content: '客户表示需要时间考虑，主要顾虑是预算和时间安排。已向客户详细介绍了我们的优势和服务流程，客户表示会认真考虑。',
      status: 'waiting'
    },
    price_sensitive: {
      feedback: 'price_sensitive',
      content: '客户对价格比较敏感，希望能够获得更优惠的价格。已向客户介绍了不同的套餐选择，建议客户考虑性价比更高的方案。',
      status: 'pending'
    },
    no_contact: {
      feedback: 'no_contact',
      content: '多次尝试联系客户，电话无人接听，微信消息未回复。可能客户比较忙碌或者联系方式有变化。',
      status: 'waiting'
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
    // 设置下次跟进时间
    const days = type === 'considering' ? 7 : type === 'no_contact' ? 5 : 3
    nextFollowTimeValue.value = Date.now() + days * 24 * 60 * 60 * 1000
  }
}

// 表单验证规则
const rules: FormRules = {
  follow_time: {
    required: true,
    type: 'string',
    message: '请选择跟进时间',
    trigger: ['blur', 'change']
  },
  type: {
    required: true,
    message: '请选择跟进方式',
    trigger: ['blur', 'change']
  },
  content: {
    required: true,
    message: '请输入跟进内容',
    trigger: ['blur', 'input'],
    min: 10,
    max: 500
  },
  status: {
    required: true,
    message: '请选择跟进状态',
    trigger: ['blur', 'change']
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.editData) {
    Object.assign(formData, {
      ...props.editData,
      feedback: props.editData.remark || ''
    })
  } else {
    Object.assign(formData, {
      customer_id: props.customerData?.id || 0,
      follow_time: new Date().toISOString(),
      type: 'phone',
      content: '',
      feedback: '',
      next_follow_time: '',
      status: 'pending',
      remark: ''
    })
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 监听显示状态变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    initFormData()
  }
})

// 监听编辑数据变化
watch(() => props.editData, () => {
  if (props.show) {
    initFormData()
  }
})
</script>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.p-4 {
  padding: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-500 {
  color: #6b7280;
}
</style>