<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    :title="`权限管理 - ${role?.name}`"
    :style="{ width: '700px' }"
    :mask-closable="false"
  >
    <div class="permission-modal">
      <!-- 角色信息 -->
      <n-card class="role-info" size="small">
        <n-space align="center">
          <n-tag :type="role?.is_system ? 'warning' : 'default'">
            {{ role?.is_system ? '系统角色' : '自定义角色' }}
          </n-tag>
          <span class="role-name">{{ role?.name }}</span>
          <n-tag :type="role?.status ? 'success' : 'error'" size="small">
            {{ role?.status ? '启用' : '禁用' }}
          </n-tag>
        </n-space>
        <p class="role-description">{{ role?.description || '暂无描述' }}</p>
      </n-card>

      <!-- 权限配置 -->
      <div class="permission-config">
        <div class="config-header">
          <n-space justify="space-between" align="center">
            <h4>权限配置</h4>
            <n-space>
              <n-button size="small" @click="handleSelectAll">
                全选
              </n-button>
              <n-button size="small" @click="handleSelectNone">
                全不选
              </n-button>
            </n-space>
          </n-space>
        </div>
        
        <div class="permission-list">
          <n-space vertical size="large">
            <div v-for="(permissions, module) in permissionsByModule" :key="module" class="permission-module">
              <div class="module-header">
                <n-checkbox
                  :checked="isModuleAllSelected(module)"
                  :indeterminate="isModuleIndeterminate(module)"
                  @update:checked="handleModuleSelect(module, $event)"
                  :disabled="role?.is_system"
                >
                  <span class="module-name">{{ getModuleName(module) }}</span>
                  <n-tag size="small" class="permission-count">
                    {{ getSelectedCount(module) }}/{{ permissions.length }}
                  </n-tag>
                </n-checkbox>
              </div>
              
              <div class="module-permissions">
                <n-checkbox-group v-model:value="selectedPermissions">
                  <div class="permission-grid">
                    <n-checkbox
                      v-for="permission in permissions"
                      :key="permission.id"
                      :value="permission.id"
                      :disabled="role?.is_system"
                      class="permission-item"
                    >
                      <div class="permission-content">
                        <span class="permission-name">{{ permission.display_name || permission.name }}</span>
                        <span class="permission-code">{{ permission.name }}</span>
                        <p class="permission-description">{{ permission.description }}</p>
                      </div>
                    </n-checkbox>
                  </div>
                </n-checkbox-group>
              </div>
            </div>
          </n-space>
        </div>
      </div>
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button 
          type="primary" 
          :loading="loading" 
          @click="handleSubmit"
          :disabled="role?.is_system"
        >
          保存权限
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import {
  NModal,
  NCard,
  NSpace,
  NTag,
  NButton,
  NCheckbox,
  NCheckboxGroup,
  useMessage
} from 'naive-ui'
import { useRoleStore } from '@/stores/modules/role'
import type { RoleWithPermissions } from '@/api/roleService'

interface Props {
  show: boolean
  role?: RoleWithPermissions | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

const emit = defineEmits<Emits>()

const message = useMessage()
const roleStore = useRoleStore()
const loading = ref(false)
const selectedPermissions = ref<number[]>([])

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 权限按模块分组
const permissionsByModule = computed(() => roleStore.permissionsByModule)

// 模块名称映射
const moduleNameMap: Record<string, string> = {
  customer: '客户管理',
  role: '角色管理',
  user: '用户管理',
  pool: '公海管理',
  marketing: '营销活动',
  meeting: '会议管理',
  analytics: '数据分析',
  system: '系统设置'
}

const getModuleName = (module: string) => {
  return moduleNameMap[module] || module
}

// 获取模块选中数量
const getSelectedCount = (module: string) => {
  const permissions = permissionsByModule.value[module] || []
  return permissions.filter(p => selectedPermissions.value.includes(p.id)).length
}

// 检查模块是否全选
const isModuleAllSelected = (module: string) => {
  const permissions = permissionsByModule.value[module] || []
  return permissions.length > 0 && permissions.every(p => selectedPermissions.value.includes(p.id))
}

// 检查模块是否部分选中
const isModuleIndeterminate = (module: string) => {
  const permissions = permissionsByModule.value[module] || []
  const selectedCount = permissions.filter(p => selectedPermissions.value.includes(p.id)).length
  return selectedCount > 0 && selectedCount < permissions.length
}

// 处理模块选择
const handleModuleSelect = (module: string, checked: boolean) => {
  const permissions = permissionsByModule.value[module] || []
  const permissionIds = permissions.map(p => p.id)
  
  if (checked) {
    // 添加该模块的所有权限
    permissionIds.forEach(id => {
      if (!selectedPermissions.value.includes(id)) {
        selectedPermissions.value.push(id)
      }
    })
  } else {
    // 移除该模块的所有权限
    selectedPermissions.value = selectedPermissions.value.filter(
      id => !permissionIds.includes(id)
    )
  }
}

// 全选
const handleSelectAll = () => {
  selectedPermissions.value = roleStore.permissions.map(p => p.id)
}

// 全不选
const handleSelectNone = () => {
  selectedPermissions.value = []
}

// 初始化权限数据
const initPermissions = () => {
  if (props.role) {
    selectedPermissions.value = props.role.permissions?.map(p => p.id) || []
  } else {
    selectedPermissions.value = []
  }
}

// 提交权限配置
const handleSubmit = async () => {
  if (!props.role) return
  
  try {
    loading.value = true
    
    await roleStore.updateRole(props.role.id, {
      name: props.role.name,
      display_name: props.role.display_name,
      description: props.role.description || undefined,
      status: props.role.status,
      permission_ids: selectedPermissions.value
    })
    
    message.success('权限配置保存成功')
    emit('success')
  } catch (error) {
    console.error('保存权限配置失败:', error)
    message.error('保存权限配置失败')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (show) => {
    if (show) {
      nextTick(() => {
        initPermissions()
      })
    }
  }
)
</script>

<style scoped>
.permission-modal {
  max-height: 600px;
  overflow-y: auto;
}

.role-info {
  margin-bottom: 20px;
}

.role-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.role-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.permission-config {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.config-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e6;
}

.config-header h4 {
  margin: 0;
  color: #333;
}

.permission-list {
  max-height: 400px;
  overflow-y: auto;
}

.permission-module {
  background: #fff;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e0e0e6;
}

.module-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.module-name {
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.permission-count {
  background: #f0f0f0;
  color: #666;
}

.module-permissions {
  margin-left: 24px;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.permission-item {
  border: 1px solid #e0e0e6;
  border-radius: 4px;
  padding: 8px;
  transition: all 0.2s;
}

.permission-item:hover {
  border-color: #2080f0;
  background: #f6f9ff;
}

.permission-content {
  margin-left: 8px;
}

.permission-name {
  font-weight: 500;
  color: #333;
  display: block;
}

.permission-code {
  font-size: 12px;
  color: #999;
  font-family: monospace;
  display: block;
  margin-top: 2px;
}

.permission-description {
  font-size: 12px;
  color: #666;
  margin: 4px 0 0 0;
  line-height: 1.4;
}
</style>