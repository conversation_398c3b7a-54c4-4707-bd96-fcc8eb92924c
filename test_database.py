#!/usr/bin/env python3
"""数据库连接和基础模型操作测试脚本

测试数据库连接、表创建、基础CRUD操作等功能
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_database_connection():
    """测试数据库连接"""
    try:
        from app.database import test_database_connection, engine
        
        logger.info("开始测试数据库连接...")
        
        # 测试连接
        is_connected = await test_database_connection()
        
        if is_connected:
            logger.info("✅ 数据库连接成功")
            return True
        else:
            logger.error("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据库连接测试异常: {e}")
        return False


async def test_table_creation():
    """测试表创建"""
    try:
        from app.database import create_tables, drop_tables
        
        logger.info("开始测试表创建...")
        
        # 先删除所有表（如果存在）
        await drop_tables()
        logger.info("已删除现有表")
        
        # 创建所有表
        await create_tables()
        logger.info("✅ 表创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 表创建测试异常: {e}")
        return False


async def test_database_manager():
    """测试数据库管理器"""
    try:
        from app.services.database_manager import db_manager
        
        logger.info("开始测试数据库管理器...")
        
        # 测试健康检查
        health_status = await db_manager.health_check()
        logger.info(f"数据库健康状态: {health_status}")
        
        # 测试获取会话
        async with db_manager.get_session() as session:
            logger.info("✅ 数据库会话获取成功")
            
            # 测试原生SQL执行
            result = await db_manager.execute_raw_sql(
                session, 
                "SELECT 1 as test_value"
            )
            logger.info(f"原生SQL执行结果: {result}")
        
        logger.info("✅ 数据库管理器测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库管理器测试异常: {e}")
        return False


async def test_basic_crud_operations():
    """测试基础CRUD操作"""
    try:
        from app.database import get_db
        from app.services.crud import department_crud, user_crud
        from app.models.department import Department
        from app.models.user import User
        from app.utils import generate_uuid
        
        logger.info("开始测试基础CRUD操作...")
        
        # 获取数据库会话
        async for db in get_db():
            # 测试部门CRUD
            logger.info("测试部门CRUD操作...")
            
            # 创建测试部门
            department_data = {
                "id": generate_uuid(),
                "name": "测试部门",
                "code": "TEST_DEPT",
                "description": "这是一个测试部门",
                "level": 1,
                "sort_order": 1,
                "is_active": True
            }
            
            created_dept = await department_crud.create(db, obj_in=department_data)
            logger.info(f"✅ 部门创建成功: {created_dept.name} (ID: {created_dept.id})")
            
            # 查询部门
            found_dept = await department_crud.get(db, id=created_dept.id)
            if found_dept:
                logger.info(f"✅ 部门查询成功: {found_dept.name}")
            else:
                logger.error("❌ 部门查询失败")
                return False
            
            # 更新部门
            update_data = {"description": "更新后的测试部门描述"}
            updated_dept = await department_crud.update(db, db_obj=found_dept, obj_in=update_data)
            logger.info(f"✅ 部门更新成功: {updated_dept.description}")
            
            # 测试用户CRUD
            logger.info("测试用户CRUD操作...")
            
            # 创建测试用户
            user_data = {
                "id": generate_uuid(),
                "username": "testuser",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "password_hash": "hashed_password",
                "real_name": "测试用户",
                "department_id": created_dept.id,
                "is_active": True
            }
            
            created_user = await user_crud.create(db, obj_in=user_data)
            logger.info(f"✅ 用户创建成功: {created_user.username} (ID: {created_user.id})")
            
            # 查询用户
            found_user = await user_crud.get(db, id=created_user.id)
            if found_user:
                logger.info(f"✅ 用户查询成功: {found_user.username}")
            else:
                logger.error("❌ 用户查询失败")
                return False
            
            # 测试按用户名查询
            user_by_username = await user_crud.get_by_username(db, username="testuser")
            if user_by_username:
                logger.info(f"✅ 按用户名查询成功: {user_by_username.username}")
            else:
                logger.error("❌ 按用户名查询失败")
                return False
            
            # 测试分页查询
            users_page = await user_crud.get_multi(db, skip=0, limit=10)
            logger.info(f"✅ 分页查询成功，共 {len(users_page)} 条用户记录")
            
            # 清理测试数据
            await user_crud.remove(db, id=created_user.id)
            await department_crud.remove(db, id=created_dept.id)
            logger.info("✅ 测试数据清理完成")
            
            break  # 退出async for循环
        
        logger.info("✅ 基础CRUD操作测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 基础CRUD操作测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_transaction_handling():
    """测试事务处理"""
    try:
        from app.database import get_db
        from app.services.crud import department_crud
        from app.services.database_manager import db_manager, transactional
        from app.utils import generate_uuid
        
        logger.info("开始测试事务处理...")
        
        @transactional
        async def create_departments_with_rollback(db):
            """创建部门但会回滚的事务测试"""
            # 创建第一个部门
            dept1_data = {
                "id": generate_uuid(),
                "name": "事务测试部门1",
                "code": "TRANS_TEST_1",
                "level": 1,
                "sort_order": 1,
                "is_active": True
            }
            dept1 = await department_crud.create(db, obj_in=dept1_data)
            logger.info(f"创建部门1: {dept1.name}")
            
            # 创建第二个部门
            dept2_data = {
                "id": generate_uuid(),
                "name": "事务测试部门2",
                "code": "TRANS_TEST_2",
                "level": 1,
                "sort_order": 2,
                "is_active": True
            }
            dept2 = await department_crud.create(db, obj_in=dept2_data)
            logger.info(f"创建部门2: {dept2.name}")
            
            # 故意抛出异常以触发回滚
            raise Exception("故意触发事务回滚")
        
        # 获取数据库会话
        async for db in get_db():
            try:
                await create_departments_with_rollback(db)
            except Exception as e:
                logger.info(f"预期的异常: {e}")
            
            # 检查部门是否被回滚
            dept1 = await department_crud.get_by_code(db, code="TRANS_TEST_1")
            dept2 = await department_crud.get_by_code(db, code="TRANS_TEST_2")
            
            if dept1 is None and dept2 is None:
                logger.info("✅ 事务回滚成功，部门未被创建")
            else:
                logger.error("❌ 事务回滚失败，部门仍然存在")
                return False
            
            break  # 退出async for循环
        
        logger.info("✅ 事务处理测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 事务处理测试异常: {e}")
        return False


async def test_utility_functions():
    """测试工具函数"""
    try:
        from app.utils import generate_uuid, format_phone, validate_email
        
        logger.info("开始测试工具函数...")
        
        # 测试UUID生成
        uuid1 = generate_uuid()
        uuid2 = generate_uuid()
        if uuid1 != uuid2 and len(uuid1) == 36:
            logger.info(f"✅ UUID生成成功: {uuid1}")
        else:
            logger.error("❌ UUID生成失败")
            return False
        
        # 测试手机号格式化
        formatted_phone = format_phone("13800138000")
        if formatted_phone == "138-0013-8000":
            logger.info(f"✅ 手机号格式化成功: {formatted_phone}")
        else:
            logger.error(f"❌ 手机号格式化失败: {formatted_phone}")
            return False
        
        # 测试邮箱验证
        valid_email = validate_email("<EMAIL>")
        invalid_email = validate_email("invalid-email")
        if valid_email and not invalid_email:
            logger.info("✅ 邮箱验证功能正常")
        else:
            logger.error("❌ 邮箱验证功能异常")
            return False
        
        logger.info("✅ 工具函数测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 工具函数测试异常: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    logger.info("="*50)
    logger.info("开始数据库层完整测试")
    logger.info("="*50)
    
    test_results = []
    
    # 1. 测试数据库连接
    result = await test_database_connection()
    test_results.append(("数据库连接", result))
    
    if not result:
        logger.error("数据库连接失败，跳过后续测试")
        return False
    
    # 2. 测试表创建
    result = await test_table_creation()
    test_results.append(("表创建", result))
    
    if not result:
        logger.error("表创建失败，跳过后续测试")
        return False
    
    # 3. 测试数据库管理器
    result = await test_database_manager()
    test_results.append(("数据库管理器", result))
    
    # 4. 测试基础CRUD操作
    result = await test_basic_crud_operations()
    test_results.append(("基础CRUD操作", result))
    
    # 5. 测试事务处理
    result = await test_transaction_handling()
    test_results.append(("事务处理", result))
    
    # 6. 测试工具函数
    result = await test_utility_functions()
    test_results.append(("工具函数", result))
    
    # 输出测试结果
    logger.info("="*50)
    logger.info("测试结果汇总")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    if all_passed:
        logger.info("🎉 所有测试通过！数据库层实现完成")
    else:
        logger.error("💥 部分测试失败，请检查相关问题")
    
    return all_passed


if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_all_tests())