# 硬编码数据迁移到Supabase - 实施计划文档

## 1. 项目概述

本文档详细规划了将客户管理系统中所有硬编码选项数据迁移到Supabase数据库的完整实施方案，包括数据库迁移、前端代码重构、API接口开发和系统设置功能实现。

## 2. 实施阶段规划

### 阶段一：数据库设计与迁移 (1-2天)

**目标**：创建数据库表结构并迁移现有数据

**任务清单**：

1. 创建数据库迁移脚本
2. 设计option\_categories和option\_items表结构
3. 编写数据迁移SQL脚本
4. 执行数据库迁移并验证数据完整性

**交付物**：

* `003_create_option_tables.sql` - 表结构创建脚本

* `004_migrate_option_data.sql` - 数据迁移脚本

* 数据验证报告

### 阶段二：API服务层开发 (2-3天)

**目标**：开发选项数据的CRUD API接口

**任务清单**：

1. 创建选项数据类型定义
2. 开发Supabase客户端服务
3. 实现选项数据的增删改查API
4. 添加数据缓存机制
5. 编写API单元测试

**交付物**：

* `src/types/options.ts` - 类型定义文件

* `src/api/optionService.ts` - API服务文件

* `src/stores/optionStore.ts` - Pinia状态管理

* API测试用例

### 阶段三：前端组件重构 (3-4天)

**目标**：重构现有组件使用动态选项数据

**任务清单**：

1. 重构BasicInfoForm组件
2. 更新客户列表和详情页面
3. 修改营销活动相关页面
4. 实现选项数据的前端缓存
5. 添加加载状态和错误处理

**交付物**：

* 重构后的Vue组件文件

* 选项数据渲染组件

* 错误处理机制

### 阶段四：系统设置功能开发 (4-5天)

**目标**：开发选项数据管理界面

**任务清单**：

1. 创建系统设置路由和页面
2. 开发选项分类管理界面
3. 实现选项数据的增删改查功能
4. 添加数据验证和权限控制
5. 实现批量操作功能

**交付物**：

* `src/views/settings/OptionManagement.vue` - 主管理页面

* `src/views/settings/components/` - 相关组件

* 权限控制逻辑

### 阶段五：测试与优化 (2-3天)

**目标**：全面测试和性能优化

**任务清单**：

1. 编写端到端测试用例
2. 性能测试和优化
3. 用户体验测试
4. 数据一致性验证
5. 文档更新和部署准备

**交付物**：

* E2E测试用例

* 性能优化报告

* 用户使用文档

## 3. 详细实施步骤

### 3.1 数据库迁移步骤

**步骤1：创建表结构**

```bash
# 在supabase/migrations/目录下创建迁移文件
supabase migration new create_option_tables
```

**步骤2：执行迁移**

```bash
# 应用数据库迁移
supabase db push
```

**步骤3：验证数据**

```sql
-- 验证表创建成功
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('option_categories', 'option_items');

-- 验证数据迁移成功
SELECT 
    oc.name as category_name,
    COUNT(oi.id) as item_count
FROM option_categories oc
LEFT JOIN option_items oi ON oc.id = oi.category_id
GROUP BY oc.id, oc.name
ORDER BY oc.sort_order;
```

### 3.2 API服务开发步骤

**步骤1：创建类型定义**

```typescript
// src/types/options.ts
export interface OptionCategory {
  id: string
  name: string
  code: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface OptionItem {
  id: string
  category_id: string
  label: string
  value: string
  color?: string
  icon?: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}
```

**步骤2：实现API服务**

```typescript
// src/api/optionService.ts
import { supabase } from '@/lib/supabase'
import type { OptionCategory, OptionItem } from '@/types/options'

export class OptionService {
  // 获取所有分类
  static async getCategories(): Promise<OptionCategory[]> {
    const { data, error } = await supabase
      .from('option_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order')
    
    if (error) throw error
    return data || []
  }
  
  // 获取指定分类的选项
  static async getItemsByCategory(categoryCode: string): Promise<OptionItem[]> {
    const { data, error } = await supabase
      .from('option_items')
      .select(`
        *,
        category:option_categories!inner(code)
      `)
      .eq('category.code', categoryCode)
      .eq('is_active', true)
      .order('sort_order')
    
    if (error) throw error
    return data || []
  }
}
```

### 3.3 前端组件重构步骤

**步骤1：创建选项数据Hook**

```typescript
// src/composables/useOptions.ts
import { ref, computed } from 'vue'
import { useOptionStore } from '@/stores/optionStore'
import type { OptionItem } from '@/types/options'

export function useOptions(categoryCode: string) {
  const optionStore = useOptionStore()
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const options = computed(() => 
    optionStore.getOptionsByCategory(categoryCode)
  )
  
  const loadOptions = async () => {
    try {
      loading.value = true
      error.value = null
      await optionStore.loadOptionsByCategory(categoryCode)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载选项数据失败'
    } finally {
      loading.value = false
    }
  }
  
  return {
    options,
    loading,
    error,
    loadOptions
  }
}
```

**步骤2：重构BasicInfoForm组件**

```vue
<!-- 修改src/views/customer/components/BasicInfoForm.vue -->
<script setup lang="ts">
import { useOptions } from '@/composables/useOptions'

// 使用动态选项数据
const { options: channelSourceOptions, loadOptions: loadChannelSources } = useOptions('customer_source')
const { options: categoryOptions, loadOptions: loadCategories } = useOptions('customer_level')
const { options: genderOptions, loadOptions: loadGenders } = useOptions('gender')

// 组件挂载时加载选项数据
onMounted(async () => {
  await Promise.all([
    loadChannelSources(),
    loadCategories(),
    loadGenders()
  ])
})
</script>
```

### 3.4 系统设置功能开发步骤

**步骤1：创建路由配置**

```typescript
// src/router/modules/settings.ts
export default {
  path: '/settings',
  name: 'Settings',
  component: () => import('@/layouts/DefaultLayout.vue'),
  children: [
    {
      path: 'options',
      name: 'OptionManagement',
      component: () => import('@/views/settings/OptionManagement.vue'),
      meta: {
        title: '选项数据管理',
        requiresAuth: true,
        permissions: ['system:option:read']
      }
    }
  ]
}
```

**步骤2：开发管理界面**

```vue
<!-- src/views/settings/OptionManagement.vue -->
<template>
  <div class="option-management">
    <n-layout has-sider>
      <!-- 左侧分类导航 -->
      <n-layout-sider width="240">
        <CategoryTree 
          :categories="categories"
          :selected="selectedCategory"
          @select="handleCategorySelect"
        />
      </n-layout-sider>
      
      <!-- 右侧选项管理 -->
      <n-layout-content>
        <OptionItemManager 
          :category="selectedCategory"
          :items="currentItems"
          @create="handleCreateItem"
          @update="handleUpdateItem"
          @delete="handleDeleteItem"
        />
      </n-layout-content>
    </n-layout>
  </div>
</template>
```

## 4. 风险控制与回滚方案

### 4.1 风险识别

1. **数据迁移风险**：现有数据可能丢失或损坏
2. **性能风险**：动态加载可能影响页面加载速度
3. **兼容性风险**：现有功能可能受到影响
4. **用户体验风险**：界面变化可能影响用户操作习惯

### 4.2 风险缓解措施

1. **数据备份**：迁移前完整备份现有数据
2. **分阶段发布**：采用灰度发布策略
3. **性能监控**：实时监控系统性能指标
4. **用户培训**：提供操作指南和培训材料

### 4.3 回滚方案

1. **数据库回滚**：保留原始数据结构，可快速回滚
2. **代码回滚**：使用Git版本控制，支持快速代码回滚
3. **配置回滚**：保留原始配置文件，支持配置快速恢复

## 5. 验收标准

### 5.1 功能验收

* [ ] 所有硬编码选项数据已迁移到数据库

* [ ] 系统设置中可以管理所有选项数据

* [ ] 客户管理页面正常使用动态选项数据

* [ ] 选项数据的增删改查功能正常

* [ ] 数据缓存机制工作正常

### 5.2 性能验收

* [ ] 页面加载时间不超过原有时间的120%

* [ ] 选项数据加载时间不超过500ms

* [ ] 系统并发处理能力不低于原有水平

### 5.3 安全验收

* [ ] 选项数据管理需要相应权限

* [ ] 数据库访问权限配置正确

* [ ] 敏感操作有审计日志

### 5.4 用户体验验收

* [ ] 界面操作流畅，无明显卡顿

* [ ] 错误提示信息清晰明确

* [ ] 加载状态提示及时准确

* [ ] 数据更新实时生效

## 6. 后续优化建议

1. **缓存优化**：实现更智能的缓存策略
2. **权限细化**：按业务需求细化权限控制
3. **数据分析**：添加选项使用情况统计
4. **国际化支持**：为选项数据添加多语言支持
5. **API优化**：实现选项数据的批量操作接口

