{"migrationId": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "timestamp": "2025-08-18T07:15:32.276Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:13:46.654Z", "errors": [], "endTime": "2025-08-18T07:13:53.206Z", "duration": 6552}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:13:53.211Z", "errors": [], "endTime": "2025-08-18T07:13:55.889Z", "duration": 2678}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:13:55.890Z", "errors": [], "endTime": "2025-08-18T07:13:59.070Z", "duration": 3180}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:13:59.073Z", "errors": [], "endTime": "2025-08-18T07:14:02.201Z", "duration": 3128}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:02.204Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:14:02.588Z", "errors": [], "endTime": "2025-08-18T07:14:04.657Z", "duration": 2069}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:14:04.660Z", "errors": [], "endTime": "2025-08-18T07:14:06.487Z", "duration": 1827}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:14:06.489Z", "errors": [], "endTime": "2025-08-18T07:14:08.782Z", "duration": 2293}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:08.785Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:14:09.260Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:14:12.258Z", "duration": 2998}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:12.260Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:12.763Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:14:13.517Z", "errors": ["Unknown column 'attendees' in 'field list'"], "endTime": "2025-08-18T07:14:18.116Z", "duration": 4599}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:18.119Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:14:18.523Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:14:21.206Z", "duration": 2683}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:21.209Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:21.586Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:22.303Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:14:22.974Z", "errors": [], "endTime": "2025-08-18T07:15:26.384Z", "duration": 63410}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:15:26.387Z", "errors": ["Field 'assigned_to' doesn't have a default value"], "endTime": "2025-08-18T07:15:29.541Z", "duration": 3154}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:15:29.543Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:15:32.274Z", "duration": 2731}], "logs": [{"id": "b2ea15a5-b921-43a5-85dc-ced4139f5eed", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:13:46.654Z", "end_time": "2025-08-18T07:13:53.206Z", "duration_ms": 6552}, {"id": "8c1c453f-0773-4f82-a56e-a13778c5cb61", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:13:53.211Z", "end_time": "2025-08-18T07:13:55.889Z", "duration_ms": 2678}, {"id": "f0de74ff-7cf7-439f-8921-1b6a44be831e", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:13:55.890Z", "end_time": "2025-08-18T07:13:59.070Z", "duration_ms": 3180}, {"id": "1604176c-65ef-448f-b3df-a77571c20add", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:13:59.073Z", "end_time": "2025-08-18T07:14:02.201Z", "duration_ms": 3128}, {"id": "b627f7d1-e437-4317-8b84-48e51fff1019", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:02.204Z", "end_time": "2025-08-18T07:14:02.588Z", "duration_ms": 384}, {"id": "ab973b18-3ce3-4e54-86bc-622ffb9d8cef", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:14:02.588Z", "end_time": "2025-08-18T07:14:04.657Z", "duration_ms": 2069}, {"id": "59d34f6c-f97a-40d9-8d15-10f9b73e0143", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:14:04.660Z", "end_time": "2025-08-18T07:14:06.487Z", "duration_ms": 1827}, {"id": "5adfd9e8-91b2-4ed2-8cdc-5e57a75308d2", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:14:06.489Z", "end_time": "2025-08-18T07:14:08.782Z", "duration_ms": 2293}, {"id": "2c0d0c7f-71c0-4ffc-91bf-8c5f612a9063", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:08.785Z", "end_time": "2025-08-18T07:14:09.260Z", "duration_ms": 475}, {"id": "fbae2067-7817-4835-b209-de55f56d0a29", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:09.260Z", "end_time": "2025-08-18T07:14:12.258Z", "duration_ms": 2998}, {"id": "1d957ca0-d8b2-4410-bf78-9a46d70242cd", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:12.261Z", "end_time": "2025-08-18T07:14:12.763Z", "duration_ms": 502}, {"id": "b804d0b5-8948-407a-9661-472bc3b4c1bc", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:12.763Z", "end_time": "2025-08-18T07:14:13.517Z", "duration_ms": 754}, {"id": "5f8690a1-ebf4-42f8-9a64-787058d4ff9c", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:13.517Z", "end_time": "2025-08-18T07:14:18.116Z", "duration_ms": 4599}, {"id": "3fe2aaab-062a-4dad-822d-16f58acd00c9", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:18.119Z", "end_time": "2025-08-18T07:14:18.523Z", "duration_ms": 404}, {"id": "40aca080-aa1a-4e85-91ab-b318b0eb94cc", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:18.523Z", "end_time": "2025-08-18T07:14:21.206Z", "duration_ms": 2683}, {"id": "04b7771d-3dfe-4cdf-98f1-76a12f17adc3", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:21.209Z", "end_time": "2025-08-18T07:14:21.585Z", "duration_ms": 376}, {"id": "134d3727-24d6-4b59-9c31-cb793d61b96f", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:21.586Z", "end_time": "2025-08-18T07:14:22.302Z", "duration_ms": 716}, {"id": "723baee5-9ba2-4355-8e86-3fa985432f03", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:22.303Z", "end_time": "2025-08-18T07:14:22.974Z", "duration_ms": 671}, {"id": "5653cc7a-79ca-4d6f-b1dc-79d9e20f8f26", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:14:22.974Z", "end_time": "2025-08-18T07:15:26.384Z", "duration_ms": 63410}, {"id": "3808e42f-511a-4d2f-b218-58856c010232", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:15:26.387Z", "end_time": "2025-08-18T07:15:29.541Z", "duration_ms": 3154}, {"id": "715730a8-887f-4a10-8233-62ffca53ca92", "migration_id": "4d85e395-b8fe-435e-ae1e-18141bc3e53e", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:15:29.543Z", "end_time": "2025-08-18T07:15:32.274Z", "duration_ms": 2731}]}