import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  wechatCustomerService,
  wechatGroupService,
  browsingTrackService,
  shareRecordService,
  wechatMessageService,
  wechatStatisticsService,
  type BrowsingTrackQuery,
  type WechatCustomerQuery,
  type ShareRecordQuery
} from '@/api/wechatService'
import type {
  BrowsingTrack,
  ShareRecord,
  WechatGroup,
  WechatMessage,
  WechatStatistics,
  WechatCustomer
} from '@/types'

export const useWechatStore = defineStore('wechat', () => {
  // 微信客户相关状态
  const wechatCustomers = ref<WechatCustomer[]>([])
  const wechatCustomersLoading = ref(false)
  const wechatCustomersTotal = ref(0)
  const currentWechatCustomer = ref<WechatCustomer | null>(null)

  // 浏览轨迹相关状态
  const browsingTracks = ref<BrowsingTrack[]>([])
  const browsingTracksLoading = ref(false)
  const browsingTracksTotal = ref(0)
  const currentBrowsingTrack = ref<BrowsingTrack | null>(null)

  // 分享记录相关状态
  const shareRecords = ref<ShareRecord[]>([])
  const shareRecordsLoading = ref(false)
  const shareRecordsTotal = ref(0)
  const currentShareRecord = ref<ShareRecord | null>(null)

  // 微信群组相关状态
  const wechatGroups = ref<WechatGroup[]>([])
  const wechatGroupsLoading = ref(false)
  const wechatGroupsTotal = ref(0)
  const currentWechatGroup = ref<WechatGroup | null>(null)

  // 微信消息相关状态
  const wechatMessages = ref<WechatMessage[]>([])
  const wechatMessagesLoading = ref(false)
  const wechatMessagesTotal = ref(0)
  const unreadMessagesCount = ref(0)

  // 统计数据相关状态
  const wechatStatistics = ref<WechatStatistics | null>(null)
  const statisticsLoading = ref(false)

  // 计算属性
  const subscribedCustomers = computed(() => 
    wechatCustomers.value.filter((customer: any) => customer.subscribe_status === 'subscribed')
  )

  const subscribedCount = computed(() => 
    wechatCustomers.value.filter((customer: any) => customer.subscribe_status === 'subscribed').length
  )

  const unsubscribedCount = computed(() => 
    wechatCustomers.value.filter((customer: any) => customer.subscribe_status === 'unsubscribed').length
  )

  const blockedCount = computed(() => 
    wechatCustomers.value.filter((customer: any) => customer.subscribe_status === 'blocked').length
  )

  const totalShareViews = computed(() => shareRecords.value.reduce((total: number, record: any) => total + record.view_count, 0))

  const totalShareClicks = computed(() => shareRecords.value.reduce((total: number, record: any) => total + record.click_count, 0))

  const averageConversionRate = computed(() => {
    const totalShares = shareRecords.value.reduce((total: number, record: any) => total + record.share_count, 0)
    const totalConversions = shareRecords.value.reduce((total: number, record: any) => total + record.conversion_count, 0)
    return totalShares > 0 ? (totalConversions / totalShares) * 100 : 0
  })

  // 微信客户管理方法
  const fetchWechatCustomers = async (params: WechatCustomerQuery = {}) => {
    wechatCustomersLoading.value = true
    try {
      const response = await wechatCustomerService.getCustomers(params)
      wechatCustomers.value = response.data
      wechatCustomersTotal.value = response.total
      return response
    } catch (error) {
      console.error('Failed to fetch wechat customers:', error)
      throw error
    } finally {
      wechatCustomersLoading.value = false
    }
  }

  const fetchWechatCustomer = async (id: number) => {
    try {
      const response = await wechatCustomerService.getCustomer(id)
      currentWechatCustomer.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch wechat customer:', error)
      throw error
    }
  }

  const updateWechatCustomer = async (id: number, data: Partial<WechatCustomer>) => {
    try {
      const response = await wechatCustomerService.updateCustomer(id, data)
      
      // 更新本地数据
      const index = wechatCustomers.value.findIndex((customer: any) => customer.id === id)
      if (index !== -1) {
        wechatCustomers.value[index] = response
      }
      
      if (currentWechatCustomer.value?.id === id) {
        currentWechatCustomer.value = response
      }
      
      return response
    } catch (error) {
      console.error('Failed to update wechat customer:', error)
      throw error
    }
  }

  const syncWechatCustomer = async (openid: string) => {
    try {
      const response = await wechatCustomerService.syncCustomer(openid)
      
      // 检查是否已存在，如果存在则更新，否则添加
      const existingIndex = wechatCustomers.value.findIndex((customer: any) => customer.openid === openid)
      if (existingIndex !== -1) {
        wechatCustomers.value[existingIndex] = response
      } else {
        wechatCustomers.value.unshift(response)
        wechatCustomersTotal.value += 1
      }
      
      return response
    } catch (error) {
      console.error('Failed to sync wechat customer:', error)
      throw error
    }
  }

  const batchSyncWechatCustomers = async (openids: string[]) => {
    try {
      const response = await wechatCustomerService.batchSyncCustomers(openids)
      
      // 重新获取客户列表
      await fetchWechatCustomers()
      
      return response
    } catch (error) {
      console.error('Failed to batch sync wechat customers:', error)
      throw error
    }
  }

  const setCustomerTags = async (id: number, tags: string[]) => {
    try {
      await wechatCustomerService.setCustomerTags(id, tags)
      
      // 更新本地数据
      const index = wechatCustomers.value.findIndex((customer: any) => customer.id === id)
      if (index !== -1) {
        wechatCustomers.value[index].tags = tags
      }
      
      if (currentWechatCustomer.value?.id === id) {
        currentWechatCustomer.value.tags = tags
      }
    } catch (error) {
      console.error('Failed to set customer tags:', error)
      throw error
    }
  }

  const setCustomerRemark = async (id: number, remark: string) => {
    try {
      await wechatCustomerService.setCustomerRemark(id, remark)
      
      // 更新本地数据
      const index = wechatCustomers.value.findIndex((customer: any) => customer.id === id)
      if (index !== -1) {
        wechatCustomers.value[index].remark = remark
      }
      
      if (currentWechatCustomer.value?.id === id) {
        currentWechatCustomer.value.remark = remark
      }
    } catch (error) {
      console.error('Failed to set customer remark:', error)
      throw error
    }
  }

  // 浏览轨迹管理方法
  const fetchBrowsingTracks = async (params: BrowsingTrackQuery = {}) => {
    browsingTracksLoading.value = true
    try {
      const response = await browsingTrackService.getTracks(params)
      browsingTracks.value = response.data
      browsingTracksTotal.value = response.total
      return response
    } catch (error) {
      console.error('Failed to fetch browsing tracks:', error)
      throw error
    } finally {
      browsingTracksLoading.value = false
    }
  }

  const fetchBrowsingTrack = async (id: number) => {
    try {
      const response = await browsingTrackService.getTrack(id)
      currentBrowsingTrack.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch browsing track:', error)
      throw error
    }
  }

  const fetchCustomerBrowsingTracks = async (customerId: number, params: Omit<BrowsingTrackQuery, 'customer_id'> = {}) => {
    try {
      const response = await browsingTrackService.getCustomerTracks(customerId, params)
      return response
    } catch (error) {
      console.error('Failed to fetch customer browsing tracks:', error)
      throw error
    }
  }

  const recordBrowsingTrack = async (data: Omit<BrowsingTrack, 'id' | 'created_at'>) => {
    try {
      const response = await browsingTrackService.recordTrack(data)
      browsingTracks.value.unshift(response)
      browsingTracksTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to record browsing track:', error)
      throw error
    }
  }

  const getHeatmapData = async (params: { page_url: string; date_range?: [string, string] }) => {
    try {
      const response = await browsingTrackService.getHeatmapData(params)
      return response
    } catch (error) {
      console.error('Failed to get heatmap data:', error)
      throw error
    }
  }

  // 分享记录管理方法
  const fetchShareRecords = async (params: ShareRecordQuery = {}) => {
    shareRecordsLoading.value = true
    try {
      const response = await shareRecordService.getRecords(params)
      shareRecords.value = response.data
      shareRecordsTotal.value = response.total
      return response
    } catch (error) {
      console.error('Failed to fetch share records:', error)
      throw error
    } finally {
      shareRecordsLoading.value = false
    }
  }

  const fetchShareRecord = async (id: number) => {
    try {
      const response = await shareRecordService.getRecord(id)
      currentShareRecord.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch share record:', error)
      throw error
    }
  }

  const createShareRecord = async (data: Omit<ShareRecord, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await shareRecordService.createRecord(data)
      shareRecords.value.unshift(response)
      shareRecordsTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to create share record:', error)
      throw error
    }
  }

  const updateShareStats = async (id: number, stats: { view_count?: number; click_count?: number; conversion_count?: number }) => {
    try {
      await shareRecordService.updateStats(id, stats)
      
      // 更新本地数据
      const index = shareRecords.value.findIndex((record: any) => record.id === id)
      if (index !== -1) {
        Object.assign(shareRecords.value[index], stats)
      }
      
      if (currentShareRecord.value?.id === id) {
        Object.assign(currentShareRecord.value, stats)
      }
    } catch (error) {
      console.error('Failed to update share stats:', error)
      throw error
    }
  }

  const getShareStats = async (params: { date_range?: [string, string]; content_type?: string }) => {
    try {
      const response = await shareRecordService.getShareStats(params)
      return response
    } catch (error) {
      console.error('Failed to get share stats:', error)
      throw error
    }
  }

  // 微信群组管理方法
  const fetchWechatGroups = async (params: { page?: number; pageSize?: number; keyword?: string; group_type?: string } = {}) => {
    wechatGroupsLoading.value = true
    try {
      const response = await wechatGroupService.getGroups(params)
      wechatGroups.value = response.data
      wechatGroupsTotal.value = response.total
      return response
    } catch (error) {
      console.error('Failed to fetch wechat groups:', error)
      throw error
    } finally {
      wechatGroupsLoading.value = false
    }
  }

  const fetchWechatGroup = async (id: number) => {
    try {
      const response = await wechatGroupService.getGroup(id)
      currentWechatGroup.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch wechat group:', error)
      throw error
    }
  }

  const createWechatGroup = async (data: Omit<WechatGroup, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await wechatGroupService.createGroup(data)
      wechatGroups.value.unshift(response)
      wechatGroupsTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to create wechat group:', error)
      throw error
    }
  }

  const updateWechatGroup = async (id: number, data: Partial<WechatGroup>) => {
    try {
      const response = await wechatGroupService.updateGroup(id, data)
      
      // 更新本地数据
      const index = wechatGroups.value.findIndex((group: any) => group.id === id)
      if (index !== -1) {
        wechatGroups.value[index] = response
      }
      
      if (currentWechatGroup.value?.id === id) {
        currentWechatGroup.value = response
      }
      
      return response
    } catch (error) {
      console.error('Failed to update wechat group:', error)
      throw error
    }
  }

  const deleteWechatGroup = async (id: number) => {
    try {
      await wechatGroupService.deleteGroup(id)
      
      // 从本地数据中移除
      const index = wechatGroups.value.findIndex((group: any) => group.id === id)
      if (index !== -1) {
        wechatGroups.value.splice(index, 1)
        wechatGroupsTotal.value -= 1
      }
      
      if (currentWechatGroup.value?.id === id) {
        currentWechatGroup.value = null
      }
    } catch (error) {
      console.error('Failed to delete wechat group:', error)
      throw error
    }
  }

  const getGroupMembers = async (groupId: number) => {
    try {
      const response = await wechatGroupService.getGroupMembers(groupId)
      return response
    } catch (error) {
      console.error('Failed to get group members:', error)
      throw error
    }
  }

  const addGroupMembers = async (groupId: number, openids: string[]) => {
    try {
      await wechatGroupService.addGroupMembers(groupId, openids)
      
      // 更新群组成员数量
      const index = wechatGroups.value.findIndex((group: any) => group.id === groupId)
      if (index !== -1) {
        wechatGroups.value[index].memberCount += openids.length
      }
      
      if (currentWechatGroup.value?.id === groupId) {
        currentWechatGroup.value.memberCount += openids.length
      }
    } catch (error) {
      console.error('Failed to add group members:', error)
      throw error
    }
  }

  const removeGroupMembers = async (groupId: number, openids: string[]) => {
    try {
      await wechatGroupService.removeGroupMembers(groupId, openids)
      
      // 更新群组成员数量
      const index = wechatGroups.value.findIndex((group: any) => group.id === groupId)
      if (index !== -1) {
        wechatGroups.value[index].memberCount -= openids.length
      }
      
      if (currentWechatGroup.value?.id === groupId) {
        currentWechatGroup.value.memberCount -= openids.length
      }
    } catch (error) {
      console.error('Failed to remove group members:', error)
      throw error
    }
  }

  // 微信消息管理方法
  const fetchWechatMessages = async (params: {
    page?: number
    pageSize?: number
    from_openid?: string
    group_id?: number
    message_type?: string
    is_read?: boolean
    date_range?: [string, string]
  } = {}) => {
    wechatMessagesLoading.value = true
    try {
      const response = await wechatMessageService.getMessages(params)
      wechatMessages.value = response.data
      wechatMessagesTotal.value = response.total
      
      // 计算未读消息数量
      unreadMessagesCount.value = wechatMessages.value.filter((msg: any) => !msg.is_read).length
      
      return response
    } catch (error) {
      console.error('Failed to fetch wechat messages:', error)
      throw error
    } finally {
      wechatMessagesLoading.value = false
    }
  }

  const sendWechatMessage = async (data: {
    to_openid?: string
    group_id?: number
    message_type: string
    content: string
    media_url?: string
  }) => {
    try {
      const response = await wechatMessageService.sendMessage(data)
      wechatMessages.value.unshift(response)
      wechatMessagesTotal.value += 1
      return response
    } catch (error) {
      console.error('Failed to send wechat message:', error)
      throw error
    }
  }

  const markMessageAsRead = async (id: number) => {
    try {
      await wechatMessageService.markAsRead(id)
      
      // 更新本地数据
      const index = wechatMessages.value.findIndex((msg: any) => msg.id === id)
      if (index !== -1) {
        wechatMessages.value[index].is_read = true
        unreadMessagesCount.value -= 1
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error)
      throw error
    }
  }

  const replyWechatMessage = async (id: number, content: string) => {
    try {
      await wechatMessageService.replyMessage(id, content)
      
      // 更新本地数据
      const index = wechatMessages.value.findIndex((msg: any) => msg.id === id)
      if (index !== -1) {
        wechatMessages.value[index].is_replied = true
        wechatMessages.value[index].reply_content = content
        wechatMessages.value[index].reply_time = new Date().toISOString()
      }
    } catch (error) {
      console.error('Failed to reply wechat message:', error)
      throw error
    }
  }

  const batchMarkMessagesAsRead = async (ids: number[]) => {
    try {
      await wechatMessageService.batchMarkAsRead(ids)
      
      // 更新本地数据
      ids.forEach(id => {
        const index = wechatMessages.value.findIndex((msg: any) => msg.id === id)
        if (index !== -1 && !wechatMessages.value[index].is_read) {
          wechatMessages.value[index].is_read = true
          unreadMessagesCount.value -= 1
        }
      })
    } catch (error) {
      console.error('Failed to batch mark messages as read:', error)
      throw error
    }
  }

  // 统计数据方法
  const fetchWechatStatistics = async (params: { date_range?: [string, string] } = {}) => {
    statisticsLoading.value = true
    try {
      const response = await wechatStatisticsService.getOverallStats(params)
      wechatStatistics.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch wechat statistics:', error)
      throw error
    } finally {
      statisticsLoading.value = false
    }
  }

  const getCustomerGrowthTrend = async (params: { date_range?: [string, string]; period?: 'day' | 'week' | 'month' } = {}) => {
    try {
      const response = await wechatStatisticsService.getCustomerGrowthTrend(params)
      return response
    } catch (error) {
      console.error('Failed to get customer growth trend:', error)
      throw error
    }
  }

  const getMessageStats = async (params: { date_range?: [string, string] } = {}) => {
    try {
      const response = await wechatStatisticsService.getMessageStats(params)
      return response
    } catch (error) {
      console.error('Failed to get message stats:', error)
      throw error
    }
  }

  const getBrowsingStats = async (params: { date_range?: [string, string] } = {}) => {
    try {
      const response = await wechatStatisticsService.getBrowsingStats(params)
      return response
    } catch (error) {
      console.error('Failed to get browsing stats:', error)
      throw error
    }
  }

  const getShareStatsData = async (params: { date_range?: [string, string] } = {}) => {
    try {
      const response = await wechatStatisticsService.getShareStats(params)
      return response
    } catch (error) {
      console.error('Failed to get share stats:', error)
      throw error
    }
  }

  // 重置状态方法
  const resetWechatCustomers = () => {
    wechatCustomers.value = []
    wechatCustomersTotal.value = 0
    currentWechatCustomer.value = null
  }

  const resetBrowsingTracks = () => {
    browsingTracks.value = []
    browsingTracksTotal.value = 0
    currentBrowsingTrack.value = null
  }

  const resetShareRecords = () => {
    shareRecords.value = []
    shareRecordsTotal.value = 0
    currentShareRecord.value = null
  }

  const resetWechatGroups = () => {
    wechatGroups.value = []
    wechatGroupsTotal.value = 0
    currentWechatGroup.value = null
  }

  const resetWechatMessages = () => {
    wechatMessages.value = []
    wechatMessagesTotal.value = 0
    unreadMessagesCount.value = 0
  }

  const resetStatistics = () => {
    wechatStatistics.value = null
  }

  return {
    // 状态
    wechatCustomers,
    wechatCustomersLoading,
    wechatCustomersTotal,
    currentWechatCustomer,
    browsingTracks,
    browsingTracksLoading,
    browsingTracksTotal,
    currentBrowsingTrack,
    shareRecords,
    shareRecordsLoading,
    shareRecordsTotal,
    currentShareRecord,
    wechatGroups,
    wechatGroupsLoading,
    wechatGroupsTotal,
    currentWechatGroup,
    wechatMessages,
    wechatMessagesLoading,
    wechatMessagesTotal,
    unreadMessagesCount,
    wechatStatistics,
    statisticsLoading,

    // 计算属性
    subscribedCustomers,
    subscribedCount,
    unsubscribedCount,
    blockedCount,
    totalShareViews,
    totalShareClicks,
    averageConversionRate,

    // 微信客户方法
    fetchWechatCustomers,
    fetchWechatCustomer,
    updateWechatCustomer,
    syncWechatCustomer,
    batchSyncWechatCustomers,
    setCustomerTags,
    setCustomerRemark,

    // 浏览轨迹方法
    fetchBrowsingTracks,
    fetchBrowsingTrack,
    fetchCustomerBrowsingTracks,
    recordBrowsingTrack,
    getHeatmapData,

    // 分享记录方法
    fetchShareRecords,
    fetchShareRecord,
    createShareRecord,
    updateShareStats,
    getShareStats,

    // 微信群组方法
    fetchWechatGroups,
    fetchWechatGroup,
    createWechatGroup,
    updateWechatGroup,
    deleteWechatGroup,
    getGroupMembers,
    addGroupMembers,
    removeGroupMembers,

    // 微信消息方法
    fetchWechatMessages,
    sendWechatMessage,
    markMessageAsRead,
    replyWechatMessage,
    batchMarkMessagesAsRead,

    // 统计数据方法
    fetchWechatStatistics,
    getCustomerGrowthTrend,
    getMessageStats,
    getBrowsingStats,
    getShareStatsData,

    // 重置方法
    resetWechatCustomers,
    resetBrowsingTracks,
    resetShareRecords,
    resetWechatGroups,
    resetWechatMessages,
    resetStatistics
  }
})