<template>
  <n-modal v-model:show="visible" preset="dialog" :title="props.title || '分配客户跟进权限'">
    <div class="organization-select">
      <!-- 客户列表显示 -->
      <div v-if="props.showCustomerList && props.customers && props.customers.length > 0" class="customer-list-section">
        <div class="section-title">待分配客户 ({{ props.customers.length }})</div>
        <div class="customer-list">
          <n-tag
            v-for="customer in props.customers"
            :key="customer.id"
            type="info"
          >
            {{ customer.name }} - {{ customer.company || '暂无公司' }}
          </n-tag>
        </div>
      </div>

      <div class="search-section">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索部门或员工"
          clearable
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </div>

      <div class="tree-section">
        <n-tree
          :data="filteredTreeData"
          :expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          :checked-keys="checkedKeys"
          key-field="id"
          label-field="name"
          children-field="children"
          checkable
          :render-prefix="renderPrefix"
          :render-label="renderLabel"
          @update:expanded-keys="handleExpandedKeysChange"
          @update:selected-keys="handleSelectedKeysChange"
          @update:checked-keys="handleCheckedKeysChange"
        />
      </div>

      <div class="selected-section" v-if="selectedEmployees.length > 0">
        <div class="section-title">已选择的员工 ({{ selectedEmployees.length }})</div>
        <div class="selected-list">
          <n-tag
            v-for="employee in selectedEmployees"
            :key="employee.id"
            closable
            @close="handleRemoveEmployee(employee.id)"
          >
            {{ employee.name }} - {{ employee.position }}
          </n-tag>
        </div>
      </div>
    </div>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleConfirm" :disabled="selectedEmployees.length === 0">
          确认分配 ({{ selectedEmployees.length }})
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import {
  NModal,
  NTree,
  NInput,
  NButton,
  NSpace,
  NIcon,
  NTag,
  useMessage,
  type TreeOption
} from 'naive-ui'
import {
  SearchOutline,
  BusinessOutline,
  PersonOutline
} from '@vicons/ionicons5'

interface Employee {
  id: string
  name: string
  type: 'employee'
  position: string
  phone: string
  email: string
  departmentId: string
}

interface Department {
  id: string
  name: string
  type: 'department'
  description?: string
  children?: (Department | Employee)[]
}

interface Props {
  show: boolean
  customer?: any
  customers?: any[]
  title?: string
  showCustomerList?: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'confirm', data: { customer?: any; customers?: any[]; employees: Employee[] }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()

// 弹窗显示状态
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 搜索关键词
const searchKeyword = ref('')

// 树形数据状态
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])

// 组织架构数据（与OrganizationSettings.vue保持一致）
const organizationData = ref<(Department | Employee)[]>([
  {
    id: 'dept-1',
    name: '总经理办公室',
    type: 'department',
    description: '公司最高管理层',
    children: [
      {
        id: 'emp-1',
        name: '张总',
        type: 'employee' as const,
        position: '总经理',
        phone: '13800138001',
        email: '<EMAIL>',
        departmentId: 'dept-1'
      },
      {
        id: 'emp-2',
        name: '李秘书',
        type: 'employee' as const,
        position: '总经理秘书',
        phone: '13800138002',
        email: '<EMAIL>',
        departmentId: 'dept-1'
      }
    ]
  },
  {
    id: 'dept-2',
    name: '销售部',
    type: 'department',
    description: '负责公司产品销售和客户关系维护',
    children: [
      {
        id: 'emp-3',
        name: '王经理',
        type: 'employee' as const,
        position: '销售经理',
        phone: '13800138003',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      },
      {
        id: 'emp-4',
        name: '赵专员',
        type: 'employee' as const,
        position: '销售专员',
        phone: '13800138004',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      },
      {
        id: 'emp-5',
        name: '钱专员',
        type: 'employee' as const,
        position: '销售专员',
        phone: '13800138005',
        email: '<EMAIL>',
        departmentId: 'dept-2'
      }
    ]
  },
  {
    id: 'dept-3',
    name: '技术部',
    type: 'department',
    description: '负责产品研发和技术支持',
    children: [
      {
        id: 'dept-3-1',
        name: '前端开发组',
        type: 'department',
        description: '负责前端界面开发',
        children: [
          {
            id: 'emp-6',
            name: '孙工程师',
            type: 'employee' as const,
            position: '前端工程师',
            phone: '13800138006',
            email: '<EMAIL>',
            departmentId: 'dept-3-1'
          },
          {
            id: 'emp-7',
            name: '周工程师',
            type: 'employee' as const,
            position: '前端工程师',
            phone: '13800138007',
            email: '<EMAIL>',
            departmentId: 'dept-3-1'
          }
        ]
      },
      {
        id: 'dept-3-2',
        name: '后端开发组',
        type: 'department',
        description: '负责后端服务开发',
        children: [
          {
            id: 'emp-8',
            name: '吴工程师',
            type: 'employee' as const,
            position: '后端工程师',
            phone: '13800138008',
            email: '<EMAIL>',
            departmentId: 'dept-3-2'
          }
        ]
      }
    ]
  },
  {
    id: 'dept-4',
    name: '人事部',
    type: 'department',
    description: '负责人力资源管理',
    children: [
      {
        id: 'emp-9',
        name: '郑主管',
        type: 'employee' as const,
        position: '人事主管',
        phone: '13800138009',
        email: '<EMAIL>',
        departmentId: 'dept-4'
      }
    ]
  }
])

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value) {
    return organizationData.value
  }

  const filterTree = (data: any[]): any[] => {
    return data.reduce((acc, item) => {
      const matchesSearch = item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (item.position && item.position.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      
      let filteredChildren: any[] = []
      if (item.children) {
        filteredChildren = filterTree(item.children)
      }

      if (matchesSearch || filteredChildren.length > 0) {
        acc.push({
          ...item,
          children: filteredChildren.length > 0 ? filteredChildren : item.children
        })
      }

      return acc
    }, [])
  }

  return filterTree(organizationData.value)
})

// 获取所有员工数据
const getAllEmployees = (data: any[]): Employee[] => {
  const employees: Employee[] = []
  
  const traverse = (items: any[]) => {
    items.forEach(item => {
      if (item.type === 'employee') {
        employees.push(item)
      } else if (item.children) {
        traverse(item.children)
      }
    })
  }
  
  traverse(data)
  return employees
}

// 已选择的员工
const selectedEmployees = computed(() => {
  const allEmployees = getAllEmployees(organizationData.value)
  return allEmployees.filter(emp => checkedKeys.value.includes(emp.id))
})

// 渲染树节点前缀图标
const renderPrefix = ({ option }: { option: TreeOption }) => {
  const item = option as any
  return h(NIcon, {
    style: { marginRight: '8px' },
    color: item.type === 'department' ? '#1890ff' : '#52c41a'
  }, {
    default: () => h(item.type === 'department' ? BusinessOutline : PersonOutline)
  })
}

// 渲染树节点标签
const renderLabel = ({ option }: { option: TreeOption }) => {
  const item = option as any
  if (item.type === 'employee') {
    return h('span', {
      style: { display: 'flex', alignItems: 'center', gap: '8px' }
    }, [
      h('span', { style: { fontWeight: '500' } }, item.name),
      h('span', { style: { color: '#8c8c8c', fontSize: '12px' } }, `(${item.position})`)
    ])
  }
  return item.name
}

// 处理展开状态变化
const handleExpandedKeysChange = (keys: string[]) => {
  expandedKeys.value = keys
}

// 处理选中状态变化
const handleSelectedKeysChange = (keys: string[]) => {
  selectedKeys.value = keys
}

// 处理勾选状态变化
const handleCheckedKeysChange = (keys: string[]) => {
  // 只允许勾选员工，过滤掉部门
  const allEmployees = getAllEmployees(organizationData.value)
  const employeeIds = allEmployees.map(emp => emp.id)
  checkedKeys.value = keys.filter(key => employeeIds.includes(key))
}

// 移除已选择的员工
const handleRemoveEmployee = (employeeId: string) => {
  checkedKeys.value = checkedKeys.value.filter(id => id !== employeeId)
}

// 取消
const handleCancel = () => {
  visible.value = false
  // 重置状态
  searchKeyword.value = ''
  checkedKeys.value = []
  selectedKeys.value = []
}

// 确认分配
const handleConfirm = () => {
  if (selectedEmployees.value.length === 0) {
    message.warning('请至少选择一个员工')
    return
  }

  const confirmData: any = {
    employees: selectedEmployees.value
  }

  // 单个客户分配
  if (props.customer) {
    confirmData.customer = props.customer
  }
  
  // 批量客户分配
  if (props.customers && props.customers.length > 0) {
    confirmData.customers = props.customers
  }

  emit('confirm', confirmData)

  const customerCount = props.customers ? props.customers.length : 1
  message.success(`已将 ${customerCount} 个客户分配给 ${selectedEmployees.value.length} 名员工`)
  handleCancel()
}

// 监听弹窗显示状态，自动展开第一级部门
watch(visible, (newVisible) => {
  if (newVisible) {
    expandedKeys.value = organizationData.value
      .filter(item => item.type === 'department')
      .map(item => item.id)
  }
})
</script>

<style scoped>
.organization-select {
  max-height: 500px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-section {
  margin-bottom: 8px;
}

.tree-section {
  flex: 1;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.selected-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.section-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #262626;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.customer-list-section {
  border: 1px solid #e8f4fd;
  border-radius: 6px;
  padding: 12px;
  background: #f6fbff;
  margin-bottom: 16px;
}

.customer-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

:deep(.n-tree .n-tree-node-content) {
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.n-tree .n-tree-node-content:hover) {
  background: #f0f0f0;
}

:deep(.n-tree .n-tree-node-content--selected) {
  background: #e6f7ff;
}

:deep(.n-tree .n-tree-node-content__text) {
  font-weight: 400;
}

:deep(.n-tree .n-tree-node-checkbox) {
  margin-right: 8px;
}
</style>