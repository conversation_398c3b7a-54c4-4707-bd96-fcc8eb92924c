<template>
  <n-modal :show="show" @update:show="$emit('update:show', $event)">
    <n-card
      style="width: 1000px; max-height: 80vh"
      title="参与者列表"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="exportParticipants">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出数据
          </n-button>
        </n-space>
      </template>

      <!-- 筛选器 -->
      <n-space class="mb-4">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索参与者姓名、手机号"
          style="width: 200px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        
        <n-select
          v-model:value="statusFilter"
          placeholder="参与状态"
          style="width: 120px"
          clearable
          :options="statusOptions"
          @update:value="handleSearch"
        />
        
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          placeholder="参与时间范围"
          style="width: 240px"
          clearable
          @update:value="handleSearch"
        />
      </n-space>

      <!-- 参与者列表 -->
      <n-data-table
        :columns="columns"
        :data="filteredParticipants"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        size="small"
        flex-height
        style="height: 400px"
      />
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="$emit('update:show', false)">关闭</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, h } from 'vue'
import { NIcon, useMessage } from 'naive-ui'
import { SearchOutline, DownloadOutline } from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

interface Participant {
  id: number
  customer_id: number
  customer_name: string
  customer_phone: string
  customer_wechat?: string
  participation_time: string
  status: string
  result?: string
  reward?: string
  notes?: string
}

interface Props {
  show: boolean
  campaignId?: number
  campaignType?: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = defineProps<Props>()
defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const participants = ref<Participant[]>([])
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)

// 分页配置
const pagination = {
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}

// 状态选项
const statusOptions = [
  { label: '已参与', value: 'participated' },
  { label: '已中奖', value: 'won' },
  { label: '已领奖', value: 'claimed' },
  { label: '已购买', value: 'purchased' },
  { label: '已分享', value: 'shared' }
]

// 表格列配置
const columns: DataTableColumns<Participant> = [
  {
    title: '客户姓名',
    key: 'customer_name',
    width: 100,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '手机号',
    key: 'customer_phone',
    width: 120
  },
  {
    title: '微信号',
    key: 'customer_wechat',
    width: 120,
    render(row) {
      return row.customer_wechat || '-'
    }
  },
  {
    title: '参与时间',
    key: 'participation_time',
    width: 160,
    render(row) {
      return new Date(row.participation_time).toLocaleString()
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap: Record<string, { label: string; type: string }> = {
        participated: { label: '已参与', type: 'info' },
        won: { label: '已中奖', type: 'success' },
        claimed: { label: '已领奖', type: 'warning' },
        purchased: { label: '已购买', type: 'success' },
        shared: { label: '已分享', type: 'info' }
      }
      const status = statusMap[row.status] || { label: row.status, type: 'default' }
      return h('n-tag', { type: status.type }, { default: () => status.label })
    }
  },
  {
    title: '结果/奖励',
    key: 'result',
    width: 150,
    render(row) {
      if (row.reward) {
        return h('n-tag', { type: 'success' }, { default: () => row.reward })
      }
      return row.result || '-'
    }
  },
  {
    title: '备注',
    key: 'notes',
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.notes || '-'
    }
  }
]

// 计算属性
const filteredParticipants = computed(() => {
  let result = participants.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(p => 
      p.customer_name.toLowerCase().includes(keyword) ||
      p.customer_phone.includes(keyword)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(p => p.status === statusFilter.value)
  }

  // 时间范围筛选
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(p => {
      const time = new Date(p.participation_time).getTime()
      return time >= start && time <= end
    })
  }

  return result
})

// 方法
const loadParticipants = async () => {
  if (!props.campaignId) return
  
  loading.value = true
  try {
    // 这里应该调用实际的API
    // const response = await campaignService.getParticipants(props.campaignId)
    // participants.value = response.data
    
    // 模拟数据
    participants.value = generateMockParticipants()
  } catch (error) {
    message.error('加载参与者数据失败')
    console.error('Load participants error:', error)
  } finally {
    loading.value = false
  }
}

const generateMockParticipants = (): Participant[] => {
  const mockData: Participant[] = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const statuses = ['participated', 'won', 'claimed', 'purchased', 'shared']
  const rewards = ['一等奖', '二等奖', '三等奖', '参与奖', '优惠券']
  
  for (let i = 1; i <= 50; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    mockData.push({
      id: i,
      customer_id: 1000 + i,
      customer_name: names[Math.floor(Math.random() * names.length)] + i,
      customer_phone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      customer_wechat: Math.random() > 0.3 ? `wx_${i}` : undefined,
      participation_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      status,
      result: status === 'won' ? rewards[Math.floor(Math.random() * rewards.length)] : undefined,
      reward: status === 'won' || status === 'claimed' ? rewards[Math.floor(Math.random() * rewards.length)] : undefined,
      notes: Math.random() > 0.7 ? '备注信息' : undefined
    })
  }
  
  return mockData.sort((a, b) => new Date(b.participation_time).getTime() - new Date(a.participation_time).getTime())
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const exportParticipants = () => {
  try {
    // 准备导出数据
    const exportData = filteredParticipants.value.map(p => ({
      '客户姓名': p.customer_name,
      '手机号': p.customer_phone,
      '微信号': p.customer_wechat || '',
      '参与时间': new Date(p.participation_time).toLocaleString(),
      '状态': getStatusLabel(p.status),
      '结果奖励': p.reward || p.result || '',
      '备注': p.notes || ''
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0] || {})
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header as keyof typeof row]}"`).join(','))
    ].join('\n')

    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `活动参与者_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('Export error:', error)
  }
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    participated: '已参与',
    won: '已中奖',
    claimed: '已领奖',
    purchased: '已购买',
    shared: '已分享'
  }
  return statusMap[status] || status
}

// 监听弹窗显示状态
watch(() => props.show, (newShow) => {
  if (newShow && props.campaignId) {
    loadParticipants()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  if (props.show && props.campaignId) {
    loadParticipants()
  }
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>