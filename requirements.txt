# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库相关
sqlalchemy>=2.0.0
alembic>=1.12.0
aiomysql>=0.2.0
pymysql>=1.1.0

# 认证和安全
pyjwt>=2.8.0
bcrypt==4.0.1
passlib[bcrypt]==1.7.4
python-multipart>=0.0.6
captcha>=0.4

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0

# HTTP客户端
httpx==0.25.2

# 工具库
python-dotenv==1.0.0
typing-extensions==4.8.0

# 开发和测试依赖
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 日志
loguru==0.7.2

# 时间处理
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10