<template>
  <div class="material-library">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
          <p class="page-description">企业素材库，管理图片、视频等营销素材</p>
        </div>
        <n-space>
          <n-button type="primary" @click="showUploadModal = true">
            <template #icon>
              <n-icon><cloud-upload-outline /></n-icon>
            </template>
            上传素材
          </n-button>
          <n-button type="info" @click="showCategoryModal = true">
            <template #icon>
              <n-icon><folder-outline /></n-icon>
            </template>
            管理分类
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 筛选和搜索 -->
    <n-card class="filter-card">
      <n-space>
        <n-select
          v-model:value="filters.category"
          placeholder="选择分类"
          style="width: 150px"
          :options="categoryOptions"
          clearable
        />
        <n-select
          v-model:value="filters.type"
          placeholder="文件类型"
          style="width: 120px"
          :options="typeOptions"
          clearable
        />
        <n-select
          v-model:value="filters.tag"
          placeholder="选择标签"
          style="width: 150px"
          :options="tagOptions"
          clearable
        />
        <n-input
          v-model:value="filters.keyword"
          placeholder="搜索文件名或描述"
          style="width: 250px"
          clearable
        >
          <template #prefix>
            <n-icon><search-outline /></n-icon>
          </template>
        </n-input>
        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <n-icon><search-outline /></n-icon>
          </template>
          搜索
        </n-button>
        <n-button @click="handleReset">
          <template #icon>
            <n-icon><refresh-outline /></n-icon>
          </template>
          重置
        </n-button>
      </n-space>
    </n-card>

    <!-- 视图切换 -->
    <n-card class="view-controls">
      <n-space justify="space-between">
        <n-radio-group v-model:value="viewMode">
          <n-radio-button value="grid">网格视图</n-radio-button>
          <n-radio-button value="list">列表视图</n-radio-button>
        </n-radio-group>
        <n-space>
          <span class="total-count">共 {{ materialList.length }} 个素材</span>
          <n-button-group>
            <n-button @click="handleBatchDownload" :disabled="selectedMaterials.length === 0">
              <template #icon>
                <n-icon><download-outline /></n-icon>
              </template>
              批量下载
            </n-button>
            <n-button @click="handleBatchDelete" :disabled="selectedMaterials.length === 0" type="error">
              <template #icon>
                <n-icon><trash-outline /></n-icon>
              </template>
              批量删除
            </n-button>
          </n-button-group>
        </n-space>
      </n-space>
    </n-card>

    <!-- 素材展示区域 -->
    <n-card>
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view">
        <div class="material-grid">
          <div
            v-for="material in materialList"
            :key="material.id"
            class="material-item"
            :class="{ selected: selectedMaterials.includes(material.id) }"
            @click="handleSelectMaterial(material)"
          >
            <div class="material-preview">
              <img
                v-if="material.type === 'image'"
                :src="material.thumbnail"
                :alt="material.name"
                class="preview-image"
              />
              <div v-else-if="material.type === 'video'" class="video-preview">
                <video :src="material.url" class="preview-video" muted></video>
                <div class="video-overlay">
                  <n-icon size="40" color="white"><play-outline /></n-icon>
                </div>
              </div>
              <div v-else class="file-preview">
                <n-icon size="40" color="#666"><document-outline /></n-icon>
              </div>
              
              <!-- 选择框 -->
              <n-checkbox
                :checked="selectedMaterials.includes(material.id)"
                class="select-checkbox"
                @click.stop="toggleSelection(material.id)"
              />
              
              <!-- 操作按钮 -->
              <div class="item-actions">
                <n-button-group size="small">
                  <n-button @click.stop="handlePreview(material)" type="info">
                    <template #icon>
                      <n-icon><eye-outline /></n-icon>
                    </template>
                  </n-button>
                  <n-button @click.stop="handleShare(material)" type="warning">
                    <template #icon>
                      <n-icon><share-outline /></n-icon>
                    </template>
                  </n-button>
                  <n-button @click.stop="handleDownload(material)" type="success">
                    <template #icon>
                      <n-icon><download-outline /></n-icon>
                    </template>
                  </n-button>
                  <n-button @click.stop="handleEdit(material)" type="primary">
                    <template #icon>
                      <n-icon><create-outline /></n-icon>
                    </template>
                  </n-button>
                  <n-button @click.stop="handleDelete(material)" type="error">
                    <template #icon>
                      <n-icon><trash-outline /></n-icon>
                    </template>
                  </n-button>
                </n-button-group>
              </div>
            </div>
            
            <div class="material-info">
              <h4 class="material-name">{{ material.name }}</h4>
              <p class="material-desc">{{ material.description }}</p>
              <div class="material-meta">
                <n-tag size="small" type="info">{{ getTypeLabel(material.type) }}</n-tag>
                <n-tag v-for="tag in material.tags" :key="tag" size="small">{{ tag }}</n-tag>
                <span class="file-size">{{ formatFileSize(material.size) }}</span>
              </div>
              <div class="material-stats">
                <span>{{ material.uploadTime }}</span>
                <span>下载: {{ material.downloadCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else>
        <n-data-table
          :columns="listColumns"
          :data="materialList"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row: any) => row.id"
          :checked-row-keys="selectedMaterials"
          @update:checked-row-keys="selectedMaterials = $event"
          @update:page="handlePageChange"
        />
      </div>
    </n-card>

    <!-- 上传素材弹窗 -->
    <n-modal v-model:show="showUploadModal" preset="card" title="上传素材" style="width: 600px">
      <n-form
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        label-placement="top"
      >
        <n-form-item label="文件上传" path="files">
          <n-upload
            ref="uploadRef"
            multiple
            directory-dnd
            :file-list="uploadForm.files"
            :max="20"
            accept="image/*,video/*"
            @update:file-list="handleFileUpload"
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <cloud-upload-outline />
                </n-icon>
              </div>
              <n-text style="font-size: 16px">
                点击或者拖动文件到该区域来上传
              </n-text>
              <n-p depth="3" style="margin: 8px 0 0 0">
                支持图片和视频文件，单次最多上传20个文件
              </n-p>
            </n-upload-dragger>
          </n-upload>
        </n-form-item>
        
        <n-grid :cols="2" :x-gap="16">
          <n-form-item-gi label="分类" path="category">
            <n-select
              v-model:value="uploadForm.category"
              placeholder="请选择分类"
              :options="categoryOptions"
            />
          </n-form-item-gi>
          <n-form-item-gi label="标签">
            <n-dynamic-tags
              v-model:value="uploadForm.tags"
              placeholder="添加标签"
            />
          </n-form-item-gi>
        </n-grid>
        
        <n-form-item label="描述">
          <n-input
            v-model:value="uploadForm.description"
            type="textarea"
            placeholder="请输入素材描述"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showUploadModal = false">取消</n-button>
          <n-button type="primary" @click="handleUploadSubmit" :loading="uploading">
            上传
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 预览弹窗 -->
    <n-modal v-model:show="showPreviewModal" preset="card" title="素材预览" style="width: 80%; max-width: 1000px">
      <div v-if="previewMaterial" class="preview-container">
        <div class="preview-content">
          <img
            v-if="previewMaterial.type === 'image'"
            :src="previewMaterial.url"
            :alt="previewMaterial.name"
            class="preview-full-image"
          />
          <video
            v-else-if="previewMaterial.type === 'video'"
            :src="previewMaterial.url"
            controls
            class="preview-full-video"
          ></video>
        </div>
        <div class="preview-info">
          <h3>{{ previewMaterial.name }}</h3>
          <p>{{ previewMaterial.description }}</p>
          <div class="preview-meta">
            <n-descriptions :column="2" size="small">
              <n-descriptions-item label="文件类型">{{ getTypeLabel(previewMaterial.type) }}</n-descriptions-item>
              <n-descriptions-item label="文件大小">{{ formatFileSize(previewMaterial.size) }}</n-descriptions-item>
              <n-descriptions-item label="上传时间">{{ previewMaterial.uploadTime }}</n-descriptions-item>
              <n-descriptions-item label="下载次数">{{ previewMaterial.downloadCount }}</n-descriptions-item>
            </n-descriptions>
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 分享二维码弹窗 -->
    <n-modal v-model:show="showQrModal" preset="card" title="分享素材" style="width: 400px">
      <div class="qr-container">
        <div class="qr-code">
          <canvas ref="qrCanvas" width="200" height="200"></canvas>
        </div>
        <p class="qr-tip">扫描二维码分享此素材</p>
        <n-space justify="center">
          <n-button type="primary" @click="downloadQr">下载二维码</n-button>
        </n-space>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, markRaw } from 'vue'
import { NButton, NIcon, NTag, NSpace, NImage, useMessage } from 'naive-ui'
import {
  CloudUploadOutline,
  FolderOutline,
  SearchOutline,
  RefreshOutline,
  DownloadOutline,
  TrashOutline,
  EyeOutline,
  ShareOutline,
  CreateOutline,
  PlayOutline,
  DocumentOutline
} from '@vicons/ionicons5'
import QRCode from 'qrcode'
import { useAuthStore } from '@/stores'

const message = useMessage()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const showUploadModal = ref(false)
const showCategoryModal = ref(false)
const showPreviewModal = ref(false)
const showQrModal = ref(false)
const viewMode = ref('grid')
const selectedMaterials = ref<string[]>([])
const previewMaterial = ref<any>(null)
const qrCanvas = ref<HTMLCanvasElement>()
const currentShareUrl = ref('')

// 筛选条件
const filters = reactive({
  category: null,
  type: null,
  tag: null,
  keyword: ''
})

// 上传表单
const uploadForm = reactive({
  files: [],
  category: '',
  tags: [],
  description: ''
})

// 上传表单验证
const uploadRules = {
  files: {
    required: true,
    message: '请选择要上传的文件',
    trigger: 'change'
  },
  category: {
    required: true,
    message: '请选择分类',
    trigger: 'change'
  }
}

// 选项数据
const categoryOptions = [
  { label: '产品图片', value: 'product' },
  { label: '营销海报', value: 'poster' },
  { label: '宣传视频', value: 'video' },
  { label: 'Logo素材', value: 'logo' },
  { label: '背景图片', value: 'background' },
  { label: '图标素材', value: 'icon' },
  { label: '其他', value: 'other' }
]

const typeOptions = [
  { label: '图片', value: 'image' },
  { label: '视频', value: 'video' },
  { label: '文档', value: 'document' }
]

const tagOptions = [
  { label: '热门', value: '热门' },
  { label: '推荐', value: '推荐' },
  { label: '新品', value: '新品' },
  { label: '促销', value: '促销' },
  { label: '节日', value: '节日' }
]

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [20, 50, 100]
})

// 素材列表数据
const materialList = ref([
  {
    id: '1',
    name: '产品宣传海报.jpg',
    description: '2024年新品宣传海报，高清设计',
    type: 'image',
    category: 'poster',
    tags: ['热门', '新品'],
    url: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20product%20poster%20design&image_size=landscape_4_3',
    thumbnail: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20product%20poster%20design&image_size=square',
    size: 2048576,
    uploadTime: '2024-01-15 10:30:00',
    downloadCount: 156
  },
  {
    id: '2',
    name: '公司介绍视频.mp4',
    description: '公司形象宣传视频，时长3分钟',
    type: 'video',
    category: 'video',
    tags: ['推荐'],
    url: '/videos/company-intro.mp4',
    thumbnail: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=corporate%20video%20thumbnail&image_size=landscape_16_9',
    size: 52428800,
    uploadTime: '2024-01-12 14:20:00',
    downloadCount: 89
  },
  {
    id: '3',
    name: '品牌Logo.png',
    description: '公司品牌Logo，透明背景',
    type: 'image',
    category: 'logo',
    tags: ['品牌'],
    url: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20corporate%20logo%20design&image_size=square',
    thumbnail: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20corporate%20logo%20design&image_size=square',
    size: 512000,
    uploadTime: '2024-01-10 09:15:00',
    downloadCount: 234
  },
  {
    id: '4',
    name: '节日促销背景.jpg',
    description: '春节促销活动背景图片',
    type: 'image',
    category: 'background',
    tags: ['节日', '促销'],
    url: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=chinese%20new%20year%20promotion%20background&image_size=landscape_4_3',
    thumbnail: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=chinese%20new%20year%20promotion%20background&image_size=square',
    size: 1536000,
    uploadTime: '2024-01-08 16:45:00',
    downloadCount: 67
  }
])

// 列表视图列配置
const listColumns = [
  {
    type: 'selection'
  },
  {
    title: '预览',
    key: 'thumbnail',
    width: 80,
    render(row: any) {
      return h(NImage, {
        width: 50,
        height: 50,
        src: row.thumbnail,
        objectFit: 'cover',
        style: 'border-radius: 4px'
      })
    }
  },
  {
    title: '名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 250,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    render(row: any) {
      return h(NTag, { type: 'info', size: 'small' }, { default: () => getTypeLabel(row.type) })
    }
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
    render(row: any) {
      const category = categoryOptions.find(item => item.value === row.category)
      return category?.label || row.category
    }
  },
  {
    title: '标签',
    key: 'tags',
    width: 150,
    render(row: any) {
      return h(NSpace, { size: 'small' }, {
        default: () => row.tags.map((tag: string) => 
          h(NTag, { size: 'small', key: tag }, { default: () => tag })
        )
      })
    }
  },
  {
    title: '大小',
    key: 'size',
    width: 100,
    render(row: any) {
      return formatFileSize(row.size)
    }
  },
  {
    title: '下载次数',
    key: 'downloadCount',
    width: 100
  },
  {
    title: '上传时间',
    key: 'uploadTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row: any) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'info',
            onClick: () => handlePreview(row)
          }, {
            default: () => '预览',
            icon: () => h(NIcon, null, { default: () => h(markRaw(EyeOutline)) })
          }),
          h(NButton, {
            size: 'small',
            type: 'warning',
            onClick: () => handleShare(row)
          }, {
            default: () => '分享',
            icon: () => h(NIcon, null, { default: () => h(markRaw(ShareOutline)) })
          }),
          h(NButton, {
            size: 'small',
            type: 'success',
            onClick: () => handleDownload(row)
          }, {
            default: () => '下载',
            icon: () => h(NIcon, null, { default: () => h(markRaw(DownloadOutline)) })
          })
        ]
      })
    }
  }
]

// 方法
const getTypeLabel = (type: string) => {
  const typeMap = {
    image: '图片',
    video: '视频',
    document: '文档'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleSearch = () => {
  pagination.page = 1
  loadMaterialList()
}

const handleReset = () => {
  filters.category = null
  filters.type = null
  filters.tag = null
  filters.keyword = ''
  pagination.page = 1
  loadMaterialList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadMaterialList()
}

const loadMaterialList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.itemCount = materialList.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSelectMaterial = (material: any) => {
  toggleSelection(material.id)
}

const toggleSelection = (id: string) => {
  const index = selectedMaterials.value.indexOf(id)
  if (index > -1) {
    selectedMaterials.value.splice(index, 1)
  } else {
    selectedMaterials.value.push(id)
  }
}

const handlePreview = (material: any) => {
  previewMaterial.value = material
  showPreviewModal.value = true
}

const handleShare = async (material: any) => {
  const userId = authStore.user?.id || 'default'
  currentShareUrl.value = `${window.location.origin}/share/material/${material.id}?userId=${userId}`
  
  showQrModal.value = true
  
  // 生成二维码
  if (qrCanvas.value) {
    try {
      await QRCode.toCanvas(qrCanvas.value, currentShareUrl.value, {
        width: 200,
        margin: 2
      })
    } catch (error) {
      message.error('生成二维码失败')
    }
  }
}

const downloadQr = () => {
  if (qrCanvas.value) {
    const link = document.createElement('a')
    link.download = 'material-share-qrcode.png'
    link.href = qrCanvas.value.toDataURL()
    link.click()
  }
}

const handleDownload = (material: any) => {
  // 下载文件逻辑
  const link = document.createElement('a')
  link.href = material.url
  link.download = material.name
  link.click()
  
  // 更新下载次数
  material.downloadCount++
  message.success(`开始下载：${material.name}`)
}

const handleEdit = (material: any) => {
  // 编辑素材信息
  message.info(`编辑素材：${material.name}`)
}

const handleDelete = (material: any) => {
  // 删除素材
  message.success(`删除素材：${material.name}`)
}

const handleBatchDownload = () => {
  message.success(`批量下载 ${selectedMaterials.value.length} 个素材`)
}

const handleBatchDelete = () => {
  message.success(`批量删除 ${selectedMaterials.value.length} 个素材`)
  selectedMaterials.value = []
}

const handleFileUpload = (fileList: any[]) => {
uploadForm.files = fileList as never[]
}

const handleUploadSubmit = async () => {
  uploading.value = true
  try {
    // 模拟上传API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success(`成功上传 ${uploadForm.files.length} 个文件`)
    showUploadModal.value = false
    resetUploadForm()
    loadMaterialList()
  } catch (error) {
    message.error('上传失败')
  } finally {
    uploading.value = false
  }
}

const resetUploadForm = () => {
  Object.assign(uploadForm, {
    files: [],
    category: '',
    tags: [],
    description: ''
  })
}

// 生命周期
onMounted(() => {
  loadMaterialList()
})
</script>

<style scoped>
.material-library {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.view-controls {
  margin-bottom: 16px;
}

.total-count {
  color: #666;
  font-size: 14px;
}

.grid-view {
  padding: 16px 0;
}

.material-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.material-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.material-item:hover {
  border-color: #18a058;
  box-shadow: 0 4px 12px rgba(24, 160, 88, 0.15);
}

.material-item.selected {
  border-color: #18a058;
  box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.2);
}

.material-preview {
  position: relative;
  height: 200px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.select-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.item-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.material-item:hover .item-actions {
  opacity: 1;
}

.material-info {
  padding: 12px;
}

.material-name {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-desc {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  height: 32px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.material-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.material-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.preview-container {
  display: flex;
  gap: 20px;
}

.preview-content {
  flex: 1;
  text-align: center;
}

.preview-full-image,
.preview-full-video {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.preview-info {
  width: 300px;
  padding-left: 20px;
  border-left: 1px solid #e0e0e0;
}

.preview-info h3 {
  margin: 0 0 12px 0;
  color: #333;
}

.preview-info p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.qr-container {
  text-align: center;
  padding: 20px;
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.qr-tip {
  color: #666;
  margin-bottom: 16px;
}
</style>