<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    title="导入客户数据"
    class="import-modal"
    style="width: 800px;"
  >
    <template #header>
      <div class="modal-header">
        <n-icon :component="UploadOutlined" />
        <span>导入客户数据</span>
      </div>
    </template>

    <div class="modal-content">
      <!-- 步骤指示器 -->
      <n-steps
        :current="currentStep"
        :status="stepStatus"
        size="small"
        class="import-steps"
      >
        <n-step title="选择文件" description="上传Excel文件" />
        <n-step title="数据预览" description="检查导入数据" />
        <n-step title="导入完成" description="查看导入结果" />
      </n-steps>

      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="upload-section">
          <div class="section-title">
            <n-icon :component="FileExcelOutlined" />
            <span>上传Excel文件</span>
          </div>

          <!-- 模板下载 -->
          <div class="template-download">
            <n-alert type="info" :show-icon="false" class="template-alert">
              <template #header>
                <div class="alert-header">
                  <n-icon :component="InfoCircleOutlined" />
                  <span>导入说明</span>
                </div>
              </template>
              <div class="template-content">
                <p>请按照模板格式准备数据，支持的文件格式：.xlsx、.xls</p>
                <div class="template-actions">
                  <n-button
                    type="primary"
                    ghost
                    size="small"
                    :loading="downloadingTemplate"
                    @click="downloadTemplate"
                  >
                    <template #icon>
                      <n-icon :component="DownloadOutlined" />
                    </template>
                    下载导入模板
                  </n-button>
                  <n-button
                    text
                    size="small"
                    @click="showFormatHelp = true"
                  >
                    <template #icon>
                      <n-icon :component="QuestionCircleOutlined" />
                    </template>
                    格式说明
                  </n-button>
                </div>
              </div>
            </n-alert>
          </div>

          <!-- 文件上传区域 -->
          <n-upload
            ref="uploadRef"
            :file-list="fileList"
            :max="1"
            accept=".xlsx,.xls"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :on-finish="handleUploadFinish"
            :on-error="handleUploadError"
            :custom-request="customRequest"
            class="upload-area"
          >
            <n-upload-dragger>
              <div class="upload-content">
                <div class="upload-icon">
                  <n-icon :component="InboxOutlined" size="48" />
                </div>
                <div class="upload-text">
                  <p class="upload-hint">点击或拖拽文件到此区域上传</p>
                  <p class="upload-desc">支持 .xlsx、.xls 格式，文件大小不超过 10MB</p>
                </div>
              </div>
            </n-upload-dragger>
          </n-upload>

          <!-- 上传进度 -->
          <div v-if="uploading" class="upload-progress">
            <n-progress
              type="line"
              :percentage="uploadProgress"
              :show-indicator="true"
              processing
            />
            <p class="progress-text">正在上传文件...</p>
          </div>
        </div>
      </div>

      <!-- 步骤2: 数据预览 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="preview-section">
          <div class="section-title">
            <n-icon :component="EyeOutlined" />
            <span>数据预览</span>
          </div>

          <!-- 导入统计 -->
          <div class="import-stats">
            <n-space>
              <n-statistic label="总记录数" :value="previewData.total" />
              <n-statistic label="有效记录" :value="previewData.valid" />
              <n-statistic label="错误记录" :value="previewData.errors" />
            </n-space>
          </div>

          <!-- 错误提示 -->
          <div v-if="previewData.errors > 0" class="error-summary">
            <n-alert type="warning" :show-icon="true">
              <template #header>
                <span>发现 {{ previewData.errors }} 条错误记录</span>
              </template>
              <div class="error-details">
                <p>请检查以下问题：</p>
                <ul>
                  <li v-for="error in errorSummary" :key="error.type">
                    {{ error.message }} ({{ error.count }}条)
                  </li>
                </ul>
              </div>
            </n-alert>
          </div>

          <!-- 数据表格 -->
          <div class="preview-table">
            <n-data-table
              :columns="previewColumns"
              :data="previewData.rows"
              :pagination="{
                pageSize: 10,
                showSizePicker: false,
                showQuickJumper: true
              }"
              :scroll-x="1200"
              size="small"
              class="preview-data-table"
            />
          </div>

          <!-- 导入选项 -->
          <div class="import-options">
            <div class="section-title">
              <n-icon :component="SettingOutlined" />
              <span>导入选项</span>
            </div>
            <n-form
              :model="importOptions"
              label-placement="left"
              label-width="120px"
              size="small"
            >
              <n-form-item label="重复数据处理">
                <n-radio-group v-model:value="importOptions.duplicateHandling">
                  <n-space>
                    <n-radio value="skip">跳过重复</n-radio>
                    <n-radio value="update">更新覆盖</n-radio>
                    <n-radio value="create">创建新记录</n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
              <n-form-item label="错误数据处理">
                <n-radio-group v-model:value="importOptions.errorHandling">
                  <n-space>
                    <n-radio value="skip">跳过错误记录</n-radio>
                    <n-radio value="abort">遇错终止导入</n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
              <n-form-item label="导入后操作">
                <n-checkbox-group v-model:value="importOptions.postActions">
                  <n-space>
                    <n-checkbox value="notify">发送通知</n-checkbox>
                    <n-checkbox value="assign">自动分配</n-checkbox>
                    <n-checkbox value="tag">添加标签</n-checkbox>
                  </n-space>
                </n-checkbox-group>
              </n-form-item>
            </n-form>
          </div>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <div class="result-header">
            <n-result
              :status="importResult.success ? 'success' : 'error'"
              :title="importResult.success ? '导入成功' : '导入失败'"
              :description="importResult.message"
            >
              <template #icon>
                <n-icon
                  :component="importResult.success ? CheckCircleOutlined : CloseCircleOutlined"
                  size="48"
                />
              </template>
            </n-result>
          </div>

          <!-- 导入统计 -->
          <div class="result-stats">
            <n-space justify="center">
              <n-statistic
                label="成功导入"
                :value="importResult.successCount"
                :value-style="{ color: '#52c41a' }"
              />
              <n-statistic
                label="失败记录"
                :value="importResult.failureCount"
                :value-style="{ color: '#ff4d4f' }"
              />
              <n-statistic
                label="跳过记录"
                :value="importResult.skipCount"
                :value-style="{ color: '#faad14' }"
              />
            </n-space>
          </div>

          <!-- 失败记录详情 -->
          <div v-if="importResult.failures.length > 0" class="failure-details">
            <div class="section-title">
              <n-icon :component="ExclamationCircleOutlined" />
              <span>失败记录详情</span>
            </div>
            <n-data-table
              :columns="failureColumns"
              :data="importResult.failures"
              :pagination="{ pageSize: 5 }"
              size="small"
              class="failure-table"
            />
          </div>

          <!-- 操作建议 -->
          <div class="action-suggestions">
            <n-alert type="info" :show-icon="false">
              <template #header>
                <span>后续操作建议</span>
              </template>
              <div class="suggestions">
                <p v-if="importResult.successCount > 0">
                  ✓ {{ importResult.successCount }} 条客户数据已成功导入系统
                </p>
                <p v-if="importResult.failureCount > 0">
                  ⚠ 请修正失败记录中的问题后重新导入
                </p>
                <p v-if="importResult.skipCount > 0">
                  ℹ {{ importResult.skipCount }} 条重复记录已跳过
                </p>
              </div>
            </n-alert>
          </div>
        </div>
      </div>
    </div>

    <template #action>
      <div class="modal-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="fileList.length === 0 || uploading"
          :loading="parsing"
          @click="handleNext"
        >
          下一步
        </n-button>
        <n-button
          v-if="currentStep === 2"
          @click="handlePrevious"
        >
          上一步
        </n-button>
        <n-button
          v-if="currentStep === 2"
          type="primary"
          :disabled="previewData.valid === 0"
          :loading="importing"
          @click="handleImport"
        >
          开始导入
        </n-button>
        <n-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleFinish"
        >
          完成
        </n-button>
      </div>
    </template>
  </n-modal>

  <!-- 格式说明弹窗 -->
  <n-modal
    v-model:show="showFormatHelp"
    preset="dialog"
    title="导入格式说明"
    style="width: 600px;"
  >
    <div class="format-help">
      <h4>Excel文件格式要求：</h4>
      <n-table size="small" class="format-table">
        <thead>
          <tr>
            <th>字段名</th>
            <th>是否必填</th>
            <th>格式说明</th>
            <th>示例</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="field in formatFields" :key="field.name">
            <td>{{ field.name }}</td>
            <td>
              <n-tag :type="field.required ? 'error' : 'default'" size="small">
                {{ field.required ? '必填' : '可选' }}
              </n-tag>
            </td>
            <td>{{ field.format }}</td>
            <td>{{ field.example }}</td>
          </tr>
        </tbody>
      </n-table>
      <div class="format-notes">
        <h4>注意事项：</h4>
        <ul>
          <li>Excel文件第一行必须为字段标题</li>
          <li>手机号码格式：11位数字，以1开头</li>
          <li>邮箱格式：符合邮箱地址规范</li>
          <li>日期格式：YYYY-MM-DD 或 YYYY/MM/DD</li>
          <li>性别：男/女 或 M/F</li>
        </ul>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import {
  NModal,
  NSteps,
  NStep,
  NUpload,
  NUploadDragger,
  NButton,
  NIcon,
  NAlert,
  NProgress,
  NDataTable,
  NStatistic,
  NSpace,
  NForm,
  NFormItem,
  NRadioGroup,
  NRadio,
  NCheckboxGroup,
  NCheckbox,
  NResult,
  NTable,
  NTag,
  useMessage
} from 'naive-ui'
import type { UploadFileInfo, DataTableColumns } from 'naive-ui'
import {
  CloudUpload as UploadOutlined,
  Document as FileExcelOutlined,
  InformationCircle as InfoCircleOutlined,
  Download as DownloadOutlined,
  HelpCircle as QuestionCircleOutlined,
  Archive as InboxOutlined,
  Eye as EyeOutlined,
  Settings as SettingOutlined,
  CheckmarkCircle as CheckCircleOutlined,
  CloseCircle as CloseCircleOutlined,
  Warning as ExclamationCircleOutlined
} from '@vicons/ionicons5'

// 导入结果接口
interface ImportResult {
  success: boolean
  message: string
  successCount: number
  failureCount: number
  skipCount: number
  failures: Array<{
    row: number
    data: Record<string, any>
    errors: string[]
  }>
}

// 预览数据接口
interface PreviewData {
  total: number
  valid: number
  errors: number
  rows: Array<Record<string, any>>
}

// 导入选项接口
interface ImportOptions {
  duplicateHandling: 'skip' | 'update' | 'create'
  errorHandling: 'skip' | 'abort'
  postActions: string[]
}

// Props
interface Props {
  show: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false
})

// Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', result: ImportResult): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// Message
const message = useMessage()

// Refs
const uploadRef = ref()
const fileList = ref<UploadFileInfo[]>([])
const currentStep = ref(1)
const stepStatus = ref<'process' | 'finish' | 'error' | 'wait'>('process')
const showFormatHelp = ref(false)

// 状态
const uploading = ref(false)
const uploadProgress = ref(0)
const parsing = ref(false)
const importing = ref(false)
const downloadingTemplate = ref(false)

// Computed
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 预览数据
const previewData = reactive<PreviewData>({
  total: 0,
  valid: 0,
  errors: 0,
  rows: []
})

// 错误汇总
const errorSummary = ref<Array<{ type: string; message: string; count: number }>>([])

// 导入选项
const importOptions = reactive<ImportOptions>({
  duplicateHandling: 'skip',
  errorHandling: 'skip',
  postActions: ['notify']
})

// 导入结果
const importResult = reactive<ImportResult>({
  success: false,
  message: '',
  successCount: 0,
  failureCount: 0,
  skipCount: 0,
  failures: []
})

// 预览表格列配置
const previewColumns: DataTableColumns = [
  {
    title: '行号',
    key: 'rowNumber',
    width: 60,
    render: (_, index) => index + 1
  },
  {
    title: '客户姓名',
    key: 'name',
    width: 100,
    render: (row: any) => {
      return row.errors?.includes('name') ? 
        h('span', { style: 'color: #ff4d4f' }, row.name || '缺失') :
        row.name
    }
  },
  {
    title: '手机号码',
    key: 'phone',
    width: 120,
    render: (row: any) => {
      return row.errors?.includes('phone') ? 
        h('span', { style: 'color: #ff4d4f' }, row.phone || '格式错误') :
        row.phone
    }
  },
  {
    title: '邮箱',
    key: 'email',
    width: 150,
    render: (row: any) => {
      return row.errors?.includes('email') ? 
        h('span', { style: 'color: #ff4d4f' }, row.email || '格式错误') :
        row.email
    }
  },
  {
    title: '性别',
    key: 'gender',
    width: 80
  },
  {
    title: '年龄',
    key: 'age',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row: any) => {
      if (row.errors && row.errors.length > 0) {
        return h(NTag, { type: 'error', size: 'small' }, { default: () => '错误' })
      }
      return h(NTag, { type: 'success', size: 'small' }, { default: () => '正常' })
    }
  }
]

// 失败记录表格列配置
const failureColumns: DataTableColumns = [
  {
    title: '行号',
    key: 'row',
    width: 60
  },
  {
    title: '客户姓名',
    key: 'data.name',
    width: 100
  },
  {
    title: '错误信息',
    key: 'errors',
    render: (row: any) => {
      return h('div', row.errors.join('; '))
    }
  }
]

// 格式字段说明
const formatFields = [
  { name: '客户姓名', required: true, format: '文本，2-20个字符', example: '张三' },
  { name: '手机号码', required: true, format: '11位数字', example: '13800138000' },
  { name: '邮箱', required: false, format: '邮箱格式', example: '<EMAIL>' },
  { name: '性别', required: false, format: '男/女', example: '男' },
  { name: '年龄', required: false, format: '数字，18-100', example: '30' },
  { name: '地址', required: false, format: '文本', example: '北京市朝阳区' },
  { name: '备注', required: false, format: '文本', example: '重要客户' }
]

// 方法
const downloadTemplate = async () => {
  try {
    downloadingTemplate.value = true
    // TODO: 调用API下载模板
    // await customerService.downloadImportTemplate()
    
    // 模拟下载
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    message.error('下载模板失败')
  } finally {
    downloadingTemplate.value = false
  }
}

const beforeUpload = (data: { file: UploadFileInfo }) => {
  const file = data.file.file
  if (!file) return false
  
  // 检查文件类型
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ]
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 Excel 文件格式')
    return false
  }
  
  // 检查文件大小（10MB）
  if (file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过 10MB')
    return false
  }
  
  return true
}

const customRequest = ({ file, onProgress, onFinish, onError }: any) => {
  uploading.value = true
  uploadProgress.value = 0
  
  // 模拟上传进度
  const timer = setInterval(() => {
    uploadProgress.value += 10
    onProgress({ percent: uploadProgress.value })
    
    if (uploadProgress.value >= 100) {
      clearInterval(timer)
      uploading.value = false
      onFinish()
    }
  }, 200)
}

const handleUploadFinish = () => {
  message.success('文件上传成功')
}

const handleUploadError = () => {
  uploading.value = false
  message.error('文件上传失败')
}

const handleRemove = () => {
  fileList.value = []
  resetPreviewData()
}

const resetPreviewData = () => {
  Object.assign(previewData, {
    total: 0,
    valid: 0,
    errors: 0,
    rows: []
  })
  errorSummary.value = []
}

const handleNext = async () => {
  if (fileList.value.length === 0) {
    message.error('请先上传文件')
    return
  }
  
  try {
    parsing.value = true
    
    // TODO: 调用API解析文件
    // const response = await customerService.parseImportFile(fileList.value[0])
    
    // 模拟解析结果
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟预览数据
    const mockData = [
      { name: '张三', phone: '13800138001', email: '<EMAIL>', gender: '男', age: 30 },
      { name: '李四', phone: '13800138002', email: '<EMAIL>', gender: '女', age: 25 },
      { name: '', phone: '138001380', email: 'invalid-email', gender: '男', age: 35, errors: ['name', 'phone', 'email'] },
      { name: '王五', phone: '13800138004', email: '<EMAIL>', gender: '男', age: 28 }
    ]
    
    Object.assign(previewData, {
      total: mockData.length,
      valid: mockData.filter(item => !item.errors).length,
      errors: mockData.filter(item => item.errors).length,
      rows: mockData
    })
    
    errorSummary.value = [
      { type: 'name', message: '客户姓名不能为空', count: 1 },
      { type: 'phone', message: '手机号码格式错误', count: 1 },
      { type: 'email', message: '邮箱格式错误', count: 1 }
    ]
    
    currentStep.value = 2
  } catch (error) {
    console.error('解析文件失败:', error)
    message.error('解析文件失败')
  } finally {
    parsing.value = false
  }
}

const handlePrevious = () => {
  currentStep.value = 1
}

const handleImport = async () => {
  try {
    importing.value = true
    
    // TODO: 调用API导入数据
    // const response = await customerService.importCustomers({
    //   data: previewData.rows.filter(row => !row.errors),
    //   options: importOptions
    // })
    
    // 模拟导入结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    Object.assign(importResult, {
      success: true,
      message: '客户数据导入完成',
      successCount: previewData.valid,
      failureCount: previewData.errors,
      skipCount: 0,
      failures: previewData.rows
        .filter(row => row.errors)
        .map((row, index) => ({
          row: index + 1,
          data: row,
          errors: row.errors || []
        }))
    })
    
    currentStep.value = 3
    stepStatus.value = 'finish'
  } catch (error) {
    console.error('导入失败:', error)
    Object.assign(importResult, {
      success: false,
      message: '导入过程中发生错误',
      successCount: 0,
      failureCount: previewData.total,
      skipCount: 0,
      failures: []
    })
    currentStep.value = 3
    stepStatus.value = 'error'
  } finally {
    importing.value = false
  }
}

const handleFinish = () => {
  emit('success', importResult)
  handleCancel()
}

const handleCancel = () => {
  emit('cancel')
  showModal.value = false
  
  // 重置状态
  currentStep.value = 1
  stepStatus.value = 'process'
  fileList.value = []
  resetPreviewData()
  Object.assign(importOptions, {
    duplicateHandling: 'skip',
    errorHandling: 'skip',
    postActions: ['notify']
  })
}
</script>

<style scoped>
.import-modal {
  max-height: 90vh;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.modal-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.import-steps {
  margin-bottom: 24px;
}

.step-content {
  min-height: 400px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f2f5;
}

.upload-section {
  margin-bottom: 24px;
}

.template-download {
  margin-bottom: 24px;
}

.template-alert {
  border-radius: 6px;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.template-content p {
  margin-bottom: 12px;
  color: #595959;
}

.template-actions {
  display: flex;
  gap: 12px;
}

.upload-area {
  margin-bottom: 16px;
}

.upload-content {
  text-align: center;
  padding: 40px 20px;
}

.upload-icon {
  margin-bottom: 16px;
  color: #d9d9d9;
}

.upload-hint {
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
}

.upload-desc {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.upload-progress {
  margin-top: 16px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #595959;
  font-size: 14px;
}

.preview-section {
  margin-bottom: 24px;
}

.import-stats {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f0f2f5;
}

.error-summary {
  margin-bottom: 16px;
}

.error-details ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.error-details li {
  margin-bottom: 4px;
  color: #595959;
}

.preview-table {
  margin-bottom: 24px;
}

.preview-data-table {
  border-radius: 6px;
}

.import-options {
  margin-bottom: 16px;
}

.result-section {
  text-align: center;
}

.result-header {
  margin-bottom: 24px;
}

.result-stats {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 6px;
}

.failure-details {
  margin-bottom: 24px;
  text-align: left;
}

.failure-table {
  border-radius: 6px;
}

.action-suggestions {
  text-align: left;
}

.suggestions p {
  margin-bottom: 8px;
  color: #595959;
}

.suggestions p:last-child {
  margin-bottom: 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.modal-actions .n-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0 24px;
  height: 36px;
}

.format-help {
  max-height: 60vh;
  overflow-y: auto;
}

.format-help h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-weight: 600;
}

.format-table {
  margin-bottom: 24px;
  border-radius: 6px;
}

.format-table th,
.format-table td {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f2f5;
}

.format-table th {
  background: #fafbfc;
  font-weight: 600;
  color: #262626;
}

.format-notes ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.format-notes li {
  margin-bottom: 8px;
  color: #595959;
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar,
.format-help::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track,
.format-help::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb,
.format-help::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.format-help::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .import-modal {
    width: 95vw !important;
    max-width: 95vw !important;
  }
  
  .template-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .template-actions .n-button {
    width: 100%;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .modal-actions .n-button {
    width: 100%;
  }
  
  .import-stats .n-space {
    flex-direction: column;
    gap: 16px;
  }
  
  .result-stats .n-space {
    flex-direction: column;
    gap: 16px;
  }
}
</style>