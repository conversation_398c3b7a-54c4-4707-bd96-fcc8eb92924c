{"migrationId": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "timestamp": "2025-08-18T07:22:08.391Z", "config": {"batchSize": 100, "enableLogging": true, "validateData": true, "incrementalMode": false}, "summary": {"totalTables": 21, "successfulTables": 16, "failedTables": 5, "totalRecords": 165, "migratedRecords": 154, "failedRecords": 11}, "tableStats": [{"tableName": "users", "totalRecords": 3, "migratedRecords": 3, "failedRecords": 0, "startTime": "2025-08-18T07:21:24.950Z", "errors": [], "endTime": "2025-08-18T07:21:29.579Z", "duration": 4629}, {"tableName": "roles", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:21:29.584Z", "errors": [], "endTime": "2025-08-18T07:21:31.803Z", "duration": 2219}, {"tableName": "permissions", "totalRecords": 77, "migratedRecords": 77, "failedRecords": 0, "startTime": "2025-08-18T07:21:31.804Z", "errors": [], "endTime": "2025-08-18T07:21:33.643Z", "duration": 1839}, {"tableName": "role_permissions", "totalRecords": 6, "migratedRecords": 6, "failedRecords": 0, "startTime": "2025-08-18T07:21:33.645Z", "errors": [], "endTime": "2025-08-18T07:21:35.432Z", "duration": 1787}, {"tableName": "user_roles", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:35.435Z", "errors": []}, {"tableName": "option_categories", "totalRecords": 15, "migratedRecords": 15, "failedRecords": 0, "startTime": "2025-08-18T07:21:35.742Z", "errors": [], "endTime": "2025-08-18T07:21:37.629Z", "duration": 1887}, {"tableName": "option_items", "totalRecords": 42, "migratedRecords": 42, "failedRecords": 0, "startTime": "2025-08-18T07:21:37.632Z", "errors": [], "endTime": "2025-08-18T07:21:40.453Z", "duration": 2821}, {"tableName": "customers", "totalRecords": 5, "migratedRecords": 5, "failedRecords": 0, "startTime": "2025-08-18T07:21:40.457Z", "errors": [], "endTime": "2025-08-18T07:21:42.688Z", "duration": 2231}, {"tableName": "customer_follow_records", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:42.690Z", "errors": []}, {"tableName": "marketing_campaigns", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:21:43.127Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`marketing_campaigns`, CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE)"], "endTime": "2025-08-18T07:21:45.912Z", "duration": 2785}, {"tableName": "campaign_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:45.914Z", "errors": []}, {"tableName": "campaign_shares", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:46.518Z", "errors": []}, {"tableName": "meetings", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:21:47.272Z", "errors": ["Field 'meeting_type' doesn't have a default value"], "endTime": "2025-08-18T07:21:51.032Z", "duration": 3760}, {"tableName": "meeting_participants", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:51.034Z", "errors": []}, {"tableName": "pool_rules", "totalRecords": 2, "migratedRecords": 0, "failedRecords": 2, "startTime": "2025-08-18T07:21:51.323Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`pool_rules`, CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:21:56.390Z", "duration": 5067}, {"tableName": "customer_behaviors", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:56.393Z", "errors": []}, {"tableName": "wechat_customer_tracking", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:56.923Z", "errors": []}, {"tableName": "sales_funnel_stats", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:57.925Z", "errors": []}, {"tableName": "customer_value_analysis", "totalRecords": 0, "migratedRecords": 0, "failedRecords": 0, "startTime": "2025-08-18T07:21:59.762Z", "errors": []}, {"tableName": "follow_ups", "totalRecords": 3, "migratedRecords": 0, "failedRecords": 3, "startTime": "2025-08-18T07:22:00.783Z", "errors": ["Field 'due_date' doesn't have a default value"], "endTime": "2025-08-18T07:22:03.871Z", "duration": 3088}, {"tableName": "public_pool", "totalRecords": 1, "migratedRecords": 0, "failedRecords": 1, "startTime": "2025-08-18T07:22:03.874Z", "errors": ["Cannot add or update a child row: a foreign key constraint fails (`workchat_admin`.`public_pool`, CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL)"], "endTime": "2025-08-18T07:22:08.388Z", "duration": 4514}], "logs": [{"id": "7111ac48-c8fc-46b3-97db-0d7e2c759775", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "users", "operation": "migrate", "status": "completed", "records_count": 3, "start_time": "2025-08-18T07:21:24.950Z", "end_time": "2025-08-18T07:21:29.579Z", "duration_ms": 4629}, {"id": "1d9481a7-287d-4d1b-8bbc-c6d3d9ab06fe", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "roles", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:21:29.584Z", "end_time": "2025-08-18T07:21:31.803Z", "duration_ms": 2219}, {"id": "90056218-978b-4919-a9e9-ea43c2db8585", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "permissions", "operation": "migrate", "status": "completed", "records_count": 77, "start_time": "2025-08-18T07:21:31.804Z", "end_time": "2025-08-18T07:21:33.643Z", "duration_ms": 1839}, {"id": "3d333743-ef87-4394-b0c1-23e42efa246e", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "role_permissions", "operation": "migrate", "status": "completed", "records_count": 6, "start_time": "2025-08-18T07:21:33.645Z", "end_time": "2025-08-18T07:21:35.432Z", "duration_ms": 1787}, {"id": "e0928127-378d-41fd-a9cb-70209df2d455", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "user_roles", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:35.435Z", "end_time": "2025-08-18T07:21:35.741Z", "duration_ms": 306}, {"id": "087dc05b-7380-4c60-a9c6-27ce2f9a8692", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "option_categories", "operation": "migrate", "status": "completed", "records_count": 15, "start_time": "2025-08-18T07:21:35.742Z", "end_time": "2025-08-18T07:21:37.629Z", "duration_ms": 1887}, {"id": "ca3db811-bdf9-4d2d-8bda-f42c94dc54fd", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "option_items", "operation": "migrate", "status": "completed", "records_count": 42, "start_time": "2025-08-18T07:21:37.632Z", "end_time": "2025-08-18T07:21:40.453Z", "duration_ms": 2821}, {"id": "76b0715f-717b-4745-bc6a-0087cd2645bf", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "customers", "operation": "migrate", "status": "completed", "records_count": 5, "start_time": "2025-08-18T07:21:40.457Z", "end_time": "2025-08-18T07:21:42.688Z", "duration_ms": 2231}, {"id": "03c4a9dc-cd2f-450b-bfad-03d7d1869d38", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "customer_follow_records", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:42.690Z", "end_time": "2025-08-18T07:21:43.127Z", "duration_ms": 437}, {"id": "9ff5ce7b-3352-45bb-85b4-00edb9e0f611", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "marketing_campaigns", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:43.127Z", "end_time": "2025-08-18T07:21:45.912Z", "duration_ms": 2785}, {"id": "73e898ad-0656-49e8-a2a8-796b5a01c44e", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "campaign_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:45.915Z", "end_time": "2025-08-18T07:21:46.518Z", "duration_ms": 603}, {"id": "9e5e8838-c884-4ff0-82db-94e07a5d618f", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "campaign_shares", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:46.518Z", "end_time": "2025-08-18T07:21:47.271Z", "duration_ms": 753}, {"id": "520bc52b-aca3-412a-96a5-954d65069eb9", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "meetings", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:47.272Z", "end_time": "2025-08-18T07:21:51.032Z", "duration_ms": 3760}, {"id": "6573f076-22e0-4de5-8774-b46e4c44beb3", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "meeting_participants", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:51.034Z", "end_time": "2025-08-18T07:21:51.323Z", "duration_ms": 289}, {"id": "bf2909f8-8ec9-4deb-857e-4e66716af19b", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "pool_rules", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:51.323Z", "end_time": "2025-08-18T07:21:56.390Z", "duration_ms": 5067}, {"id": "7727e3d8-9af3-40ba-baac-a665086f057f", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "customer_behaviors", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:56.393Z", "end_time": "2025-08-18T07:21:56.923Z", "duration_ms": 530}, {"id": "09f977f5-68d3-491a-be01-2b9428167b1d", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "wechat_customer_tracking", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:56.923Z", "end_time": "2025-08-18T07:21:57.925Z", "duration_ms": 1002}, {"id": "f4fc4c00-3886-41b2-9fbc-113f174467e9", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "sales_funnel_stats", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:57.925Z", "end_time": "2025-08-18T07:21:59.761Z", "duration_ms": 1836}, {"id": "3b0c5266-db1a-4d57-b734-fea1d8fc0364", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "customer_value_analysis", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:21:59.762Z", "end_time": "2025-08-18T07:22:00.783Z", "duration_ms": 1021}, {"id": "b473fe4f-e6cf-479f-869a-20e5e69fa224", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "follow_ups", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:22:00.783Z", "end_time": "2025-08-18T07:22:03.871Z", "duration_ms": 3088}, {"id": "9ce55a82-f373-4a39-b383-255aef443ce4", "migration_id": "1c435548-c3e0-49db-bd5d-89e902ef9d90", "table_name": "public_pool", "operation": "migrate", "status": "completed", "records_count": 0, "start_time": "2025-08-18T07:22:03.874Z", "end_time": "2025-08-18T07:22:08.388Z", "duration_ms": 4514}]}