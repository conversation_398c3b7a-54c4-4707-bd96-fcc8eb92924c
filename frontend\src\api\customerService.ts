import { request, downloadFile, uploadFile } from '@/utils/request'

// 客户基础信息接口
export interface Customer {
  id: number
  name: string
  gender?: 'male' | 'female' | 'unknown'
  phone: string
  avatar?: string
  area?: number
  decorationType?: 'rough' | 'fine' | 'simple' | 'luxury'
  created_at?: string
  updated_at?: string
  last_follow_time?: string
  remark?: string
  address?: string
  isImportant?: boolean
  position?: string
  region?: string
  community?: string
  houseStatus?: 'delivered' | 'undelivered' | 'pending'
  status: 'potential' | 'contacted' | 'deal' | 'negotiating' | 'lost'
  assigned_to?: number
  source?: 'phone' | 'online' | 'referral' | 'store' | 'exhibition' | 'other'
  level?: 'A' | 'B' | 'C' | 'D'
  tags?: string[]
  company?: string
}

// 客户筛选条件接口
export interface CustomerFilters {
  search?: string
  level?: string[]
  status?: string[]
  source?: string[]
  assignedTo?: string
  page?: number
  page_size?: number
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 创建客户数据接口
export interface CreateCustomerData {
  name: string
  gender?: 'male' | 'female' | 'unknown'
  phone: string
  avatar?: string
  area?: number
  decorationType?: 'rough' | 'fine' | 'simple' | 'luxury'
  remark?: string
  address?: string
  isImportant?: boolean
  position?: string
  region?: string
  community?: string
  houseStatus?: 'delivered' | 'undelivered' | 'pending'
  status?: 'potential' | 'contacted' | 'deal' | 'negotiating' | 'lost'
  assigned_to?: number
  source?: 'phone' | 'online' | 'referral' | 'store' | 'exhibition' | 'other'
  level?: 'A' | 'B' | 'C' | 'D'
  tags?: string[]
  company?: string
}

// 更新客户数据接口
export interface UpdateCustomerData {
  name?: string
  gender?: 'male' | 'female' | 'unknown'
  phone?: string
  avatar?: string
  area?: number
  decorationType?: 'rough' | 'fine' | 'simple' | 'luxury'
  remark?: string
  address?: string
  isImportant?: boolean
  position?: string
  region?: string
  community?: string
  houseStatus?: 'delivered' | 'undelivered' | 'pending'
  status?: 'potential' | 'contacted' | 'deal' | 'negotiating' | 'lost'
  assigned_to?: number
  source?: 'phone' | 'online' | 'referral' | 'store' | 'exhibition' | 'other'
  level?: 'A' | 'B' | 'C' | 'D'
  tags?: string[]
  company?: string
}

// 客户服务类
export class CustomerService {
  // 获取客户列表
  static async getCustomers(filters: CustomerFilters = {}): Promise<PaginationResponse<Customer>> {
    try {
      const response = await request.get<any>('/customers', {
        params: filters
      })
      
      return {
        data: response.data.data || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        page_size: response.data.pageSize || 20,
        total_pages: response.data.totalPages || 0
      } as PaginationResponse<Customer>
    } catch (error: any) {
      throw new Error(`获取客户列表失败: ${error.message}`)
    }
  }

  // 获取客户详情
  static async getCustomer(id: number): Promise<Customer> {
    try {
      const response = await request.get<any>(`/customers/${id}`)
      return response.data
    } catch (error: any) {
      throw new Error(`获取客户详情失败: ${error.message}`)
    }
  }

  // 创建客户
  static async createCustomer(customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>): Promise<Customer> {
    try {
      const response = await request.post<any>('/customers', customerData)
      return response.data
    } catch (error: any) {
      throw new Error(`创建客户失败: ${error.message}`)
    }
  }

  // 更新客户
  static async updateCustomer(id: number, customerData: Partial<Customer>): Promise<Customer> {
    try {
      const response = await request.put<any>(`/customers/${id}`, customerData)
      return response.data
    } catch (error: any) {
      throw new Error(`更新客户失败: ${error.message}`)
    }
  }

  // 删除客户
  static async deleteCustomer(id: number): Promise<void> {
    try {
      await request.delete(`/customers/${id}`)
    } catch (error: any) {
      throw new Error(`删除客户失败: ${error.message}`)
    }
  }

  // 批量删除客户
  static async batchDeleteCustomers(ids: number[]): Promise<void> {
    try {
      await request.delete('/customers/batch', {
        data: { ids }
      })
    } catch (error: any) {
      throw new Error(`批量删除客户失败: ${error.message}`)
    }
  }

  // 分配客户
  static async assignCustomer(customerId: number, userId: string): Promise<void> {
    try {
      await request.put(`/customers/${customerId}`, {
        assigned_to: userId
      })
    } catch (error: any) {
      throw new Error(`分配客户失败: ${error.message}`)
    }
  }

  // 批量分配客户
  static async batchAssignCustomers(customerIds: number[], userId: string): Promise<void> {
    try {
      await request.put('/customers/batch', {
        ids: customerIds,
        assigned_to: userId
      })
    } catch (error: any) {
      throw new Error(`批量分配客户失败: ${error.message}`)
    }
  }

  // 批量更新客户
  static async batchUpdateCustomers(ids: number[], data: UpdateCustomerData): Promise<void> {
    try {
      await request.put('/customers/batch', {
        ids,
        ...data
      })
    } catch (error: any) {
      throw new Error(`批量更新客户失败: ${error.message}`)
    }
  }

  // 获取客户统计
  static async getCustomerStats(): Promise<{
    total: number
    byStatus: Record<string, number>
    byLevel: Record<string, number>
    bySource: Record<string, number>
  }> {
    try {
      const response = await request.get<any>('/customers/stats')
      return response.data
    } catch (error: any) {
      throw new Error(`获取客户统计失败: ${error.message}`)
    }
  }

  // 获取客户标签
  static async getCustomerTags(): Promise<string[]> {
    try {
      const response = await request.get<any>('/customers/tags')
      return response.data
    } catch (error: any) {
      throw new Error(`获取客户标签失败: ${error.message}`)
    }
  }

  // 创建客户标签
  static async createCustomerTag(name: string): Promise<void> {
    try {
      await request.post('/customers/tags', { name })
    } catch (error: any) {
      throw new Error(`创建客户标签失败: ${error.message}`)
    }
  }

  // 为客户添加标签
  static async addCustomerTag(customerId: number, tagName: string): Promise<void> {
    try {
      await request.post(`/customers/${customerId}/tags`, { tagName })
    } catch (error: any) {
      throw new Error(`添加客户标签失败: ${error.message}`)
    }
  }

  // 移除客户标签
  static async removeCustomerTag(customerId: number, tagName: string): Promise<void> {
    try {
      await request.delete(`/customers/${customerId}/tags/${tagName}`)
    } catch (error: any) {
      throw new Error(`移除客户标签失败: ${error.message}`)
    }
  }

  // 搜索客户
  static async searchCustomers(keyword: string): Promise<Customer[]> {
    try {
      const response = await request.get<any>('/customers/search', {
        params: { keyword }
      })
      return response.data
    } catch (error: any) {
      throw new Error(`搜索客户失败: ${error.message}`)
    }
  }

  // 导出客户数据
  static async exportCustomers(filters?: CustomerFilters): Promise<void> {
    try {
      await downloadFile('/customers/export', '客户数据.xlsx', {
        params: filters
      })
    } catch (error: any) {
      throw new Error(`导出客户数据失败: ${error.message}`)
    }
  }

  // 导入客户数据
  static async importCustomers(file: File): Promise<{
    success: number
    failed: number
    errors?: string[]
  }> {
    try {
      const response = await uploadFile('/customers/import', file)
      return response.data
    } catch (error: any) {
      throw new Error(`导入客户数据失败: ${error.message}`)
    }
  }

  // 下载导入模板
  static async downloadImportTemplate(): Promise<void> {
    try {
      await downloadFile('/customers/import/template', '客户导入模板.xlsx')
    } catch (error: any) {
      throw new Error(`下载导入模板失败: ${error.message}`)
    }
  }
}

export default CustomerService