#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
创建数据库、表结构和默认admin用户
"""

import sys
import os
import asyncio
import pymysql
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from app.config import settings
from app.database import engine, create_tables
from app.utils.security import get_password_hash
from app.models import *  # 导入所有模型

def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=settings.database.host,
            port=settings.database.port,
            user=settings.database.username,
            password=settings.database.password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{settings.database.database}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 '{settings.database.database}' 创建成功或已存在")
        
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

async def init_database():
    """初始化数据库表结构"""
    try:
        print("🔄 开始创建数据库表结构...")
        await create_tables()
        print("✅ 数据库表结构创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建数据库表结构失败: {e}")
        return False

async def create_admin_user():
    """创建默认admin用户"""
    try:
        from app.database import AsyncSessionLocal
        from app.models.user import User
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as session:
            # 检查admin用户是否已存在
            result = await session.execute(
                select(User).where(User.username == 'admin')
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("📝 admin用户已存在，更新密码...")
                # 更新密码
                existing_user.password = get_password_hash('admin123')
                await session.commit()
                print("✅ admin用户密码更新成功")
            else:
                print("👤 创建admin用户...")
                # 创建新用户
                admin_user = User(
                    username='admin',
                    password=get_password_hash('admin123'),
                    name='系统管理员',
                    role='admin',
                    status='active'
                )
                session.add(admin_user)
                await session.commit()
                print("✅ admin用户创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 创建admin用户失败: {e}")
        return False

async def test_login():
    """测试admin用户登录"""
    try:
        from app.utils.security import verify_password
        from app.database import AsyncSessionLocal
        from app.models.user import User
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(User).where(User.username == 'admin')
            )
            user = result.scalar_one_or_none()
            
            if user:
                is_valid = verify_password('admin123', user.password)
                if is_valid:
                    print("✅ admin用户登录测试成功")
                    print(f"用户信息: ID={user.id}, 用户名={user.username}, 姓名={user.name}")
                    return True
                else:
                    print("❌ admin用户密码验证失败")
                    return False
            else:
                print("❌ admin用户不存在")
                return False
    except Exception as e:
        print(f"❌ 测试登录失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    print(f"📊 数据库配置: {settings.database.host}:{settings.database.port}/{settings.database.database}")
    
    # 1. 创建数据库
    if not create_database_if_not_exists():
        return False
    
    # 2. 创建表结构
    if not await init_database():
        return False
    
    # 3. 创建admin用户
    if not await create_admin_user():
        return False
    
    # 4. 测试登录
    if not await test_login():
        return False
    
    print("🎉 数据库初始化完成！")
    print("📝 默认管理员账号: admin / admin123")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n✅ 数据库初始化成功，可以启动应用程序了！")
        else:
            print("\n❌ 数据库初始化失败，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 初始化过程中发生错误: {e}")
        sys.exit(1)