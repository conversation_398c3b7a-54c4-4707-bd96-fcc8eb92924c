// 营销活动示例数据
export const mockCampaigns = [
  {
    id: 1,
    name: '双11狂欢秒杀活动',
    type: 'flash_sale',
    description: '限时秒杀，iPhone 15 Pro Max 直降2000元！数量有限，先到先得！',
    status: 'active',
    start_time: '2024-11-11T00:00:00Z',
    end_time: '2024-11-11T23:59:59Z',
    target_audience: '全体用户',
    budget: 500000,
    participants_count: 1256,
    conversion_rate: 0.15,
    created_at: '2024-11-01T10:00:00Z',
    updated_at: '2024-11-10T15:30:00Z',
    config: {
      product_name: 'iPhone 15 Pro Max 256GB',
      original_price: 9999,
      sale_price: 7999,
      stock_quantity: 100,
      max_purchase_per_user: 1
    }
  },
  {
    id: 2,
    name: '幸运大转盘抽奖',
    type: 'lottery',
    description: '每日签到即可参与抽奖，iPhone、iPad、AirPods等豪礼等你来拿！',
    status: 'active',
    start_time: '2024-11-01T00:00:00Z',
    end_time: '2024-11-30T23:59:59Z',
    target_audience: '注册用户',
    budget: 200000,
    participants_count: 3456,
    conversion_rate: 0.08,
    created_at: '2024-10-25T14:20:00Z',
    updated_at: '2024-11-08T09:15:00Z',
    config: {
      prizes: [
        { name: 'iPhone 15', quantity: 5, probability: 0.1 },
        { name: 'iPad Air', quantity: 10, probability: 0.5 },
        { name: 'AirPods Pro', quantity: 20, probability: 2.0 },
        { name: '100元优惠券', quantity: 100, probability: 10.0 },
        { name: '50元优惠券', quantity: 200, probability: 20.0 },
        { name: '谢谢参与', quantity: -1, probability: 67.4 }
      ],
      participation_rules: ['follow', 'share'],
      max_attempts_per_user: 3
    }
  },
  {
    id: 3,
    name: '分享有礼活动',
    type: 'share',
    description: '分享活动到朋友圈，即可获得积分奖励，积分可兑换精美礼品！',
    status: 'active',
    start_time: '2024-11-05T00:00:00Z',
    end_time: '2024-12-05T23:59:59Z',
    target_audience: '所有用户',
    budget: 50000,
    participants_count: 892,
    conversion_rate: 0.25,
    created_at: '2024-11-01T16:45:00Z',
    updated_at: '2024-11-07T11:20:00Z',
    config: {
      share_content: '🎉 超值好物推荐！快来看看这些精选商品，品质保证，价格优惠！#好物分享 #优惠活动',
      reward_type: '积分',
      reward_amount: 100,
      target_shares: 10000
    }
  },
  {
    id: 4,
    name: '新用户专享优惠券',
    type: 'coupon',
    description: '新用户注册即送100元优惠券大礼包，首单立减！',
    status: 'active',
    start_time: '2024-10-01T00:00:00Z',
    end_time: '2024-12-31T23:59:59Z',
    target_audience: '新注册用户',
    budget: 100000,
    participants_count: 567,
    conversion_rate: 0.45,
    created_at: '2024-09-25T12:00:00Z',
    updated_at: '2024-11-05T14:30:00Z',
    config: {
      coupon_type: 'cash',
      discount_amount: 100,
      min_order_amount: 299,
      total_quantity: 1000
    }
  },
  {
    id: 5,
    name: '积分商城兑换活动',
    type: 'points',
    description: '完成任务赚积分，积分兑换心仪商品，越多越划算！',
    status: 'active',
    start_time: '2024-11-01T00:00:00Z',
    end_time: '2024-11-30T23:59:59Z',
    target_audience: '会员用户',
    budget: 80000,
    participants_count: 1234,
    conversion_rate: 0.18,
    created_at: '2024-10-28T09:30:00Z',
    updated_at: '2024-11-06T16:45:00Z',
    config: {
      point_rules: [
        { action: '每日签到', points: 10, daily_limit: 1 },
        { action: '分享商品', points: 20, daily_limit: 3 },
        { action: '完成购买', points: 100, daily_limit: 0 },
        { action: '邀请好友', points: 500, daily_limit: 0 }
      ],
      exchange_items: [
        { name: '星巴克咖啡券', points: 1000, stock: 50 },
        { name: '电影票', points: 800, stock: 100 },
        { name: '20元优惠券', points: 500, stock: 200 }
      ]
    }
  },
  {
    id: 6,
    name: '黑五购物节',
    type: 'flash_sale',
    description: '黑色星期五特惠，全场5折起，限时24小时！',
    status: 'draft',
    start_time: '2024-11-29T00:00:00Z',
    end_time: '2024-11-29T23:59:59Z',
    target_audience: '全体用户',
    budget: 800000,
    participants_count: 0,
    conversion_rate: 0,
    created_at: '2024-11-08T10:15:00Z',
    updated_at: '2024-11-08T10:15:00Z',
    config: {
      product_name: '精选商品组合',
      original_price: 1999,
      sale_price: 999,
      stock_quantity: 500,
      max_purchase_per_user: 2
    }
  },
  {
    id: 7,
    name: '圣诞节抽奖活动',
    type: 'lottery',
    description: '圣诞特别企划，参与即有机会赢取圣诞大礼包！',
    status: 'ended',
    start_time: '2023-12-20T00:00:00Z',
    end_time: '2023-12-25T23:59:59Z',
    target_audience: '全体用户',
    budget: 150000,
    participants_count: 2345,
    conversion_rate: 0.12,
    created_at: '2023-12-15T14:00:00Z',
    updated_at: '2023-12-26T10:00:00Z',
    config: {
      prizes: [
        { name: '圣诞大礼包', quantity: 10, probability: 0.5 },
        { name: '圣诞帽', quantity: 50, probability: 5.0 },
        { name: '圣诞贺卡', quantity: 100, probability: 15.0 },
        { name: '谢谢参与', quantity: -1, probability: 79.5 }
      ],
      participation_rules: ['follow'],
      max_attempts_per_user: 1
    }
  }
]

export const mockParticipants = [
  {
    id: 1,
    campaign_id: 1,
    customer_id: 101,
    customer_name: '张三',
    customer_phone: '13800138001',
    customer_wechat: 'zhangsan_wx',
    participation_time: '2024-11-11T08:30:00Z',
    status: 'completed',
    result: 'success',
    reward: 'iPhone 15 Pro Max',
    notes: '秒杀成功'
  },
  {
    id: 2,
    campaign_id: 1,
    customer_id: 102,
    customer_name: '李四',
    customer_phone: '13800138002',
    customer_wechat: 'lisi_wx',
    participation_time: '2024-11-11T08:31:00Z',
    status: 'failed',
    result: 'failed',
    reward: '',
    notes: '库存不足'
  },
  {
    id: 3,
    campaign_id: 2,
    customer_id: 103,
    customer_name: '王五',
    customer_phone: '13800138003',
    customer_wechat: 'wangwu_wx',
    participation_time: '2024-11-10T15:20:00Z',
    status: 'completed',
    result: 'success',
    reward: '100元优惠券',
    notes: '抽奖获得优惠券'
  }
]

export const mockCampaignShares = [
  {
    id: 1,
    campaign_id: 3,
    customer_id: 101,
    share_platform: 'wechat',
    share_time: '2024-11-08T10:15:00Z',
    share_content: '🎉 超值好物推荐！快来看看这些精选商品',
    click_count: 25,
    conversion_count: 3
  },
  {
    id: 2,
    campaign_id: 3,
    customer_id: 102,
    share_platform: 'weibo',
    share_time: '2024-11-08T14:30:00Z',
    share_content: '分享有礼活动，积分兑换好礼',
    click_count: 18,
    conversion_count: 2
  }
]