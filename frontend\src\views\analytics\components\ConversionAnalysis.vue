<template>
  <div class="conversion-analysis">
    <!-- 分析配置 -->
    <n-card title="转化分析配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>分析类型</label>
            <n-select v-model:value="analysisConfig.type" :options="analysisTypeOptions" placeholder="选择分析类型" />
          </div>
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="analysisConfig.dateRange" type="daterange" clearable />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>转化目标</label>
            <n-select v-model:value="analysisConfig.goal" :options="goalOptions" placeholder="选择转化目标" />
          </div>
          <div class="form-item">
            <label>客户分群</label>
            <n-select v-model:value="analysisConfig.segment" :options="segmentOptions" multiple placeholder="选择客户分群" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化概览 -->
    <n-card title="转化概览" class="overview-card">
      <div class="conversion-overview">
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#2080f0"><TrendingUpOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.totalConversions }}</div>
              <div class="stat-label">总转化数</div>
              <div class="stat-change" :class="conversionOverview.conversionsChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.conversionsChange >= 0 ? '+' : '' }}{{ conversionOverview.conversionsChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#18a058"><AtOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.conversionRate }}%</div>
              <div class="stat-label">转化率</div>
              <div class="stat-change" :class="conversionOverview.rateChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.rateChange >= 0 ? '+' : '' }}{{ conversionOverview.rateChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#f0a020"><TimeOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ conversionOverview.avgTime }}</div>
              <div class="stat-label">平均转化时间</div>
              <div class="stat-change" :class="conversionOverview.timeChange <= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.timeChange >= 0 ? '+' : '' }}{{ conversionOverview.timeChange }}%
              </div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <n-icon color="#d03050"><CashOutline /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">¥{{ conversionOverview.avgValue }}</div>
              <div class="stat-label">平均转化价值</div>
              <div class="stat-change" :class="conversionOverview.valueChange >= 0 ? 'positive' : 'negative'">
                {{ conversionOverview.valueChange >= 0 ? '+' : '' }}{{ conversionOverview.valueChange }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化趋势图 -->
    <n-card title="转化趋势分析" class="trend-card">
      <div class="chart-container">
        <div ref="trendChartRef" class="chart"></div>
      </div>
    </n-card>
    
    <!-- 转化漏斗分析 -->
    <n-card title="转化漏斗分析" class="funnel-card">
      <div class="funnel-content">
        <div class="funnel-chart">
          <div ref="funnelChartRef" class="chart"></div>
        </div>
        <div class="funnel-details">
          <div class="funnel-step" v-for="(step, index) in funnelSteps" :key="step.name">
            <div class="step-header">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-name">{{ step.name }}</div>
              <div class="step-rate">{{ step.conversionRate }}%</div>
            </div>
            <div class="step-stats">
              <div class="stat">
                <span class="stat-label">用户数:</span>
                <span class="stat-value">{{ step.users.toLocaleString() }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">转化数:</span>
                <span class="stat-value">{{ step.conversions.toLocaleString() }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">流失率:</span>
                <span class="stat-value text-error">{{ step.dropRate }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化路径分析 -->
    <n-card title="转化路径分析" class="path-card">
      <div class="path-analysis">
        <div class="path-filters">
          <n-select v-model:value="pathFilter" :options="pathFilterOptions" placeholder="选择路径类型" style="width: 200px;" />
        </div>
        
        <div class="path-list">
          <div class="path-item" v-for="path in conversionPaths" :key="path.id">
            <div class="path-header">
              <div class="path-name">{{ path.name }}</div>
              <div class="path-stats">
                <span class="path-users">{{ path.users }}用户</span>
                <span class="path-rate">{{ path.conversionRate }}%转化</span>
              </div>
            </div>
            
            <div class="path-flow">
              <div class="flow-step" v-for="(step, index) in path.steps" :key="index">
                <div class="step-box">
                  <div class="step-name">{{ step.name }}</div>
                  <div class="step-count">{{ step.count }}</div>
                </div>
                <div v-if="index < path.steps.length - 1" class="flow-arrow">
                  <div class="arrow-line"></div>
                  <div class="arrow-rate">{{ step.nextRate }}%</div>
                </div>
              </div>
            </div>
            
            <div class="path-insights">
              <div class="insight" v-for="insight in path.insights" :key="insight.type">
                <n-tag :type="insight.type === 'bottleneck' ? 'error' : insight.type === 'opportunity' ? 'warning' : 'success'" size="small">
                  {{ insight.label }}
                </n-tag>
                <span class="insight-text">{{ insight.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化归因分析 -->
    <n-card title="转化归因分析" class="attribution-card">
      <div class="attribution-content">
        <div class="attribution-chart">
          <div ref="attributionChartRef" class="chart"></div>
        </div>
        
        <div class="attribution-details">
          <div class="attribution-item" v-for="item in attributionData" :key="item.channel">
            <div class="attribution-header">
              <div class="channel-name">{{ item.channel }}</div>
              <div class="channel-contribution">{{ item.contribution }}%</div>
            </div>
            
            <div class="attribution-metrics">
              <div class="metric">
                <span class="metric-label">首次接触:</span>
                <span class="metric-value">{{ item.firstTouch }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">最后接触:</span>
                <span class="metric-value">{{ item.lastTouch }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">辅助转化:</span>
                <span class="metric-value">{{ item.assisted }}%</span>
              </div>
            </div>
            
            <div class="attribution-progress">
              <n-progress
                type="line"
                :percentage="item.contribution"
                :color="getProgressColor(item.contribution)"
                :show-indicator="false"
              />
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 转化优化建议 -->
    <n-card title="转化优化建议" class="optimization-card">
      <div class="optimization-list">
        <div class="optimization-item" v-for="optimization in optimizationSuggestions" :key="optimization.id">
          <div class="optimization-header">
            <div class="optimization-icon">
              <n-icon :color="optimization.priority === 'high' ? '#d03050' : optimization.priority === 'medium' ? '#f0a020' : '#18a058'">
                <BulbOutline />
              </n-icon>
            </div>
            <div class="optimization-info">
              <div class="optimization-title">{{ optimization.title }}</div>
              <div class="optimization-description">{{ optimization.description }}</div>
            </div>
            <div class="optimization-priority">
              <n-tag :type="optimization.priority === 'high' ? 'error' : optimization.priority === 'medium' ? 'warning' : 'success'">
                {{ optimization.priority === 'high' ? '高优先级' : optimization.priority === 'medium' ? '中优先级' : '低优先级' }}
              </n-tag>
            </div>
          </div>
          
          <div class="optimization-details">
            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">当前转化率:</span>
                <span class="detail-value">{{ optimization.currentRate }}%</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">预期提升:</span>
                <span class="detail-value text-success">{{ optimization.expectedImprovement }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">实施难度:</span>
                <span class="detail-value">{{ optimization.difficulty }}</span>
              </div>
            </div>
            
            <div class="optimization-actions">
              <div class="action-item" v-for="(action, index) in optimization.actions" :key="index">
                {{ index + 1 }}. {{ action }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NTag, NProgress
} from 'naive-ui'
import {
  TrendingUpOutline, AtOutline, TimeOutline, CashOutline, BulbOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const trendChartRef = ref<HTMLElement>()
const funnelChartRef = ref<HTMLElement>()
const attributionChartRef = ref<HTMLElement>()

const pathFilter = ref('all')

// 分析配置
const analysisConfig = reactive({
  type: 'sales',
  dateRange: null,
  goal: 'purchase',
  segment: []
})

// 转化概览数据
const conversionOverview = ref({
  totalConversions: 2580,
  conversionsChange: 12.5,
  conversionRate: 6.8,
  rateChange: 1.2,
  avgTime: '3.5天',
  timeChange: -8.5,
  avgValue: 1250,
  valueChange: 15.8
})

// 漏斗步骤数据
const funnelSteps = ref([
  {
    name: '访问网站',
    users: 50000,
    conversions: 50000,
    conversionRate: 100,
    dropRate: 0
  },
  {
    name: '浏览产品',
    users: 35000,
    conversions: 35000,
    conversionRate: 70,
    dropRate: 30
  },
  {
    name: '加入购物车',
    users: 12000,
    conversions: 12000,
    conversionRate: 24,
    dropRate: 66
  },
  {
    name: '开始结账',
    users: 6000,
    conversions: 6000,
    conversionRate: 12,
    dropRate: 50
  },
  {
    name: '完成购买',
    users: 3400,
    conversions: 3400,
    conversionRate: 6.8,
    dropRate: 43
  }
])

// 转化路径数据
const conversionPaths = ref([
  {
    id: 1,
    name: '搜索引擎 → 产品页 → 购买',
    users: 15000,
    conversionRate: 8.5,
    steps: [
      { name: '搜索引擎', count: 15000, nextRate: 65 },
      { name: '产品页面', count: 9750, nextRate: 35 },
      { name: '购物车', count: 3413, nextRate: 38 },
      { name: '完成购买', count: 1275 }
    ],
    insights: [
      { type: 'bottleneck', label: '瓶颈', text: '产品页到购物车转化率偏低' },
      { type: 'opportunity', label: '机会', text: '可优化产品页面设计' }
    ]
  },
  {
    id: 2,
    name: '社交媒体 → 活动页 → 注册',
    users: 8500,
    conversionRate: 12.3,
    steps: [
      { name: '社交媒体', count: 8500, nextRate: 78 },
      { name: '活动页面', count: 6630, nextRate: 45 },
      { name: '注册表单', count: 2984, nextRate: 35 },
      { name: '完成注册', count: 1045 }
    ],
    insights: [
      { type: 'success', label: '优势', text: '社交媒体引流效果良好' },
      { type: 'bottleneck', label: '瓶颈', text: '注册表单填写环节流失较多' }
    ]
  },
  {
    id: 3,
    name: '邮件营销 → 落地页 → 咨询',
    users: 5200,
    conversionRate: 15.8,
    steps: [
      { name: '邮件营销', count: 5200, nextRate: 85 },
      { name: '落地页面', count: 4420, nextRate: 28 },
      { name: '在线咨询', count: 1238, nextRate: 66 },
      { name: '留下联系方式', count: 821 }
    ],
    insights: [
      { type: 'success', label: '优势', text: '邮件打开率和点击率较高' },
      { type: 'opportunity', label: '机会', text: '可增加咨询引导元素' }
    ]
  }
])

// 归因分析数据
const attributionData = ref([
  {
    channel: '搜索引擎',
    contribution: 35,
    firstTouch: 45,
    lastTouch: 28,
    assisted: 32
  },
  {
    channel: '社交媒体',
    contribution: 25,
    firstTouch: 30,
    lastTouch: 22,
    assisted: 28
  },
  {
    channel: '直接访问',
    contribution: 20,
    firstTouch: 15,
    lastTouch: 35,
    assisted: 18
  },
  {
    channel: '邮件营销',
    contribution: 15,
    firstTouch: 8,
    lastTouch: 12,
    assisted: 22
  },
  {
    channel: '广告投放',
    contribution: 5,
    firstTouch: 2,
    lastTouch: 3,
    assisted: 8
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    title: '优化购物车转化',
    description: '购物车到结账的转化率较低，需要减少摩擦点',
    priority: 'high',
    currentRate: 50,
    expectedImprovement: '+15-20%',
    difficulty: '中等',
    actions: [
      '简化结账流程，减少必填字段',
      '增加多种支付方式选择',
      '添加安全认证标识',
      '提供客服在线支持'
    ]
  },
  {
    id: 2,
    title: '提升产品页转化',
    description: '产品页面到购物车的转化率有提升空间',
    priority: 'high',
    currentRate: 35,
    expectedImprovement: '+10-15%',
    difficulty: '较低',
    actions: [
      '优化产品图片和描述',
      '增加用户评价展示',
      '添加相关产品推荐',
      '优化加购按钮设计'
    ]
  },
  {
    id: 3,
    title: '改善注册流程',
    description: '注册表单的完成率需要提升',
    priority: 'medium',
    currentRate: 35,
    expectedImprovement: '+8-12%',
    difficulty: '较低',
    actions: [
      '减少注册表单字段',
      '支持第三方账号登录',
      '优化表单验证提示',
      '增加注册激励措施'
    ]
  },
  {
    id: 4,
    title: '增强邮件营销效果',
    description: '邮件营销转化率虽高但规模较小',
    priority: 'low',
    currentRate: 66,
    expectedImprovement: '+5-8%',
    difficulty: '较高',
    actions: [
      '扩大邮件订阅用户基数',
      '个性化邮件内容',
      '优化发送时间和频率',
      'A/B测试邮件模板'
    ]
  }
])

// 选项数据
const analysisTypeOptions = [
  { label: '销售转化', value: 'sales' },
  { label: '注册转化', value: 'registration' },
  { label: '营销转化', value: 'marketing' },
  { label: '留存转化', value: 'retention' }
]

const goalOptions = [
  { label: '完成购买', value: 'purchase' },
  { label: '用户注册', value: 'registration' },
  { label: '订阅服务', value: 'subscription' },
  { label: '下载应用', value: 'download' }
]

const segmentOptions = [
  { label: '新用户', value: 'new' },
  { label: '老用户', value: 'returning' },
  { label: '高价值用户', value: 'high_value' },
  { label: '移动端用户', value: 'mobile' }
]

const pathFilterOptions = [
  { label: '全部路径', value: 'all' },
  { label: '高转化路径', value: 'high_conversion' },
  { label: '低转化路径', value: 'low_conversion' },
  { label: '热门路径', value: 'popular' }
]

// 工具方法
const getProgressColor = (value: number) => {
  if (value >= 30) return '#18a058'
  if (value >= 20) return '#f0a020'
  return '#d03050'
}

// 图表渲染方法
const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const conversionRate = [5.2, 5.8, 6.1, 5.9, 6.5, 6.8, 6.3, 7.2, 6.9, 7.5, 7.1, 6.8]
  const conversionCount = [2080, 2320, 2440, 2360, 2600, 2720, 2520, 2880, 2760, 3000, 2840, 2580]
  
  const option = {
    title: {
      text: '转化趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['转化率', '转化数量'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '转化率(%)',
        position: 'left',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '转化数量',
        position: 'right'
      }
    ],
    series: [
      {
        name: '转化率',
        type: 'line',
        yAxisIndex: 0,
        data: conversionRate,
        lineStyle: { color: '#2080f0', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '转化数量',
        type: 'bar',
        yAxisIndex: 1,
        data: conversionCount,
        itemStyle: { color: '#18a058' }
      }
    ]
  }
  
  chart.setOption(option)
}

const renderFunnelChart = () => {
  if (!funnelChartRef.value) return
  
  const chart = echarts.init(funnelChartRef.value)
  
  const funnelData = funnelSteps.value.map(step => ({
    value: step.conversionRate,
    name: `${step.name} (${step.users.toLocaleString()})`
  }))
  
  const option = {
    title: {
      text: '转化漏斗',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%'
    },
    series: [
      {
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: funnelData
      }
    ]
  }
  
  chart.setOption(option)
}

const renderAttributionChart = () => {
  if (!attributionChartRef.value) return
  
  const chart = echarts.init(attributionChartRef.value)
  
  const channels = attributionData.value.map(item => item.channel)
  const firstTouch = attributionData.value.map(item => item.firstTouch)
  const lastTouch = attributionData.value.map(item => item.lastTouch)
  const assisted = attributionData.value.map(item => item.assisted)
  
  const option = {
    title: {
      text: '转化归因分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['首次接触', '最后接触', '辅助转化'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: channels
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '首次接触',
        type: 'bar',
        stack: 'total',
        data: firstTouch,
        itemStyle: { color: '#2080f0' }
      },
      {
        name: '最后接触',
        type: 'bar',
        stack: 'total',
        data: lastTouch,
        itemStyle: { color: '#18a058' }
      },
      {
        name: '辅助转化',
        type: 'bar',
        stack: 'total',
        data: assisted,
        itemStyle: { color: '#f0a020' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  renderTrendChart()
  renderFunnelChart()
  renderAttributionChart()
})
</script>

<style scoped>
.conversion-analysis {
  padding: 24px;
}

.config-card,
.overview-card,
.trend-card,
.funnel-card,
.path-card,
.attribution-card,
.optimization-card {
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #18a058;
}

.stat-change.negative {
  color: #d03050;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.funnel-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.funnel-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.funnel-step {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-name {
  flex: 1;
  font-weight: 600;
  color: #1a1a1a;
}

.step-rate {
  font-weight: 600;
  color: #2080f0;
}

.step-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
  color: #1a1a1a;
}

.stat-value.text-error {
  color: #d03050;
}

.path-filters {
  margin-bottom: 20px;
}

.path-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.path-item {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.path-name {
  font-weight: 600;
  color: #1a1a1a;
}

.path-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.path-users {
  color: #666;
}

.path-rate {
  color: #2080f0;
  font-weight: 500;
}

.path-flow {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  overflow-x: auto;
  padding: 8px 0;
}

.flow-step {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-box {
  padding: 8px 12px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  text-align: center;
  min-width: 80px;
}

.step-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.step-count {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
}

.flow-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.arrow-line {
  width: 20px;
  height: 2px;
  background: #2080f0;
  position: relative;
}

.arrow-line::after {
  content: '';
  position: absolute;
  right: -4px;
  top: -2px;
  width: 0;
  height: 0;
  border-left: 4px solid #2080f0;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
}

.arrow-rate {
  font-size: 10px;
  color: #2080f0;
  font-weight: 500;
}

.path-insights {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.insight {
  display: flex;
  align-items: center;
  gap: 8px;
}

.insight-text {
  font-size: 14px;
  color: #666;
}

.attribution-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.attribution-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.attribution-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.attribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.channel-name {
  font-weight: 600;
  color: #1a1a1a;
}

.channel-contribution {
  font-weight: 600;
  color: #2080f0;
  font-size: 18px;
}

.attribution-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}

.metric {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 500;
  color: #1a1a1a;
}

.optimization-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.optimization-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
}

.optimization-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.optimization-icon {
  font-size: 24px;
}

.optimization-info {
  flex: 1;
}

.optimization-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.optimization-description {
  color: #666;
  font-size: 14px;
}

.optimization-details {
  padding: 20px;
}

.detail-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.detail-label {
  color: #666;
}

.detail-value {
  font-weight: 500;
  color: #1a1a1a;
}

.detail-value.text-success {
  color: #18a058;
}

.optimization-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-item {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}
</style>