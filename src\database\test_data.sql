-- 测试数据插入脚本

-- 插入测试销售人员
INSERT INTO `users` (`id`, `username`, `password`, `name`, `role`, `department_id`, `status`) VALUES
(2, 'sales1', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', 'sales', 1, 1),
(3, 'sales2', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', 'sales', 1, 1),
(4, 'designer1', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', 'designer', 2, 1)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 插入测试客户数据
INSERT INTO `customers` (`name`, `phone`, `email`, `company`, `wechat`, `address`, `source`, `status`, `level`, `gender`, `is_vip`, `remark`, `owner_id`, `deal_amount`) VALUES
('陈先生', '13800138001', '<EMAIL>', '阿里巴巴', 'chen_wechat', '杭州市西湖区', 'online', 'interested', 'A', 'male', 1, '重要客户，有装修需求', 2, 0.00),
('刘女士', '13800138002', '<EMAIL>', '腾讯科技', 'liu_wechat', '深圳市南山区', 'referral', 'potential', 'B', 'female', 0, '朋友推荐，考虑中', 2, 0.00),
('王总', '13800138003', '<EMAIL>', '华为技术', 'wang_wechat', '深圳市龙岗区', 'event', 'deal', 'A', 'male', 1, '已成交，满意度高', 3, 150000.00),
('赵小姐', '13800138004', '<EMAIL>', '字节跳动', 'zhao_wechat', '北京市海淀区', 'telemarketing', 'interested', 'B', 'female', 0, '电话联系，有意向', 2, 0.00),
('孙先生', '13800138005', '<EMAIL>', '美团', 'sun_wechat', '北京市朝阳区', 'store', 'potential', 'C', 'male', 0, '到店咨询过', 3, 0.00),
('周女士', '13800138006', '<EMAIL>', '滴滴出行', 'zhou_wechat', '北京市昌平区', 'online', 'lost', 'C', 'female', 0, '价格原因流失', 2, 0.00),
('吴先生', '13800138007', '<EMAIL>', '小米科技', 'wu_wechat', '北京市海淀区', 'referral', 'deal', 'A', 'male', 1, '二次成交客户', 3, 200000.00),
('郑女士', '13800138008', '<EMAIL>', '京东', 'zheng_wechat', '北京市大兴区', 'event', 'interested', 'B', 'female', 0, '展会认识，跟进中', 2, 0.00);

-- 插入跟进记录
INSERT INTO `follow_records` (`customer_id`, `user_id`, `type`, `stage`, `content`, `result`, `has_next_plan`, `next_time`, `next_content`) VALUES
(1, 2, 'phone', '初次接触', '电话沟通了解客户需求，客户对我们的服务很感兴趣', 'effective', 1, '2024-12-25 14:00:00', '安排上门量房'),
(1, 2, 'wechat', '需求确认', '微信发送了案例图片，客户很满意我们的设计风格', 'effective', 1, '2024-12-26 10:00:00', '准备详细方案'),
(2, 2, 'phone', '初次接触', '朋友推荐的客户，了解基本情况', 'effective', 1, '2024-12-24 16:00:00', '发送公司介绍资料'),
(3, 3, 'visit', '签约成交', '客户到店签约，项目金额15万', 'effective', 0, NULL, NULL),
(4, 2, 'phone', '电话营销', '电话联系客户，了解装修时间和预算', 'effective', 1, '2024-12-25 09:00:00', '发送报价单'),
(5, 3, 'visit', '到店咨询', '客户到店了解服务，留下联系方式', 'effective', 1, '2024-12-24 15:00:00', '电话回访'),
(6, 2, 'phone', '价格谈判', '客户觉得价格偏高，暂时不考虑', 'invalid', 0, NULL, NULL),
(7, 3, 'meeting', '二次合作', '老客户介绍新项目，顺利签约', 'effective', 0, NULL, NULL),
(8, 2, 'wechat', '展会跟进', '展会后微信联系，发送详细资料', 'effective', 1, '2024-12-26 14:00:00', '邀请到店参观');

-- 插入见面记录
INSERT INTO `meeting_records` (`follow_record_id`, `customer_id`, `user_id`, `meeting_type`, `meeting_time`, `designer_id`, `designer_name`, `visit_count`, `address`, `notes`) VALUES
(4, 3, 3, 'visit', '2024-12-20 14:00:00', 4, '王五', 1, '华为技术有限公司', '客户到店签约，项目进展顺利'),
(6, 5, 3, 'visit', '2024-12-21 10:00:00', 4, '王五', 1, '美团总部', '客户到店咨询，了解服务流程'),
(8, 7, 3, 'external', '2024-12-22 16:00:00', 4, '王五', 2, '小米科技园区', '老客户新项目洽谈，合作愉快');

-- 更新客户的最后跟进时间和跟进次数
UPDATE customers SET 
  last_follow_time = (SELECT MAX(created_at) FROM follow_records WHERE customer_id = customers.id),
  follow_count = (SELECT COUNT(*) FROM follow_records WHERE customer_id = customers.id),
  deal_time = CASE WHEN status = 'deal' THEN NOW() ELSE NULL END
WHERE id IN (1,2,3,4,5,6,7,8);