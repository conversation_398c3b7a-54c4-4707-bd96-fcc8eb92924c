import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs/promises';

// 加载环境变量
dotenv.config();

// 数据库连接配置接口
export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  charset?: string;
  timezone?: string;
  ssl?: any;
  connectionLimit?: number;
  acquireTimeout?: number;
  timeout?: number;
}

// Supabase配置接口
export interface SupabaseConfig {
  url: string;
  anon_key: string;
  service_role_key: string;
}

// 迁移配置接口
export interface MigrationConfig {
  // 数据库配置
  mysql: DatabaseConfig;
  supabase: SupabaseConfig;
  
  // 迁移设置
  batch_size: number;
  max_retries: number;
  retry_delay: number; // 毫秒
  timeout: number; // 毫秒
  parallel_tables: number;
  
  // 数据验证
  enable_validation: boolean;
  validation_sample_size: number;
  strict_validation: boolean;
  
  // 日志配置
  logging: {
    enabled: boolean;
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    log_to_file: boolean;
    log_to_database: boolean;
    log_to_console: boolean;
    file_path: string;
    max_file_size: number; // MB
    max_files: number;
  };
  
  // 备份配置
  backup: {
    enabled: boolean;
    backup_before_migration: boolean;
    backup_path: string;
    compress_backup: boolean;
    keep_backups: number;
  };
  
  // 性能配置
  performance: {
    enable_monitoring: boolean;
    memory_limit: number; // MB
    cpu_limit: number; // 百分比
    progress_report_interval: number; // 秒
  };
  
  // 表迁移配置
  tables: {
    [tableName: string]: {
      enabled: boolean;
      priority: number;
      batch_size?: number;
      custom_query?: string;
      transform_data?: boolean;
      skip_validation?: boolean;
      dependencies?: string[];
    };
  };
  
  // 错误处理
  error_handling: {
    continue_on_error: boolean;
    max_errors: number;
    error_log_path: string;
    auto_rollback: boolean;
  };
}

// 默认配置
const defaultConfig: MigrationConfig = {
  mysql: {
    host: process.env.MYSQL_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'workchat_admin',
    charset: 'utf8mb4',
    timezone: '+08:00',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
  },
  
  supabase: {
    url: process.env.SUPABASE_URL || '',
    anon_key: process.env.SUPABASE_ANON_KEY || '',
    service_role_key: process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  },
  
  batch_size: 1000,
  max_retries: 3,
  retry_delay: 5000,
  timeout: 300000, // 5分钟
  parallel_tables: 3,
  
  enable_validation: true,
  validation_sample_size: 100,
  strict_validation: false,
  
  logging: {
    enabled: true,
    level: 'INFO',
    log_to_file: true,
    log_to_database: true,
    log_to_console: true,
    file_path: './logs/migration.log',
    max_file_size: 100,
    max_files: 10
  },
  
  backup: {
    enabled: true,
    backup_before_migration: true,
    backup_path: './backups',
    compress_backup: true,
    keep_backups: 5
  },
  
  performance: {
    enable_monitoring: true,
    memory_limit: 2048, // 2GB
    cpu_limit: 80,
    progress_report_interval: 30
  },
  
  tables: {
    users: {
      enabled: true,
      priority: 1,
      batch_size: 500,
      dependencies: []
    },
    roles: {
      enabled: true,
      priority: 1,
      dependencies: []
    },
    permissions: {
      enabled: true,
      priority: 1,
      dependencies: []
    },
    role_permissions: {
      enabled: true,
      priority: 2,
      dependencies: ['roles', 'permissions']
    },
    user_roles: {
      enabled: true,
      priority: 3,
      dependencies: ['users', 'roles']
    },
    customers: {
      enabled: true,
      priority: 2,
      batch_size: 1000,
      dependencies: ['users']
    },
    customer_follow_records: {
      enabled: true,
      priority: 3,
      dependencies: ['customers', 'users']
    },
    marketing_campaigns: {
      enabled: true,
      priority: 2,
      dependencies: ['users']
    },
    campaign_participants: {
      enabled: true,
      priority: 3,
      dependencies: ['marketing_campaigns', 'customers']
    },
    campaign_shares: {
      enabled: true,
      priority: 3,
      dependencies: ['marketing_campaigns', 'customers']
    },
    meetings: {
      enabled: true,
      priority: 2,
      dependencies: ['users']
    },
    meeting_participants: {
      enabled: true,
      priority: 3,
      dependencies: ['meetings', 'customers']
    },
    pool_rules: {
      enabled: true,
      priority: 1,
      dependencies: []
    },
    public_pool: {
      enabled: true,
      priority: 3,
      dependencies: ['customers', 'pool_rules']
    },
    customer_behaviors: {
      enabled: true,
      priority: 3,
      dependencies: ['customers']
    },
    wechat_customer_tracking: {
      enabled: true,
      priority: 3,
      dependencies: ['customers']
    },
    sales_funnel_stats: {
      enabled: true,
      priority: 4,
      dependencies: ['customers']
    },
    customer_value_analysis: {
      enabled: true,
      priority: 4,
      dependencies: ['customers']
    },
    follow_ups: {
      enabled: true,
      priority: 3,
      dependencies: ['customers', 'users']
    },
    option_categories: {
      enabled: true,
      priority: 1,
      dependencies: []
    },
    option_items: {
      enabled: true,
      priority: 2,
      dependencies: ['option_categories']
    }
  },
  
  error_handling: {
    continue_on_error: false,
    max_errors: 10,
    error_log_path: './logs/migration-errors.log',
    auto_rollback: true
  }
};

/**
 * 配置管理器类
 */
export class ConfigManager {
  private config: MigrationConfig;
  private configPath: string;

  constructor(configPath?: string) {
    this.configPath = configPath || './migration-config.json';
    this.config = { ...defaultConfig };
  }

  /**
   * 加载配置文件
   */
  async loadConfig(): Promise<MigrationConfig> {
    try {
      const configExists = await this.fileExists(this.configPath);
      if (configExists) {
        const configData = await fs.readFile(this.configPath, 'utf8');
        const userConfig = JSON.parse(configData);
        this.config = this.mergeConfig(defaultConfig, userConfig);
      } else {
        // 创建默认配置文件
        await this.saveConfig();
      }
    } catch (error) {
      console.warn('加载配置文件失败，使用默认配置:', error);
      this.config = { ...defaultConfig };
    }

    // 验证配置
    this.validateConfig();
    
    return this.config;
  }

  /**
   * 保存配置文件
   */
  async saveConfig(): Promise<void> {
    try {
      const configDir = path.dirname(this.configPath);
      await fs.mkdir(configDir, { recursive: true });
      
      const configData = JSON.stringify(this.config, null, 2);
      await fs.writeFile(this.configPath, configData, 'utf8');
    } catch (error) {
      console.error('保存配置文件失败:', error);
      throw error;
    }
  }

  /**
   * 获取配置
   */
  getConfig(): MigrationConfig {
    return this.config;
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<MigrationConfig>): void {
    this.config = this.mergeConfig(this.config, updates);
  }

  /**
   * 获取表配置
   */
  getTableConfig(tableName: string): MigrationConfig['tables'][string] | undefined {
    return this.config.tables[tableName];
  }

  /**
   * 获取启用的表列表（按优先级排序）
   */
  getEnabledTables(): string[] {
    return Object.entries(this.config.tables)
      .filter(([_, config]) => config.enabled)
      .sort(([_, a], [__, b]) => a.priority - b.priority)
      .map(([tableName]) => tableName);
  }

  /**
   * 获取表依赖关系
   */
  getTableDependencies(): Map<string, string[]> {
    const dependencies = new Map<string, string[]>();
    
    Object.entries(this.config.tables).forEach(([tableName, config]) => {
      if (config.enabled && config.dependencies) {
        dependencies.set(tableName, config.dependencies);
      }
    });
    
    return dependencies;
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    const errors: string[] = [];

    // 验证数据库配置
    if (!this.config.mysql.host) {
      errors.push('MySQL主机地址不能为空');
    }
    if (!this.config.mysql.user) {
      errors.push('MySQL用户名不能为空');
    }
    if (!this.config.mysql.database) {
      errors.push('MySQL数据库名不能为空');
    }

    // 验证Supabase配置
    if (!this.config.supabase.url) {
      errors.push('Supabase URL不能为空');
    }
    if (!this.config.supabase.service_role_key) {
      errors.push('Supabase Service Role Key不能为空');
    }

    // 验证批处理大小
    if (this.config.batch_size <= 0) {
      errors.push('批处理大小必须大于0');
    }

    // 验证表依赖关系
    this.validateTableDependencies(errors);

    if (errors.length > 0) {
      throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }
  }

  /**
   * 验证表依赖关系
   */
  private validateTableDependencies(errors: string[]): void {
    const enabledTables = new Set(this.getEnabledTables());
    
    Object.entries(this.config.tables).forEach(([tableName, config]) => {
      if (config.enabled && config.dependencies) {
        config.dependencies.forEach(dep => {
          if (!enabledTables.has(dep)) {
            errors.push(`表 ${tableName} 依赖的表 ${dep} 未启用`);
          }
        });
      }
    });

    // 检查循环依赖
    this.checkCircularDependencies(errors);
  }

  /**
   * 检查循环依赖
   */
  private checkCircularDependencies(errors: string[]): void {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    
    const hasCycle = (tableName: string): boolean => {
      if (recursionStack.has(tableName)) {
        return true;
      }
      if (visited.has(tableName)) {
        return false;
      }
      
      visited.add(tableName);
      recursionStack.add(tableName);
      
      const config = this.config.tables[tableName];
      if (config && config.dependencies) {
        for (const dep of config.dependencies) {
          if (hasCycle(dep)) {
            return true;
          }
        }
      }
      
      recursionStack.delete(tableName);
      return false;
    };
    
    Object.keys(this.config.tables).forEach(tableName => {
      if (!visited.has(tableName) && hasCycle(tableName)) {
        errors.push(`检测到循环依赖，涉及表: ${tableName}`);
      }
    });
  }

  /**
   * 合并配置
   */
  private mergeConfig(base: any, override: any): any {
    const result = { ...base };
    
    Object.keys(override).forEach(key => {
      if (override[key] !== null && typeof override[key] === 'object' && !Array.isArray(override[key])) {
        result[key] = this.mergeConfig(base[key] || {}, override[key]);
      } else {
        result[key] = override[key];
      }
    });
    
    return result;
  }

  /**
   * 检查文件是否存在
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 环境变量管理器
 */
export class EnvironmentManager {
  private envPath: string;

  constructor(envPath?: string) {
    this.envPath = envPath || '.env';
  }

  /**
   * 检查必需的环境变量
   */
  async checkRequiredEnvVars(): Promise<{ missing: string[], invalid: string[] }> {
    const required = [
      'MYSQL_HOST',
      'MYSQL_PORT',
      'MYSQL_USER',
      'MYSQL_PASSWORD',
      'MYSQL_DATABASE',
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];

    const missing: string[] = [];
    const invalid: string[] = [];

    required.forEach(varName => {
      const value = process.env[varName];
      if (!value) {
        missing.push(varName);
      } else {
        // 验证特定变量的格式
        if (varName === 'MYSQL_PORT' && isNaN(parseInt(value))) {
          invalid.push(`${varName}: 必须是有效的端口号`);
        }
        if (varName === 'SUPABASE_URL' && !value.startsWith('https://')) {
          invalid.push(`${varName}: 必须是有效的HTTPS URL`);
        }
      }
    });

    return { missing, invalid };
  }

  /**
   * 创建示例环境变量文件
   */
  async createExampleEnvFile(): Promise<void> {
    const exampleContent = `# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=workchat_admin

# Supabase配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 迁移配置
MIGRATION_BATCH_SIZE=1000
MIGRATION_MAX_RETRIES=3
MIGRATION_TIMEOUT=300000

# 日志配置
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_TO_DATABASE=true
`;

    const examplePath = this.envPath + '.example';
    await fs.writeFile(examplePath, exampleContent, 'utf8');
  }

  /**
   * 验证数据库连接
   */
  async validateDatabaseConnection(config: DatabaseConfig): Promise<boolean> {
    try {
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database
      });
      
      await connection.execute('SELECT 1');
      await connection.end();
      return true;
    } catch (error) {
      console.error('数据库连接验证失败:', error);
      return false;
    }
  }

  /**
   * 验证Supabase连接
   */
  async validateSupabaseConnection(config: SupabaseConfig): Promise<boolean> {
    try {
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(config.url, config.service_role_key);
      
      const { data, error } = await supabase.from('users').select('count').limit(1);
      return !error;
    } catch (error) {
      console.error('Supabase连接验证失败:', error);
      return false;
    }
  }
}

// 导出配置实例
export const configManager = new ConfigManager();
export const environmentManager = new EnvironmentManager();

// 导出默认配置
export { defaultConfig };

// 配置初始化函数
export async function initializeConfig(): Promise<MigrationConfig> {
  // 检查环境变量
  const envCheck = await environmentManager.checkRequiredEnvVars();
  
  if (envCheck.missing.length > 0) {
    console.warn('缺少必需的环境变量:', envCheck.missing);
    await environmentManager.createExampleEnvFile();
    console.log('已创建 .env.example 文件，请配置相应的环境变量');
  }
  
  if (envCheck.invalid.length > 0) {
    console.error('无效的环境变量:', envCheck.invalid);
    throw new Error('请修正无效的环境变量配置');
  }
  
  // 加载配置
  const config = await configManager.loadConfig();
  
  // 验证数据库连接
  const mysqlValid = await environmentManager.validateDatabaseConnection(config.mysql);
  if (!mysqlValid) {
    throw new Error('MySQL数据库连接失败，请检查配置');
  }
  
  const supabaseValid = await environmentManager.validateSupabaseConnection(config.supabase);
  if (!supabaseValid) {
    throw new Error('Supabase连接失败，请检查配置');
  }
  
  console.log('配置验证通过，迁移系统已就绪');
  return config;
}