<template>
  <div class="customer-filters">
    <!-- 搜索头部 -->
    <div class="search-header">
      <div class="search-input-wrapper">
        <n-input
          v-model:value="searchForm.search"
          placeholder="搜索客户姓名、电话、公司"
          clearable
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <n-icon :component="Search" />
          </template>
        </n-input>
      </div>
      <div class="search-header-actions">
        <n-button
          type="primary"
          @click="handleSearch"
          :loading="loading"
        >
          <template #icon>
            <n-icon :component="Search" />
          </template>
          搜索
        </n-button>
        <n-button @click="toggleAdvancedSearch">
          <template #icon>
            <n-icon :component="Filter" />
          </template>
          {{ showAdvancedSearch ? '收起筛选' : '高级筛选' }}
        </n-button>
        <n-button @click="handleReset">
          <template #icon>
            <n-icon :component="Refresh" />
          </template>
          重置
        </n-button>
      </div>
    </div>

    <!-- 高级搜索表单 -->
    <n-collapse-transition :show="showAdvancedSearch">
      <n-card class="search-card" size="small">
        <n-form
          ref="searchFormRef"
          :model="searchForm"
          class="search-form"
          label-placement="left"
          label-width="80px"
        >
          <n-grid :cols="24" :x-gap="16" :y-gap="8">
            <!-- 客户状态 -->
            <n-form-item-gi :span="6" label="客户状态">
              <n-select
                v-model:value="searchForm.status"
                placeholder="请选择状态"
                clearable
                :options="statusOptions"
              />
            </n-form-item-gi>

            <!-- 客户等级 -->
            <n-form-item-gi :span="6" label="客户等级">
              <n-select
                v-model:value="searchForm.level"
                placeholder="请选择等级"
                clearable
                :options="levelOptions"
              />
            </n-form-item-gi>

            <!-- 客户来源 -->
            <n-form-item-gi :span="6" label="客户来源">
              <n-select
                v-model:value="searchForm.source"
                placeholder="请选择来源"
                clearable
                :options="sourceOptions"
              />
            </n-form-item-gi>

            <!-- 分配人员 -->
            <n-form-item-gi :span="6" label="分配人员">
              <n-select
                v-model:value="searchForm.assigned_to"
                placeholder="选择分配人员"
                clearable
                multiple
                :options="assignedToOptions"
              />
            </n-form-item-gi>

            <!-- 时间范围 -->
            <n-form-item-gi :span="8" label="时间范围">
              <n-date-picker
                v-model:value="searchForm.date_range"
                type="daterange"
                clearable
                placeholder="选择时间范围"
                style="width: 100%"
              />
            </n-form-item-gi>

            <!-- 客户标签 -->
            <n-form-item-gi :span="8" label="客户标签">
              <n-select
                v-model:value="searchForm.tags"
                placeholder="请选择标签"
                multiple
                clearable
                :options="tagOptions"
                :loading="tagsLoading"
              />
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </n-card>
    </n-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import {
  NInput,
  NButton,
  NIcon,
  NCard,
  NForm,
  NFormItemGi,
  NGrid,
  NSelect,
  NInputGroup,
  NInputNumber,
  NDatePicker,
  NCollapseTransition,
  NSpace,
  NTag,
  type FormInst
} from 'naive-ui'
import {
  Search,
  Filter,
  Refresh
} from '@vicons/ionicons5'
import type { CustomerFilters } from '@/api/customerService'

// Props
interface Props {
  loading?: boolean
  modelValue?: CustomerFilters
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  modelValue: () => ({})
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: CustomerFilters): void
  (e: 'search', filters: CustomerFilters): void
  (e: 'reset'): void
}

const emit = defineEmits<Emits>()

// Refs
const searchFormRef = ref<FormInst>()
const showAdvancedSearch = ref(false)
const tagsLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  search: '',
  status: [],
  level: [],
  source: [],
  assigned_to: [],
  date_range: undefined,
  tags: []
})

// 选项数据
const statusOptions = [
  { label: '潜在客户', value: 'potential' },
  { label: '意向客户', value: 'interested' },
  { label: '跟进中', value: 'following' },
  { label: '已成交', value: 'deal' },
  { label: '已失效', value: 'invalid' }
]

const levelOptions = [
  { label: 'A级客户', value: 'A' },
  { label: 'B级客户', value: 'B' },
  { label: 'C级客户', value: 'C' },
  { label: 'D级客户', value: 'D' }
]

const sourceOptions = [
  { label: '线上推广', value: 'online' },
  { label: '朋友介绍', value: 'referral' },
  { label: '电话营销', value: 'telemarketing' },
  { label: '门店咨询', value: 'store' },
  { label: '展会活动', value: 'exhibition' },
  { label: '其他', value: 'other' }
]

const assignedToOptions = [
  { label: '张三', value: 'user1' },
  { label: '李四', value: 'user2' },
  { label: '王五', value: 'user3' },
  { label: '赵六', value: 'user4' }
]

const tagOptions = ref<Array<{ label: string; value: string }>>([])

// 计算属性
const hasAdvancedFilters = computed(() => {
  return (
    (searchForm.status && searchForm.status.length > 0) ||
    (searchForm.level && searchForm.level.length > 0) ||
    (searchForm.source && searchForm.source.length > 0) ||
    (searchForm.assigned_to && searchForm.assigned_to.length > 0) ||
    searchForm.date_range ||
    (searchForm.tags && searchForm.tags.length > 0)
  )
})

// 方法
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const handleSearch = () => {
  emit('search', { ...searchForm })
}

const handleReset = () => {
  // 重置表单
  Object.assign(searchForm, {
    search: '',
    status: [],
    level: [],
    source: [],
    assigned_to: [],
    date_range: undefined,
    tags: []
  })
  
  showAdvancedSearch.value = false
  emit('reset')
}

// 加载标签选项
const loadTagOptions = async () => {
  try {
    tagsLoading.value = true
    // TODO: 从API加载标签选项
    // const tags = await customerService.getCustomerTags()
    // tagOptions.value = tags.map(tag => ({ label: tag.name, value: tag.id }))
    
    // 模拟数据
    tagOptions.value = [
      { label: '高端客户', value: 'high_end' },
      { label: '价格敏感', value: 'price_sensitive' },
      { label: '设计要求高', value: 'design_focused' },
      { label: '时间紧急', value: 'urgent' },
      { label: '预算充足', value: 'sufficient_budget' }
    ]
  } catch (error) {
    console.error('加载标签选项失败:', error)
  } finally {
    tagsLoading.value = false
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(searchForm, newValue)
    }
  },
  { immediate: true, deep: true }
)

// 监听表单变化，同步到父组件
watch(
  searchForm,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  loadTagOptions()
})
</script>

<style scoped>
.customer-filters {
  margin-bottom: 16px;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.search-input-wrapper {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
}

.search-header-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.search-card {
  margin-top: 12px;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-form :deep(.n-form-item-gi) {
  margin-bottom: 8px;
}

.search-form :deep(.n-form-item-label) {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.search-form :deep(.n-input),
.search-form :deep(.n-select),
.search-form :deep(.n-date-picker) {
  font-size: 13px;
}

.search-form :deep(.n-base-selection-input__content) {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-header-actions {
    align-self: flex-end;
  }

  .search-form :deep(.n-grid) {
    grid-template-columns: repeat(3, 1fr);
  }

  .search-form :deep(.n-form-item-gi[data-span="8"]) {
    grid-column: span 8;
  }
}

@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-header-actions {
    align-self: stretch;
  }

  .search-form :deep(.n-form-item-gi) {
    grid-column: span 24 !important;
  }
}

@media (max-width: 480px) {
  .search-form :deep(.n-grid) {
    grid-template-columns: 1fr;
  }
}
</style>