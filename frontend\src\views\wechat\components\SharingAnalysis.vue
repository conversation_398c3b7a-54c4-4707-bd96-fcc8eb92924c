<template>
  <div class="sharing-analysis">
    <!-- 分析配置 -->
    <div class="config-section">
      <n-form inline :model="configForm" label-placement="left">
        <n-form-item label="分析维度">
          <n-select
            v-model:value="configForm.dimension"
            placeholder="选择分析维度"
            style="width: 150px"
            :options="dimensionOptions"
          />
        </n-form-item>
        <n-form-item label="时间范围">
          <n-date-picker
            v-model:value="configForm.time_range"
            type="daterange"
            clearable
            style="width: 240px"
          />
        </n-form-item>
        <n-form-item label="对比分析">
          <n-checkbox v-model:checked="configForm.compare_enabled">
            启用对比
          </n-checkbox>
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleAnalyze" :loading="loading">
              <template #icon>
                <n-icon><AnalyticsOutline /></n-icon>
              </template>
              开始分析
            </n-button>
            <n-button @click="handleExportReport">
              <template #icon>
                <n-icon><DownloadOutline /></n-icon>
              </template>
              导出报告
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-results" v-if="analysisData">
      <!-- 概览指标 -->
      <n-card title="分享效果概览" class="overview-card">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic label="总分享次数" :value="analysisData.overview.total_shares">
              <template #prefix>
                <n-icon color="#18a058">
                  <ShareSocialOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="总浏览量" :value="analysisData.overview.total_views">
              <template #prefix>
                <n-icon color="#2080f0">
                  <EyeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="平均转化率" :value="analysisData.overview.avg_conversion_rate + '%'">
              <template #prefix>
                <n-icon color="#f0a020">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="总收益" :value="'¥' + analysisData.overview.total_revenue">
              <template #prefix>
                <n-icon color="#d03050">
                  <CashOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 趋势分析图 -->
      <n-card title="分享趋势分析" class="trend-card">
        <template #header-extra>
          <n-space>
            <n-select
              v-model:value="trendMetric"
              size="small"
              style="width: 120px"
              :options="trendMetricOptions"
            />
            <n-button size="small" @click="refreshTrendChart">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
            </n-button>
          </n-space>
        </template>
        <div class="trend-chart" ref="trendChartRef"></div>
      </n-card>

      <!-- 分享类型分析 -->
      <n-grid :cols="2" :x-gap="16" class="type-analysis-grid">
        <n-grid-item>
          <n-card title="分享类型分布">
            <div class="type-chart" ref="typeChartRef"></div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card title="平台分布">
            <div class="platform-chart" ref="platformChartRef"></div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 用户分享行为分析 -->
      <n-card title="用户分享行为分析" class="behavior-card">
        <div class="behavior-metrics">
          <n-grid :cols="3" :x-gap="16">
            <n-grid-item>
              <div class="behavior-metric">
                <div class="metric-title">活跃分享用户</div>
                <div class="metric-value">{{ analysisData.user_behavior.active_sharers }}</div>
                <div class="metric-trend">
                  <n-icon color="#18a058">
                    <TrendingUpOutline />
                  </n-icon>
                  <span>+12.5%</span>
                </div>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="behavior-metric">
                <div class="metric-title">平均分享频次</div>
                <div class="metric-value">{{ analysisData.user_behavior.avg_share_frequency }}</div>
                <div class="metric-trend">
                  <n-icon color="#f0a020">
                    <TrendingDownOutline />
                  </n-icon>
                  <span>-3.2%</span>
                </div>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="behavior-metric">
                <div class="metric-title">分享转化率</div>
                <div class="metric-value">{{ analysisData.user_behavior.share_conversion_rate }}%</div>
                <div class="metric-trend">
                  <n-icon color="#18a058">
                    <TrendingUpOutline />
                  </n-icon>
                  <span>+8.7%</span>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </div>
        
        <!-- 用户分享排行 -->
        <div class="user-ranking">
          <h5>分享达人排行</h5>
          <div class="ranking-list">
            <div class="ranking-item" v-for="(user, index) in analysisData.top_sharers" :key="user.user_id">
              <div class="ranking-number">
                <span class="rank-badge" :class="getRankClass(index + 1)">{{ index + 1 }}</span>
              </div>
              <div class="user-info">
                <n-avatar size="small" :src="user.avatar" fallback-src="/default-avatar.png" />
                <div class="user-details">
                  <div class="user-name">{{ user.nickname }}</div>
                  <div class="user-stats">分享{{ user.share_count }}次 · 转化{{ user.conversion_count }}次</div>
                </div>
              </div>
              <div class="user-metrics">
                <div class="metric-item">
                  <span class="metric-label">转化率</span>
                  <span class="metric-value">{{ user.conversion_rate }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">收益</span>
                  <span class="metric-value">¥{{ user.revenue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 内容分析 -->
      <n-card title="分享内容分析" class="content-card">
        <div class="content-performance">
          <h5>热门分享内容</h5>
          <n-list>
            <n-list-item v-for="content in analysisData.popular_content" :key="content.id">
              <div class="content-item">
                <div class="content-info">
                  <div class="content-title">{{ content.title }}</div>
                  <div class="content-stats">
                    <span>分享{{ content.share_count }}次</span>
                    <span>浏览{{ content.view_count }}次</span>
                    <span>转化{{ content.conversion_count }}次</span>
                  </div>
                </div>
                <div class="content-metrics">
                  <n-progress
                    type="circle"
                    :percentage="content.conversion_rate"
                    :stroke-width="6"
                    :size="60"
                  >
                    <span style="font-size: 10px">{{ content.conversion_rate }}%</span>
                  </n-progress>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </div>
      </n-card>

      <!-- 时间分析 -->
      <n-card title="时间分布分析" class="time-card">
        <div class="time-analysis">
          <div class="time-heatmap" ref="timeHeatmapRef"></div>
        </div>
      </n-card>

      <!-- 转化漏斗分析 -->
      <n-card title="转化漏斗分析" class="funnel-card">
        <div class="funnel-chart" ref="funnelChartRef"></div>
      </n-card>

      <!-- 优化建议 -->
      <n-card title="优化建议" class="suggestions-card">
        <div class="suggestions-list">
          <div class="suggestion-item" v-for="suggestion in analysisData.suggestions" :key="suggestion.id">
            <div class="suggestion-priority">
              <n-tag :type="getPriorityType(suggestion.priority)" size="small">
                {{ suggestion.priority }}
              </n-tag>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
              <div class="suggestion-impact">
                <span>预期提升: {{ suggestion.expected_improvement }}</span>
                <span>实施难度: {{ suggestion.difficulty }}</span>
              </div>
            </div>
            <div class="suggestion-actions">
              <n-button size="small" type="primary" @click="handleImplementSuggestion(suggestion)">
                实施
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!analysisData && !loading">
      <n-empty description="请选择分享记录并开始分析">
        <template #icon>
          <n-icon size="60" color="#ccc">
            <AnalyticsOutline />
          </n-icon>
        </template>
        <template #extra>
          <n-button type="primary" @click="handleAnalyze">
            开始分析
          </n-button>
        </template>
      </n-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import {
  NCard,
  NForm,
  NFormItem,
  NSelect,
  NDatePicker,
  NCheckbox,
  NButton,
  NSpace,
  NIcon,
  NGrid,
  NGridItem,
  NStatistic,
  NList,
  NListItem,
  NProgress,
  NTag,
  NEmpty,
  NAvatar,
  useMessage
} from 'naive-ui'
import {
  AnalyticsOutline,
  DownloadOutline,
  ShareSocialOutline,
  EyeOutline,
  TrendingUpOutline,
  TrendingDownOutline,
  CashOutline,
  RefreshOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

interface Props {
  shareIds: number[]
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 配置表单
const configForm = reactive({
  dimension: 'time',
  time_range: null,
  compare_enabled: false
})

// 选项数据
const dimensionOptions = [
  { label: '时间维度', value: 'time' },
  { label: '用户维度', value: 'user' },
  { label: '内容维度', value: 'content' },
  { label: '平台维度', value: 'platform' }
]

const trendMetricOptions = [
  { label: '分享次数', value: 'shares' },
  { label: '浏览量', value: 'views' },
  { label: '转化率', value: 'conversion_rate' },
  { label: '收益', value: 'revenue' }
]

// 状态
const loading = ref(false)
const analysisData = ref<any>(null)
const trendMetric = ref('shares')

// 引用
const trendChartRef = ref<HTMLElement>()
const typeChartRef = ref<HTMLElement>()
const platformChartRef = ref<HTMLElement>()
const timeHeatmapRef = ref<HTMLElement>()
const funnelChartRef = ref<HTMLElement>()

// 图表实例
let trendChart: echarts.ECharts | null = null
let typeChart: echarts.ECharts | null = null
let platformChart: echarts.ECharts | null = null
let timeHeatmap: echarts.ECharts | null = null
let funnelChart: echarts.ECharts | null = null

// 方法
const handleAnalyze = async () => {
  if (!props.shareIds.length) {
    message.warning('请先选择要分析的分享记录')
    return
  }
  
  loading.value = true
  try {
    // 模拟分析数据
    analysisData.value = {
      overview: {
        total_shares: 1250,
        total_views: 15680,
        avg_conversion_rate: 15.8,
        total_revenue: 45680
      },
      trend_data: {
        dates: ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        shares: [120, 150, 180, 160, 200],
        views: [1200, 1800, 2200, 1900, 2500],
        conversion_rate: [12, 15, 18, 14, 20],
        revenue: [3600, 5400, 6600, 5700, 7500]
      },
      share_type_distribution: {
        moments: 45,
        group: 30,
        private: 20,
        official: 5
      },
      platform_distribution: {
        wechat: 70,
        weibo: 15,
        qq: 10,
        other: 5
      },
      user_behavior: {
        active_sharers: 456,
        avg_share_frequency: 2.8,
        share_conversion_rate: 15.8
      },
      top_sharers: [
        {
          user_id: 'wx_001',
          nickname: '张小明',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=young%20man%20avatar%20profile%20picture&image_size=square',
          share_count: 45,
          conversion_count: 12,
          conversion_rate: 26.7,
          revenue: 3600
        },
        {
          user_id: 'wx_002',
          nickname: '李美丽',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=young%20woman%20avatar%20profile%20picture&image_size=square',
          share_count: 38,
          conversion_count: 9,
          conversion_rate: 23.7,
          revenue: 2700
        },
        {
          user_id: 'wx_003',
          nickname: '王大力',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=middle%20aged%20man%20avatar%20profile%20picture&image_size=square',
          share_count: 32,
          conversion_count: 7,
          conversion_rate: 21.9,
          revenue: 2100
        }
      ],
      popular_content: [
        {
          id: 1,
          title: '春季新品发布会',
          share_count: 156,
          view_count: 2340,
          conversion_count: 45,
          conversion_rate: 28.8
        },
        {
          id: 2,
          title: '限时优惠活动',
          share_count: 134,
          view_count: 1890,
          conversion_count: 32,
          conversion_rate: 23.9
        },
        {
          id: 3,
          title: '用户体验分享',
          share_count: 98,
          view_count: 1456,
          conversion_count: 18,
          conversion_rate: 18.4
        }
      ],
      time_heatmap_data: [
        // 24小时 x 7天的热力图数据
        [0, 0, 5], [0, 1, 8], [0, 2, 12], [0, 3, 15],
        [1, 0, 8], [1, 1, 12], [1, 2, 18], [1, 3, 22],
        [2, 0, 12], [2, 1, 18], [2, 2, 25], [2, 3, 30]
        // ... 更多数据
      ],
      funnel_data: [
        { name: '分享曝光', value: 10000 },
        { name: '点击查看', value: 6500 },
        { name: '访问页面', value: 4200 },
        { name: '产生兴趣', value: 2800 },
        { name: '完成转化', value: 1580 }
      ],
      suggestions: [
        {
          id: 1,
          title: '优化分享时机',
          description: '根据数据分析，晚上8-10点是分享的黄金时间，建议引导用户在此时段分享',
          priority: '高',
          expected_improvement: '转化率提升15%',
          difficulty: '简单'
        },
        {
          id: 2,
          title: '增加分享激励',
          description: '为活跃分享用户提供专属优惠或积分奖励，提高分享积极性',
          priority: '高',
          expected_improvement: '分享量增加25%',
          difficulty: '中等'
        },
        {
          id: 3,
          title: '优化分享内容',
          description: '根据热门内容特征，优化其他内容的标题和描述，提高吸引力',
          priority: '中',
          expected_improvement: '点击率提升20%',
          difficulty: '中等'
        }
      ]
    }
    
    // 渲染图表
    await nextTick()
    renderCharts()
    
    message.success('分享分析完成')
  } catch (error) {
    message.error('分析失败')
  } finally {
    loading.value = false
  }
}

const handleExportReport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const refreshTrendChart = () => {
  if (trendChart && analysisData.value) {
    renderTrendChart()
  }
}

const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-gold'
  if (rank === 2) return 'rank-silver'
  if (rank === 3) return 'rank-bronze'
  return 'rank-normal'
}

const getPriorityType = (priority: string): 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary' => {
  const typeMap: Record<string, 'success' | 'error' | 'info' | 'warning' | 'default' | 'primary'> = {
    '高': 'error',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'default'
}

const handleImplementSuggestion = (suggestion: any) => {
  // TODO: 实现建议实施功能
  message.info(`开始实施建议: ${suggestion.title}`)
}

// 图表渲染
const renderCharts = () => {
  renderTrendChart()
  renderTypeChart()
  renderPlatformChart()
  renderTimeHeatmap()
  renderFunnelChart()
}

const renderTrendChart = () => {
  if (!trendChartRef.value || !analysisData.value) return
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }
  
  const data = analysisData.value.trend_data
  const metricData = data[trendMetric.value]
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'line',
        data: metricData,
        smooth: true,
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

const renderTypeChart = () => {
  if (!typeChartRef.value || !analysisData.value) return
  
  if (!typeChart) {
    typeChart = echarts.init(typeChartRef.value)
  }
  
  const data = analysisData.value.share_type_distribution
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)'
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        data: [
          { value: data.moments, name: '朋友圈' },
          { value: data.group, name: '群聊' },
          { value: data.private, name: '私聊' },
          { value: data.official, name: '公众号' }
        ]
      }
    ]
  }
  
  typeChart.setOption(option)
}

const renderPlatformChart = () => {
  if (!platformChartRef.value || !analysisData.value) return
  
  if (!platformChart) {
    platformChart = echarts.init(platformChartRef.value)
  }
  
  const data = analysisData.value.platform_distribution
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: data.wechat, name: '微信' },
          { value: data.weibo, name: '微博' },
          { value: data.qq, name: 'QQ' },
          { value: data.other, name: '其他' }
        ]
      }
    ]
  }
  
  platformChart.setOption(option)
}

const renderTimeHeatmap = () => {
  if (!timeHeatmapRef.value || !analysisData.value) return
  
  if (!timeHeatmap) {
    timeHeatmap = echarts.init(timeHeatmapRef.value)
  }
  
  const hours = Array.from({ length: 24 }, (_, i) => i + 'h')
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  const option = {
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 50,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [
      {
        name: '分享热度',
        type: 'heatmap',
        data: analysisData.value.time_heatmap_data,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  timeHeatmap.setOption(option)
}

const renderFunnelChart = () => {
  if (!funnelChartRef.value || !analysisData.value) return
  
  if (!funnelChart) {
    funnelChart = echarts.init(funnelChartRef.value)
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}'
    },
    series: [
      {
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: analysisData.value.funnel_data
      }
    ]
  }
  
  funnelChart.setOption(option)
}

// 监听趋势指标变化
watch(trendMetric, () => {
  refreshTrendChart()
})

// 响应式处理
const handleResize = () => {
  trendChart?.resize()
  typeChart?.resize()
  platformChart?.resize()
  timeHeatmap?.resize()
  funnelChart?.resize()
}

// 初始化
onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 如果有传入的分享ID，自动开始分析
  if (props.shareIds.length > 0) {
    handleAnalyze()
  }
})

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  typeChart?.dispose()
  platformChart?.dispose()
  timeHeatmap?.dispose()
  funnelChart?.dispose()
}

// 组件卸载时清理
const { onBeforeUnmount } = require('vue')
onBeforeUnmount(cleanup)
</script>

<style scoped>
.sharing-analysis {
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.overview-card,
.trend-card,
.behavior-card,
.content-card,
.time-card,
.funnel-card,
.suggestions-card {
  margin-bottom: 0;
}

.trend-chart,
.type-chart,
.platform-chart,
.time-heatmap,
.funnel-chart {
  height: 300px;
}

.type-analysis-grid {
  margin-bottom: 0;
}

.behavior-metrics {
  margin-bottom: 24px;
}

.behavior-metric {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.user-ranking h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.ranking-number {
  display: flex;
  align-items: center;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.rank-gold {
  background: #ffd700;
}

.rank-silver {
  background: #c0c0c0;
}

.rank-bronze {
  background: #cd7f32;
}

.rank-normal {
  background: #666;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
}

.user-stats {
  font-size: 12px;
  color: #666;
}

.user-metrics {
  display: flex;
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
}

.content-performance h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.content-item:last-child {
  border-bottom: none;
}

.content-info {
  flex: 1;
}

.content-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.content-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.content-metrics {
  display: flex;
  align-items: center;
}

.time-analysis {
  padding: 16px 0;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.suggestion-priority {
  display: flex;
  align-items: center;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.suggestion-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.suggestion-impact {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.suggestion-actions {
  display: flex;
  align-items: center;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}
</style>