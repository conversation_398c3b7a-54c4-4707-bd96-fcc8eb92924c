# YYSH FastAPI应用环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置值

# =============================================================================
# 应用基础配置
# =============================================================================

# 应用名称
APP_NAME=YYSH-API

# 运行环境: development, production, testing
APP_ENVIRONMENT=development

# 调试模式
APP_DEBUG=true

# 服务器配置
APP_HOST=0.0.0.0
APP_PORT=8000

# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
APP_LOG_LEVEL=INFO

# API文档路径
APP_DOCS_URL=/docs
APP_REDOC_URL=/redoc

# 应用版本
APP_VERSION=1.0.0

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=yysh_db
DATABASE_USER=yysh_user
DATABASE_PASSWORD=your_password_here

# 数据库连接池配置
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# 数据库SSL配置
DATABASE_SSL_MODE=prefer

# 数据库连接字符串（可选，会覆盖上述单独配置）
# DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# =============================================================================
# Redis配置
# =============================================================================

# Redis服务器配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis连接池配置
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# Redis连接字符串（可选）
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# 安全配置
# =============================================================================

# JWT密钥（生产环境请使用强密钥）
SECRET_KEY=your-super-secret-key-change-this-in-production

# JWT算法
JWT_ALGORITHM=HS256

# JWT过期时间（分钟）
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码加密轮数
PASSWORD_HASH_ROUNDS=12

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOW_HEADERS=*

# =============================================================================
# 文件存储配置
# =============================================================================

# 上传文件配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
UPLOAD_PATH=uploads

# 静态文件配置
STATIC_FILES_PATH=static
STATIC_FILES_URL=/static

# =============================================================================
# 邮件配置
# =============================================================================

# SMTP服务器配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# 邮件发送配置
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=YYSH System

# =============================================================================
# 缓存配置
# =============================================================================

# 缓存后端: redis, memory
CACHE_BACKEND=redis

# 缓存过期时间（秒）
CACHE_DEFAULT_EXPIRE=3600
CACHE_USER_SESSION_EXPIRE=1800
CACHE_API_RESPONSE_EXPIRE=300

# =============================================================================
# 任务队列配置
# =============================================================================

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# =============================================================================
# 监控和日志配置
# =============================================================================

# Sentry配置（错误监控）
SENTRY_DSN=

# 日志文件配置
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# 第三方服务配置
# =============================================================================

# 微信配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=
WECHAT_MERCHANT_ID=
WECHAT_API_KEY=

# 支付宝配置
ALIPAY_APP_ID=
ALIPAY_PRIVATE_KEY=
ALIPAY_PUBLIC_KEY=

# 短信服务配置
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=
SMS_TEMPLATE_CODE=

# 对象存储配置（阿里云OSS/腾讯云COS等）
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_BUCKET_NAME=
OSS_ENDPOINT=
OSS_REGION=

# =============================================================================
# 开发和测试配置
# =============================================================================

# 是否跳过数据库初始化
SKIP_DB_INIT=false

# 是否启用API文档
ENABLE_DOCS=true

# 是否启用调试工具
ENABLE_DEBUG_TOOLBAR=false

# 测试数据库配置
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_yysh_db

# 工作进程数量（生产环境）
WORKERS=4

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# API限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60  # 秒