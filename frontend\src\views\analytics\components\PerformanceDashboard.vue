<template>
  <div class="performance-dashboard">
    <!-- 仪表板配置 -->
    <n-card title="仪表板配置" class="config-card">
      <div class="config-form">
        <div class="form-row">
          <div class="form-item">
            <label>时间范围</label>
            <n-date-picker v-model:value="dashboardConfig.dateRange" type="daterange" clearable />
          </div>
          <div class="form-item">
            <label>业务线</label>
            <n-select v-model:value="dashboardConfig.businessLine" :options="businessLineOptions" multiple placeholder="选择业务线" />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>销售团队</label>
            <n-select v-model:value="dashboardConfig.salesTeam" :options="salesTeamOptions" multiple placeholder="选择销售团队" />
          </div>
          <div class="form-item">
            <label>对比周期</label>
            <n-select v-model:value="dashboardConfig.compareWith" :options="compareOptions" placeholder="选择对比周期" />
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 核心指标概览 -->
    <n-card title="核心业绩指标" class="metrics-card">
      <div class="metrics-overview">
        <div class="metric-item" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-icon">
            <n-icon :color="metric.color"><component :is="metric.icon" /></n-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-change" :class="metric.change >= 0 ? 'positive' : 'negative'">
              <n-icon><TrendingUpOutline v-if="metric.change >= 0" /><TrendingDownOutline v-else /></n-icon>
              {{ Math.abs(metric.change) }}% {{ metric.compareLabel }}
            </div>
          </div>
          <div class="metric-chart">
            <div :ref="el => metricChartRefs[metric.key] = el as HTMLElement" class="mini-chart"></div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 销售业绩趋势 -->
    <div class="chart-row">
      <n-card title="销售业绩趋势" class="trend-card">
        <div class="chart-container">
          <div ref="salesTrendChartRef" class="chart"></div>
        </div>
      </n-card>
      
      <n-card title="目标完成情况" class="target-card">
        <div class="chart-container">
          <div ref="targetChartRef" class="chart"></div>
        </div>
      </n-card>
    </div>
    
    <!-- 团队业绩排行 -->
    <n-card title="团队业绩排行" class="ranking-card">
      <div class="ranking-content">
        <div class="ranking-list">
          <div class="ranking-item" v-for="(team, index) in teamRanking" :key="team.id">
            <div class="ranking-position">
              <div class="position-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
              <div v-if="index < 3" class="position-medal">
                <n-icon :color="index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32'">
                  <TrophyOutline />
                </n-icon>
              </div>
            </div>
            
            <div class="ranking-info">
              <div class="team-name">{{ team.name }}</div>
              <div class="team-leader">负责人: {{ team.leader }}</div>
            </div>
            
            <div class="ranking-stats">
              <div class="stat-item">
                <div class="stat-value">¥{{ team.revenue.toLocaleString() }}</div>
                <div class="stat-label">销售额</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ team.targetCompletion }}%</div>
                <div class="stat-label">目标完成率</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ team.customerCount }}</div>
                <div class="stat-label">客户数</div>
              </div>
            </div>
            
            <div class="ranking-progress">
              <n-progress
                type="line"
                :percentage="team.targetCompletion"
                :color="team.targetCompletion >= 100 ? '#18a058' : team.targetCompletion >= 80 ? '#f0a020' : '#d03050'"
                :show-indicator="false"
              />
            </div>
          </div>
        </div>
      </div>
    </n-card>
    
    <!-- 销售漏斗和转化分析 -->
    <div class="chart-row">
      <n-card title="销售漏斗分析" class="funnel-card">
        <div class="chart-container">
          <div ref="funnelChartRef" class="chart"></div>
        </div>
      </n-card>
      
      <n-card title="客户来源分析" class="source-card">
        <div class="chart-container">
          <div ref="sourceChartRef" class="chart"></div>
        </div>
      </n-card>
    </div>
    
    <!-- 产品业绩分析 -->
    <n-card title="产品业绩分析" class="product-card">
      <div class="product-analysis">
        <div class="product-tabs">
          <n-tabs v-model:value="activeProductTab" type="line">
            <n-tab-pane name="revenue" tab="销售收入">
              <div class="chart-container">
                <div ref="productRevenueChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
            <n-tab-pane name="quantity" tab="销售数量">
              <div class="chart-container">
                <div ref="productQuantityChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
            <n-tab-pane name="profit" tab="利润分析">
              <div class="chart-container">
                <div ref="productProfitChartRef" class="chart"></div>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>
    </n-card>
    
    <!-- 业绩预测和建议 -->
    <div class="chart-row">
      <n-card title="业绩预测" class="forecast-card">
        <div class="chart-container">
          <div ref="forecastChartRef" class="chart"></div>
        </div>
      </n-card>
      
      <n-card title="业绩提升建议" class="suggestions-card">
        <div class="suggestions-list">
          <div class="suggestion-item" v-for="suggestion in performanceSuggestions" :key="suggestion.id">
            <div class="suggestion-icon">
              <n-icon :color="suggestion.priority === 'high' ? '#d03050' : suggestion.priority === 'medium' ? '#f0a020' : '#18a058'">
                <BulbOutline />
              </n-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-description">{{ suggestion.description }}</div>
              <div class="suggestion-impact">预期提升: {{ suggestion.expectedImpact }}</div>
            </div>
            <div class="suggestion-priority">
              <n-tag :type="suggestion.priority === 'high' ? 'error' : suggestion.priority === 'medium' ? 'warning' : 'success'">
                {{ suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级' }}
              </n-tag>
            </div>
          </div>
        </div>
      </n-card>
    </div>
    
    <!-- 实时业绩监控 -->
    <n-card title="实时业绩监控" class="realtime-card">
      <div class="realtime-content">
        <div class="realtime-stats">
          <div class="realtime-item" v-for="item in realtimeStats" :key="item.key">
            <div class="realtime-label">{{ item.label }}</div>
            <div class="realtime-value" :class="item.trend">
              {{ item.value }}
              <span class="realtime-trend">
                <n-icon><TrendingUpOutline v-if="item.trend === 'up'" /><TrendingDownOutline v-else /></n-icon>
              </span>
            </div>
            <div class="realtime-change">{{ item.change }}</div>
          </div>
        </div>
        
        <div class="realtime-alerts">
          <div class="alert-item" v-for="alert in realtimeAlerts" :key="alert.id">
            <div class="alert-icon">
              <n-icon :color="alert.type === 'warning' ? '#f0a020' : alert.type === 'error' ? '#d03050' : '#18a058'">
                <WarningOutline v-if="alert.type === 'warning'" />
                <CloseCircleOutline v-else-if="alert.type === 'error'" />
                <CheckmarkCircleOutline v-else />
              </n-icon>
            </div>
            <div class="alert-content">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-time">{{ alert.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import {
  NCard, NSelect, NDatePicker, NIcon, NProgress, NTabs, NTabPane, NTag
} from 'naive-ui'
import {
  TrendingUpOutline, TrendingDownOutline, TrophyOutline, BulbOutline,
  WarningOutline, CloseCircleOutline, CheckmarkCircleOutline,
  CashOutline, PersonOutline, CartOutline, AtOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'

const salesTrendChartRef = ref<HTMLElement>()
const targetChartRef = ref<HTMLElement>()
const funnelChartRef = ref<HTMLElement>()
const sourceChartRef = ref<HTMLElement>()
const productRevenueChartRef = ref<HTMLElement>()
const productQuantityChartRef = ref<HTMLElement>()
const productProfitChartRef = ref<HTMLElement>()
const forecastChartRef = ref<HTMLElement>()
const metricChartRefs = ref<Record<string, HTMLElement | null>>({})

const activeProductTab = ref('revenue')

// 仪表板配置
const dashboardConfig = reactive({
  dateRange: null,
  businessLine: [],
  salesTeam: [],
  compareWith: 'last_month'
})

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '总销售额',
    value: '¥2,580,000',
    change: 12.5,
    compareLabel: '环比上月',
    color: '#2080f0',
    icon: CashOutline
  },
  {
    key: 'customers',
    label: '新增客户',
    value: '1,245',
    change: 8.3,
    compareLabel: '环比上月',
    color: '#18a058',
    icon: PersonOutline
  },
  {
    key: 'orders',
    label: '订单数量',
    value: '3,680',
    change: -2.1,
    compareLabel: '环比上月',
    color: '#f0a020',
    icon: CartOutline
  },
  {
    key: 'target',
    label: '目标完成率',
    value: '87.5%',
    change: 5.2,
    compareLabel: '环比上月',
    color: '#d03050',
    icon: AtOutline
  }
])

// 团队排行数据
const teamRanking = ref([
  {
    id: 1,
    name: '华东销售团队',
    leader: '张经理',
    revenue: 850000,
    targetCompletion: 112,
    customerCount: 320
  },
  {
    id: 2,
    name: '华南销售团队',
    leader: '李经理',
    revenue: 720000,
    targetCompletion: 96,
    customerCount: 280
  },
  {
    id: 3,
    name: '华北销售团队',
    leader: '王经理',
    revenue: 680000,
    targetCompletion: 91,
    customerCount: 260
  },
  {
    id: 4,
    name: '西南销售团队',
    leader: '赵经理',
    revenue: 520000,
    targetCompletion: 78,
    customerCount: 190
  },
  {
    id: 5,
    name: '华中销售团队',
    leader: '刘经理',
    revenue: 480000,
    targetCompletion: 72,
    customerCount: 175
  }
])

// 业绩提升建议
const performanceSuggestions = ref([
  {
    id: 1,
    title: '加强客户跟进',
    description: '提高客户跟进频率，缩短销售周期',
    expectedImpact: '销售额提升15%',
    priority: 'high'
  },
  {
    id: 2,
    title: '优化产品组合',
    description: '推广高利润产品，提升整体盈利能力',
    expectedImpact: '利润率提升8%',
    priority: 'medium'
  },
  {
    id: 3,
    title: '扩大市场覆盖',
    description: '开拓新的市场区域和客户群体',
    expectedImpact: '客户数增长20%',
    priority: 'medium'
  },
  {
    id: 4,
    title: '提升团队技能',
    description: '加强销售培训，提高团队专业能力',
    expectedImpact: '转化率提升12%',
    priority: 'low'
  }
])

// 实时统计数据
const realtimeStats = ref([
  {
    key: 'today_revenue',
    label: '今日销售额',
    value: '¥125,800',
    change: '+8.5% vs 昨日',
    trend: 'up'
  },
  {
    key: 'today_orders',
    label: '今日订单',
    value: '68',
    change: '+12 vs 昨日',
    trend: 'up'
  },
  {
    key: 'active_customers',
    label: '活跃客户',
    value: '1,245',
    change: '+35 vs 昨日',
    trend: 'up'
  },
  {
    key: 'conversion_rate',
    label: '转化率',
    value: '6.8%',
    change: '-0.3% vs 昨日',
    trend: 'down'
  }
])

// 实时预警
const realtimeAlerts = ref([
  {
    id: 1,
    type: 'warning',
    title: '目标完成预警',
    message: '华中销售团队本月目标完成率偏低，需要关注',
    time: '2分钟前'
  },
  {
    id: 2,
    type: 'success',
    title: '业绩突破',
    message: '华东销售团队提前完成月度目标',
    time: '15分钟前'
  },
  {
    id: 3,
    type: 'error',
    title: '客户流失预警',
    message: '检测到重要客户活跃度下降，建议及时跟进',
    time: '1小时前'
  }
])

// 选项数据
const businessLineOptions = [
  { label: '企业服务', value: 'enterprise' },
  { label: '消费电子', value: 'consumer' },
  { label: '工业设备', value: 'industrial' },
  { label: '医疗器械', value: 'medical' }
]

const salesTeamOptions = [
  { label: '华东销售团队', value: 'east' },
  { label: '华南销售团队', value: 'south' },
  { label: '华北销售团队', value: 'north' },
  { label: '西南销售团队', value: 'southwest' },
  { label: '华中销售团队', value: 'central' }
]

const compareOptions = [
  { label: '上月同期', value: 'last_month' },
  { label: '去年同期', value: 'last_year' },
  { label: '上季度', value: 'last_quarter' },
  { label: '自定义', value: 'custom' }
]

// 图表渲染方法
const renderSalesTrendChart = () => {
  if (!salesTrendChartRef.value) return
  
  const chart = echarts.init(salesTrendChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const currentYear = [1800, 2100, 2300, 2000, 2400, 2600, 2200, 2800, 2500, 2900, 2700, 2580]
  const lastYear = [1600, 1900, 2000, 1800, 2200, 2300, 2000, 2500, 2200, 2600, 2400, 2300]
  
  const option = {
    title: {
      text: '销售业绩趋势对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['今年', '去年'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}万'
      }
    },
    series: [
      {
        name: '今年',
        type: 'line',
        data: currentYear,
        lineStyle: { color: '#2080f0', width: 3 },
        areaStyle: { color: 'rgba(32, 128, 240, 0.1)' },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '去年',
        type: 'line',
        data: lastYear,
        lineStyle: { color: '#18a058', width: 3 },
        areaStyle: { color: 'rgba(24, 160, 88, 0.1)' },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }
  
  chart.setOption(option)
}

const renderTargetChart = () => {
  if (!targetChartRef.value) return
  
  const chart = echarts.init(targetChartRef.value)
  
  const targetData = teamRanking.value.map(team => ({
    name: team.name,
    value: team.targetCompletion
  }))
  
  const option = {
    title: {
      text: '各团队目标完成情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%'
    },
    series: [
      {
        name: '目标完成率',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: targetData.map((item, index) => ({
          ...item,
          itemStyle: {
            color: item.value >= 100 ? '#18a058' : item.value >= 80 ? '#f0a020' : '#d03050'
          }
        }))
      }
    ]
  }
  
  chart.setOption(option)
}

const renderFunnelChart = () => {
  if (!funnelChartRef.value) return
  
  const chart = echarts.init(funnelChartRef.value)
  
  const funnelData = [
    { value: 100, name: '潜在客户 (5000)' },
    { value: 60, name: '意向客户 (3000)' },
    { value: 35, name: '商机客户 (1750)' },
    { value: 20, name: '成交客户 (1000)' }
  ]
  
  const option = {
    title: {
      text: '销售漏斗分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%'
    },
    series: [
      {
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: funnelData
      }
    ]
  }
  
  chart.setOption(option)
}

const renderSourceChart = () => {
  if (!sourceChartRef.value) return
  
  const chart = echarts.init(sourceChartRef.value)
  
  const sourceData = [
    { value: 35, name: '线上推广' },
    { value: 25, name: '客户推荐' },
    { value: 20, name: '电话营销' },
    { value: 15, name: '展会活动' },
    { value: 5, name: '其他渠道' }
  ]
  
  const option = {
    title: {
      text: '客户来源分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '客户来源',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: sourceData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const renderProductCharts = () => {
  // 产品收入图表
  if (productRevenueChartRef.value) {
    const chart = echarts.init(productRevenueChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const revenue = [850, 720, 680, 520, 480]
    
    const option = {
      title: {
        text: '产品销售收入排行',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '¥{value}万'
        }
      },
      series: [
        {
          name: '销售收入',
          type: 'bar',
          data: revenue,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2080f0' },
              { offset: 1, color: '#1890ff' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
  
  // 产品数量图表
  if (productQuantityChartRef.value) {
    const chart = echarts.init(productQuantityChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const quantity = [1200, 980, 850, 720, 650]
    
    const option = {
      title: {
        text: '产品销售数量排行',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销售数量',
          type: 'bar',
          data: quantity,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#18a058' },
              { offset: 1, color: '#52c41a' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
  
  // 产品利润图表
  if (productProfitChartRef.value) {
    const chart = echarts.init(productProfitChartRef.value)
    
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const profit = [25.5, 32.8, 28.6, 22.3, 35.2]
    
    const option = {
      title: {
        text: '产品利润率分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: products
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '利润率',
          type: 'bar',
          data: profit,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#f0a020' },
              { offset: 1, color: '#faad14' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  }
}

const renderForecastChart = () => {
  if (!forecastChartRef.value) return
  
  const chart = echarts.init(forecastChartRef.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const actual = [1800, 2100, 2300, 2000, 2400, 2600, 2200, 2800, 2500, null, null, null]
  const forecast = [null, null, null, null, null, null, null, null, 2500, 2900, 3100, 3300]
  const target = [2000, 2200, 2400, 2200, 2600, 2800, 2400, 3000, 2700, 3100, 3300, 3500]
  
  const option = {
    title: {
      text: '业绩预测分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['实际业绩', '预测业绩', '目标业绩'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}万'
      }
    },
    series: [
      {
        name: '实际业绩',
        type: 'line',
        data: actual,
        lineStyle: { color: '#2080f0', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '预测业绩',
        type: 'line',
        data: forecast,
        lineStyle: { color: '#f0a020', width: 3, type: 'dashed' },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '目标业绩',
        type: 'line',
        data: target,
        lineStyle: { color: '#18a058', width: 2 },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }
  
  chart.setOption(option)
}

const renderMiniCharts = () => {
  coreMetrics.value.forEach(metric => {
    const chartEl = metricChartRefs.value[metric.key]
    if (!chartEl) return
    
    const chart = echarts.init(chartEl)
    
    // 生成模拟数据
    const data = Array.from({ length: 7 }, () => Math.floor(Math.random() * 100) + 50)
    
    const option = {
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      },
      xAxis: {
        type: 'category',
        show: false,
        data: ['', '', '', '', '', '', '']
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [
        {
          type: 'line',
          data: data,
          lineStyle: { color: metric.color, width: 2 },
          symbol: 'none',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: metric.color + '40' },
              { offset: 1, color: metric.color + '10' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
  })
}

// 监听产品标签页切换
watch(activeProductTab, () => {
  nextTick(() => {
    renderProductCharts()
  })
})

// 生命周期
onMounted(async () => {
  await nextTick()
  renderSalesTrendChart()
  renderTargetChart()
  renderFunnelChart()
  renderSourceChart()
  renderProductCharts()
  renderForecastChart()
  renderMiniCharts()
})
</script>

<style scoped>
.performance-dashboard {
  padding: 24px;
}

.config-card,
.metrics-card,
.ranking-card,
.product-card,
.realtime-card {
  margin-bottom: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 500;
  color: #333;
}

.metrics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-icon {
  font-size: 32px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #18a058;
}

.metric-change.negative {
  color: #d03050;
}

.metric-chart {
  width: 80px;
  height: 40px;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.ranking-position {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 60px;
}

.position-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
}

.position-number.rank-1 {
  background: #ffd700;
}

.position-number.rank-2 {
  background: #c0c0c0;
}

.position-number.rank-3 {
  background: #cd7f32;
}

.position-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: #999;
}

.position-medal {
  font-size: 20px;
}

.ranking-info {
  flex: 1;
}

.team-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.team-leader {
  color: #666;
  font-size: 14px;
}

.ranking-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.ranking-progress {
  width: 120px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.suggestion-icon {
  font-size: 20px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.suggestion-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.suggestion-impact {
  color: #18a058;
  font-size: 12px;
  font-weight: 500;
}

.realtime-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.realtime-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.realtime-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.realtime-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.realtime-value {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.realtime-value.up {
  color: #18a058;
}

.realtime-value.down {
  color: #d03050;
}

.realtime-trend {
  font-size: 16px;
}

.realtime-change {
  color: #666;
  font-size: 12px;
}

.realtime-alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.alert-icon {
  font-size: 18px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-size: 14px;
}

.alert-message {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.alert-time {
  color: #999;
  font-size: 12px;
}
</style>