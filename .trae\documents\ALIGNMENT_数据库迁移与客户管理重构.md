# 数据库迁移与客户管理重构 - 对齐文档

## 1. 项目上下文分析

### 1.1 现有项目架构

* **前端技术栈**: Vue 3 + TypeScript + Pinia + Naive UI + Vite

* **后端技术栈**: Express.js + TypeScript

* **当前数据库**: Supabase (PostgreSQL)

* **本地数据库**: SQLite (DatabaseManager.js)

* **目标数据库**: 本地 MySQL

### 1.2 现有功能模块

* **客户管理**: CustomerList.vue, CustomerPool.vue, customerStore.ts, customerService.ts

* **选项管理**: OptionsManagement.vue (包含选项分类和选项数据两个标签页)

* **数据库管理**: DatabaseManager.js (SQLite), Supabase migrations

* **API路由**: auth.ts, options.ts, optionsManagement.ts

### 1.3 现有数据模型

* **客户表**: customers (包含基本信息、来源、状态、等级等字段)

* **选项分类表**: option\_categories

* **选项数据表**: option\_items

* **用户表**: users

* **跟进记录表**: follow\_records

* **见面记录表**: meeting\_records

## 2. 需求理解确认

### 2.1 原始需求

用户要求:

1. 删除所有项目本身的测试数据
2. 将所有数据移入本地MySQL数据库
3. 在选项管理标签页中新建客户管理选项卡
4. 将客户表的参数增删改查移动到选项卡中

### 2.2 需求分析

#### 2.2.1 数据库迁移需求

* **源数据库**: Supabase (PostgreSQL) + SQLite

* **目标数据库**: 本地 MySQL

* **迁移范围**: 所有业务数据表和测试数据

* **数据清理**: 删除测试数据，保留结构和必要的基础数据

#### 2.2.2 前端界面重构需求

* **现有结构**: OptionsManagement.vue 包含两个标签页

  * 选项分类管理 (OptionCategoriesManagement.vue)

  * 选项数据管理 (OptionItemsManagement.vue)

* **目标结构**: 增加第三个标签页

  * 选项分类管理

  * 选项数据管理

  * **客户管理** (新增)

#### 2.2.3 功能迁移需求

* 将现有的客户管理功能从独立页面迁移到选项管理的标签页中

* 保持客户的增删改查功能完整性

* 保持现有的客户筛选、搜索、批量操作等功能

### 2.3 边界确认

#### 2.3.1 包含范围

* 数据库从 Supabase 迁移到本地 MySQL

* 客户管理界面重构到选项管理页面

* 相关 API 接口调整

* 数据库连接配置更新

#### 2.3.2 不包含范围

* 不修改客户管理的核心业务逻辑

* 不改变现有的权限控制机制

* 不修改其他业务模块(如跟进记录、营销管理等)

## 3. 疑问澄清

### 3.1 数据库迁移相关

* **MySQL连接配置**: 环境变量中已有MySQL配置，需要确认是否使用现有配置

* **数据保留策略**: 是否需要保留用户账户数据和权限数据

* **迁移时机**: 是否需要支持数据库切换的平滑过渡

### 3.2 界面重构相关

* **客户管理功能范围**: 是否包含客户池管理、跟进记录等相关功能

* **导航调整**: 原有的客户管理菜单项是否需要移除或重定向

* **权限控制**: 客户管理在选项管理页面中的权限如何控制

### 3.3 技术实现相关

* **API路由调整**: 是否需要新建客户管理的API路由文件

* **状态管理**: customerStore 是否需要调整以适应新的页面结构

* **组件复用**: 现有的客户管理组件如何在新的标签页中复用

## 4. 技术约束

### 4.1 现有技术栈约束

* 必须保持 Vue 3 + TypeScript + Pinia + Naive UI 的技术栈

* 必须保持现有的组件化架构

* 必须保持现有的 API 接口规范

### 4.2 数据库约束

* MySQL 版本兼容性要求

* 数据类型映射 (PostgreSQL -> MySQL)

* 外键约束和索引的迁移

### 4.3 业务约束

* 不能影响现有用户的使用体验

* 必须保持数据的完整性和一致性

* 必须保持现有的业务流程不变

## 5. 验收标准

### 5.1 数据库迁移验收

* [ ] 成功连接本地 MySQL 数据库

* [ ] 所有表结构正确迁移

* [ ] 基础数据(选项分类、选项数据)正确迁移

* [ ] 测试数据已清理

* [ ] 数据库连接配置正确更新

### 5.2 前端界面验收

* [ ] 选项管理页面新增客户管理标签页

* [ ] 客户管理功能在新标签页中正常工作

* [ ] 客户列表、搜索、筛选功能正常

* [ ] 客户增删改查功能正常

* [ ] 批量操作功能正常

### 5.3 API接口验收

* [ ] 客户管理 API 接口正常工作

* [ ] 数据库操作正确连接到 MySQL

* [ ] API 响应格式保持一致

* [ ] 错误处理机制正常

### 5.4 整体验收

* [ ] 项目编译通过

* [ ] 类型检查通过

* [ ] 功能测试通过

* [ ] 性能无明显下降

