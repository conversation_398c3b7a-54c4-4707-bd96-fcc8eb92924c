-- 插入选项数据到 option_categories 和 option_items 表
-- 此脚本将所有硬编码的选项数据迁移到数据库中

-- 插入选项分类数据（如果不存在则插入）
INSERT INTO option_categories (code, name, description, sort_order) VALUES
('customer_source', '客户来源', '客户获取渠道分类', 1),
('customer_category', '客户等级', '客户重要程度分类', 2),
('gender', '性别', '客户性别分类', 3),
('decoration_type', '装修类型', '装修风格分类', 4),
('house_status', '房屋状态', '房屋交付状态分类', 5),
('contract_package', '合同套餐', '合同套餐类型分类', 6),
('business_line', '业务线', '公司业务线分类', 7),
('sales_team', '销售团队', '销售团队分类', 8),
('compare_period', '对比周期', '数据对比周期分类', 9)
ON CONFLICT (code) DO NOTHING;

-- 插入客户来源选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'online_promotion', '线上推广', 'online_promotion', '#2080f0', 'globe-outline', '通过网络广告、SEO等线上渠道获得的客户', 1, true),
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'friend_referral', '朋友介绍', 'friend_referral', '#18a058', 'people-outline', '通过现有客户或朋友推荐获得的客户', 2, true),
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'exhibition', '展会活动', 'exhibition', '#f0a020', 'calendar-outline', '通过参加展会、活动等获得的客户', 3, true),
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'store_consultation', '门店咨询', 'store_consultation', '#d03050', 'storefront-outline', '客户主动到门店咨询获得的客户', 4, true),
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'telemarketing', '电话营销', 'telemarketing', '#7c3aed', 'call-outline', '通过电话营销获得的客户', 5, true),
((SELECT id FROM option_categories WHERE code = 'customer_source'), 'other_channel', '其他渠道', 'other_channel', '#6b7280', 'ellipsis-horizontal-outline', '其他未分类的客户获取渠道', 6, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入客户等级选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'customer_category'), 'vip', 'VIP客户', 'vip', '#ffd700', 'star-outline', '高价值重要客户', 1, true),
((SELECT id FROM option_categories WHERE code = 'customer_category'), 'important', '重要客户', 'important', '#f0a020', 'diamond-outline', '具有较高价值的客户', 2, true),
((SELECT id FROM option_categories WHERE code = 'customer_category'), 'potential', '潜在客户', 'potential', '#2080f0', 'eye-outline', '有潜在价值的客户', 3, true),
((SELECT id FROM option_categories WHERE code = 'customer_category'), 'ordinary', '普通客户', 'ordinary', '#18a058', 'person-outline', '一般价值客户', 4, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入性别选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'gender'), 'male', '男', 'male', '#2080f0', 'male-outline', '男性客户', 1, true),
((SELECT id FROM option_categories WHERE code = 'gender'), 'female', '女', 'female', '#d03050', 'female-outline', '女性客户', 2, true),
((SELECT id FROM option_categories WHERE code = 'gender'), 'unknown', '未知', 'unknown', '#6b7280', 'help-outline', '性别未知', 3, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入装修类型选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'modern', '现代简约', 'modern', '#2080f0', 'cube-outline', '现代简约装修风格', 1, true),
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'chinese', '中式风格', 'chinese', '#d03050', 'library-outline', '传统中式装修风格', 2, true),
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'european', '欧式风格', 'european', '#f0a020', 'home-outline', '欧式古典装修风格', 3, true),
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'american', '美式风格', 'american', '#18a058', 'flag-outline', '美式乡村装修风格', 4, true),
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'nordic', '北欧风格', 'nordic', '#7c3aed', 'snow-outline', '北欧简约装修风格', 5, true),
((SELECT id FROM option_categories WHERE code = 'decoration_type'), 'industrial', '工业风格', 'industrial', '#6b7280', 'construct-outline', '工业风装修风格', 6, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入房屋状态选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'house_status'), 'delivered', '已交房', 'delivered', '#18a058', 'checkmark-circle-outline', '房屋已交付使用', 1, true),
((SELECT id FROM option_categories WHERE code = 'house_status'), 'under_construction', '建设中', 'under_construction', '#f0a020', 'hammer-outline', '房屋正在建设中', 2, true),
((SELECT id FROM option_categories WHERE code = 'house_status'), 'not_delivered', '未交房', 'not_delivered', '#d03050', 'time-outline', '房屋尚未交付', 3, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入合同套餐选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'contract_package'), 'basic', '基础套餐', 'basic', '#6b7280', 'layers-outline', '基础装修套餐', 1, true),
((SELECT id FROM option_categories WHERE code = 'contract_package'), 'standard', '标准套餐', 'standard', '#2080f0', 'cube-outline', '标准装修套餐', 2, true),
((SELECT id FROM option_categories WHERE code = 'contract_package'), 'premium', '高级套餐', 'premium', '#f0a020', 'diamond-outline', '高级装修套餐', 3, true),
((SELECT id FROM option_categories WHERE code = 'contract_package'), 'luxury', '豪华套餐', 'luxury', '#d03050', 'star-outline', '豪华装修套餐', 4, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入业务线选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'business_line'), 'home_decoration', '家装', 'home_decoration', '#2080f0', 'home-outline', '家庭装修业务', 1, true),
((SELECT id FROM option_categories WHERE code = 'business_line'), 'office_decoration', '工装', 'office_decoration', '#f0a020', 'business-outline', '办公室装修业务', 2, true),
((SELECT id FROM option_categories WHERE code = 'business_line'), 'soft_decoration', '软装', 'soft_decoration', '#d03050', 'color-palette-outline', '软装设计业务', 3, true),
((SELECT id FROM option_categories WHERE code = 'business_line'), 'whole_house', '整装', 'whole_house', '#18a058', 'cube-outline', '整屋装修业务', 4, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入销售团队选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'sales_team'), 'team_a', 'A组', 'team_a', '#2080f0', 'people-outline', '销售A组团队', 1, true),
((SELECT id FROM option_categories WHERE code = 'sales_team'), 'team_b', 'B组', 'team_b', '#18a058', 'people-outline', '销售B组团队', 2, true),
((SELECT id FROM option_categories WHERE code = 'sales_team'), 'team_c', 'C组', 'team_c', '#f0a020', 'people-outline', '销售C组团队', 3, true),
((SELECT id FROM option_categories WHERE code = 'sales_team'), 'team_d', 'D组', 'team_d', '#d03050', 'people-outline', '销售D组团队', 4, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 插入对比周期选项数据
INSERT INTO option_items (category_id, code, label, value, color, icon, description, sort_order, is_active) VALUES
((SELECT id FROM option_categories WHERE code = 'compare_period'), 'last_week', '上周', 'last_week', '#2080f0', 'calendar-outline', '与上周数据对比', 1, true),
((SELECT id FROM option_categories WHERE code = 'compare_period'), 'last_month', '上月', 'last_month', '#18a058', 'calendar-outline', '与上月数据对比', 2, true),
((SELECT id FROM option_categories WHERE code = 'compare_period'), 'last_quarter', '上季度', 'last_quarter', '#f0a020', 'calendar-outline', '与上季度数据对比', 3, true),
((SELECT id FROM option_categories WHERE code = 'compare_period'), 'last_year', '去年同期', 'last_year', '#d03050', 'calendar-outline', '与去年同期数据对比', 4, true)
ON CONFLICT (category_id, code) DO NOTHING;

-- 验证插入的数据
SELECT 
    oc.name as category_name,
    COUNT(oi.id) as item_count
FROM option_categories oc
LEFT JOIN option_items oi ON oc.id = oi.category_id
GROUP BY oc.id, oc.name, oc.sort_order
ORDER BY oc.sort_order;

-- 显示所有插入的选项数据
SELECT 
    oc.name as category_name,
    oi.label,
    oi.value,
    oi.color,
    oi.icon,
    oi.description,
    oi.sort_order,
    oi.is_active
FROM option_categories oc
JOIN option_items oi ON oc.id = oi.category_id
ORDER BY oc.sort_order, oi.sort_order;