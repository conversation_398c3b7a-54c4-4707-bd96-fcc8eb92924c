const express = require('express');
const { authenticateToken, requireRole } = require('./auth');
const router = express.Router();

// 应用认证中间件（临时禁用）
// router.use(authenticateToken);

// 获取客户列表
router.get('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { 
      page = 1, 
      pageSize = 10, 
      keyword, 
      status, 
      source, 
      level,
      ownerId,
      isInPool,
      startDate,
      endDate
    } = req.query;

    let whereClause = '1=1';
    let whereParams = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.wechat LIKE ? OR c.company LIKE ?)';
      const searchTerm = `%${keyword}%`;
      whereParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND c.status = ?';
      whereParams.push(status);
    }

    // 来源筛选
    if (source) {
      whereClause += ' AND c.source = ?';
      whereParams.push(source);
    }

    // 客户等级筛选
    if (level) {
      whereClause += ' AND c.level = ?';
      whereParams.push(level);
    }

    // 负责人筛选
    if (ownerId) {
      whereClause += ' AND c.owner_id = ?';
      whereParams.push(ownerId);
    }

    // 公海池筛选
    if (isInPool !== undefined) {
      whereClause += ' AND c.is_in_pool = ?';
      whereParams.push(isInPool === 'true' ? 1 : 0);
    }

    // 日期范围筛选
    if (startDate) {
      whereClause += ' AND c.created_at >= ?';
      whereParams.push(startDate);
    }
    if (endDate) {
      whereClause += ' AND c.created_at <= ?';
      whereParams.push(endDate + ' 23:59:59');
    }

    // 权限控制：普通销售只能看到自己的客户（临时禁用）
    // if (req.user && req.user.role === 'sales') {
    //   whereClause += ' AND c.owner_id = ?';
    //   whereParams.push(req.user.userId);
    // }

    const offset = (page - 1) * pageSize;
    
    const sql = `
      SELECT 
        c.*,
        u.name as owner_name,
        d.name as department_name,
        GROUP_CONCAT(ct.name) as tag_names
      FROM customers c
      LEFT JOIN users u ON c.owner_id = u.id
      LEFT JOIN departments d ON u.department_id = d.id
      LEFT JOIN customer_tag_relations ctr ON c.id = ctr.customer_id
      LEFT JOIN customer_tags ct ON ctr.tag_id = ct.id
      WHERE ${whereClause}
      GROUP BY c.id
      ORDER BY c.updated_at DESC
      LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
    `;

    const countSql = `
      SELECT COUNT(DISTINCT c.id) as total
      FROM customers c
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE ${whereClause}
    `;
    const customers = await db.query(sql, whereParams);
    const countResult = await db.query(countSql, whereParams);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: customers.rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取客户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取客户列表失败',
      data: [] // 返回空数组作为兜底
    });
  }
});

// 获取客户详情
router.get('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const customer = await db.query(`
      SELECT 
        c.*,
        u.name as owner_name,
        u.position as owner_position,
        d.name as department_name
      FROM customers c
      LEFT JOIN users u ON c.owner_id = u.id
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE c.id = ?
    `, [id]);

    if (!customer.rows.length) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    const customerData = customer.rows[0];

    // 权限检查：普通销售只能查看自己的客户（临时禁用）
    // if (req.user && req.user.role === 'sales' && customerData.owner_id !== req.user.userId) {
    //   return res.status(403).json({
    //     success: false,
    //     message: '权限不足'
    //   });
    // }

    // 获取客户标签
    const tags = await db.query(`
      SELECT ct.id, ct.name, ct.color, ct.category
      FROM customer_tags ct
      JOIN customer_tag_relations ctr ON ct.id = ctr.tag_id
      WHERE ctr.customer_id = ?
    `, [id]);

    // 获取跟进记录
    const followRecords = await db.query(`
      SELECT 
        fr.*,
        u.name as user_name,
        u.position as user_position
      FROM follow_records fr
      LEFT JOIN users u ON fr.user_id = u.id
      WHERE fr.customer_id = ?
      ORDER BY fr.created_at DESC
      LIMIT 20
    `, [id]);

    // 获取见面记录
    const meetingRecords = await db.query(`
      SELECT 
        mr.*,
        u.name as user_name,
        fr.content as follow_content
      FROM meeting_records mr
      LEFT JOIN users u ON mr.user_id = u.id
      LEFT JOIN follow_records fr ON mr.follow_record_id = fr.id
      WHERE mr.customer_id = ?
      ORDER BY mr.meeting_time DESC
      LIMIT 10
    `, [id]);

    res.json({
      success: true,
      data: {
        ...customerData,
        tags: tags.rows,
        followRecords: followRecords.rows,
        meetingRecords: meetingRecords.rows
      }
    });

  } catch (error) {
    console.error('获取客户详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取客户详情失败'
    });
  }
});

// 创建客户
router.post('/', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const {
      name,
      phone,
      email,
      wechat,
      address,
      source,
      status,
      level,
      birthday,
      gender,
      is_vip,
      is_high_value,
      tags,
      remark,
      owner_id
    } = req.body;

    // 验证必填字段
    if (!name || !phone) {
      return res.status(400).json({
        success: false,
        message: '客户姓名和手机号为必填项'
      });
    }

    // 检查手机号是否已存在
    const existingCustomer = await db.query('SELECT id FROM customers WHERE phone = ?', [phone]);
    if (existingCustomer.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该手机号的客户已存在'
      });
    }

    // 创建客户
    const customerData = {
      name,
      phone,
      email: email || null,
      wechat: wechat || null,
      address: address || null,
      source: source || 'other',
      status: status || 'potential',
      level: level || 'C',
      birthday: birthday || null,
      gender: gender || 'unknown',
      is_vip: is_vip ? 1 : 0,
      is_high_value: is_high_value ? 1 : 0,
      remark: remark || null,
      owner_id: owner_id || 1, // 默认分配给管理员
      is_in_pool: 0,
      follow_count: 0,
      deal_amount: 0.00
    };

    const result = await db.query(
      `INSERT INTO customers (${Object.keys(customerData).join(', ')}) 
       VALUES (${Object.keys(customerData).map(() => '?').join(', ')})`,
      Object.values(customerData)
    );

    const customerId = result.insertId;

    // 处理标签关联
    if (tags && Array.isArray(tags) && tags.length > 0) {
      for (const tagId of tags) {
        await db.query(
          'INSERT INTO customer_tag_relations (customer_id, tag_id) VALUES (?, ?)',
          [customerId, tagId]
        );
      }
    }

    // 记录操作日志
    await db.query(
      `INSERT INTO operation_logs (user_id, module, action, target_type, target_id, content, ip_address) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [1, 'customer', 'create', 'customer', customerId, `创建客户：${name}`, req.ip || '127.0.0.1']
    );

    res.json({
      success: true,
      message: '客户创建成功',
      data: { id: customerId }
    });

  } catch (error) {
    console.error('创建客户错误:', error);
    res.status(500).json({
      success: false,
      message: '创建客户失败'
    });
  }
});

// 更新客户
router.put('/:id', async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const {
      name,
      phone,
      email,
      company,
      position,
      source,
      level,
      status,
      assigned_to,
      notes
    } = req.body;

    // 检查客户是否存在
    const customer = await db.findOne('customers', 'id = ?', [id]);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    // 权限检查：普通员工只能修改自己的客户
    if (req.user.role === 'employee' && customer.assigned_to !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 检查手机号是否被其他客户使用
    if (phone && phone !== customer.phone) {
      const existingCustomer = await db.findOne('customers', 'phone = ? AND id != ?', [phone, id]);
      if (existingCustomer) {
        return res.status(400).json({
          success: false,
          message: '该手机号已被其他客户使用'
        });
      }
    }

    // 更新客户信息
    const updateData = {
      updated_at: new Date()
    };

    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (email !== undefined) updateData.email = email;
    if (company !== undefined) updateData.company = company;
    if (position !== undefined) updateData.position = position;
    if (source) updateData.source = source;
    if (level) updateData.level = level;
    if (status) updateData.status = status;
    if (assigned_to) updateData.assigned_to = assigned_to;
    if (notes !== undefined) updateData.notes = notes;

    await db.update('customers', updateData, 'id = ?', [id]);

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'update_customer',
      target_type: 'customer',
      target_id: id,
      description: `更新客户：${customer.name}`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '客户更新成功'
    });

  } catch (error) {
    console.error('更新客户错误:', error);
    res.status(500).json({
      success: false,
      message: '更新客户失败'
    });
  }
});

// 删除客户（软删除）
router.delete('/:id', requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查客户是否存在
    const customer = await db.findOne('customers', 'id = ?', [id]);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    // 软删除
    await db.update(
      'customers',
      { 
        status: 'deleted',
        deleted_at: new Date(),
        updated_at: new Date()
      },
      'id = ?',
      [id]
    );

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'delete_customer',
      target_type: 'customer',
      target_id: id,
      description: `删除客户：${customer.name}`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '客户删除成功'
    });

  } catch (error) {
    console.error('删除客户错误:', error);
    res.status(500).json({
      success: false,
      message: '删除客户失败'
    });
  }
});

// 分配客户
router.post('/:id/assign', requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { assigned_to } = req.body;

    if (!assigned_to) {
      return res.status(400).json({
        success: false,
        message: '请选择负责人'
      });
    }

    // 检查客户是否存在
    const customer = await db.findOne('customers', 'id = ?', [id]);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    // 检查负责人是否存在
    const user = await db.findOne('users', 'id = ?', [assigned_to]);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '负责人不存在'
      });
    }

    // 更新客户负责人
    await db.update(
      'customers',
      { 
        assigned_to,
        updated_at: new Date()
      },
      'id = ?',
      [id]
    );

    // 记录操作日志
    await db.insert('operation_logs', {
      user_id: req.user.userId,
      action: 'assign_customer',
      target_type: 'customer',
      target_id: id,
      description: `将客户 ${customer.name} 分配给 ${user.name}`,
      created_at: new Date()
    });

    res.json({
      success: true,
      message: '客户分配成功'
    });

  } catch (error) {
    console.error('分配客户错误:', error);
    res.status(500).json({
      success: false,
      message: '分配客户失败'
    });
  }
});

// 获取客户统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const db = req.app.locals.db;

    let whereClause = 'status != "deleted"';
    let whereParams = [];

    // 权限控制：普通员工只能看到自己的统计
    if (req.user.role === 'employee') {
      whereClause += ' AND assigned_to = ?';
      whereParams.push(req.user.userId);
    }

    const stats = await Promise.all([
      // 总客户数
      db.query(`SELECT COUNT(*) as total FROM customers WHERE ${whereClause}`, whereParams),
      // 各状态客户数
      db.query(`
        SELECT status, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY status
      `, whereParams),
      // 各等级客户数
      db.query(`
        SELECT level, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY level
      `, whereParams),
      // 各来源客户数
      db.query(`
        SELECT source, COUNT(*) as count 
        FROM customers 
        WHERE ${whereClause}
        GROUP BY source
      `, whereParams),
      // 最近7天新增客户
      db.query(`
        SELECT COUNT(*) as new_customers 
        FROM customers 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND ${whereClause}
      `, whereParams)
    ]);

    const statusStats = {};
    stats[1].rows.forEach(item => {
      statusStats[item.status] = item.count;
    });

    const levelStats = {};
    stats[2].rows.forEach(item => {
      levelStats[item.level] = item.count;
    });

    const sourceStats = {};
    stats[3].rows.forEach(item => {
      sourceStats[item.source] = item.count;
    });

    res.json({
      success: true,
      data: {
        total: stats[0].rows[0].total,
        newCustomers: stats[4].rows[0].new_customers,
        statusStats,
        levelStats,
        sourceStats
      }
    });

  } catch (error) {
    console.error('获取客户统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取客户统计失败'
    });
  }
});

module.exports = router;