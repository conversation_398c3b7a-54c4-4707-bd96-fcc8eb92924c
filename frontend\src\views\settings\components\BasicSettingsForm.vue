<template>
  <div class="basic-settings-form">
    <n-form
      ref="basicFormRef"
      :model="basicSettings"
      :rules="basicRules"
      label-placement="left"
      label-width="150px"
      class="settings-form"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="系统名称" path="systemName">
          <n-input
            v-model:value="basicSettings.systemName"
            placeholder="请输入系统名称"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="系统版本" path="systemVersion">
          <n-input
            v-model:value="basicSettings.systemVersion"
            placeholder="请输入系统版本"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="公司名称" path="companyName">
          <n-input
            v-model:value="basicSettings.companyName"
            placeholder="请输入公司名称"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="联系电话" path="contactPhone">
          <n-input
            v-model:value="basicSettings.contactPhone"
            placeholder="请输入联系电话"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-grid :cols="2" :x-gap="24">
        <n-form-item-gi label="联系邮箱" path="contactEmail">
          <n-input
            v-model:value="basicSettings.contactEmail"
            placeholder="请输入联系邮箱"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="网站地址" path="websiteUrl">
          <n-input
            v-model:value="basicSettings.websiteUrl"
            placeholder="请输入网站地址"
          />
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="公司地址">
        <n-input
          v-model:value="basicSettings.companyAddress"
          placeholder="请输入公司地址"
        />
      </n-form-item>
      
      <n-form-item label="系统描述">
        <n-input
          v-model:value="basicSettings.systemDescription"
          type="textarea"
          placeholder="请输入系统描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item>
        <n-space>
          <n-button type="primary" @click="handleSave">
            <template #icon>
              <n-icon><SaveOutline /></n-icon>
            </template>
            保存设置
          </n-button>
          <n-button type="default" @click="handleReset">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            重置
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NButton,
  NIcon,
  NSpace,
  useMessage
} from 'naive-ui'
import { SaveOutline, RefreshOutline } from '@vicons/ionicons5'

const message = useMessage()

interface BasicSettings {
  systemName: string
  systemVersion: string
  companyName: string
  contactPhone: string
  contactEmail: string
  websiteUrl: string
  companyAddress: string
  systemDescription: string
}

const basicFormRef = ref()
const basicSettings = reactive<BasicSettings>({
  systemName: 'YYSH客户管理系统',
  systemVersion: '1.0.0',
  companyName: '',
  contactPhone: '',
  contactEmail: '',
  websiteUrl: '',
  companyAddress: '',
  systemDescription: ''
})

const basicRules = {
  systemName: {
    required: true,
    message: '请输入系统名称',
    trigger: 'blur'
  },
  systemVersion: {
    required: true,
    message: '请输入系统版本',
    trigger: 'blur'
  }
}

const handleSave = async () => {
  try {
    await basicFormRef.value?.validate()
    // TODO: 调用API保存设置
    message.success('基础设置保存成功')
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const handleReset = () => {
  basicFormRef.value?.restoreValidation()
  Object.assign(basicSettings, {
    systemName: 'YYSH客户管理系统',
    systemVersion: '1.0.0',
    companyName: '',
    contactPhone: '',
    contactEmail: '',
    websiteUrl: '',
    companyAddress: '',
    systemDescription: ''
  })
}

onMounted(() => {
  // TODO: 从API加载设置数据
})
</script>

<style scoped>
.basic-settings-form {
  padding: 24px;
}

.settings-form {
  max-width: 800px;
}

/* 操作按钮统一样式 */
:deep(.n-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

:deep(.n-button--primary-type:hover) {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

:deep(.n-button--default-type) {
  background: var(--n-button-color);
  border: 1px solid var(--n-border-color);
}

:deep(.n-button--default-type:hover) {
  background: var(--n-button-color-hover);
  border-color: var(--n-border-color-hover);
  transform: translateY(-1px);
}
</style>