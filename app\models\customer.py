"""客户模型

定义客户相关的数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Enum, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class CustomerStatus(enum.Enum):
    """客户状态枚举"""
    POTENTIAL = "potential"  # 潜在客户
    CONTACTED = "contacted"  # 已联系
    INTERESTED = "interested"  # 有意向
    NEGOTIATING = "negotiating"  # 洽谈中
    CLOSED_WON = "closed_won"  # 成交
    CLOSED_LOST = "closed_lost"  # 流失
    INACTIVE = "inactive"  # 非活跃


class CustomerLevel(enum.Enum):
    """客户等级枚举"""
    A = "A"  # A级客户
    B = "B"  # B级客户
    C = "C"  # C级客户
    D = "D"  # D级客户


class CustomerSource(enum.Enum):
    """客户来源枚举"""
    WEBSITE = "website"  # 官网
    REFERRAL = "referral"  # 推荐
    ADVERTISEMENT = "advertisement"  # 广告
    SOCIAL_MEDIA = "social_media"  # 社交媒体
    PHONE = "phone"  # 电话
    EMAIL = "email"  # 邮件
    EXHIBITION = "exhibition"  # 展会
    OTHER = "other"  # 其他


class Customer(BaseModel):
    """客户模型
    
    对应数据库中的customers表
    """
    __tablename__ = "customers"
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="客户姓名")
    phone = Column(String(20), nullable=True, comment="手机号")
    email = Column(String(100), nullable=True, comment="邮箱")
    company = Column(String(200), nullable=True, comment="公司名称")
    position = Column(String(100), nullable=True, comment="职位")
    
    # 状态信息
    status = Column(Enum(CustomerStatus), default=CustomerStatus.POTENTIAL, nullable=False, comment="客户状态")
    level = Column(Enum(CustomerLevel), default=CustomerLevel.C, nullable=False, comment="客户等级")
    source = Column(Enum(CustomerSource), nullable=True, comment="客户来源")
    is_vip = Column(Boolean, default=False, nullable=False, comment="是否VIP")
    
    # 联系信息
    wechat = Column(String(100), nullable=True, comment="微信号")
    qq = Column(String(20), nullable=True, comment="QQ号")
    address = Column(Text, nullable=True, comment="地址")
    website = Column(String(255), nullable=True, comment="公司网站")
    
    # 业务信息
    industry = Column(String(100), nullable=True, comment="所属行业")
    annual_revenue = Column(String(50), nullable=True, comment="年营业额")
    employee_count = Column(String(20), nullable=True, comment="员工数量")
    budget = Column(String(50), nullable=True, comment="预算")
    
    # 管理信息
    assigned_user_id = Column(String(36), ForeignKey('users.id'), nullable=True, comment="负责人ID")
    department_id = Column(String(36), ForeignKey('departments.id'), nullable=True, comment="所属部门ID")
    created_by = Column(String(36), ForeignKey('users.id'), nullable=True, comment="创建人ID")
    
    # 时间信息
    last_contact_time = Column(DateTime, nullable=True, comment="最后联系时间")
    next_follow_time = Column(DateTime, nullable=True, comment="下次跟进时间")
    first_contact_time = Column(DateTime, nullable=True, comment="首次联系时间")
    
    # 标签和备注
    tags = Column(Text, nullable=True, comment="标签（JSON格式）")
    notes = Column(Text, nullable=True, comment="备注")
    description = Column(Text, nullable=True, comment="客户描述")
    
    # 统计信息
    follow_count = Column(Integer, default=0, nullable=False, comment="跟进次数")
    meeting_count = Column(Integer, default=0, nullable=False, comment="会议次数")
    
    # 状态标记
    is_active = Column(Boolean, default=True, nullable=False, comment="是否活跃")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")
    
    # 关联关系
    # TODO: 暂时注释掉用户关系，避免循环引用问题
    # assigned_user = relationship("User", foreign_keys=[assigned_user_id], back_populates="assigned_customers", lazy="select")
    # creator = relationship("User", foreign_keys=[created_by], back_populates="created_customers", lazy="select")
    department = relationship("Department", lazy="select")
    
    # 跟进记录
    # TODO: 暂时注释掉跟进记录关系，避免循环引用问题
    # follow_records = relationship("CustomerFollowRecord", back_populates="customer", lazy="select")
    
    # 会议记录
    meetings = relationship("Meeting", secondary="meeting_participants", back_populates="customers", lazy="select")
    
    # 公海记录
    pool_records = relationship("PublicPool", back_populates="customer", lazy="select")
    
    # 行为记录
    behaviors = relationship("CustomerBehavior", back_populates="customer", lazy="select")
    
    def __repr__(self):
        return f"<Customer(id={self.id}, name={self.name}, company={self.company})>"
    
    @property
    def display_name(self):
        """显示名称"""
        if self.company:
            return f"{self.name}({self.company})"
        return self.name
    
    @property
    def status_display(self):
        """状态显示名称"""
        status_map = {
            CustomerStatus.POTENTIAL: "潜在客户",
            CustomerStatus.CONTACTED: "已联系",
            CustomerStatus.INTERESTED: "有意向",
            CustomerStatus.NEGOTIATING: "洽谈中",
            CustomerStatus.CLOSED_WON: "成交",
            CustomerStatus.CLOSED_LOST: "流失",
            CustomerStatus.INACTIVE: "非活跃"
        }
        return status_map.get(self.status, self.status.value)
    
    @property
    def level_display(self):
        """等级显示名称"""
        return f"{self.level.value}级客户"
    
    def update_follow_count(self):
        """更新跟进次数"""
        # TODO: 暂时注释掉，因为follow_records关系已被注释
        # self.follow_count = len(self.follow_records)
        self.follow_count = 0  # 临时设置为0
    
    def update_meeting_count(self):
        """更新会议次数"""
        self.meeting_count = len(self.meetings)