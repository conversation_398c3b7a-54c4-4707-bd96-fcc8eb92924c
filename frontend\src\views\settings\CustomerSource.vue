<template>
  <div class="customer-source">
    <div class="header">
      <n-space justify="space-between">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索来源名称"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <n-icon><search-outline /></n-icon>
          </template>
        </n-input>
        <n-button type="primary" @click="showModal = true">
          <template #icon>
            <n-icon><add-outline /></n-icon>
          </template>
          新增来源
        </n-button>
      </n-space>
    </div>

    <n-data-table
      :columns="columns"
      :data="filteredSources"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: Source) => row.id"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="card" style="width: 500px" title="客户来源">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item label="来源名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入来源名称" />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入来源描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort">
          <n-input-number
            v-model:value="formData.sort"
            :min="1"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-switch
            v-model:value="formData.status"
            :checked-value="'active'"
            :unchecked-value="'inactive'"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  SearchOutline,
  AddOutline
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

interface Source {
  id: number
  name: string
  description: string
  sort: number
  status: 'active' | 'inactive'
  createTime: string
}

const message = useMessage()
const dialog = useDialog()
const loading = ref(false)
const showModal = ref(false)
const searchKeyword = ref('')
const formRef = ref()

const formData = ref({
  id: 0,
  name: '',
  description: '',
  sort: 1,
  status: 'active' as 'active' | 'inactive'
})

const rules = {
  name: {
    required: true,
    message: '请输入来源名称',
    trigger: 'blur'
  }
}

const sources = ref<Source[]>([
  {
    id: 1,
    name: '官网咨询',
    description: '通过官网咨询获得的客户',
    sort: 1,
    status: 'active',
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    name: '电话营销',
    description: '通过电话营销获得的客户',
    sort: 2,
    status: 'active',
    createTime: '2024-01-15 10:05:00'
  }
])

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
}

const filteredSources = computed(() => {
  if (!searchKeyword.value) return sources.value
  return sources.value.filter(item =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const columns: DataTableColumns<Source> = [
  {
    title: '来源名称',
    key: 'name'
  },
  {
    title: '描述',
    key: 'description'
  },
  {
    title: '排序',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h('span', {
        class: row.status === 'active' ? 'text-green-600' : 'text-red-600'
      }, row.status === 'active' ? '启用' : '禁用')
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return h('div', { class: 'flex gap-2' }, [
        h('button', {
          class: 'px-2 py-1 text-blue-600 hover:bg-blue-50 rounded',
          onClick: () => handleEdit(row)
        }, '编辑'),
        h('button', {
          class: 'px-2 py-1 text-red-600 hover:bg-red-50 rounded',
          onClick: () => handleDelete(row)
        }, '删除')
      ])
    }
  }
]

const handleEdit = (row: Source) => {
  formData.value = { ...row }
  showModal.value = true
}

const handleDelete = (row: Source) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除来源"${row.name}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = sources.value.findIndex(item => item.id === row.id)
      if (index > -1) {
        sources.value.splice(index, 1)
        message.success('删除成功')
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.value.id === 0) {
      // 新增
      const newId = Math.max(...sources.value.map(item => item.id)) + 1
      sources.value.push({
        ...formData.value,
        id: newId,
        createTime: new Date().toLocaleString()
      })
      message.success('新增成功')
    } else {
      // 编辑
      const index = sources.value.findIndex(item => item.id === formData.value.id)
      if (index > -1) {
        sources.value[index] = { ...sources.value[index], ...formData.value }
        message.success('编辑成功')
      }
    }
    
    showModal.value = false
    resetForm()
  } catch (error) {
    message.error('请检查表单输入')
  }
}

const resetForm = () => {
  formData.value = {
    id: 0,
    name: '',
    description: '',
    sort: 1,
    status: 'active'
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.customer-source {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}
</style>